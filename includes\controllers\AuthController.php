<?php
require_once __DIR__ . '/../helpers/functions.php';

class AuthController {
    private $pdo;

    public function __construct($pdo) {
        $this->pdo = $pdo;
    }

    public function login($username, $password) {
        try {
            $stmt = $this->pdo->prepare("SELECT * FROM users WHERE username = ?");
            $stmt->execute([$username]);
            $user = $stmt->fetch();

            if ($user && password_verify($password, $user['password'])) {
                // Set session
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['username'] = $user['username'];
                $_SESSION['role'] = $user['role'];
                
                // Log aktivitas login
                logActivity($user['id'], "User {$user['username']} berhasil login", 'login');
                
                return true;
            }

            return false;
        } catch (PDOException $e) {
            error_log("Login error: " . $e->getMessage());
            return false;
        }
    }

    public function register($username, $password, $nama, $role = 'user') {
        try {
            // Cek username sudah ada atau belum
            $stmt = $this->pdo->prepare("SELECT COUNT(*) FROM users WHERE username = ?");
            $stmt->execute([$username]);
            if ($stmt->fetchColumn() > 0) {
                return false;
            }
            
            // Hash password
            $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
            
            // Insert user baru
            $stmt = $this->pdo->prepare("
                INSERT INTO users (username, password, nama, role)
                VALUES (?, ?, ?, ?)
            ");
            
            $result = $stmt->execute([$username, $hashedPassword, $nama, $role]);
            
            if ($result) {
                $userId = $this->pdo->lastInsertId();
                logActivity($userId, "User baru {$username} berhasil didaftarkan", 'create');
            }
            
            return $result;
        } catch (PDOException $e) {
            error_log("Registration error: " . $e->getMessage());
            return false;
        }
    }

    public function logout() {
        if (isset($_SESSION['user_id'])) {
            // Log aktivitas logout sebelum menghapus session
            logActivity($_SESSION['user_id'], "User {$_SESSION['username']} berhasil logout", 'logout');
        }
        
        // Hapus semua data session
        session_unset();
        session_destroy();
        
        return true;
    }

    public function changePassword($userId, $currentPassword, $newPassword) {
        try {
            // Verifikasi password lama
            $stmt = $this->pdo->prepare("SELECT password FROM users WHERE id = ?");
            $stmt->execute([$userId]);
            $user = $stmt->fetch();
            
            if (!$user || !password_verify($currentPassword, $user['password'])) {
                return false;
            }
            
            // Update password
            $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
            $stmt = $this->pdo->prepare("UPDATE users SET password = ? WHERE id = ?");
            $result = $stmt->execute([$hashedPassword, $userId]);
            
            if ($result) {
                logActivity($userId, "User berhasil mengubah password", 'update');
            }
            
            return $result;
        } catch (PDOException $e) {
            error_log("Password change error: " . $e->getMessage());
            return false;
        }
    }

    public function resetPassword($email) {
        try {
            // Get user
            $stmt = $this->pdo->prepare("SELECT * FROM users WHERE email = ?");
            $stmt->execute([$email]);
            $user = $stmt->fetch();

            if (!$user) {
                return [
                    'success' => false,
                    'message' => 'Email tidak terdaftar'
                ];
            }

            // Generate reset token
            $token = bin2hex(random_bytes(32));
            $expires = date('Y-m-d H:i:s', strtotime('+1 hour'));

            // Save token
            $stmt = $this->pdo->prepare("
                INSERT INTO password_resets (user_id, token, expires_at)
                VALUES (?, ?, ?)
            ");
            $stmt->execute([$user['id'], $token, $expires]);

            // Send reset email
            $resetLink = "http://{$_SERVER['HTTP_HOST']}/reset-password.php?token=" . $token;
            $to = $user['email'];
            $subject = "Reset Password";
            $message = "Klik link berikut untuk reset password Anda: $resetLink";
            $headers = "From: <EMAIL>";

            mail($to, $subject, $message, $headers);

            return [
                'success' => true,
                'message' => 'Link reset password telah dikirim ke email Anda'
            ];
        } catch (PDOException $e) {
            return [
                'success' => false,
                'message' => 'Terjadi kesalahan saat reset password'
            ];
        }
    }
} 