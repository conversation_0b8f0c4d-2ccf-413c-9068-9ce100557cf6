<?php
require_once 'includes/config/database.php';
require_once 'includes/helpers/auth_helper.php';
require_once 'includes/helpers/functions.php';

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isLoggedIn()) {
    die('Please login first');
}

$currentUser = getCurrentUser();
$results = [];

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_insert'])) {
    try {
        // Check if status column exists
        $statusColumnExists = true;
        try {
            $stmt = $pdo->query("SHOW COLUMNS FROM supplier LIKE 'status'");
            $statusColumnExists = $stmt->rowCount() > 0;
        } catch (PDOException $e) {
            $statusColumnExists = false;
        }
        
        $testName = 'Test Supplier ' . date('Y-m-d H:i:s');
        
        if ($statusColumnExists) {
            $stmt = $pdo->prepare("INSERT INTO supplier (user_id, nama_supplier, kontak, email, alamat, keterangan, status) VALUES (?, ?, ?, ?, ?, ?, ?)");
            $result = $stmt->execute([
                $currentUser['id'],
                $testName,
                '081234567890',
                '<EMAIL>',
                'Test Address',
                'Test Description',
                'aktif'
            ]);
        } else {
            $stmt = $pdo->prepare("INSERT INTO supplier (user_id, nama_supplier, kontak, email, alamat, keterangan) VALUES (?, ?, ?, ?, ?, ?)");
            $result = $stmt->execute([
                $currentUser['id'],
                $testName,
                '081234567890',
                '<EMAIL>',
                'Test Address',
                'Test Description'
            ]);
        }
        
        if ($result) {
            $insertId = $pdo->lastInsertId();
            $results[] = "✅ Insert berhasil! ID: $insertId";
            $results[] = "✅ Nama: $testName";
            $results[] = "✅ Status column exists: " . ($statusColumnExists ? 'Yes' : 'No');
        } else {
            $results[] = "❌ Insert gagal";
        }
        
    } catch (PDOException $e) {
        $results[] = "❌ PDO Error: " . $e->getMessage();
        $results[] = "❌ SQL State: " . $e->getCode();
    } catch (Exception $e) {
        $results[] = "❌ General Error: " . $e->getMessage();
    }
}

// Get current suppliers
try {
    $stmt = $pdo->prepare("SELECT * FROM supplier WHERE user_id = ? ORDER BY id DESC LIMIT 5");
    $stmt->execute([$currentUser['id']]);
    $suppliers = $stmt->fetchAll();
} catch (PDOException $e) {
    $suppliers = [];
    $results[] = "❌ Error fetching suppliers: " . $e->getMessage();
}

?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Supplier Insert - KeuanganKu</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">
                            <i class="fas fa-flask me-2"></i>Test Supplier Insert
                        </h4>
                    </div>
                    <div class="card-body">
                        
                        <!-- Test Results -->
                        <?php if (!empty($results)): ?>
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>Test Results:</h6>
                            <ul class="mb-0">
                                <?php foreach ($results as $result): ?>
                                <li><?= $result ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                        <?php endif; ?>

                        <!-- Test Form -->
                        <div class="text-center mb-4">
                            <form method="POST">
                                <button type="submit" name="test_insert" class="btn btn-warning btn-lg">
                                    <i class="fas fa-flask me-2"></i>Test Insert Supplier
                                </button>
                            </form>
                        </div>

                        <!-- Current Suppliers -->
                        <h5>Latest Suppliers (Last 5):</h5>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead class="table-dark">
                                    <tr>
                                        <th>ID</th>
                                        <th>Nama</th>
                                        <th>Kontak</th>
                                        <th>Email</th>
                                        <th>Status</th>
                                        <th>Created At</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (empty($suppliers)): ?>
                                    <tr>
                                        <td colspan="7" class="text-center text-muted">No suppliers found</td>
                                    </tr>
                                    <?php else: ?>
                                    <?php foreach ($suppliers as $supplier): ?>
                                    <tr>
                                        <td><?= htmlspecialchars($supplier['id']) ?></td>
                                        <td><?= htmlspecialchars($supplier['nama_supplier']) ?></td>
                                        <td><?= htmlspecialchars($supplier['kontak'] ?? '-') ?></td>
                                        <td><?= htmlspecialchars($supplier['email'] ?? '-') ?></td>
                                        <td>
                                            <?php if (isset($supplier['status'])): ?>
                                                <span class="badge bg-<?= $supplier['status'] === 'aktif' ? 'success' : 'secondary' ?>">
                                                    <?= ucfirst($supplier['status']) ?>
                                                </span>
                                            <?php else: ?>
                                                <span class="badge bg-info">N/A</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?= formatTanggal($supplier['created_at'] ?? '') ?></td>
                                        <td>
                                            <?php if (strpos($supplier['nama_supplier'], 'Test Supplier') !== false): ?>
                                            <form method="POST" style="display: inline;">
                                                <input type="hidden" name="delete_id" value="<?= $supplier['id'] ?>">
                                                <button type="submit" name="delete_test" class="btn btn-sm btn-danger" onclick="return confirm('Delete this test record?')">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>

                        <div class="text-center mt-4">
                            <a href="/keuangan/supplier.php" class="btn btn-success me-2">
                                <i class="fas fa-truck me-2"></i>Go to Supplier Page
                            </a>
                            <a href="/keuangan/debug_supplier.php" class="btn btn-info me-2">
                                <i class="fas fa-bug me-2"></i>Debug Info
                            </a>
                            <a href="/keuangan/fix_supplier_table.php" class="btn btn-warning">
                                <i class="fas fa-wrench me-2"></i>Fix Table
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>

<?php
// Handle delete test records
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['delete_test']) && isset($_POST['delete_id'])) {
    try {
        $stmt = $pdo->prepare("DELETE FROM supplier WHERE id = ? AND user_id = ? AND nama_supplier LIKE 'Test Supplier%'");
        $result = $stmt->execute([$_POST['delete_id'], $currentUser['id']]);
        if ($result) {
            echo "<script>alert('Test record deleted successfully'); window.location.reload();</script>";
        }
    } catch (PDOException $e) {
        echo "<script>alert('Error deleting record: " . addslashes($e->getMessage()) . "');</script>";
    }
}
?>
