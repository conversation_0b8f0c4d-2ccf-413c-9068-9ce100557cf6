<?php
/**
 * Get total income for the current user
 * @return float
 */
function getTotalPemasukan() {
    global $pdo;
    try {
        $stmt = $pdo->prepare("
            SELECT COALESCE(SUM(t.jumlah), 0) as total
            FROM transaksi t
            JOIN kategori k ON t.kategori_id = k.id
            WHERE t.user_id = ? AND k.tipe = 'pemasukan'
        ");
        $stmt->execute([$_SESSION['user_id']]);
        $result = $stmt->fetch();
        return $result['total'] ?? 0;
    } catch (PDOException $e) {
        error_log("Error getting total pemasukan: " . $e->getMessage());
        return 0;
    }
}

/**
 * Get total expenses for the current user
 * @return float
 */
function getTotalPengeluaran() {
    global $pdo;
    try {
        $stmt = $pdo->prepare("
            SELECT COALESCE(SUM(t.jumlah), 0) as total
            FROM transaksi t
            JOIN kategori k ON t.kategori_id = k.id
            WHERE t.user_id = ? AND k.tipe = 'pengeluaran'
        ");
        $stmt->execute([$_SESSION['user_id']]);
        $result = $stmt->fetch();
        return $result['total'] ?? 0;
    } catch (PDOException $e) {
        error_log("Error getting total pengeluaran: " . $e->getMessage());
        return 0;
    }
}

/**
 * Get recent transactions for the current user
 * @param int $limit Number of transactions to return
 * @return array
 */
function getRecentTransactions($limit = 5) {
    global $pdo;
    try {
        $stmt = $pdo->prepare("
            SELECT t.*, k.nama_kategori, k.tipe
            FROM transaksi t
            JOIN kategori k ON t.kategori_id = k.id
            WHERE t.user_id = ?
            ORDER BY t.tanggal DESC
            LIMIT ?
        ");
        $stmt->execute([$_SESSION['user_id'], $limit]);
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        error_log("Error getting recent transactions: " . $e->getMessage());
        return [];
    }
}

/**
 * Get last transactions for the current user
 * @param int $limit Number of transactions to return
 * @return array
 */
function getTransaksiTerakhir($limit = 5) {
    global $pdo;
    try {
        $stmt = $pdo->prepare("
            SELECT t.*, k.nama_kategori, k.tipe
            FROM transaksi t
            JOIN kategori k ON t.kategori_id = k.id
            WHERE t.user_id = ?
            ORDER BY t.tanggal DESC, t.created_at DESC
            LIMIT ?
        ");
        $stmt->execute([$_SESSION['user_id'], $limit]);
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        error_log("Error getting recent transactions: " . $e->getMessage());
        return [];
    }
}

/**
 * Get most used categories for the current user
 * @param int $limit Number of categories to return
 * @return array
 */
function getKategoriTerbanyak($limit = 5) {
    global $pdo;
    try {
        $stmt = $pdo->prepare("
            SELECT 
                k.nama_kategori,
                COUNT(t.id) as total_transaksi,
                COALESCE(SUM(t.jumlah), 0) as total_jumlah
            FROM kategori k
            LEFT JOIN transaksi t ON k.id = t.kategori_id AND t.user_id = ?
            WHERE k.user_id = ?
            GROUP BY k.id
            ORDER BY total_transaksi DESC
            LIMIT ?
        ");
        $stmt->execute([$_SESSION['user_id'], $_SESSION['user_id'], $limit]);
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        error_log("Error getting top categories: " . $e->getMessage());
        return [];
    }
}

/**
 * Get today's sales for the current user
 * @return float
 */
function getPenjualanHariIni() {
    global $pdo;
    try {
        $stmt = $pdo->prepare("
            SELECT COALESCE(SUM(jumlah * harga_jual), 0) as total
            FROM transaksi_produk
            WHERE user_id = ? AND DATE(tanggal) = CURDATE()
        ");
        $stmt->execute([$_SESSION['user_id']]);
        $result = $stmt->fetch();
        return $result['total'] ?? 0;
    } catch (PDOException $e) {
        error_log("Error getting today's sales: " . $e->getMessage());
        return 0;
    }
}

/**
 * Get this week's sales for the current user
 * @return float
 */
function getPenjualanMingguIni() {
    global $pdo;
    try {
        $stmt = $pdo->prepare("
            SELECT COALESCE(SUM(jumlah * harga_jual), 0) as total
            FROM transaksi_produk
            WHERE user_id = ? 
            AND YEARWEEK(tanggal) = YEARWEEK(CURDATE())
        ");
        $stmt->execute([$_SESSION['user_id']]);
        $result = $stmt->fetch();
        return $result['total'] ?? 0;
    } catch (PDOException $e) {
        error_log("Error getting this week's sales: " . $e->getMessage());
        return 0;
    }
}

/**
 * Get this month's sales for the current user
 * @return float
 */
function getPenjualanBulanIni() {
    global $pdo;
    try {
        $stmt = $pdo->prepare("
            SELECT COALESCE(SUM(jumlah * harga_jual), 0) as total
            FROM transaksi_produk
            WHERE user_id = ? 
            AND MONTH(tanggal) = MONTH(CURDATE())
            AND YEAR(tanggal) = YEAR(CURDATE())
        ");
        $stmt->execute([$_SESSION['user_id']]);
        $result = $stmt->fetch();
        return $result['total'] ?? 0;
    } catch (PDOException $e) {
        error_log("Error getting this month's sales: " . $e->getMessage());
        return 0;
    }
} 