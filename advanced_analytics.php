<?php
require_once 'includes/config/database.php';
require_once 'includes/helpers/function_check.php';
require_once 'includes/helpers/functions.php';
require_once 'includes/helpers/advanced_analytics.php';
require_once 'includes/helpers/multi_currency.php';

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isLoggedIn()) {
    setFlashMessage('danger', 'Silakan login terlebih dahulu');
    redirect('login.php');
}

$currentUser = getCurrentUser();
$currentPage = 'advanced_analytics';

// Create currency tables
createCurrencyTables();

// Get analysis period from request
$period = $_GET['period'] ?? 'last_3_months';
$currency = $_GET['currency'] ?? getUserCurrency($currentUser['id']);

// Generate financial insights
$insights = generateFinancialInsights($currentUser['id'], ['period' => $period]);

// Include header and sidebar
require_once 'includes/views/layouts/header.php';
require_once 'includes/views/layouts/sidebar.php';
?>

<div class="modern-layout">
    <div class="modern-container">
        <!-- Modern Page Header -->
        <div class="modern-page-header modern-flex modern-justify-between modern-items-center modern-mb-xl">
            <div>
                <h1 class="modern-page-title modern-mb-0">Advanced Analytics</h1>
                <p class="modern-page-subtitle">AI-powered financial insights dan prediksi</p>
            </div>
            <div class="modern-page-actions">
                <select class="modern-form-select" onchange="changePeriod(this.value)">
                    <option value="last_month" <?= $period === 'last_month' ? 'selected' : '' ?>>Bulan Lalu</option>
                    <option value="last_3_months" <?= $period === 'last_3_months' ? 'selected' : '' ?>>3 Bulan Terakhir</option>
                    <option value="last_6_months" <?= $period === 'last_6_months' ? 'selected' : '' ?>>6 Bulan Terakhir</option>
                    <option value="last_year" <?= $period === 'last_year' ? 'selected' : '' ?>>Tahun Lalu</option>
                </select>
            </div>
        </div>

        <!-- AI Insights Summary -->
        <div class="modern-card modern-mb-lg">
            <div class="modern-card-header">
                <h5 class="modern-card-title">
                    <i class="fas fa-brain modern-text-primary modern-mr-sm"></i>
                    AI Financial Insights
                </h5>
                <div class="modern-card-actions">
                    <span class="modern-badge modern-badge-success">
                        <i class="fas fa-robot"></i>
                        AI Powered
                    </span>
                </div>
            </div>
            <div class="modern-card-body">
                <div class="modern-insights-grid">
                    <?php if (!empty($insights['spending_patterns']['insights'])): ?>
                    <?php foreach ($insights['spending_patterns']['insights'] as $insight): ?>
                    <div class="modern-insight-card modern-insight-<?= $insight['severity'] ?>">
                        <div class="modern-insight-icon">
                            <i class="fas fa-chart-pie"></i>
                        </div>
                        <div class="modern-insight-content">
                            <h6 class="modern-insight-title">Pola Pengeluaran</h6>
                            <p class="modern-insight-message"><?= htmlspecialchars($insight['message']) ?></p>
                        </div>
                    </div>
                    <?php endforeach; ?>
                    <?php endif; ?>

                    <?php if (!empty($insights['income_trends']['insights'])): ?>
                    <?php foreach ($insights['income_trends']['insights'] as $insight): ?>
                    <div class="modern-insight-card modern-insight-<?= $insight['severity'] ?>">
                        <div class="modern-insight-icon">
                            <i class="fas fa-trending-up"></i>
                        </div>
                        <div class="modern-insight-content">
                            <h6 class="modern-insight-title">Tren Pendapatan</h6>
                            <p class="modern-insight-message"><?= htmlspecialchars($insight['message']) ?></p>
                        </div>
                    </div>
                    <?php endforeach; ?>
                    <?php endif; ?>

                    <?php if (!empty($insights['predictions']['insights'])): ?>
                    <?php foreach ($insights['predictions']['insights'] as $insight): ?>
                    <div class="modern-insight-card modern-insight-<?= $insight['severity'] ?>">
                        <div class="modern-insight-icon">
                            <i class="fas fa-crystal-ball"></i>
                        </div>
                        <div class="modern-insight-content">
                            <h6 class="modern-insight-title">Prediksi</h6>
                            <p class="modern-insight-message"><?= htmlspecialchars($insight['message']) ?></p>
                        </div>
                    </div>
                    <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <div class="modern-grid modern-grid-cols-2 modern-gap-lg modern-mb-lg">
            <!-- Spending Patterns -->
            <div class="modern-card">
                <div class="modern-card-header">
                    <h5 class="modern-card-title">
                        <i class="fas fa-chart-pie modern-text-warning modern-mr-sm"></i>
                        Pola Pengeluaran
                    </h5>
                </div>
                <div class="modern-card-body">
                    <?php if (!empty($insights['spending_patterns']['by_category'])): ?>
                    <div class="modern-chart-container">
                        <canvas id="spendingPatternsChart" width="400" height="300"></canvas>
                    </div>
                    <div class="modern-category-breakdown modern-mt-lg">
                        <?php 
                        $categories = $insights['spending_patterns']['by_category'];
                        arsort($categories);
                        $total = array_sum($categories);
                        $colors = ['#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF', '#FF9F40'];
                        $colorIndex = 0;
                        ?>
                        <?php foreach (array_slice($categories, 0, 5) as $category => $amount): ?>
                        <div class="modern-category-item">
                            <div class="modern-category-color" style="background-color: <?= $colors[$colorIndex % count($colors)] ?>"></div>
                            <div class="modern-category-info">
                                <div class="modern-category-name"><?= htmlspecialchars($category) ?></div>
                                <div class="modern-category-amount"><?= formatCurrency($amount, $currency) ?></div>
                            </div>
                            <div class="modern-category-percentage">
                                <?= number_format(($amount / $total) * 100, 1) ?>%
                            </div>
                        </div>
                        <?php $colorIndex++; ?>
                        <?php endforeach; ?>
                    </div>
                    <?php else: ?>
                    <div class="modern-empty-state">
                        <i class="fas fa-chart-pie"></i>
                        <p>Tidak ada data pengeluaran untuk periode ini</p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Income Trends -->
            <div class="modern-card">
                <div class="modern-card-header">
                    <h5 class="modern-card-title">
                        <i class="fas fa-trending-up modern-text-success modern-mr-sm"></i>
                        Tren Pendapatan
                    </h5>
                </div>
                <div class="modern-card-body">
                    <?php if (!empty($insights['income_trends']['monthly_income'])): ?>
                    <div class="modern-trend-stats modern-mb-lg">
                        <div class="modern-trend-stat">
                            <div class="modern-trend-label">Pertumbuhan</div>
                            <div class="modern-trend-value modern-trend-<?= $insights['income_trends']['growth_rate'] >= 0 ? 'positive' : 'negative' ?>">
                                <?= number_format($insights['income_trends']['growth_rate'], 1) ?>%
                            </div>
                        </div>
                        <div class="modern-trend-stat">
                            <div class="modern-trend-label">Stabilitas</div>
                            <div class="modern-trend-value">
                                <?= number_format($insights['income_trends']['stability_score'], 0) ?>/100
                            </div>
                        </div>
                    </div>
                    <div class="modern-chart-container">
                        <canvas id="incomeTrendsChart" width="400" height="200"></canvas>
                    </div>
                    <?php else: ?>
                    <div class="modern-empty-state">
                        <i class="fas fa-trending-up"></i>
                        <p>Tidak ada data pendapatan untuk periode ini</p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <div class="modern-grid modern-grid-cols-3 modern-gap-lg modern-mb-lg">
            <!-- Predictions -->
            <div class="modern-card">
                <div class="modern-card-header">
                    <h5 class="modern-card-title">
                        <i class="fas fa-crystal-ball modern-text-info modern-mr-sm"></i>
                        Prediksi Bulan Depan
                    </h5>
                </div>
                <div class="modern-card-body">
                    <div class="modern-prediction-items">
                        <div class="modern-prediction-item">
                            <div class="modern-prediction-label">Prediksi Pendapatan</div>
                            <div class="modern-prediction-value modern-text-success">
                                <?= formatCurrency($insights['predictions']['next_month_income'] ?? 0, $currency) ?>
                            </div>
                        </div>
                        <div class="modern-prediction-item">
                            <div class="modern-prediction-label">Prediksi Pengeluaran</div>
                            <div class="modern-prediction-value modern-text-danger">
                                <?= formatCurrency($insights['predictions']['next_month_expenses'] ?? 0, $currency) ?>
                            </div>
                        </div>
                        <div class="modern-prediction-item">
                            <div class="modern-prediction-label">Prediksi Cash Flow</div>
                            <?php 
                            $netFlow = ($insights['predictions']['next_month_income'] ?? 0) - ($insights['predictions']['next_month_expenses'] ?? 0);
                            ?>
                            <div class="modern-prediction-value <?= $netFlow >= 0 ? 'modern-text-success' : 'modern-text-danger' ?>">
                                <?= formatCurrency($netFlow, $currency) ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Risk Assessment -->
            <div class="modern-card">
                <div class="modern-card-header">
                    <h5 class="modern-card-title">
                        <i class="fas fa-shield-alt modern-text-warning modern-mr-sm"></i>
                        Penilaian Risiko
                    </h5>
                </div>
                <div class="modern-card-body">
                    <?php $riskAssessment = $insights['risk_assessment'] ?? ['overall_score' => 0, 'level' => 'low']; ?>
                    <div class="modern-risk-score">
                        <div class="modern-risk-circle modern-risk-<?= $riskAssessment['level'] ?>">
                            <div class="modern-risk-percentage"><?= number_format($riskAssessment['overall_score'], 0) ?></div>
                            <div class="modern-risk-label">Risk Score</div>
                        </div>
                        <div class="modern-risk-level">
                            Level: <span class="modern-risk-level-text modern-risk-<?= $riskAssessment['level'] ?>">
                                <?= ucfirst($riskAssessment['level']) ?>
                            </span>
                        </div>
                    </div>
                    <?php if (!empty($riskAssessment['factors'])): ?>
                    <div class="modern-risk-factors modern-mt-lg">
                        <h6>Faktor Risiko:</h6>
                        <?php foreach ($riskAssessment['factors'] as $factor): ?>
                        <div class="modern-risk-factor">
                            <div class="modern-risk-factor-name"><?= $factor['description'] ?></div>
                            <div class="modern-risk-factor-score"><?= number_format($factor['score'], 0) ?></div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Recommendations -->
            <div class="modern-card">
                <div class="modern-card-header">
                    <h5 class="modern-card-title">
                        <i class="fas fa-lightbulb modern-text-warning modern-mr-sm"></i>
                        Rekomendasi AI
                    </h5>
                </div>
                <div class="modern-card-body">
                    <?php if (!empty($insights['recommendations'])): ?>
                    <div class="modern-recommendations">
                        <?php foreach ($insights['recommendations'] as $recommendation): ?>
                        <div class="modern-recommendation-item modern-recommendation-<?= $recommendation['priority'] ?>">
                            <div class="modern-recommendation-icon">
                                <i class="fas fa-lightbulb"></i>
                            </div>
                            <div class="modern-recommendation-content">
                                <h6 class="modern-recommendation-title"><?= htmlspecialchars($recommendation['title']) ?></h6>
                                <p class="modern-recommendation-message"><?= htmlspecialchars($recommendation['message']) ?></p>
                                <div class="modern-recommendation-action">
                                    <small><strong>Action:</strong> <?= htmlspecialchars($recommendation['action']) ?></small>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                    <?php else: ?>
                    <div class="modern-empty-state">
                        <i class="fas fa-lightbulb"></i>
                        <p>Tidak ada rekomendasi saat ini</p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Anomalies Detection -->
        <?php if (!empty($insights['anomalies'])): ?>
        <div class="modern-card modern-mb-lg">
            <div class="modern-card-header">
                <h5 class="modern-card-title">
                    <i class="fas fa-exclamation-triangle modern-text-danger modern-mr-sm"></i>
                    Deteksi Anomali
                </h5>
                <div class="modern-card-actions">
                    <span class="modern-badge modern-badge-danger">
                        <?= count($insights['anomalies']) ?> anomali terdeteksi
                    </span>
                </div>
            </div>
            <div class="modern-card-body">
                <div class="modern-anomalies-list">
                    <?php foreach ($insights['anomalies'] as $anomaly): ?>
                    <div class="modern-anomaly-item modern-anomaly-<?= $anomaly['severity'] ?>">
                        <div class="modern-anomaly-icon">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="modern-anomaly-content">
                            <div class="modern-anomaly-message"><?= htmlspecialchars($anomaly['message']) ?></div>
                            <div class="modern-anomaly-meta">
                                <span class="modern-anomaly-type"><?= ucfirst(str_replace('_', ' ', $anomaly['type'])) ?></span>
                                <span class="modern-anomaly-date"><?= formatTanggal($anomaly['date']) ?></span>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>
</div>

<?php include 'includes/views/layouts/footer.php'; ?>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
function changePeriod(period) {
    window.location.href = '?period=' + period + '&currency=<?= $currency ?>';
}

// Spending Patterns Chart
<?php if (!empty($insights['spending_patterns']['by_category'])): ?>
const spendingCtx = document.getElementById('spendingPatternsChart').getContext('2d');
new Chart(spendingCtx, {
    type: 'doughnut',
    data: {
        labels: <?= json_encode(array_keys($insights['spending_patterns']['by_category'])) ?>,
        datasets: [{
            data: <?= json_encode(array_values($insights['spending_patterns']['by_category'])) ?>,
            backgroundColor: ['#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF', '#FF9F40']
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        }
    }
});
<?php endif; ?>

// Income Trends Chart
<?php if (!empty($insights['income_trends']['monthly_income'])): ?>
const incomeCtx = document.getElementById('incomeTrendsChart').getContext('2d');
new Chart(incomeCtx, {
    type: 'line',
    data: {
        labels: <?= json_encode(array_keys($insights['income_trends']['monthly_income'])) ?>,
        datasets: [{
            label: 'Pendapatan',
            data: <?= json_encode(array_values($insights['income_trends']['monthly_income'])) ?>,
            borderColor: '#28a745',
            backgroundColor: 'rgba(40, 167, 69, 0.1)',
            tension: 0.4,
            fill: true
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});
<?php endif; ?>
</script>

<style>
.modern-insights-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.modern-insight-card {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    padding: 20px;
    border-radius: 8px;
    border-left: 4px solid;
}

.modern-insight-high { border-left-color: #dc3545; background: #f8d7da; }
.modern-insight-medium { border-left-color: #ffc107; background: #fff3cd; }
.modern-insight-low { border-left-color: #17a2b8; background: #d1ecf1; }
.modern-insight-positive { border-left-color: #28a745; background: #d4edda; }

.modern-insight-icon {
    font-size: 24px;
    color: #6c757d;
}

.modern-insight-title {
    margin: 0 0 5px 0;
    font-weight: 600;
    font-size: 14px;
}

.modern-insight-message {
    margin: 0;
    font-size: 13px;
    line-height: 1.4;
}

.modern-category-breakdown {
    max-height: 200px;
    overflow-y: auto;
}

.modern-category-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px 0;
    border-bottom: 1px solid #eee;
}

.modern-category-color {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.modern-category-info {
    flex: 1;
}

.modern-category-name {
    font-weight: 500;
    font-size: 13px;
}

.modern-category-amount {
    font-size: 12px;
    color: #6c757d;
}

.modern-category-percentage {
    font-weight: 600;
    font-size: 12px;
}

.modern-trend-stats {
    display: flex;
    gap: 20px;
}

.modern-trend-stat {
    text-align: center;
}

.modern-trend-label {
    font-size: 12px;
    color: #6c757d;
    margin-bottom: 5px;
}

.modern-trend-value {
    font-size: 18px;
    font-weight: 600;
}

.modern-trend-positive { color: #28a745; }
.modern-trend-negative { color: #dc3545; }

.modern-prediction-items {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.modern-prediction-item {
    text-align: center;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
}

.modern-prediction-label {
    font-size: 12px;
    color: #6c757d;
    margin-bottom: 5px;
}

.modern-prediction-value {
    font-size: 16px;
    font-weight: 600;
}

.modern-risk-score {
    text-align: center;
}

.modern-risk-circle {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin: 0 auto 15px;
    border: 4px solid;
}

.modern-risk-low { border-color: #28a745; color: #28a745; }
.modern-risk-medium { border-color: #ffc107; color: #ffc107; }
.modern-risk-high { border-color: #dc3545; color: #dc3545; }

.modern-risk-percentage {
    font-size: 18px;
    font-weight: 600;
}

.modern-risk-label {
    font-size: 10px;
}

.modern-risk-factors {
    margin-top: 15px;
}

.modern-risk-factor {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 5px 0;
    font-size: 12px;
}

.modern-recommendations {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.modern-recommendation-item {
    display: flex;
    gap: 10px;
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid;
}

.modern-recommendation-high { border-left-color: #dc3545; background: #f8d7da; }
.modern-recommendation-medium { border-left-color: #ffc107; background: #fff3cd; }
.modern-recommendation-low { border-left-color: #17a2b8; background: #d1ecf1; }

.modern-recommendation-icon {
    font-size: 16px;
    color: #6c757d;
}

.modern-recommendation-title {
    margin: 0 0 5px 0;
    font-size: 13px;
    font-weight: 600;
}

.modern-recommendation-message {
    margin: 0 0 5px 0;
    font-size: 12px;
    line-height: 1.4;
}

.modern-recommendation-action {
    font-size: 11px;
    color: #6c757d;
}

.modern-anomalies-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.modern-anomaly-item {
    display: flex;
    gap: 10px;
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid;
}

.modern-anomaly-high { border-left-color: #dc3545; background: #f8d7da; }
.modern-anomaly-medium { border-left-color: #ffc107; background: #fff3cd; }

.modern-anomaly-icon {
    font-size: 16px;
    color: #6c757d;
}

.modern-anomaly-message {
    font-weight: 500;
    margin-bottom: 5px;
}

.modern-anomaly-meta {
    font-size: 12px;
    color: #6c757d;
}

.modern-anomaly-type {
    margin-right: 10px;
}
</style>
