<?php
require_once 'includes/config/database.php';
require_once 'includes/helpers/auth_helper.php';

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isLoggedIn()) {
    die('Please login first');
}

$currentUser = getCurrentUser();

?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Supplier Table - KeuanganKu</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="card shadow">
                    <div class="card-header bg-info text-white">
                        <h4 class="mb-0">
                            <i class="fas fa-bug me-2"></i>Debug Supplier Table
                        </h4>
                    </div>
                    <div class="card-body">
                        
                        <!-- Table Structure -->
                        <h5>Table Structure:</h5>
                        <div class="table-responsive mb-4">
                            <table class="table table-bordered">
                                <thead class="table-dark">
                                    <tr>
                                        <th>Field</th>
                                        <th>Type</th>
                                        <th>Null</th>
                                        <th>Key</th>
                                        <th>Default</th>
                                        <th>Extra</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    try {
                                        $stmt = $pdo->query("DESCRIBE supplier");
                                        $columns = $stmt->fetchAll();
                                        foreach ($columns as $column) {
                                            echo "<tr>";
                                            echo "<td><strong>" . htmlspecialchars($column['Field']) . "</strong></td>";
                                            echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
                                            echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
                                            echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
                                            echo "<td>" . htmlspecialchars($column['Default']) . "</td>";
                                            echo "<td>" . htmlspecialchars($column['Extra']) . "</td>";
                                            echo "</tr>";
                                        }
                                    } catch (PDOException $e) {
                                        echo "<tr><td colspan='6' class='text-danger'>Error: " . htmlspecialchars($e->getMessage()) . "</td></tr>";
                                    }
                                    ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- Check Status Column -->
                        <h5>Status Column Check:</h5>
                        <div class="alert alert-info">
                            <?php
                            try {
                                $stmt = $pdo->query("SHOW COLUMNS FROM supplier LIKE 'status'");
                                if ($stmt->rowCount() > 0) {
                                    echo "<i class='fas fa-check-circle text-success'></i> Status column EXISTS";
                                    $statusColumn = $stmt->fetch();
                                    echo "<br><small>Type: " . htmlspecialchars($statusColumn['Type']) . "</small>";
                                } else {
                                    echo "<i class='fas fa-times-circle text-danger'></i> Status column DOES NOT EXIST";
                                }
                            } catch (PDOException $e) {
                                echo "<i class='fas fa-exclamation-triangle text-warning'></i> Error checking status column: " . htmlspecialchars($e->getMessage());
                            }
                            ?>
                        </div>

                        <!-- Sample Data -->
                        <h5>Sample Data (First 5 records):</h5>
                        <div class="table-responsive mb-4">
                            <table class="table table-striped">
                                <thead class="table-light">
                                    <tr>
                                        <th>ID</th>
                                        <th>User ID</th>
                                        <th>Nama Supplier</th>
                                        <th>Kontak</th>
                                        <th>Email</th>
                                        <th>Status</th>
                                        <th>Created At</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    try {
                                        $stmt = $pdo->prepare("SELECT * FROM supplier WHERE user_id = ? LIMIT 5");
                                        $stmt->execute([$currentUser['id']]);
                                        $suppliers = $stmt->fetchAll();
                                        
                                        if (empty($suppliers)) {
                                            echo "<tr><td colspan='7' class='text-center text-muted'>No data found</td></tr>";
                                        } else {
                                            foreach ($suppliers as $supplier) {
                                                echo "<tr>";
                                                echo "<td>" . htmlspecialchars($supplier['id']) . "</td>";
                                                echo "<td>" . htmlspecialchars($supplier['user_id']) . "</td>";
                                                echo "<td>" . htmlspecialchars($supplier['nama_supplier']) . "</td>";
                                                echo "<td>" . htmlspecialchars($supplier['kontak'] ?? '-') . "</td>";
                                                echo "<td>" . htmlspecialchars($supplier['email'] ?? '-') . "</td>";
                                                echo "<td>" . htmlspecialchars($supplier['status'] ?? 'N/A') . "</td>";
                                                echo "<td>" . htmlspecialchars($supplier['created_at'] ?? '-') . "</td>";
                                                echo "</tr>";
                                            }
                                        }
                                    } catch (PDOException $e) {
                                        echo "<tr><td colspan='7' class='text-danger'>Error: " . htmlspecialchars($e->getMessage()) . "</td></tr>";
                                    }
                                    ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- Test Insert -->
                        <h5>Test Insert:</h5>
                        <div class="alert alert-warning">
                            <?php
                            if (isset($_POST['test_insert'])) {
                                try {
                                    // Check if status column exists
                                    $stmt = $pdo->query("SHOW COLUMNS FROM supplier LIKE 'status'");
                                    $statusExists = $stmt->rowCount() > 0;
                                    
                                    if ($statusExists) {
                                        $stmt = $pdo->prepare("INSERT INTO supplier (user_id, nama_supplier, kontak, email, alamat, keterangan, status) VALUES (?, ?, ?, ?, ?, ?, ?)");
                                        $result = $stmt->execute([
                                            $currentUser['id'],
                                            'Test Supplier ' . date('Y-m-d H:i:s'),
                                            '081234567890',
                                            '<EMAIL>',
                                            'Test Address',
                                            'Test Description',
                                            'aktif'
                                        ]);
                                    } else {
                                        $stmt = $pdo->prepare("INSERT INTO supplier (user_id, nama_supplier, kontak, email, alamat, keterangan) VALUES (?, ?, ?, ?, ?, ?)");
                                        $result = $stmt->execute([
                                            $currentUser['id'],
                                            'Test Supplier ' . date('Y-m-d H:i:s'),
                                            '081234567890',
                                            '<EMAIL>',
                                            'Test Address',
                                            'Test Description'
                                        ]);
                                    }
                                    
                                    if ($result) {
                                        echo "<i class='fas fa-check-circle text-success'></i> Test insert SUCCESSFUL";
                                    } else {
                                        echo "<i class='fas fa-times-circle text-danger'></i> Test insert FAILED";
                                    }
                                } catch (PDOException $e) {
                                    echo "<i class='fas fa-exclamation-triangle text-danger'></i> Test insert ERROR: " . htmlspecialchars($e->getMessage());
                                }
                            }
                            ?>
                        </div>

                        <div class="text-center">
                            <form method="POST" class="d-inline">
                                <button type="submit" name="test_insert" class="btn btn-warning me-2">
                                    <i class="fas fa-flask me-2"></i>Test Insert
                                </button>
                            </form>
                            <a href="/keuangan/setup_database.php" class="btn btn-primary me-2">
                                <i class="fas fa-database me-2"></i>Setup Database
                            </a>
                            <a href="/keuangan/supplier.php" class="btn btn-success">
                                <i class="fas fa-truck me-2"></i>Back to Supplier
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
