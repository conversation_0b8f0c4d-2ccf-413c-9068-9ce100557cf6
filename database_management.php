<?php
require_once 'includes/config/database.php';
require_once 'includes/helpers/functions.php';

// Check admin access
$currentUser = getCurrentUser();
if (!$currentUser || $currentUser['role'] !== 'admin') {
    setFlashMessage('danger', 'Akses ditolak! Anda tidak memiliki izin untuk mengakses halaman ini.');
    redirect('dashboard.php');
}

$pageTitle = 'Database Management Dashboard';

// Handle actions
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    try {
        switch ($_POST['action']) {
            case 'run_sql_file':
                $sqlFile = $_POST['sql_file'] ?? '';
                if (file_exists($sqlFile)) {
                    $sql = file_get_contents($sqlFile);
                    $statements = array_filter(array_map('trim', explode(';', $sql)));

                    $executed = 0;
                    foreach ($statements as $statement) {
                        if (!empty($statement) && !preg_match('/^\s*--/', $statement)) {
                            $pdo->exec($statement);
                            $executed++;
                        }
                    }

                    setFlashMessage('success', "Successfully executed $executed SQL statements from " . basename($sqlFile));
                } else {
                    setFlashMessage('danger', 'SQL file not found: ' . $sqlFile);
                }
                break;

            case 'check_tables':
                $requiredTables = [
                    'system_settings', 'custom_themes', 'custom_menus', 'layout_components',
                    'custom_css', 'page_templates', 'customization_presets', 'user_customizations',
                    'database_versions', 'database_migrations', 'system_info'
                ];

                $missingTables = [];
                foreach ($requiredTables as $table) {
                    $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
                    if ($stmt->rowCount() == 0) {
                        $missingTables[] = $table;
                    }
                }

                if (empty($missingTables)) {
                    setFlashMessage('success', 'All required tables exist in the database');
                } else {
                    setFlashMessage('warning', 'Missing tables: ' . implode(', ', $missingTables));
                }
                break;

            case 'backup_database':
                // Simple backup functionality
                $backupDir = 'database/backups';
                if (!is_dir($backupDir)) {
                    mkdir($backupDir, 0755, true);
                }

                $backupFile = $backupDir . '/backup_' . date('Y-m-d_H-i-s') . '.sql';
                $command = "mysqldump -u root -proot keuangan > $backupFile";

                exec($command, $output, $returnCode);

                if ($returnCode === 0) {
                    setFlashMessage('success', 'Database backup created: ' . basename($backupFile));
                } else {
                    setFlashMessage('danger', 'Failed to create database backup');
                }
                break;
        }
        redirect('database_management.php');
    } catch (Exception $e) {
        setFlashMessage('danger', 'Error: ' . $e->getMessage());
    }
}

// Get database info
$dbInfo = [];
try {
    $stmt = $pdo->query("SELECT DATABASE() as db_name");
    $dbInfo = $stmt->fetch();

    $stmt = $pdo->query("SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE()");
    $dbInfo['table_count'] = $stmt->fetchColumn();

    $stmt = $pdo->query("SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS db_size FROM information_schema.tables WHERE table_schema = DATABASE()");
    $dbInfo['size_mb'] = $stmt->fetchColumn();
} catch (Exception $e) {
    $dbInfo = ['db_name' => 'Unknown', 'table_count' => 0, 'size_mb' => 0];
}

// Check file status
$files = [
    'database/create_all_tables.sql' => [
        'name' => 'Comprehensive SQL File',
        'description' => 'Creates all required tables for customization system',
        'type' => 'sql'
    ],
    'create_tables_direct.php' => [
        'name' => 'Direct Table Creation Tool',
        'description' => 'PHP tool for creating tables directly',
        'type' => 'php'
    ],
    'fix_database.php' => [
        'name' => 'Database Repair Tool',
        'description' => 'Comprehensive database repair and fix tool',
        'type' => 'php'
    ],
    'update_database.php' => [
        'name' => 'Database Update System',
        'description' => 'Migration and update management system',
        'type' => 'php'
    ],
    'menu_permissions_advanced.php' => [
        'name' => 'Advanced Menu Permissions',
        'description' => 'Per-user menu permission management',
        'type' => 'php'
    ]
];

include 'includes/views/layouts/header.php';
?>

<div class="container-fluid px-4 py-3">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="mb-1">🗄️ Database Management Dashboard</h2>
                    <p class="text-muted mb-0">Manage database tools, files, and operations</p>
                </div>
                <div class="btn-group">
                    <a href="/keuangan/dashboard.php" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
                    </a>
                    <button type="button" class="btn btn-success" onclick="checkAllTables()">
                        <i class="fas fa-check-circle me-1"></i>Check Tables
                    </button>
                </div>
            </div>

            <!-- Database Info Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body text-center">
                            <i class="fas fa-database fa-2x mb-2"></i>
                            <h5><?= htmlspecialchars($dbInfo['db_name']) ?></h5>
                            <small>Database Name</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <i class="fas fa-table fa-2x mb-2"></i>
                            <h5><?= $dbInfo['table_count'] ?></h5>
                            <small>Total Tables</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body text-center">
                            <i class="fas fa-hdd fa-2x mb-2"></i>
                            <h5><?= $dbInfo['size_mb'] ?> MB</h5>
                            <small>Database Size</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body text-center">
                            <i class="fas fa-tools fa-2x mb-2"></i>
                            <h5><?= count($files) ?></h5>
                            <small>Management Tools</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Database Tools -->
            <div class="row">
                <!-- SQL Files -->
                <div class="col-lg-6 mb-4">
                    <div class="card h-100">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-file-code me-2"></i>SQL Files
                            </h5>
                        </div>
                        <div class="card-body">
                            <?php foreach ($files as $filePath => $fileInfo): ?>
                                <?php if ($fileInfo['type'] === 'sql'): ?>
                                    <div class="border rounded p-3 mb-3">
                                        <div class="d-flex justify-content-between align-items-start">
                                            <div>
                                                <h6 class="mb-1">
                                                    <?php if (file_exists($filePath)): ?>
                                                        <i class="fas fa-check-circle text-success me-2"></i>
                                                    <?php else: ?>
                                                        <i class="fas fa-times-circle text-danger me-2"></i>
                                                    <?php endif; ?>
                                                    <?= $fileInfo['name'] ?>
                                                </h6>
                                                <p class="text-muted small mb-2"><?= $fileInfo['description'] ?></p>
                                                <code class="small"><?= $filePath ?></code>
                                            </div>
                                            <div class="btn-group-vertical">
                                                <?php if (file_exists($filePath)): ?>
                                                    <button type="button" class="btn btn-sm btn-primary" onclick="runSQLFile('<?= $filePath ?>')">
                                                        <i class="fas fa-play me-1"></i>Execute
                                                    </button>
                                                    <button type="button" class="btn btn-sm btn-outline-info" onclick="viewFile('<?= $filePath ?>')">
                                                        <i class="fas fa-eye me-1"></i>View
                                                    </button>
                                                <?php else: ?>
                                                    <button type="button" class="btn btn-sm btn-outline-danger" disabled>
                                                        <i class="fas fa-exclamation-triangle me-1"></i>Missing
                                                    </button>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>

                <!-- PHP Tools -->
                <div class="col-lg-6 mb-4">
                    <div class="card h-100">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-code me-2"></i>PHP Tools
                            </h5>
                        </div>
                        <div class="card-body">
                            <?php foreach ($files as $filePath => $fileInfo): ?>
                                <?php if ($fileInfo['type'] === 'php'): ?>
                                    <div class="border rounded p-3 mb-3">
                                        <div class="d-flex justify-content-between align-items-start">
                                            <div>
                                                <h6 class="mb-1">
                                                    <?php if (file_exists($filePath)): ?>
                                                        <i class="fas fa-check-circle text-success me-2"></i>
                                                    <?php else: ?>
                                                        <i class="fas fa-times-circle text-danger me-2"></i>
                                                    <?php endif; ?>
                                                    <?= $fileInfo['name'] ?>
                                                </h6>
                                                <p class="text-muted small mb-2"><?= $fileInfo['description'] ?></p>
                                                <code class="small"><?= $filePath ?></code>
                                            </div>
                                            <div class="btn-group-vertical">
                                                <?php if (file_exists($filePath)): ?>
                                                    <a href="<?= $filePath ?>" class="btn btn-sm btn-success" target="_blank">
                                                        <i class="fas fa-external-link-alt me-1"></i>Open
                                                    </a>
                                                    <button type="button" class="btn btn-sm btn-outline-info" onclick="viewFile('<?= $filePath ?>')">
                                                        <i class="fas fa-eye me-1"></i>View
                                                    </button>
                                                <?php else: ?>
                                                    <button type="button" class="btn btn-sm btn-outline-danger" disabled>
                                                        <i class="fas fa-exclamation-triangle me-1"></i>Missing
                                                    </button>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header bg-warning text-dark">
                            <h5 class="mb-0">
                                <i class="fas fa-bolt me-2"></i>Quick Actions
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3 mb-3">
                                    <button type="button" class="btn btn-primary w-100" onclick="checkAllTables()">
                                        <i class="fas fa-check-circle me-2"></i>
                                        Check All Tables
                                    </button>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <button type="button" class="btn btn-success w-100" onclick="runSQLFile('database/create_all_tables.sql')">
                                        <i class="fas fa-plus-circle me-2"></i>
                                        Create All Tables
                                    </button>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <button type="button" class="btn btn-info w-100" onclick="backupDatabase()">
                                        <i class="fas fa-download me-2"></i>
                                        Backup Database
                                    </button>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <a href="update_database.php" class="btn btn-warning w-100">
                                        <i class="fas fa-sync-alt me-2"></i>
                                        Update Database
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- File Viewer Modal -->
<div class="modal fade" id="fileViewerModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="fileViewerTitle">File Viewer</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <div>
                        <span class="badge bg-secondary" id="fileType">File Type</span>
                        <span class="badge bg-info" id="fileSize">File Size</span>
                    </div>
                    <div>
                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="copyFileContent()">
                            <i class="fas fa-copy me-1"></i>Copy Content
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadFile()">
                            <i class="fas fa-download me-1"></i>Download
                        </button>
                    </div>
                </div>
                <div class="border rounded" style="max-height: 500px; overflow-y: auto;">
                    <pre id="fileContent" class="p-3 mb-0" style="background: #f8f9fa;"></pre>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Confirmation Modal -->
<div class="modal fade" id="confirmModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="confirmTitle">Confirm Action</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p id="confirmMessage">Are you sure you want to perform this action?</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="confirmButton">Confirm</button>
            </div>
        </div>
    </div>
</div>

<!-- Table Status Modal -->
<div class="modal fade" id="tableStatusModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-table me-2"></i>Database Table Status
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="tableStatusContent">
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Checking table status...</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-success" onclick="createMissingTables()">
                    <i class="fas fa-plus me-1"></i>Create Missing Tables
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.border:hover {
    border-color: #007bff !important;
}

#fileContent {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 12px;
    line-height: 1.4;
}

.btn-group-vertical .btn {
    margin-bottom: 2px;
}

.btn-group-vertical .btn:last-child {
    margin-bottom: 0;
}

pre {
    white-space: pre-wrap;
    word-wrap: break-word;
}
</style>

<script>
let currentFile = '';

// Run SQL File
function runSQLFile(filePath) {
    const confirmModal = new bootstrap.Modal(document.getElementById('confirmModal'));
    document.getElementById('confirmTitle').textContent = 'Execute SQL File';
    document.getElementById('confirmMessage').textContent = `Are you sure you want to execute ${filePath}? This will run all SQL statements in the file.`;

    document.getElementById('confirmButton').onclick = function() {
        const form = document.createElement('form');
        form.method = 'POST';
        form.style.display = 'none';

        const actionInput = document.createElement('input');
        actionInput.type = 'hidden';
        actionInput.name = 'action';
        actionInput.value = 'run_sql_file';
        form.appendChild(actionInput);

        const fileInput = document.createElement('input');
        fileInput.type = 'hidden';
        fileInput.name = 'sql_file';
        fileInput.value = filePath;
        form.appendChild(fileInput);

        document.body.appendChild(form);
        form.submit();

        confirmModal.hide();
    };

    confirmModal.show();
}

// View File
function viewFile(filePath) {
    currentFile = filePath;
    const modal = new bootstrap.Modal(document.getElementById('fileViewerModal'));

    document.getElementById('fileViewerTitle').textContent = `Viewing: ${filePath}`;
    document.getElementById('fileType').textContent = filePath.split('.').pop().toUpperCase();
    document.getElementById('fileContent').textContent = 'Loading...';

    // Fetch file content
    fetch(`view_file.php?file=${encodeURIComponent(filePath)}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('fileContent').textContent = data.content;
                document.getElementById('fileSize').textContent = formatFileSize(data.size);
            } else {
                document.getElementById('fileContent').textContent = 'Error: ' + data.message;
            }
        })
        .catch(error => {
            document.getElementById('fileContent').textContent = 'Error loading file: ' + error.message;
        });

    modal.show();
}

// Check All Tables
function checkAllTables() {
    const modal = new bootstrap.Modal(document.getElementById('tableStatusModal'));
    modal.show();

    // Fetch table status
    fetch('check_tables.php')
        .then(response => response.json())
        .then(data => {
            let html = '<div class="row">';

            data.tables.forEach(table => {
                const statusClass = table.exists ? 'success' : 'danger';
                const statusIcon = table.exists ? 'check-circle' : 'times-circle';
                const statusText = table.exists ? 'Exists' : 'Missing';

                html += `
                    <div class="col-md-6 mb-2">
                        <div class="d-flex justify-content-between align-items-center p-2 border rounded">
                            <span>${table.name}</span>
                            <span class="badge bg-${statusClass}">
                                <i class="fas fa-${statusIcon} me-1"></i>${statusText}
                            </span>
                        </div>
                    </div>
                `;
            });

            html += '</div>';

            if (data.missing_count > 0) {
                html += `
                    <div class="alert alert-warning mt-3">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>${data.missing_count} tables are missing.</strong>
                        Click "Create Missing Tables" to create them.
                    </div>
                `;
            } else {
                html += `
                    <div class="alert alert-success mt-3">
                        <i class="fas fa-check-circle me-2"></i>
                        <strong>All required tables exist!</strong> Your database is properly configured.
                    </div>
                `;
            }

            document.getElementById('tableStatusContent').innerHTML = html;
        })
        .catch(error => {
            document.getElementById('tableStatusContent').innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    Error checking table status: ${error.message}
                </div>
            `;
        });
}

// Create Missing Tables
function createMissingTables() {
    runSQLFile('database/create_all_tables.sql');
}

// Backup Database
function backupDatabase() {
    const confirmModal = new bootstrap.Modal(document.getElementById('confirmModal'));
    document.getElementById('confirmTitle').textContent = 'Backup Database';
    document.getElementById('confirmMessage').textContent = 'This will create a backup of the entire database. Continue?';

    document.getElementById('confirmButton').onclick = function() {
        const form = document.createElement('form');
        form.method = 'POST';
        form.style.display = 'none';

        const actionInput = document.createElement('input');
        actionInput.type = 'hidden';
        actionInput.name = 'action';
        actionInput.value = 'backup_database';
        form.appendChild(actionInput);

        document.body.appendChild(form);
        form.submit();

        confirmModal.hide();
    };

    confirmModal.show();
}

// Copy File Content
function copyFileContent() {
    const content = document.getElementById('fileContent').textContent;
    navigator.clipboard.writeText(content).then(() => {
        showToast('File content copied to clipboard!', 'success');
    }).catch(err => {
        showToast('Failed to copy content', 'danger');
    });
}

// Download File
function downloadFile() {
    if (currentFile) {
        const link = document.createElement('a');
        link.href = `download_file.php?file=${encodeURIComponent(currentFile)}`;
        link.download = currentFile.split('/').pop();
        link.click();
    }
}

// Format File Size
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Show Toast
function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    toast.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(toast);

    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 3000);
}

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    // Auto-refresh database info every 30 seconds
    setInterval(() => {
        location.reload();
    }, 30000);
});
</script>

<?php include 'includes/views/layouts/footer.php'; ?>
