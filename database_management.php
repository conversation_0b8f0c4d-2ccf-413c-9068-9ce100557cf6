<?php
require_once 'includes/config/database.php';
require_once 'includes/helpers/function_check.php';
require_once 'includes/helpers/functions.php';
require_once 'includes/helpers/backup_system.php';

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in and is admin
if (!isLoggedIn()) {
    setFlashMessage('danger', 'Silakan login terlebih dahulu');
    redirect('login.php');
}

$currentUser = getCurrentUser();
if ($currentUser['role'] !== 'admin') {
    setFlashMessage('danger', 'Akses ditolak. Hanya admin yang dapat mengakses halaman ini.');
    redirect('dashboard.php');
}

$currentPage = 'database_management';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'optimize_database':
                $result = optimizeDatabase();
                if ($result['success']) {
                    setFlashMessage('success', $result['message']);
                } else {
                    setFlashMessage('danger', $result['message']);
                }
                break;
                
            case 'repair_tables':
                $result = repairDatabaseTables();
                if ($result['success']) {
                    setFlashMessage('success', $result['message']);
                } else {
                    setFlashMessage('danger', $result['message']);
                }
                break;
                
            case 'analyze_tables':
                $result = analyzeDatabaseTables();
                if ($result['success']) {
                    setFlashMessage('success', $result['message']);
                } else {
                    setFlashMessage('danger', $result['message']);
                }
                break;
                
            case 'check_tables':
                $result = checkDatabaseTables();
                if ($result['success']) {
                    setFlashMessage('success', $result['message']);
                } else {
                    setFlashMessage('danger', $result['message']);
                }
                break;
        }
        redirect('database_management.php');
    }
}

// Get database statistics
$dbStats = getDatabaseStatistics();
$tableInfo = getTableInformation();

// Include header and sidebar
require_once 'includes/views/layouts/header.php';
require_once 'includes/views/layouts/sidebar.php';

/**
 * Get database statistics
 */
function getDatabaseStatistics() {
    global $pdo;
    
    try {
        $stats = [];
        
        // Database size
        $stmt = $pdo->query("
            SELECT 
                ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS db_size_mb,
                COUNT(*) AS total_tables
            FROM information_schema.tables 
            WHERE table_schema = DATABASE()
        ");
        $result = $stmt->fetch();
        $stats['size_mb'] = $result['db_size_mb'] ?? 0;
        $stats['total_tables'] = $result['total_tables'] ?? 0;
        
        // Total records
        $stmt = $pdo->query("
            SELECT SUM(table_rows) AS total_records
            FROM information_schema.tables 
            WHERE table_schema = DATABASE()
        ");
        $stats['total_records'] = $stmt->fetchColumn() ?? 0;
        
        // Database version
        $stats['version'] = $pdo->query('SELECT VERSION()')->fetchColumn();
        
        // Character set
        $stmt = $pdo->query("SELECT DEFAULT_CHARACTER_SET_NAME FROM information_schema.SCHEMATA WHERE SCHEMA_NAME = DATABASE()");
        $stats['charset'] = $stmt->fetchColumn();
        
        return $stats;
        
    } catch (Exception $e) {
        error_log("Get database statistics error: " . $e->getMessage());
        return [
            'size_mb' => 0,
            'total_tables' => 0,
            'total_records' => 0,
            'version' => 'Unknown',
            'charset' => 'Unknown'
        ];
    }
}

/**
 * Get table information
 */
function getTableInformation() {
    global $pdo;
    
    try {
        $stmt = $pdo->query("
            SELECT 
                table_name,
                table_rows,
                ROUND((data_length + index_length) / 1024 / 1024, 2) AS size_mb,
                ROUND(data_length / 1024 / 1024, 2) AS data_mb,
                ROUND(index_length / 1024 / 1024, 2) AS index_mb,
                engine,
                table_collation,
                create_time,
                update_time
            FROM information_schema.tables 
            WHERE table_schema = DATABASE()
            ORDER BY (data_length + index_length) DESC
        ");
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
        
    } catch (Exception $e) {
        error_log("Get table information error: " . $e->getMessage());
        return [];
    }
}

/**
 * Optimize database
 */
function optimizeDatabase() {
    global $pdo;
    
    try {
        $optimized = 0;
        $errors = [];
        
        // Get all tables
        $stmt = $pdo->query("SHOW TABLES");
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        foreach ($tables as $table) {
            try {
                $pdo->exec("OPTIMIZE TABLE `$table`");
                $optimized++;
            } catch (Exception $e) {
                $errors[] = "Table $table: " . $e->getMessage();
            }
        }
        
        logSystemEvent("Database optimized", 'info', [
            'tables_optimized' => $optimized,
            'errors' => count($errors)
        ]);
        
        if (empty($errors)) {
            return [
                'success' => true,
                'message' => "Database berhasil dioptimasi. $optimized tabel dioptimasi."
            ];
        } else {
            return [
                'success' => false,
                'message' => "Optimasi selesai dengan beberapa error: " . implode(', ', $errors)
            ];
        }
        
    } catch (Exception $e) {
        error_log("Optimize database error: " . $e->getMessage());
        return [
            'success' => false,
            'message' => 'Gagal mengoptimasi database: ' . $e->getMessage()
        ];
    }
}

/**
 * Repair database tables
 */
function repairDatabaseTables() {
    global $pdo;
    
    try {
        $repaired = 0;
        $errors = [];
        
        // Get all tables
        $stmt = $pdo->query("SHOW TABLES");
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        foreach ($tables as $table) {
            try {
                $pdo->exec("REPAIR TABLE `$table`");
                $repaired++;
            } catch (Exception $e) {
                $errors[] = "Table $table: " . $e->getMessage();
            }
        }
        
        logSystemEvent("Database tables repaired", 'info', [
            'tables_repaired' => $repaired,
            'errors' => count($errors)
        ]);
        
        if (empty($errors)) {
            return [
                'success' => true,
                'message' => "Tabel database berhasil diperbaiki. $repaired tabel diperbaiki."
            ];
        } else {
            return [
                'success' => false,
                'message' => "Perbaikan selesai dengan beberapa error: " . implode(', ', $errors)
            ];
        }
        
    } catch (Exception $e) {
        error_log("Repair database tables error: " . $e->getMessage());
        return [
            'success' => false,
            'message' => 'Gagal memperbaiki tabel database: ' . $e->getMessage()
        ];
    }
}

/**
 * Analyze database tables
 */
function analyzeDatabaseTables() {
    global $pdo;
    
    try {
        $analyzed = 0;
        $errors = [];
        
        // Get all tables
        $stmt = $pdo->query("SHOW TABLES");
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        foreach ($tables as $table) {
            try {
                $pdo->exec("ANALYZE TABLE `$table`");
                $analyzed++;
            } catch (Exception $e) {
                $errors[] = "Table $table: " . $e->getMessage();
            }
        }
        
        logSystemEvent("Database tables analyzed", 'info', [
            'tables_analyzed' => $analyzed,
            'errors' => count($errors)
        ]);
        
        if (empty($errors)) {
            return [
                'success' => true,
                'message' => "Tabel database berhasil dianalisis. $analyzed tabel dianalisis."
            ];
        } else {
            return [
                'success' => false,
                'message' => "Analisis selesai dengan beberapa error: " . implode(', ', $errors)
            ];
        }
        
    } catch (Exception $e) {
        error_log("Analyze database tables error: " . $e->getMessage());
        return [
            'success' => false,
            'message' => 'Gagal menganalisis tabel database: ' . $e->getMessage()
        ];
    }
}

/**
 * Check database tables
 */
function checkDatabaseTables() {
    global $pdo;
    
    try {
        $checked = 0;
        $errors = [];
        $issues = [];
        
        // Get all tables
        $stmt = $pdo->query("SHOW TABLES");
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        foreach ($tables as $table) {
            try {
                $stmt = $pdo->query("CHECK TABLE `$table`");
                $result = $stmt->fetch();
                
                if ($result['Msg_text'] !== 'OK') {
                    $issues[] = "Table $table: " . $result['Msg_text'];
                }
                
                $checked++;
            } catch (Exception $e) {
                $errors[] = "Table $table: " . $e->getMessage();
            }
        }
        
        logSystemEvent("Database tables checked", 'info', [
            'tables_checked' => $checked,
            'issues_found' => count($issues),
            'errors' => count($errors)
        ]);
        
        if (empty($errors) && empty($issues)) {
            return [
                'success' => true,
                'message' => "Semua tabel database dalam kondisi baik. $checked tabel diperiksa."
            ];
        } else {
            $message = "Pemeriksaan selesai. ";
            if (!empty($issues)) {
                $message .= count($issues) . " masalah ditemukan. ";
            }
            if (!empty($errors)) {
                $message .= count($errors) . " error terjadi.";
            }
            
            return [
                'success' => false,
                'message' => $message
            ];
        }
        
    } catch (Exception $e) {
        error_log("Check database tables error: " . $e->getMessage());
        return [
            'success' => false,
            'message' => 'Gagal memeriksa tabel database: ' . $e->getMessage()
        ];
    }
}
?>

<div class="modern-layout">
    <div class="modern-container">
        <!-- Modern Page Header -->
        <div class="modern-page-header modern-flex modern-justify-between modern-items-center modern-mb-xl">
            <div>
                <h1 class="modern-page-title modern-mb-0">Database Management</h1>
                <p class="modern-page-subtitle">Kelola dan optimasi database sistem</p>
            </div>
            <div class="modern-page-actions">
                <a href="backup_management.php" class="modern-btn modern-btn-primary">
                    <i class="fas fa-database"></i>
                    Backup Management
                </a>
            </div>
        </div>

        <!-- Flash Messages -->
        <?php if ($flash = getFlashMessage()): ?>
        <div class="modern-alert modern-alert-<?= $flash['type'] ?> modern-mb-lg">
            <div class="modern-alert-content">
                <i class="fas fa-<?= $flash['type'] === 'success' ? 'check-circle' : 'exclamation-triangle' ?> modern-alert-icon"></i>
                <div class="modern-alert-message"><?= $flash['message'] ?></div>
            </div>
            <button type="button" class="modern-alert-close" onclick="this.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <?php endif; ?>

        <!-- Database Statistics -->
        <div class="modern-grid modern-grid-cols-4 modern-gap-lg modern-mb-xl">
            <div class="modern-stats-card modern-stats-primary">
                <div class="modern-stats-content">
                    <div class="modern-stats-info">
                        <div class="modern-stats-label">Database Size</div>
                        <div class="modern-stats-value"><?= $dbStats['size_mb'] ?> MB</div>
                        <div class="modern-stats-meta">Total ukuran</div>
                    </div>
                    <div class="modern-stats-icon">
                        <i class="fas fa-database"></i>
                    </div>
                </div>
                <div class="modern-stats-glow"></div>
            </div>

            <div class="modern-stats-card modern-stats-success">
                <div class="modern-stats-content">
                    <div class="modern-stats-info">
                        <div class="modern-stats-label">Total Tables</div>
                        <div class="modern-stats-value"><?= $dbStats['total_tables'] ?></div>
                        <div class="modern-stats-meta">Tabel database</div>
                    </div>
                    <div class="modern-stats-icon">
                        <i class="fas fa-table"></i>
                    </div>
                </div>
                <div class="modern-stats-glow"></div>
            </div>

            <div class="modern-stats-card modern-stats-info">
                <div class="modern-stats-content">
                    <div class="modern-stats-info">
                        <div class="modern-stats-label">Total Records</div>
                        <div class="modern-stats-value"><?= number_format($dbStats['total_records']) ?></div>
                        <div class="modern-stats-meta">Baris data</div>
                    </div>
                    <div class="modern-stats-icon">
                        <i class="fas fa-list"></i>
                    </div>
                </div>
                <div class="modern-stats-glow"></div>
            </div>

            <div class="modern-stats-card modern-stats-warning">
                <div class="modern-stats-content">
                    <div class="modern-stats-info">
                        <div class="modern-stats-label">MySQL Version</div>
                        <div class="modern-stats-value"><?= explode('-', $dbStats['version'])[0] ?></div>
                        <div class="modern-stats-meta"><?= $dbStats['charset'] ?></div>
                    </div>
                    <div class="modern-stats-icon">
                        <i class="fas fa-server"></i>
                    </div>
                </div>
                <div class="modern-stats-glow"></div>
            </div>
        </div>

        <!-- Database Operations -->
        <div class="modern-card modern-mb-lg">
            <div class="modern-card-header">
                <h5 class="modern-card-title">
                    <i class="fas fa-tools modern-text-primary modern-mr-sm"></i>
                    Database Operations
                </h5>
            </div>
            <div class="modern-card-body">
                <div class="modern-grid modern-grid-cols-4 modern-gap-sm">
                    <form method="POST" style="display: inline;">
                        <input type="hidden" name="action" value="optimize_database">
                        <button type="submit" class="modern-btn modern-btn-success modern-w-full" onclick="return confirm('Optimasi database? Proses ini mungkin memakan waktu.')">
                            <i class="fas fa-rocket"></i>
                            Optimize Database
                        </button>
                    </form>
                    
                    <form method="POST" style="display: inline;">
                        <input type="hidden" name="action" value="repair_tables">
                        <button type="submit" class="modern-btn modern-btn-warning modern-w-full" onclick="return confirm('Perbaiki semua tabel database?')">
                            <i class="fas fa-wrench"></i>
                            Repair Tables
                        </button>
                    </form>
                    
                    <form method="POST" style="display: inline;">
                        <input type="hidden" name="action" value="analyze_tables">
                        <button type="submit" class="modern-btn modern-btn-info modern-w-full" onclick="return confirm('Analisis semua tabel database?')">
                            <i class="fas fa-chart-line"></i>
                            Analyze Tables
                        </button>
                    </form>
                    
                    <form method="POST" style="display: inline;">
                        <input type="hidden" name="action" value="check_tables">
                        <button type="submit" class="modern-btn modern-btn-primary modern-w-full" onclick="return confirm('Periksa integritas semua tabel?')">
                            <i class="fas fa-check-circle"></i>
                            Check Tables
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Table Information -->
        <div class="modern-card">
            <div class="modern-card-header">
                <h5 class="modern-card-title">
                    <i class="fas fa-table modern-text-primary modern-mr-sm"></i>
                    Table Information
                </h5>
                <div class="modern-card-actions">
                    <span class="modern-badge modern-badge-info">
                        <?= count($tableInfo) ?> tables
                    </span>
                </div>
            </div>
            <div class="modern-card-body modern-p-0">
                <div class="modern-table-responsive">
                    <table class="modern-table">
                        <thead class="modern-table-header">
                            <tr>
                                <th class="modern-table-th">Table Name</th>
                                <th class="modern-table-th">Rows</th>
                                <th class="modern-table-th">Size (MB)</th>
                                <th class="modern-table-th">Engine</th>
                                <th class="modern-table-th">Collation</th>
                                <th class="modern-table-th">Last Update</th>
                            </tr>
                        </thead>
                        <tbody class="modern-table-body">
                            <?php foreach ($tableInfo as $table): ?>
                            <tr class="modern-table-row">
                                <td class="modern-table-td">
                                    <div class="modern-table-content">
                                        <div class="modern-table-title">
                                            <i class="fas fa-table modern-text-primary modern-mr-sm"></i>
                                            <?= htmlspecialchars($table['table_name']) ?>
                                        </div>
                                    </div>
                                </td>
                                <td class="modern-table-td">
                                    <span class="modern-badge modern-badge-light">
                                        <?= number_format($table['table_rows']) ?>
                                    </span>
                                </td>
                                <td class="modern-table-td">
                                    <div class="modern-table-size">
                                        <div class="modern-size-total"><?= $table['size_mb'] ?> MB</div>
                                        <div class="modern-size-breakdown">
                                            Data: <?= $table['data_mb'] ?> MB | Index: <?= $table['index_mb'] ?> MB
                                        </div>
                                    </div>
                                </td>
                                <td class="modern-table-td">
                                    <span class="modern-badge modern-badge-<?= $table['engine'] === 'InnoDB' ? 'success' : 'warning' ?>">
                                        <?= $table['engine'] ?>
                                    </span>
                                </td>
                                <td class="modern-table-td">
                                    <span class="modern-text-sm modern-text-muted">
                                        <?= $table['table_collation'] ?>
                                    </span>
                                </td>
                                <td class="modern-table-td">
                                    <div class="modern-table-date">
                                        <?= $table['update_time'] ? date('d/m/Y H:i', strtotime($table['update_time'])) : 'Never' ?>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/views/layouts/footer.php'; ?>

<style>
.modern-size-breakdown {
    font-size: 11px;
    color: #6c757d;
    margin-top: 2px;
}

.modern-table-size {
    text-align: center;
}

.modern-size-total {
    font-weight: 600;
    font-size: 13px;
}
</style>
