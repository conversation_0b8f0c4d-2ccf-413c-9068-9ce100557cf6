<?php
require_once 'includes/config/database.php';
require_once 'includes/helpers/functions.php';

// Jika user sudah login, redirect ke dashboard
if (isLoggedIn()) {
    redirect('index.php');
}

$errors = [];
$token = $_GET['token'] ?? '';

// Validasi token
if (empty($token)) {
    setFlashMessage('error', 'Token tidak valid');
    redirect('login.php');
}

// Cek apakah token valid dan belum kadaluarsa
try {
    $stmt = $pdo->prepare("
        SELECT pr.*, u.email, u.nama 
        FROM password_resets pr 
        JOIN users u ON pr.user_id = u.id 
        WHERE pr.token = ? AND pr.expires_at > NOW() AND pr.used = 0
    ");
    $stmt->execute([$token]);
    $reset = $stmt->fetch();
    
    if (!$reset) {
        setFlashMessage('error', 'Token tidak valid atau sudah kadaluarsa');
        redirect('login.php');
    }
} catch (PDOException $e) {
    error_log("Token Validation Error: " . $e->getMessage());
    setFlashMessage('error', 'Terjadi kesalahan. Silakan coba lagi.');
    redirect('login.php');
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $password = $_POST['password'] ?? '';
    $konfirmasi_password = $_POST['konfirmasi_password'] ?? '';
    
    // Validasi password
    if (empty($password)) {
        $errors['password'] = 'Password harus diisi';
    } elseif (strlen($password) < 6) {
        $errors['password'] = 'Password minimal 6 karakter';
    }
    
    if ($password !== $konfirmasi_password) {
        $errors['konfirmasi_password'] = 'Konfirmasi password tidak sesuai';
    }
    
    // Jika tidak ada error, update password
    if (empty($errors)) {
        try {
            // Hash password baru
            $hashed_password = password_hash($password, PASSWORD_DEFAULT);
            
            // Update password user
            $stmt = $pdo->prepare("UPDATE users SET password = ? WHERE id = ?");
            if ($stmt->execute([$hashed_password, $reset['user_id']])) {
                // Tandai token sebagai sudah digunakan
                $stmt = $pdo->prepare("UPDATE password_resets SET used = 1 WHERE token = ?");
                $stmt->execute([$token]);
                
                // Log aktivitas
                logActivity($reset['user_id'], 'User reset password');
                
                // Set flash message
                setFlashMessage('success', 'Password berhasil diubah. Silakan login dengan password baru.');
                
                // Redirect ke halaman login
                redirect('login.php');
            } else {
                $errors['general'] = 'Gagal mengubah password. Silakan coba lagi.';
            }
        } catch (PDOException $e) {
            error_log("Password Reset Error: " . $e->getMessage());
            $errors['general'] = 'Terjadi kesalahan. Silakan coba lagi.';
        }
    }
}
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Password - Sistem Keuangan</title>
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container">
        <div class="row justify-content-center mt-5">
            <div class="col-md-6">
                <div class="card shadow">
                    <div class="card-body p-5">
                        <h2 class="text-center mb-4">Reset Password</h2>
                        
                        <?php if (isset($errors['general'])): ?>
                        <div class="alert alert-danger"><?= $errors['general'] ?></div>
                        <?php endif; ?>
                        
                        <form method="POST" action="" novalidate>
                            <div class="mb-3">
                                <label for="password" class="form-label">Password Baru</label>
                                <div class="input-group">
                                    <input type="password" class="form-control <?= isset($errors['password']) ? 'is-invalid' : '' ?>" 
                                           id="password" name="password" required>
                                    <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <?php if (isset($errors['password'])): ?>
                                    <div class="invalid-feedback"><?= $errors['password'] ?></div>
                                    <?php endif; ?>
                                </div>
                                <div class="form-text">
                                    Password minimal 6 karakter
                                </div>
                            </div>
                            
                            <div class="mb-4">
                                <label for="konfirmasi_password" class="form-label">Konfirmasi Password</label>
                                <div class="input-group">
                                    <input type="password" class="form-control <?= isset($errors['konfirmasi_password']) ? 'is-invalid' : '' ?>" 
                                           id="konfirmasi_password" name="konfirmasi_password" required>
                                    <button class="btn btn-outline-secondary" type="button" id="toggleConfirmPassword">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <?php if (isset($errors['konfirmasi_password'])): ?>
                                    <div class="invalid-feedback"><?= $errors['konfirmasi_password'] ?></div>
                                    <?php endif; ?>
                                </div>
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">Reset Password</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
    // Toggle password visibility
    document.getElementById('togglePassword').addEventListener('click', function() {
        const password = document.getElementById('password');
        const icon = this.querySelector('i');
        
        if (password.type === 'password') {
            password.type = 'text';
            icon.classList.remove('fa-eye');
            icon.classList.add('fa-eye-slash');
        } else {
            password.type = 'password';
            icon.classList.remove('fa-eye-slash');
            icon.classList.add('fa-eye');
        }
    });
    
    document.getElementById('toggleConfirmPassword').addEventListener('click', function() {
        const password = document.getElementById('konfirmasi_password');
        const icon = this.querySelector('i');
        
        if (password.type === 'password') {
            password.type = 'text';
            icon.classList.remove('fa-eye');
            icon.classList.add('fa-eye-slash');
        } else {
            password.type = 'password';
            icon.classList.remove('fa-eye-slash');
            icon.classList.add('fa-eye');
        }
    });
    </script>
</body>
</html> 