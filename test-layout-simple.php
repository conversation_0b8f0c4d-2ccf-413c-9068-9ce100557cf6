<!DOCTYPE html>
<html lang="id" data-bs-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Layout & Dark Mode - KeuanganKu</title>

    <!-- CSS Libraries -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="assets/css/layout-manager.css?v=<?= time() ?>" rel="stylesheet">
    <link href="assets/css/control-sidebar.css?v=<?= time() ?>" rel="stylesheet">
    
    <!-- Initialize Theme Early -->
    <script>
        // Apply saved theme immediately to prevent flash
        (function() {
            const savedTheme = localStorage.getItem('theme') || 'light';
            document.documentElement.setAttribute('data-bs-theme', savedTheme);
        })();
    </script>
</head>
<body>
    <!-- Simple Navbar for Testing -->
    <nav class="modern-navbar navbar navbar-expand-lg" id="mainNavbar">
        <div class="container-fluid">
            <!-- Brand -->
            <div class="navbar-brand">
                <div class="brand-logo">
                    <i class="fas fa-wallet" id="greetingIcon"></i>
                </div>
                <div class="brand-text">
                    <div class="brand-name" id="dynamicGreeting">Test Layout</div>
                    <div class="brand-subtitle">Layout & Dark Mode Testing</div>
                </div>
            </div>

            <!-- Navbar Actions -->
            <ul class="navbar-nav ms-auto d-flex flex-row">
                <!-- Control Sidebar Toggle -->
                <li class="nav-item me-2">
                    <button class="nav-link btn btn-link" type="button" data-widget="control-sidebar"
                            data-bs-toggle="tooltip" title="Customize Layout">
                        <i class="fas fa-cogs"></i>
                    </button>
                </li>

                <!-- Dark Mode Toggle -->
                <li class="nav-item me-2">
                    <button class="nav-link btn btn-link" type="button" onclick="toggleTheme()"
                            data-theme-toggle data-bs-toggle="tooltip" title="Toggle Dark Mode">
                        <i class="fas fa-moon"></i>
                    </button>
                </li>
            </ul>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="main-content" style="margin-top: 72px; padding: 2rem;">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-cogs me-2"></i>Layout & Dark Mode Test
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle me-2"></i>Test Instructions:</h6>
                                <ol>
                                    <li><strong>Customize Layout:</strong> Click the <strong>gear icon (⚙️)</strong> to open Layout Customizer</li>
                                    <li><strong>Dark Mode:</strong> Click the <strong>moon/sun icon (🌙/☀️)</strong> to toggle dark mode</li>
                                    <li><strong>Test Features:</strong> Try different navbar colors, icon colors, and layout options</li>
                                    <li><strong>Persistence:</strong> Reload the page to test if settings are saved</li>
                                </ol>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0"><i class="fas fa-palette me-2"></i>Theme Test Card</h6>
                                        </div>
                                        <div class="card-body">
                                            <p>This card should change colors when you toggle dark mode.</p>
                                            <button class="btn btn-primary btn-sm">Primary Button</button>
                                            <button class="btn btn-outline-secondary btn-sm ms-2">Outline Button</button>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0"><i class="fas fa-form me-2"></i>Form Test</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="mb-3">
                                                <label class="form-label">Test Input</label>
                                                <input type="text" class="form-control" placeholder="Type something...">
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="testCheck">
                                                <label class="form-check-label" for="testCheck">Test Checkbox</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="mt-4">
                                <h6>Status Information</h6>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="card bg-light">
                                            <div class="card-body text-center">
                                                <h6>Current Theme</h6>
                                                <p id="currentTheme" class="mb-0">Loading...</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="card bg-light">
                                            <div class="card-body text-center">
                                                <h6>Control Sidebar</h6>
                                                <p id="controlSidebarState" class="mb-0">Loading...</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="card bg-light">
                                            <div class="card-body text-center">
                                                <h6>Test Buttons</h6>
                                                <button class="btn btn-primary btn-sm" onclick="testControlSidebar()">
                                                    <i class="fas fa-cogs me-1"></i>Toggle Sidebar
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Control Sidebar -->
    <div class="control-sidebar" id="controlSidebar">
        <div class="control-sidebar-content">
            <div class="control-sidebar-header">
                <h5><i class="fas fa-cogs me-2"></i>Layout Customizer</h5>
                <button class="control-sidebar-close" type="button">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="control-sidebar-body">
                <!-- Quick Settings -->
                <div class="control-section">
                    <div class="control-section-title">
                        <i class="fas fa-bolt me-2"></i>Quick Settings
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="darkModeSwitch">
                        <label class="form-check-label" for="darkModeSwitch">Dark Mode</label>
                    </div>
                </div>

                <!-- Navbar Colors -->
                <div class="control-section">
                    <div class="control-section-title">
                        <i class="fas fa-palette me-2"></i>Navbar Colors
                    </div>
                    <div class="color-variants">
                        <div class="color-option" data-target="navbar" data-color="white">
                            <div class="color-preview" style="background: #ffffff; border: 1px solid #dee2e6;"></div>
                            <span>White</span>
                        </div>
                        <div class="color-option" data-target="navbar" data-color="primary">
                            <div class="color-preview" style="background: #007bff;"></div>
                            <span>Primary</span>
                        </div>
                        <div class="color-option" data-target="navbar" data-color="success">
                            <div class="color-preview" style="background: #28a745;"></div>
                            <span>Success</span>
                        </div>
                        <div class="color-option" data-target="navbar" data-color="dark">
                            <div class="color-preview" style="background: #343a40;"></div>
                            <span>Dark</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Control Sidebar Overlay -->
    <div class="control-sidebar-overlay" id="controlSidebarOverlay"></div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/layout-manager.js?v=<?= time() ?>"></script>
    <script src="assets/js/control-sidebar.js?v=<?= time() ?>"></script>

    <script>
        // Test functions
        function testControlSidebar() {
            if (typeof window.toggleControlSidebarGlobal === 'function') {
                window.toggleControlSidebarGlobal();
            } else {
                const controlSidebar = document.getElementById('controlSidebar');
                const controlSidebarOverlay = document.getElementById('controlSidebarOverlay');
                
                if (controlSidebar && controlSidebarOverlay) {
                    controlSidebar.classList.toggle('open');
                    controlSidebarOverlay.classList.toggle('show');
                    document.body.style.overflow = controlSidebar.classList.contains('open') ? 'hidden' : '';
                }
            }
            refreshStatus();
        }

        function refreshStatus() {
            // Update current theme
            const currentTheme = document.documentElement.getAttribute('data-bs-theme') || 'light';
            document.getElementById('currentTheme').textContent = currentTheme;
            
            // Update control sidebar state
            const controlSidebar = document.getElementById('controlSidebar');
            const controlSidebarOpen = controlSidebar ? controlSidebar.classList.contains('open') : false;
            document.getElementById('controlSidebarState').textContent = controlSidebarOpen ? 'Open' : 'Closed';
        }

        // Initialize status on page load
        document.addEventListener('DOMContentLoaded', function() {
            refreshStatus();
            
            // Update theme toggle button based on current theme
            const currentTheme = document.documentElement.getAttribute('data-bs-theme') || 'light';
            const themeToggle = document.querySelector('[data-theme-toggle] i');
            if (themeToggle) {
                themeToggle.className = currentTheme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
            }
        });
    </script>
</body>
</html>
