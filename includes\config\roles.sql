-- Create roles table
CREATE TABLE IF NOT EXISTS roles (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VA<PERSON><PERSON><PERSON>(50) NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMES<PERSON>MP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create permissions table
CREATE TABLE IF NOT EXISTS permissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create role_permissions table (junction table)
CREATE TABLE IF NOT EXISTS role_permissions (
    role_id INT NOT NULL,
    permission_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (role_id, permission_id),
    <PERSON><PERSON><PERSON><PERSON><PERSON> KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
    FOREIG<PERSON> KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE
);

-- Insert default roles
INSERT IGNORE INTO roles (name, description) VALUES
('admin', 'Administrator dengan akses penuh ke sistem'),
('user', 'Pengguna biasa dengan akses terbatas');

-- Insert default permissions
INSERT IGNORE INTO permissions (name, description) VALUES
('manage_users', 'Dapat mengelola pengguna'),
('manage_roles', 'Dapat mengelola role'),
('manage_permissions', 'Dapat mengelola hak akses'),
('view_logs', 'Dapat melihat log aktivitas'),
('manage_backup', 'Dapat mengelola backup data'),
('manage_settings', 'Dapat mengelola pengaturan sistem'),
('view_reports', 'Dapat melihat laporan'),
('manage_transactions', 'Dapat mengelola transaksi'),
('manage_categories', 'Dapat mengelola kategori'),
('manage_products', 'Dapat mengelola produk'),
('manage_sales', 'Dapat mengelola penjualan');

-- Assign permissions to admin role
INSERT IGNORE INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id
FROM roles r
CROSS JOIN permissions p
WHERE r.name = 'admin';

-- Assign basic permissions to user role
INSERT IGNORE INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id
FROM roles r
CROSS JOIN permissions p
WHERE r.name = 'user'
AND p.name IN ('view_reports', 'manage_transactions', 'manage_categories'); 