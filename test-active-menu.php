<!DOCTYPE html>
<html lang="id" data-bs-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Active Menu - KeuanganKu</title>

    <!-- CSS Libraries -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="assets/css/layout-manager.css?v=<?= time() ?>" rel="stylesheet">
    <link href="assets/css/control-sidebar.css?v=<?= time() ?>" rel="stylesheet">
</head>
<body>
    <!-- Modern Sidebar -->
    <aside class="modern-sidebar">
        <!-- Sidebar Header -->
        <div class="sidebar-header">
            <div class="brand-container">
                <div class="brand-logo">
                    <div class="logo-icon">
                        <i class="fas fa-wallet"></i>
                    </div>
                </div>
                <div class="brand-content">
                    <h1 class="brand-title">KeuanganKu</h1>
                    <p class="brand-subtitle">Financial Manager</p>
                </div>
            </div>
        </div>

        <!-- User Profile Section -->
        <div class="sidebar-user">
            <div class="user-profile">
                <div class="user-avatar">
                    <img src="https://ui-avatars.com/api/?name=Test+User&background=667eea&color=fff&size=48" alt="User Avatar" class="avatar-img">
                    <div class="status-indicator"></div>
                </div>
                <div class="user-details">
                    <h6 class="user-name">Test User</h6>
                    <span class="user-role">Admin</span>
                    <div class="user-status">Online</div>
                </div>
            </div>
        </div>

        <!-- Navigation Menu -->
        <div class="sidebar-navigation">
            <nav class="nav-menu">
                <!-- Main Section -->
                <div class="menu-section">
                    <div class="menu-section-title">Main</div>
                    
                    <a class="menu-item" href="/keuangan/dashboard.php" data-tooltip="Dashboard">
                        <div class="menu-icon">
                            <i class="fas fa-home"></i>
                        </div>
                        <span class="menu-text">Dashboard</span>
                    </a>

                    <a class="menu-item" href="test-active-menu.php" data-tooltip="Test Active Menu">
                        <div class="menu-icon">
                            <i class="fas fa-vial"></i>
                        </div>
                        <span class="menu-text">Test Active Menu</span>
                    </a>

                    <button class="menu-item" data-bs-toggle="collapse" data-bs-target="#transactionSubmenu" data-tooltip="Transactions">
                        <div class="menu-icon">
                            <i class="fas fa-exchange-alt"></i>
                        </div>
                        <span class="menu-text">Transactions</span>
                        <i class="fas fa-chevron-down menu-arrow"></i>
                    </button>
                    <div class="collapse submenu" id="transactionSubmenu">
                        <a class="submenu-item" href="transaksi.php">
                            <div class="submenu-icon">
                                <i class="fas fa-plus"></i>
                            </div>
                            Add Transaction
                        </a>
                        <a class="submenu-item" href="laporan.php">
                            <div class="submenu-icon">
                                <i class="fas fa-chart-bar"></i>
                            </div>
                            Reports
                        </a>
                    </div>
                </div>

                <!-- Management Section -->
                <div class="menu-section">
                    <div class="menu-section-title">Management</div>
                    
                    <a class="menu-item" href="kategori.php" data-tooltip="Categories">
                        <div class="menu-icon">
                            <i class="fas fa-tags"></i>
                        </div>
                        <span class="menu-text">Categories</span>
                    </a>

                    <a class="menu-item" href="profile.php" data-tooltip="Profile">
                        <div class="menu-icon">
                            <i class="fas fa-user"></i>
                        </div>
                        <span class="menu-text">Profile</span>
                    </a>
                </div>
            </nav>
        </div>

        <!-- Sidebar Footer -->
        <div class="sidebar-footer">
            <div class="sidebar-footer-content">
                <button class="sidebar-toggle-btn" data-bs-toggle="tooltip" title="Toggle Sidebar">
                    <i class="fas fa-chevron-left"></i>
                </button>
                <button class="theme-toggle-btn" onclick="toggleTheme()" data-bs-toggle="tooltip" title="Toggle Dark Mode">
                    <i class="fas fa-moon"></i>
                </button>
            </div>
        </div>
    </aside>

    <!-- Sidebar Overlay for Mobile -->
    <div class="sidebar-overlay"></div>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Navbar -->
        <nav class="navbar navbar-expand-lg modern-navbar">
            <div class="container-fluid">
                <!-- Mobile Sidebar Toggle -->
                <button class="mobile-sidebar-toggle d-lg-none" type="button">
                    <i class="fas fa-bars"></i>
                </button>

                <!-- Brand -->
                <div class="navbar-brand">
                    <div class="brand-logo">
                        <i class="fas fa-vial" style="color: #667eea;"></i>
                    </div>
                    <span style="color: #667eea; font-weight: 600;">Active Menu Test</span>
                </div>

                <!-- Controls -->
                <div class="navbar-nav ms-auto">
                    <button class="nav-link btn btn-link me-2" onclick="toggleTheme()">
                        <i class="fas fa-moon"></i>
                    </button>
                    <button class="nav-link btn btn-link" onclick="window.modernSidebar?.toggle()">
                        <i class="fas fa-cogs"></i>
                    </button>
                </div>
            </div>
        </nav>

        <!-- Content -->
        <div class="content-container">
            <div class="container-fluid">
                <!-- Header -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h4 class="card-title mb-0">
                                    <i class="fas fa-vial me-2"></i>Active Menu Detection Test
                                </h4>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-info">
                                    <h6><i class="fas fa-info-circle me-2"></i>Test Instructions:</h6>
                                    <ol class="mb-0">
                                        <li>Check that "Test Active Menu" is highlighted in the sidebar</li>
                                        <li>Click on different menu items to navigate</li>
                                        <li>Use browser back/forward buttons</li>
                                        <li>Refresh the page</li>
                                        <li>Verify that the correct menu stays active</li>
                                    </ol>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Test Navigation -->
                <div class="row">
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="card-title mb-0">
                                    <i class="fas fa-link me-2"></i>Test Navigation
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="d-grid gap-2">
                                    <a href="/keuangan/dashboard.php" class="btn btn-outline-primary">
                                        <i class="fas fa-home me-2"></i>Go to Dashboard
                                    </a>
                                    <a href="transaksi.php" class="btn btn-outline-success">
                                        <i class="fas fa-plus me-2"></i>Go to Transactions
                                    </a>
                                    <a href="kategori.php" class="btn btn-outline-warning">
                                        <i class="fas fa-tags me-2"></i>Go to Categories
                                    </a>
                                    <a href="profile.php" class="btn btn-outline-info">
                                        <i class="fas fa-user me-2"></i>Go to Profile
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="card-title mb-0">
                                    <i class="fas fa-bug me-2"></i>Debug Info
                                </h6>
                            </div>
                            <div class="card-body">
                                <div id="debugInfo">
                                    <p><strong>Current URL:</strong> <span id="currentUrl"></span></p>
                                    <p><strong>Current Page:</strong> <span id="currentPage"></span></p>
                                    <p><strong>Active Menu:</strong> <span id="activeMenu"></span></p>
                                    <p><strong>Active Submenu:</strong> <span id="activeSubmenu"></span></p>
                                </div>
                                <button class="btn btn-sm btn-secondary" onclick="updateDebugInfo()">
                                    <i class="fas fa-refresh me-1"></i>Refresh Debug Info
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Manual Test Controls -->
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="card-title mb-0">
                                    <i class="fas fa-gamepad me-2"></i>Manual Test Controls
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row g-2">
                                    <div class="col-md-3">
                                        <button class="btn btn-primary w-100" onclick="window.modernSidebar?.refreshActiveMenu()">
                                            <i class="fas fa-sync me-1"></i>Refresh Active Menu
                                        </button>
                                    </div>
                                    <div class="col-md-3">
                                        <button class="btn btn-success w-100" onclick="simulateNavigation('dashboard')">
                                            <i class="fas fa-home me-1"></i>Simulate Dashboard
                                        </button>
                                    </div>
                                    <div class="col-md-3">
                                        <button class="btn btn-warning w-100" onclick="simulateNavigation('transaksi')">
                                            <i class="fas fa-plus me-1"></i>Simulate Transaksi
                                        </button>
                                    </div>
                                    <div class="col-md-3">
                                        <button class="btn btn-info w-100" onclick="toggleTheme()">
                                            <i class="fas fa-palette me-1"></i>Toggle Theme
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/modern-sidebar.js?v=<?= time() ?>"></script>
    <script src="assets/js/theme-manager.js?v=<?= time() ?>"></script>

    <script>
        // Debug functions
        function updateDebugInfo() {
            document.getElementById('currentUrl').textContent = window.location.href;
            document.getElementById('currentPage').textContent = window.location.pathname.split('/').pop().replace('.php', '') || 'index';
            
            const activeMenu = document.querySelector('.menu-item.active');
            document.getElementById('activeMenu').textContent = activeMenu ? activeMenu.querySelector('.menu-text').textContent : 'None';
            
            const activeSubmenu = document.querySelector('.submenu-item.active');
            document.getElementById('activeSubmenu').textContent = activeSubmenu ? activeSubmenu.textContent.trim() : 'None';
        }

        function simulateNavigation(page) {
            // Simulate URL change without actually navigating
            history.pushState({}, '', page + '.php');
            
            // Refresh active menu
            if (window.modernSidebar) {
                window.modernSidebar.refreshActiveMenu();
            }
            
            // Update debug info
            updateDebugInfo();
            
            console.log('🔄 Simulated navigation to:', page);
        }

        // Initialize debug info
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(updateDebugInfo, 200);
            
            // Initialize tooltips
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        });

        // Update debug info when active menu changes
        document.addEventListener('click', function(e) {
            if (e.target.closest('.menu-item, .submenu-item')) {
                setTimeout(updateDebugInfo, 100);
            }
        });
    </script>
</body>
</html>
