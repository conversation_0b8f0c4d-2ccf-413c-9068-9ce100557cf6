<?php

declare(strict_types=1);

/**
 * This file is part of the Carbon package.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Carbon;

use Carbon\Traits\Date;
use DateTime;
use DateTimeInterface;

/**
 * A simple API extension for DateTime.
 *
 * <autodoc generated by `composer phpdoc`>
 *
 * @property      string           $localeDayOfWeek                                                                   the day of week in current locale
 * @property      string           $shortLocaleDayOfWeek                                                              the abbreviated day of week in current locale
 * @property      string           $localeMonth                                                                       the month in current locale
 * @property      string           $shortLocaleMonth                                                                  the abbreviated month in current locale
 * @property      int              $year
 * @property      int              $yearIso
 * @property      int              $month
 * @property      int              $day
 * @property      int              $hour
 * @property      int              $minute
 * @property      int              $second
 * @property      int              $micro
 * @property      int              $microsecond
 * @property      int              $dayOfWeekIso                                                                      1 (for Monday) through 7 (for Sunday)
 * @property      int|float|string $timestamp                                                                         seconds since the Unix Epoch
 * @property      string           $englishDayOfWeek                                                                  the day of week in English
 * @property      string           $shortEnglishDayOfWeek                                                             the abbreviated day of week in English
 * @property      string           $englishMonth                                                                      the month in English
 * @property      string           $shortEnglishMonth                                                                 the abbreviated month in English
 * @property      int              $milliseconds
 * @property      int              $millisecond
 * @property      int              $milli
 * @property      int              $week                                                                              1 through 53
 * @property      int              $isoWeek                                                                           1 through 53
 * @property      int              $weekYear                                                                          year according to week format
 * @property      int              $isoWeekYear                                                                       year according to ISO week format
 * @property      int              $age                                                                               does a diffInYears() with default parameters
 * @property      int              $offset                                                                            the timezone offset in seconds from UTC
 * @property      int              $offsetMinutes                                                                     the timezone offset in minutes from UTC
 * @property      int              $offsetHours                                                                       the timezone offset in hours from UTC
 * @property      CarbonTimeZone   $timezone                                                                          the current timezone
 * @property      CarbonTimeZone   $tz                                                                                alias of $timezone
 * @property      int              $centuryOfMillennium                                                               The value of the century starting from the beginning of the current millennium
 * @property      int              $dayOfCentury                                                                      The value of the day starting from the beginning of the current century
 * @property      int              $dayOfDecade                                                                       The value of the day starting from the beginning of the current decade
 * @property      int              $dayOfMillennium                                                                   The value of the day starting from the beginning of the current millennium
 * @property      int              $dayOfMonth                                                                        The value of the day starting from the beginning of the current month
 * @property      int              $dayOfQuarter                                                                      The value of the day starting from the beginning of the current quarter
 * @property      int              $dayOfWeek                                                                         0 (for Sunday) through 6 (for Saturday)
 * @property      int              $dayOfYear                                                                         1 through 366
 * @property      int              $decadeOfCentury                                                                   The value of the decade starting from the beginning of the current century
 * @property      int              $decadeOfMillennium                                                                The value of the decade starting from the beginning of the current millennium
 * @property      int              $hourOfCentury                                                                     The value of the hour starting from the beginning of the current century
 * @property      int              $hourOfDay                                                                         The value of the hour starting from the beginning of the current day
 * @property      int              $hourOfDecade                                                                      The value of the hour starting from the beginning of the current decade
 * @property      int              $hourOfMillennium                                                                  The value of the hour starting from the beginning of the current millennium
 * @property      int              $hourOfMonth                                                                       The value of the hour starting from the beginning of the current month
 * @property      int              $hourOfQuarter                                                                     The value of the hour starting from the beginning of the current quarter
 * @property      int              $hourOfWeek                                                                        The value of the hour starting from the beginning of the current week
 * @property      int              $hourOfYear                                                                        The value of the hour starting from the beginning of the current year
 * @property      int              $microsecondOfCentury                                                              The value of the microsecond starting from the beginning of the current century
 * @property      int              $microsecondOfDay                                                                  The value of the microsecond starting from the beginning of the current day
 * @property      int              $microsecondOfDecade                                                               The value of the microsecond starting from the beginning of the current decade
 * @property      int              $microsecondOfHour                                                                 The value of the microsecond starting from the beginning of the current hour
 * @property      int              $microsecondOfMillennium                                                           The value of the microsecond starting from the beginning of the current millennium
 * @property      int              $microsecondOfMillisecond                                                          The value of the microsecond starting from the beginning of the current millisecond
 * @property      int              $microsecondOfMinute                                                               The value of the microsecond starting from the beginning of the current minute
 * @property      int              $microsecondOfMonth                                                                The value of the microsecond starting from the beginning of the current month
 * @property      int              $microsecondOfQuarter                                                              The value of the microsecond starting from the beginning of the current quarter
 * @property      int              $microsecondOfSecond                                                               The value of the microsecond starting from the beginning of the current second
 * @property      int              $microsecondOfWeek                                                                 The value of the microsecond starting from the beginning of the current week
 * @property      int              $microsecondOfYear                                                                 The value of the microsecond starting from the beginning of the current year
 * @property      int              $millisecondOfCentury                                                              The value of the millisecond starting from the beginning of the current century
 * @property      int              $millisecondOfDay                                                                  The value of the millisecond starting from the beginning of the current day
 * @property      int              $millisecondOfDecade                                                               The value of the millisecond starting from the beginning of the current decade
 * @property      int              $millisecondOfHour                                                                 The value of the millisecond starting from the beginning of the current hour
 * @property      int              $millisecondOfMillennium                                                           The value of the millisecond starting from the beginning of the current millennium
 * @property      int              $millisecondOfMinute                                                               The value of the millisecond starting from the beginning of the current minute
 * @property      int              $millisecondOfMonth                                                                The value of the millisecond starting from the beginning of the current month
 * @property      int              $millisecondOfQuarter                                                              The value of the millisecond starting from the beginning of the current quarter
 * @property      int              $millisecondOfSecond                                                               The value of the millisecond starting from the beginning of the current second
 * @property      int              $millisecondOfWeek                                                                 The value of the millisecond starting from the beginning of the current week
 * @property      int              $millisecondOfYear                                                                 The value of the millisecond starting from the beginning of the current year
 * @property      int              $minuteOfCentury                                                                   The value of the minute starting from the beginning of the current century
 * @property      int              $minuteOfDay                                                                       The value of the minute starting from the beginning of the current day
 * @property      int              $minuteOfDecade                                                                    The value of the minute starting from the beginning of the current decade
 * @property      int              $minuteOfHour                                                                      The value of the minute starting from the beginning of the current hour
 * @property      int              $minuteOfMillennium                                                                The value of the minute starting from the beginning of the current millennium
 * @property      int              $minuteOfMonth                                                                     The value of the minute starting from the beginning of the current month
 * @property      int              $minuteOfQuarter                                                                   The value of the minute starting from the beginning of the current quarter
 * @property      int              $minuteOfWeek                                                                      The value of the minute starting from the beginning of the current week
 * @property      int              $minuteOfYear                                                                      The value of the minute starting from the beginning of the current year
 * @property      int              $monthOfCentury                                                                    The value of the month starting from the beginning of the current century
 * @property      int              $monthOfDecade                                                                     The value of the month starting from the beginning of the current decade
 * @property      int              $monthOfMillennium                                                                 The value of the month starting from the beginning of the current millennium
 * @property      int              $monthOfQuarter                                                                    The value of the month starting from the beginning of the current quarter
 * @property      int              $monthOfYear                                                                       The value of the month starting from the beginning of the current year
 * @property      int              $quarterOfCentury                                                                  The value of the quarter starting from the beginning of the current century
 * @property      int              $quarterOfDecade                                                                   The value of the quarter starting from the beginning of the current decade
 * @property      int              $quarterOfMillennium                                                               The value of the quarter starting from the beginning of the current millennium
 * @property      int              $quarterOfYear                                                                     The value of the quarter starting from the beginning of the current year
 * @property      int              $secondOfCentury                                                                   The value of the second starting from the beginning of the current century
 * @property      int              $secondOfDay                                                                       The value of the second starting from the beginning of the current day
 * @property      int              $secondOfDecade                                                                    The value of the second starting from the beginning of the current decade
 * @property      int              $secondOfHour                                                                      The value of the second starting from the beginning of the current hour
 * @property      int              $secondOfMillennium                                                                The value of the second starting from the beginning of the current millennium
 * @property      int              $secondOfMinute                                                                    The value of the second starting from the beginning of the current minute
 * @property      int              $secondOfMonth                                                                     The value of the second starting from the beginning of the current month
 * @property      int              $secondOfQuarter                                                                   The value of the second starting from the beginning of the current quarter
 * @property      int              $secondOfWeek                                                                      The value of the second starting from the beginning of the current week
 * @property      int              $secondOfYear                                                                      The value of the second starting from the beginning of the current year
 * @property      int              $weekOfCentury                                                                     The value of the week starting from the beginning of the current century
 * @property      int              $weekOfDecade                                                                      The value of the week starting from the beginning of the current decade
 * @property      int              $weekOfMillennium                                                                  The value of the week starting from the beginning of the current millennium
 * @property      int              $weekOfMonth                                                                       1 through 5
 * @property      int              $weekOfQuarter                                                                     The value of the week starting from the beginning of the current quarter
 * @property      int              $weekOfYear                                                                        ISO-8601 week number of year, weeks starting on Monday
 * @property      int              $yearOfCentury                                                                     The value of the year starting from the beginning of the current century
 * @property      int              $yearOfDecade                                                                      The value of the year starting from the beginning of the current decade
 * @property      int              $yearOfMillennium                                                                  The value of the year starting from the beginning of the current millennium
 * @property-read string           $latinMeridiem                                                                     "am"/"pm" (Ante meridiem or Post meridiem latin lowercase mark)
 * @property-read string           $latinUpperMeridiem                                                                "AM"/"PM" (Ante meridiem or Post meridiem latin uppercase mark)
 * @property-read string           $timezoneAbbreviatedName                                                           the current timezone abbreviated name
 * @property-read string           $tzAbbrName                                                                        alias of $timezoneAbbreviatedName
 * @property-read string           $dayName                                                                           long name of weekday translated according to Carbon locale, in english if no translation available for current language
 * @property-read string           $shortDayName                                                                      short name of weekday translated according to Carbon locale, in english if no translation available for current language
 * @property-read string           $minDayName                                                                        very short name of weekday translated according to Carbon locale, in english if no translation available for current language
 * @property-read string           $monthName                                                                         long name of month translated according to Carbon locale, in english if no translation available for current language
 * @property-read string           $shortMonthName                                                                    short name of month translated according to Carbon locale, in english if no translation available for current language
 * @property-read string           $meridiem                                                                          lowercase meridiem mark translated according to Carbon locale, in latin if no translation available for current language
 * @property-read string           $upperMeridiem                                                                     uppercase meridiem mark translated according to Carbon locale, in latin if no translation available for current language
 * @property-read int              $noZeroHour                                                                        current hour from 1 to 24
 * @property-read int              $isoWeeksInYear                                                                    51 through 53
 * @property-read int              $weekNumberInMonth                                                                 1 through 5
 * @property-read int              $firstWeekDay                                                                      0 through 6
 * @property-read int              $lastWeekDay                                                                       0 through 6
 * @property-read int              $quarter                                                                           the quarter of this instance, 1 - 4
 * @property-read int              $decade                                                                            the decade of this instance
 * @property-read int              $century                                                                           the century of this instance
 * @property-read int              $millennium                                                                        the millennium of this instance
 * @property-read bool             $dst                                                                               daylight savings time indicator, true if DST, false otherwise
 * @property-read bool             $local                                                                             checks if the timezone is local, true if local, false otherwise
 * @property-read bool             $utc                                                                               checks if the timezone is UTC, true if UTC, false otherwise
 * @property-read string           $timezoneName                                                                      the current timezone name
 * @property-read string           $tzName                                                                            alias of $timezoneName
 * @property-read string           $locale                                                                            locale of the current instance
 * @property-read int              $centuriesInMillennium                                                             The number of centuries contained in the current millennium
 * @property-read int              $daysInCentury                                                                     The number of days contained in the current century
 * @property-read int              $daysInDecade                                                                      The number of days contained in the current decade
 * @property-read int              $daysInMillennium                                                                  The number of days contained in the current millennium
 * @property-read int              $daysInMonth                                                                       number of days in the given month
 * @property-read int              $daysInQuarter                                                                     The number of days contained in the current quarter
 * @property-read int              $daysInWeek                                                                        The number of days contained in the current week
 * @property-read int              $daysInYear                                                                        365 or 366
 * @property-read int              $decadesInCentury                                                                  The number of decades contained in the current century
 * @property-read int              $decadesInMillennium                                                               The number of decades contained in the current millennium
 * @property-read int              $hoursInCentury                                                                    The number of hours contained in the current century
 * @property-read int              $hoursInDay                                                                        The number of hours contained in the current day
 * @property-read int              $hoursInDecade                                                                     The number of hours contained in the current decade
 * @property-read int              $hoursInMillennium                                                                 The number of hours contained in the current millennium
 * @property-read int              $hoursInMonth                                                                      The number of hours contained in the current month
 * @property-read int              $hoursInQuarter                                                                    The number of hours contained in the current quarter
 * @property-read int              $hoursInWeek                                                                       The number of hours contained in the current week
 * @property-read int              $hoursInYear                                                                       The number of hours contained in the current year
 * @property-read int              $microsecondsInCentury                                                             The number of microseconds contained in the current century
 * @property-read int              $microsecondsInDay                                                                 The number of microseconds contained in the current day
 * @property-read int              $microsecondsInDecade                                                              The number of microseconds contained in the current decade
 * @property-read int              $microsecondsInHour                                                                The number of microseconds contained in the current hour
 * @property-read int              $microsecondsInMillennium                                                          The number of microseconds contained in the current millennium
 * @property-read int              $microsecondsInMillisecond                                                         The number of microseconds contained in the current millisecond
 * @property-read int              $microsecondsInMinute                                                              The number of microseconds contained in the current minute
 * @property-read int              $microsecondsInMonth                                                               The number of microseconds contained in the current month
 * @property-read int              $microsecondsInQuarter                                                             The number of microseconds contained in the current quarter
 * @property-read int              $microsecondsInSecond                                                              The number of microseconds contained in the current second
 * @property-read int              $microsecondsInWeek                                                                The number of microseconds contained in the current week
 * @property-read int              $microsecondsInYear                                                                The number of microseconds contained in the current year
 * @property-read int              $millisecondsInCentury                                                             The number of milliseconds contained in the current century
 * @property-read int              $millisecondsInDay                                                                 The number of milliseconds contained in the current day
 * @property-read int              $millisecondsInDecade                                                              The number of milliseconds contained in the current decade
 * @property-read int              $millisecondsInHour                                                                The number of milliseconds contained in the current hour
 * @property-read int              $millisecondsInMillennium                                                          The number of milliseconds contained in the current millennium
 * @property-read int              $millisecondsInMinute                                                              The number of milliseconds contained in the current minute
 * @property-read int              $millisecondsInMonth                                                               The number of milliseconds contained in the current month
 * @property-read int              $millisecondsInQuarter                                                             The number of milliseconds contained in the current quarter
 * @property-read int              $millisecondsInSecond                                                              The number of milliseconds contained in the current second
 * @property-read int              $millisecondsInWeek                                                                The number of milliseconds contained in the current week
 * @property-read int              $millisecondsInYear                                                                The number of milliseconds contained in the current year
 * @property-read int              $minutesInCentury                                                                  The number of minutes contained in the current century
 * @property-read int              $minutesInDay                                                                      The number of minutes contained in the current day
 * @property-read int              $minutesInDecade                                                                   The number of minutes contained in the current decade
 * @property-read int              $minutesInHour                                                                     The number of minutes contained in the current hour
 * @property-read int              $minutesInMillennium                                                               The number of minutes contained in the current millennium
 * @property-read int              $minutesInMonth                                                                    The number of minutes contained in the current month
 * @property-read int              $minutesInQuarter                                                                  The number of minutes contained in the current quarter
 * @property-read int              $minutesInWeek                                                                     The number of minutes contained in the current week
 * @property-read int              $minutesInYear                                                                     The number of minutes contained in the current year
 * @property-read int              $monthsInCentury                                                                   The number of months contained in the current century
 * @property-read int              $monthsInDecade                                                                    The number of months contained in the current decade
 * @property-read int              $monthsInMillennium                                                                The number of months contained in the current millennium
 * @property-read int              $monthsInQuarter                                                                   The number of months contained in the current quarter
 * @property-read int              $monthsInYear                                                                      The number of months contained in the current year
 * @property-read int              $quartersInCentury                                                                 The number of quarters contained in the current century
 * @property-read int              $quartersInDecade                                                                  The number of quarters contained in the current decade
 * @property-read int              $quartersInMillennium                                                              The number of quarters contained in the current millennium
 * @property-read int              $quartersInYear                                                                    The number of quarters contained in the current year
 * @property-read int              $secondsInCentury                                                                  The number of seconds contained in the current century
 * @property-read int              $secondsInDay                                                                      The number of seconds contained in the current day
 * @property-read int              $secondsInDecade                                                                   The number of seconds contained in the current decade
 * @property-read int              $secondsInHour                                                                     The number of seconds contained in the current hour
 * @property-read int              $secondsInMillennium                                                               The number of seconds contained in the current millennium
 * @property-read int              $secondsInMinute                                                                   The number of seconds contained in the current minute
 * @property-read int              $secondsInMonth                                                                    The number of seconds contained in the current month
 * @property-read int              $secondsInQuarter                                                                  The number of seconds contained in the current quarter
 * @property-read int              $secondsInWeek                                                                     The number of seconds contained in the current week
 * @property-read int              $secondsInYear                                                                     The number of seconds contained in the current year
 * @property-read int              $weeksInCentury                                                                    The number of weeks contained in the current century
 * @property-read int              $weeksInDecade                                                                     The number of weeks contained in the current decade
 * @property-read int              $weeksInMillennium                                                                 The number of weeks contained in the current millennium
 * @property-read int              $weeksInMonth                                                                      The number of weeks contained in the current month
 * @property-read int              $weeksInQuarter                                                                    The number of weeks contained in the current quarter
 * @property-read int              $weeksInYear                                                                       51 through 53
 * @property-read int              $yearsInCentury                                                                    The number of years contained in the current century
 * @property-read int              $yearsInDecade                                                                     The number of years contained in the current decade
 * @property-read int              $yearsInMillennium                                                                 The number of years contained in the current millennium
 *
 * @method        bool             isUtc()                                                                            Check if the current instance has UTC timezone. (Both isUtc and isUTC cases are valid.)
 * @method        bool             isLocal()                                                                          Check if the current instance has non-UTC timezone.
 * @method        bool             isValid()                                                                          Check if the current instance is a valid date.
 * @method        bool             isDST()                                                                            Check if the current instance is in a daylight saving time.
 * @method        bool             isSunday()                                                                         Checks if the instance day is sunday.
 * @method        bool             isMonday()                                                                         Checks if the instance day is monday.
 * @method        bool             isTuesday()                                                                        Checks if the instance day is tuesday.
 * @method        bool             isWednesday()                                                                      Checks if the instance day is wednesday.
 * @method        bool             isThursday()                                                                       Checks if the instance day is thursday.
 * @method        bool             isFriday()                                                                         Checks if the instance day is friday.
 * @method        bool             isSaturday()                                                                       Checks if the instance day is saturday.
 * @method        bool             isSameYear(DateTimeInterface|string $date)                                         Checks if the given date is in the same year as the instance. If null passed, compare to now (with the same timezone).
 * @method        bool             isCurrentYear()                                                                    Checks if the instance is in the same year as the current moment.
 * @method        bool             isNextYear()                                                                       Checks if the instance is in the same year as the current moment next year.
 * @method        bool             isLastYear()                                                                       Checks if the instance is in the same year as the current moment last year.
 * @method        bool             isCurrentMonth()                                                                   Checks if the instance is in the same month as the current moment.
 * @method        bool             isNextMonth()                                                                      Checks if the instance is in the same month as the current moment next month.
 * @method        bool             isLastMonth()                                                                      Checks if the instance is in the same month as the current moment last month.
 * @method        bool             isSameWeek(DateTimeInterface|string $date)                                         Checks if the given date is in the same week as the instance. If null passed, compare to now (with the same timezone).
 * @method        bool             isCurrentWeek()                                                                    Checks if the instance is in the same week as the current moment.
 * @method        bool             isNextWeek()                                                                       Checks if the instance is in the same week as the current moment next week.
 * @method        bool             isLastWeek()                                                                       Checks if the instance is in the same week as the current moment last week.
 * @method        bool             isSameDay(DateTimeInterface|string $date)                                          Checks if the given date is in the same day as the instance. If null passed, compare to now (with the same timezone).
 * @method        bool             isCurrentDay()                                                                     Checks if the instance is in the same day as the current moment.
 * @method        bool             isNextDay()                                                                        Checks if the instance is in the same day as the current moment next day.
 * @method        bool             isLastDay()                                                                        Checks if the instance is in the same day as the current moment last day.
 * @method        bool             isSameHour(DateTimeInterface|string $date)                                         Checks if the given date is in the same hour as the instance. If null passed, compare to now (with the same timezone).
 * @method        bool             isCurrentHour()                                                                    Checks if the instance is in the same hour as the current moment.
 * @method        bool             isNextHour()                                                                       Checks if the instance is in the same hour as the current moment next hour.
 * @method        bool             isLastHour()                                                                       Checks if the instance is in the same hour as the current moment last hour.
 * @method        bool             isSameMinute(DateTimeInterface|string $date)                                       Checks if the given date is in the same minute as the instance. If null passed, compare to now (with the same timezone).
 * @method        bool             isCurrentMinute()                                                                  Checks if the instance is in the same minute as the current moment.
 * @method        bool             isNextMinute()                                                                     Checks if the instance is in the same minute as the current moment next minute.
 * @method        bool             isLastMinute()                                                                     Checks if the instance is in the same minute as the current moment last minute.
 * @method        bool             isSameSecond(DateTimeInterface|string $date)                                       Checks if the given date is in the same second as the instance. If null passed, compare to now (with the same timezone).
 * @method        bool             isCurrentSecond()                                                                  Checks if the instance is in the same second as the current moment.
 * @method        bool             isNextSecond()                                                                     Checks if the instance is in the same second as the current moment next second.
 * @method        bool             isLastSecond()                                                                     Checks if the instance is in the same second as the current moment last second.
 * @method        bool             isSameMilli(DateTimeInterface|string $date)                                        Checks if the given date is in the same millisecond as the instance. If null passed, compare to now (with the same timezone).
 * @method        bool             isCurrentMilli()                                                                   Checks if the instance is in the same millisecond as the current moment.
 * @method        bool             isNextMilli()                                                                      Checks if the instance is in the same millisecond as the current moment next millisecond.
 * @method        bool             isLastMilli()                                                                      Checks if the instance is in the same millisecond as the current moment last millisecond.
 * @method        bool             isSameMillisecond(DateTimeInterface|string $date)                                  Checks if the given date is in the same millisecond as the instance. If null passed, compare to now (with the same timezone).
 * @method        bool             isCurrentMillisecond()                                                             Checks if the instance is in the same millisecond as the current moment.
 * @method        bool             isNextMillisecond()                                                                Checks if the instance is in the same millisecond as the current moment next millisecond.
 * @method        bool             isLastMillisecond()                                                                Checks if the instance is in the same millisecond as the current moment last millisecond.
 * @method        bool             isSameMicro(DateTimeInterface|string $date)                                        Checks if the given date is in the same microsecond as the instance. If null passed, compare to now (with the same timezone).
 * @method        bool             isCurrentMicro()                                                                   Checks if the instance is in the same microsecond as the current moment.
 * @method        bool             isNextMicro()                                                                      Checks if the instance is in the same microsecond as the current moment next microsecond.
 * @method        bool             isLastMicro()                                                                      Checks if the instance is in the same microsecond as the current moment last microsecond.
 * @method        bool             isSameMicrosecond(DateTimeInterface|string $date)                                  Checks if the given date is in the same microsecond as the instance. If null passed, compare to now (with the same timezone).
 * @method        bool             isCurrentMicrosecond()                                                             Checks if the instance is in the same microsecond as the current moment.
 * @method        bool             isNextMicrosecond()                                                                Checks if the instance is in the same microsecond as the current moment next microsecond.
 * @method        bool             isLastMicrosecond()                                                                Checks if the instance is in the same microsecond as the current moment last microsecond.
 * @method        bool             isSameDecade(DateTimeInterface|string $date)                                       Checks if the given date is in the same decade as the instance. If null passed, compare to now (with the same timezone).
 * @method        bool             isCurrentDecade()                                                                  Checks if the instance is in the same decade as the current moment.
 * @method        bool             isNextDecade()                                                                     Checks if the instance is in the same decade as the current moment next decade.
 * @method        bool             isLastDecade()                                                                     Checks if the instance is in the same decade as the current moment last decade.
 * @method        bool             isSameCentury(DateTimeInterface|string $date)                                      Checks if the given date is in the same century as the instance. If null passed, compare to now (with the same timezone).
 * @method        bool             isCurrentCentury()                                                                 Checks if the instance is in the same century as the current moment.
 * @method        bool             isNextCentury()                                                                    Checks if the instance is in the same century as the current moment next century.
 * @method        bool             isLastCentury()                                                                    Checks if the instance is in the same century as the current moment last century.
 * @method        bool             isSameMillennium(DateTimeInterface|string $date)                                   Checks if the given date is in the same millennium as the instance. If null passed, compare to now (with the same timezone).
 * @method        bool             isCurrentMillennium()                                                              Checks if the instance is in the same millennium as the current moment.
 * @method        bool             isNextMillennium()                                                                 Checks if the instance is in the same millennium as the current moment next millennium.
 * @method        bool             isLastMillennium()                                                                 Checks if the instance is in the same millennium as the current moment last millennium.
 * @method        bool             isCurrentQuarter()                                                                 Checks if the instance is in the same quarter as the current moment.
 * @method        bool             isNextQuarter()                                                                    Checks if the instance is in the same quarter as the current moment next quarter.
 * @method        bool             isLastQuarter()                                                                    Checks if the instance is in the same quarter as the current moment last quarter.
 * @method        $this            years(int $value)                                                                  Set current instance year to the given value.
 * @method        $this            year(int $value)                                                                   Set current instance year to the given value.
 * @method        $this            setYears(int $value)                                                               Set current instance year to the given value.
 * @method        $this            setYear(int $value)                                                                Set current instance year to the given value.
 * @method        $this            months(Month|int $value)                                                           Set current instance month to the given value.
 * @method        $this            month(Month|int $value)                                                            Set current instance month to the given value.
 * @method        $this            setMonths(Month|int $value)                                                        Set current instance month to the given value.
 * @method        $this            setMonth(Month|int $value)                                                         Set current instance month to the given value.
 * @method        $this            days(int $value)                                                                   Set current instance day to the given value.
 * @method        $this            day(int $value)                                                                    Set current instance day to the given value.
 * @method        $this            setDays(int $value)                                                                Set current instance day to the given value.
 * @method        $this            setDay(int $value)                                                                 Set current instance day to the given value.
 * @method        $this            hours(int $value)                                                                  Set current instance hour to the given value.
 * @method        $this            hour(int $value)                                                                   Set current instance hour to the given value.
 * @method        $this            setHours(int $value)                                                               Set current instance hour to the given value.
 * @method        $this            setHour(int $value)                                                                Set current instance hour to the given value.
 * @method        $this            minutes(int $value)                                                                Set current instance minute to the given value.
 * @method        $this            minute(int $value)                                                                 Set current instance minute to the given value.
 * @method        $this            setMinutes(int $value)                                                             Set current instance minute to the given value.
 * @method        $this            setMinute(int $value)                                                              Set current instance minute to the given value.
 * @method        $this            seconds(int $value)                                                                Set current instance second to the given value.
 * @method        $this            second(int $value)                                                                 Set current instance second to the given value.
 * @method        $this            setSeconds(int $value)                                                             Set current instance second to the given value.
 * @method        $this            setSecond(int $value)                                                              Set current instance second to the given value.
 * @method        $this            millis(int $value)                                                                 Set current instance millisecond to the given value.
 * @method        $this            milli(int $value)                                                                  Set current instance millisecond to the given value.
 * @method        $this            setMillis(int $value)                                                              Set current instance millisecond to the given value.
 * @method        $this            setMilli(int $value)                                                               Set current instance millisecond to the given value.
 * @method        $this            milliseconds(int $value)                                                           Set current instance millisecond to the given value.
 * @method        $this            millisecond(int $value)                                                            Set current instance millisecond to the given value.
 * @method        $this            setMilliseconds(int $value)                                                        Set current instance millisecond to the given value.
 * @method        $this            setMillisecond(int $value)                                                         Set current instance millisecond to the given value.
 * @method        $this            micros(int $value)                                                                 Set current instance microsecond to the given value.
 * @method        $this            micro(int $value)                                                                  Set current instance microsecond to the given value.
 * @method        $this            setMicros(int $value)                                                              Set current instance microsecond to the given value.
 * @method        $this            setMicro(int $value)                                                               Set current instance microsecond to the given value.
 * @method        $this            microseconds(int $value)                                                           Set current instance microsecond to the given value.
 * @method        $this            microsecond(int $value)                                                            Set current instance microsecond to the given value.
 * @method        $this            setMicroseconds(int $value)                                                        Set current instance microsecond to the given value.
 * @method        $this            setMicrosecond(int $value)                                                         Set current instance microsecond to the given value.
 * @method        $this            addYears(int|float $value = 1)                                                     Add years (the $value count passed in) to the instance (using date interval).
 * @method        $this            addYear()                                                                          Add one year to the instance (using date interval).
 * @method        $this            subYears(int|float $value = 1)                                                     Sub years (the $value count passed in) to the instance (using date interval).
 * @method        $this            subYear()                                                                          Sub one year to the instance (using date interval).
 * @method        $this            addYearsWithOverflow(int|float $value = 1)                                         Add years (the $value count passed in) to the instance (using date interval) with overflow explicitly allowed.
 * @method        $this            addYearWithOverflow()                                                              Add one year to the instance (using date interval) with overflow explicitly allowed.
 * @method        $this            subYearsWithOverflow(int|float $value = 1)                                         Sub years (the $value count passed in) to the instance (using date interval) with overflow explicitly allowed.
 * @method        $this            subYearWithOverflow()                                                              Sub one year to the instance (using date interval) with overflow explicitly allowed.
 * @method        $this            addYearsWithoutOverflow(int|float $value = 1)                                      Add years (the $value count passed in) to the instance (using date interval) with overflow explicitly forbidden.
 * @method        $this            addYearWithoutOverflow()                                                           Add one year to the instance (using date interval) with overflow explicitly forbidden.
 * @method        $this            subYearsWithoutOverflow(int|float $value = 1)                                      Sub years (the $value count passed in) to the instance (using date interval) with overflow explicitly forbidden.
 * @method        $this            subYearWithoutOverflow()                                                           Sub one year to the instance (using date interval) with overflow explicitly forbidden.
 * @method        $this            addYearsWithNoOverflow(int|float $value = 1)                                       Add years (the $value count passed in) to the instance (using date interval) with overflow explicitly forbidden.
 * @method        $this            addYearWithNoOverflow()                                                            Add one year to the instance (using date interval) with overflow explicitly forbidden.
 * @method        $this            subYearsWithNoOverflow(int|float $value = 1)                                       Sub years (the $value count passed in) to the instance (using date interval) with overflow explicitly forbidden.
 * @method        $this            subYearWithNoOverflow()                                                            Sub one year to the instance (using date interval) with overflow explicitly forbidden.
 * @method        $this            addYearsNoOverflow(int|float $value = 1)                                           Add years (the $value count passed in) to the instance (using date interval) with overflow explicitly forbidden.
 * @method        $this            addYearNoOverflow()                                                                Add one year to the instance (using date interval) with overflow explicitly forbidden.
 * @method        $this            subYearsNoOverflow(int|float $value = 1)                                           Sub years (the $value count passed in) to the instance (using date interval) with overflow explicitly forbidden.
 * @method        $this            subYearNoOverflow()                                                                Sub one year to the instance (using date interval) with overflow explicitly forbidden.
 * @method        $this            addMonths(int|float $value = 1)                                                    Add months (the $value count passed in) to the instance (using date interval).
 * @method        $this            addMonth()                                                                         Add one month to the instance (using date interval).
 * @method        $this            subMonths(int|float $value = 1)                                                    Sub months (the $value count passed in) to the instance (using date interval).
 * @method        $this            subMonth()                                                                         Sub one month to the instance (using date interval).
 * @method        $this            addMonthsWithOverflow(int|float $value = 1)                                        Add months (the $value count passed in) to the instance (using date interval) with overflow explicitly allowed.
 * @method        $this            addMonthWithOverflow()                                                             Add one month to the instance (using date interval) with overflow explicitly allowed.
 * @method        $this            subMonthsWithOverflow(int|float $value = 1)                                        Sub months (the $value count passed in) to the instance (using date interval) with overflow explicitly allowed.
 * @method        $this            subMonthWithOverflow()                                                             Sub one month to the instance (using date interval) with overflow explicitly allowed.
 * @method        $this            addMonthsWithoutOverflow(int|float $value = 1)                                     Add months (the $value count passed in) to the instance (using date interval) with overflow explicitly forbidden.
 * @method        $this            addMonthWithoutOverflow()                                                          Add one month to the instance (using date interval) with overflow explicitly forbidden.
 * @method        $this            subMonthsWithoutOverflow(int|float $value = 1)                                     Sub months (the $value count passed in) to the instance (using date interval) with overflow explicitly forbidden.
 * @method        $this            subMonthWithoutOverflow()                                                          Sub one month to the instance (using date interval) with overflow explicitly forbidden.
 * @method        $this            addMonthsWithNoOverflow(int|float $value = 1)                                      Add months (the $value count passed in) to the instance (using date interval) with overflow explicitly forbidden.
 * @method        $this            addMonthWithNoOverflow()                                                           Add one month to the instance (using date interval) with overflow explicitly forbidden.
 * @method        $this            subMonthsWithNoOverflow(int|float $value = 1)                                      Sub months (the $value count passed in) to the instance (using date interval) with overflow explicitly forbidden.
 * @method        $this            subMonthWithNoOverflow()                                                           Sub one month to the instance (using date interval) with overflow explicitly forbidden.
 * @method        $this            addMonthsNoOverflow(int|float $value = 1)                                          Add months (the $value count passed in) to the instance (using date interval) with overflow explicitly forbidden.
 * @method        $this            addMonthNoOverflow()                                                               Add one month to the instance (using date interval) with overflow explicitly forbidden.
 * @method        $this            subMonthsNoOverflow(int|float $value = 1)                                          Sub months (the $value count passed in) to the instance (using date interval) with overflow explicitly forbidden.
 * @method        $this            subMonthNoOverflow()                                                               Sub one month to the instance (using date interval) with overflow explicitly forbidden.
 * @method        $this            addDays(int|float $value = 1)                                                      Add days (the $value count passed in) to the instance (using date interval).
 * @method        $this            addDay()                                                                           Add one day to the instance (using date interval).
 * @method        $this            subDays(int|float $value = 1)                                                      Sub days (the $value count passed in) to the instance (using date interval).
 * @method        $this            subDay()                                                                           Sub one day to the instance (using date interval).
 * @method        $this            addHours(int|float $value = 1)                                                     Add hours (the $value count passed in) to the instance (using date interval).
 * @method        $this            addHour()                                                                          Add one hour to the instance (using date interval).
 * @method        $this            subHours(int|float $value = 1)                                                     Sub hours (the $value count passed in) to the instance (using date interval).
 * @method        $this            subHour()                                                                          Sub one hour to the instance (using date interval).
 * @method        $this            addMinutes(int|float $value = 1)                                                   Add minutes (the $value count passed in) to the instance (using date interval).
 * @method        $this            addMinute()                                                                        Add one minute to the instance (using date interval).
 * @method        $this            subMinutes(int|float $value = 1)                                                   Sub minutes (the $value count passed in) to the instance (using date interval).
 * @method        $this            subMinute()                                                                        Sub one minute to the instance (using date interval).
 * @method        $this            addSeconds(int|float $value = 1)                                                   Add seconds (the $value count passed in) to the instance (using date interval).
 * @method        $this            addSecond()                                                                        Add one second to the instance (using date interval).
 * @method        $this            subSeconds(int|float $value = 1)                                                   Sub seconds (the $value count passed in) to the instance (using date interval).
 * @method        $this            subSecond()                                                                        Sub one second to the instance (using date interval).
 * @method        $this            addMillis(int|float $value = 1)                                                    Add milliseconds (the $value count passed in) to the instance (using date interval).
 * @method        $this            addMilli()                                                                         Add one millisecond to the instance (using date interval).
 * @method        $this            subMillis(int|float $value = 1)                                                    Sub milliseconds (the $value count passed in) to the instance (using date interval).
 * @method        $this            subMilli()                                                                         Sub one millisecond to the instance (using date interval).
 * @method        $this            addMilliseconds(int|float $value = 1)                                              Add milliseconds (the $value count passed in) to the instance (using date interval).
 * @method        $this            addMillisecond()                                                                   Add one millisecond to the instance (using date interval).
 * @method        $this            subMilliseconds(int|float $value = 1)                                              Sub milliseconds (the $value count passed in) to the instance (using date interval).
 * @method        $this            subMillisecond()                                                                   Sub one millisecond to the instance (using date interval).
 * @method        $this            addMicros(int|float $value = 1)                                                    Add microseconds (the $value count passed in) to the instance (using date interval).
 * @method        $this            addMicro()                                                                         Add one microsecond to the instance (using date interval).
 * @method        $this            subMicros(int|float $value = 1)                                                    Sub microseconds (the $value count passed in) to the instance (using date interval).
 * @method        $this            subMicro()                                                                         Sub one microsecond to the instance (using date interval).
 * @method        $this            addMicroseconds(int|float $value = 1)                                              Add microseconds (the $value count passed in) to the instance (using date interval).
 * @method        $this            addMicrosecond()                                                                   Add one microsecond to the instance (using date interval).
 * @method        $this            subMicroseconds(int|float $value = 1)                                              Sub microseconds (the $value count passed in) to the instance (using date interval).
 * @method        $this            subMicrosecond()                                                                   Sub one microsecond to the instance (using date interval).
 * @method        $this            addMillennia(int|float $value = 1)                                                 Add millennia (the $value count passed in) to the instance (using date interval).
 * @method        $this            addMillennium()                                                                    Add one millennium to the instance (using date interval).
 * @method        $this            subMillennia(int|float $value = 1)                                                 Sub millennia (the $value count passed in) to the instance (using date interval).
 * @method        $this            subMillennium()                                                                    Sub one millennium to the instance (using date interval).
 * @method        $this            addMillenniaWithOverflow(int|float $value = 1)                                     Add millennia (the $value count passed in) to the instance (using date interval) with overflow explicitly allowed.
 * @method        $this            addMillenniumWithOverflow()                                                        Add one millennium to the instance (using date interval) with overflow explicitly allowed.
 * @method        $this            subMillenniaWithOverflow(int|float $value = 1)                                     Sub millennia (the $value count passed in) to the instance (using date interval) with overflow explicitly allowed.
 * @method        $this            subMillenniumWithOverflow()                                                        Sub one millennium to the instance (using date interval) with overflow explicitly allowed.
 * @method        $this            addMillenniaWithoutOverflow(int|float $value = 1)                                  Add millennia (the $value count passed in) to the instance (using date interval) with overflow explicitly forbidden.
 * @method        $this            addMillenniumWithoutOverflow()                                                     Add one millennium to the instance (using date interval) with overflow explicitly forbidden.
 * @method        $this            subMillenniaWithoutOverflow(int|float $value = 1)                                  Sub millennia (the $value count passed in) to the instance (using date interval) with overflow explicitly forbidden.
 * @method        $this            subMillenniumWithoutOverflow()                                                     Sub one millennium to the instance (using date interval) with overflow explicitly forbidden.
 * @method        $this            addMillenniaWithNoOverflow(int|float $value = 1)                                   Add millennia (the $value count passed in) to the instance (using date interval) with overflow explicitly forbidden.
 * @method        $this            addMillenniumWithNoOverflow()                                                      Add one millennium to the instance (using date interval) with overflow explicitly forbidden.
 * @method        $this            subMillenniaWithNoOverflow(int|float $value = 1)                                   Sub millennia (the $value count passed in) to the instance (using date interval) with overflow explicitly forbidden.
 * @method        $this            subMillenniumWithNoOverflow()                                                      Sub one millennium to the instance (using date interval) with overflow explicitly forbidden.
 * @method        $this            addMillenniaNoOverflow(int|float $value = 1)                                       Add millennia (the $value count passed in) to the instance (using date interval) with overflow explicitly forbidden.
 * @method        $this            addMillenniumNoOverflow()                                                          Add one millennium to the instance (using date interval) with overflow explicitly forbidden.
 * @method        $this            subMillenniaNoOverflow(int|float $value = 1)                                       Sub millennia (the $value count passed in) to the instance (using date interval) with overflow explicitly forbidden.
 * @method        $this            subMillenniumNoOverflow()                                                          Sub one millennium to the instance (using date interval) with overflow explicitly forbidden.
 * @method        $this            addCenturies(int|float $value = 1)                                                 Add centuries (the $value count passed in) to the instance (using date interval).
 * @method        $this            addCentury()                                                                       Add one century to the instance (using date interval).
 * @method        $this            subCenturies(int|float $value = 1)                                                 Sub centuries (the $value count passed in) to the instance (using date interval).
 * @method        $this            subCentury()                                                                       Sub one century to the instance (using date interval).
 * @method        $this            addCenturiesWithOverflow(int|float $value = 1)                                     Add centuries (the $value count passed in) to the instance (using date interval) with overflow explicitly allowed.
 * @method        $this            addCenturyWithOverflow()                                                           Add one century to the instance (using date interval) with overflow explicitly allowed.
 * @method        $this            subCenturiesWithOverflow(int|float $value = 1)                                     Sub centuries (the $value count passed in) to the instance (using date interval) with overflow explicitly allowed.
 * @method        $this            subCenturyWithOverflow()                                                           Sub one century to the instance (using date interval) with overflow explicitly allowed.
 * @method        $this            addCenturiesWithoutOverflow(int|float $value = 1)                                  Add centuries (the $value count passed in) to the instance (using date interval) with overflow explicitly forbidden.
 * @method        $this            addCenturyWithoutOverflow()                                                        Add one century to the instance (using date interval) with overflow explicitly forbidden.
 * @method        $this            subCenturiesWithoutOverflow(int|float $value = 1)                                  Sub centuries (the $value count passed in) to the instance (using date interval) with overflow explicitly forbidden.
 * @method        $this            subCenturyWithoutOverflow()                                                        Sub one century to the instance (using date interval) with overflow explicitly forbidden.
 * @method        $this            addCenturiesWithNoOverflow(int|float $value = 1)                                   Add centuries (the $value count passed in) to the instance (using date interval) with overflow explicitly forbidden.
 * @method        $this            addCenturyWithNoOverflow()                                                         Add one century to the instance (using date interval) with overflow explicitly forbidden.
 * @method        $this            subCenturiesWithNoOverflow(int|float $value = 1)                                   Sub centuries (the $value count passed in) to the instance (using date interval) with overflow explicitly forbidden.
 * @method        $this            subCenturyWithNoOverflow()                                                         Sub one century to the instance (using date interval) with overflow explicitly forbidden.
 * @method        $this            addCenturiesNoOverflow(int|float $value = 1)                                       Add centuries (the $value count passed in) to the instance (using date interval) with overflow explicitly forbidden.
 * @method        $this            addCenturyNoOverflow()                                                             Add one century to the instance (using date interval) with overflow explicitly forbidden.
 * @method        $this            subCenturiesNoOverflow(int|float $value = 1)                                       Sub centuries (the $value count passed in) to the instance (using date interval) with overflow explicitly forbidden.
 * @method        $this            subCenturyNoOverflow()                                                             Sub one century to the instance (using date interval) with overflow explicitly forbidden.
 * @method        $this            addDecades(int|float $value = 1)                                                   Add decades (the $value count passed in) to the instance (using date interval).
 * @method        $this            addDecade()                                                                        Add one decade to the instance (using date interval).
 * @method        $this            subDecades(int|float $value = 1)                                                   Sub decades (the $value count passed in) to the instance (using date interval).
 * @method        $this            subDecade()                                                                        Sub one decade to the instance (using date interval).
 * @method        $this            addDecadesWithOverflow(int|float $value = 1)                                       Add decades (the $value count passed in) to the instance (using date interval) with overflow explicitly allowed.
 * @method        $this            addDecadeWithOverflow()                                                            Add one decade to the instance (using date interval) with overflow explicitly allowed.
 * @method        $this            subDecadesWithOverflow(int|float $value = 1)                                       Sub decades (the $value count passed in) to the instance (using date interval) with overflow explicitly allowed.
 * @method        $this            subDecadeWithOverflow()                                                            Sub one decade to the instance (using date interval) with overflow explicitly allowed.
 * @method        $this            addDecadesWithoutOverflow(int|float $value = 1)                                    Add decades (the $value count passed in) to the instance (using date interval) with overflow explicitly forbidden.
 * @method        $this            addDecadeWithoutOverflow()                                                         Add one decade to the instance (using date interval) with overflow explicitly forbidden.
 * @method        $this            subDecadesWithoutOverflow(int|float $value = 1)                                    Sub decades (the $value count passed in) to the instance (using date interval) with overflow explicitly forbidden.
 * @method        $this            subDecadeWithoutOverflow()                                                         Sub one decade to the instance (using date interval) with overflow explicitly forbidden.
 * @method        $this            addDecadesWithNoOverflow(int|float $value = 1)                                     Add decades (the $value count passed in) to the instance (using date interval) with overflow explicitly forbidden.
 * @method        $this            addDecadeWithNoOverflow()                                                          Add one decade to the instance (using date interval) with overflow explicitly forbidden.
 * @method        $this            subDecadesWithNoOverflow(int|float $value = 1)                                     Sub decades (the $value count passed in) to the instance (using date interval) with overflow explicitly forbidden.
 * @method        $this            subDecadeWithNoOverflow()                                                          Sub one decade to the instance (using date interval) with overflow explicitly forbidden.
 * @method        $this            addDecadesNoOverflow(int|float $value = 1)                                         Add decades (the $value count passed in) to the instance (using date interval) with overflow explicitly forbidden.
 * @method        $this            addDecadeNoOverflow()                                                              Add one decade to the instance (using date interval) with overflow explicitly forbidden.
 * @method        $this            subDecadesNoOverflow(int|float $value = 1)                                         Sub decades (the $value count passed in) to the instance (using date interval) with overflow explicitly forbidden.
 * @method        $this            subDecadeNoOverflow()                                                              Sub one decade to the instance (using date interval) with overflow explicitly forbidden.
 * @method        $this            addQuarters(int|float $value = 1)                                                  Add quarters (the $value count passed in) to the instance (using date interval).
 * @method        $this            addQuarter()                                                                       Add one quarter to the instance (using date interval).
 * @method        $this            subQuarters(int|float $value = 1)                                                  Sub quarters (the $value count passed in) to the instance (using date interval).
 * @method        $this            subQuarter()                                                                       Sub one quarter to the instance (using date interval).
 * @method        $this            addQuartersWithOverflow(int|float $value = 1)                                      Add quarters (the $value count passed in) to the instance (using date interval) with overflow explicitly allowed.
 * @method        $this            addQuarterWithOverflow()                                                           Add one quarter to the instance (using date interval) with overflow explicitly allowed.
 * @method        $this            subQuartersWithOverflow(int|float $value = 1)                                      Sub quarters (the $value count passed in) to the instance (using date interval) with overflow explicitly allowed.
 * @method        $this            subQuarterWithOverflow()                                                           Sub one quarter to the instance (using date interval) with overflow explicitly allowed.
 * @method        $this            addQuartersWithoutOverflow(int|float $value = 1)                                   Add quarters (the $value count passed in) to the instance (using date interval) with overflow explicitly forbidden.
 * @method        $this            addQuarterWithoutOverflow()                                                        Add one quarter to the instance (using date interval) with overflow explicitly forbidden.
 * @method        $this            subQuartersWithoutOverflow(int|float $value = 1)                                   Sub quarters (the $value count passed in) to the instance (using date interval) with overflow explicitly forbidden.
 * @method        $this            subQuarterWithoutOverflow()                                                        Sub one quarter to the instance (using date interval) with overflow explicitly forbidden.
 * @method        $this            addQuartersWithNoOverflow(int|float $value = 1)                                    Add quarters (the $value count passed in) to the instance (using date interval) with overflow explicitly forbidden.
 * @method        $this            addQuarterWithNoOverflow()                                                         Add one quarter to the instance (using date interval) with overflow explicitly forbidden.
 * @method        $this            subQuartersWithNoOverflow(int|float $value = 1)                                    Sub quarters (the $value count passed in) to the instance (using date interval) with overflow explicitly forbidden.
 * @method        $this            subQuarterWithNoOverflow()                                                         Sub one quarter to the instance (using date interval) with overflow explicitly forbidden.
 * @method        $this            addQuartersNoOverflow(int|float $value = 1)                                        Add quarters (the $value count passed in) to the instance (using date interval) with overflow explicitly forbidden.
 * @method        $this            addQuarterNoOverflow()                                                             Add one quarter to the instance (using date interval) with overflow explicitly forbidden.
 * @method        $this            subQuartersNoOverflow(int|float $value = 1)                                        Sub quarters (the $value count passed in) to the instance (using date interval) with overflow explicitly forbidden.
 * @method        $this            subQuarterNoOverflow()                                                             Sub one quarter to the instance (using date interval) with overflow explicitly forbidden.
 * @method        $this            addWeeks(int|float $value = 1)                                                     Add weeks (the $value count passed in) to the instance (using date interval).
 * @method        $this            addWeek()                                                                          Add one week to the instance (using date interval).
 * @method        $this            subWeeks(int|float $value = 1)                                                     Sub weeks (the $value count passed in) to the instance (using date interval).
 * @method        $this            subWeek()                                                                          Sub one week to the instance (using date interval).
 * @method        $this            addWeekdays(int|float $value = 1)                                                  Add weekdays (the $value count passed in) to the instance (using date interval).
 * @method        $this            addWeekday()                                                                       Add one weekday to the instance (using date interval).
 * @method        $this            subWeekdays(int|float $value = 1)                                                  Sub weekdays (the $value count passed in) to the instance (using date interval).
 * @method        $this            subWeekday()                                                                       Sub one weekday to the instance (using date interval).
 * @method        $this            addUTCMicros(int|float $value = 1)                                                 Add microseconds (the $value count passed in) to the instance (using timestamp).
 * @method        $this            addUTCMicro()                                                                      Add one microsecond to the instance (using timestamp).
 * @method        $this            subUTCMicros(int|float $value = 1)                                                 Sub microseconds (the $value count passed in) to the instance (using timestamp).
 * @method        $this            subUTCMicro()                                                                      Sub one microsecond to the instance (using timestamp).
 * @method        CarbonPeriod     microsUntil($endDate = null, int|float $factor = 1)                                Return an iterable period from current date to given end (string, DateTime or Carbon instance) for each microsecond or every X microseconds if a factor is given.
 * @method        float            diffInUTCMicros(DateTimeInterface|string|null $date, bool $absolute = false)       Convert current and given date in UTC timezone and return a floating number of microseconds.
 * @method        $this            addUTCMicroseconds(int|float $value = 1)                                           Add microseconds (the $value count passed in) to the instance (using timestamp).
 * @method        $this            addUTCMicrosecond()                                                                Add one microsecond to the instance (using timestamp).
 * @method        $this            subUTCMicroseconds(int|float $value = 1)                                           Sub microseconds (the $value count passed in) to the instance (using timestamp).
 * @method        $this            subUTCMicrosecond()                                                                Sub one microsecond to the instance (using timestamp).
 * @method        CarbonPeriod     microsecondsUntil($endDate = null, int|float $factor = 1)                          Return an iterable period from current date to given end (string, DateTime or Carbon instance) for each microsecond or every X microseconds if a factor is given.
 * @method        float            diffInUTCMicroseconds(DateTimeInterface|string|null $date, bool $absolute = false) Convert current and given date in UTC timezone and return a floating number of microseconds.
 * @method        $this            addUTCMillis(int|float $value = 1)                                                 Add milliseconds (the $value count passed in) to the instance (using timestamp).
 * @method        $this            addUTCMilli()                                                                      Add one millisecond to the instance (using timestamp).
 * @method        $this            subUTCMillis(int|float $value = 1)                                                 Sub milliseconds (the $value count passed in) to the instance (using timestamp).
 * @method        $this            subUTCMilli()                                                                      Sub one millisecond to the instance (using timestamp).
 * @method        CarbonPeriod     millisUntil($endDate = null, int|float $factor = 1)                                Return an iterable period from current date to given end (string, DateTime or Carbon instance) for each millisecond or every X milliseconds if a factor is given.
 * @method        float            diffInUTCMillis(DateTimeInterface|string|null $date, bool $absolute = false)       Convert current and given date in UTC timezone and return a floating number of milliseconds.
 * @method        $this            addUTCMilliseconds(int|float $value = 1)                                           Add milliseconds (the $value count passed in) to the instance (using timestamp).
 * @method        $this            addUTCMillisecond()                                                                Add one millisecond to the instance (using timestamp).
 * @method        $this            subUTCMilliseconds(int|float $value = 1)                                           Sub milliseconds (the $value count passed in) to the instance (using timestamp).
 * @method        $this            subUTCMillisecond()                                                                Sub one millisecond to the instance (using timestamp).
 * @method        CarbonPeriod     millisecondsUntil($endDate = null, int|float $factor = 1)                          Return an iterable period from current date to given end (string, DateTime or Carbon instance) for each millisecond or every X milliseconds if a factor is given.
 * @method        float            diffInUTCMilliseconds(DateTimeInterface|string|null $date, bool $absolute = false) Convert current and given date in UTC timezone and return a floating number of milliseconds.
 * @method        $this            addUTCSeconds(int|float $value = 1)                                                Add seconds (the $value count passed in) to the instance (using timestamp).
 * @method        $this            addUTCSecond()                                                                     Add one second to the instance (using timestamp).
 * @method        $this            subUTCSeconds(int|float $value = 1)                                                Sub seconds (the $value count passed in) to the instance (using timestamp).
 * @method        $this            subUTCSecond()                                                                     Sub one second to the instance (using timestamp).
 * @method        CarbonPeriod     secondsUntil($endDate = null, int|float $factor = 1)                               Return an iterable period from current date to given end (string, DateTime or Carbon instance) for each second or every X seconds if a factor is given.
 * @method        float            diffInUTCSeconds(DateTimeInterface|string|null $date, bool $absolute = false)      Convert current and given date in UTC timezone and return a floating number of seconds.
 * @method        $this            addUTCMinutes(int|float $value = 1)                                                Add minutes (the $value count passed in) to the instance (using timestamp).
 * @method        $this            addUTCMinute()                                                                     Add one minute to the instance (using timestamp).
 * @method        $this            subUTCMinutes(int|float $value = 1)                                                Sub minutes (the $value count passed in) to the instance (using timestamp).
 * @method        $this            subUTCMinute()                                                                     Sub one minute to the instance (using timestamp).
 * @method        CarbonPeriod     minutesUntil($endDate = null, int|float $factor = 1)                               Return an iterable period from current date to given end (string, DateTime or Carbon instance) for each minute or every X minutes if a factor is given.
 * @method        float            diffInUTCMinutes(DateTimeInterface|string|null $date, bool $absolute = false)      Convert current and given date in UTC timezone and return a floating number of minutes.
 * @method        $this            addUTCHours(int|float $value = 1)                                                  Add hours (the $value count passed in) to the instance (using timestamp).
 * @method        $this            addUTCHour()                                                                       Add one hour to the instance (using timestamp).
 * @method        $this            subUTCHours(int|float $value = 1)                                                  Sub hours (the $value count passed in) to the instance (using timestamp).
 * @method        $this            subUTCHour()                                                                       Sub one hour to the instance (using timestamp).
 * @method        CarbonPeriod     hoursUntil($endDate = null, int|float $factor = 1)                                 Return an iterable period from current date to given end (string, DateTime or Carbon instance) for each hour or every X hours if a factor is given.
 * @method        float            diffInUTCHours(DateTimeInterface|string|null $date, bool $absolute = false)        Convert current and given date in UTC timezone and return a floating number of hours.
 * @method        $this            addUTCDays(int|float $value = 1)                                                   Add days (the $value count passed in) to the instance (using timestamp).
 * @method        $this            addUTCDay()                                                                        Add one day to the instance (using timestamp).
 * @method        $this            subUTCDays(int|float $value = 1)                                                   Sub days (the $value count passed in) to the instance (using timestamp).
 * @method        $this            subUTCDay()                                                                        Sub one day to the instance (using timestamp).
 * @method        CarbonPeriod     daysUntil($endDate = null, int|float $factor = 1)                                  Return an iterable period from current date to given end (string, DateTime or Carbon instance) for each day or every X days if a factor is given.
 * @method        float            diffInUTCDays(DateTimeInterface|string|null $date, bool $absolute = false)         Convert current and given date in UTC timezone and return a floating number of days.
 * @method        $this            addUTCWeeks(int|float $value = 1)                                                  Add weeks (the $value count passed in) to the instance (using timestamp).
 * @method        $this            addUTCWeek()                                                                       Add one week to the instance (using timestamp).
 * @method        $this            subUTCWeeks(int|float $value = 1)                                                  Sub weeks (the $value count passed in) to the instance (using timestamp).
 * @method        $this            subUTCWeek()                                                                       Sub one week to the instance (using timestamp).
 * @method        CarbonPeriod     weeksUntil($endDate = null, int|float $factor = 1)                                 Return an iterable period from current date to given end (string, DateTime or Carbon instance) for each week or every X weeks if a factor is given.
 * @method        float            diffInUTCWeeks(DateTimeInterface|string|null $date, bool $absolute = false)        Convert current and given date in UTC timezone and return a floating number of weeks.
 * @method        $this            addUTCMonths(int|float $value = 1)                                                 Add months (the $value count passed in) to the instance (using timestamp).
 * @method        $this            addUTCMonth()                                                                      Add one month to the instance (using timestamp).
 * @method        $this            subUTCMonths(int|float $value = 1)                                                 Sub months (the $value count passed in) to the instance (using timestamp).
 * @method        $this            subUTCMonth()                                                                      Sub one month to the instance (using timestamp).
 * @method        CarbonPeriod     monthsUntil($endDate = null, int|float $factor = 1)                                Return an iterable period from current date to given end (string, DateTime or Carbon instance) for each month or every X months if a factor is given.
 * @method        float            diffInUTCMonths(DateTimeInterface|string|null $date, bool $absolute = false)       Convert current and given date in UTC timezone and return a floating number of months.
 * @method        $this            addUTCQuarters(int|float $value = 1)                                               Add quarters (the $value count passed in) to the instance (using timestamp).
 * @method        $this            addUTCQuarter()                                                                    Add one quarter to the instance (using timestamp).
 * @method        $this            subUTCQuarters(int|float $value = 1)                                               Sub quarters (the $value count passed in) to the instance (using timestamp).
 * @method        $this            subUTCQuarter()                                                                    Sub one quarter to the instance (using timestamp).
 * @method        CarbonPeriod     quartersUntil($endDate = null, int|float $factor = 1)                              Return an iterable period from current date to given end (string, DateTime or Carbon instance) for each quarter or every X quarters if a factor is given.
 * @method        float            diffInUTCQuarters(DateTimeInterface|string|null $date, bool $absolute = false)     Convert current and given date in UTC timezone and return a floating number of quarters.
 * @method        $this            addUTCYears(int|float $value = 1)                                                  Add years (the $value count passed in) to the instance (using timestamp).
 * @method        $this            addUTCYear()                                                                       Add one year to the instance (using timestamp).
 * @method        $this            subUTCYears(int|float $value = 1)                                                  Sub years (the $value count passed in) to the instance (using timestamp).
 * @method        $this            subUTCYear()                                                                       Sub one year to the instance (using timestamp).
 * @method        CarbonPeriod     yearsUntil($endDate = null, int|float $factor = 1)                                 Return an iterable period from current date to given end (string, DateTime or Carbon instance) for each year or every X years if a factor is given.
 * @method        float            diffInUTCYears(DateTimeInterface|string|null $date, bool $absolute = false)        Convert current and given date in UTC timezone and return a floating number of years.
 * @method        $this            addUTCDecades(int|float $value = 1)                                                Add decades (the $value count passed in) to the instance (using timestamp).
 * @method        $this            addUTCDecade()                                                                     Add one decade to the instance (using timestamp).
 * @method        $this            subUTCDecades(int|float $value = 1)                                                Sub decades (the $value count passed in) to the instance (using timestamp).
 * @method        $this            subUTCDecade()                                                                     Sub one decade to the instance (using timestamp).
 * @method        CarbonPeriod     decadesUntil($endDate = null, int|float $factor = 1)                               Return an iterable period from current date to given end (string, DateTime or Carbon instance) for each decade or every X decades if a factor is given.
 * @method        float            diffInUTCDecades(DateTimeInterface|string|null $date, bool $absolute = false)      Convert current and given date in UTC timezone and return a floating number of decades.
 * @method        $this            addUTCCenturies(int|float $value = 1)                                              Add centuries (the $value count passed in) to the instance (using timestamp).
 * @method        $this            addUTCCentury()                                                                    Add one century to the instance (using timestamp).
 * @method        $this            subUTCCenturies(int|float $value = 1)                                              Sub centuries (the $value count passed in) to the instance (using timestamp).
 * @method        $this            subUTCCentury()                                                                    Sub one century to the instance (using timestamp).
 * @method        CarbonPeriod     centuriesUntil($endDate = null, int|float $factor = 1)                             Return an iterable period from current date to given end (string, DateTime or Carbon instance) for each century or every X centuries if a factor is given.
 * @method        float            diffInUTCCenturies(DateTimeInterface|string|null $date, bool $absolute = false)    Convert current and given date in UTC timezone and return a floating number of centuries.
 * @method        $this            addUTCMillennia(int|float $value = 1)                                              Add millennia (the $value count passed in) to the instance (using timestamp).
 * @method        $this            addUTCMillennium()                                                                 Add one millennium to the instance (using timestamp).
 * @method        $this            subUTCMillennia(int|float $value = 1)                                              Sub millennia (the $value count passed in) to the instance (using timestamp).
 * @method        $this            subUTCMillennium()                                                                 Sub one millennium to the instance (using timestamp).
 * @method        CarbonPeriod     millenniaUntil($endDate = null, int|float $factor = 1)                             Return an iterable period from current date to given end (string, DateTime or Carbon instance) for each millennium or every X millennia if a factor is given.
 * @method        float            diffInUTCMillennia(DateTimeInterface|string|null $date, bool $absolute = false)    Convert current and given date in UTC timezone and return a floating number of millennia.
 * @method        $this            roundYear(float $precision = 1, string $function = "round")                        Round the current instance year with given precision using the given function.
 * @method        $this            roundYears(float $precision = 1, string $function = "round")                       Round the current instance year with given precision using the given function.
 * @method        $this            floorYear(float $precision = 1)                                                    Truncate the current instance year with given precision.
 * @method        $this            floorYears(float $precision = 1)                                                   Truncate the current instance year with given precision.
 * @method        $this            ceilYear(float $precision = 1)                                                     Ceil the current instance year with given precision.
 * @method        $this            ceilYears(float $precision = 1)                                                    Ceil the current instance year with given precision.
 * @method        $this            roundMonth(float $precision = 1, string $function = "round")                       Round the current instance month with given precision using the given function.
 * @method        $this            roundMonths(float $precision = 1, string $function = "round")                      Round the current instance month with given precision using the given function.
 * @method        $this            floorMonth(float $precision = 1)                                                   Truncate the current instance month with given precision.
 * @method        $this            floorMonths(float $precision = 1)                                                  Truncate the current instance month with given precision.
 * @method        $this            ceilMonth(float $precision = 1)                                                    Ceil the current instance month with given precision.
 * @method        $this            ceilMonths(float $precision = 1)                                                   Ceil the current instance month with given precision.
 * @method        $this            roundDay(float $precision = 1, string $function = "round")                         Round the current instance day with given precision using the given function.
 * @method        $this            roundDays(float $precision = 1, string $function = "round")                        Round the current instance day with given precision using the given function.
 * @method        $this            floorDay(float $precision = 1)                                                     Truncate the current instance day with given precision.
 * @method        $this            floorDays(float $precision = 1)                                                    Truncate the current instance day with given precision.
 * @method        $this            ceilDay(float $precision = 1)                                                      Ceil the current instance day with given precision.
 * @method        $this            ceilDays(float $precision = 1)                                                     Ceil the current instance day with given precision.
 * @method        $this            roundHour(float $precision = 1, string $function = "round")                        Round the current instance hour with given precision using the given function.
 * @method        $this            roundHours(float $precision = 1, string $function = "round")                       Round the current instance hour with given precision using the given function.
 * @method        $this            floorHour(float $precision = 1)                                                    Truncate the current instance hour with given precision.
 * @method        $this            floorHours(float $precision = 1)                                                   Truncate the current instance hour with given precision.
 * @method        $this            ceilHour(float $precision = 1)                                                     Ceil the current instance hour with given precision.
 * @method        $this            ceilHours(float $precision = 1)                                                    Ceil the current instance hour with given precision.
 * @method        $this            roundMinute(float $precision = 1, string $function = "round")                      Round the current instance minute with given precision using the given function.
 * @method        $this            roundMinutes(float $precision = 1, string $function = "round")                     Round the current instance minute with given precision using the given function.
 * @method        $this            floorMinute(float $precision = 1)                                                  Truncate the current instance minute with given precision.
 * @method        $this            floorMinutes(float $precision = 1)                                                 Truncate the current instance minute with given precision.
 * @method        $this            ceilMinute(float $precision = 1)                                                   Ceil the current instance minute with given precision.
 * @method        $this            ceilMinutes(float $precision = 1)                                                  Ceil the current instance minute with given precision.
 * @method        $this            roundSecond(float $precision = 1, string $function = "round")                      Round the current instance second with given precision using the given function.
 * @method        $this            roundSeconds(float $precision = 1, string $function = "round")                     Round the current instance second with given precision using the given function.
 * @method        $this            floorSecond(float $precision = 1)                                                  Truncate the current instance second with given precision.
 * @method        $this            floorSeconds(float $precision = 1)                                                 Truncate the current instance second with given precision.
 * @method        $this            ceilSecond(float $precision = 1)                                                   Ceil the current instance second with given precision.
 * @method        $this            ceilSeconds(float $precision = 1)                                                  Ceil the current instance second with given precision.
 * @method        $this            roundMillennium(float $precision = 1, string $function = "round")                  Round the current instance millennium with given precision using the given function.
 * @method        $this            roundMillennia(float $precision = 1, string $function = "round")                   Round the current instance millennium with given precision using the given function.
 * @method        $this            floorMillennium(float $precision = 1)                                              Truncate the current instance millennium with given precision.
 * @method        $this            floorMillennia(float $precision = 1)                                               Truncate the current instance millennium with given precision.
 * @method        $this            ceilMillennium(float $precision = 1)                                               Ceil the current instance millennium with given precision.
 * @method        $this            ceilMillennia(float $precision = 1)                                                Ceil the current instance millennium with given precision.
 * @method        $this            roundCentury(float $precision = 1, string $function = "round")                     Round the current instance century with given precision using the given function.
 * @method        $this            roundCenturies(float $precision = 1, string $function = "round")                   Round the current instance century with given precision using the given function.
 * @method        $this            floorCentury(float $precision = 1)                                                 Truncate the current instance century with given precision.
 * @method        $this            floorCenturies(float $precision = 1)                                               Truncate the current instance century with given precision.
 * @method        $this            ceilCentury(float $precision = 1)                                                  Ceil the current instance century with given precision.
 * @method        $this            ceilCenturies(float $precision = 1)                                                Ceil the current instance century with given precision.
 * @method        $this            roundDecade(float $precision = 1, string $function = "round")                      Round the current instance decade with given precision using the given function.
 * @method        $this            roundDecades(float $precision = 1, string $function = "round")                     Round the current instance decade with given precision using the given function.
 * @method        $this            floorDecade(float $precision = 1)                                                  Truncate the current instance decade with given precision.
 * @method        $this            floorDecades(float $precision = 1)                                                 Truncate the current instance decade with given precision.
 * @method        $this            ceilDecade(float $precision = 1)                                                   Ceil the current instance decade with given precision.
 * @method        $this            ceilDecades(float $precision = 1)                                                  Ceil the current instance decade with given precision.
 * @method        $this            roundQuarter(float $precision = 1, string $function = "round")                     Round the current instance quarter with given precision using the given function.
 * @method        $this            roundQuarters(float $precision = 1, string $function = "round")                    Round the current instance quarter with given precision using the given function.
 * @method        $this            floorQuarter(float $precision = 1)                                                 Truncate the current instance quarter with given precision.
 * @method        $this            floorQuarters(float $precision = 1)                                                Truncate the current instance quarter with given precision.
 * @method        $this            ceilQuarter(float $precision = 1)                                                  Ceil the current instance quarter with given precision.
 * @method        $this            ceilQuarters(float $precision = 1)                                                 Ceil the current instance quarter with given precision.
 * @method        $this            roundMillisecond(float $precision = 1, string $function = "round")                 Round the current instance millisecond with given precision using the given function.
 * @method        $this            roundMilliseconds(float $precision = 1, string $function = "round")                Round the current instance millisecond with given precision using the given function.
 * @method        $this            floorMillisecond(float $precision = 1)                                             Truncate the current instance millisecond with given precision.
 * @method        $this            floorMilliseconds(float $precision = 1)                                            Truncate the current instance millisecond with given precision.
 * @method        $this            ceilMillisecond(float $precision = 1)                                              Ceil the current instance millisecond with given precision.
 * @method        $this            ceilMilliseconds(float $precision = 1)                                             Ceil the current instance millisecond with given precision.
 * @method        $this            roundMicrosecond(float $precision = 1, string $function = "round")                 Round the current instance microsecond with given precision using the given function.
 * @method        $this            roundMicroseconds(float $precision = 1, string $function = "round")                Round the current instance microsecond with given precision using the given function.
 * @method        $this            floorMicrosecond(float $precision = 1)                                             Truncate the current instance microsecond with given precision.
 * @method        $this            floorMicroseconds(float $precision = 1)                                            Truncate the current instance microsecond with given precision.
 * @method        $this            ceilMicrosecond(float $precision = 1)                                              Ceil the current instance microsecond with given precision.
 * @method        $this            ceilMicroseconds(float $precision = 1)                                             Ceil the current instance microsecond with given precision.
 * @method        string           shortAbsoluteDiffForHumans(DateTimeInterface $other = null, int $parts = 1)        Get the difference (short format, 'Absolute' mode) in a human readable format in the current locale. ($other and $parts parameters can be swapped.)
 * @method        string           longAbsoluteDiffForHumans(DateTimeInterface $other = null, int $parts = 1)         Get the difference (long format, 'Absolute' mode) in a human readable format in the current locale. ($other and $parts parameters can be swapped.)
 * @method        string           shortRelativeDiffForHumans(DateTimeInterface $other = null, int $parts = 1)        Get the difference (short format, 'Relative' mode) in a human readable format in the current locale. ($other and $parts parameters can be swapped.)
 * @method        string           longRelativeDiffForHumans(DateTimeInterface $other = null, int $parts = 1)         Get the difference (long format, 'Relative' mode) in a human readable format in the current locale. ($other and $parts parameters can be swapped.)
 * @method        string           shortRelativeToNowDiffForHumans(DateTimeInterface $other = null, int $parts = 1)   Get the difference (short format, 'RelativeToNow' mode) in a human readable format in the current locale. ($other and $parts parameters can be swapped.)
 * @method        string           longRelativeToNowDiffForHumans(DateTimeInterface $other = null, int $parts = 1)    Get the difference (long format, 'RelativeToNow' mode) in a human readable format in the current locale. ($other and $parts parameters can be swapped.)
 * @method        string           shortRelativeToOtherDiffForHumans(DateTimeInterface $other = null, int $parts = 1) Get the difference (short format, 'RelativeToOther' mode) in a human readable format in the current locale. ($other and $parts parameters can be swapped.)
 * @method        string           longRelativeToOtherDiffForHumans(DateTimeInterface $other = null, int $parts = 1)  Get the difference (long format, 'RelativeToOther' mode) in a human readable format in the current locale. ($other and $parts parameters can be swapped.)
 * @method        int              centuriesInMillennium()                                                            Return the number of centuries contained in the current millennium
 * @method        int|static       centuryOfMillennium(?int $century = null)                                          Return the value of the century starting from the beginning of the current millennium when called with no parameters, change the current century when called with an integer value
 * @method        int|static       dayOfCentury(?int $day = null)                                                     Return the value of the day starting from the beginning of the current century when called with no parameters, change the current day when called with an integer value
 * @method        int|static       dayOfDecade(?int $day = null)                                                      Return the value of the day starting from the beginning of the current decade when called with no parameters, change the current day when called with an integer value
 * @method        int|static       dayOfMillennium(?int $day = null)                                                  Return the value of the day starting from the beginning of the current millennium when called with no parameters, change the current day when called with an integer value
 * @method        int|static       dayOfMonth(?int $day = null)                                                       Return the value of the day starting from the beginning of the current month when called with no parameters, change the current day when called with an integer value
 * @method        int|static       dayOfQuarter(?int $day = null)                                                     Return the value of the day starting from the beginning of the current quarter when called with no parameters, change the current day when called with an integer value
 * @method        int|static       dayOfWeek(?int $day = null)                                                        Return the value of the day starting from the beginning of the current week when called with no parameters, change the current day when called with an integer value
 * @method        int              daysInCentury()                                                                    Return the number of days contained in the current century
 * @method        int              daysInDecade()                                                                     Return the number of days contained in the current decade
 * @method        int              daysInMillennium()                                                                 Return the number of days contained in the current millennium
 * @method        int              daysInMonth()                                                                      Return the number of days contained in the current month
 * @method        int              daysInQuarter()                                                                    Return the number of days contained in the current quarter
 * @method        int              daysInWeek()                                                                       Return the number of days contained in the current week
 * @method        int              daysInYear()                                                                       Return the number of days contained in the current year
 * @method        int|static       decadeOfCentury(?int $decade = null)                                               Return the value of the decade starting from the beginning of the current century when called with no parameters, change the current decade when called with an integer value
 * @method        int|static       decadeOfMillennium(?int $decade = null)                                            Return the value of the decade starting from the beginning of the current millennium when called with no parameters, change the current decade when called with an integer value
 * @method        int              decadesInCentury()                                                                 Return the number of decades contained in the current century
 * @method        int              decadesInMillennium()                                                              Return the number of decades contained in the current millennium
 * @method        int|static       hourOfCentury(?int $hour = null)                                                   Return the value of the hour starting from the beginning of the current century when called with no parameters, change the current hour when called with an integer value
 * @method        int|static       hourOfDay(?int $hour = null)                                                       Return the value of the hour starting from the beginning of the current day when called with no parameters, change the current hour when called with an integer value
 * @method        int|static       hourOfDecade(?int $hour = null)                                                    Return the value of the hour starting from the beginning of the current decade when called with no parameters, change the current hour when called with an integer value
 * @method        int|static       hourOfMillennium(?int $hour = null)                                                Return the value of the hour starting from the beginning of the current millennium when called with no parameters, change the current hour when called with an integer value
 * @method        int|static       hourOfMonth(?int $hour = null)                                                     Return the value of the hour starting from the beginning of the current month when called with no parameters, change the current hour when called with an integer value
 * @method        int|static       hourOfQuarter(?int $hour = null)                                                   Return the value of the hour starting from the beginning of the current quarter when called with no parameters, change the current hour when called with an integer value
 * @method        int|static       hourOfWeek(?int $hour = null)                                                      Return the value of the hour starting from the beginning of the current week when called with no parameters, change the current hour when called with an integer value
 * @method        int|static       hourOfYear(?int $hour = null)                                                      Return the value of the hour starting from the beginning of the current year when called with no parameters, change the current hour when called with an integer value
 * @method        int              hoursInCentury()                                                                   Return the number of hours contained in the current century
 * @method        int              hoursInDay()                                                                       Return the number of hours contained in the current day
 * @method        int              hoursInDecade()                                                                    Return the number of hours contained in the current decade
 * @method        int              hoursInMillennium()                                                                Return the number of hours contained in the current millennium
 * @method        int              hoursInMonth()                                                                     Return the number of hours contained in the current month
 * @method        int              hoursInQuarter()                                                                   Return the number of hours contained in the current quarter
 * @method        int              hoursInWeek()                                                                      Return the number of hours contained in the current week
 * @method        int              hoursInYear()                                                                      Return the number of hours contained in the current year
 * @method        int|static       microsecondOfCentury(?int $microsecond = null)                                     Return the value of the microsecond starting from the beginning of the current century when called with no parameters, change the current microsecond when called with an integer value
 * @method        int|static       microsecondOfDay(?int $microsecond = null)                                         Return the value of the microsecond starting from the beginning of the current day when called with no parameters, change the current microsecond when called with an integer value
 * @method        int|static       microsecondOfDecade(?int $microsecond = null)                                      Return the value of the microsecond starting from the beginning of the current decade when called with no parameters, change the current microsecond when called with an integer value
 * @method        int|static       microsecondOfHour(?int $microsecond = null)                                        Return the value of the microsecond starting from the beginning of the current hour when called with no parameters, change the current microsecond when called with an integer value
 * @method        int|static       microsecondOfMillennium(?int $microsecond = null)                                  Return the value of the microsecond starting from the beginning of the current millennium when called with no parameters, change the current microsecond when called with an integer value
 * @method        int|static       microsecondOfMillisecond(?int $microsecond = null)                                 Return the value of the microsecond starting from the beginning of the current millisecond when called with no parameters, change the current microsecond when called with an integer value
 * @method        int|static       microsecondOfMinute(?int $microsecond = null)                                      Return the value of the microsecond starting from the beginning of the current minute when called with no parameters, change the current microsecond when called with an integer value
 * @method        int|static       microsecondOfMonth(?int $microsecond = null)                                       Return the value of the microsecond starting from the beginning of the current month when called with no parameters, change the current microsecond when called with an integer value
 * @method        int|static       microsecondOfQuarter(?int $microsecond = null)                                     Return the value of the microsecond starting from the beginning of the current quarter when called with no parameters, change the current microsecond when called with an integer value
 * @method        int|static       microsecondOfSecond(?int $microsecond = null)                                      Return the value of the microsecond starting from the beginning of the current second when called with no parameters, change the current microsecond when called with an integer value
 * @method        int|static       microsecondOfWeek(?int $microsecond = null)                                        Return the value of the microsecond starting from the beginning of the current week when called with no parameters, change the current microsecond when called with an integer value
 * @method        int|static       microsecondOfYear(?int $microsecond = null)                                        Return the value of the microsecond starting from the beginning of the current year when called with no parameters, change the current microsecond when called with an integer value
 * @method        int              microsecondsInCentury()                                                            Return the number of microseconds contained in the current century
 * @method        int              microsecondsInDay()                                                                Return the number of microseconds contained in the current day
 * @method        int              microsecondsInDecade()                                                             Return the number of microseconds contained in the current decade
 * @method        int              microsecondsInHour()                                                               Return the number of microseconds contained in the current hour
 * @method        int              microsecondsInMillennium()                                                         Return the number of microseconds contained in the current millennium
 * @method        int              microsecondsInMillisecond()                                                        Return the number of microseconds contained in the current millisecond
 * @method        int              microsecondsInMinute()                                                             Return the number of microseconds contained in the current minute
 * @method        int              microsecondsInMonth()                                                              Return the number of microseconds contained in the current month
 * @method        int              microsecondsInQuarter()                                                            Return the number of microseconds contained in the current quarter
 * @method        int              microsecondsInSecond()                                                             Return the number of microseconds contained in the current second
 * @method        int              microsecondsInWeek()                                                               Return the number of microseconds contained in the current week
 * @method        int              microsecondsInYear()                                                               Return the number of microseconds contained in the current year
 * @method        int|static       millisecondOfCentury(?int $millisecond = null)                                     Return the value of the millisecond starting from the beginning of the current century when called with no parameters, change the current millisecond when called with an integer value
 * @method        int|static       millisecondOfDay(?int $millisecond = null)                                         Return the value of the millisecond starting from the beginning of the current day when called with no parameters, change the current millisecond when called with an integer value
 * @method        int|static       millisecondOfDecade(?int $millisecond = null)                                      Return the value of the millisecond starting from the beginning of the current decade when called with no parameters, change the current millisecond when called with an integer value
 * @method        int|static       millisecondOfHour(?int $millisecond = null)                                        Return the value of the millisecond starting from the beginning of the current hour when called with no parameters, change the current millisecond when called with an integer value
 * @method        int|static       millisecondOfMillennium(?int $millisecond = null)                                  Return the value of the millisecond starting from the beginning of the current millennium when called with no parameters, change the current millisecond when called with an integer value
 * @method        int|static       millisecondOfMinute(?int $millisecond = null)                                      Return the value of the millisecond starting from the beginning of the current minute when called with no parameters, change the current millisecond when called with an integer value
 * @method        int|static       millisecondOfMonth(?int $millisecond = null)                                       Return the value of the millisecond starting from the beginning of the current month when called with no parameters, change the current millisecond when called with an integer value
 * @method        int|static       millisecondOfQuarter(?int $millisecond = null)                                     Return the value of the millisecond starting from the beginning of the current quarter when called with no parameters, change the current millisecond when called with an integer value
 * @method        int|static       millisecondOfSecond(?int $millisecond = null)                                      Return the value of the millisecond starting from the beginning of the current second when called with no parameters, change the current millisecond when called with an integer value
 * @method        int|static       millisecondOfWeek(?int $millisecond = null)                                        Return the value of the millisecond starting from the beginning of the current week when called with no parameters, change the current millisecond when called with an integer value
 * @method        int|static       millisecondOfYear(?int $millisecond = null)                                        Return the value of the millisecond starting from the beginning of the current year when called with no parameters, change the current millisecond when called with an integer value
 * @method        int              millisecondsInCentury()                                                            Return the number of milliseconds contained in the current century
 * @method        int              millisecondsInDay()                                                                Return the number of milliseconds contained in the current day
 * @method        int              millisecondsInDecade()                                                             Return the number of milliseconds contained in the current decade
 * @method        int              millisecondsInHour()                                                               Return the number of milliseconds contained in the current hour
 * @method        int              millisecondsInMillennium()                                                         Return the number of milliseconds contained in the current millennium
 * @method        int              millisecondsInMinute()                                                             Return the number of milliseconds contained in the current minute
 * @method        int              millisecondsInMonth()                                                              Return the number of milliseconds contained in the current month
 * @method        int              millisecondsInQuarter()                                                            Return the number of milliseconds contained in the current quarter
 * @method        int              millisecondsInSecond()                                                             Return the number of milliseconds contained in the current second
 * @method        int              millisecondsInWeek()                                                               Return the number of milliseconds contained in the current week
 * @method        int              millisecondsInYear()                                                               Return the number of milliseconds contained in the current year
 * @method        int|static       minuteOfCentury(?int $minute = null)                                               Return the value of the minute starting from the beginning of the current century when called with no parameters, change the current minute when called with an integer value
 * @method        int|static       minuteOfDay(?int $minute = null)                                                   Return the value of the minute starting from the beginning of the current day when called with no parameters, change the current minute when called with an integer value
 * @method        int|static       minuteOfDecade(?int $minute = null)                                                Return the value of the minute starting from the beginning of the current decade when called with no parameters, change the current minute when called with an integer value
 * @method        int|static       minuteOfHour(?int $minute = null)                                                  Return the value of the minute starting from the beginning of the current hour when called with no parameters, change the current minute when called with an integer value
 * @method        int|static       minuteOfMillennium(?int $minute = null)                                            Return the value of the minute starting from the beginning of the current millennium when called with no parameters, change the current minute when called with an integer value
 * @method        int|static       minuteOfMonth(?int $minute = null)                                                 Return the value of the minute starting from the beginning of the current month when called with no parameters, change the current minute when called with an integer value
 * @method        int|static       minuteOfQuarter(?int $minute = null)                                               Return the value of the minute starting from the beginning of the current quarter when called with no parameters, change the current minute when called with an integer value
 * @method        int|static       minuteOfWeek(?int $minute = null)                                                  Return the value of the minute starting from the beginning of the current week when called with no parameters, change the current minute when called with an integer value
 * @method        int|static       minuteOfYear(?int $minute = null)                                                  Return the value of the minute starting from the beginning of the current year when called with no parameters, change the current minute when called with an integer value
 * @method        int              minutesInCentury()                                                                 Return the number of minutes contained in the current century
 * @method        int              minutesInDay()                                                                     Return the number of minutes contained in the current day
 * @method        int              minutesInDecade()                                                                  Return the number of minutes contained in the current decade
 * @method        int              minutesInHour()                                                                    Return the number of minutes contained in the current hour
 * @method        int              minutesInMillennium()                                                              Return the number of minutes contained in the current millennium
 * @method        int              minutesInMonth()                                                                   Return the number of minutes contained in the current month
 * @method        int              minutesInQuarter()                                                                 Return the number of minutes contained in the current quarter
 * @method        int              minutesInWeek()                                                                    Return the number of minutes contained in the current week
 * @method        int              minutesInYear()                                                                    Return the number of minutes contained in the current year
 * @method        int|static       monthOfCentury(?int $month = null)                                                 Return the value of the month starting from the beginning of the current century when called with no parameters, change the current month when called with an integer value
 * @method        int|static       monthOfDecade(?int $month = null)                                                  Return the value of the month starting from the beginning of the current decade when called with no parameters, change the current month when called with an integer value
 * @method        int|static       monthOfMillennium(?int $month = null)                                              Return the value of the month starting from the beginning of the current millennium when called with no parameters, change the current month when called with an integer value
 * @method        int|static       monthOfQuarter(?int $month = null)                                                 Return the value of the month starting from the beginning of the current quarter when called with no parameters, change the current month when called with an integer value
 * @method        int|static       monthOfYear(?int $month = null)                                                    Return the value of the month starting from the beginning of the current year when called with no parameters, change the current month when called with an integer value
 * @method        int              monthsInCentury()                                                                  Return the number of months contained in the current century
 * @method        int              monthsInDecade()                                                                   Return the number of months contained in the current decade
 * @method        int              monthsInMillennium()                                                               Return the number of months contained in the current millennium
 * @method        int              monthsInQuarter()                                                                  Return the number of months contained in the current quarter
 * @method        int              monthsInYear()                                                                     Return the number of months contained in the current year
 * @method        int|static       quarterOfCentury(?int $quarter = null)                                             Return the value of the quarter starting from the beginning of the current century when called with no parameters, change the current quarter when called with an integer value
 * @method        int|static       quarterOfDecade(?int $quarter = null)                                              Return the value of the quarter starting from the beginning of the current decade when called with no parameters, change the current quarter when called with an integer value
 * @method        int|static       quarterOfMillennium(?int $quarter = null)                                          Return the value of the quarter starting from the beginning of the current millennium when called with no parameters, change the current quarter when called with an integer value
 * @method        int|static       quarterOfYear(?int $quarter = null)                                                Return the value of the quarter starting from the beginning of the current year when called with no parameters, change the current quarter when called with an integer value
 * @method        int              quartersInCentury()                                                                Return the number of quarters contained in the current century
 * @method        int              quartersInDecade()                                                                 Return the number of quarters contained in the current decade
 * @method        int              quartersInMillennium()                                                             Return the number of quarters contained in the current millennium
 * @method        int              quartersInYear()                                                                   Return the number of quarters contained in the current year
 * @method        int|static       secondOfCentury(?int $second = null)                                               Return the value of the second starting from the beginning of the current century when called with no parameters, change the current second when called with an integer value
 * @method        int|static       secondOfDay(?int $second = null)                                                   Return the value of the second starting from the beginning of the current day when called with no parameters, change the current second when called with an integer value
 * @method        int|static       secondOfDecade(?int $second = null)                                                Return the value of the second starting from the beginning of the current decade when called with no parameters, change the current second when called with an integer value
 * @method        int|static       secondOfHour(?int $second = null)                                                  Return the value of the second starting from the beginning of the current hour when called with no parameters, change the current second when called with an integer value
 * @method        int|static       secondOfMillennium(?int $second = null)                                            Return the value of the second starting from the beginning of the current millennium when called with no parameters, change the current second when called with an integer value
 * @method        int|static       secondOfMinute(?int $second = null)                                                Return the value of the second starting from the beginning of the current minute when called with no parameters, change the current second when called with an integer value
 * @method        int|static       secondOfMonth(?int $second = null)                                                 Return the value of the second starting from the beginning of the current month when called with no parameters, change the current second when called with an integer value
 * @method        int|static       secondOfQuarter(?int $second = null)                                               Return the value of the second starting from the beginning of the current quarter when called with no parameters, change the current second when called with an integer value
 * @method        int|static       secondOfWeek(?int $second = null)                                                  Return the value of the second starting from the beginning of the current week when called with no parameters, change the current second when called with an integer value
 * @method        int|static       secondOfYear(?int $second = null)                                                  Return the value of the second starting from the beginning of the current year when called with no parameters, change the current second when called with an integer value
 * @method        int              secondsInCentury()                                                                 Return the number of seconds contained in the current century
 * @method        int              secondsInDay()                                                                     Return the number of seconds contained in the current day
 * @method        int              secondsInDecade()                                                                  Return the number of seconds contained in the current decade
 * @method        int              secondsInHour()                                                                    Return the number of seconds contained in the current hour
 * @method        int              secondsInMillennium()                                                              Return the number of seconds contained in the current millennium
 * @method        int              secondsInMinute()                                                                  Return the number of seconds contained in the current minute
 * @method        int              secondsInMonth()                                                                   Return the number of seconds contained in the current month
 * @method        int              secondsInQuarter()                                                                 Return the number of seconds contained in the current quarter
 * @method        int              secondsInWeek()                                                                    Return the number of seconds contained in the current week
 * @method        int              secondsInYear()                                                                    Return the number of seconds contained in the current year
 * @method        int|static       weekOfCentury(?int $week = null)                                                   Return the value of the week starting from the beginning of the current century when called with no parameters, change the current week when called with an integer value
 * @method        int|static       weekOfDecade(?int $week = null)                                                    Return the value of the week starting from the beginning of the current decade when called with no parameters, change the current week when called with an integer value
 * @method        int|static       weekOfMillennium(?int $week = null)                                                Return the value of the week starting from the beginning of the current millennium when called with no parameters, change the current week when called with an integer value
 * @method        int|static       weekOfMonth(?int $week = null)                                                     Return the value of the week starting from the beginning of the current month when called with no parameters, change the current week when called with an integer value
 * @method        int|static       weekOfQuarter(?int $week = null)                                                   Return the value of the week starting from the beginning of the current quarter when called with no parameters, change the current week when called with an integer value
 * @method        int|static       weekOfYear(?int $week = null)                                                      Return the value of the week starting from the beginning of the current year when called with no parameters, change the current week when called with an integer value
 * @method        int              weeksInCentury()                                                                   Return the number of weeks contained in the current century
 * @method        int              weeksInDecade()                                                                    Return the number of weeks contained in the current decade
 * @method        int              weeksInMillennium()                                                                Return the number of weeks contained in the current millennium
 * @method        int              weeksInMonth()                                                                     Return the number of weeks contained in the current month
 * @method        int              weeksInQuarter()                                                                   Return the number of weeks contained in the current quarter
 * @method        int|static       yearOfCentury(?int $year = null)                                                   Return the value of the year starting from the beginning of the current century when called with no parameters, change the current year when called with an integer value
 * @method        int|static       yearOfDecade(?int $year = null)                                                    Return the value of the year starting from the beginning of the current decade when called with no parameters, change the current year when called with an integer value
 * @method        int|static       yearOfMillennium(?int $year = null)                                                Return the value of the year starting from the beginning of the current millennium when called with no parameters, change the current year when called with an integer value
 * @method        int              yearsInCentury()                                                                   Return the number of years contained in the current century
 * @method        int              yearsInDecade()                                                                    Return the number of years contained in the current decade
 * @method        int              yearsInMillennium()                                                                Return the number of years contained in the current millennium
 *
 * </autodoc>
 */
class Carbon extends DateTime implements CarbonInterface
{
    use Date;

    /**
     * Returns true if the current class/instance is mutable.
     */
    public static function isMutable(): bool
    {
        return true;
    }
}
