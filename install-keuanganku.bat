@echo off
title KeuanganKu - Auto Setup PC Baru
color 0A

echo.
echo ========================================
echo    KEUANGANKU - AUTO SETUP PC BARU
echo ========================================
echo.

:: Check if running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo [OK] Running as Administrator
) else (
    echo [ERROR] Please run as Administrator!
    echo Right-click and select "Run as administrator"
    pause
    exit /b 1
)

echo.
echo [INFO] Starting automatic setup...
echo.

:: Create directory structure
echo [STEP 1] Creating directory structure...
if not exist "C:\Projects" mkdir "C:\Projects"
if not exist "C:\Projects\keuanganku-dev" mkdir "C:\Projects\keuanganku-dev"
if not exist "C:\Projects\backups" mkdir "C:\Projects\backups"
if not exist "C:\Tools" mkdir "C:\Tools"
if not exist "C:\Tools\scripts" mkdir "C:\Tools\scripts"
if not exist "C:\Tools\configs" mkdir "C:\Tools\configs"
echo [OK] Directory structure created

:: Check if XAMPP is installed
echo.
echo [STEP 2] Checking XAMPP installation...
if exist "C:\xampp\xampp-control.exe" (
    echo [OK] XAMPP found
) else (
    echo [WARNING] XAMPP not found!
    echo Please install XAMPP first from: https://www.apachefriends.org/
    echo After installing XAMPP, run this script again.
    pause
    exit /b 1
)

:: Check if project files exist
echo.
echo [STEP 3] Checking project files...
if exist "keuanganku" (
    echo [OK] Project folder found
    echo [INFO] Copying project files to XAMPP...
    xcopy "keuanganku" "C:\xampp\htdocs\keuanganku" /E /I /H /Y
    echo [OK] Project files copied
) else (
    echo [WARNING] Project folder 'keuanganku' not found in current directory
    echo Please place your project folder here and run again.
    pause
    exit /b 1
)

:: Create backup script
echo.
echo [STEP 4] Creating backup script...
(
echo @echo off
echo title KeuanganKu - Backup
echo echo Creating backup...
echo set timestamp=%%date:~-4,4%%%%date:~-10,2%%%%date:~-7,2%%_%%time:~0,2%%%%time:~3,2%%%%time:~6,2%%
echo set timestamp=%%timestamp: =0%%
echo.
echo mkdir "C:\Projects\backups\%%timestamp%%"
echo xcopy "C:\xampp\htdocs\keuanganku" "C:\Projects\backups\%%timestamp%%\files" /E /I /H
echo.
echo echo Exporting database...
echo cd /d "C:\xampp\mysql\bin"
echo mysqldump -u root keuanganku_db ^> "C:\Projects\backups\%%timestamp%%\database.sql"
echo.
echo echo Backup completed: %%timestamp%%
echo echo Files: C:\Projects\backups\%%timestamp%%\files
echo echo Database: C:\Projects\backups\%%timestamp%%\database.sql
echo pause
) > "C:\Tools\scripts\backup-keuanganku.bat"
echo [OK] Backup script created: C:\Tools\scripts\backup-keuanganku.bat

:: Create start development script
echo.
echo [STEP 5] Creating development start script...
(
echo @echo off
echo title KeuanganKu - Start Development
echo echo Starting development environment...
echo.
echo echo [1/4] Starting XAMPP Control Panel...
echo cd /d "C:\xampp"
echo start xampp-control.exe
echo.
echo echo [2/4] Waiting for services to start...
echo timeout /t 5 /nobreak ^>nul
echo.
echo echo [3/4] Opening project in browser...
echo start http://localhost/keuanganku/
echo.
echo echo [4/4] Opening VS Code...
echo if exist "C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\Code.exe" (
echo     start "" "C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\Code.exe" "C:\xampp\htdocs\keuanganku"
echo ^) else (
echo     echo VS Code not found, opening folder instead...
echo     start explorer "C:\xampp\htdocs\keuanganku"
echo ^)
echo.
echo echo Development environment started!
echo echo - XAMPP Control Panel: Running
echo echo - Website: http://localhost/keuanganku/
echo echo - Project folder: C:\xampp\htdocs\keuanganku
echo pause
) > "C:\Tools\scripts\start-dev-keuanganku.bat"
echo [OK] Development start script created: C:\Tools\scripts\start-dev-keuanganku.bat

:: Create database import script
echo.
echo [STEP 6] Creating database import script...
(
echo @echo off
echo title KeuanganKu - Import Database
echo echo Importing database...
echo.
echo if not exist "database.sql" (
echo     echo [ERROR] database.sql file not found!
echo     echo Please place your database backup file here and rename it to 'database.sql'
echo     pause
echo     exit /b 1
echo ^)
echo.
echo echo [INFO] Creating database...
echo cd /d "C:\xampp\mysql\bin"
echo mysql -u root -e "CREATE DATABASE IF NOT EXISTS keuanganku_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
echo.
echo echo [INFO] Importing data...
echo mysql -u root keuanganku_db ^< "%%~dp0database.sql"
echo.
echo if %%errorlevel%% == 0 (
echo     echo [OK] Database imported successfully!
echo ^) else (
echo     echo [ERROR] Database import failed!
echo ^)
echo pause
) > "C:\Tools\scripts\import-database.bat"
echo [OK] Database import script created: C:\Tools\scripts\import-database.bat

:: Create PHP configuration
echo.
echo [STEP 7] Updating PHP configuration...
set php_ini="C:\xampp\php\php.ini"
if exist %php_ini% (
    echo [INFO] Backing up original php.ini...
    copy %php_ini% "C:\Tools\configs\php.ini.backup" >nul
    
    echo [INFO] Updating PHP settings...
    powershell -Command "(gc %php_ini%) -replace ';extension=mysqli', 'extension=mysqli' | Out-File -encoding ASCII %php_ini%"
    powershell -Command "(gc %php_ini%) -replace ';extension=pdo_mysql', 'extension=pdo_mysql' | Out-File -encoding ASCII %php_ini%"
    powershell -Command "(gc %php_ini%) -replace ';extension=mbstring', 'extension=mbstring' | Out-File -encoding ASCII %php_ini%"
    powershell -Command "(gc %php_ini%) -replace ';extension=openssl', 'extension=openssl' | Out-File -encoding ASCII %php_ini%"
    powershell -Command "(gc %php_ini%) -replace ';extension=curl', 'extension=curl' | Out-File -encoding ASCII %php_ini%"
    powershell -Command "(gc %php_ini%) -replace ';extension=gd', 'extension=gd' | Out-File -encoding ASCII %php_ini%"
    powershell -Command "(gc %php_ini%) -replace 'memory_limit = 128M', 'memory_limit = 512M' | Out-File -encoding ASCII %php_ini%"
    powershell -Command "(gc %php_ini%) -replace 'upload_max_filesize = 2M', 'upload_max_filesize = 64M' | Out-File -encoding ASCII %php_ini%"
    powershell -Command "(gc %php_ini%) -replace 'post_max_size = 8M', 'post_max_size = 64M' | Out-File -encoding ASCII %php_ini%"
    
    echo [OK] PHP configuration updated
) else (
    echo [WARNING] php.ini not found, skipping PHP configuration
)

:: Create desktop shortcuts
echo.
echo [STEP 8] Creating desktop shortcuts...
set desktop="%USERPROFILE%\Desktop"

:: XAMPP Control shortcut
powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%desktop%\XAMPP Control.lnk'); $Shortcut.TargetPath = 'C:\xampp\xampp-control.exe'; $Shortcut.Save()"

:: KeuanganKu Website shortcut
powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%desktop%\KeuanganKu Website.lnk'); $Shortcut.TargetPath = 'http://localhost/keuanganku/'; $Shortcut.Save()"

:: Start Development shortcut
powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%desktop%\Start KeuanganKu Dev.lnk'); $Shortcut.TargetPath = 'C:\Tools\scripts\start-dev-keuanganku.bat'; $Shortcut.Save()"

echo [OK] Desktop shortcuts created

:: Create quick access menu
echo.
echo [STEP 9] Creating quick access menu...
(
echo @echo off
echo title KeuanganKu - Quick Access Menu
echo :menu
echo cls
echo.
echo ========================================
echo        KEUANGANKU - QUICK ACCESS
echo ========================================
echo.
echo 1. Start Development Environment
echo 2. Open Website
echo 3. Open phpMyAdmin
echo 4. Open Project Folder
echo 5. Create Backup
echo 6. Import Database
echo 7. View Logs
echo 8. Exit
echo.
echo ========================================
echo.
set /p choice="Select option (1-8): "
echo.
if "%%choice%%"=="1" call "C:\Tools\scripts\start-dev-keuanganku.bat"
if "%%choice%%"=="2" start http://localhost/keuanganku/
if "%%choice%%"=="3" start http://localhost/phpmyadmin/
if "%%choice%%"=="4" start explorer "C:\xampp\htdocs\keuanganku"
if "%%choice%%"=="5" call "C:\Tools\scripts\backup-keuanganku.bat"
if "%%choice%%"=="6" call "C:\Tools\scripts\import-database.bat"
if "%%choice%%"=="7" start explorer "C:\xampp\apache\logs"
if "%%choice%%"=="8" exit
echo.
pause
goto menu
) > "C:\Tools\scripts\keuanganku-menu.bat"

:: Create menu shortcut
powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%desktop%\KeuanganKu Menu.lnk'); $Shortcut.TargetPath = 'C:\Tools\scripts\keuanganku-menu.bat'; $Shortcut.Save()"

echo [OK] Quick access menu created

:: Final summary
echo.
echo ========================================
echo           SETUP COMPLETED!
echo ========================================
echo.
echo [CREATED FILES]
echo - Project: C:\xampp\htdocs\keuanganku\
echo - Backup Script: C:\Tools\scripts\backup-keuanganku.bat
echo - Start Dev Script: C:\Tools\scripts\start-dev-keuanganku.bat
echo - Import DB Script: C:\Tools\scripts\import-database.bat
echo - Quick Menu: C:\Tools\scripts\keuanganku-menu.bat
echo.
echo [DESKTOP SHORTCUTS]
echo - XAMPP Control
echo - KeuanganKu Website
echo - Start KeuanganKu Dev
echo - KeuanganKu Menu
echo.
echo [NEXT STEPS]
echo 1. Import your database using the import script
echo 2. Update database configuration in your project
echo 3. Start development environment
echo 4. Test your website
echo.
echo [QUICK ACCESS]
echo Double-click "KeuanganKu Menu" on desktop for quick access!
echo.
pause

:: Ask to start development environment
echo.
set /p start_now="Start development environment now? (y/n): "
if /i "%start_now%"=="y" (
    call "C:\Tools\scripts\start-dev-keuanganku.bat"
)

echo.
echo Setup completed successfully!
pause
