<!DOCTYPE html>
<html lang="id" data-bs-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $pageTitle ?? $page_title ?? 'KeuanganKu - Financial Management System' ?></title>

    <!-- SEO Meta Tags -->
    <meta name="description" content="KeuanganKu - Modern Financial Management System">
    <meta name="keywords" content="keuangan, financial, management, sistem">
    <meta name="author" content="KeuanganKu Team">

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="assets/img/favicon.svg">
    <link rel="icon" type="image/png" href="assets/img/favicon.png">
    <link rel="apple-touch-icon" href="assets/img/apple-touch-icon.png">
    <link rel="manifest" href="assets/img/site.webmanifest">

    <!-- Preconnect for performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="preconnect" href="https://cdn.jsdelivr.net">

    <!-- CSS Libraries -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-T3c6CoIi6uLrA9TneNEoa7RxnatzjcDSCmG1MXxSR1GAsXEV/Dwwykc2MPK8M2HN" crossorigin="anonymous">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet" integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA==" crossorigin="anonymous">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js"></script>

    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
    <link href="assets/css/admin-dashboard.css" rel="stylesheet">

    <!-- Modern UI Framework CSS -->
    <link href="assets/css/modern-ui.css?v=<?= time() ?>" rel="stylesheet">

    <!-- Consolidated Layout Manager CSS -->
    <link href="assets/css/layout-manager.css?v=<?= time() ?>" rel="stylesheet">

    <!-- Control Sidebar CSS -->
    <link href="assets/css/control-sidebar.css?v=<?= time() ?>" rel="stylesheet">

    <!-- Initialize Theme Early -->
    <script>
        // Apply saved theme immediately to prevent flash
        (function() {
            const savedTheme = localStorage.getItem('theme') || 'light';
            document.documentElement.setAttribute('data-bs-theme', savedTheme);
            document.body.setAttribute('data-bs-theme', savedTheme);

            // Add preload class to prevent transitions during page load
            document.addEventListener('DOMContentLoaded', function() {
                document.body.classList.add('preload');
            });
        })();
    </script>

    <?php
    // Apply user theme and layout preferences
    require_once 'includes/helpers/theme_helper.php';
    require_once 'includes/helpers/layout_helper.php';

    $currentUser = getCurrentUser();
    if ($currentUser) {
        // Apply theme preferences
        $userTheme = getUserThemePreferences($currentUser['id']);
        if ($userTheme) {
            echo '<style id="user-theme-css">' . generateThemeCSS($userTheme) . '</style>';
            $themeMode = $userTheme['theme_mode'] ?? 'light';
            echo '<script>document.documentElement.setAttribute("data-bs-theme", "' . $themeMode . '");</script>';
        }

        // Apply layout preferences
        $layoutPrefs = getUserLayoutPreferences($currentUser['id']);
        if ($layoutPrefs) {
            echo generateLayoutCSS($layoutPrefs);
            $layoutClasses = getLayoutBodyClasses($currentUser['id']);
            echo '<script>document.addEventListener("DOMContentLoaded", function() { document.body.className += " ' . $layoutClasses . '"; });</script>';
        }
    }
    ?>
</head>
<body>
    <div class="wrapper">
        <!-- Sidebar -->
        <?php include 'sidebar.php'; ?>

        <div class="main-content" id="mainContent">
            <!-- Navbar -->
            <?php include 'navbar.php'; ?>

            <!-- Content Container -->
            <div class="content-container">

            <!-- Flash Message -->
            <?php if ($flash = getFlashMessage()): ?>
            <div class="alert alert-<?= $flash['type'] ?> alert-dismissible fade show modern-alert" role="alert">
                <div class="d-flex align-items-center">
                    <i class="fas fa-<?= $flash['type'] === 'success' ? 'check-circle' : ($flash['type'] === 'danger' ? 'exclamation-triangle' : 'info-circle') ?> me-2"></i>
                    <span><?= $flash['message'] ?></span>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            <?php endif; ?>

<!-- Layout initialization moved to consolidated script -->

<!-- All CSS moved to external files for better performance -->

<!-- Consolidated Layout Manager JavaScript -->
<script src="assets/js/layout-manager.js?v=<?= time() ?>"></script>