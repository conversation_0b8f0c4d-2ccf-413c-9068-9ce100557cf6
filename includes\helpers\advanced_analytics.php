<?php
/**
 * Advanced Analytics Helper Functions
 * 
 * This file contains functions for advanced reporting and AI-powered insights
 */

/**
 * Generate financial insights using AI-like analysis
 * @param int $userId User ID
 * @param array $options Analysis options
 * @return array Insights and recommendations
 */
function generateFinancialInsights($userId, $options = []) {
    global $pdo;
    
    try {
        $insights = [];
        $period = $options['period'] ?? 'last_3_months';
        
        // Get transaction data for analysis
        $transactionData = getTransactionDataForAnalysis($userId, $period);
        
        // 1. Spending Pattern Analysis
        $insights['spending_patterns'] = analyzeSpendingPatterns($transactionData);
        
        // 2. Income Trend Analysis
        $insights['income_trends'] = analyzeIncomeTrends($transactionData);
        
        // 3. Budget Variance Analysis (placeholder for now)
        $insights['budget_variance'] = [];
        
        // 4. Seasonal Analysis
        $insights['seasonal_analysis'] = analyzeSeasonalPatterns($transactionData);
        
        // 5. Anomaly Detection
        $insights['anomalies'] = detectFinancialAnomalies($transactionData);
        
        // 6. Predictive Analysis
        $insights['predictions'] = generateFinancialPredictions($transactionData);
        
        // 7. Recommendations
        $insights['recommendations'] = generateRecommendations($insights);
        
        // 8. Risk Assessment
        $insights['risk_assessment'] = assessFinancialRisk($transactionData);
        
        return $insights;
        
    } catch (Exception $e) {
        error_log("Generate financial insights error: " . $e->getMessage());
        return [];
    }
}

/**
 * Get transaction data for analysis
 * @param int $userId User ID
 * @param string $period Time period
 * @return array Transaction data
 */
function getTransactionDataForAnalysis($userId, $period) {
    global $pdo;
    
    $dateCondition = '';
    switch ($period) {
        case 'last_month':
            $dateCondition = "AND t.tanggal >= DATE_SUB(CURDATE(), INTERVAL 1 MONTH)";
            break;
        case 'last_3_months':
            $dateCondition = "AND t.tanggal >= DATE_SUB(CURDATE(), INTERVAL 3 MONTH)";
            break;
        case 'last_6_months':
            $dateCondition = "AND t.tanggal >= DATE_SUB(CURDATE(), INTERVAL 6 MONTH)";
            break;
        case 'last_year':
            $dateCondition = "AND t.tanggal >= DATE_SUB(CURDATE(), INTERVAL 1 YEAR)";
            break;
    }
    
    $stmt = $pdo->prepare("
        SELECT 
            t.*,
            k.nama as kategori_nama,
            YEAR(t.tanggal) as tahun,
            MONTH(t.tanggal) as bulan,
            WEEK(t.tanggal) as minggu,
            DAYOFWEEK(t.tanggal) as hari_minggu,
            DAYOFMONTH(t.tanggal) as hari_bulan
        FROM transaksi t
        LEFT JOIN kategori k ON t.kategori_id = k.id
        WHERE t.user_id = ? $dateCondition
        ORDER BY t.tanggal ASC
    ");
    
    $stmt->execute([$userId]);
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

/**
 * Analyze spending patterns
 * @param array $transactions Transaction data
 * @return array Spending pattern analysis
 */
function analyzeSpendingPatterns($transactions) {
    $patterns = [
        'by_category' => [],
        'by_day_of_week' => [],
        'by_time_of_month' => [],
        'trends' => [],
        'insights' => []
    ];
    
    $expenses = array_filter($transactions, function($t) {
        return $t['jenis'] === 'pengeluaran';
    });
    
    // Category analysis
    foreach ($expenses as $expense) {
        $category = $expense['kategori_nama'] ?? 'Lainnya';
        if (!isset($patterns['by_category'][$category])) {
            $patterns['by_category'][$category] = 0;
        }
        $patterns['by_category'][$category] += $expense['jumlah'];
    }
    
    // Day of week analysis
    $dayNames = ['', 'Minggu', 'Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat', 'Sabtu'];
    foreach ($expenses as $expense) {
        $dayOfWeek = $dayNames[$expense['hari_minggu']];
        if (!isset($patterns['by_day_of_week'][$dayOfWeek])) {
            $patterns['by_day_of_week'][$dayOfWeek] = 0;
        }
        $patterns['by_day_of_week'][$dayOfWeek] += $expense['jumlah'];
    }
    
    // Time of month analysis
    foreach ($expenses as $expense) {
        $timeOfMonth = $expense['hari_bulan'] <= 10 ? 'Awal Bulan' : 
                      ($expense['hari_bulan'] <= 20 ? 'Pertengahan Bulan' : 'Akhir Bulan');
        if (!isset($patterns['by_time_of_month'][$timeOfMonth])) {
            $patterns['by_time_of_month'][$timeOfMonth] = 0;
        }
        $patterns['by_time_of_month'][$timeOfMonth] += $expense['jumlah'];
    }
    
    // Generate insights
    if (!empty($patterns['by_category'])) {
        $topCategory = array_keys($patterns['by_category'], max($patterns['by_category']))[0];
        $topAmount = max($patterns['by_category']);
        $totalSpending = array_sum($patterns['by_category']);
        $percentage = ($topAmount / $totalSpending) * 100;
        
        $patterns['insights'][] = [
            'type' => 'category_dominance',
            'message' => "Kategori '$topCategory' mendominasi pengeluaran Anda dengan " . number_format($percentage, 1) . "% dari total pengeluaran.",
            'severity' => $percentage > 50 ? 'high' : ($percentage > 30 ? 'medium' : 'low')
        ];
    }
    
    return $patterns;
}

/**
 * Analyze income trends
 * @param array $transactions Transaction data
 * @return array Income trend analysis
 */
function analyzeIncomeTrends($transactions) {
    $trends = [
        'monthly_income' => [],
        'growth_rate' => 0,
        'stability_score' => 0,
        'insights' => []
    ];
    
    $income = array_filter($transactions, function($t) {
        return $t['jenis'] === 'pemasukan';
    });
    
    // Monthly income calculation
    foreach ($income as $transaction) {
        $monthKey = $transaction['tahun'] . '-' . str_pad($transaction['bulan'], 2, '0', STR_PAD_LEFT);
        if (!isset($trends['monthly_income'][$monthKey])) {
            $trends['monthly_income'][$monthKey] = 0;
        }
        $trends['monthly_income'][$monthKey] += $transaction['jumlah'];
    }
    
    ksort($trends['monthly_income']);
    
    // Calculate growth rate
    $monthlyValues = array_values($trends['monthly_income']);
    if (count($monthlyValues) >= 2) {
        $firstMonth = $monthlyValues[0];
        $lastMonth = end($monthlyValues);
        $trends['growth_rate'] = $firstMonth > 0 ? (($lastMonth - $firstMonth) / $firstMonth) * 100 : 0;
    }
    
    // Calculate stability score (lower coefficient of variation = more stable)
    if (count($monthlyValues) > 1) {
        $mean = array_sum($monthlyValues) / count($monthlyValues);
        $variance = array_sum(array_map(function($x) use ($mean) { return pow($x - $mean, 2); }, $monthlyValues)) / count($monthlyValues);
        $stdDev = sqrt($variance);
        $coefficientOfVariation = $mean > 0 ? ($stdDev / $mean) : 1;
        $trends['stability_score'] = max(0, 100 - ($coefficientOfVariation * 100));
    }
    
    // Generate insights
    if ($trends['growth_rate'] > 10) {
        $trends['insights'][] = [
            'type' => 'positive_growth',
            'message' => "Pendapatan Anda menunjukkan tren positif dengan pertumbuhan " . number_format($trends['growth_rate'], 1) . "%.",
            'severity' => 'positive'
        ];
    } elseif ($trends['growth_rate'] < -10) {
        $trends['insights'][] = [
            'type' => 'negative_growth',
            'message' => "Pendapatan Anda mengalami penurunan " . number_format(abs($trends['growth_rate']), 1) . "%. Perlu perhatian khusus.",
            'severity' => 'high'
        ];
    }
    
    if ($trends['stability_score'] < 50) {
        $trends['insights'][] = [
            'type' => 'income_volatility',
            'message' => "Pendapatan Anda cukup fluktuatif. Pertimbangkan untuk diversifikasi sumber pendapatan.",
            'severity' => 'medium'
        ];
    }
    
    return $trends;
}

/**
 * Detect financial anomalies
 * @param array $transactions Transaction data
 * @return array Detected anomalies
 */
function detectFinancialAnomalies($transactions) {
    $anomalies = [];
    
    // Calculate statistical thresholds
    $amounts = array_column($transactions, 'jumlah');
    if (empty($amounts)) return $anomalies;
    
    $mean = array_sum($amounts) / count($amounts);
    $variance = array_sum(array_map(function($x) use ($mean) { return pow($x - $mean, 2); }, $amounts)) / count($amounts);
    $stdDev = sqrt($variance);
    $threshold = $mean + (2 * $stdDev); // 2 standard deviations
    
    // Detect unusually large transactions
    foreach ($transactions as $transaction) {
        if ($transaction['jumlah'] > $threshold) {
            $anomalies[] = [
                'type' => 'large_transaction',
                'transaction' => $transaction,
                'message' => "Transaksi dengan jumlah tidak biasa: " . formatRupiah($transaction['jumlah']),
                'severity' => 'medium',
                'date' => $transaction['tanggal']
            ];
        }
    }
    
    // Detect spending spikes
    $dailySpending = [];
    foreach ($transactions as $transaction) {
        if ($transaction['jenis'] === 'pengeluaran') {
            $date = $transaction['tanggal'];
            if (!isset($dailySpending[$date])) {
                $dailySpending[$date] = 0;
            }
            $dailySpending[$date] += $transaction['jumlah'];
        }
    }
    
    if (!empty($dailySpending)) {
        $dailyAmounts = array_values($dailySpending);
        $dailyMean = array_sum($dailyAmounts) / count($dailyAmounts);
        $dailyThreshold = $dailyMean * 3; // 3x average daily spending
        
        foreach ($dailySpending as $date => $amount) {
            if ($amount > $dailyThreshold) {
                $anomalies[] = [
                    'type' => 'spending_spike',
                    'date' => $date,
                    'amount' => $amount,
                    'message' => "Pengeluaran harian tinggi pada $date: " . formatRupiah($amount),
                    'severity' => 'high'
                ];
            }
        }
    }
    
    return $anomalies;
}

/**
 * Generate financial predictions
 * @param array $transactions Transaction data
 * @return array Predictions
 */
function generateFinancialPredictions($transactions) {
    $predictions = [
        'next_month_income' => 0,
        'next_month_expenses' => 0,
        'cash_flow_forecast' => [],
        'insights' => []
    ];
    
    // Simple moving average prediction
    $monthlyData = [];
    foreach ($transactions as $transaction) {
        $monthKey = $transaction['tahun'] . '-' . str_pad($transaction['bulan'], 2, '0', STR_PAD_LEFT);
        if (!isset($monthlyData[$monthKey])) {
            $monthlyData[$monthKey] = ['income' => 0, 'expenses' => 0];
        }
        
        if ($transaction['jenis'] === 'pemasukan') {
            $monthlyData[$monthKey]['income'] += $transaction['jumlah'];
        } else {
            $monthlyData[$monthKey]['expenses'] += $transaction['jumlah'];
        }
    }
    
    if (!empty($monthlyData)) {
        $recentMonths = array_slice($monthlyData, -3, 3, true); // Last 3 months
        
        $avgIncome = array_sum(array_column($recentMonths, 'income')) / count($recentMonths);
        $avgExpenses = array_sum(array_column($recentMonths, 'expenses')) / count($recentMonths);
        
        $predictions['next_month_income'] = $avgIncome;
        $predictions['next_month_expenses'] = $avgExpenses;
        
        // Generate 3-month forecast
        for ($i = 1; $i <= 3; $i++) {
            $forecastDate = date('Y-m', strtotime("+$i month"));
            $predictions['cash_flow_forecast'][$forecastDate] = [
                'income' => $avgIncome,
                'expenses' => $avgExpenses,
                'net_flow' => $avgIncome - $avgExpenses
            ];
        }
        
        // Generate insights
        $netFlow = $avgIncome - $avgExpenses;
        if ($netFlow < 0) {
            $predictions['insights'][] = [
                'type' => 'negative_cash_flow',
                'message' => "Prediksi cash flow negatif bulan depan: " . formatRupiah($netFlow),
                'severity' => 'high'
            ];
        } elseif ($netFlow > 0) {
            $predictions['insights'][] = [
                'type' => 'positive_cash_flow',
                'message' => "Prediksi cash flow positif bulan depan: " . formatRupiah($netFlow),
                'severity' => 'positive'
            ];
        }
    }
    
    return $predictions;
}

/**
 * Generate recommendations based on insights
 * @param array $insights All insights data
 * @return array Recommendations
 */
function generateRecommendations($insights) {
    $recommendations = [];
    
    // Budget recommendations
    if (isset($insights['spending_patterns']['by_category'])) {
        $categories = $insights['spending_patterns']['by_category'];
        arsort($categories);
        $topCategory = array_key_first($categories);
        $topAmount = $categories[$topCategory];
        $totalSpending = array_sum($categories);
        
        if (($topAmount / $totalSpending) > 0.4) {
            $recommendations[] = [
                'type' => 'budget_diversification',
                'title' => 'Diversifikasi Pengeluaran',
                'message' => "Pertimbangkan untuk mengurangi pengeluaran di kategori '$topCategory' dan alokasikan ke kategori lain.",
                'priority' => 'medium',
                'action' => 'Set budget limit untuk kategori ' . $topCategory
            ];
        }
    }
    
    // Savings recommendations
    if (isset($insights['predictions']['next_month_income']) && isset($insights['predictions']['next_month_expenses'])) {
        $income = $insights['predictions']['next_month_income'];
        $expenses = $insights['predictions']['next_month_expenses'];
        $savingsRate = $income > 0 ? (($income - $expenses) / $income) * 100 : 0;
        
        if ($savingsRate < 20) {
            $recommendations[] = [
                'type' => 'increase_savings',
                'title' => 'Tingkatkan Tabungan',
                'message' => "Tingkat tabungan Anda saat ini " . number_format($savingsRate, 1) . "%. Target ideal adalah 20-30%.",
                'priority' => 'high',
                'action' => 'Buat target tabungan bulanan'
            ];
        }
    }
    
    // Income stability recommendations
    if (isset($insights['income_trends']['stability_score']) && $insights['income_trends']['stability_score'] < 60) {
        $recommendations[] = [
            'type' => 'income_stability',
            'title' => 'Stabilkan Pendapatan',
            'message' => "Pendapatan Anda cukup fluktuatif. Pertimbangkan untuk mencari sumber pendapatan yang lebih stabil.",
            'priority' => 'medium',
            'action' => 'Diversifikasi sumber pendapatan'
        ];
    }
    
    return $recommendations;
}

/**
 * Assess financial risk
 * @param array $transactions Transaction data
 * @return array Risk assessment
 */
function assessFinancialRisk($transactions) {
    $risk = [
        'overall_score' => 0,
        'factors' => [],
        'level' => 'low',
        'recommendations' => []
    ];
    
    $riskFactors = [];
    
    // Calculate monthly data
    $monthlyData = [];
    foreach ($transactions as $transaction) {
        $monthKey = $transaction['tahun'] . '-' . str_pad($transaction['bulan'], 2, '0', STR_PAD_LEFT);
        if (!isset($monthlyData[$monthKey])) {
            $monthlyData[$monthKey] = ['income' => 0, 'expenses' => 0];
        }
        
        if ($transaction['jenis'] === 'pemasukan') {
            $monthlyData[$monthKey]['income'] += $transaction['jumlah'];
        } else {
            $monthlyData[$monthKey]['expenses'] += $transaction['jumlah'];
        }
    }
    
    if (!empty($monthlyData)) {
        // 1. Income volatility risk
        $incomes = array_column($monthlyData, 'income');
        if (count($incomes) > 1) {
            $mean = array_sum($incomes) / count($incomes);
            $variance = array_sum(array_map(function($x) use ($mean) { return pow($x - $mean, 2); }, $incomes)) / count($incomes);
            $coefficientOfVariation = $mean > 0 ? (sqrt($variance) / $mean) : 0;
            
            if ($coefficientOfVariation > 0.3) {
                $riskFactors[] = [
                    'factor' => 'income_volatility',
                    'score' => min(100, $coefficientOfVariation * 100),
                    'description' => 'Pendapatan tidak stabil'
                ];
            }
        }
        
        // 2. Expense ratio risk
        $avgIncome = array_sum($incomes) / count($incomes);
        $avgExpenses = array_sum(array_column($monthlyData, 'expenses')) / count($monthlyData);
        $expenseRatio = $avgIncome > 0 ? ($avgExpenses / $avgIncome) : 1;
        
        if ($expenseRatio > 0.8) {
            $riskFactors[] = [
                'factor' => 'high_expense_ratio',
                'score' => min(100, $expenseRatio * 100),
                'description' => 'Rasio pengeluaran tinggi'
            ];
        }
        
        // 3. Negative cash flow months
        $negativeMonths = 0;
        foreach ($monthlyData as $data) {
            if ($data['expenses'] > $data['income']) {
                $negativeMonths++;
            }
        }
        
        $negativeRatio = $negativeMonths / count($monthlyData);
        if ($negativeRatio > 0.2) {
            $riskFactors[] = [
                'factor' => 'frequent_deficits',
                'score' => $negativeRatio * 100,
                'description' => 'Sering mengalami defisit bulanan'
            ];
        }
    }
    
    // Calculate overall risk score
    if (!empty($riskFactors)) {
        $risk['overall_score'] = array_sum(array_column($riskFactors, 'score')) / count($riskFactors);
        $risk['factors'] = $riskFactors;
        
        if ($risk['overall_score'] > 70) {
            $risk['level'] = 'high';
        } elseif ($risk['overall_score'] > 40) {
            $risk['level'] = 'medium';
        } else {
            $risk['level'] = 'low';
        }
    }
    
    return $risk;
}
?>
