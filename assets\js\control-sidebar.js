/**
 * Control Sidebar JavaScript - Consolidated and Optimized
 * Handles Control Sidebar functionality and layout customization
 */

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    console.log('Control Sidebar: Initializing...');

    // Try to initialize immediately
    tryInitialize();

    // If elements not found, try again after a short delay
    setTimeout(function() {
        if (!document.getElementById('controlSidebar')) {
            console.log('Control Sidebar: Retrying initialization after delay...');
            tryInitialize();
        }
    }, 100);

    // Also try after window load
    window.addEventListener('load', function() {
        if (!document.getElementById('controlSidebar')) {
            console.log('Control Sidebar: Retrying initialization after window load...');
            tryInitialize();
        }
    });

    console.log('Control Sidebar: Initialization complete');
});

function tryInitialize() {
    initializeControlSidebar();
    initializeColorVariants();
    initializeIconVariants();
    initializeLayoutOptions();
    initializeAllFeatures();
    loadSavedSettings();
}

/**
 * Initialize Control Sidebar
 */
function initializeControlSidebar() {
    const controlSidebar = document.getElementById('controlSidebar');
    const controlSidebarOverlay = document.getElementById('controlSidebarOverlay');
    const controlSidebarToggle = document.querySelector('[data-widget="control-sidebar"]');
    const controlSidebarClose = document.querySelector('.control-sidebar-close');

    console.log('Control Sidebar: Elements found - sidebar:', !!controlSidebar, 'overlay:', !!controlSidebarOverlay, 'toggle:', !!controlSidebarToggle, 'close:', !!controlSidebarClose);

    if (!controlSidebar) {
        console.warn('Control Sidebar: Control sidebar element not found');
        return; // Control sidebar not present
    }

    if (!controlSidebarOverlay) {
        console.warn('Control Sidebar: Control sidebar overlay not found');
        return;
    }

    // Toggle control sidebar
    if (controlSidebarToggle) {
        console.log('🎯 Control Sidebar: Toggle button found, adding event listener');
        controlSidebarToggle.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            console.log('🚀 Control Sidebar: Toggle button clicked!');
            toggleControlSidebar();
        });

        // Also add onclick as backup
        controlSidebarToggle.onclick = function(e) {
            e.preventDefault();
            e.stopPropagation();
            console.log('🚀 Control Sidebar: Toggle button clicked via onclick!');
            toggleControlSidebar();
        };

        console.log('✅ Control Sidebar: Event listeners added successfully');
    } else {
        console.warn('❌ Control Sidebar: Toggle button not found');

        // Try to find button with different selectors
        const altToggle1 = document.getElementById('controlSidebarToggleBtn');
        const altToggle2 = document.querySelector('.control-sidebar-toggle');
        const altToggle3 = document.querySelector('[title="Customize Layout"]');

        console.log('🔍 Alternative button search:', {
            byId: !!altToggle1,
            byClass: !!altToggle2,
            byTitle: !!altToggle3
        });

        if (altToggle1) {
            console.log('✅ Found button by ID, adding event listener');
            altToggle1.addEventListener('click', function(e) {
                e.preventDefault();
                console.log('🚀 Control Sidebar: Button clicked via ID selector!');
                toggleControlSidebar();
            });
        }
    }

    // Close control sidebar
    if (controlSidebarClose) {
        controlSidebarClose.addEventListener('click', function() {
            closeControlSidebar();
        });
    }

    // Close on overlay click
    if (controlSidebarOverlay) {
        controlSidebarOverlay.addEventListener('click', function() {
            closeControlSidebar();
        });
    }

    function toggleControlSidebar() {
        console.log('🎯 Control Sidebar: toggleControlSidebar called');

        // Re-query elements in case they weren't available during initialization
        const currentControlSidebar = document.getElementById('controlSidebar');
        const currentControlSidebarOverlay = document.getElementById('controlSidebarOverlay');

        console.log('🔍 Control Sidebar: Current elements check:', {
            sidebar: !!currentControlSidebar,
            overlay: !!currentControlSidebarOverlay,
            sidebarFromInit: !!controlSidebar,
            overlayFromInit: !!controlSidebarOverlay
        });

        const sidebarElement = currentControlSidebar || controlSidebar;
        const overlayElement = currentControlSidebarOverlay || controlSidebarOverlay;

        if (sidebarElement && overlayElement) {
            const isOpen = sidebarElement.classList.contains('open');
            console.log('✅ Control Sidebar: Elements found! Current state - open:', isOpen);

            // Toggle classes
            sidebarElement.classList.toggle('open');
            overlayElement.classList.toggle('show');
            document.body.style.overflow = sidebarElement.classList.contains('open') ? 'hidden' : '';

            const newState = sidebarElement.classList.contains('open');
            console.log('🎉 Control Sidebar: Toggle complete! New state - open:', newState);
            console.log('📊 Control Sidebar: Element classes:', {
                sidebarClasses: sidebarElement.className,
                overlayClasses: overlayElement.className,
                bodyOverflow: document.body.style.overflow
            });
        } else {
            console.error('❌ Control Sidebar: Elements not found!', {
                sidebarFromQuery: !!currentControlSidebar,
                overlayFromQuery: !!currentControlSidebarOverlay,
                sidebarFromInit: !!controlSidebar,
                overlayFromInit: !!controlSidebarOverlay,
                allElements: document.querySelectorAll('*').length,
                elementsWithId: document.querySelectorAll('[id]').length
            });

            // Show user-friendly error
            alert('Layout Customizer not available. Please refresh the page and try again.');
        }
    }

    function closeControlSidebar() {
        if (controlSidebar && controlSidebarOverlay) {
            controlSidebar.classList.remove('open');
            controlSidebarOverlay.classList.remove('show');
            document.body.style.overflow = '';
        }
    }
}

/**
 * Initialize Color Variants
 */
function initializeColorVariants() {
    const colorOptions = document.querySelectorAll('.color-option');
    colorOptions.forEach(option => {
        option.addEventListener('click', function() {
            const target = this.dataset.target;
            const color = this.dataset.color;

            // Remove active class from siblings
            this.parentElement.querySelectorAll('.color-option').forEach(opt => {
                opt.classList.remove('active');
            });

            // Add active class to clicked option
            this.classList.add('active');

            // Apply color variant
            applyColorVariant(target, color);

            // Save setting
            saveSettings(`${target}Color`, color);

            // Show notification
            showLivePreview('navbarColor', color);
        });
    });
}

/**
 * Initialize Icon Variants
 */
function initializeIconVariants() {
    const iconOptions = document.querySelectorAll('.icon-option');
    iconOptions.forEach(option => {
        option.addEventListener('click', function() {
            const target = this.dataset.target;
            const iconColor = this.dataset.iconColor;

            // Remove active class from siblings
            this.parentElement.querySelectorAll('.icon-option').forEach(opt => {
                opt.classList.remove('active');
            });

            // Add active class to clicked option
            this.classList.add('active');

            // Apply icon color variant
            applyIconVariant(target, iconColor);

            // Save setting
            saveSettings(`${target}Color`, iconColor);
        });
    });
}

/**
 * Initialize Layout Options
 */
function initializeLayoutOptions() {
    // Dark Mode Switch
    const darkModeSwitch = document.getElementById('darkModeSwitch');
    if (darkModeSwitch) {
        const currentTheme = localStorage.getItem('theme') || 'light';
        darkModeSwitch.checked = currentTheme === 'dark';

        darkModeSwitch.addEventListener('change', function() {
            const newTheme = this.checked ? 'dark' : 'light';
            document.documentElement.setAttribute('data-bs-theme', newTheme);
            localStorage.setItem('theme', newTheme);

            // Update theme toggle buttons
            updateThemeToggleButton(newTheme);

            // Show notification
            showLivePreview('darkMode', this.checked);
        });
    }

    // Sidebar Collapsed
    const sidebarCollapsed = document.getElementById('sidebarCollapsed');
    if (sidebarCollapsed) {
        sidebarCollapsed.checked = document.body.classList.contains('sidebar-collapsed');
        sidebarCollapsed.addEventListener('change', function() {
            if (this.checked) {
                document.body.classList.add('sidebar-collapsed');
            } else {
                document.body.classList.remove('sidebar-collapsed');
            }
            localStorage.setItem('sidebarCollapsed', this.checked);
        });
    }

    // Header Fixed
    const headerFixed = document.getElementById('headerFixed');
    if (headerFixed) {
        headerFixed.addEventListener('change', function() {
            const navbar = document.querySelector('.modern-navbar');
            if (navbar) {
                if (this.checked) {
                    navbar.style.position = 'fixed';
                    document.body.classList.add('header-fixed');
                } else {
                    navbar.style.position = 'relative';
                    document.body.classList.remove('header-fixed');
                }
            }
            saveSettings('headerFixed', this.checked);
        });
    }

    // No Border
    const noBorder = document.getElementById('noBorder');
    if (noBorder) {
        noBorder.addEventListener('change', function() {
            if (this.checked) {
                document.body.classList.add('header-no-border');
            } else {
                document.body.classList.remove('header-no-border');
            }
            saveSettings('noBorder', this.checked);
        });
    }

    // Dropdown Legacy
    const dropdownLegacy = document.getElementById('dropdownLegacy');
    if (dropdownLegacy) {
        dropdownLegacy.addEventListener('change', function() {
            if (this.checked) {
                document.body.classList.add('dropdown-legacy');
            } else {
                document.body.classList.remove('dropdown-legacy');
            }
            saveSettings('dropdownLegacy', this.checked);
        });
    }

    // Footer Fixed
    const footerFixed = document.getElementById('footerFixed');
    if (footerFixed) {
        footerFixed.addEventListener('change', function() {
            if (this.checked) {
                document.body.classList.add('footer-fixed');
            } else {
                document.body.classList.remove('footer-fixed');
            }
            saveSettings('footerFixed', this.checked);
        });
    }

    // Sidebar Fixed
    const sidebarFixed = document.getElementById('sidebarFixed');
    if (sidebarFixed) {
        sidebarFixed.addEventListener('change', function() {
            const sidebar = document.querySelector('.modern-sidebar');
            if (sidebar) {
                if (this.checked) {
                    sidebar.style.position = 'fixed';
                    document.body.classList.add('sidebar-fixed');
                } else {
                    sidebar.style.position = 'absolute';
                    document.body.classList.remove('sidebar-fixed');
                }
            }
            saveSettings('sidebarFixed', this.checked);
        });
    }

    // Small Text Options
    const textOptions = ['navbarSmall', 'sidebarNavSmall'];
    textOptions.forEach(option => {
        const checkbox = document.getElementById(option);
        if (checkbox) {
            checkbox.addEventListener('change', function() {
                const target = option.replace('Small', '');
                applySmallText(target, this.checked);
                saveSettings(option, this.checked);
            });
        }
    });

    // Sidebar Options
    const sidebarOptions = ['sidebarMini', 'sidebarMiniMD', 'sidebarMiniXS', 'navFlat', 'navLegacy', 'navCompact', 'navChildIndent', 'navChildHide', 'disableHover'];
    sidebarOptions.forEach(option => {
        const checkbox = document.getElementById(option);
        if (checkbox) {
            checkbox.addEventListener('change', function() {
                applySidebarOption(option, this.checked);
                saveSettings(option, this.checked);
            });
        }
    });

    // Enhanced Layout Options
    const enhancedOptions = ['navbarShadow', 'sidebarShadow'];
    enhancedOptions.forEach(option => {
        const checkbox = document.getElementById(option);
        if (checkbox) {
            checkbox.addEventListener('change', function() {
                applyEnhancedOption(option, this.checked);
                saveSettings(option, this.checked);
            });
        }
    });
}

/**
 * Apply Color Variant
 */
function applyColorVariant(target, color) {
    switch(target) {
        case 'navbar':
            applyNavbarColor(color);
            break;
    }
}

/**
 * Apply Icon Variant
 */
function applyIconVariant(target, iconColor) {
    switch(target) {
        case 'navbar-icons':
            applyNavbarIconColor(iconColor);
            break;
    }
}

/**
 * Apply Navbar Color
 */
function applyNavbarColor(color) {
    const navbar = document.querySelector('.modern-navbar');
    if (navbar) {
        // Remove existing variant classes
        navbar.className = navbar.className.replace(/navbar-variant-[\w-]+/g, '');

        // Add new variant class
        navbar.classList.add(`navbar-variant-${color}`);

        // Update text color for navbar elements based on background
        const navbarElements = navbar.querySelectorAll('a, span, button:not(.btn), .navbar-brand, .nav-link');
        navbarElements.forEach(element => {
            if (color === 'white' || color === 'transparent') {
                element.style.color = '#333333';
            } else {
                element.style.color = '#ffffff';
            }
        });
    }
}

/**
 * Apply Navbar Icon Color
 */
function applyNavbarIconColor(iconColor) {
    const navbar = document.querySelector('.modern-navbar');
    if (navbar) {
        // Remove existing icon color classes
        navbar.className = navbar.className.replace(/navbar-icons-\w+/g, '');

        // Add new icon color class
        navbar.classList.add(`navbar-icons-${iconColor}`);
    }
}

/**
 * Apply Small Text
 */
function applySmallText(target, enabled) {
    const targetMap = {
        'navbar': '.modern-navbar',
        'sidebarNav': '.modern-sidebar'
    };

    const element = document.querySelector(targetMap[target]);
    if (element) {
        if (enabled) {
            element.classList.add('text-sm');
        } else {
            element.classList.remove('text-sm');
        }
    }
}

/**
 * Apply Sidebar Option
 */
function applySidebarOption(option, enabled) {
    const sidebar = document.querySelector('.modern-sidebar');
    if (!sidebar) return;

    const classMap = {
        'sidebarMini': 'sidebar-mini',
        'sidebarMiniMD': 'sidebar-mini-md',
        'sidebarMiniXS': 'sidebar-mini-xs',
        'navFlat': 'nav-flat',
        'navLegacy': 'nav-legacy',
        'navCompact': 'nav-compact',
        'navChildIndent': 'nav-child-indent',
        'navChildHide': 'nav-child-hide',
        'disableHover': 'nav-disable-hover'
    };

    const className = classMap[option];
    if (className) {
        if (enabled) {
            sidebar.classList.add(className);
        } else {
            sidebar.classList.remove(className);
        }
    }
}

/**
 * Apply Enhanced Option
 */
function applyEnhancedOption(option, enabled) {
    const classMap = {
        'navbarShadow': 'navbar-shadow',
        'sidebarShadow': 'sidebar-shadow'
    };

    const className = classMap[option];
    if (className) {
        if (enabled) {
            document.body.classList.add(className);
        } else {
            document.body.classList.remove(className);
        }
    }
}

/**
 * Update Theme Toggle Button
 */
function updateThemeToggleButton(theme) {
    const toggleButtons = document.querySelectorAll('[data-theme-toggle]');
    toggleButtons.forEach(button => {
        const icon = button.querySelector('i');
        if (icon) {
            icon.className = theme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
        }
        button.setAttribute('title', theme === 'dark' ? 'Switch to Light Mode' : 'Switch to Dark Mode');
    });
}

/**
 * Save Settings
 */
function saveSettings(key, value) {
    localStorage.setItem(`layout_${key}`, JSON.stringify(value));
}

/**
 * Load Saved Settings
 */
function loadSavedSettings() {
    // Load and apply all saved settings
    const settings = [
        'headerFixed', 'footerFixed', 'sidebarFixed', 'noBorder', 'dropdownLegacy',
        'navbarSmall', 'sidebarNavSmall',
        'sidebarMini', 'sidebarMiniMD', 'sidebarMiniXS', 'navFlat', 'navLegacy',
        'navCompact', 'navChildIndent', 'navChildHide', 'disableHover',
        'navbarShadow', 'sidebarShadow'
    ];

    const colorSettings = [
        'navbarColor'
    ];

    const iconSettings = [
        'navbar-iconsColor'
    ];

    // Load boolean settings
    settings.forEach(setting => {
        const saved = localStorage.getItem(`layout_${setting}`);
        if (saved !== null) {
            const value = JSON.parse(saved);
            const checkbox = document.getElementById(setting);

            if (checkbox && typeof value === 'boolean') {
                checkbox.checked = value;
                checkbox.dispatchEvent(new Event('change'));
            }
        }
    });

    // Load color settings
    colorSettings.forEach(setting => {
        const saved = localStorage.getItem(`layout_${setting}`);
        if (saved !== null) {
            const value = JSON.parse(saved);
            const target = setting.replace('Color', '');
            applyColorVariant(target, value);

            // Update active color option
            const colorOption = document.querySelector(`[data-target="${target}"][data-color="${value}"]`);
            if (colorOption) {
                colorOption.parentElement.querySelectorAll('.color-option').forEach(opt => {
                    opt.classList.remove('active');
                });
                colorOption.classList.add('active');
            }
        }
    });

    // Load icon settings
    iconSettings.forEach(setting => {
        const saved = localStorage.getItem(`layout_${setting}`);
        if (saved !== null) {
            const value = JSON.parse(saved);
            const target = setting.replace('Color', '');
            applyIconVariant(target, value);

            // Update active icon option
            const iconOption = document.querySelector(`[data-target="${target}"][data-icon-color="${value}"]`);
            if (iconOption) {
                iconOption.parentElement.querySelectorAll('.icon-option').forEach(opt => {
                    opt.classList.remove('active');
                });
                iconOption.classList.add('active');
            }
        }
    });

    // Load dark mode setting
    const darkModeSwitch = document.getElementById('darkModeSwitch');
    if (darkModeSwitch) {
        const currentTheme = localStorage.getItem('theme') || 'light';
        darkModeSwitch.checked = currentTheme === 'dark';
    }

    // Load sidebar collapsed setting
    const sidebarCollapsed = document.getElementById('sidebarCollapsed');
    if (sidebarCollapsed) {
        const isCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
        sidebarCollapsed.checked = isCollapsed;
        if (isCollapsed) {
            document.body.classList.add('sidebar-collapsed');
        }
    }
}

/**
 * Global function to toggle control sidebar
 */
function toggleControlSidebarGlobal() {
    const controlSidebar = document.getElementById('controlSidebar');
    const controlSidebarOverlay = document.getElementById('controlSidebarOverlay');

    if (controlSidebar && controlSidebarOverlay) {
        const isOpen = controlSidebar.classList.contains('open');
        console.log('Global Toggle: Current state - open:', isOpen);

        controlSidebar.classList.toggle('open');
        controlSidebarOverlay.classList.toggle('show');
        document.body.style.overflow = controlSidebar.classList.contains('open') ? 'hidden' : '';

        console.log('Global Toggle: New state - open:', controlSidebar.classList.contains('open'));
    } else {
        console.warn('Global Toggle: Control sidebar elements not found');
    }
}

// Expose global functions
window.toggleControlSidebarGlobal = toggleControlSidebarGlobal;

// Also expose the main toggle function
window.toggleControlSidebar = function() {
    console.log('🌐 Global toggleControlSidebar called');

    const controlSidebar = document.getElementById('controlSidebar');
    const controlSidebarOverlay = document.getElementById('controlSidebarOverlay');

    if (controlSidebar && controlSidebarOverlay) {
        const isOpen = controlSidebar.classList.contains('open');
        console.log('🌐 Global: Current state - open:', isOpen);

        controlSidebar.classList.toggle('open');
        controlSidebarOverlay.classList.toggle('show');
        document.body.style.overflow = controlSidebar.classList.contains('open') ? 'hidden' : '';

        console.log('🌐 Global: New state - open:', controlSidebar.classList.contains('open'));
    } else {
        console.error('🌐 Global: Elements not found');
    }
};

// Expose open function specifically
window.openControlSidebar = function() {
    console.log('🌐 Global openControlSidebar called');

    const controlSidebar = document.getElementById('controlSidebar');
    const controlSidebarOverlay = document.getElementById('controlSidebarOverlay');

    if (controlSidebar && controlSidebarOverlay) {
        console.log('🌐 Global: Opening control sidebar');
        controlSidebar.classList.add('open');
        controlSidebarOverlay.classList.add('show');
        document.body.style.overflow = 'hidden';
        console.log('🌐 Global: Control sidebar opened');
    } else {
        console.error('🌐 Global: Cannot open - elements not found');
    }
};

// Expose close function specifically
window.closeControlSidebar = function() {
    console.log('🌐 Global closeControlSidebar called');

    const controlSidebar = document.getElementById('controlSidebar');
    const controlSidebarOverlay = document.getElementById('controlSidebarOverlay');

    if (controlSidebar && controlSidebarOverlay) {
        console.log('🌐 Global: Closing control sidebar');
        controlSidebar.classList.remove('open');
        controlSidebarOverlay.classList.remove('show');
        document.body.style.overflow = '';
        console.log('🌐 Global: Control sidebar closed');
    } else {
        console.error('🌐 Global: Cannot close - elements not found');
    }
};

/**
 * Reset All Settings
 */
function resetAllSettings() {
    console.log('🔄 Resetting all layout settings...');

    // Confirm with user
    if (!confirm('Are you sure you want to reset all layout settings to default? This action cannot be undone.')) {
        return;
    }

    // Clear all layout settings from localStorage
    const keys = Object.keys(localStorage);
    keys.forEach(key => {
        if (key.startsWith('layout_')) {
            localStorage.removeItem(key);
        }
    });

    // Reset theme
    localStorage.removeItem('theme');
    localStorage.removeItem('sidebarCollapsed');

    // Reset to default state
    document.documentElement.setAttribute('data-bs-theme', 'light');
    document.body.className = '';

    // Reset navbar
    const navbar = document.querySelector('.modern-navbar');
    if (navbar) {
        navbar.className = 'modern-navbar navbar navbar-expand-lg';
    }

    // Reset sidebar
    const sidebar = document.querySelector('.modern-sidebar');
    if (sidebar) {
        sidebar.className = 'modern-sidebar';
    }

    // Reload page to apply all defaults
    setTimeout(() => {
        window.location.reload();
    }, 500);

    console.log('✅ All settings reset successfully');
}

/**
 * Export Settings
 */
function exportSettings() {
    console.log('📤 Exporting layout settings...');

    const settings = {};
    const keys = Object.keys(localStorage);

    keys.forEach(key => {
        if (key.startsWith('layout_') || key === 'theme' || key === 'sidebarCollapsed') {
            settings[key] = localStorage.getItem(key);
        }
    });

    const dataStr = JSON.stringify(settings, null, 2);
    const dataBlob = new Blob([dataStr], {type: 'application/json'});

    const link = document.createElement('a');
    link.href = URL.createObjectURL(dataBlob);
    link.download = `keuanganku-layout-settings-${new Date().toISOString().split('T')[0]}.json`;
    link.click();

    console.log('✅ Settings exported successfully');
}

/**
 * Import Settings
 */
function importSettings() {
    console.log('📥 Importing layout settings...');

    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';

    input.onchange = function(e) {
        const file = e.target.files[0];
        if (!file) return;

        const reader = new FileReader();
        reader.onload = function(e) {
            try {
                const settings = JSON.parse(e.target.result);

                // Validate settings
                if (typeof settings !== 'object') {
                    throw new Error('Invalid settings format');
                }

                // Confirm with user
                if (!confirm('Are you sure you want to import these settings? This will overwrite your current layout settings.')) {
                    return;
                }

                // Clear existing settings
                const keys = Object.keys(localStorage);
                keys.forEach(key => {
                    if (key.startsWith('layout_') || key === 'theme' || key === 'sidebarCollapsed') {
                        localStorage.removeItem(key);
                    }
                });

                // Import new settings
                Object.keys(settings).forEach(key => {
                    localStorage.setItem(key, settings[key]);
                });

                // Reload page to apply settings
                setTimeout(() => {
                    window.location.reload();
                }, 500);

                console.log('✅ Settings imported successfully');

            } catch (error) {
                console.error('❌ Error importing settings:', error);
                alert('Error importing settings. Please check the file format.');
            }
        };
        reader.readAsText(file);
    };

    input.click();
}

/**
 * Show Toast Notification
 */
function showToast(message, type = 'success') {
    // Create toast container if it doesn't exist
    let toastContainer = document.getElementById('toastContainer');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toastContainer';
        toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
        toastContainer.style.zIndex = '9999';
        document.body.appendChild(toastContainer);
    }

    // Create toast element
    const toastId = 'toast_' + Date.now();
    const toastHtml = `
        <div id="${toastId}" class="toast align-items-center text-bg-${type} border-0" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="d-flex">
                <div class="toast-body">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'warning' ? 'exclamation-triangle' : type === 'danger' ? 'times-circle' : 'info-circle'} me-2"></i>
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        </div>
    `;

    toastContainer.insertAdjacentHTML('beforeend', toastHtml);

    // Initialize and show toast
    const toastElement = document.getElementById(toastId);
    const toast = new bootstrap.Toast(toastElement, {
        autohide: true,
        delay: 3000
    });

    toast.show();

    // Remove toast element after it's hidden
    toastElement.addEventListener('hidden.bs.toast', function() {
        this.remove();
    });
}

/**
 * Enhanced Save Settings with Toast
 */
function saveSettingsWithToast(key, value, message = null) {
    saveSettings(key, value);

    if (message) {
        showToast(message, 'success');
    }
}

/**
 * Live Preview Notification
 */
function showLivePreview(setting, value) {
    const messages = {
        'darkMode': value ? 'Dark mode enabled' : 'Light mode enabled',
        'sidebarCollapsed': value ? 'Sidebar collapsed' : 'Sidebar expanded',
        'headerFixed': value ? 'Header fixed' : 'Header relative',
        'footerFixed': value ? 'Footer fixed' : 'Footer relative',
        'navbarColor': `Navbar color changed to ${value}`,
        'iconColor': `Icon color changed to ${value}`,
        'textSize': value ? 'Small text enabled' : 'Normal text size',
        'shadow': value ? 'Shadow enabled' : 'Shadow disabled'
    };

    const message = messages[setting] || `${setting} ${value ? 'enabled' : 'disabled'}`;
    showToast(message, 'info');
}

/**
 * Initialize All New Features
 */
function initializeAllFeatures() {
    initializeSidebarCustomization();
    initializeContentAreaCustomization();
    initializeTypographyCustomization();
    initializeAnimationEffects();
    initializeAccessibilityFeatures();
    initializeLayoutPresets();
}

/**
 * Initialize Sidebar Customization
 */
function initializeSidebarCustomization() {
    // Sidebar color variants
    const sidebarColorOptions = document.querySelectorAll('[data-target="sidebar"]');
    sidebarColorOptions.forEach(option => {
        option.addEventListener('click', function() {
            const color = this.dataset.color;
            applySidebarColor(color);
            saveSettings('sidebarColor', color);
            showLivePreview('sidebarColor', color);
        });
    });

    // Sidebar layout options
    const sidebarOptions = [
        'sidebarFixed', 'sidebarMini', 'sidebarMiniMD', 'sidebarMiniXS',
        'sidebarNavSmall', 'sidebarShadow', 'navFlat', 'navLegacy',
        'navCompact', 'navChildIndent', 'navChildHide', 'disableHover'
    ];

    sidebarOptions.forEach(optionId => {
        const element = document.getElementById(optionId);
        if (element) {
            element.addEventListener('change', function() {
                applySidebarOption(optionId, this.checked);
                saveSettings(optionId, this.checked);
                showLivePreview(optionId, this.checked);
            });
        }
    });
}

/**
 * Initialize Content Area Customization
 */
function initializeContentAreaCustomization() {
    // Content background variants
    const contentColorOptions = document.querySelectorAll('[data-target="content"]');
    contentColorOptions.forEach(option => {
        option.addEventListener('click', function() {
            const color = this.dataset.color;
            applyContentBackground(color);
            saveSettings('contentBackground', color);
            showLivePreview('contentBackground', color);
        });
    });

    // Content layout options
    const contentOptions = ['contentBoxed', 'contentPadding', 'cardShadows', 'roundedCorners'];
    contentOptions.forEach(optionId => {
        const element = document.getElementById(optionId);
        if (element) {
            element.addEventListener('change', function() {
                applyContentOption(optionId, this.checked);
                saveSettings(optionId, this.checked);
                showLivePreview(optionId, this.checked);
            });
        }
    });
}

/**
 * Initialize Typography Customization
 */
function initializeTypographyCustomization() {
    // Font family
    const fontFamily = document.getElementById('fontFamily');
    if (fontFamily) {
        fontFamily.addEventListener('change', function() {
            applyFontFamily(this.value);
            saveSettings('fontFamily', this.value);
            showLivePreview('fontFamily', this.value);
        });
    }

    // Font size
    const fontSize = document.getElementById('fontSize');
    if (fontSize) {
        fontSize.addEventListener('change', function() {
            applyFontSize(this.value);
            saveSettings('fontSize', this.value);
            showLivePreview('fontSize', this.value);
        });
    }

    // Typography options
    const typographyOptions = ['fontSmoothing', 'textShadow'];
    typographyOptions.forEach(optionId => {
        const element = document.getElementById(optionId);
        if (element) {
            element.addEventListener('change', function() {
                applyTypographyOption(optionId, this.checked);
                saveSettings(optionId, this.checked);
                showLivePreview(optionId, this.checked);
            });
        }
    });
}

/**
 * Initialize Animation & Effects
 */
function initializeAnimationEffects() {
    // Animation speed
    const animationSpeed = document.getElementById('animationSpeed');
    if (animationSpeed) {
        animationSpeed.addEventListener('change', function() {
            applyAnimationSpeed(this.value);
            saveSettings('animationSpeed', this.value);
            showLivePreview('animationSpeed', this.value);
        });
    }

    // Effect options
    const effectOptions = ['hoverEffects', 'fadeTransitions', 'slideAnimations', 'parallaxEffect'];
    effectOptions.forEach(optionId => {
        const element = document.getElementById(optionId);
        if (element) {
            element.addEventListener('change', function() {
                applyEffectOption(optionId, this.checked);
                saveSettings(optionId, this.checked);
                showLivePreview(optionId, this.checked);
            });
        }
    });
}

/**
 * Initialize Accessibility Features
 */
function initializeAccessibilityFeatures() {
    const accessibilityOptions = ['highContrast', 'focusIndicators', 'reducedMotion', 'screenReaderOptimized'];
    accessibilityOptions.forEach(optionId => {
        const element = document.getElementById(optionId);
        if (element) {
            element.addEventListener('change', function() {
                applyAccessibilityOption(optionId, this.checked);
                saveSettings(optionId, this.checked);
                showLivePreview(optionId, this.checked);
            });
        }
    });
}

/**
 * Apply Functions
 */
function applySidebarColor(color) {
    const sidebar = document.querySelector('.modern-sidebar');
    if (sidebar) {
        // Remove existing color classes
        sidebar.className = sidebar.className.replace(/sidebar-variant-\w+/g, '');
        // Add new color class
        sidebar.classList.add(`sidebar-variant-${color}`);
    }
}

function applySidebarOption(option, enabled) {
    const body = document.body;
    const className = option.replace(/([A-Z])/g, '-$1').toLowerCase();

    if (enabled) {
        body.classList.add(className);
    } else {
        body.classList.remove(className);
    }
}

function applyContentBackground(color) {
    const body = document.body;
    // Remove existing content background classes
    body.className = body.className.replace(/content-bg-\w+/g, '');
    // Add new background class
    body.classList.add(`content-bg-${color}`);
}

function applyContentOption(option, enabled) {
    const body = document.body;
    const className = option.replace(/([A-Z])/g, '-$1').toLowerCase();

    if (enabled) {
        body.classList.add(className);
    } else {
        body.classList.remove(className);
    }
}

function applyFontFamily(font) {
    const body = document.body;
    // Remove existing font classes
    body.className = body.className.replace(/font-\w+/g, '');
    // Add new font class
    if (font !== 'default') {
        body.classList.add(`font-${font}`);
    }
}

function applyFontSize(size) {
    const body = document.body;
    // Remove existing font size classes
    body.className = body.className.replace(/font-size-\w+/g, '');
    // Add new font size class
    body.classList.add(`font-size-${size}`);
}

function applyTypographyOption(option, enabled) {
    const body = document.body;
    const className = option.replace(/([A-Z])/g, '-$1').toLowerCase();

    if (enabled) {
        body.classList.add(className);
    } else {
        body.classList.remove(className);
    }
}

function applyAnimationSpeed(speed) {
    const body = document.body;
    // Remove existing animation classes
    body.className = body.className.replace(/animation-\w+/g, '');
    // Add new animation class
    body.classList.add(`animation-${speed}`);
}

function applyEffectOption(option, enabled) {
    const body = document.body;
    const className = option.replace(/([A-Z])/g, '-$1').toLowerCase();

    if (enabled) {
        body.classList.add(className);
    } else {
        body.classList.remove(className);
    }
}

function applyAccessibilityOption(option, enabled) {
    const body = document.body;
    const className = option.replace(/([A-Z])/g, '-$1').toLowerCase();

    if (enabled) {
        body.classList.add(className);
    } else {
        body.classList.remove(className);
    }
}

/**
 * Initialize Layout Presets
 */
function initializeLayoutPresets() {
    // Layout presets are handled by onclick attributes in HTML
    console.log('✅ Layout presets initialized');
}

/**
 * Apply Layout Presets
 */
function applyPreset(presetName) {
    console.log(`🎨 Applying preset: ${presetName}`);

    const presets = {
        modern: {
            theme: 'light',
            navbarColor: 'primary',
            iconColor: 'white',
            sidebarColor: 'default',
            fontFamily: 'inter',
            fontSize: 'default',
            animationSpeed: 'normal',
            hoverEffects: true,
            fadeTransitions: true,
            slideAnimations: true,
            cardShadows: true,
            roundedCorners: true
        },
        classic: {
            theme: 'light',
            navbarColor: 'white',
            iconColor: 'dark',
            sidebarColor: 'default',
            fontFamily: 'default',
            fontSize: 'default',
            animationSpeed: 'slow',
            hoverEffects: false,
            fadeTransitions: false,
            slideAnimations: false,
            cardShadows: false,
            roundedCorners: false
        },
        minimal: {
            theme: 'light',
            navbarColor: 'white',
            iconColor: 'default',
            sidebarColor: 'default',
            fontFamily: 'lato',
            fontSize: 'default',
            animationSpeed: 'fast',
            hoverEffects: true,
            fadeTransitions: true,
            slideAnimations: false,
            cardShadows: false,
            roundedCorners: true
        },
        dark: {
            theme: 'dark',
            navbarColor: 'dark',
            iconColor: 'white',
            sidebarColor: 'dark',
            fontFamily: 'roboto',
            fontSize: 'default',
            animationSpeed: 'normal',
            hoverEffects: true,
            fadeTransitions: true,
            slideAnimations: true,
            cardShadows: true,
            roundedCorners: true
        },
        compact: {
            theme: 'light',
            navbarColor: 'secondary',
            iconColor: 'white',
            sidebarColor: 'default',
            fontFamily: 'default',
            fontSize: 'small',
            animationSpeed: 'fast',
            hoverEffects: true,
            fadeTransitions: false,
            slideAnimations: false,
            cardShadows: true,
            roundedCorners: true,
            sidebarMini: true,
            navCompact: true
        }
    };

    const preset = presets[presetName];
    if (!preset) {
        console.error(`❌ Preset '${presetName}' not found`);
        showToast(`Preset '${presetName}' not found`, 'danger');
        return;
    }

    // Apply theme
    if (preset.theme) {
        document.documentElement.setAttribute('data-bs-theme', preset.theme);
        localStorage.setItem('theme', preset.theme);

        const darkModeSwitch = document.getElementById('darkModeSwitch');
        if (darkModeSwitch) {
            darkModeSwitch.checked = preset.theme === 'dark';
        }

        updateThemeToggleButton(preset.theme);
    }

    // Apply navbar color
    if (preset.navbarColor) {
        applyColorVariant('navbar', preset.navbarColor);
        saveSettings('navbarColor', preset.navbarColor);
    }

    // Apply icon color
    if (preset.iconColor) {
        applyColorVariant('icon', preset.iconColor);
        saveSettings('iconColor', preset.iconColor);
    }

    // Apply sidebar color
    if (preset.sidebarColor) {
        applySidebarColor(preset.sidebarColor);
        saveSettings('sidebarColor', preset.sidebarColor);
    }

    // Apply typography
    if (preset.fontFamily) {
        applyFontFamily(preset.fontFamily);
        saveSettings('fontFamily', preset.fontFamily);

        const fontFamilySelect = document.getElementById('fontFamily');
        if (fontFamilySelect) {
            fontFamilySelect.value = preset.fontFamily;
        }
    }

    if (preset.fontSize) {
        applyFontSize(preset.fontSize);
        saveSettings('fontSize', preset.fontSize);

        const fontSizeSelect = document.getElementById('fontSize');
        if (fontSizeSelect) {
            fontSizeSelect.value = preset.fontSize;
        }
    }

    // Apply animation speed
    if (preset.animationSpeed) {
        applyAnimationSpeed(preset.animationSpeed);
        saveSettings('animationSpeed', preset.animationSpeed);

        const animationSpeedSelect = document.getElementById('animationSpeed');
        if (animationSpeedSelect) {
            animationSpeedSelect.value = preset.animationSpeed;
        }
    }

    // Apply boolean options
    const booleanOptions = [
        'hoverEffects', 'fadeTransitions', 'slideAnimations', 'cardShadows',
        'roundedCorners', 'sidebarMini', 'navCompact'
    ];

    booleanOptions.forEach(option => {
        if (preset.hasOwnProperty(option)) {
            const element = document.getElementById(option);
            if (element) {
                element.checked = preset[option];

                // Apply the setting
                if (option.startsWith('sidebar') || option.startsWith('nav')) {
                    applySidebarOption(option, preset[option]);
                } else {
                    applyEffectOption(option, preset[option]);
                }

                saveSettings(option, preset[option]);
            }
        }
    });

    // Show success message
    showToast(`${presetName.charAt(0).toUpperCase() + presetName.slice(1)} preset applied successfully!`, 'success');

    console.log(`✅ Preset '${presetName}' applied successfully`);
}

// Expose utility functions globally
window.resetAllSettings = resetAllSettings;
window.exportSettings = exportSettings;
window.importSettings = importSettings;
window.showToast = showToast;
window.saveSettingsWithToast = saveSettingsWithToast;
window.showLivePreview = showLivePreview;
window.initializeAllFeatures = initializeAllFeatures;
window.applyPreset = applyPreset;
