<?php
// Start session with secure settings
if (session_status() === PHP_SESSION_NONE) {
    session_set_cookie_params([
        'lifetime' => 86400,
        'path' => '/',
        'secure' => false, // Set to false for local development
        'httponly' => true,
        'samesite' => 'Lax' // Changed from Strict to Lax for better compatibility
    ]);
    session_start();
}

// Include required files
require_once 'config/database.php';
require_once 'includes/helpers/functions.php';
require_once 'includes/helpers/notifications.php';

// Check if user is logged in
if (!isLoggedIn()) {
    setFlashMessage('danger', 'Silakan login terlebih dahulu');
    redirect('login.php');
}

$currentUser = getCurrentUser();
if (!$currentUser) {
    session_destroy();
    setFlashMessage('danger', 'Sesi tidak valid. Silakan login kembali.');
    redirect('login.php');
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        try {
            switch ($_POST['action']) {
                case 'add':
                    // Validasi input
                    $errors = [];
                    
                    if (empty($_POST['nama_produk'])) {
                        $errors[] = 'Nama produk harus diisi';
                    } elseif (strlen($_POST['nama_produk']) < 3 || strlen($_POST['nama_produk']) > 100) {
                        $errors[] = 'Nama produk harus antara 3-100 karakter';
                    }
                    
                    if (empty($_POST['harga_beli'])) {
                        $errors[] = 'Harga beli harus diisi';
                    } elseif (!is_numeric($_POST['harga_beli']) || $_POST['harga_beli'] < 0) {
                        $errors[] = 'Harga beli tidak valid';
                    }
                    
                    if (empty($_POST['harga_jual'])) {
                        $errors[] = 'Harga jual harus diisi';
                    } elseif (!is_numeric($_POST['harga_jual']) || $_POST['harga_jual'] < 0) {
                        $errors[] = 'Harga jual tidak valid';
                    }
                    
                    if (empty($_POST['stok'])) {
                        $errors[] = 'Stok harus diisi';
                    } elseif (!is_numeric($_POST['stok']) || $_POST['stok'] < 0) {
                        $errors[] = 'Stok tidak valid';
                    }
                    
                    if (empty($errors)) {
                        // Insert produk
                        $stmt = executeQuery("
                            INSERT INTO produk (nama_produk, harga_beli, harga_jual, stok) 
                            VALUES (?, ?, ?, ?)
                        ", [
                            $_POST['nama_produk'],
                            $_POST['harga_beli'],
                            $_POST['harga_jual'],
                            $_POST['stok']
                        ]);
                        
                        if ($stmt->rowCount() > 0) {
                            // Log aktivitas
                            executeQuery("
                                INSERT INTO activity_logs (user_id, aktivitas) 
                                VALUES (?, ?)
                            ", [
                                $currentUser['id'],
                                sprintf('Menambahkan produk %s', $_POST['nama_produk'])
                            ]);
                            
                            setFlashMessage('success', 'Produk berhasil ditambahkan');
                            redirect('produk.php');
                        } else {
                            throw new Exception('Gagal menyimpan produk');
                        }
                    } else {
                        setFlashMessage('danger', implode('<br>', $errors));
                    }
                    break;

                case 'update':
                    if (empty($_POST['id'])) {
                        setFlashMessage('danger', 'ID produk tidak valid');
                        break;
                    }

                    // Validasi input
                    $errors = [];
                    
                    if (empty($_POST['nama_produk'])) {
                        $errors[] = 'Nama produk harus diisi';
                    } elseif (strlen($_POST['nama_produk']) < 3 || strlen($_POST['nama_produk']) > 100) {
                        $errors[] = 'Nama produk harus antara 3-100 karakter';
                    }
                    
                    if (empty($_POST['harga_beli'])) {
                        $errors[] = 'Harga beli harus diisi';
                    } elseif (!is_numeric($_POST['harga_beli']) || $_POST['harga_beli'] < 0) {
                        $errors[] = 'Harga beli tidak valid';
                    }
                    
                    if (empty($_POST['harga_jual'])) {
                        $errors[] = 'Harga jual harus diisi';
                    } elseif (!is_numeric($_POST['harga_jual']) || $_POST['harga_jual'] < 0) {
                        $errors[] = 'Harga jual tidak valid';
                    }
                    
                    if (empty($_POST['stok'])) {
                        $errors[] = 'Stok harus diisi';
                    } elseif (!is_numeric($_POST['stok']) || $_POST['stok'] < 0) {
                        $errors[] = 'Stok tidak valid';
                    }
                    
                    if (empty($errors)) {
                        $stmt = executeQuery("
                            UPDATE produk 
                            SET nama_produk = ?, harga_beli = ?, harga_jual = ?, stok = ?
                            WHERE id = ?
                        ", [
                            $_POST['nama_produk'],
                            $_POST['harga_beli'],
                            $_POST['harga_jual'],
                            $_POST['stok'],
                            $_POST['id']
                        ]);

                        if ($stmt->rowCount() > 0) {
                            // Log aktivitas
                            executeQuery("
                                INSERT INTO activity_logs (user_id, aktivitas) 
                                VALUES (?, ?)
                            ", [
                                $currentUser['id'],
                                sprintf('Memperbarui produk ID %s', $_POST['id'])
                            ]);
                            
                            setFlashMessage('success', 'Produk berhasil diperbarui');
                        } else {
                            setFlashMessage('danger', 'Gagal memperbarui produk');
                        }
                    } else {
                        setFlashMessage('danger', implode('<br>', $errors));
                    }
                    break;
            }
        } catch (Exception $e) {
            error_log("Product Error: " . $e->getMessage());
            setFlashMessage('danger', 'Terjadi kesalahan. Silakan coba lagi.');
        }
        redirect('produk.php');
    }
}

// Initialize variables
$whereClause = "1=1";
$params = [];

// Handle search
if (!empty($_GET['search'])) {
    $whereClause .= " AND nama_produk LIKE ?";
    $params[] = '%' . $_GET['search'] . '%';
}

// Pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$perPage = 10;
$offset = ($page - 1) * $perPage;

try {
    // Get total records
    $sql = "SELECT COUNT(*) as total FROM produk WHERE $whereClause";
    $stmt = executeQuery($sql, $params);
    $totalRecords = $stmt->fetch()['total'];
    $totalPages = ceil($totalRecords / $perPage);

    // Get products with proper error handling
    $sql = "SELECT * FROM produk WHERE $whereClause ORDER BY id ASC LIMIT ? OFFSET ?";
    $params[] = $perPage;
    $params[] = $offset;
    
    try {
        $stmt = executeQuery($sql, $params);
        $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if ($products === false) {
            throw new Exception("Gagal mengambil data produk");
        }
    } catch (PDOException $e) {
        error_log("Database Error in produk.php: " . $e->getMessage());
        throw new Exception("Gagal mengambil data produk: " . $e->getMessage());
    }
} catch (Exception $e) {
    error_log("Error in produk.php: " . $e->getMessage());
    setFlashMessage('danger', 'Terjadi kesalahan saat mengambil data produk: ' . $e->getMessage());
    $products = [];
    $totalPages = 0;
}

// Set page title
$page_title = 'Produk';

// Include header and sidebar
require_once 'includes/views/layouts/header.php';
require_once 'includes/views/layouts/sidebar.php';
?>

<div class="modern-layout">
    <div class="modern-container">
        <!-- Modern Page Header -->
        <div class="modern-page-header modern-flex modern-justify-between modern-items-center modern-mb-xl">
            <div>
                <h1 class="modern-page-title modern-mb-0">Produk</h1>
                <p class="modern-page-subtitle">Kelola data produk dan stok dengan mudah</p>
            </div>
            <div class="modern-page-actions">
                <button type="button" class="modern-btn modern-btn-primary" data-bs-toggle="modal" data-bs-target="#addProductModal">
                    <i class="fas fa-plus"></i>
                    Tambah Produk
                </button>
            </div>
        </div>

        <!-- Flash Messages -->
        <?php if ($flash = getFlashMessage()): ?>
        <div class="modern-alert modern-alert-<?= $flash['type'] ?> modern-mb-lg">
            <div class="modern-alert-content">
                <i class="fas fa-<?= $flash['type'] === 'success' ? 'check-circle' : 'exclamation-triangle' ?> modern-alert-icon"></i>
                <div class="modern-alert-message"><?= $flash['message'] ?></div>
            </div>
            <button type="button" class="modern-alert-close" onclick="this.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <?php endif; ?>

        <!-- Modern Filter Section -->
        <div class="modern-card modern-mb-lg">
            <div class="modern-card-header">
                <h5 class="modern-card-title">
                    <i class="fas fa-search modern-text-primary modern-mr-sm"></i>
                    Pencarian Produk
                </h5>
            </div>
            <div class="modern-card-body">
                <form action="" method="GET" class="modern-grid modern-grid-cols-2 modern-gap-md modern-items-end">
                    <div class="modern-form-group">
                        <label class="modern-form-label">
                            <i class="fas fa-box modern-text-primary"></i>
                            Cari Produk
                        </label>
                        <div class="modern-input-group">
                            <span class="modern-input-group-text">
                                <i class="fas fa-search"></i>
                            </span>
                            <input type="text" name="search" class="modern-form-control" value="<?= $_GET['search'] ?? '' ?>" placeholder="Masukkan nama produk...">
                        </div>
                    </div>

                    <div class="modern-form-group modern-flex modern-gap-sm">
                        <button type="submit" class="modern-btn modern-btn-primary modern-flex-1">
                            <i class="fas fa-search"></i>
                            Cari
                        </button>
                        <a href="produk.php" class="modern-btn modern-btn-light">
                            <i class="fas fa-sync"></i>
                            Reset
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- Modern Products Table -->
        <div class="modern-card">
            <div class="modern-card-header">
                <h5 class="modern-card-title">
                    <i class="fas fa-box modern-text-primary modern-mr-sm"></i>
                    Daftar Produk
                </h5>
                <div class="modern-card-actions">
                    <span class="modern-badge modern-badge-info">
                        Total: <?= count($products) ?> produk
                    </span>
                </div>
            </div>
            <div class="modern-card-body modern-p-0">
                <div class="modern-table-responsive">
                    <table class="modern-table">
                        <thead class="modern-table-header">
                            <tr>
                                <th class="modern-table-th">
                                    <i class="fas fa-box modern-mr-xs"></i>
                                    Nama Produk
                                </th>
                                <th class="modern-table-th">
                                    <i class="fas fa-money-bill modern-mr-xs"></i>
                                    Harga Beli
                                </th>
                                <th class="modern-table-th">
                                    <i class="fas fa-tag modern-mr-xs"></i>
                                    Harga Jual
                                </th>
                                <th class="modern-table-th modern-text-center">
                                    <i class="fas fa-warehouse modern-mr-xs"></i>
                                    Stok
                                </th>
                                <th class="modern-table-th">
                                    <i class="fas fa-calendar modern-mr-xs"></i>
                                    Tanggal Dibuat
                                </th>
                                <th class="modern-table-th modern-text-center">
                                    <i class="fas fa-cogs modern-mr-xs"></i>
                                    Aksi
                                </th>
                            </tr>
                        </thead>
                        <tbody class="modern-table-body">
                            <?php if (empty($products)): ?>
                            <tr>
                                <td colspan="6" class="modern-table-td">
                                    <div class="modern-empty-state">
                                        <div class="modern-empty-icon">
                                            <i class="fas fa-box"></i>
                                        </div>
                                        <div class="modern-empty-content">
                                            <h6 class="modern-empty-title">Belum Ada Produk</h6>
                                            <p class="modern-empty-text">Belum ada produk yang terdaftar dalam sistem</p>
                                            <button type="button" class="modern-btn modern-btn-primary modern-btn-sm" data-bs-toggle="modal" data-bs-target="#addProductModal">
                                                <i class="fas fa-plus"></i>
                                                Tambah Produk Pertama
                                            </button>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <?php else: ?>
                            <?php foreach ($products as $p): ?>
                            <tr class="modern-table-row">
                                <td class="modern-table-td">
                                    <div class="modern-table-content">
                                        <div class="modern-table-title"><?= htmlspecialchars($p['nama_produk']) ?></div>
                                    </div>
                                </td>
                                <td class="modern-table-td">
                                    <div class="modern-table-amount modern-text-muted"><?= formatRupiah($p['harga_beli']) ?></div>
                                </td>
                                <td class="modern-table-td">
                                    <div class="modern-table-amount modern-text-success"><?= formatRupiah($p['harga_jual']) ?></div>
                                </td>
                                <td class="modern-table-td modern-text-center">
                                    <div class="modern-badge modern-badge-<?= $p['stok'] > 0 ? 'success' : 'danger' ?>">
                                        <i class="fas fa-<?= $p['stok'] > 0 ? 'check' : 'exclamation-triangle' ?>"></i>
                                        <?= $p['stok'] ?>
                                    </div>
                                </td>
                                <td class="modern-table-td">
                                    <div class="modern-table-date">
                                        <i class="fas fa-calendar modern-text-muted modern-mr-xs"></i>
                                        <?= date('d/m/Y H:i', strtotime($p['created_at'])) ?>
                                    </div>
                                </td>
                                <td class="modern-table-td modern-text-center">
                                    <div class="modern-action-buttons">
                                        <button type="button" class="modern-btn modern-btn-primary modern-btn-sm" onclick="editProduct(
                                            '<?= $p['id'] ?>',
                                            '<?= htmlspecialchars($p['nama_produk']) ?>',
                                            '<?= $p['harga_beli'] ?>',
                                            '<?= $p['harga_jual'] ?>',
                                            '<?= $p['stok'] ?>'
                                        )" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button type="button" class="modern-btn modern-btn-danger modern-btn-sm" onclick="deleteProduct(<?= $p['id'] ?>)" title="Hapus">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Modern Pagination -->
                <?php if ($totalPages > 1): ?>
                <div class="modern-card-footer">
                    <div class="modern-pagination-wrapper">
                        <div class="modern-pagination-info">
                            <span class="modern-text-muted">
                                Halaman <?= $page ?> dari <?= $totalPages ?>
                                (<?= count($products) ?> produk ditampilkan)
                            </span>
                        </div>
                        <nav class="modern-pagination">
                            <?php if ($page > 1): ?>
                            <a class="modern-pagination-btn modern-pagination-prev"
                               href="?page=<?= $page - 1 ?><?= !empty($_GET['search']) ? '&search=' . urlencode($_GET['search']) : '' ?>">
                                <i class="fas fa-chevron-left"></i>
                                Sebelumnya
                            </a>
                            <?php endif; ?>

                            <div class="modern-pagination-numbers">
                                <?php
                                $start = max(1, $page - 2);
                                $end = min($totalPages, $page + 2);

                                if ($start > 1): ?>
                                    <a class="modern-pagination-number" href="?page=1<?= !empty($_GET['search']) ? '&search=' . urlencode($_GET['search']) : '' ?>">1</a>
                                    <?php if ($start > 2): ?>
                                        <span class="modern-pagination-dots">...</span>
                                    <?php endif; ?>
                                <?php endif; ?>

                                <?php for ($i = $start; $i <= $end; $i++): ?>
                                <a class="modern-pagination-number <?= $i === $page ? 'active' : '' ?>"
                                   href="?page=<?= $i ?><?= !empty($_GET['search']) ? '&search=' . urlencode($_GET['search']) : '' ?>"><?= $i ?></a>
                                <?php endfor; ?>

                                <?php if ($end < $totalPages): ?>
                                    <?php if ($end < $totalPages - 1): ?>
                                        <span class="modern-pagination-dots">...</span>
                                    <?php endif; ?>
                                    <a class="modern-pagination-number" href="?page=<?= $totalPages ?><?= !empty($_GET['search']) ? '&search=' . urlencode($_GET['search']) : '' ?>"><?= $totalPages ?></a>
                                <?php endif; ?>
                            </div>

                            <?php if ($page < $totalPages): ?>
                            <a class="modern-pagination-btn modern-pagination-next"
                               href="?page=<?= $page + 1 ?><?= !empty($_GET['search']) ? '&search=' . urlencode($_GET['search']) : '' ?>">
                                Selanjutnya
                                <i class="fas fa-chevron-right"></i>
                            </a>
                            <?php endif; ?>
                        </nav>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Add Product Modal -->
<div class="modal fade" id="addProductModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content border-0 shadow">
            <div class="modal-header border-0">
                <h5 class="modal-title">Tambah Produk</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="" method="POST" class="needs-validation" novalidate>
                <div class="modal-body">
                    <input type="hidden" name="action" value="add">
                    
                    <div class="mb-3">
                        <label class="form-label">Nama Produk</label>
                        <input type="text" name="nama_produk" class="form-control" required 
                               minlength="3" maxlength="100">
                        <div class="invalid-feedback">
                            Nama produk harus diisi (3-100 karakter)
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Harga Beli</label>
                        <div class="input-group">
                            <span class="input-group-text">Rp</span>
                            <input type="number" name="harga_beli" class="form-control" required 
                                   min="0" step="100">
                        </div>
                        <div class="invalid-feedback">
                            Harga beli harus diisi (minimal 0)
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Harga Jual</label>
                        <div class="input-group">
                            <span class="input-group-text">Rp</span>
                            <input type="number" name="harga_jual" class="form-control" required 
                                   min="0" step="100">
                        </div>
                        <div class="invalid-feedback">
                            Harga jual harus diisi (minimal 0)
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Stok</label>
                        <input type="number" name="stok" class="form-control" required 
                               min="0">
                        <div class="invalid-feedback">
                            Stok harus diisi (minimal 0)
                        </div>
                    </div>
                </div>
                <div class="modal-footer border-0">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">Simpan</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Product Modal -->
<div class="modal fade" id="editProductModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content border-0 shadow">
            <div class="modal-header border-0">
                <h5 class="modal-title">Edit Produk</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="" method="POST" class="needs-validation" novalidate>
                <div class="modal-body">
                    <input type="hidden" name="action" value="update">
                    <input type="hidden" name="id" id="edit_id">
                    
                    <div class="mb-3">
                        <label class="form-label">Nama Produk</label>
                        <input type="text" name="nama_produk" id="edit_nama_produk" class="form-control" required 
                               minlength="3" maxlength="100">
                        <div class="invalid-feedback">
                            Nama produk harus diisi (3-100 karakter)
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Harga Beli</label>
                        <div class="input-group">
                            <span class="input-group-text">Rp</span>
                            <input type="number" name="harga_beli" id="edit_harga_beli" class="form-control" required 
                                   min="0" step="100">
                        </div>
                        <div class="invalid-feedback">
                            Harga beli harus diisi (minimal 0)
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Harga Jual</label>
                        <div class="input-group">
                            <span class="input-group-text">Rp</span>
                            <input type="number" name="harga_jual" id="edit_harga_jual" class="form-control" required 
                                   min="0" step="100">
                        </div>
                        <div class="invalid-feedback">
                            Harga jual harus diisi (minimal 0)
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Stok</label>
                        <input type="number" name="stok" id="edit_stok" class="form-control" required 
                               min="0">
                        <div class="invalid-feedback">
                            Stok harus diisi (minimal 0)
                        </div>
                    </div>
                </div>
                <div class="modal-footer border-0">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">Simpan</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php include 'includes/views/layouts/footer.php'; ?>

<style>
.card {
    transition: transform 0.2s ease-in-out;
    background: #fff;
}

.card:hover {
    transform: translateY(-5px);
}

.btn {
    padding: 0.5rem 1rem;
    border-radius: 8px;
    transition: all 0.2s ease-in-out;
}

.btn:hover {
    transform: translateY(-2px);
}

.modal-content {
    border-radius: 15px;
}

.form-control {
    border-radius: 8px;
    padding: 0.6rem 1rem;
    border: 1px solid #e0e0e0;
}

.form-control:focus {
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.15);
    border-color: #86b7fe;
}

.form-select {
    border-radius: 8px;
    padding: 0.6rem 1rem;
    border: 1px solid #e0e0e0;
}

.input-group-text {
    border-radius: 8px 0 0 8px;
    background-color: #f8f9fa;
    border: 1px solid #e0e0e0;
}

.badge {
    font-weight: 500;
    padding: 0.5rem 1rem;
}

.bg-success {
    background-color: #198754 !important;
}

.bg-primary {
    background-color: #0d6efd !important;
}

.bg-danger {
    background-color: #dc3545 !important;
}

.btn-success {
    background-color: #198754;
    border-color: #198754;
}

.btn-primary {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.btn-danger {
    background-color: #dc3545;
    border-color: #dc3545;
}

.btn-light {
    background-color: #f8f9fa;
    border-color: #e0e0e0;
}

.btn-outline-danger {
    color: #dc3545;
    border-color: #dc3545;
}

.btn-outline-danger:hover {
    background-color: #dc3545;
    color: #fff;
}

.text-muted {
    color: #6c757d !important;
}

.text-gray-800 {
    color: #212529 !important;
}

.shadow-sm {
    box-shadow: 0 .125rem .25rem rgba(0,0,0,.075) !important;
}

.shadow {
    box-shadow: 0 .5rem 1rem rgba(0,0,0,.15) !important;
}

.table > :not(caption) > * > * {
    padding: 1rem;
}

.pagination .page-link {
    border-radius: 8px;
    margin: 0 2px;
    border: none;
    color: #6c757d;
}

.pagination .page-item.active .page-link {
    background-color: #0d6efd;
    color: #fff;
}

.pagination .page-link:hover {
    background-color: #f8f9fa;
    color: #0d6efd;
}
</style>

<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>
// Edit product
function editProduct(id, nama, hargaBeli, hargaJual, stok) {
    document.getElementById('edit_id').value = id;
    document.getElementById('edit_nama_produk').value = nama;
    document.getElementById('edit_harga_beli').value = hargaBeli;
    document.getElementById('edit_harga_jual').value = hargaJual;
    document.getElementById('edit_stok').value = stok;
    
    new bootstrap.Modal(document.getElementById('editProductModal')).show();
}

// Delete product
function deleteProduct(id) {
    Swal.fire({
        title: 'Apakah Anda yakin?',
        text: "Data produk yang dihapus tidak dapat dikembalikan!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'Ya, hapus!',
        cancelButtonText: 'Batal'
    }).then((result) => {
        if (result.isConfirmed) {
            window.location.href = `hapus-produk.php?id=${id}`;
        }
    });
}

// Form validation
const forms = document.querySelectorAll('.needs-validation');
Array.from(forms).forEach(form => {
    form.addEventListener('submit', event => {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        }
        form.classList.add('was-validated');
    }, false);
});
</script> 