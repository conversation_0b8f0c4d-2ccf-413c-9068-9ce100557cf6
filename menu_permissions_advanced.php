<?php
require_once 'includes/config/database.php';
require_once 'includes/helpers/functions.php';
require_once 'includes/helpers/notifications.php';
require_once 'includes/helpers/security.php';
require_once 'includes/helpers/validation.php';
require_once 'includes/helpers/system_monitor.php';

// Check admin access
$currentUser = getCurrentUser();
if (!$currentUser || $currentUser['role'] !== 'admin') {
    setFlashMessage('danger', 'Akses ditolak! Anda tidak memiliki izin untuk mengakses halaman ini.');
    redirect('dashboard.php');
}

$currentPage = 'permissions';
$pageTitle = 'Kelola Menu Permissions per User';

// Handle AJAX requests
if (isset($_GET['action']) && $_GET['action'] === 'get_users_by_role') {
    header('Content-Type: application/json');
    try {
        $roleId = $_GET['role_id'] ?? '';
        if (empty($roleId)) {
            throw new Exception('Role ID required');
        }

        $stmt = $pdo->prepare("SELECT id, username, email FROM users WHERE role = (SELECT name FROM roles WHERE id = ?) ORDER BY username");
        $stmt->execute([$roleId]);
        $users = $stmt->fetchAll();

        echo json_encode(['success' => true, 'users' => $users]);
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
    exit;
}

if (isset($_GET['action']) && $_GET['action'] === 'get_user_permissions') {
    header('Content-Type: application/json');
    try {
        $userId = $_GET['user_id'] ?? '';
        if (empty($userId)) {
            throw new Exception('User ID required');
        }

        $stmt = $pdo->prepare("SELECT menu_id FROM user_menu_permissions WHERE user_id = ?");
        $stmt->execute([$userId]);
        $permissions = $stmt->fetchAll(PDO::FETCH_COLUMN);

        echo json_encode(['success' => true, 'permissions' => $permissions]);
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
    exit;
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    try {
        if ($_POST['action'] === 'save_user_permissions') {
            $userId = $_POST['user_id'] ?? '';
            $allowedMenus = $_POST['allowed_menus'] ?? [];

            if (empty($userId)) {
                throw new Exception('Silakan pilih user terlebih dahulu');
            }

            // Create user_menu_permissions table if not exists
            $pdo->exec("CREATE TABLE IF NOT EXISTS user_menu_permissions (
                user_id INT,
                menu_id VARCHAR(50),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (user_id, menu_id),
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
            )");

            // Delete existing permissions for this user
            $stmt = $pdo->prepare("DELETE FROM user_menu_permissions WHERE user_id = ?");
            $stmt->execute([$userId]);

            // Insert new permissions
            if (!empty($allowedMenus)) {
                $stmt = $pdo->prepare("INSERT INTO user_menu_permissions (user_id, menu_id) VALUES (?, ?)");
                foreach ($allowedMenus as $menuId) {
                    $stmt->execute([$userId, $menuId]);
                }
            }

            // Get user info
            $stmt = $pdo->prepare("SELECT username FROM users WHERE id = ?");
            $stmt->execute([$userId]);
            $username = $stmt->fetchColumn();

            setFlashMessage('success', "Pengaturan menu berhasil disimpan untuk user: $username");
            redirect('menu_permissions_advanced.php');
        }
    } catch (Exception $e) {
        setFlashMessage('danger', $e->getMessage());
    }
}

// Get roles
$stmt = $pdo->query("SELECT * FROM roles ORDER BY name");
$roles = $stmt->fetchAll();

// Define all available menus and submenus
$availableMenus = [
    // Core Menus
    'dashboard' => 'Dashboard',
    'admin_dashboard' => 'Admin Dashboard',

    // Keuangan Menus
    'keuangan' => 'Keuangan (Parent Menu)',
    'transaksi' => '→ Transaksi',
    'kategori' => '→ Kategori',
    'target' => '→ Target Keuangan',
    'anggaran' => '→ Anggaran',
    'investasi' => '→ Investasi',
    'hutang' => '→ Hutang & Piutang',

    // Bisnis Menus
    'bisnis' => 'Bisnis (Parent Menu)',
    'produk' => '→ Produk',
    'penjualan' => '→ Penjualan',
    'pembelian' => '→ Pembelian',
    'supplier' => '→ Supplier',
    'inventory' => '→ Inventory',
    'retur' => '→ Retur',

    // Laporan Menus
    'laporan' => 'Laporan (Parent Menu)',
    'laporan_keuangan' => '→ Laporan Keuangan',
    'laporan_bisnis' => '→ Laporan Bisnis',
    'laporan_tax' => '→ Laporan Pajak',

    // Tools Menus
    'tools' => 'Tools (Parent Menu)',
    'kalkulator' => '→ Kalkulator',
    'konverter' => '→ Konverter Mata Uang',
    'kalender' => '→ Kalender',
    'pengingat' => '→ Pengingat',

    // Admin Panel Menus
    'admin_panel' => 'Admin Panel (Parent Menu)',
    'users' => '→ Kelola User',
    'permissions' => '→ Kelola Hak Akses',
    'notifications' => '→ Notifikasi Sistem',
    'system_customization' => '→ System Customization',
    'database_management' => '→ Database Management',
    'fix_errors' => '→ Fix System Errors',
    'update_database' => '→ Update Database',
    'system_check' => '→ System Health Check',
    'clear_cache' => '→ Clear Cache',
    'backup' => '→ Backup Data',
    'logs' => '→ Log Aktivitas',
    'settings' => '→ Pengaturan Sistem',

    // Security & Monitoring
    'security_monitor' => '→ Security Monitor',
    'system_monitor' => '→ System Monitor',
    'error_logs' => '→ Error Logs',
    'performance_monitor' => '→ Performance Monitor',
    'rate_limiting' => '→ Rate Limiting',
    'ip_management' => '→ IP Management',
    'security_logs' => '→ Security Logs',

    // Advanced Admin Tools
    'cache_management' => '→ Cache Management',
    'file_manager' => '→ File Manager',
    'database_optimizer' => '→ Database Optimizer',
    'system_diagnostics' => '→ System Diagnostics',
    'maintenance_mode' => '→ Maintenance Mode',
    'update_manager' => '→ Update Manager',
    'plugin_manager' => '→ Plugin Manager',
    'api_management' => '→ API Management',

    // Customization Menus
    'customization' => 'Customization (Parent Menu)',
    'customization_dashboard' => '→ Customization Dashboard',
    'theme_manager' => '→ Theme Manager',
    'menu_editor' => '→ Menu Editor',
    'layout_builder' => '→ Layout Builder',
    'widget_manager' => '→ Widget Manager',
    'template_manager' => '→ Template Manager',
    'preset_manager' => '→ Preset Manager',
    'css_editor' => '→ CSS Editor',
    'component_library' => '→ Component Library',
    'advanced_settings' => '→ Advanced Settings',
    'ensure_customization_functionality' => '→ Ensure Functionality',

    // Modern UI Features
    'modern_ui' => '→ Modern UI Components',
    'dashboard_customizer' => '→ Dashboard Customizer',
    'color_scheme_manager' => '→ Color Scheme Manager',
    'font_manager' => '→ Font Manager',
    'animation_settings' => '→ Animation Settings',
    'responsive_settings' => '→ Responsive Settings',
    'dark_mode_settings' => '→ Dark Mode Settings',
    'ui_presets' => '→ UI Presets',

    // Other Menus
    'profile' => 'Profil',

    // Bantuan Menus
    'bantuan' => 'Bantuan (Parent Menu)',
    'panduan' => '→ Panduan',
    'faq' => '→ FAQ',
    'tutorial' => '→ Tutorial',
    'support' => '→ Support',

    // Analytics & Reports
    'analytics' => 'Analytics (Parent Menu)',
    'user_analytics' => '→ User Analytics',
    'system_analytics' => '→ System Analytics',
    'performance_analytics' => '→ Performance Analytics',
    'security_analytics' => '→ Security Analytics',
    'business_analytics' => '→ Business Analytics',
    'financial_analytics' => '→ Financial Analytics',

    // Integration & API
    'integrations' => 'Integrations (Parent Menu)',
    'api_keys' => '→ API Keys',
    'webhooks' => '→ Webhooks',
    'third_party_services' => '→ Third Party Services',
    'payment_gateways' => '→ Payment Gateways',
    'email_services' => '→ Email Services',
    'sms_services' => '→ SMS Services',
    'cloud_storage' => '→ Cloud Storage',

    // Developer Tools
    'developer' => 'Developer Tools (Parent Menu)',
    'api_documentation' => '→ API Documentation',
    'code_editor' => '→ Code Editor',
    'database_console' => '→ Database Console',
    'log_viewer' => '→ Log Viewer',
    'debug_tools' => '→ Debug Tools',
    'testing_tools' => '→ Testing Tools',
    'migration_tools' => '→ Migration Tools',

    // System Menu
    'logout' => 'Keluar'
];

include 'includes/views/layouts/header.php';
?>

<div class="container-fluid px-4 py-3">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-gradient-primary text-white py-3">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-user-shield me-2"></i>
                        Kelola Menu Permissions per User
                    </h5>
                    <p class="mb-0 small opacity-75">Atur menu yang dapat diakses oleh user tertentu</p>
                </div>
                <div class="card-body p-4">

                    <!-- Step 1: Select Role -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card border-primary">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-user-tag me-2"></i>
                                        Step 1: Pilih Role
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <select class="form-select form-select-lg" id="roleSelect" onchange="loadUsersByRole()">
                                        <option value="">-- Pilih Role --</option>
                                        <?php foreach ($roles as $role): ?>
                                            <option value="<?= $role['id'] ?>"><?= ucfirst($role['name']) ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Step 2: Select User -->
                        <div class="col-md-6">
                            <div class="card border-info">
                                <div class="card-header bg-info text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-user me-2"></i>
                                        Step 2: Pilih User
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <select class="form-select form-select-lg" id="userSelect" onchange="loadUserPermissions()" disabled>
                                        <option value="">-- Pilih User --</option>
                                    </select>
                                    <div id="userInfo" class="mt-2 small text-muted" style="display: none;"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Step 3: Configure Permissions -->
                    <div id="permissionsSection" style="display: none;">
                        <div class="card border-success">
                            <div class="card-header bg-success text-white">
                                <h6 class="mb-0">
                                    <i class="fas fa-cogs me-2"></i>
                                    Step 3: Atur Menu Permissions
                                </h6>
                            </div>
                            <div class="card-body">
                                <form method="POST" id="permissionsForm">
                                    <input type="hidden" name="action" value="save_user_permissions">
                                    <input type="hidden" name="user_id" id="selectedUserId">

                                    <!-- Current Status -->
                                    <div class="mb-4">
                                        <h6 class="text-success">Status Menu Saat Ini:</h6>
                                        <div id="currentPermissions" class="p-3 bg-light rounded">
                                            <span class="text-muted">Pilih user untuk melihat status menu</span>
                                        </div>
                                    </div>

                                    <!-- Menu Selection -->
                                    <div class="mb-4">
                                        <div class="d-flex justify-content-between align-items-center mb-3">
                                            <h6 class="text-success mb-0">Pilih Menu yang Ditampilkan:</h6>
                                            <div>
                                                <button type="button" class="btn btn-sm btn-outline-success" onclick="selectAllMenus()">
                                                    <i class="fas fa-check-square me-1"></i>Pilih Semua
                                                </button>
                                                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="clearAllMenus()">
                                                    <i class="fas fa-square me-1"></i>Kosongkan
                                                </button>
                                            </div>
                                        </div>

                                        <div class="border rounded p-3 bg-light">
                                            <div class="row">
                                                <?php foreach ($availableMenus as $menuId => $menuLabel): ?>
                                                    <div class="col-md-6 mb-3">
                                                        <div class="form-check form-switch">
                                                            <input class="form-check-input menu-checkbox" type="checkbox"
                                                                   name="allowed_menus[]" value="<?= $menuId ?>"
                                                                   id="menu_<?= $menuId ?>" onchange="updateMenuStatus()">
                                                            <label class="form-check-label fw-medium" for="menu_<?= $menuId ?>">
                                                                <span class="menu-status" id="status_<?= $menuId ?>">
                                                                    <i class="fas fa-eye-slash text-danger me-2"></i>
                                                                </span>
                                                                <?= $menuLabel ?>
                                                            </label>
                                                        </div>
                                                    </div>
                                                <?php endforeach; ?>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Action Buttons -->
                                    <div class="d-flex gap-2 justify-content-between">
                                        <div>
                                            <button type="submit" class="btn btn-success btn-lg">
                                                <i class="fas fa-save me-2"></i>Simpan Pengaturan Menu
                                            </button>
                                            <button type="button" class="btn btn-outline-info" onclick="resetToRoleDefaults()">
                                                <i class="fas fa-undo me-2"></i>Reset ke Default Role
                                            </button>
                                        </div>
                                        <div>
                                            <button type="button" class="btn btn-warning" onclick="previewChanges()">
                                                <i class="fas fa-eye me-2"></i>Preview Perubahan
                                            </button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- Preview Modal -->
                    <div class="modal fade" id="previewModal" tabindex="-1">
                        <div class="modal-dialog modal-lg">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title">
                                        <i class="fas fa-eye me-2"></i>Preview Menu Changes
                                    </h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                </div>
                                <div class="modal-body">
                                    <div id="previewContent"></div>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
                                    <button type="button" class="btn btn-success" onclick="confirmSave()">
                                        <i class="fas fa-save me-2"></i>Konfirmasi & Simpan
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- System Statistics -->
                    <div class="mt-4">
                        <div class="card border-warning">
                            <div class="card-header bg-warning text-dark">
                                <h6 class="mb-0">
                                    <i class="fas fa-chart-bar me-2"></i>System Statistics & Information
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="text-center p-3 bg-primary bg-opacity-10 rounded">
                                            <h4 class="text-primary mb-1"><?= count($roles) ?></h4>
                                            <small class="text-muted">Total Roles</small>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="text-center p-3 bg-success bg-opacity-10 rounded">
                                            <?php
                                            $stmt = $pdo->query("SELECT COUNT(*) FROM users");
                                            $totalUsers = $stmt->fetchColumn();
                                            ?>
                                            <h4 class="text-success mb-1"><?= $totalUsers ?></h4>
                                            <small class="text-muted">Total Users</small>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="text-center p-3 bg-info bg-opacity-10 rounded">
                                            <h4 class="text-info mb-1"><?= count($availableMenus) ?></h4>
                                            <small class="text-muted">Available Menus</small>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="text-center p-3 bg-warning bg-opacity-10 rounded">
                                            <?php
                                            try {
                                                $stmt = $pdo->query("SELECT COUNT(DISTINCT user_id) FROM user_menu_permissions");
                                                $customizedUsers = $stmt->fetchColumn();
                                            } catch (Exception $e) {
                                                $customizedUsers = 0;
                                            }
                                            ?>
                                            <h4 class="text-warning mb-1"><?= $customizedUsers ?></h4>
                                            <small class="text-muted">Customized Users</small>
                                        </div>
                                    </div>
                                </div>

                                <!-- System Health -->
                                <div class="mt-3">
                                    <h6 class="text-warning mb-2">System Health:</h6>
                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-database text-primary me-2"></i>
                                                <span class="small">Database: </span>
                                                <span class="badge bg-success ms-auto">Connected</span>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-memory text-info me-2"></i>
                                                <span class="small">Memory: </span>
                                                <span class="badge bg-info ms-auto"><?= round(memory_get_usage(true) / 1024 / 1024, 1) ?>MB</span>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-server text-success me-2"></i>
                                                <span class="small">PHP: </span>
                                                <span class="badge bg-success ms-auto"><?= PHP_VERSION ?></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Help Section -->
                    <div class="mt-4 p-3 bg-info bg-opacity-10 border border-info rounded">
                        <h6 class="text-info mb-2">
                            <i class="fas fa-info-circle me-2"></i>Cara Penggunaan:
                        </h6>
                        <ol class="small mb-0">
                            <li><strong>Pilih Role:</strong> Admin atau User</li>
                            <li><strong>Pilih User:</strong> User yang terdaftar dengan role tersebut</li>
                            <li><strong>Atur Menu:</strong> Centang untuk menampilkan, uncheck untuk menyembunyikan</li>
                            <li><strong>Preview:</strong> Lihat perubahan sebelum menyimpan</li>
                            <li><strong>Simpan:</strong> Menu akan langsung berubah untuk user tersebut</li>
                        </ol>

                        <div class="mt-3 p-2 bg-warning bg-opacity-25 rounded">
                            <h6 class="text-warning mb-1">
                                <i class="fas fa-exclamation-triangle me-2"></i>New Features Available:
                            </h6>
                            <ul class="small mb-0">
                                <li><strong>Security Monitor:</strong> Monitor keamanan sistem real-time</li>
                                <li><strong>System Monitor:</strong> Pantau performa dan kesehatan sistem</li>
                                <li><strong>Cache Management:</strong> Kelola cache untuk performa optimal</li>
                                <li><strong>Modern UI Components:</strong> Komponen UI modern yang telah diupdate</li>
                                <li><strong>Analytics Tools:</strong> Tools analisis untuk business intelligence</li>
                                <li><strong>Developer Tools:</strong> Tools untuk developer dan debugging</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Load users by selected role
function loadUsersByRole() {
    const roleSelect = document.getElementById('roleSelect');
    const userSelect = document.getElementById('userSelect');
    const permissionsSection = document.getElementById('permissionsSection');

    const roleId = roleSelect.value;

    // Reset user select
    userSelect.innerHTML = '<option value="">-- Pilih User --</option>';
    userSelect.disabled = true;
    permissionsSection.style.display = 'none';

    if (!roleId) return;

    // Fetch users for this role
    fetch(`?action=get_users_by_role&role_id=${roleId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                userSelect.disabled = false;
                data.users.forEach(user => {
                    const option = document.createElement('option');
                    option.value = user.id;
                    option.textContent = `${user.username} (${user.email})`;
                    userSelect.appendChild(option);
                });

                if (data.users.length === 0) {
                    userSelect.innerHTML = '<option value="">-- Tidak ada user dengan role ini --</option>';
                    userSelect.disabled = true;
                }
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Terjadi kesalahan saat memuat data user.');
        });
}

// Load user permissions
function loadUserPermissions() {
    const userSelect = document.getElementById('userSelect');
    const permissionsSection = document.getElementById('permissionsSection');
    const userInfo = document.getElementById('userInfo');
    const selectedUserId = document.getElementById('selectedUserId');

    const userId = userSelect.value;

    if (!userId) {
        permissionsSection.style.display = 'none';
        userInfo.style.display = 'none';
        return;
    }

    // Set selected user ID
    selectedUserId.value = userId;

    // Show user info
    const selectedText = userSelect.options[userSelect.selectedIndex].text;
    userInfo.innerHTML = `<i class="fas fa-user me-1"></i>Selected: ${selectedText}`;
    userInfo.style.display = 'block';

    // Show permissions section
    permissionsSection.style.display = 'block';

    // Fetch user permissions
    fetch(`?action=get_user_permissions&user_id=${userId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Clear all checkboxes first
                document.querySelectorAll('.menu-checkbox').forEach(cb => cb.checked = false);

                // Check allowed menus
                data.permissions.forEach(menuId => {
                    const checkbox = document.getElementById(`menu_${menuId}`);
                    if (checkbox) checkbox.checked = true;
                });

                // Update status display
                updateMenuStatus();
                updateCurrentPermissions(data.permissions);
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Terjadi kesalahan saat memuat permissions user.');
        });
}

// Update menu status icons
function updateMenuStatus() {
    document.querySelectorAll('.menu-checkbox').forEach(checkbox => {
        const menuId = checkbox.value;
        const statusElement = document.getElementById(`status_${menuId}`);

        if (checkbox.checked) {
            statusElement.innerHTML = '<i class="fas fa-eye text-success me-2"></i>';
        } else {
            statusElement.innerHTML = '<i class="fas fa-eye-slash text-danger me-2"></i>';
        }
    });
}

// Update current permissions display
function updateCurrentPermissions(permissions) {
    const currentPermissions = document.getElementById('currentPermissions');

    if (permissions.length === 0) {
        currentPermissions.innerHTML = '<span class="text-danger"><i class="fas fa-ban me-2"></i>Tidak ada menu yang ditampilkan</span>';
    } else {
        const menuLabels = {
            // Core Menus
            'dashboard': 'Dashboard',
            'admin_dashboard': 'Admin Dashboard',

            // Keuangan Menus
            'keuangan': 'Keuangan',
            'transaksi': 'Transaksi',
            'kategori': 'Kategori',
            'target': 'Target Keuangan',
            'anggaran': 'Anggaran',
            'investasi': 'Investasi',
            'hutang': 'Hutang & Piutang',

            // Bisnis Menus
            'bisnis': 'Bisnis',
            'produk': 'Produk',
            'penjualan': 'Penjualan',
            'pembelian': 'Pembelian',
            'supplier': 'Supplier',
            'inventory': 'Inventory',
            'retur': 'Retur',

            // Laporan Menus
            'laporan': 'Laporan',
            'laporan_keuangan': 'Laporan Keuangan',
            'laporan_bisnis': 'Laporan Bisnis',
            'laporan_tax': 'Laporan Pajak',

            // Tools Menus
            'tools': 'Tools',
            'kalkulator': 'Kalkulator',
            'konverter': 'Konverter Mata Uang',
            'kalender': 'Kalender',
            'pengingat': 'Pengingat',

            // Admin Panel Menus
            'admin_panel': 'Admin Panel',
            'users': 'Kelola User',
            'permissions': 'Kelola Hak Akses',
            'notifications': 'Notifikasi Sistem',
            'system_customization': 'System Customization',
            'database_management': 'Database Management',
            'fix_errors': 'Fix System Errors',
            'update_database': 'Update Database',
            'system_check': 'System Health Check',
            'clear_cache': 'Clear Cache',
            'backup': 'Backup Data',
            'logs': 'Log Aktivitas',
            'settings': 'Pengaturan Sistem',

            // Security & Monitoring
            'security_monitor': 'Security Monitor',
            'system_monitor': 'System Monitor',
            'error_logs': 'Error Logs',
            'performance_monitor': 'Performance Monitor',
            'rate_limiting': 'Rate Limiting',
            'ip_management': 'IP Management',
            'security_logs': 'Security Logs',

            // Advanced Admin Tools
            'cache_management': 'Cache Management',
            'file_manager': 'File Manager',
            'database_optimizer': 'Database Optimizer',
            'system_diagnostics': 'System Diagnostics',
            'maintenance_mode': 'Maintenance Mode',
            'update_manager': 'Update Manager',
            'plugin_manager': 'Plugin Manager',
            'api_management': 'API Management',

            // Customization Menus
            'customization': 'Customization',
            'customization_dashboard': 'Customization Dashboard',
            'theme_manager': 'Theme Manager',
            'menu_editor': 'Menu Editor',
            'layout_builder': 'Layout Builder',
            'widget_manager': 'Widget Manager',
            'template_manager': 'Template Manager',
            'preset_manager': 'Preset Manager',
            'css_editor': 'CSS Editor',
            'component_library': 'Component Library',
            'advanced_settings': 'Advanced Settings',
            'ensure_customization_functionality': 'Ensure Functionality',

            // Modern UI Features
            'modern_ui': 'Modern UI Components',
            'dashboard_customizer': 'Dashboard Customizer',
            'color_scheme_manager': 'Color Scheme Manager',
            'font_manager': 'Font Manager',
            'animation_settings': 'Animation Settings',
            'responsive_settings': 'Responsive Settings',
            'dark_mode_settings': 'Dark Mode Settings',
            'ui_presets': 'UI Presets',

            // Other Menus
            'profile': 'Profil',

            // Bantuan Menus
            'bantuan': 'Bantuan',
            'panduan': 'Panduan',
            'faq': 'FAQ',
            'tutorial': 'Tutorial',
            'support': 'Support',

            // Analytics & Reports
            'analytics': 'Analytics',
            'user_analytics': 'User Analytics',
            'system_analytics': 'System Analytics',
            'performance_analytics': 'Performance Analytics',
            'security_analytics': 'Security Analytics',
            'business_analytics': 'Business Analytics',
            'financial_analytics': 'Financial Analytics',

            // Integration & API
            'integrations': 'Integrations',
            'api_keys': 'API Keys',
            'webhooks': 'Webhooks',
            'third_party_services': 'Third Party Services',
            'payment_gateways': 'Payment Gateways',
            'email_services': 'Email Services',
            'sms_services': 'SMS Services',
            'cloud_storage': 'Cloud Storage',

            // Developer Tools
            'developer': 'Developer Tools',
            'api_documentation': 'API Documentation',
            'code_editor': 'Code Editor',
            'database_console': 'Database Console',
            'log_viewer': 'Log Viewer',
            'debug_tools': 'Debug Tools',
            'testing_tools': 'Testing Tools',
            'migration_tools': 'Migration Tools',

            // System Menu
            'logout': 'Keluar'
        };

        let html = '<div class="d-flex flex-wrap gap-1">';
        permissions.forEach(menuId => {
            const label = menuLabels[menuId] || menuId;
            html += `<span class="badge bg-success">${label}</span>`;
        });
        html += '</div>';

        currentPermissions.innerHTML = html;
    }
}

// Select all menus
function selectAllMenus() {
    document.querySelectorAll('.menu-checkbox').forEach(cb => cb.checked = true);
    updateMenuStatus();
}

// Clear all menus
function clearAllMenus() {
    document.querySelectorAll('.menu-checkbox').forEach(cb => cb.checked = false);
    updateMenuStatus();
}

// Reset to role defaults
function resetToRoleDefaults() {
    const roleSelect = document.getElementById('roleSelect');
    const roleName = roleSelect.options[roleSelect.selectedIndex].text.toLowerCase();

    clearAllMenus();

    if (roleName.includes('admin')) {
        // Admin gets all menus
        selectAllMenus();
    } else {
        // User gets basic menus (no admin_dashboard for regular users)
        const userMenus = ['dashboard', 'transaksi', 'kategori', 'target', 'profile', 'logout'];
        userMenus.forEach(menuId => {
            const checkbox = document.getElementById(`menu_${menuId}`);
            if (checkbox) checkbox.checked = true;
        });
    }

    updateMenuStatus();
    alert(`Permissions direset ke default ${roleName}!`);
}

// Preview changes
function previewChanges() {
    const userSelect = document.getElementById('userSelect');
    const selectedUser = userSelect.options[userSelect.selectedIndex].text;

    const checkedMenus = [];
    const uncheckedMenus = [];

    document.querySelectorAll('.menu-checkbox').forEach(checkbox => {
        const menuLabel = checkbox.nextElementSibling.textContent.trim();
        if (checkbox.checked) {
            checkedMenus.push(menuLabel);
        } else {
            uncheckedMenus.push(menuLabel);
        }
    });

    let previewHtml = `<h6>User: ${selectedUser}</h6>`;

    if (checkedMenus.length > 0) {
        previewHtml += '<h6 class="text-success mt-3">Menu yang DITAMPILKAN:</h6><ul>';
        checkedMenus.forEach(menu => {
            previewHtml += `<li><i class="fas fa-eye text-success me-2"></i>${menu}</li>`;
        });
        previewHtml += '</ul>';
    }

    if (uncheckedMenus.length > 0) {
        previewHtml += '<h6 class="text-danger mt-3">Menu yang DISEMBUNYIKAN:</h6><ul>';
        uncheckedMenus.forEach(menu => {
            previewHtml += `<li><i class="fas fa-eye-slash text-danger me-2"></i>${menu}</li>`;
        });
        previewHtml += '</ul>';
    }

    document.getElementById('previewContent').innerHTML = previewHtml;

    const modal = new bootstrap.Modal(document.getElementById('previewModal'));
    modal.show();
}

// Confirm and save
function confirmSave() {
    document.getElementById('permissionsForm').submit();
}

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    updateMenuStatus();
});
</script>

<?php include 'includes/views/layouts/footer.php'; ?>
