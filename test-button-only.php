<!DOCTYPE html>
<html lang="id" data-bs-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Button Only - KeuanganKu</title>

    <!-- CSS Libraries -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="assets/css/layout-manager.css?v=<?= time() ?>" rel="stylesheet">
    <link href="assets/css/control-sidebar.css?v=<?= time() ?>" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-bug me-2"></i>Button Test Only
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>Simple Button Test:</h6>
                            <p>This page tests ONLY the button functionality without any complex layout.</p>
                        </div>

                        <div class="text-center mb-4">
                            <!-- Test Button - Exact same as navbar -->
                            <button class="btn btn-primary btn-lg me-3" type="button" data-widget="control-sidebar"
                                    id="controlSidebarToggleBtn">
                                <i class="fas fa-cogs me-2"></i>Customize Layout (data-widget)
                            </button>

                            <!-- Manual Test Button -->
                            <button class="btn btn-success btn-lg me-3" onclick="manualOpen()">
                                <i class="fas fa-play me-2"></i>Manual Open
                            </button>

                            <!-- Global Function Test -->
                            <button class="btn btn-warning btn-lg" onclick="window.openControlSidebar()">
                                <i class="fas fa-globe me-2"></i>Global Function
                            </button>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <h6>Button Status</h6>
                                <div id="buttonStatus" class="bg-light p-3 rounded">
                                    <div>Checking...</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6>Element Status</h6>
                                <div id="elementStatus" class="bg-light p-3 rounded">
                                    <div>Checking...</div>
                                </div>
                            </div>
                        </div>

                        <div class="mt-4">
                            <h6>Live Console</h6>
                            <div id="liveConsole" class="bg-dark text-light p-3 rounded" style="height: 200px; overflow-y: auto; font-family: monospace; font-size: 0.875rem;">
                                <div class="text-success">[INIT] Console ready...</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Control Sidebar - Minimal -->
    <div class="control-sidebar" id="controlSidebar">
        <div class="control-sidebar-content">
            <div class="control-sidebar-header">
                <h5><i class="fas fa-cogs me-2"></i>IT WORKS!</h5>
                <button class="control-sidebar-close" type="button" onclick="closeControlSidebar()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="control-sidebar-body">
                <div class="alert alert-success">
                    <h6><i class="fas fa-check-circle me-2"></i>Success!</h6>
                    <p class="mb-0">The Control Sidebar is working correctly!</p>
                </div>
                <div class="text-center">
                    <button class="btn btn-primary" onclick="closeControlSidebar()">
                        <i class="fas fa-times me-1"></i>Close
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Control Sidebar Overlay -->
    <div class="control-sidebar-overlay" id="controlSidebarOverlay" onclick="closeControlSidebar()"></div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/control-sidebar.js?v=<?= time() ?>"></script>

    <script>
        // Live console
        function logToConsole(message, type = 'info') {
            const console = document.getElementById('liveConsole');
            const timestamp = new Date().toLocaleTimeString();
            const colorClass = type === 'error' ? 'text-danger' : type === 'warn' ? 'text-warning' : type === 'success' ? 'text-success' : 'text-light';
            
            const div = document.createElement('div');
            div.className = colorClass;
            div.innerHTML = `[${timestamp}] ${message}`;
            console.appendChild(div);
            console.scrollTop = console.scrollHeight;
        }

        // Override console methods
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;

        console.log = function(...args) {
            originalLog.apply(console, args);
            logToConsole(args.join(' '), 'info');
        };

        console.error = function(...args) {
            originalError.apply(console, args);
            logToConsole(args.join(' '), 'error');
        };

        console.warn = function(...args) {
            originalWarn.apply(console, args);
            logToConsole(args.join(' '), 'warn');
        };

        // Manual functions
        function manualOpen() {
            logToConsole('Manual open button clicked', 'info');
            
            const controlSidebar = document.getElementById('controlSidebar');
            const controlSidebarOverlay = document.getElementById('controlSidebarOverlay');
            
            if (controlSidebar && controlSidebarOverlay) {
                logToConsole('Elements found, opening sidebar', 'success');
                controlSidebar.classList.add('open');
                controlSidebarOverlay.classList.add('show');
                document.body.style.overflow = 'hidden';
                logToConsole('Sidebar opened successfully!', 'success');
            } else {
                logToConsole('Elements not found!', 'error');
            }
        }

        function closeControlSidebar() {
            logToConsole('Closing control sidebar', 'info');
            
            const controlSidebar = document.getElementById('controlSidebar');
            const controlSidebarOverlay = document.getElementById('controlSidebarOverlay');
            
            if (controlSidebar && controlSidebarOverlay) {
                controlSidebar.classList.remove('open');
                controlSidebarOverlay.classList.remove('show');
                document.body.style.overflow = '';
                logToConsole('Sidebar closed successfully!', 'success');
            }
        }

        function updateStatus() {
            // Button status
            const button = document.querySelector('[data-widget="control-sidebar"]');
            const buttonById = document.getElementById('controlSidebarToggleBtn');
            
            document.getElementById('buttonStatus').innerHTML = `
                <div><strong>Button Status:</strong></div>
                <div>✅ data-widget selector: ${button ? 'Found' : 'Not Found'}</div>
                <div>✅ ID selector: ${buttonById ? 'Found' : 'Not Found'}</div>
                <div>✅ Button same element: ${button === buttonById ? 'Yes' : 'No'}</div>
            `;
            
            // Element status
            const controlSidebar = document.getElementById('controlSidebar');
            const controlSidebarOverlay = document.getElementById('controlSidebarOverlay');
            
            document.getElementById('elementStatus').innerHTML = `
                <div><strong>Element Status:</strong></div>
                <div>✅ Control Sidebar: ${controlSidebar ? 'Found' : 'Not Found'}</div>
                <div>✅ Overlay: ${controlSidebarOverlay ? 'Found' : 'Not Found'}</div>
                <div>✅ Global Function: ${typeof window.openControlSidebar === 'function' ? 'Available' : 'Not Available'}</div>
            `;
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            logToConsole('DOM Content Loaded', 'success');
            updateStatus();
            
            // Test button click detection
            const button = document.querySelector('[data-widget="control-sidebar"]');
            if (button) {
                logToConsole('Button found, testing click detection...', 'info');
                
                // Add manual event listener for testing
                button.addEventListener('click', function(e) {
                    logToConsole('MANUAL EVENT LISTENER: Button clicked!', 'success');
                });
                
                // Test if button is clickable
                setTimeout(function() {
                    logToConsole('Button setup complete', 'success');
                }, 1000);
            } else {
                logToConsole('Button not found!', 'error');
            }
        });
    </script>
</body>
</html>
