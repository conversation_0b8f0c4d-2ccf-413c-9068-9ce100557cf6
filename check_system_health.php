<?php
require_once 'includes/config/database.php';
require_once 'includes/helpers/functions.php';

// Check admin access
$currentUser = getCurrentUser();
if (!$currentUser || $currentUser['role'] !== 'admin') {
    setFlashMessage('danger', 'Aks<PERSON> ditolak! Anda tidak memiliki izin untuk mengakses halaman ini.');
    redirect('dashboard.php');
}

$pageTitle = 'System Health Check';

// Define all required tables with their creation SQL
$requiredTables = [
    'users' => "CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) UNIQUE NOT NULL,
        email VARCHAR(100) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        full_name VARCHAR(100),
        phone VARCHAR(20),
        address TEXT,
        role ENUM('admin', 'user') DEFAULT 'user',
        status ENUM('active', 'inactive') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )",
    
    'system_notifications' => "CREATE TABLE IF NOT EXISTS system_notifications (
        id INT AUTO_INCREMENT PRIMARY KEY,
        type ENUM('error', 'warning', 'info', 'success') DEFAULT 'info',
        title VARCHAR(255) NOT NULL,
        message TEXT NOT NULL,
        source VARCHAR(100),
        user_id INT,
        is_read BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_type (type),
        INDEX idx_created_at (created_at),
        INDEX idx_is_read (is_read)
    )",
    
    'database_versions' => "CREATE TABLE IF NOT EXISTS database_versions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        version VARCHAR(20) NOT NULL UNIQUE,
        description TEXT,
        applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        applied_by INT,
        INDEX idx_version (version),
        FOREIGN KEY (applied_by) REFERENCES users(id) ON DELETE SET NULL
    )",
    
    'system_logs' => "CREATE TABLE IF NOT EXISTS system_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        level ENUM('debug', 'info', 'warning', 'error', 'critical') DEFAULT 'info',
        message TEXT NOT NULL,
        context JSON,
        user_id INT,
        ip_address VARCHAR(45),
        user_agent TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_level (level),
        INDEX idx_created_at (created_at),
        INDEX idx_user_id (user_id),
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
    )",
    
    'user_menu_permissions' => "CREATE TABLE IF NOT EXISTS user_menu_permissions (
        user_id INT,
        menu_id VARCHAR(50),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (user_id, menu_id),
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    )",
    
    'categories' => "CREATE TABLE IF NOT EXISTS categories (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        name VARCHAR(100) NOT NULL,
        type ENUM('income', 'expense') NOT NULL,
        color VARCHAR(7) DEFAULT '#007bff',
        icon VARCHAR(50) DEFAULT 'fas fa-circle',
        description TEXT,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        UNIQUE KEY unique_user_category (user_id, name, type)
    )",
    
    'transactions' => "CREATE TABLE IF NOT EXISTS transactions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        type ENUM('income', 'expense') NOT NULL,
        category_id INT,
        amount DECIMAL(15,2) NOT NULL,
        description TEXT,
        transaction_date DATE NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL,
        INDEX idx_user_date (user_id, transaction_date),
        INDEX idx_type (type),
        INDEX idx_category (category_id)
    )",
    
    'financial_targets' => "CREATE TABLE IF NOT EXISTS financial_targets (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        name VARCHAR(255) NOT NULL,
        description TEXT,
        target_amount DECIMAL(15,2) NOT NULL,
        current_amount DECIMAL(15,2) DEFAULT 0,
        target_date DATE,
        category ENUM('saving', 'investment', 'debt_payment', 'purchase', 'emergency_fund', 'other') DEFAULT 'saving',
        status ENUM('active', 'completed', 'paused', 'cancelled') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        INDEX idx_user_status (user_id, status),
        INDEX idx_target_date (target_date)
    )",
    
    'target_transactions' => "CREATE TABLE IF NOT EXISTS target_transactions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        target_id INT NOT NULL,
        amount DECIMAL(15,2) NOT NULL,
        description TEXT,
        transaction_date DATE NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (target_id) REFERENCES financial_targets(id) ON DELETE CASCADE
    )",
    
    'budgets' => "CREATE TABLE IF NOT EXISTS budgets (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        name VARCHAR(255) NOT NULL,
        category_id INT,
        budget_amount DECIMAL(15,2) NOT NULL,
        spent_amount DECIMAL(15,2) DEFAULT 0,
        period_type ENUM('monthly', 'weekly', 'yearly') DEFAULT 'monthly',
        start_date DATE NOT NULL,
        end_date DATE NOT NULL,
        status ENUM('active', 'completed', 'exceeded', 'paused') DEFAULT 'active',
        alert_percentage INT DEFAULT 80,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL,
        INDEX idx_user_period (user_id, start_date, end_date),
        INDEX idx_status (status)
    )",
    
    'user_preferences' => "CREATE TABLE IF NOT EXISTS user_preferences (
        user_id INT PRIMARY KEY,
        currency VARCHAR(3) DEFAULT 'IDR',
        date_format VARCHAR(20) DEFAULT 'd/m/Y',
        timezone VARCHAR(50) DEFAULT 'Asia/Jakarta',
        language VARCHAR(5) DEFAULT 'id',
        notifications BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    )"
];

// Handle actions
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    try {
        switch ($_POST['action']) {
            case 'create_all_tables':
                $created = 0;
                $errors = [];
                
                foreach ($requiredTables as $tableName => $sql) {
                    try {
                        $pdo->exec($sql);
                        $created++;
                    } catch (PDOException $e) {
                        $errors[] = "Error creating $tableName: " . $e->getMessage();
                        error_log("Error creating table $tableName: " . $e->getMessage());
                    }
                }
                
                if ($created > 0) {
                    setFlashMessage('success', "Berhasil membuat/memverifikasi $created tabel");
                }
                
                if (!empty($errors)) {
                    setFlashMessage('warning', 'Beberapa tabel gagal dibuat: ' . implode('; ', $errors));
                }
                break;
                
            case 'fix_missing_tables':
                $fixed = 0;
                $errors = [];
                
                foreach ($requiredTables as $tableName => $sql) {
                    // Check if table exists
                    try {
                        $stmt = $pdo->query("SHOW TABLES LIKE '$tableName'");
                        if ($stmt->rowCount() == 0) {
                            // Table doesn't exist, create it
                            $pdo->exec($sql);
                            $fixed++;
                        }
                    } catch (PDOException $e) {
                        $errors[] = "Error fixing $tableName: " . $e->getMessage();
                        error_log("Error fixing table $tableName: " . $e->getMessage());
                    }
                }
                
                if ($fixed > 0) {
                    setFlashMessage('success', "Berhasil memperbaiki $fixed tabel yang hilang");
                } else {
                    setFlashMessage('info', 'Semua tabel sudah ada, tidak ada yang perlu diperbaiki');
                }
                
                if (!empty($errors)) {
                    setFlashMessage('warning', 'Beberapa tabel gagal diperbaiki: ' . implode('; ', $errors));
                }
                break;
        }
        redirect('check_system_health.php');
    } catch (Exception $e) {
        setFlashMessage('danger', 'Error: ' . $e->getMessage());
    }
}

// Check table status
$tableStatus = [];
$missingTables = [];
$existingTables = [];

foreach (array_keys($requiredTables) as $tableName) {
    try {
        $stmt = $pdo->query("SHOW TABLES LIKE '$tableName'");
        $exists = $stmt->rowCount() > 0;
        $tableStatus[$tableName] = $exists;
        
        if ($exists) {
            $existingTables[] = $tableName;
        } else {
            $missingTables[] = $tableName;
        }
    } catch (PDOException $e) {
        $tableStatus[$tableName] = false;
        $missingTables[] = $tableName;
    }
}

// Get database info
$dbInfo = [];
try {
    $stmt = $pdo->query("SELECT VERSION() as version");
    $dbInfo['mysql_version'] = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT DATABASE() as name");
    $dbInfo['database_name'] = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS size_mb FROM information_schema.tables WHERE table_schema = DATABASE()");
    $dbInfo['size_mb'] = $stmt->fetchColumn() ?: 0;
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE()");
    $dbInfo['table_count'] = $stmt->fetchColumn();
} catch (Exception $e) {
    $dbInfo = [
        'mysql_version' => 'Unknown',
        'database_name' => 'Unknown',
        'size_mb' => 'Unknown',
        'table_count' => 'Unknown'
    ];
}

include 'includes/views/layouts/header.php';
?>

<div class="container-fluid px-4 py-3">
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-gradient-primary text-white py-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h5 class="card-title mb-0">
                                <i class="fas fa-heartbeat me-2"></i>
                                System Health Check
                            </h5>
                            <p class="mb-0 small opacity-75">Pemeriksaan kesehatan sistem dan database</p>
                        </div>
                        <div class="d-flex gap-2">
                            <?php if (!empty($missingTables)): ?>
                                <form method="POST" class="d-inline">
                                    <input type="hidden" name="action" value="fix_missing_tables">
                                    <button type="submit" class="btn btn-warning btn-sm">
                                        <i class="fas fa-tools me-1"></i>Fix Missing Tables
                                    </button>
                                </form>
                            <?php endif; ?>
                            <form method="POST" class="d-inline">
                                <input type="hidden" name="action" value="create_all_tables">
                                <button type="submit" class="btn btn-light btn-sm">
                                    <i class="fas fa-sync me-1"></i>Verify All Tables
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
                <div class="card-body p-4">
                    
                    <!-- System Status -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="mb-3">
                                <i class="fas fa-info-circle me-2"></i>System Information
                            </h6>
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="card bg-primary text-white">
                                        <div class="card-body text-center py-3">
                                            <h6 class="mb-1"><?= $dbInfo['mysql_version'] ?></h6>
                                            <small>MySQL Version</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card bg-info text-white">
                                        <div class="card-body text-center py-3">
                                            <h6 class="mb-1"><?= $dbInfo['database_name'] ?></h6>
                                            <small>Database Name</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card bg-success text-white">
                                        <div class="card-body text-center py-3">
                                            <h6 class="mb-1"><?= $dbInfo['size_mb'] ?> MB</h6>
                                            <small>Database Size</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card bg-secondary text-white">
                                        <div class="card-body text-center py-3">
                                            <h6 class="mb-1"><?= $dbInfo['table_count'] ?></h6>
                                            <small>Total Tables</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Health Summary -->
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="card bg-<?= empty($missingTables) ? 'success' : 'danger' ?> text-white">
                                <div class="card-body text-center py-3">
                                    <h4 class="mb-1"><?= count($existingTables) ?>/<?= count($requiredTables) ?></h4>
                                    <small>Tables Status</small>
                                    <div class="mt-2">
                                        <i class="fas fa-<?= empty($missingTables) ? 'check-circle' : 'exclamation-triangle' ?> fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-<?= empty($missingTables) ? 'success' : 'warning' ?> text-white">
                                <div class="card-body text-center py-3">
                                    <h4 class="mb-1"><?= count($missingTables) ?></h4>
                                    <small>Missing Tables</small>
                                    <div class="mt-2">
                                        <i class="fas fa-<?= empty($missingTables) ? 'check' : 'times' ?> fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-<?= empty($missingTables) ? 'success' : 'info' ?> text-white">
                                <div class="card-body text-center py-3">
                                    <h4 class="mb-1"><?= empty($missingTables) ? 'HEALTHY' : 'NEEDS FIX' ?></h4>
                                    <small>System Status</small>
                                    <div class="mt-2">
                                        <i class="fas fa-<?= empty($missingTables) ? 'heart' : 'wrench' ?> fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Table Status Details -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="mb-3">
                                <i class="fas fa-table me-2"></i>Table Status Details
                            </h6>
                            <div class="row">
                                <?php foreach ($tableStatus as $tableName => $exists): ?>
                                    <div class="col-md-6 col-lg-4 mb-3">
                                        <div class="card border-<?= $exists ? 'success' : 'danger' ?>">
                                            <div class="card-body py-3">
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <div>
                                                        <h6 class="mb-1"><?= $tableName ?></h6>
                                                        <small class="text-<?= $exists ? 'success' : 'danger' ?>">
                                                            <i class="fas fa-<?= $exists ? 'check-circle' : 'times-circle' ?> me-1"></i>
                                                            <?= $exists ? 'EXISTS' : 'MISSING' ?>
                                                        </small>
                                                    </div>
                                                    <div>
                                                        <i class="fas fa-table fa-2x text-<?= $exists ? 'success' : 'danger' ?>"></i>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Missing Tables Alert -->
                    <?php if (!empty($missingTables)): ?>
                        <div class="alert alert-warning">
                            <h6 class="alert-heading">
                                <i class="fas fa-exclamation-triangle me-2"></i>Missing Tables Detected
                            </h6>
                            <p class="mb-2">The following tables are missing and need to be created:</p>
                            <ul class="mb-3">
                                <?php foreach ($missingTables as $table): ?>
                                    <li><code><?= $table ?></code></li>
                                <?php endforeach; ?>
                            </ul>
                            <form method="POST" class="d-inline">
                                <input type="hidden" name="action" value="fix_missing_tables">
                                <button type="submit" class="btn btn-warning">
                                    <i class="fas fa-tools me-2"></i>Fix Missing Tables Now
                                </button>
                            </form>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-success">
                            <h6 class="alert-heading">
                                <i class="fas fa-check-circle me-2"></i>System Health: GOOD
                            </h6>
                            <p class="mb-0">All required database tables are present and the system is healthy.</p>
                        </div>
                    <?php endif; ?>
                    
                    <!-- Navigation -->
                    <div class="mt-4 text-center">
                        <a href="notifications.php" class="btn btn-outline-primary me-2">
                            <i class="fas fa-bell me-2"></i>Notifications
                        </a>
                        <a href="update_database.php" class="btn btn-outline-info">
                            <i class="fas fa-database me-2"></i>Database Updates
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/views/layouts/footer.php'; ?>
