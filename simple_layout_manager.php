<?php
require_once 'includes/config/database.php';
require_once 'includes/helpers/functions.php';
require_once 'includes/helpers/layout_helper.php';

// Check if user is logged in
$currentUser = getCurrentUser();
if (!$currentUser) {
    redirect('login.php');
}

$pageTitle = 'Simple Layout Manager';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['save_layout'])) {
    try {
        // Check if table exists, create if not
        $stmt = $pdo->query("SHOW TABLES LIKE 'layout_preferences'");
        if ($stmt->rowCount() === 0) {
            setFlashMessage('warning', 'Layout preferences table not found. Please create it first.');
            redirect('create_layout_table.php');
        }

        $stmt = $pdo->prepare("
            INSERT INTO layout_preferences (user_id, layout_type, color_scheme, border_radius, shadow_style, animation_style)
            VALUES (?, ?, ?, ?, ?, ?)
            ON DUPLICATE KEY UPDATE
            layout_type = VALUES(layout_type),
            color_scheme = VALUES(color_scheme),
            border_radius = VALUES(border_radius),
            shadow_style = VALUES(shadow_style),
            animation_style = VALUES(animation_style),
            updated_at = CURRENT_TIMESTAMP
        ");

        $result = $stmt->execute([
            $currentUser['id'],
            $_POST['layout_type'],
            $_POST['color_scheme'],
            $_POST['border_radius'],
            $_POST['shadow_style'],
            $_POST['animation_style']
        ]);

        if ($result) {
            setFlashMessage('success', 'Layout preferences saved successfully!');
        } else {
            setFlashMessage('danger', 'Failed to save layout preferences');
        }
        redirect('simple_layout_manager.php');
    } catch (Exception $e) {
        setFlashMessage('danger', 'Error: ' . $e->getMessage());
    }
}

// Get current preferences
$currentPrefs = getUserLayoutPreferences($currentUser['id']);

include 'includes/views/layouts/header.php';
?>

<div class="container-fluid px-4 py-3">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h3 class="mb-1">
                        <i class="fas fa-paint-brush me-2 text-primary"></i>
                        Simple Layout Manager
                    </h3>
                    <p class="text-muted mb-0">Easy layout customization with live preview</p>
                </div>
                <div class="btn-group">
                    <a href="advanced_layout_manager.php" class="btn btn-outline-primary">
                        <i class="fas fa-cogs me-1"></i>Advanced
                    </a>
                    <a href="/keuangan/dashboard.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i>Dashboard
                    </a>
                </div>
            </div>

            <div class="row">
                <!-- Layout Configuration -->
                <div class="col-lg-8 mb-4">
                    <form method="POST" id="layoutForm">
                        <!-- Layout Types -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-th-large me-2"></i>
                                    Choose Layout Style
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                                    <div class="col-md-4">
                                        <div class="layout-card">
                                            <input type="radio" id="layout_classic" name="layout_type" value="classic" <?= $currentPrefs['layout_type'] === 'classic' ? 'checked' : '' ?>>
                                            <label for="layout_classic" class="layout-label">
                                                <div class="layout-preview">
                                                    <div class="preview-sidebar classic-sidebar"></div>
                                                    <div class="preview-main">
                                                        <div class="preview-navbar classic-navbar"></div>
                                                        <div class="preview-content"></div>
                                                    </div>
                                                </div>
                                                <div class="layout-info">
                                                    <h6>Classic</h6>
                                                    <small>Professional & Traditional</small>
                                                </div>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="layout-card">
                                            <input type="radio" id="layout_modern" name="layout_type" value="modern" <?= $currentPrefs['layout_type'] === 'modern' ? 'checked' : '' ?>>
                                            <label for="layout_modern" class="layout-label">
                                                <div class="layout-preview">
                                                    <div class="preview-sidebar modern-sidebar"></div>
                                                    <div class="preview-main">
                                                        <div class="preview-navbar modern-navbar"></div>
                                                        <div class="preview-content"></div>
                                                    </div>
                                                </div>
                                                <div class="layout-info">
                                                    <h6>Modern</h6>
                                                    <small>Rounded & Contemporary</small>
                                                </div>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="layout-card">
                                            <input type="radio" id="layout_colorful" name="layout_type" value="colorful" <?= $currentPrefs['layout_type'] === 'colorful' ? 'checked' : '' ?>>
                                            <label for="layout_colorful" class="layout-label">
                                                <div class="layout-preview">
                                                    <div class="preview-sidebar colorful-sidebar"></div>
                                                    <div class="preview-main">
                                                        <div class="preview-navbar colorful-navbar"></div>
                                                        <div class="preview-content"></div>
                                                    </div>
                                                </div>
                                                <div class="layout-info">
                                                    <h6>Colorful</h6>
                                                    <small>Vibrant & Creative</small>
                                                </div>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="layout-card">
                                            <input type="radio" id="layout_minimal" name="layout_type" value="minimal" <?= $currentPrefs['layout_type'] === 'minimal' ? 'checked' : '' ?>>
                                            <label for="layout_minimal" class="layout-label">
                                                <div class="layout-preview">
                                                    <div class="preview-sidebar minimal-sidebar"></div>
                                                    <div class="preview-main">
                                                        <div class="preview-navbar minimal-navbar"></div>
                                                        <div class="preview-content"></div>
                                                    </div>
                                                </div>
                                                <div class="layout-info">
                                                    <h6>Minimal</h6>
                                                    <small>Clean & Simple</small>
                                                </div>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="layout-card">
                                            <input type="radio" id="layout_glassmorphism" name="layout_type" value="glassmorphism" <?= $currentPrefs['layout_type'] === 'glassmorphism' ? 'checked' : '' ?>>
                                            <label for="layout_glassmorphism" class="layout-label">
                                                <div class="layout-preview">
                                                    <div class="preview-sidebar glass-sidebar"></div>
                                                    <div class="preview-main">
                                                        <div class="preview-navbar glass-navbar"></div>
                                                        <div class="preview-content"></div>
                                                    </div>
                                                </div>
                                                <div class="layout-info">
                                                    <h6>Glassmorphism</h6>
                                                    <small>Glass Effect & Modern</small>
                                                </div>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="layout-card">
                                            <input type="radio" id="layout_neon" name="layout_type" value="neon" <?= $currentPrefs['layout_type'] === 'neon' ? 'checked' : '' ?>>
                                            <label for="layout_neon" class="layout-label">
                                                <div class="layout-preview">
                                                    <div class="preview-sidebar neon-sidebar"></div>
                                                    <div class="preview-main">
                                                        <div class="preview-navbar neon-navbar"></div>
                                                        <div class="preview-content"></div>
                                                    </div>
                                                </div>
                                                <div class="layout-info">
                                                    <h6>Neon</h6>
                                                    <small>Glowing & Futuristic</small>
                                                </div>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Color Schemes -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-palette me-2"></i>
                                    Choose Color Scheme
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                                    <div class="col-md-3">
                                        <div class="color-card">
                                            <input type="radio" id="color_default" name="color_scheme" value="default" <?= $currentPrefs['color_scheme'] === 'default' ? 'checked' : '' ?>>
                                            <label for="color_default" class="color-label">
                                                <div class="color-preview default-colors"></div>
                                                <span>Default</span>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="color-card">
                                            <input type="radio" id="color_vibrant" name="color_scheme" value="vibrant" <?= $currentPrefs['color_scheme'] === 'vibrant' ? 'checked' : '' ?>>
                                            <label for="color_vibrant" class="color-label">
                                                <div class="color-preview vibrant-colors"></div>
                                                <span>Vibrant</span>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="color-card">
                                            <input type="radio" id="color_pastel" name="color_scheme" value="pastel" <?= $currentPrefs['color_scheme'] === 'pastel' ? 'checked' : '' ?>>
                                            <label for="color_pastel" class="color-label">
                                                <div class="color-preview pastel-colors"></div>
                                                <span>Pastel</span>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="color-card">
                                            <input type="radio" id="color_neon" name="color_scheme" value="neon" <?= $currentPrefs['color_scheme'] === 'neon' ? 'checked' : '' ?>>
                                            <label for="color_neon" class="color-label">
                                                <div class="color-preview neon-colors"></div>
                                                <span>Neon</span>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="color-card">
                                            <input type="radio" id="color_ocean" name="color_scheme" value="ocean" <?= $currentPrefs['color_scheme'] === 'ocean' ? 'checked' : '' ?>>
                                            <label for="color_ocean" class="color-label">
                                                <div class="color-preview ocean-colors"></div>
                                                <span>Ocean</span>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="color-card">
                                            <input type="radio" id="color_sunset" name="color_scheme" value="sunset" <?= $currentPrefs['color_scheme'] === 'sunset' ? 'checked' : '' ?>>
                                            <label for="color_sunset" class="color-label">
                                                <div class="color-preview sunset-colors"></div>
                                                <span>Sunset</span>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="color-card">
                                            <input type="radio" id="color_midnight" name="color_scheme" value="midnight" <?= $currentPrefs['color_scheme'] === 'midnight' ? 'checked' : '' ?>>
                                            <label for="color_midnight" class="color-label">
                                                <div class="color-preview midnight-colors"></div>
                                                <span>Midnight</span>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="color-card">
                                            <input type="radio" id="color_royal" name="color_scheme" value="royal" <?= $currentPrefs['color_scheme'] === 'royal' ? 'checked' : '' ?>>
                                            <label for="color_royal" class="color-label">
                                                <div class="color-preview royal-colors"></div>
                                                <span>Royal</span>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Quick Options -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-sliders-h me-2"></i>
                                    Quick Options
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                                    <div class="col-md-4">
                                        <label class="form-label">Border Radius</label>
                                        <select name="border_radius" class="form-select">
                                            <option value="none" <?= $currentPrefs['border_radius'] === 'none' ? 'selected' : '' ?>>None</option>
                                            <option value="small" <?= $currentPrefs['border_radius'] === 'small' ? 'selected' : '' ?>>Small</option>
                                            <option value="medium" <?= $currentPrefs['border_radius'] === 'medium' ? 'selected' : '' ?>>Medium</option>
                                            <option value="large" <?= $currentPrefs['border_radius'] === 'large' ? 'selected' : '' ?>>Large</option>
                                            <option value="xl" <?= $currentPrefs['border_radius'] === 'xl' ? 'selected' : '' ?>>Extra Large</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">Shadow Style</label>
                                        <select name="shadow_style" class="form-select">
                                            <option value="none" <?= $currentPrefs['shadow_style'] === 'none' ? 'selected' : '' ?>>None</option>
                                            <option value="soft" <?= $currentPrefs['shadow_style'] === 'soft' ? 'selected' : '' ?>>Soft</option>
                                            <option value="medium" <?= $currentPrefs['shadow_style'] === 'medium' ? 'selected' : '' ?>>Medium</option>
                                            <option value="strong" <?= $currentPrefs['shadow_style'] === 'strong' ? 'selected' : '' ?>>Strong</option>
                                            <option value="colored" <?= $currentPrefs['shadow_style'] === 'colored' ? 'selected' : '' ?>>Colored</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">Animation</label>
                                        <select name="animation_style" class="form-select">
                                            <option value="none" <?= $currentPrefs['animation_style'] === 'none' ? 'selected' : '' ?>>None</option>
                                            <option value="subtle" <?= $currentPrefs['animation_style'] === 'subtle' ? 'selected' : '' ?>>Subtle</option>
                                            <option value="smooth" <?= $currentPrefs['animation_style'] === 'smooth' ? 'selected' : '' ?>>Smooth</option>
                                            <option value="bouncy" <?= $currentPrefs['animation_style'] === 'bouncy' ? 'selected' : '' ?>>Bouncy</option>
                                            <option value="elastic" <?= $currentPrefs['animation_style'] === 'elastic' ? 'selected' : '' ?>>Elastic</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="card">
                            <div class="card-body text-center">
                                <button type="submit" name="save_layout" class="btn btn-primary btn-lg me-3">
                                    <i class="fas fa-save me-2"></i>Save Layout
                                </button>
                                <button type="button" class="btn btn-info btn-lg me-3" onclick="applyPreview()">
                                    <i class="fas fa-eye me-2"></i>Apply Preview
                                </button>
                                <button type="button" class="btn btn-secondary btn-lg" onclick="resetLayout()">
                                    <i class="fas fa-undo me-2"></i>Reset
                                </button>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- Live Preview -->
                <div class="col-lg-4">
                    <div class="card sticky-top">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-eye me-2"></i>
                                Live Preview
                            </h5>
                        </div>
                        <div class="card-body">
                            <div id="livePreview" class="live-preview-container">
                                <div class="preview-app">
                                    <div class="preview-sidebar-large" id="previewSidebar">
                                        <div class="preview-logo">
                                            <i class="fas fa-chart-line"></i>
                                            <span>App</span>
                                        </div>
                                        <div class="preview-menu">
                                            <div class="preview-menu-item active">
                                                <i class="fas fa-home"></i>
                                                <span>Dashboard</span>
                                            </div>
                                            <div class="preview-menu-item">
                                                <i class="fas fa-users"></i>
                                                <span>Users</span>
                                            </div>
                                            <div class="preview-menu-item">
                                                <i class="fas fa-cog"></i>
                                                <span>Settings</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="preview-main-large">
                                        <div class="preview-navbar-large" id="previewNavbar">
                                            <div class="preview-nav-content">
                                                <span>Dashboard</span>
                                                <div class="preview-nav-actions">
                                                    <i class="fas fa-bell"></i>
                                                    <i class="fas fa-user"></i>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="preview-content-large">
                                            <div class="preview-card-large">
                                                <h6>Sample Card</h6>
                                                <p>This is how your content will look</p>
                                            </div>
                                            <div class="preview-card-large">
                                                <h6>Another Card</h6>
                                                <p>With the selected layout style</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="text-center mt-3">
                                <button type="button" class="btn btn-sm btn-outline-primary" onclick="updateLivePreview()">
                                    <i class="fas fa-sync me-1"></i>Update Preview
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Layout Cards */
.layout-card {
    position: relative;
}

.layout-card input[type="radio"] {
    display: none;
}

.layout-label {
    display: block;
    border: 2px solid #dee2e6;
    border-radius: 12px;
    padding: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #fff;
    height: 100%;
}

.layout-label:hover {
    border-color: #007bff;
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,123,255,0.15);
}

.layout-card input[type="radio"]:checked + .layout-label {
    border-color: #007bff;
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(0,123,255,0.3);
}

.layout-preview {
    width: 100%;
    height: 80px;
    border-radius: 8px;
    overflow: hidden;
    display: flex;
    margin-bottom: 12px;
    background: #f8f9fa;
}

.preview-sidebar {
    width: 30%;
    position: relative;
}

.preview-main {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.preview-navbar {
    height: 25%;
}

.preview-content {
    flex: 1;
    background: #e9ecef;
}

/* Layout Specific Styles */
.classic-sidebar { background: #343a40; }
.classic-navbar { background: #007bff; }

.modern-sidebar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 0 8px 8px 0;
}
.modern-navbar {
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    border-radius: 0 0 8px 8px;
}

.colorful-sidebar { background: linear-gradient(135deg, #ff6b6b 0%, #4ecdc4 50%, #45b7d1 100%); }
.colorful-navbar { background: linear-gradient(90deg, #ff6b6b 0%, #4ecdc4 50%, #45b7d1 100%); }

.minimal-sidebar {
    background: #f8f9fa;
    border-right: 1px solid #dee2e6;
}
.minimal-navbar {
    background: #fff;
    border-bottom: 1px solid #dee2e6;
}

.glass-sidebar {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.18);
}
.glass-navbar {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.18);
}

.neon-sidebar {
    background: linear-gradient(135deg, #39ff14 0%, #ff073a 50%, #00ffff 100%);
    box-shadow: 0 0 10px rgba(57, 255, 20, 0.3);
}
.neon-navbar {
    background: linear-gradient(90deg, #39ff14 0%, #ff073a 50%, #00ffff 100%);
    box-shadow: 0 0 10px rgba(57, 255, 20, 0.3);
}

.layout-info h6 {
    margin: 0 0 5px 0;
    font-weight: 600;
}

.layout-info small {
    opacity: 0.8;
}

/* Color Cards */
.color-card {
    position: relative;
}

.color-card input[type="radio"] {
    display: none;
}

.color-label {
    display: block;
    border: 2px solid #dee2e6;
    border-radius: 8px;
    padding: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #fff;
    text-align: center;
}

.color-label:hover {
    border-color: #007bff;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,123,255,0.15);
}

.color-card input[type="radio"]:checked + .color-label {
    border-color: #007bff;
    background: #f8f9fa;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,123,255,0.2);
}

.color-preview {
    width: 100%;
    height: 40px;
    border-radius: 6px;
    margin-bottom: 8px;
    border: 1px solid rgba(0,0,0,0.1);
}

/* Color Scheme Previews */
.default-colors { background: linear-gradient(45deg, #007bff, #6c757d); }
.vibrant-colors { background: linear-gradient(45deg, #ff0080, #00ff80, #8000ff); }
.pastel-colors { background: linear-gradient(45deg, #ffb3ba, #bae1ff, #baffc9); }
.neon-colors { background: linear-gradient(45deg, #39ff14, #ff073a, #00ffff); }
.ocean-colors { background: linear-gradient(45deg, #006994, #0099cc, #66ccff); }
.sunset-colors { background: linear-gradient(45deg, #ff4500, #ff6347, #ffd700); }
.midnight-colors { background: linear-gradient(45deg, #2c3e50, #34495e, #1a252f); }
.royal-colors { background: linear-gradient(45deg, #663399, #9966cc, #cc99ff); }

/* Live Preview */
.live-preview-container {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    overflow: hidden;
    background: #f8f9fa;
}

.preview-app {
    display: flex;
    height: 300px;
}

.preview-sidebar-large {
    width: 35%;
    background: #343a40;
    color: white;
    padding: 15px;
    display: flex;
    flex-direction: column;
}

.preview-logo {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    font-weight: bold;
    font-size: 16px;
}

.preview-logo i {
    margin-right: 8px;
    font-size: 18px;
}

.preview-menu-item {
    display: flex;
    align-items: center;
    padding: 8px 0;
    margin-bottom: 5px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.preview-menu-item:hover {
    background: rgba(255,255,255,0.1);
}

.preview-menu-item.active {
    background: rgba(255,255,255,0.2);
}

.preview-menu-item i {
    margin-right: 8px;
    width: 16px;
}

.preview-main-large {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.preview-navbar-large {
    background: #007bff;
    color: white;
    padding: 12px 15px;
    display: flex;
    align-items: center;
}

.preview-nav-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

.preview-nav-actions {
    display: flex;
    gap: 10px;
}

.preview-content-large {
    flex: 1;
    padding: 15px;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.preview-card-large {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.preview-card-large h6 {
    margin: 0 0 8px 0;
    color: #333;
}

.preview-card-large p {
    margin: 0;
    color: #666;
    font-size: 14px;
}

/* Responsive */
@media (max-width: 768px) {
    .preview-app {
        height: 200px;
    }

    .preview-sidebar-large {
        width: 40%;
        padding: 10px;
    }

    .preview-logo {
        font-size: 14px;
        margin-bottom: 15px;
    }

    .preview-menu-item {
        padding: 6px 0;
        font-size: 12px;
    }

    .preview-navbar-large {
        padding: 8px 10px;
        font-size: 14px;
    }

    .preview-content-large {
        padding: 10px;
    }

    .preview-card-large {
        padding: 10px;
    }
}

/* Animation */
.layout-label, .color-label {
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Button Enhancements */
.btn-lg {
    padding: 12px 30px;
    font-size: 16px;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.btn-lg:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.sticky-top {
    top: 20px;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize preview
    updateLivePreview();

    // Add event listeners
    const radioButtons = document.querySelectorAll('input[type="radio"]');
    const selectBoxes = document.querySelectorAll('select');

    radioButtons.forEach(radio => {
        radio.addEventListener('change', updateLivePreview);
    });

    selectBoxes.forEach(select => {
        select.addEventListener('change', updateLivePreview);
    });
});

function updateLivePreview() {
    const layoutType = document.querySelector('input[name="layout_type"]:checked')?.value || 'classic';
    const colorScheme = document.querySelector('input[name="color_scheme"]:checked')?.value || 'default';
    const borderRadius = document.querySelector('select[name="border_radius"]')?.value || 'medium';
    const shadowStyle = document.querySelector('select[name="shadow_style"]')?.value || 'soft';

    const sidebar = document.getElementById('previewSidebar');
    const navbar = document.getElementById('previewNavbar');
    const cards = document.querySelectorAll('.preview-card-large');

    // Color schemes
    const colorSchemes = {
        'default': { sidebar: '#343a40', navbar: '#007bff', text: '#ffffff' },
        'vibrant': { sidebar: 'linear-gradient(135deg, #ff0080 0%, #00ff80 50%, #8000ff 100%)', navbar: 'linear-gradient(90deg, #ff0080 0%, #00ff80 50%, #8000ff 100%)', text: '#ffffff' },
        'pastel': { sidebar: 'linear-gradient(135deg, #ffb3ba 0%, #bae1ff 50%, #baffc9 100%)', navbar: 'linear-gradient(90deg, #ffb3ba 0%, #bae1ff 50%, #baffc9 100%)', text: '#2c3e50' },
        'neon': { sidebar: 'linear-gradient(135deg, #39ff14 0%, #ff073a 50%, #00ffff 100%)', navbar: 'linear-gradient(90deg, #39ff14 0%, #ff073a 50%, #00ffff 100%)', text: '#000000' },
        'ocean': { sidebar: 'linear-gradient(135deg, #006994 0%, #0099cc 50%, #66ccff 100%)', navbar: 'linear-gradient(90deg, #006994 0%, #0099cc 50%, #66ccff 100%)', text: '#ffffff' },
        'sunset': { sidebar: 'linear-gradient(135deg, #ff4500 0%, #ff6347 50%, #ffd700 100%)', navbar: 'linear-gradient(90deg, #ff4500 0%, #ff6347 50%, #ffd700 100%)', text: '#ffffff' },
        'midnight': { sidebar: 'linear-gradient(135deg, #2c3e50 0%, #34495e 50%, #1a252f 100%)', navbar: 'linear-gradient(90deg, #2c3e50 0%, #34495e 50%, #1a252f 100%)', text: '#ecf0f1' },
        'royal': { sidebar: 'linear-gradient(135deg, #663399 0%, #9966cc 50%, #cc99ff 100%)', navbar: 'linear-gradient(90deg, #663399 0%, #9966cc 50%, #cc99ff 100%)', text: '#ffffff' }
    };

    const colors = colorSchemes[colorScheme] || colorSchemes['default'];

    // Apply colors
    if (sidebar) {
        sidebar.style.background = colors.sidebar;
        sidebar.style.color = colors.text;
    }
    if (navbar) {
        navbar.style.background = colors.navbar;
        navbar.style.color = colors.text;
    }

    // Apply border radius
    const radiusValues = {
        'none': '0px',
        'small': '4px',
        'medium': '8px',
        'large': '15px',
        'xl': '25px'
    };

    const radius = radiusValues[borderRadius] || '8px';

    if (layoutType !== 'minimal') {
        if (sidebar) sidebar.style.borderRadius = `0 ${radius} ${radius} 0`;
        if (navbar) navbar.style.borderRadius = `0 0 ${radius} ${radius}`;
    }

    cards.forEach(card => {
        card.style.borderRadius = radius;

        // Apply shadow
        const shadowValues = {
            'none': 'none',
            'soft': '0 2px 10px rgba(0,0,0,0.08)',
            'medium': '0 4px 15px rgba(0,0,0,0.12)',
            'strong': '0 8px 25px rgba(0,0,0,0.18)',
            'colored': '0 8px 25px rgba(0, 123, 255, 0.3)'
        };

        card.style.boxShadow = shadowValues[shadowStyle] || shadowValues['soft'];
    });

    // Special layout effects
    if (layoutType === 'glassmorphism') {
        if (sidebar) {
            sidebar.style.background = 'rgba(255, 255, 255, 0.25)';
            sidebar.style.backdropFilter = 'blur(10px)';
            sidebar.style.border = '1px solid rgba(255, 255, 255, 0.18)';
        }
        if (navbar) {
            navbar.style.background = 'rgba(255, 255, 255, 0.25)';
            navbar.style.backdropFilter = 'blur(10px)';
            navbar.style.border = '1px solid rgba(255, 255, 255, 0.18)';
        }
    } else if (layoutType === 'neon' || colorScheme === 'neon') {
        if (sidebar) sidebar.style.boxShadow = '0 0 20px rgba(57, 255, 20, 0.4)';
        if (navbar) navbar.style.boxShadow = '0 0 15px rgba(57, 255, 20, 0.4)';
    } else if (layoutType === 'minimal') {
        if (sidebar) {
            sidebar.style.background = '#f8f9fa';
            sidebar.style.color = '#2c3e50';
            sidebar.style.borderRight = '1px solid #dee2e6';
            sidebar.style.borderRadius = '0';
        }
        if (navbar) {
            navbar.style.background = '#fff';
            navbar.style.color = '#2c3e50';
            navbar.style.borderBottom = '1px solid #dee2e6';
            navbar.style.borderRadius = '0';
        }
    }
}

function applyPreview() {
    const formData = new FormData(document.getElementById('layoutForm'));

    // Show loading
    const btn = document.querySelector('button[onclick="applyPreview()"]');
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Applying...';
    btn.disabled = true;

    // Create temporary CSS
    const tempCSS = generateTempCSS(formData);

    // Remove existing temp CSS
    const existingTemp = document.getElementById('temp-layout-css');
    if (existingTemp) existingTemp.remove();

    // Add new temp CSS
    const style = document.createElement('style');
    style.id = 'temp-layout-css';
    style.innerHTML = tempCSS;
    document.head.appendChild(style);

    // Restore button
    setTimeout(() => {
        btn.innerHTML = originalText;
        btn.disabled = false;
        showNotification('Preview applied! Save to make permanent.', 'info');
    }, 1000);
}

function generateTempCSS(formData) {
    const layoutType = formData.get('layout_type');
    const colorScheme = formData.get('color_scheme');
    const borderRadius = formData.get('border_radius');

    const colorSchemes = {
        'default': { sidebar: '#343a40', navbar: '#007bff', text: '#ffffff' },
        'vibrant': { sidebar: 'linear-gradient(135deg, #ff0080 0%, #00ff80 50%, #8000ff 100%)', navbar: 'linear-gradient(90deg, #ff0080 0%, #00ff80 50%, #8000ff 100%)', text: '#ffffff' },
        'pastel': { sidebar: 'linear-gradient(135deg, #ffb3ba 0%, #bae1ff 50%, #baffc9 100%)', navbar: 'linear-gradient(90deg, #ffb3ba 0%, #bae1ff 50%, #baffc9 100%)', text: '#2c3e50' },
        'neon': { sidebar: 'linear-gradient(135deg, #39ff14 0%, #ff073a 50%, #00ffff 100%)', navbar: 'linear-gradient(90deg, #39ff14 0%, #ff073a 50%, #00ffff 100%)', text: '#000000' },
        'ocean': { sidebar: 'linear-gradient(135deg, #006994 0%, #0099cc 50%, #66ccff 100%)', navbar: 'linear-gradient(90deg, #006994 0%, #0099cc 50%, #66ccff 100%)', text: '#ffffff' },
        'sunset': { sidebar: 'linear-gradient(135deg, #ff4500 0%, #ff6347 50%, #ffd700 100%)', navbar: 'linear-gradient(90deg, #ff4500 0%, #ff6347 50%, #ffd700 100%)', text: '#ffffff' },
        'midnight': { sidebar: 'linear-gradient(135deg, #2c3e50 0%, #34495e 50%, #1a252f 100%)', navbar: 'linear-gradient(90deg, #2c3e50 0%, #34495e 50%, #1a252f 100%)', text: '#ecf0f1' },
        'royal': { sidebar: 'linear-gradient(135deg, #663399 0%, #9966cc 50%, #cc99ff 100%)', navbar: 'linear-gradient(90deg, #663399 0%, #9966cc 50%, #cc99ff 100%)', text: '#ffffff' }
    };

    const radiusValues = {
        'none': '0px',
        'small': '4px',
        'medium': '8px',
        'large': '15px',
        'xl': '25px'
    };

    const colors = colorSchemes[colorScheme] || colorSchemes['default'];
    const radius = radiusValues[borderRadius] || '8px';

    return `
        /* Temporary Layout Preview */
        body .sidebar, body .modern-sidebar, body #sidebar {
            background: ${colors.sidebar} !important;
            ${layoutType !== 'minimal' ? `border-radius: 0 ${radius} ${radius} 0 !important;` : ''}
        }
        body .navbar, body .modern-navbar, body #mainNavbar {
            background: ${colors.navbar} !important;
            ${layoutType !== 'minimal' ? `border-radius: 0 0 ${radius} ${radius} !important;` : ''}
        }
        body .sidebar a, body .sidebar .nav-link, body .sidebar .brand-title {
            color: ${colors.text} !important;
        }
        body .card {
            border-radius: ${radius} !important;
        }
    `;
}

function resetLayout() {
    if (confirm('Reset to default layout?')) {
        document.getElementById('layout_classic').checked = true;
        document.getElementById('color_default').checked = true;
        document.querySelector('select[name="border_radius"]').value = 'medium';
        document.querySelector('select[name="shadow_style"]').value = 'soft';
        document.querySelector('select[name="animation_style"]').value = 'subtle';

        updateLivePreview();
        showNotification('Layout reset to default.', 'success');
    }
}

function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}
</script>

<?php include 'includes/views/layouts/footer.php'; ?>
