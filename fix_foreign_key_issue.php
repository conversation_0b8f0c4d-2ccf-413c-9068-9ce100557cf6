<?php
require_once 'includes/config/database.php';

$results = [];

try {
    // 1. Check foreign key constraints on supplier table
    $stmt = $pdo->query("
        SELECT 
            TABLE_NAME,
            CONSTRAINT_NAME,
            COLUMN_NAME,
            REFERENCED_TABLE_NAME,
            REFERENCED_COLUMN_NAME
        FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
        WHERE REFERENCED_TABLE_NAME = 'supplier'
    ");
    $constraints = $stmt->fetchAll();
    
    if (!empty($constraints)) {
        $results[] = "Found " . count($constraints) . " foreign key constraints referencing supplier table:";
        foreach ($constraints as $constraint) {
            $results[] = "  - {$constraint['TABLE_NAME']}.{$constraint['COLUMN_NAME']} -> {$constraint['REFERENCED_TABLE_NAME']}.{$constraint['REFERENCED_COLUMN_NAME']} (Constraint: {$constraint['CONSTRAINT_NAME']})";
        }
        
        // 2. Drop foreign key constraints
        foreach ($constraints as $constraint) {
            try {
                $dropSQL = "ALTER TABLE `{$constraint['TABLE_NAME']}` DROP FOREIGN KEY `{$constraint['CONSTRAINT_NAME']}`";
                $pdo->exec($dropSQL);
                $results[] = "✅ Dropped constraint {$constraint['CONSTRAINT_NAME']} from {$constraint['TABLE_NAME']}";
            } catch (PDOException $e) {
                $results[] = "⚠️ Could not drop constraint {$constraint['CONSTRAINT_NAME']}: " . $e->getMessage();
            }
        }
    } else {
        $results[] = "No foreign key constraints found referencing supplier table";
    }
    
    // 3. Now try to drop and recreate supplier table
    try {
        $pdo->exec("DROP TABLE IF EXISTS supplier");
        $results[] = "✅ Successfully dropped supplier table";
    } catch (PDOException $e) {
        $results[] = "❌ Still cannot drop supplier table: " . $e->getMessage();
        
        // Alternative: Just truncate and alter the table
        try {
            $pdo->exec("TRUNCATE TABLE supplier");
            $results[] = "✅ Truncated supplier table instead";
            
            // Check current structure
            $stmt = $pdo->query("DESCRIBE supplier");
            $currentColumns = $stmt->fetchAll(PDO::FETCH_COLUMN);
            $results[] = "Current columns: " . implode(', ', $currentColumns);
            
            // Add missing columns if needed
            $requiredColumns = ['status', 'created_at', 'updated_at'];
            foreach ($requiredColumns as $column) {
                if (!in_array($column, $currentColumns)) {
                    switch ($column) {
                        case 'status':
                            $pdo->exec("ALTER TABLE supplier ADD COLUMN status ENUM('aktif', 'nonaktif') DEFAULT 'aktif'");
                            $results[] = "✅ Added status column";
                            break;
                        case 'created_at':
                            $pdo->exec("ALTER TABLE supplier ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP");
                            $results[] = "✅ Added created_at column";
                            break;
                        case 'updated_at':
                            $pdo->exec("ALTER TABLE supplier ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP");
                            $results[] = "✅ Added updated_at column";
                            break;
                    }
                }
            }
            
            // Insert sample data
            $stmt = $pdo->prepare("INSERT INTO supplier (user_id, nama_supplier, kontak, email, alamat, keterangan, status) VALUES (?, ?, ?, ?, ?, ?, ?)");
            
            $sampleData = [
                [1, 'PT Supplier Utama', '021-12345678', '<EMAIL>', 'Jakarta', 'Supplier utama', 'aktif'],
                [1, 'CV Mitra Jaya', '081234567890', '<EMAIL>', 'Bandung', 'Supplier mitra', 'aktif'],
                [1, 'Toko Berkah', '022-87654321', '<EMAIL>', 'Surabaya', 'Supplier lokal', 'nonaktif']
            ];
            
            foreach ($sampleData as $data) {
                $stmt->execute($data);
            }
            $results[] = "✅ Inserted sample data (3 suppliers)";
            
        } catch (PDOException $e2) {
            $results[] = "❌ Cannot truncate table either: " . $e2->getMessage();
        }
    }
    
    // 4. If we successfully dropped the table, recreate it
    if (strpos(end($results), "Successfully dropped supplier table") !== false) {
        $createSQL = "
        CREATE TABLE supplier (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL DEFAULT 1,
            nama_supplier VARCHAR(255) NOT NULL,
            kontak VARCHAR(100),
            email VARCHAR(255),
            alamat TEXT,
            keterangan TEXT,
            status ENUM('aktif', 'nonaktif') DEFAULT 'aktif',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_user_id (user_id),
            INDEX idx_status (status)
        )";
        
        $pdo->exec($createSQL);
        $results[] = "✅ Created new supplier table";
        
        // Insert sample data
        $stmt = $pdo->prepare("INSERT INTO supplier (user_id, nama_supplier, kontak, email, alamat, keterangan, status) VALUES (?, ?, ?, ?, ?, ?, ?)");
        
        $sampleData = [
            [1, 'PT Supplier Utama', '021-12345678', '<EMAIL>', 'Jakarta', 'Supplier utama', 'aktif'],
            [1, 'CV Mitra Jaya', '081234567890', '<EMAIL>', 'Bandung', 'Supplier mitra', 'aktif'],
            [1, 'Toko Berkah', '022-87654321', '<EMAIL>', 'Surabaya', 'Supplier lokal', 'nonaktif']
        ];
        
        foreach ($sampleData as $data) {
            $stmt->execute($data);
        }
        $results[] = "✅ Inserted sample data (3 suppliers)";
    }
    
    // 5. Test basic operations
    $stmt = $pdo->query("SELECT COUNT(*) FROM supplier");
    $count = $stmt->fetchColumn();
    $results[] = "✅ Test SELECT: $count suppliers found";
    
    // Test insert
    $stmt = $pdo->prepare("INSERT INTO supplier (user_id, nama_supplier, status) VALUES (?, ?, ?)");
    $testResult = $stmt->execute([1, 'Test Supplier ' . date('H:i:s'), 'aktif']);
    if ($testResult) {
        $testId = $pdo->lastInsertId();
        $results[] = "✅ Test INSERT successful (ID: $testId)";
        
        // Clean up test record
        $stmt = $pdo->prepare("DELETE FROM supplier WHERE id = ?");
        $stmt->execute([$testId]);
        $results[] = "✅ Test DELETE successful";
    }
    
    $results[] = "🎉 Supplier table is now ready for use!";
    
} catch (PDOException $e) {
    $results[] = "❌ Database error: " . $e->getMessage();
    $results[] = "❌ Error code: " . $e->getCode();
} catch (Exception $e) {
    $results[] = "❌ General error: " . $e->getMessage();
}

?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fix Foreign Key Issue - KeuanganKu</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="card shadow">
                    <div class="card-header bg-warning text-dark">
                        <h4 class="mb-0">
                            <i class="fas fa-unlink me-2"></i>Fix Foreign Key Constraint Issue
                        </h4>
                    </div>
                    <div class="card-body">
                        
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>Foreign Key Fix Results:</h6>
                            <ul class="mb-0">
                                <?php foreach ($results as $result): ?>
                                <li><?= $result ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                        
                        <?php if (strpos(end($results), '🎉') !== false): ?>
                        <div class="alert alert-success">
                            <h6><i class="fas fa-check-circle me-2"></i>Success!</h6>
                            <p class="mb-0">The supplier table has been fixed and is ready for use. All foreign key constraints have been handled properly.</p>
                        </div>
                        <?php endif; ?>
                        
                        <!-- Next Steps -->
                        <div class="card mt-4">
                            <div class="card-header">
                                <h6><i class="fas fa-list-check me-2"></i>Next Steps</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6>Test the Fix:</h6>
                                        <div class="d-grid gap-2">
                                            <a href="/keuangan/supplier.php" class="btn btn-success">
                                                <i class="fas fa-truck me-2"></i>Test Supplier Page
                                            </a>
                                            <a href="/keuangan/test_supplier_insert.php" class="btn btn-info">
                                                <i class="fas fa-flask me-2"></i>Test Insert Function
                                            </a>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <h6>Additional Tools:</h6>
                                        <div class="d-grid gap-2">
                                            <a href="/keuangan/diagnose_database.php" class="btn btn-secondary">
                                                <i class="fas fa-stethoscope me-2"></i>Run Full Diagnostics
                                            </a>
                                            <a href="/keuangan/index.php" class="btn btn-primary">
                                                <i class="fas fa-home me-2"></i>Go to Dashboard
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Warning about Foreign Keys -->
                        <div class="alert alert-warning mt-4">
                            <h6><i class="fas fa-exclamation-triangle me-2"></i>Important Note:</h6>
                            <p class="mb-0">Foreign key constraints from other tables (like inventory) to the supplier table have been removed. If you need these relationships, they will need to be recreated after ensuring data consistency.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
