<?php
/**
 * Workflow Automation Helper Functions
 * 
 * This file contains functions for automated business processes
 */

/**
 * Get available workflow triggers
 * @return array List of available triggers
 */
function getWorkflowTriggers() {
    return [
        'transaction_created' => [
            'name' => 'Transaksi Dibuat',
            'description' => 'Dipicu ketika transaksi baru dibuat',
            'parameters' => ['amount', 'type', 'category', 'user_id']
        ],
        'target_achieved' => [
            'name' => 'Target Tercapai',
            'description' => 'Dipicu ketika target keuangan tercapai',
            'parameters' => ['target_id', 'target_amount', 'achieved_amount']
        ],
        'low_stock' => [
            'name' => 'Stok Rendah',
            'description' => 'Dipicu ketika stok produk rendah',
            'parameters' => ['product_id', 'current_stock', 'minimum_stock']
        ],
        'monthly_report' => [
            'name' => 'Laporan Bulanan',
            'description' => 'Dipicu setiap akhir bulan',
            'parameters' => ['month', 'year', 'user_id']
        ],
        'expense_limit_exceeded' => [
            'name' => 'Batas Pengeluaran Terlampaui',
            'description' => 'Dipicu ketika pengeluaran melebihi batas',
            'parameters' => ['category', 'limit', 'current_amount']
        ],
        'user_registered' => [
            'name' => 'User Terdaftar',
            'description' => 'Dipicu ketika user baru mendaftar',
            'parameters' => ['user_id', 'email', 'name']
        ]
    ];
}

/**
 * Get available workflow actions
 * @return array List of available actions
 */
function getWorkflowActions() {
    return [
        'send_email' => [
            'name' => 'Kirim Email',
            'description' => 'Mengirim email notifikasi',
            'parameters' => ['to', 'subject', 'template', 'data']
        ],
        'create_notification' => [
            'name' => 'Buat Notifikasi',
            'description' => 'Membuat notifikasi dalam sistem',
            'parameters' => ['user_id', 'title', 'message', 'type']
        ],
        'update_inventory' => [
            'name' => 'Update Inventory',
            'description' => 'Mengupdate data inventory',
            'parameters' => ['product_id', 'action', 'quantity']
        ],
        'create_backup' => [
            'name' => 'Buat Backup',
            'description' => 'Membuat backup database',
            'parameters' => ['type', 'filename']
        ],
        'generate_report' => [
            'name' => 'Generate Laporan',
            'description' => 'Membuat laporan otomatis',
            'parameters' => ['report_type', 'period', 'user_id']
        ],
        'webhook_call' => [
            'name' => 'Panggil Webhook',
            'description' => 'Memanggil webhook eksternal',
            'parameters' => ['url', 'method', 'data']
        ]
    ];
}

/**
 * Create workflow rule
 * @param array $ruleData Workflow rule data
 * @return int|false Workflow rule ID or false on failure
 */
function createWorkflowRule($ruleData) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("
            INSERT INTO workflow_rules (
                name, description, trigger_type, trigger_conditions, 
                actions, is_active, created_by, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
        ");
        
        $result = $stmt->execute([
            $ruleData['name'],
            $ruleData['description'],
            $ruleData['trigger_type'],
            json_encode($ruleData['trigger_conditions']),
            json_encode($ruleData['actions']),
            $ruleData['is_active'] ?? 1,
            $ruleData['created_by']
        ]);
        
        if ($result) {
            $ruleId = $pdo->lastInsertId();
            logSystemEvent("Workflow rule created", 'info', [
                'rule_id' => $ruleId,
                'rule_name' => $ruleData['name']
            ]);
            return $ruleId;
        }
        
        return false;
        
    } catch (Exception $e) {
        error_log("Create workflow rule error: " . $e->getMessage());
        return false;
    }
}

/**
 * Execute workflow trigger
 * @param string $triggerType Trigger type
 * @param array $triggerData Trigger data
 * @return bool Success status
 */
function executeWorkflowTrigger($triggerType, $triggerData) {
    global $pdo;
    
    try {
        // Get active workflow rules for this trigger
        $stmt = $pdo->prepare("
            SELECT * FROM workflow_rules 
            WHERE trigger_type = ? AND is_active = 1
        ");
        $stmt->execute([$triggerType]);
        $rules = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        foreach ($rules as $rule) {
            $triggerConditions = json_decode($rule['trigger_conditions'], true);
            
            // Check if trigger conditions are met
            if (evaluateWorkflowConditions($triggerConditions, $triggerData)) {
                executeWorkflowActions($rule, $triggerData);
                
                // Log workflow execution
                logWorkflowExecution($rule['id'], $triggerData);
            }
        }
        
        return true;
        
    } catch (Exception $e) {
        error_log("Execute workflow trigger error: " . $e->getMessage());
        return false;
    }
}

/**
 * Evaluate workflow conditions
 * @param array $conditions Conditions to evaluate
 * @param array $data Trigger data
 * @return bool True if conditions are met
 */
function evaluateWorkflowConditions($conditions, $data) {
    if (empty($conditions)) {
        return true; // No conditions means always execute
    }
    
    foreach ($conditions as $condition) {
        $field = $condition['field'];
        $operator = $condition['operator'];
        $value = $condition['value'];
        $dataValue = $data[$field] ?? null;
        
        switch ($operator) {
            case 'equals':
                if ($dataValue != $value) return false;
                break;
            case 'not_equals':
                if ($dataValue == $value) return false;
                break;
            case 'greater_than':
                if ($dataValue <= $value) return false;
                break;
            case 'less_than':
                if ($dataValue >= $value) return false;
                break;
            case 'contains':
                if (strpos($dataValue, $value) === false) return false;
                break;
            case 'not_contains':
                if (strpos($dataValue, $value) !== false) return false;
                break;
        }
    }
    
    return true;
}

/**
 * Execute workflow actions
 * @param array $rule Workflow rule
 * @param array $triggerData Trigger data
 * @return bool Success status
 */
function executeWorkflowActions($rule, $triggerData) {
    $actions = json_decode($rule['actions'], true);
    
    foreach ($actions as $action) {
        $actionType = $action['type'];
        $actionParams = $action['parameters'];
        
        // Replace placeholders in parameters with actual data
        $actionParams = replacePlaceholders($actionParams, $triggerData);
        
        switch ($actionType) {
            case 'send_email':
                executeEmailAction($actionParams);
                break;
            case 'create_notification':
                executeNotificationAction($actionParams);
                break;
            case 'update_inventory':
                executeInventoryAction($actionParams);
                break;
            case 'create_backup':
                executeBackupAction($actionParams);
                break;
            case 'generate_report':
                executeReportAction($actionParams);
                break;
            case 'webhook_call':
                executeWebhookAction($actionParams);
                break;
        }
    }
    
    return true;
}

/**
 * Replace placeholders in action parameters
 * @param array $params Action parameters
 * @param array $data Trigger data
 * @return array Parameters with replaced placeholders
 */
function replacePlaceholders($params, $data) {
    $result = [];
    
    foreach ($params as $key => $value) {
        if (is_string($value)) {
            // Replace placeholders like {{field_name}}
            $result[$key] = preg_replace_callback('/\{\{(\w+)\}\}/', function($matches) use ($data) {
                return $data[$matches[1]] ?? $matches[0];
            }, $value);
        } else {
            $result[$key] = $value;
        }
    }
    
    return $result;
}

/**
 * Execute email action
 * @param array $params Email parameters
 */
function executeEmailAction($params) {
    require_once 'email_system.php';
    
    $to = $params['to'];
    $subject = $params['subject'];
    $template = $params['template'] ?? 'default';
    $data = $params['data'] ?? [];
    
    // Generate email body from template
    $body = generateEmailFromTemplate($template, $data);
    
    sendEmail($to, $subject, $body);
}

/**
 * Execute notification action
 * @param array $params Notification parameters
 */
function executeNotificationAction($params) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("
            INSERT INTO notifications (user_id, title, message, type, created_at)
            VALUES (?, ?, ?, ?, NOW())
        ");
        
        $stmt->execute([
            $params['user_id'],
            $params['title'],
            $params['message'],
            $params['type'] ?? 'info'
        ]);
        
    } catch (Exception $e) {
        error_log("Execute notification action error: " . $e->getMessage());
    }
}

/**
 * Execute inventory action
 * @param array $params Inventory parameters
 */
function executeInventoryAction($params) {
    global $pdo;
    
    try {
        $productId = $params['product_id'];
        $action = $params['action']; // 'add', 'subtract', 'set'
        $quantity = $params['quantity'];
        
        switch ($action) {
            case 'add':
                $stmt = $pdo->prepare("
                    UPDATE inventory 
                    SET stok_masuk = stok_masuk + ? 
                    WHERE id = ?
                ");
                $stmt->execute([$quantity, $productId]);
                break;
                
            case 'subtract':
                $stmt = $pdo->prepare("
                    UPDATE inventory 
                    SET stok_keluar = stok_keluar + ? 
                    WHERE id = ?
                ");
                $stmt->execute([$quantity, $productId]);
                break;
                
            case 'set':
                $stmt = $pdo->prepare("
                    UPDATE inventory 
                    SET stok_awal = ? 
                    WHERE id = ?
                ");
                $stmt->execute([$quantity, $productId]);
                break;
        }
        
    } catch (Exception $e) {
        error_log("Execute inventory action error: " . $e->getMessage());
    }
}

/**
 * Execute backup action
 * @param array $params Backup parameters
 */
function executeBackupAction($params) {
    require_once 'backup_system.php';
    
    $type = $params['type'] ?? 'full';
    $filename = $params['filename'] ?? null;
    
    createDatabaseBackup($filename);
}

/**
 * Execute webhook action
 * @param array $params Webhook parameters
 */
function executeWebhookAction($params) {
    $url = $params['url'];
    $method = $params['method'] ?? 'POST';
    $data = $params['data'] ?? [];
    
    $context = stream_context_create([
        'http' => [
            'method' => $method,
            'header' => 'Content-Type: application/json',
            'content' => json_encode($data),
            'timeout' => 30
        ]
    ]);
    
    $result = file_get_contents($url, false, $context);
    
    if ($result === false) {
        error_log("Webhook call failed: $url");
    }
}

/**
 * Log workflow execution
 * @param int $ruleId Workflow rule ID
 * @param array $triggerData Trigger data
 */
function logWorkflowExecution($ruleId, $triggerData) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("
            INSERT INTO workflow_executions (
                rule_id, trigger_data, executed_at, status
            ) VALUES (?, ?, NOW(), 'success')
        ");
        
        $stmt->execute([$ruleId, json_encode($triggerData)]);
        
    } catch (Exception $e) {
        error_log("Log workflow execution error: " . $e->getMessage());
    }
}

/**
 * Create workflow tables
 */
function createWorkflowTables() {
    global $pdo;
    
    try {
        // Workflow rules table
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS workflow_rules (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                description TEXT,
                trigger_type VARCHAR(100) NOT NULL,
                trigger_conditions JSON,
                actions JSON NOT NULL,
                is_active BOOLEAN DEFAULT TRUE,
                created_by INT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE
            )
        ");
        
        // Workflow executions table
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS workflow_executions (
                id INT AUTO_INCREMENT PRIMARY KEY,
                rule_id INT NOT NULL,
                trigger_data JSON,
                executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                status ENUM('success', 'failed') DEFAULT 'success',
                error_message TEXT,
                FOREIGN KEY (rule_id) REFERENCES workflow_rules(id) ON DELETE CASCADE
            )
        ");
        
        return true;
        
    } catch (Exception $e) {
        error_log("Create workflow tables error: " . $e->getMessage());
        return false;
    }
}

/**
 * Generate email from template
 * @param string $template Template name
 * @param array $data Template data
 * @return string Email body
 */
function generateEmailFromTemplate($template, $data) {
    $templates = [
        'transaction_alert' => "
            <h2>Transaksi Baru</h2>
            <p>Transaksi baru telah dibuat:</p>
            <ul>
                <li>Jumlah: {{amount}}</li>
                <li>Jenis: {{type}}</li>
                <li>Kategori: {{category}}</li>
                <li>Tanggal: {{date}}</li>
            </ul>
        ",
        'target_achieved' => "
            <h2>🎉 Target Tercapai!</h2>
            <p>Selamat! Target keuangan Anda telah tercapai:</p>
            <ul>
                <li>Target: {{target_name}}</li>
                <li>Jumlah Target: {{target_amount}}</li>
                <li>Tercapai: {{achieved_amount}}</li>
            </ul>
        ",
        'low_stock_alert' => "
            <h2>⚠️ Peringatan Stok Rendah</h2>
            <p>Produk berikut memiliki stok rendah:</p>
            <ul>
                <li>Produk: {{product_name}}</li>
                <li>Stok Saat Ini: {{current_stock}}</li>
                <li>Minimum Stok: {{minimum_stock}}</li>
            </ul>
        "
    ];
    
    $body = $templates[$template] ?? $templates['transaction_alert'];
    
    // Replace placeholders
    foreach ($data as $key => $value) {
        $body = str_replace("{{$key}}", $value, $body);
    }
    
    return $body;
}
?>
