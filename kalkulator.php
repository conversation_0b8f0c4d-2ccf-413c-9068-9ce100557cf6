<?php
require_once 'includes/config/database.php';
require_once 'includes/helpers/functions.php';
require_once 'includes/helpers/notifications.php';

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isLoggedIn()) {
    setFlashMessage('danger', 'Silakan login terlebih dahulu');
    redirect('/keuangan/login.php');
}

$currentUser = getCurrentUser();
$currentPage = 'kalkulator';

// Include header and sidebar
require_once 'includes/views/layouts/header.php';
require_once 'includes/views/layouts/sidebar.php';
?>

<div class="modern-layout">
    <div class="modern-container">
        <!-- Modern Page Header -->
        <div class="modern-page-header modern-mb-xl">
            <h1 class="modern-page-title modern-mb-0">Kalkulator <PERSON>uangan</h1>
            <p class="modern-page-subtitle">Berbagai kalkulator canggih untuk membantu perencanaan keuangan Anda</p>
        </div>

        <div class="modern-grid modern-grid-cols-2 modern-gap-lg">
            <!-- Modern Basic Calculator -->
            <div class="modern-card">
                <div class="modern-card-header">
                    <h5 class="modern-card-title">
                        <i class="fas fa-calculator modern-text-primary modern-mr-sm"></i>
                        Kalkulator Dasar
                    </h5>
                </div>
                <div class="modern-card-body">
                    <div class="modern-calculator">
                        <div class="modern-calculator-display">
                            <input type="text" id="display" class="modern-calculator-screen" readonly value="0">
                        </div>
                        <div class="modern-calculator-buttons">
                            <div class="modern-calculator-row">
                                <button class="modern-calc-btn modern-calc-btn-clear" onclick="clearDisplay()">C</button>
                                <button class="modern-calc-btn modern-calc-btn-clear" onclick="deleteLast()">⌫</button>
                                <button class="modern-calc-btn modern-calc-btn-operator" onclick="appendToDisplay('/')">/</button>
                                <button class="modern-calc-btn modern-calc-btn-operator" onclick="appendToDisplay('*')">×</button>
                            </div>
                            <div class="modern-calculator-row">
                                <button class="modern-calc-btn modern-calc-btn-number" onclick="appendToDisplay('7')">7</button>
                                <button class="modern-calc-btn modern-calc-btn-number" onclick="appendToDisplay('8')">8</button>
                                <button class="modern-calc-btn modern-calc-btn-number" onclick="appendToDisplay('9')">9</button>
                                <button class="modern-calc-btn modern-calc-btn-operator" onclick="appendToDisplay('-')">-</button>
                            </div>
                            <div class="modern-calculator-row">
                                <button class="modern-calc-btn modern-calc-btn-number" onclick="appendToDisplay('4')">4</button>
                                <button class="modern-calc-btn modern-calc-btn-number" onclick="appendToDisplay('5')">5</button>
                                <button class="modern-calc-btn modern-calc-btn-number" onclick="appendToDisplay('6')">6</button>
                                <button class="modern-calc-btn modern-calc-btn-operator" onclick="appendToDisplay('+')">+</button>
                            </div>
                            <div class="modern-calculator-row">
                                <button class="modern-calc-btn modern-calc-btn-number" onclick="appendToDisplay('1')">1</button>
                                <button class="modern-calc-btn modern-calc-btn-number" onclick="appendToDisplay('2')">2</button>
                                <button class="modern-calc-btn modern-calc-btn-number" onclick="appendToDisplay('3')">3</button>
                                <button class="modern-calc-btn modern-calc-btn-equals modern-calc-btn-tall" onclick="calculate()">=</button>
                            </div>
                            <div class="modern-calculator-row">
                                <button class="modern-calc-btn modern-calc-btn-number modern-calc-btn-wide" onclick="appendToDisplay('0')">0</button>
                                <button class="modern-calc-btn modern-calc-btn-number" onclick="appendToDisplay('.')">.</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Modern Loan Calculator -->
            <div class="modern-card">
                <div class="modern-card-header">
                    <h5 class="modern-card-title">
                        <i class="fas fa-home modern-text-success modern-mr-sm"></i>
                        Kalkulator Kredit
                    </h5>
                </div>
                <div class="modern-card-body">
                    <form id="loanForm">
                        <div class="modern-form-group">
                            <label class="modern-form-label">
                                <i class="fas fa-money-bill modern-text-success"></i>
                                Jumlah Pinjaman
                            </label>
                            <div class="modern-input-group">
                                <span class="modern-input-group-text">Rp</span>
                                <input type="text" id="loanAmount" class="modern-form-control number-format" placeholder="100,000,000">
                            </div>
                        </div>

                        <div class="modern-form-group">
                            <label class="modern-form-label">
                                <i class="fas fa-percentage modern-text-success"></i>
                                Suku Bunga (% per tahun)
                            </label>
                            <input type="number" id="interestRate" class="modern-form-control" placeholder="12" step="0.1">
                        </div>

                        <div class="modern-form-group">
                            <label class="modern-form-label">
                                <i class="fas fa-calendar modern-text-success"></i>
                                Jangka Waktu (tahun)
                            </label>
                            <input type="number" id="loanTerm" class="modern-form-control" placeholder="10">
                        </div>

                        <div class="modern-form-group modern-mb-0">
                            <button type="button" class="modern-btn modern-btn-success modern-w-full" onclick="calculateLoan()">
                                <i class="fas fa-calculator"></i>
                                Hitung Cicilan
                            </button>
                        </div>
                    </form>

                    <div id="loanResult" class="modern-mt-lg" style="display: none;">
                        <div class="modern-result-card modern-result-success">
                            <div class="modern-result-header">
                                <i class="fas fa-check-circle"></i>
                                Hasil Perhitungan Kredit
                            </div>
                            <div class="modern-result-body">
                                <div class="modern-result-grid">
                                    <div class="modern-result-item">
                                        <div class="modern-result-label">Cicilan per Bulan</div>
                                        <div class="modern-result-value" id="monthlyPayment">-</div>
                                    </div>
                                    <div class="modern-result-item">
                                        <div class="modern-result-label">Total Pembayaran</div>
                                        <div class="modern-result-value" id="totalPayment">-</div>
                                    </div>
                                </div>
                                <div class="modern-result-item modern-result-highlight">
                                    <div class="modern-result-label">Total Bunga</div>
                                    <div class="modern-result-value" id="totalInterest">-</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Modern Investment Calculator -->
            <div class="modern-card">
                <div class="modern-card-header">
                    <h5 class="modern-card-title">
                        <i class="fas fa-chart-line modern-text-info modern-mr-sm"></i>
                        Kalkulator Investasi
                    </h5>
                </div>
                <div class="modern-card-body">
                    <form id="investmentForm">
                        <div class="modern-form-group">
                            <label class="modern-form-label">
                                <i class="fas fa-coins modern-text-info"></i>
                                Investasi Awal
                            </label>
                            <div class="modern-input-group">
                                <span class="modern-input-group-text">Rp</span>
                                <input type="text" id="initialInvestment" class="modern-form-control number-format" placeholder="10,000,000">
                            </div>
                        </div>

                        <div class="modern-form-group">
                            <label class="modern-form-label">
                                <i class="fas fa-calendar-plus modern-text-info"></i>
                                Investasi Bulanan
                            </label>
                            <div class="modern-input-group">
                                <span class="modern-input-group-text">Rp</span>
                                <input type="text" id="monthlyInvestment" class="modern-form-control number-format" placeholder="1,000,000">
                            </div>
                        </div>

                        <div class="modern-form-group">
                            <label class="modern-form-label">
                                <i class="fas fa-chart-line modern-text-info"></i>
                                Return Tahunan (%)
                            </label>
                            <input type="number" id="annualReturn" class="modern-form-control" placeholder="12" step="0.1">
                        </div>

                        <div class="modern-form-group">
                            <label class="modern-form-label">
                                <i class="fas fa-clock modern-text-info"></i>
                                Jangka Waktu (tahun)
                            </label>
                            <input type="number" id="investmentTerm" class="modern-form-control" placeholder="10">
                        </div>

                        <div class="modern-form-group modern-mb-0">
                            <button type="button" class="modern-btn modern-btn-info modern-w-full" onclick="calculateInvestment()">
                                <i class="fas fa-calculator"></i>
                                Hitung Investasi
                            </button>
                        </div>
                    </form>

                    <div id="investmentResult" class="modern-mt-lg" style="display: none;">
                        <div class="modern-result-card modern-result-info">
                            <div class="modern-result-header">
                                <i class="fas fa-chart-line"></i>
                                Hasil Perhitungan Investasi
                            </div>
                            <div class="modern-result-body">
                                <div class="modern-result-grid">
                                    <div class="modern-result-item">
                                        <div class="modern-result-label">Total Investasi</div>
                                        <div class="modern-result-value" id="totalInvested">-</div>
                                    </div>
                                    <div class="modern-result-item">
                                        <div class="modern-result-label">Nilai Akhir</div>
                                        <div class="modern-result-value" id="finalValue">-</div>
                                    </div>
                                </div>
                                <div class="modern-result-item modern-result-highlight">
                                    <div class="modern-result-label">Total Keuntungan</div>
                                    <div class="modern-result-value" id="totalProfit">-</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Modern Savings Calculator -->
            <div class="modern-card">
                <div class="modern-card-header">
                    <h5 class="modern-card-title">
                        <i class="fas fa-piggy-bank modern-text-warning modern-mr-sm"></i>
                        Kalkulator Tabungan
                    </h5>
                </div>
                <div class="modern-card-body">
                    <form id="savingsForm">
                        <div class="modern-form-group">
                            <label class="modern-form-label">
                                <i class="fas fa-bullseye modern-text-warning"></i>
                                Target Tabungan
                            </label>
                            <div class="modern-input-group">
                                <span class="modern-input-group-text">Rp</span>
                                <input type="text" id="savingsTarget" class="modern-form-control number-format" placeholder="100,000,000">
                            </div>
                        </div>

                        <div class="modern-form-group">
                            <label class="modern-form-label">
                                <i class="fas fa-wallet modern-text-warning"></i>
                                Tabungan Awal
                            </label>
                            <div class="modern-input-group">
                                <span class="modern-input-group-text">Rp</span>
                                <input type="text" id="initialSavings" class="modern-form-control number-format" placeholder="5,000,000">
                            </div>
                        </div>

                        <div class="modern-form-group">
                            <label class="modern-form-label">
                                <i class="fas fa-calendar-plus modern-text-warning"></i>
                                Tabungan per Bulan
                            </label>
                            <div class="modern-input-group">
                                <span class="modern-input-group-text">Rp</span>
                                <input type="text" id="monthlySavings" class="modern-form-control number-format" placeholder="2,000,000">
                            </div>
                        </div>

                        <div class="modern-form-group">
                            <label class="modern-form-label">
                                <i class="fas fa-percentage modern-text-warning"></i>
                                Bunga Tabungan (% per tahun)
                            </label>
                            <input type="number" id="savingsInterest" class="modern-form-control" placeholder="3" step="0.1">
                        </div>

                        <div class="modern-form-group modern-mb-0">
                            <button type="button" class="modern-btn modern-btn-warning modern-w-full" onclick="calculateSavings()">
                                <i class="fas fa-calculator"></i>
                                Hitung Waktu Target
                            </button>
                        </div>
                    </form>

                    <div id="savingsResult" class="modern-mt-lg" style="display: none;">
                        <div class="modern-result-card modern-result-warning">
                            <div class="modern-result-header">
                                <i class="fas fa-piggy-bank"></i>
                                Hasil Perhitungan Tabungan
                            </div>
                            <div class="modern-result-body">
                                <div class="modern-result-grid">
                                    <div class="modern-result-item">
                                        <div class="modern-result-label">Waktu Mencapai Target</div>
                                        <div class="modern-result-value" id="timeToTarget">-</div>
                                    </div>
                                    <div class="modern-result-item">
                                        <div class="modern-result-label">Total Bunga</div>
                                        <div class="modern-result-value" id="savingsInterestEarned">-</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Basic Calculator Functions
let display = document.getElementById('display');
let currentInput = '0';
let shouldResetDisplay = false;

function updateDisplay() {
    display.value = currentInput;
}

function clearDisplay() {
    currentInput = '0';
    updateDisplay();
}

function deleteLast() {
    if (currentInput.length > 1) {
        currentInput = currentInput.slice(0, -1);
    } else {
        currentInput = '0';
    }
    updateDisplay();
}

function appendToDisplay(value) {
    if (shouldResetDisplay) {
        currentInput = '0';
        shouldResetDisplay = false;
    }

    if (currentInput === '0' && value !== '.') {
        currentInput = value;
    } else {
        currentInput += value;
    }
    updateDisplay();
}

function calculate() {
    try {
        let result = eval(currentInput.replace(/×/g, '*'));
        currentInput = result.toString();
        shouldResetDisplay = true;
        updateDisplay();
    } catch (error) {
        currentInput = 'Error';
        shouldResetDisplay = true;
        updateDisplay();
    }
}

// Loan Calculator
function calculateLoan() {
    const loanAmount = parseFloat(document.getElementById('loanAmount').value.replace(/[^\d]/g, ''));
    const annualRate = parseFloat(document.getElementById('interestRate').value) / 100;
    const years = parseFloat(document.getElementById('loanTerm').value);

    if (!loanAmount || !annualRate || !years) {
        alert('Mohon isi semua field dengan benar');
        return;
    }

    const monthlyRate = annualRate / 12;
    const numPayments = years * 12;

    const monthlyPayment = loanAmount * (monthlyRate * Math.pow(1 + monthlyRate, numPayments)) /
                          (Math.pow(1 + monthlyRate, numPayments) - 1);

    const totalPayment = monthlyPayment * numPayments;
    const totalInterest = totalPayment - loanAmount;

    document.getElementById('monthlyPayment').textContent = formatRupiah(monthlyPayment);
    document.getElementById('totalPayment').textContent = formatRupiah(totalPayment);
    document.getElementById('totalInterest').textContent = formatRupiah(totalInterest);
    document.getElementById('loanResult').style.display = 'block';
}

// Investment Calculator
function calculateInvestment() {
    const initialInvestment = parseFloat(document.getElementById('initialInvestment').value.replace(/[^\d]/g, '')) || 0;
    const monthlyInvestment = parseFloat(document.getElementById('monthlyInvestment').value.replace(/[^\d]/g, '')) || 0;
    const annualReturn = parseFloat(document.getElementById('annualReturn').value) / 100;
    const years = parseFloat(document.getElementById('investmentTerm').value);

    if (!annualReturn || !years) {
        alert('Mohon isi semua field dengan benar');
        return;
    }

    const monthlyReturn = annualReturn / 12;
    const months = years * 12;

    // Future value of initial investment
    const futureValueInitial = initialInvestment * Math.pow(1 + monthlyReturn, months);

    // Future value of monthly investments (annuity)
    const futureValueMonthly = monthlyInvestment *
        ((Math.pow(1 + monthlyReturn, months) - 1) / monthlyReturn);

    const finalValue = futureValueInitial + futureValueMonthly;
    const totalInvested = initialInvestment + (monthlyInvestment * months);
    const totalProfit = finalValue - totalInvested;

    document.getElementById('totalInvested').textContent = formatRupiah(totalInvested);
    document.getElementById('finalValue').textContent = formatRupiah(finalValue);
    document.getElementById('totalProfit').textContent = formatRupiah(totalProfit);
    document.getElementById('investmentResult').style.display = 'block';
}

// Savings Calculator
function calculateSavings() {
    const target = parseFloat(document.getElementById('savingsTarget').value.replace(/[^\d]/g, ''));
    const initial = parseFloat(document.getElementById('initialSavings').value.replace(/[^\d]/g, '')) || 0;
    const monthly = parseFloat(document.getElementById('monthlySavings').value.replace(/[^\d]/g, ''));
    const annualInterest = parseFloat(document.getElementById('savingsInterest').value) / 100;

    if (!target || !monthly) {
        alert('Mohon isi target tabungan dan tabungan per bulan');
        return;
    }

    const monthlyInterest = annualInterest / 12;
    let currentAmount = initial;
    let months = 0;
    let totalInterest = 0;

    while (currentAmount < target && months < 1200) { // Max 100 years
        const interestEarned = currentAmount * monthlyInterest;
        totalInterest += interestEarned;
        currentAmount += monthly + interestEarned;
        months++;
    }

    const years = Math.floor(months / 12);
    const remainingMonths = months % 12;

    let timeText = '';
    if (years > 0) {
        timeText += years + ' tahun ';
    }
    if (remainingMonths > 0) {
        timeText += remainingMonths + ' bulan';
    }

    document.getElementById('timeToTarget').textContent = timeText || 'Target sudah tercapai';
    document.getElementById('savingsInterestEarned').textContent = formatRupiah(totalInterest);
    document.getElementById('savingsResult').style.display = 'block';
}

// Format number as Rupiah
function formatRupiah(amount) {
    return 'Rp ' + new Intl.NumberFormat('id-ID').format(Math.round(amount));
}

// Format number inputs
document.addEventListener('DOMContentLoaded', function() {
    const numberInputs = document.querySelectorAll('.number-format');

    numberInputs.forEach(function(input) {
        input.addEventListener('input', function(e) {
            let value = e.target.value.replace(/[^\d]/g, '');
            if (value) {
                e.target.value = new Intl.NumberFormat('id-ID').format(value);
            }
        });
    });
});
</script>

<?php include 'includes/views/layouts/footer.php'; ?>
