# 🚀 Setup Server KeuanganKu - PC Baru

## 📋 Daftar Kebutuhan Software

### 1. Web Server Stack
- **XAMPP** (Recommended) atau **WAMP** atau **MAMP**
- **PHP 8.1+** (sudah include di XAMPP)
- **MySQL 8.0+** (sudah include di XAMPP)
- **Apache 2.4+** (sudah include di XAMPP)

### 2. Code Editor
- **Visual Studio Code** (Recommended)
- **PHPStorm** (Alternative)
- **Sublime Text** (Alternative)

### 3. Database Management
- **phpMyAdmin** (sudah include di XAMPP)
- **HeidiSQL** (Optional, untuk management database yang lebih advanced)
- **MySQL Workbench** (Optional)

### 4. Version Control
- **Git for Windows**
- **GitHub Desktop** (Optional, untuk GUI)

### 5. Browser untuk Testing
- **Google Chrome** (dengan DevTools)
- **Mozilla Firefox** (untuk cross-browser testing)

### 6. Utilities
- **7-Zip** atau **WinRAR** (untuk extract files)
- **Notepad++** (untuk quick editing)

---

## 🔧 Langkah Instalasi

### Step 1: Download & Install XAMPP
1. Download XAMPP dari: https://www.apachefriends.org/
2. Pilih versi PHP 8.1 atau lebih baru
3. Install dengan setting default
4. Start Apache dan MySQL dari XAMPP Control Panel

### Step 2: Download & Install Visual Studio Code
1. Download dari: https://code.visualstudio.com/
2. Install dengan setting default
3. Install extensions yang direkomendasikan (lihat bagian Extensions)

### Step 3: Download & Install Git
1. Download dari: https://git-scm.com/
2. Install dengan setting default
3. Setup username dan email:
   ```bash
   git config --global user.name "Your Name"
   git config --global user.email "<EMAIL>"
   ```

---

## 📁 Struktur Folder yang Direkomendasikan

```
C:/
├── xampp/
│   ├── htdocs/
│   │   ├── keuanganku/          # Project utama
│   │   ├── backup/              # Folder backup
│   │   └── testing/             # Folder untuk testing
│   ├── mysql/data/              # Database files
│   └── php/                     # PHP configuration
├── Projects/                    # Folder development
│   ├── keuanganku-dev/         # Development version
│   └── backups/                # Project backups
└── Tools/                      # Development tools
    ├── scripts/                # Automation scripts
    └── configs/                # Configuration files
```

---

## 🗄️ Backup Data dari Server Lama

### Database Backup
1. **Export Database:**
   ```sql
   -- Via phpMyAdmin: Export > Custom > SQL
   -- Atau via command line:
   mysqldump -u root -p keuanganku_db > keuanganku_backup.sql
   ```

2. **Files yang perlu di-backup:**
   - Semua file project di `htdocs/keuanganku/`
   - File konfigurasi database (`config.php`, `database.php`)
   - Upload files (images, documents)
   - Log files (jika ada)

### Checklist Backup
- [ ] Database (.sql file)
- [ ] Project files (semua folder dan file)
- [ ] Configuration files
- [ ] Upload/media files
- [ ] Custom .htaccess files
- [ ] SSL certificates (jika ada)

---

## 🔄 Restore di PC Baru

### Step 1: Copy Project Files
1. Copy folder project ke `C:/xampp/htdocs/keuanganku/`
2. Set permissions jika diperlukan

### Step 2: Import Database
1. Buka phpMyAdmin (http://localhost/phpmyadmin)
2. Create database baru: `keuanganku_db`
3. Import file backup .sql
4. Verify data berhasil di-import

### Step 3: Update Configuration
1. Edit file `config.php` atau `database.php`
2. Update database connection:
   ```php
   $host = 'localhost';
   $username = 'root';
   $password = '';  // Kosong untuk XAMPP default
   $database = 'keuanganku_db';
   ```

### Step 4: Test Installation
1. Akses http://localhost/keuanganku/
2. Test login dan fungsi utama
3. Check error logs jika ada masalah

---

## ⚙️ Konfigurasi XAMPP

### PHP Configuration (php.ini)
```ini
; Increase memory limit
memory_limit = 512M

; Increase upload size
upload_max_filesize = 64M
post_max_size = 64M

; Enable extensions
extension=mysqli
extension=pdo_mysql
extension=mbstring
extension=openssl
extension=curl
extension=gd

; Set timezone
date.timezone = Asia/Jakarta

; Error reporting for development
display_errors = On
error_reporting = E_ALL
```

### MySQL Configuration (my.ini)
```ini
[mysql]
default-character-set = utf8mb4

[mysqld]
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci
max_allowed_packet = 64M
innodb_buffer_pool_size = 256M
```

---

## 🔌 VS Code Extensions yang Direkomendasikan

### Essential Extensions
1. **PHP Intelephense** - PHP language support
2. **PHP Debug** - Debugging support
3. **MySQL** - Database management
4. **GitLens** - Git integration
5. **Auto Rename Tag** - HTML/XML tag editing
6. **Bracket Pair Colorizer** - Code readability
7. **Live Server** - Local development server
8. **Thunder Client** - API testing

### Install Extensions
```bash
# Via VS Code Command Palette (Ctrl+Shift+P)
ext install bmewburn.vscode-intelephense-client
ext install xdebug.php-debug
ext install formulahendry.auto-rename-tag
ext install eamodio.gitlens
```

---

## 🛠️ Automation Scripts

### Backup Script (backup.bat)
```batch
@echo off
echo Creating backup...
set timestamp=%date:~-4,4%%date:~-10,2%%date:~-7,2%_%time:~0,2%%time:~3,2%%time:~6,2%
set timestamp=%timestamp: =0%

mkdir "C:\Projects\backups\%timestamp%"
xcopy "C:\xampp\htdocs\keuanganku" "C:\Projects\backups\%timestamp%\files" /E /I /H
mysqldump -u root keuanganku_db > "C:\Projects\backups\%timestamp%\database.sql"

echo Backup completed: %timestamp%
pause
```

### Start Services Script (start-dev.bat)
```batch
@echo off
echo Starting development environment...
cd /d C:\xampp
start xampp-control.exe
timeout /t 3
start http://localhost/keuanganku/
start code "C:\xampp\htdocs\keuanganku"
echo Development environment started!
```

---

## 🔍 Troubleshooting

### Common Issues

1. **Port 80 sudah digunakan:**
   - Stop IIS atau Skype
   - Atau ubah Apache port ke 8080

2. **MySQL tidak start:**
   - Check port 3306
   - Reset MySQL service

3. **Permission denied:**
   - Run XAMPP as Administrator
   - Check folder permissions

4. **Database connection error:**
   - Verify database credentials
   - Check MySQL service status

### Error Logs Location
- Apache: `C:\xampp\apache\logs\error.log`
- PHP: `C:\xampp\php\logs\php_error_log`
- MySQL: `C:\xampp\mysql\data\*.err`

---

## 📞 Quick Reference

### Important URLs
- **Project**: http://localhost/keuanganku/
- **phpMyAdmin**: http://localhost/phpmyadmin/
- **XAMPP Control**: C:\xampp\xampp-control.exe

### Default Credentials
- **MySQL Root**: username=`root`, password=`` (empty)
- **phpMyAdmin**: Same as MySQL

### Useful Commands
```bash
# Start XAMPP services
net start apache2.4
net start mysql

# Stop XAMPP services  
net stop apache2.4
net stop mysql

# Check PHP version
php -v

# Check MySQL status
mysql -u root -p -e "SELECT VERSION();"
```

---

## ✅ Post-Installation Checklist

- [ ] XAMPP installed and running
- [ ] VS Code installed with extensions
- [ ] Git configured
- [ ] Project files copied
- [ ] Database imported
- [ ] Configuration updated
- [ ] Website accessible
- [ ] All features working
- [ ] Backup script created
- [ ] Development environment ready

---

**🎉 Setup Complete! Your KeuanganKu server is ready on the new PC!**
