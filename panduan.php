<?php
require_once 'includes/config/database.php';
require_once 'includes/helpers/functions.php';
require_once 'includes/helpers/notifications.php';

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isLoggedIn()) {
    setFlashMessage('danger', 'Silakan login terlebih dahulu');
    redirect('/keuangan/login.php');
}

$currentUser = getCurrentUser();
$currentPage = 'panduan';

// Include header
include 'includes/views/layouts/header.php';
?>

<div class="container-fluid py-4">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-1">Panduan Penggunaan</h1>
            <p class="text-muted mb-0">Pelajari cara menggunakan KeuanganKu dengan efektif</p>
        </div>
    </div>

    <div class="row">
        <!-- Sidebar Navigation -->
        <div class="col-lg-3">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h6 class="mb-0"><i class="fas fa-list me-2"></i>Daftar Panduan</h6>
                </div>
                <div class="list-group list-group-flush">
                    <a href="#getting-started" class="list-group-item list-group-item-action">
                        <i class="fas fa-play-circle me-2"></i>Memulai
                    </a>
                    <a href="#dashboard" class="list-group-item list-group-item-action">
                        <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                    </a>
                    <a href="#transactions" class="list-group-item list-group-item-action">
                        <i class="fas fa-exchange-alt me-2"></i>Transaksi
                    </a>
                    <a href="#categories" class="list-group-item list-group-item-action">
                        <i class="fas fa-tags me-2"></i>Kategori
                    </a>
                    <a href="#budget" class="list-group-item list-group-item-action">
                        <i class="fas fa-chart-pie me-2"></i>Anggaran
                    </a>
                    <a href="#investment" class="list-group-item list-group-item-action">
                        <i class="fas fa-chart-line me-2"></i>Investasi
                    </a>
                    <a href="#reports" class="list-group-item list-group-item-action">
                        <i class="fas fa-chart-bar me-2"></i>Laporan
                    </a>
                    <a href="#tools" class="list-group-item list-group-item-action">
                        <i class="fas fa-tools me-2"></i>Tools
                    </a>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="col-lg-9">
            <!-- Getting Started -->
            <div id="getting-started" class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0"><i class="fas fa-play-circle me-2"></i>Memulai dengan KeuanganKu</h5>
                </div>
                <div class="card-body">
                    <p>Selamat datang di KeuanganKu! Berikut adalah langkah-langkah untuk memulai:</p>
                    <ol>
                        <li><strong>Lengkapi Profil:</strong> Pastikan informasi profil Anda sudah lengkap</li>
                        <li><strong>Buat Kategori:</strong> Tambahkan kategori pemasukan dan pengeluaran sesuai kebutuhan</li>
                        <li><strong>Catat Transaksi:</strong> Mulai mencatat semua transaksi keuangan Anda</li>
                        <li><strong>Buat Anggaran:</strong> Tentukan anggaran untuk setiap kategori pengeluaran</li>
                        <li><strong>Monitor Laporan:</strong> Pantau perkembangan keuangan melalui laporan</li>
                    </ol>
                    <div class="alert alert-info">
                        <i class="fas fa-lightbulb me-2"></i>
                        <strong>Tips:</strong> Konsistensi adalah kunci! Catat setiap transaksi secara rutin untuk mendapatkan gambaran keuangan yang akurat.
                    </div>
                </div>
            </div>

            <!-- Dashboard -->
            <div id="dashboard" class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-tachometer-alt me-2"></i>Dashboard</h5>
                </div>
                <div class="card-body">
                    <p>Dashboard adalah halaman utama yang menampilkan ringkasan keuangan Anda:</p>
                    <ul>
                        <li><strong>Kartu Statistik:</strong> Menampilkan total pemasukan, pengeluaran, dan saldo</li>
                        <li><strong>Grafik Tren:</strong> Visualisasi perkembangan keuangan dalam periode tertentu</li>
                        <li><strong>Transaksi Terbaru:</strong> Daftar transaksi yang baru saja dicatat</li>
                        <li><strong>Notifikasi:</strong> Peringatan anggaran dan pengingat penting</li>
                    </ul>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Perhatian:</strong> Data di dashboard diperbarui secara real-time setiap kali Anda menambah atau mengubah transaksi.
                    </div>
                </div>
            </div>

            <!-- Transactions -->
            <div id="transactions" class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0"><i class="fas fa-exchange-alt me-2"></i>Mengelola Transaksi</h5>
                </div>
                <div class="card-body">
                    <h6>Menambah Transaksi:</h6>
                    <ol>
                        <li>Klik tombol "Tambah Transaksi"</li>
                        <li>Pilih kategori yang sesuai</li>
                        <li>Pilih jenis transaksi (Pemasukan/Pengeluaran)</li>
                        <li>Masukkan jumlah dan tanggal</li>
                        <li>Tambahkan keterangan jika diperlukan</li>
                        <li>Klik "Simpan"</li>
                    </ol>
                    
                    <h6 class="mt-4">Filter dan Pencarian:</h6>
                    <ul>
                        <li>Gunakan filter tanggal untuk melihat transaksi dalam periode tertentu</li>
                        <li>Filter berdasarkan kategori atau jenis transaksi</li>
                        <li>Gunakan fitur pencarian untuk menemukan transaksi spesifik</li>
                    </ul>
                </div>
            </div>

            <!-- Categories -->
            <div id="categories" class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0"><i class="fas fa-tags me-2"></i>Kategori</h5>
                </div>
                <div class="card-body">
                    <p>Kategori membantu Anda mengorganisir transaksi dengan lebih baik:</p>
                    
                    <h6>Kategori Pemasukan (contoh):</h6>
                    <ul>
                        <li>Gaji</li>
                        <li>Bonus</li>
                        <li>Investasi</li>
                        <li>Freelance</li>
                    </ul>
                    
                    <h6>Kategori Pengeluaran (contoh):</h6>
                    <ul>
                        <li>Makanan & Minuman</li>
                        <li>Transportasi</li>
                        <li>Belanja</li>
                        <li>Tagihan</li>
                        <li>Hiburan</li>
                    </ul>
                    
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i>
                        <strong>Best Practice:</strong> Buat kategori yang spesifik namun tidak terlalu detail agar mudah digunakan.
                    </div>
                </div>
            </div>

            <!-- Budget -->
            <div id="budget" class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-danger text-white">
                    <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>Anggaran</h5>
                </div>
                <div class="card-body">
                    <p>Fitur anggaran membantu Anda mengontrol pengeluaran:</p>
                    
                    <h6>Membuat Anggaran:</h6>
                    <ol>
                        <li>Tentukan kategori pengeluaran</li>
                        <li>Set jumlah anggaran per periode (bulanan/tahunan)</li>
                        <li>Atur persentase peringatan (misal: 80%)</li>
                        <li>Monitor penggunaan anggaran secara berkala</li>
                    </ol>
                    
                    <h6>Tips Anggaran Efektif:</h6>
                    <ul>
                        <li>Gunakan aturan 50/30/20 (kebutuhan/keinginan/tabungan)</li>
                        <li>Review dan sesuaikan anggaran setiap bulan</li>
                        <li>Sisakan buffer untuk pengeluaran tak terduga</li>
                    </ul>
                </div>
            </div>

            <!-- Investment -->
            <div id="investment" class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>Investasi</h5>
                </div>
                <div class="card-body">
                    <p>Kelola portofolio investasi Anda dengan fitur ini:</p>
                    
                    <h6>Jenis Investasi yang Didukung:</h6>
                    <ul>
                        <li>Saham</li>
                        <li>Obligasi</li>
                        <li>Reksadana</li>
                        <li>Emas</li>
                        <li>Properti</li>
                        <li>Deposito</li>
                        <li>Cryptocurrency</li>
                    </ul>
                    
                    <h6>Fitur Utama:</h6>
                    <ul>
                        <li>Tracking nilai investasi real-time</li>
                        <li>Perhitungan profit/loss</li>
                        <li>Diversifikasi portofolio</li>
                        <li>Analisis performa investasi</li>
                    </ul>
                </div>
            </div>

            <!-- Reports -->
            <div id="reports" class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>Laporan</h5>
                </div>
                <div class="card-body">
                    <p>Analisis keuangan mendalam melalui berbagai laporan:</p>
                    
                    <h6>Jenis Laporan:</h6>
                    <ul>
                        <li><strong>Laporan Keuangan:</strong> Ringkasan pemasukan dan pengeluaran</li>
                        <li><strong>Laporan Anggaran:</strong> Perbandingan anggaran vs realisasi</li>
                        <li><strong>Laporan Investasi:</strong> Performa portofolio investasi</li>
                        <li><strong>Laporan Tren:</strong> Analisis tren keuangan</li>
                    </ul>
                    
                    <h6>Export Data:</h6>
                    <p>Semua laporan dapat diekspor dalam format PDF atau Excel untuk keperluan dokumentasi.</p>
                </div>
            </div>

            <!-- Tools -->
            <div id="tools" class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-secondary text-white">
                    <h5 class="mb-0"><i class="fas fa-tools me-2"></i>Tools Tambahan</h5>
                </div>
                <div class="card-body">
                    <p>KeuanganKu menyediakan berbagai tools untuk membantu perencanaan keuangan:</p>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="fas fa-calculator me-2"></i>Kalkulator</h6>
                            <ul>
                                <li>Kalkulator dasar</li>
                                <li>Kalkulator kredit</li>
                                <li>Kalkulator investasi</li>
                                <li>Kalkulator tabungan</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-exchange-alt me-2"></i>Konverter</h6>
                            <ul>
                                <li>Konverter mata uang</li>
                                <li>Update rate real-time</li>
                                <li>Riwayat konversi</li>
                            </ul>
                        </div>
                    </div>
                    
                    <h6 class="mt-3"><i class="fas fa-bell me-2"></i>Pengingat</h6>
                    <p>Set pengingat untuk tagihan, cicilan, atau target keuangan penting.</p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Smooth scrolling for navigation links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
            
            // Update active state
            document.querySelectorAll('.list-group-item').forEach(item => {
                item.classList.remove('active');
            });
            this.classList.add('active');
        }
    });
});

// Highlight current section on scroll
window.addEventListener('scroll', function() {
    const sections = document.querySelectorAll('[id]');
    const navLinks = document.querySelectorAll('.list-group-item');
    
    let current = '';
    sections.forEach(section => {
        const sectionTop = section.offsetTop;
        const sectionHeight = section.clientHeight;
        if (pageYOffset >= sectionTop - 200) {
            current = section.getAttribute('id');
        }
    });
    
    navLinks.forEach(link => {
        link.classList.remove('active');
        if (link.getAttribute('href') === '#' + current) {
            link.classList.add('active');
        }
    });
});
</script>

<?php include 'includes/views/layouts/footer.php'; ?>
