<?php
/**
 * Database Operations
 * 
 * This file contains functions for handling database operations.
 */

/**
 * Get database connection
 * 
 * @return PDO
 */
function get_db_connection() {
    static $db = null;
    
    if ($db === null) {
        try {
            $db = new PDO(
                "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
                DB_USER,
                DB_PASS,
                [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::ATTR_EMULATE_PREPARES => false
                ]
            );
        } catch (PDOException $e) {
            error_log("Database connection failed: " . $e->getMessage());
            die("Database connection failed. Please try again later.");
        }
    }
    
    return $db;
}

/**
 * Execute query
 * 
 * @param string $query SQL query
 * @param array $params Query parameters
 * @return PDOStatement
 */
function execute_query($query, $params = []) {
    $db = get_db_connection();
    $stmt = $db->prepare($query);
    $stmt->execute($params);
    return $stmt;
}

/**
 * Get single row
 * 
 * @param string $query SQL query
 * @param array $params Query parameters
 * @return array|false
 */
function get_row($query, $params = []) {
    $stmt = execute_query($query, $params);
    return $stmt->fetch();
}

/**
 * Get multiple rows
 * 
 * @param string $query SQL query
 * @param array $params Query parameters
 * @return array
 */
function get_rows($query, $params = []) {
    $stmt = execute_query($query, $params);
    return $stmt->fetchAll();
}

/**
 * Get single value
 * 
 * @param string $query SQL query
 * @param array $params Query parameters
 * @return mixed
 */
function get_value($query, $params = []) {
    $stmt = execute_query($query, $params);
    return $stmt->fetchColumn();
}

/**
 * Insert data
 * 
 * @param string $table Table name
 * @param array $data Data to insert
 * @return int|false
 */
function insert_data($table, $data) {
    $db = get_db_connection();
    
    $columns = implode(', ', array_keys($data));
    $values = implode(', ', array_fill(0, count($data), '?'));
    
    $query = "INSERT INTO $table ($columns) VALUES ($values)";
    
    try {
        $stmt = $db->prepare($query);
        $stmt->execute(array_values($data));
        return $db->lastInsertId();
    } catch (PDOException $e) {
        error_log("Insert failed: " . $e->getMessage());
        return false;
    }
}

/**
 * Update data
 * 
 * @param string $table Table name
 * @param array $data Data to update
 * @param string $where Where clause
 * @param array $params Where parameters
 * @return int|false
 */
function update_data($table, $data, $where, $params = []) {
    $db = get_db_connection();
    
    $set = implode(' = ?, ', array_keys($data)) . ' = ?';
    $query = "UPDATE $table SET $set WHERE $where";
    
    try {
        $stmt = $db->prepare($query);
        $stmt->execute(array_merge(array_values($data), $params));
        return $stmt->rowCount();
    } catch (PDOException $e) {
        error_log("Update failed: " . $e->getMessage());
        return false;
    }
}

/**
 * Delete data
 * 
 * @param string $table Table name
 * @param string $where Where clause
 * @param array $params Where parameters
 * @return int|false
 */
function delete_data($table, $where, $params = []) {
    $db = get_db_connection();
    
    $query = "DELETE FROM $table WHERE $where";
    
    try {
        $stmt = $db->prepare($query);
        $stmt->execute($params);
        return $stmt->rowCount();
    } catch (PDOException $e) {
        error_log("Delete failed: " . $e->getMessage());
        return false;
    }
}

/**
 * Begin transaction
 * 
 * @return bool
 */
function begin_transaction() {
    return get_db_connection()->beginTransaction();
}

/**
 * Commit transaction
 * 
 * @return bool
 */
function commit_transaction() {
    return get_db_connection()->commit();
}

/**
 * Rollback transaction
 * 
 * @return bool
 */
function rollback_transaction() {
    return get_db_connection()->rollBack();
}

/**
 * Check if table exists
 * 
 * @param string $table Table name
 * @return bool
 */
function table_exists($table) {
    $query = "SHOW TABLES LIKE ?";
    return get_value($query, [$table]) !== false;
}

/**
 * Check if column exists
 * 
 * @param string $table Table name
 * @param string $column Column name
 * @return bool
 */
function column_exists($table, $column) {
    $query = "SHOW COLUMNS FROM $table LIKE ?";
    return get_value($query, [$column]) !== false;
}

/**
 * Get table columns
 * 
 * @param string $table Table name
 * @return array
 */
function get_table_columns($table) {
    $query = "SHOW COLUMNS FROM $table";
    return get_rows($query);
}

/**
 * Get table indexes
 * 
 * @param string $table Table name
 * @return array
 */
function get_table_indexes($table) {
    $query = "SHOW INDEXES FROM $table";
    return get_rows($query);
}

/**
 * Get table foreign keys
 * 
 * @param string $table Table name
 * @return array
 */
function get_table_foreign_keys($table) {
    $query = "
        SELECT 
            COLUMN_NAME,
            REFERENCED_TABLE_NAME,
            REFERENCED_COLUMN_NAME
        FROM
            INFORMATION_SCHEMA.KEY_COLUMN_USAGE
        WHERE
            TABLE_SCHEMA = ?
            AND TABLE_NAME = ?
            AND REFERENCED_TABLE_NAME IS NOT NULL
    ";
    
    return get_rows($query, [DB_NAME, $table]);
}

/**
 * Get table structure
 * 
 * @param string $table Table name
 * @return array
 */
function get_table_structure($table) {
    return [
        'columns' => get_table_columns($table),
        'indexes' => get_table_indexes($table),
        'foreign_keys' => get_table_foreign_keys($table)
    ];
}

/**
 * Create table
 * 
 * @param string $table Table name
 * @param array $columns Column definitions
 * @param array $indexes Index definitions
 * @param array $foreign_keys Foreign key definitions
 * @return bool
 */
function create_table($table, $columns, $indexes = [], $foreign_keys = []) {
    $db = get_db_connection();
    
    $column_defs = [];
    
    foreach ($columns as $name => $def) {
        $column_defs[] = "$name $def";
    }
    
    foreach ($indexes as $name => $def) {
        $column_defs[] = "$def";
    }
    
    foreach ($foreign_keys as $name => $def) {
        $column_defs[] = "$def";
    }
    
    $query = "CREATE TABLE $table (" . implode(', ', $column_defs) . ")";
    
    try {
        $db->exec($query);
        return true;
    } catch (PDOException $e) {
        error_log("Create table failed: " . $e->getMessage());
        return false;
    }
}

/**
 * Drop table
 * 
 * @param string $table Table name
 * @return bool
 */
function drop_table($table) {
    $db = get_db_connection();
    
    $query = "DROP TABLE IF EXISTS $table";
    
    try {
        $db->exec($query);
        return true;
    } catch (PDOException $e) {
        error_log("Drop table failed: " . $e->getMessage());
        return false;
    }
}

/**
 * Truncate table
 * 
 * @param string $table Table name
 * @return bool
 */
function truncate_table($table) {
    $db = get_db_connection();
    
    $query = "TRUNCATE TABLE $table";
    
    try {
        $db->exec($query);
        return true;
    } catch (PDOException $e) {
        error_log("Truncate table failed: " . $e->getMessage());
        return false;
    }
}

/**
 * Add column
 * 
 * @param string $table Table name
 * @param string $column Column name
 * @param string $definition Column definition
 * @return bool
 */
function add_column($table, $column, $definition) {
    $db = get_db_connection();
    
    $query = "ALTER TABLE $table ADD COLUMN $column $definition";
    
    try {
        $db->exec($query);
        return true;
    } catch (PDOException $e) {
        error_log("Add column failed: " . $e->getMessage());
        return false;
    }
}

/**
 * Drop column
 * 
 * @param string $table Table name
 * @param string $column Column name
 * @return bool
 */
function drop_column($table, $column) {
    $db = get_db_connection();
    
    $query = "ALTER TABLE $table DROP COLUMN $column";
    
    try {
        $db->exec($query);
        return true;
    } catch (PDOException $e) {
        error_log("Drop column failed: " . $e->getMessage());
        return false;
    }
}

/**
 * Modify column
 * 
 * @param string $table Table name
 * @param string $column Column name
 * @param string $definition Column definition
 * @return bool
 */
function modify_column($table, $column, $definition) {
    $db = get_db_connection();
    
    $query = "ALTER TABLE $table MODIFY COLUMN $column $definition";
    
    try {
        $db->exec($query);
        return true;
    } catch (PDOException $e) {
        error_log("Modify column failed: " . $e->getMessage());
        return false;
    }
}

/**
 * Add index
 * 
 * @param string $table Table name
 * @param string $index Index name
 * @param array $columns Column names
 * @param string $type Index type
 * @return bool
 */
function add_index($table, $index, $columns, $type = 'INDEX') {
    $db = get_db_connection();
    
    $columns = implode(', ', $columns);
    $query = "ALTER TABLE $table ADD $type $index ($columns)";
    
    try {
        $db->exec($query);
        return true;
    } catch (PDOException $e) {
        error_log("Add index failed: " . $e->getMessage());
        return false;
    }
}

/**
 * Drop index
 * 
 * @param string $table Table name
 * @param string $index Index name
 * @return bool
 */
function drop_index($table, $index) {
    $db = get_db_connection();
    
    $query = "ALTER TABLE $table DROP INDEX $index";
    
    try {
        $db->exec($query);
        return true;
    } catch (PDOException $e) {
        error_log("Drop index failed: " . $e->getMessage());
        return false;
    }
}

/**
 * Add foreign key
 * 
 * @param string $table Table name
 * @param string $key Foreign key name
 * @param array $columns Column names
 * @param string $ref_table Referenced table
 * @param array $ref_columns Referenced columns
 * @param string $on_delete On delete action
 * @param string $on_update On update action
 * @return bool
 */
function add_foreign_key($table, $key, $columns, $ref_table, $ref_columns, $on_delete = 'RESTRICT', $on_update = 'RESTRICT') {
    $db = get_db_connection();
    
    $columns = implode(', ', $columns);
    $ref_columns = implode(', ', $ref_columns);
    
    $query = "ALTER TABLE $table ADD CONSTRAINT $key FOREIGN KEY ($columns) REFERENCES $ref_table ($ref_columns) ON DELETE $on_delete ON UPDATE $on_update";
    
    try {
        $db->exec($query);
        return true;
    } catch (PDOException $e) {
        error_log("Add foreign key failed: " . $e->getMessage());
        return false;
    }
}

/**
 * Drop foreign key
 * 
 * @param string $table Table name
 * @param string $key Foreign key name
 * @return bool
 */
function drop_foreign_key($table, $key) {
    $db = get_db_connection();
    
    $query = "ALTER TABLE $table DROP FOREIGN KEY $key";
    
    try {
        $db->exec($query);
        return true;
    } catch (PDOException $e) {
        error_log("Drop foreign key failed: " . $e->getMessage());
        return false;
    }
}

/**
 * Get last error
 * 
 * @return array
 */
function get_last_error() {
    $db = get_db_connection();
    return $db->errorInfo();
}

/**
 * Get last insert ID
 * 
 * @return string
 */
function get_last_insert_id() {
    return get_db_connection()->lastInsertId();
}

/**
 * Get affected rows
 * 
 * @return int
 */
function get_affected_rows() {
    return get_db_connection()->rowCount();
}

/**
 * Escape string
 * 
 * @param string $string String to escape
 * @return string
 */
function escape_string($string) {
    return get_db_connection()->quote($string);
}

/**
 * Quote identifier
 * 
 * @param string $identifier Identifier to quote
 * @return string
 */
function quote_identifier($identifier) {
    return '`' . str_replace('`', '``', $identifier) . '`';
}

/**
 * Build where clause
 * 
 * @param array $conditions Conditions
 * @param string $operator Operator
 * @return array
 */
function build_where_clause($conditions, $operator = 'AND') {
    $where = [];
    $params = [];
    
    foreach ($conditions as $key => $value) {
        if (is_array($value)) {
            $where[] = $key . ' ' . $value[0] . ' ?';
            $params[] = $value[1];
        } else {
            $where[] = $key . ' = ?';
            $params[] = $value;
        }
    }
    
    return [
        'where' => implode(" $operator ", $where),
        'params' => $params
    ];
}

/**
 * Build order clause
 * 
 * @param array $orders Orders
 * @return string
 */
function build_order_clause($orders) {
    $clause = [];
    
    foreach ($orders as $column => $direction) {
        $clause[] = quote_identifier($column) . ' ' . strtoupper($direction);
    }
    
    return implode(', ', $clause);
}

/**
 * Build limit clause
 * 
 * @param int $limit Limit
 * @param int $offset Offset
 * @return string
 */
function build_limit_clause($limit, $offset = 0) {
    return "LIMIT $offset, $limit";
}

/**
 * Build select query
 * 
 * @param string $table Table name
 * @param array $columns Columns to select
 * @param array $conditions Where conditions
 * @param array $orders Order by
 * @param int $limit Limit
 * @param int $offset Offset
 * @return array
 */
function build_select_query($table, $columns = ['*'], $conditions = [], $orders = [], $limit = null, $offset = 0) {
    $query = "SELECT " . implode(', ', $columns) . " FROM " . quote_identifier($table);
    $params = [];
    
    if (!empty($conditions)) {
        $where = build_where_clause($conditions);
        $query .= " WHERE " . $where['where'];
        $params = $where['params'];
    }
    
    if (!empty($orders)) {
        $query .= " ORDER BY " . build_order_clause($orders);
    }
    
    if ($limit !== null) {
        $query .= " " . build_limit_clause($limit, $offset);
    }
    
    return [
        'query' => $query,
        'params' => $params
    ];
}

/**
 * Build insert query
 * 
 * @param string $table Table name
 * @param array $data Data to insert
 * @return array
 */
function build_insert_query($table, $data) {
    $columns = array_keys($data);
    $values = array_fill(0, count($columns), '?');
    
    $query = "INSERT INTO " . quote_identifier($table) . " (" . implode(', ', $columns) . ") VALUES (" . implode(', ', $values) . ")";
    
    return [
        'query' => $query,
        'params' => array_values($data)
    ];
}

/**
 * Build update query
 * 
 * @param string $table Table name
 * @param array $data Data to update
 * @param array $conditions Where conditions
 * @return array
 */
function build_update_query($table, $data, $conditions) {
    $set = [];
    $params = [];
    
    foreach ($data as $column => $value) {
        $set[] = quote_identifier($column) . " = ?";
        $params[] = $value;
    }
    
    $query = "UPDATE " . quote_identifier($table) . " SET " . implode(', ', $set);
    
    if (!empty($conditions)) {
        $where = build_where_clause($conditions);
        $query .= " WHERE " . $where['where'];
        $params = array_merge($params, $where['params']);
    }
    
    return [
        'query' => $query,
        'params' => $params
    ];
}

/**
 * Build delete query
 * 
 * @param string $table Table name
 * @param array $conditions Where conditions
 * @return array
 */
function build_delete_query($table, $conditions) {
    $query = "DELETE FROM " . quote_identifier($table);
    $params = [];
    
    if (!empty($conditions)) {
        $where = build_where_clause($conditions);
        $query .= " WHERE " . $where['where'];
        $params = $where['params'];
    }
    
    return [
        'query' => $query,
        'params' => $params
    ];
} 