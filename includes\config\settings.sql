-- Create settings table
CREATE TABLE IF NOT EXISTS `settings` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `name` varchar(50) NOT NULL,
    `value` text,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create activity_logs table
CREATE TABLE IF NOT EXISTS `activity_logs` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `user_id` int(11) DEFAULT NULL,
    `activity` text NOT NULL,
    `ip_address` varchar(45) DEFAULT NULL,
    `user_agent` text,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_<PERSON>IMESTAMP,
    <PERSON><PERSON>AR<PERSON> (`id`),
    <PERSON><PERSON>Y `user_id` (`user_id`),
    CONSTRAINT `activity_logs_ibfk_1` <PERSON>OREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Insert default settings
INSERT INTO `settings` (`name`, `value`) VALUES
('site_name', '"KeuanganKu"'),
('site_description', '"Sistem Manajemen Keuangan"'),
('currency', '"IDR"'),
('date_format', '"d/m/Y"'),
('time_format', '"H:i:s"'),
('maintenance_mode', '0'),
('enable_registration', '1'),
('default_role', '"user"'),
('session_timeout', '30'),
('max_login_attempts', '5'),
('lockout_time', '15')
ON DUPLICATE KEY UPDATE `value` = VALUES(`value`); 