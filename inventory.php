<?php
require_once 'includes/config/database.php';
require_once 'includes/helpers/functions.php';
require_once 'includes/helpers/notifications.php';

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isLoggedIn()) {
    setFlashMessage('danger', 'Silakan login terlebih dahulu');
    redirect('/keuangan/login.php');
}

$currentUser = getCurrentUser();
$currentPage = 'inventory';

// Create inventory table if not exists
try {
    $pdo->exec("CREATE TABLE IF NOT EXISTS inventory (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        kode_barang VARCHAR(50) UNIQUE NOT NULL,
        nama_barang VARCHAR(255) NOT NULL,
        kategori_barang VARCHAR(100),
        satuan VARCHAR(50) DEFAULT 'pcs',
        stok_minimum INT DEFAULT 0,
        stok_saat_ini INT DEFAULT 0,
        harga_beli DECIMAL(15,2) DEFAULT 0,
        harga_jual DECIMAL(15,2) DEFAULT 0,
        lokasi_penyimpanan VARCHAR(255),
        supplier_id INT,
        tanggal_masuk DATE,
        tanggal_kadaluarsa DATE,
        status ENUM('aktif', 'nonaktif', 'habis') DEFAULT 'aktif',
        keterangan TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (supplier_id) REFERENCES supplier(id) ON DELETE SET NULL
    )");

    // Create stock movements table
    $pdo->exec("CREATE TABLE IF NOT EXISTS stock_movements (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        inventory_id INT NOT NULL,
        jenis_gerakan ENUM('masuk', 'keluar', 'adjustment') NOT NULL,
        jumlah INT NOT NULL,
        stok_sebelum INT NOT NULL,
        stok_sesudah INT NOT NULL,
        keterangan TEXT,
        referensi VARCHAR(100),
        tanggal_gerakan DATETIME DEFAULT CURRENT_TIMESTAMP,
        created_by INT,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (inventory_id) REFERENCES inventory(id) ON DELETE CASCADE,
        FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
    )");
} catch (PDOException $e) {
    error_log("Error creating inventory tables: " . $e->getMessage());
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        try {
            switch ($_POST['action']) {
                case 'add':
                    $errors = [];
                    
                    if (empty($_POST['kode_barang'])) {
                        $errors[] = 'Kode barang harus diisi';
                    }
                    
                    if (empty($_POST['nama_barang'])) {
                        $errors[] = 'Nama barang harus diisi';
                    }
                    
                    if (empty($errors)) {
                        // Check if kode_barang already exists
                        $stmt = $pdo->prepare("SELECT id FROM inventory WHERE kode_barang = ? AND user_id = ?");
                        $stmt->execute([$_POST['kode_barang'], $currentUser['id']]);
                        
                        if ($stmt->fetch()) {
                            $errors[] = 'Kode barang sudah digunakan';
                        } else {
                            $harga_beli = !empty($_POST['harga_beli']) ? str_replace(['.', ','], '', $_POST['harga_beli']) : 0;
                            $harga_jual = !empty($_POST['harga_jual']) ? str_replace(['.', ','], '', $_POST['harga_jual']) : 0;
                            
                            $stmt = $pdo->prepare("INSERT INTO inventory (user_id, kode_barang, nama_barang, kategori_barang, satuan, stok_minimum, stok_saat_ini, harga_beli, harga_jual, lokasi_penyimpanan, supplier_id, tanggal_masuk, tanggal_kadaluarsa, status, keterangan) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
                            
                            $result = $stmt->execute([
                                $currentUser['id'],
                                $_POST['kode_barang'],
                                $_POST['nama_barang'],
                                $_POST['kategori_barang'],
                                $_POST['satuan'],
                                $_POST['stok_minimum'] ?: 0,
                                $_POST['stok_saat_ini'] ?: 0,
                                $harga_beli,
                                $harga_jual,
                                $_POST['lokasi_penyimpanan'],
                                !empty($_POST['supplier_id']) ? $_POST['supplier_id'] : null,
                                $_POST['tanggal_masuk'] ?: null,
                                $_POST['tanggal_kadaluarsa'] ?: null,
                                $_POST['status'] ?? 'aktif',
                                $_POST['keterangan']
                            ]);
                            
                            if ($result) {
                                $inventoryId = $pdo->lastInsertId();
                                
                                // Record initial stock movement if stock > 0
                                if ($_POST['stok_saat_ini'] > 0) {
                                    $stmt = $pdo->prepare("INSERT INTO stock_movements (user_id, inventory_id, jenis_gerakan, jumlah, stok_sebelum, stok_sesudah, keterangan, created_by) VALUES (?, ?, 'masuk', ?, 0, ?, 'Stok awal', ?)");
                                    $stmt->execute([$currentUser['id'], $inventoryId, $_POST['stok_saat_ini'], $_POST['stok_saat_ini'], $currentUser['id']]);
                                }
                                
                                setFlashMessage('success', 'Item inventory berhasil ditambahkan');
                            } else {
                                setFlashMessage('danger', 'Gagal menambahkan item inventory');
                            }
                        }
                    }
                    
                    if (!empty($errors)) {
                        setFlashMessage('danger', implode('<br>', $errors));
                    }
                    break;

                case 'update':
                    if (!empty($_POST['id'])) {
                        $harga_beli = !empty($_POST['harga_beli']) ? str_replace(['.', ','], '', $_POST['harga_beli']) : 0;
                        $harga_jual = !empty($_POST['harga_jual']) ? str_replace(['.', ','], '', $_POST['harga_jual']) : 0;
                        
                        $stmt = $pdo->prepare("UPDATE inventory SET nama_barang = ?, kategori_barang = ?, satuan = ?, stok_minimum = ?, harga_beli = ?, harga_jual = ?, lokasi_penyimpanan = ?, supplier_id = ?, tanggal_masuk = ?, tanggal_kadaluarsa = ?, status = ?, keterangan = ? WHERE id = ? AND user_id = ?");
                        
                        $result = $stmt->execute([
                            $_POST['nama_barang'],
                            $_POST['kategori_barang'],
                            $_POST['satuan'],
                            $_POST['stok_minimum'] ?: 0,
                            $harga_beli,
                            $harga_jual,
                            $_POST['lokasi_penyimpanan'],
                            !empty($_POST['supplier_id']) ? $_POST['supplier_id'] : null,
                            $_POST['tanggal_masuk'] ?: null,
                            $_POST['tanggal_kadaluarsa'] ?: null,
                            $_POST['status'],
                            $_POST['keterangan'],
                            $_POST['id'],
                            $currentUser['id']
                        ]);
                        
                        if ($result) {
                            setFlashMessage('success', 'Item inventory berhasil diperbarui');
                        } else {
                            setFlashMessage('danger', 'Gagal memperbarui item inventory');
                        }
                    }
                    break;

                case 'delete':
                    if (!empty($_POST['id'])) {
                        $stmt = $pdo->prepare("DELETE FROM inventory WHERE id = ? AND user_id = ?");
                        $result = $stmt->execute([$_POST['id'], $currentUser['id']]);
                        
                        if ($result) {
                            setFlashMessage('success', 'Item inventory berhasil dihapus');
                        } else {
                            setFlashMessage('danger', 'Gagal menghapus item inventory');
                        }
                    }
                    break;

                case 'stock_adjustment':
                    if (!empty($_POST['inventory_id']) && isset($_POST['new_stock'])) {
                        // Get current stock
                        $stmt = $pdo->prepare("SELECT stok_saat_ini, nama_barang FROM inventory WHERE id = ? AND user_id = ?");
                        $stmt->execute([$_POST['inventory_id'], $currentUser['id']]);
                        $item = $stmt->fetch();
                        
                        if ($item) {
                            $oldStock = $item['stok_saat_ini'];
                            $newStock = (int)$_POST['new_stock'];
                            $difference = $newStock - $oldStock;
                            
                            // Update stock
                            $stmt = $pdo->prepare("UPDATE inventory SET stok_saat_ini = ? WHERE id = ? AND user_id = ?");
                            $result = $stmt->execute([$newStock, $_POST['inventory_id'], $currentUser['id']]);
                            
                            if ($result) {
                                // Record stock movement
                                $jenis = $difference > 0 ? 'masuk' : 'keluar';
                                $jumlah = abs($difference);
                                
                                $stmt = $pdo->prepare("INSERT INTO stock_movements (user_id, inventory_id, jenis_gerakan, jumlah, stok_sebelum, stok_sesudah, keterangan, created_by) VALUES (?, ?, 'adjustment', ?, ?, ?, ?, ?)");
                                $stmt->execute([
                                    $currentUser['id'],
                                    $_POST['inventory_id'],
                                    $jumlah,
                                    $oldStock,
                                    $newStock,
                                    $_POST['keterangan_adjustment'] ?: 'Penyesuaian stok',
                                    $currentUser['id']
                                ]);
                                
                                setFlashMessage('success', 'Stok berhasil disesuaikan');
                            } else {
                                setFlashMessage('danger', 'Gagal menyesuaikan stok');
                            }
                        }
                    }
                    break;
            }
        } catch (PDOException $e) {
            setFlashMessage('danger', 'Terjadi kesalahan. Silakan coba lagi.');
        }
        redirect('/keuangan/inventory.php');
    }
}

// Get inventory items with pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$perPage = 15;
$offset = ($page - 1) * $perPage;

$where = ["i.user_id = ?"];
$params = [$currentUser['id']];

if (!empty($_GET['status'])) {
    $where[] = "i.status = ?";
    $params[] = $_GET['status'];
}

if (!empty($_GET['kategori'])) {
    $where[] = "i.kategori_barang = ?";
    $params[] = $_GET['kategori'];
}

if (!empty($_GET['search'])) {
    $where[] = "(i.kode_barang LIKE ? OR i.nama_barang LIKE ?)";
    $searchTerm = '%' . $_GET['search'] . '%';
    $params[] = $searchTerm;
    $params[] = $searchTerm;
}

if (isset($_GET['low_stock']) && $_GET['low_stock'] == '1') {
    $where[] = "i.stok_saat_ini <= i.stok_minimum";
}

$whereClause = implode(" AND ", $where);

// Get total records
$stmt = $pdo->prepare("SELECT COUNT(*) as total FROM inventory i WHERE $whereClause");
$stmt->execute($params);
$totalRecords = $stmt->fetch()['total'];
$totalPages = ceil($totalRecords / $perPage);

// Get inventory items
$stmt = $pdo->prepare("
    SELECT 
        i.*,
        s.nama_supplier,
        CASE 
            WHEN i.stok_saat_ini <= 0 THEN 'habis'
            WHEN i.stok_saat_ini <= i.stok_minimum THEN 'rendah'
            ELSE 'normal'
        END as status_stok
    FROM inventory i
    LEFT JOIN supplier s ON i.supplier_id = s.id
    WHERE $whereClause
    ORDER BY i.nama_barang ASC
    LIMIT ? OFFSET ?
");

$paginationParams = $params;
$paginationParams[] = $perPage;
$paginationParams[] = $offset;

$stmt->execute($paginationParams);
$inventoryItems = $stmt->fetchAll();

// Get suppliers for dropdown
$stmt = $pdo->prepare("SELECT * FROM supplier WHERE user_id = ? ORDER BY nama_supplier ASC");
$stmt->execute([$currentUser['id']]);
$suppliers = $stmt->fetchAll();

// Get categories for filter
$stmt = $pdo->prepare("SELECT DISTINCT kategori_barang FROM inventory WHERE user_id = ? AND kategori_barang IS NOT NULL ORDER BY kategori_barang ASC");
$stmt->execute([$currentUser['id']]);
$categories = $stmt->fetchAll();

// Get statistics
$stmt = $pdo->prepare("
    SELECT 
        COUNT(*) as total_items,
        COUNT(CASE WHEN status = 'aktif' THEN 1 END) as aktif,
        COUNT(CASE WHEN stok_saat_ini <= 0 THEN 1 END) as habis,
        COUNT(CASE WHEN stok_saat_ini <= stok_minimum AND stok_saat_ini > 0 THEN 1 END) as rendah,
        SUM(stok_saat_ini * harga_beli) as nilai_inventory
    FROM inventory 
    WHERE user_id = ?
");
$stmt->execute([$currentUser['id']]);
$stats = $stmt->fetch();

// Include header and sidebar
require_once 'includes/views/layouts/header.php';
require_once 'includes/views/layouts/sidebar.php';
?>

<div class="modern-layout">
    <div class="modern-container">
        <!-- Modern Page Header -->
        <div class="modern-page-header modern-flex modern-justify-between modern-items-center modern-mb-xl">
            <div>
                <h1 class="modern-page-title modern-mb-0">Inventory Management</h1>
                <p class="modern-page-subtitle">Kelola stok barang dan inventory bisnis Anda dengan sistem modern</p>
            </div>
            <div class="modern-page-actions">
                <button type="button" class="modern-btn modern-btn-success" data-bs-toggle="modal" data-bs-target="#stockAdjustmentModal">
                    <i class="fas fa-adjust"></i>
                    Adjustment Stok
                </button>
                <button type="button" class="modern-btn modern-btn-primary" data-bs-toggle="modal" data-bs-target="#addInventoryModal">
                    <i class="fas fa-plus"></i>
                    Tambah Item
                </button>
            </div>
        </div>

        <!-- Modern Statistics Cards -->
        <div class="modern-grid modern-grid-cols-4 modern-gap-lg modern-mb-xl">
            <div class="modern-stats-card modern-stats-primary">
                <div class="modern-stats-content">
                    <div class="modern-stats-info">
                        <div class="modern-stats-label">Total Items</div>
                        <div class="modern-stats-value"><?= number_format($stats['total_items'] ?? 0) ?></div>
                        <div class="modern-stats-meta"><?= $stats['aktif'] ?? 0 ?> aktif</div>
                    </div>
                    <div class="modern-stats-icon">
                        <i class="fas fa-boxes"></i>
                    </div>
                </div>
                <div class="modern-stats-glow"></div>
            </div>

            <div class="modern-stats-card modern-stats-success">
                <div class="modern-stats-content">
                    <div class="modern-stats-info">
                        <div class="modern-stats-label">Nilai Inventory</div>
                        <div class="modern-stats-value"><?= formatRupiahShort($stats['nilai_inventory'] ?? 0) ?></div>
                        <div class="modern-stats-meta">Total nilai stok</div>
                    </div>
                    <div class="modern-stats-icon">
                        <i class="fas fa-money-bill-wave"></i>
                    </div>
                </div>
                <div class="modern-stats-glow"></div>
            </div>

            <div class="modern-stats-card modern-stats-warning">
                <div class="modern-stats-content">
                    <div class="modern-stats-info">
                        <div class="modern-stats-label">Stok Rendah</div>
                        <div class="modern-stats-value"><?= number_format($stats['rendah'] ?? 0) ?></div>
                        <div class="modern-stats-meta">⚠️ Perlu restock</div>
                    </div>
                    <div class="modern-stats-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                </div>
                <div class="modern-stats-glow"></div>
            </div>

            <div class="modern-stats-card modern-stats-danger">
                <div class="modern-stats-content">
                    <div class="modern-stats-info">
                        <div class="modern-stats-label">Stok Habis</div>
                        <div class="modern-stats-value"><?= number_format($stats['habis'] ?? 0) ?></div>
                        <div class="modern-stats-meta">❌ Out of stock</div>
                    </div>
                    <div class="modern-stats-icon">
                        <i class="fas fa-times-circle"></i>
                    </div>
                </div>
                <div class="modern-stats-glow"></div>
            </div>
        </div>

        <!-- Modern Quick Actions -->
        <div class="modern-card modern-mb-lg">
            <div class="modern-card-header">
                <h5 class="modern-card-title">
                    <i class="fas fa-bolt modern-text-primary modern-mr-sm"></i>
                    Quick Actions
                </h5>
            </div>
            <div class="modern-card-body">
                <div class="modern-grid modern-grid-cols-6 modern-gap-sm">
                    <a href="?low_stock=1" class="modern-btn modern-btn-warning modern-btn-sm">
                        <i class="fas fa-exclamation-triangle"></i>
                        Stok Rendah
                    </a>
                    <a href="?status=habis" class="modern-btn modern-btn-danger modern-btn-sm">
                        <i class="fas fa-times-circle"></i>
                        Stok Habis
                    </a>
                    <button type="button" class="modern-btn modern-btn-info modern-btn-sm" onclick="exportInventory()">
                        <i class="fas fa-download"></i>
                        Export
                    </button>
                    <button type="button" class="modern-btn modern-btn-secondary modern-btn-sm" onclick="printBarcode()">
                        <i class="fas fa-barcode"></i>
                        Barcode
                    </button>
                    <a href="/keuangan/inventory.php" class="modern-btn modern-btn-primary modern-btn-sm">
                        <i class="fas fa-sync"></i>
                        Refresh
                    </a>
                    <button type="button" class="modern-btn modern-btn-success modern-btn-sm" data-bs-toggle="modal" data-bs-target="#bulkImportModal">
                        <i class="fas fa-upload"></i>
                        Import
                    </button>
                </div>
            </div>
        </div>

        <!-- Modern Filter Section -->
        <div class="modern-card modern-mb-lg">
            <div class="modern-card-header">
                <h5 class="modern-card-title">
                    <i class="fas fa-filter modern-text-primary modern-mr-sm"></i>
                    Filter & Pencarian
                </h5>
            </div>
            <div class="modern-card-body">
                <form action="" method="GET" class="modern-grid modern-grid-cols-4 modern-gap-md modern-items-end">
                    <div class="modern-form-group">
                        <label class="modern-form-label">
                            <i class="fas fa-search modern-text-primary"></i>
                            Pencarian
                        </label>
                        <input type="text" name="search" class="modern-form-control" placeholder="Kode atau nama barang..." value="<?= $_GET['search'] ?? '' ?>">
                    </div>

                    <div class="modern-form-group">
                        <label class="modern-form-label">
                            <i class="fas fa-tags modern-text-primary"></i>
                            Kategori
                        </label>
                        <select name="kategori" class="modern-form-control">
                            <option value="">🔍 Semua Kategori</option>
                            <?php foreach ($categories as $category): ?>
                            <option value="<?= $category['kategori_barang'] ?>" <?= (isset($_GET['kategori']) && $_GET['kategori'] == $category['kategori_barang']) ? 'selected' : '' ?>>
                                📦 <?= htmlspecialchars($category['kategori_barang']) ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="modern-form-group">
                        <label class="modern-form-label">
                            <i class="fas fa-toggle-on modern-text-primary"></i>
                            Status
                        </label>
                        <select name="status" class="modern-form-control">
                            <option value="">🔍 Semua Status</option>
                            <option value="aktif" <?= (isset($_GET['status']) && $_GET['status'] == 'aktif') ? 'selected' : '' ?>>✅ Aktif</option>
                            <option value="nonaktif" <?= (isset($_GET['status']) && $_GET['status'] == 'nonaktif') ? 'selected' : '' ?>>❌ Non-Aktif</option>
                            <option value="habis" <?= (isset($_GET['status']) && $_GET['status'] == 'habis') ? 'selected' : '' ?>>🚫 Habis</option>
                        </select>
                    </div>

                    <div class="modern-form-group modern-flex modern-gap-sm modern-mb-0">
                        <button type="submit" class="modern-btn modern-btn-primary modern-flex-1">
                            <i class="fas fa-search"></i>
                            Cari
                        </button>
                        <a href="/keuangan/inventory.php" class="modern-btn modern-btn-light">
                            <i class="fas fa-times"></i>
                            Reset
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- Modern Inventory Table -->
        <div class="modern-card">
            <div class="modern-card-header">
                <h5 class="modern-card-title">
                    <i class="fas fa-list modern-text-primary modern-mr-sm"></i>
                    Daftar Inventory
                </h5>
                <div class="modern-card-actions">
                    <span class="modern-badge modern-badge-info">
                        Total: <?= count($inventoryItems) ?> items
                    </span>
                    <div class="modern-view-toggle">
                        <button type="button" class="modern-btn modern-btn-light modern-btn-sm active" onclick="toggleView('table')">
                            <i class="fas fa-table"></i>
                        </button>
                        <button type="button" class="modern-btn modern-btn-light modern-btn-sm" onclick="toggleView('grid')">
                            <i class="fas fa-th"></i>
                        </button>
                    </div>
                </div>
            </div>
            <div class="modern-card-body modern-p-0">
                <div id="tableView">
                    <div class="modern-table-responsive">
                        <table class="modern-table">
                            <thead class="modern-table-header">
                                <tr>
                                    <th class="modern-table-th">
                                        <i class="fas fa-box modern-mr-xs"></i>
                                        Kode & Nama
                                    </th>
                                    <th class="modern-table-th">
                                        <i class="fas fa-tags modern-mr-xs"></i>
                                        Kategori
                                    </th>
                                    <th class="modern-table-th modern-text-center">
                                        <i class="fas fa-cubes modern-mr-xs"></i>
                                        Stok
                                    </th>
                                    <th class="modern-table-th modern-text-end">
                                        <i class="fas fa-money-bill modern-mr-xs"></i>
                                        Harga
                                    </th>
                                    <th class="modern-table-th">
                                        <i class="fas fa-truck modern-mr-xs"></i>
                                        Supplier
                                    </th>
                                    <th class="modern-table-th modern-text-center">
                                        <i class="fas fa-toggle-on modern-mr-xs"></i>
                                        Status
                                    </th>
                                    <th class="modern-table-th modern-text-center">
                                        <i class="fas fa-cogs modern-mr-xs"></i>
                                        Aksi
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="modern-table-body">
                                <?php if (empty($inventoryItems)): ?>
                                <tr>
                                    <td colspan="7" class="modern-table-td">
                                        <div class="modern-empty-state">
                                            <div class="modern-empty-icon">
                                                <i class="fas fa-boxes"></i>
                                            </div>
                                            <div class="modern-empty-content">
                                                <h6 class="modern-empty-title">Belum Ada Item Inventory</h6>
                                                <p class="modern-empty-text">Mulai kelola inventory dengan menambahkan item pertama</p>
                                                <button type="button" class="modern-btn modern-btn-primary modern-btn-sm" data-bs-toggle="modal" data-bs-target="#addInventoryModal">
                                                    <i class="fas fa-plus"></i>
                                                    Tambah Item Pertama
                                                </button>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <?php else: ?>
                                <?php foreach ($inventoryItems as $item): ?>
                                <tr class="modern-table-row modern-inventory-row-<?= $item['status_stok'] ?>">
                                    <td class="modern-table-td">
                                        <div class="modern-inventory-item">
                                            <div class="modern-inventory-icon">
                                                <i class="fas fa-box"></i>
                                            </div>
                                            <div class="modern-inventory-info">
                                                <div class="modern-inventory-name"><?= htmlspecialchars($item['nama_barang']) ?></div>
                                                <div class="modern-inventory-code"><?= htmlspecialchars($item['kode_barang']) ?></div>
                                                <?php if ($item['lokasi_penyimpanan']): ?>
                                                    <div class="modern-inventory-location">
                                                        <i class="fas fa-map-marker-alt"></i>
                                                        <?= htmlspecialchars($item['lokasi_penyimpanan']) ?>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="modern-table-td">
                                        <?php if ($item['kategori_barang']): ?>
                                            <div class="modern-badge modern-badge-secondary">
                                                <?= htmlspecialchars($item['kategori_barang']) ?>
                                            </div>
                                        <?php else: ?>
                                            <span class="modern-text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="modern-table-td modern-text-center">
                                        <div class="modern-stock-info">
                                            <div class="modern-stock-current modern-stock-<?= $item['status_stok'] ?>">
                                                <?= number_format($item['stok_saat_ini']) ?>
                                            </div>
                                            <div class="modern-stock-unit"><?= $item['satuan'] ?></div>
                                            <?php if ($item['stok_minimum'] > 0): ?>
                                                <div class="modern-stock-min">Min: <?= number_format($item['stok_minimum']) ?></div>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td class="modern-table-td modern-text-end">
                                        <div class="modern-price-info">
                                            <div class="modern-price-buy">Beli: <?= formatRupiah($item['harga_beli']) ?></div>
                                            <div class="modern-price-sell">Jual: <?= formatRupiah($item['harga_jual']) ?></div>
                                        </div>
                                    </td>
                                    <td class="modern-table-td">
                                        <?php if ($item['nama_supplier']): ?>
                                            <div class="modern-badge modern-badge-info">
                                                <?= htmlspecialchars($item['nama_supplier']) ?>
                                            </div>
                                        <?php else: ?>
                                            <span class="modern-text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="modern-table-td modern-text-center">
                                        <div class="modern-status-group">
                                            <div class="modern-badge modern-badge-<?= $item['status'] === 'aktif' ? 'success' : 'secondary' ?>">
                                                <?php if ($item['status'] === 'aktif'): ?>
                                                    <i class="fas fa-check"></i>
                                                <?php else: ?>
                                                    <i class="fas fa-times"></i>
                                                <?php endif; ?>
                                                <?= ucfirst($item['status']) ?>
                                            </div>
                                            <?php if ($item['status_stok'] === 'habis'): ?>
                                                <div class="modern-badge modern-badge-danger modern-mt-xs">
                                                    <i class="fas fa-exclamation-triangle"></i>
                                                    Habis
                                                </div>
                                            <?php elseif ($item['status_stok'] === 'rendah'): ?>
                                                <div class="modern-badge modern-badge-warning modern-mt-xs">
                                                    <i class="fas fa-exclamation-triangle"></i>
                                                    Rendah
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td class="modern-table-td modern-text-center">
                                        <div class="modern-action-buttons">
                                            <button type="button" class="modern-btn modern-btn-light modern-btn-sm"
                                                    onclick="editInventory(<?= htmlspecialchars(json_encode($item)) ?>)" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button type="button" class="modern-btn modern-btn-success modern-btn-sm"
                                                    onclick="adjustStock(<?= $item['id'] ?>, '<?= htmlspecialchars($item['nama_barang']) ?>', <?= $item['stok_saat_ini'] ?>)" title="Adjust Stok">
                                                <i class="fas fa-adjust"></i>
                                            </button>
                                            <button type="button" class="modern-btn modern-btn-info modern-btn-sm"
                                                    onclick="viewHistory(<?= $item['id'] ?>)" title="Riwayat">
                                                <i class="fas fa-history"></i>
                                            </button>
                                            <button type="button" class="modern-btn modern-btn-danger modern-btn-sm"
                                                    onclick="deleteInventory(<?= $item['id'] ?>)" title="Hapus">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>

            <!-- Grid View (Hidden by default) -->
            <div id="gridView" style="display: none;">
                <div class="row g-3 p-3">
                    <?php foreach ($inventoryItems as $item): ?>
                    <div class="col-lg-3 col-md-4 col-sm-6">
                        <div class="card h-100 border-0 shadow-sm">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <span class="badge bg-<?= $item['status'] === 'aktif' ? 'success' : 'secondary' ?>"><?= ucfirst($item['status']) ?></span>
                                    <span class="badge bg-<?= $item['status_stok'] === 'habis' ? 'danger' : ($item['status_stok'] === 'rendah' ? 'warning' : 'success') ?>">
                                        <?= $item['status_stok'] === 'habis' ? 'Habis' : ($item['status_stok'] === 'rendah' ? 'Rendah' : 'Normal') ?>
                                    </span>
                                </div>
                                <h6 class="card-title"><?= htmlspecialchars($item['nama_barang']) ?></h6>
                                <p class="card-text">
                                    <small class="text-muted"><?= htmlspecialchars($item['kode_barang']) ?></small><br>
                                    <span class="fw-bold">Stok: <?= number_format($item['stok_saat_ini']) ?> <?= $item['satuan'] ?></span><br>
                                    <small>Harga Jual: <?= formatRupiah($item['harga_jual']) ?></small>
                                </p>
                                <div class="btn-group w-100">
                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="editInventory(<?= htmlspecialchars(json_encode($item)) ?>)">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-success" onclick="adjustStock(<?= $item['id'] ?>, '<?= htmlspecialchars($item['nama_barang']) ?>', <?= $item['stok_saat_ini'] ?>)">
                                        <i class="fas fa-adjust"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>

            <!-- Pagination -->
            <?php if ($totalPages > 1): ?>
            <div class="border-top p-3">
                <nav>
                    <ul class="pagination justify-content-center mb-0">
                        <?php if ($page > 1): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?= $page - 1 ?><?= http_build_query(array_filter($_GET)) ?>">
                                <i class="fas fa-chevron-left"></i>
                            </a>
                        </li>
                        <?php endif; ?>

                        <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                        <li class="page-item <?= $i === $page ? 'active' : '' ?>">
                            <a class="page-link" href="?page=<?= $i ?><?= http_build_query(array_filter($_GET)) ?>"><?= $i ?></a>
                        </li>
                        <?php endfor; ?>

                        <?php if ($page < $totalPages): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?= $page + 1 ?><?= http_build_query(array_filter($_GET)) ?>">
                                <i class="fas fa-chevron-right"></i>
                            </a>
                        </li>
                        <?php endif; ?>
                    </ul>
                </nav>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Modern Add Inventory Modal -->
<div class="modal fade" id="addInventoryModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modern-modal-content">
            <div class="modern-modal-header">
                <h5 class="modern-modal-title">
                    <i class="fas fa-plus modern-text-primary modern-mr-sm"></i>
                    Tambah Item Inventory
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="" method="POST" class="needs-validation" novalidate>
                <div class="modern-modal-body">
                    <input type="hidden" name="action" value="add">

                    <div class="modern-grid modern-grid-cols-3 modern-gap-md">
                        <div class="modern-form-group">
                            <label class="modern-form-label">
                                <i class="fas fa-barcode modern-text-primary"></i>
                                Kode Barang <span class="modern-text-danger">*</span>
                            </label>
                            <input type="text" name="kode_barang" class="modern-form-control" required placeholder="Contoh: BRG001">
                            <div class="invalid-feedback">Kode barang harus diisi</div>
                        </div>
                        <div class="modern-form-group" style="grid-column: span 2;">
                            <label class="modern-form-label">
                                <i class="fas fa-box modern-text-primary"></i>
                                Nama Barang <span class="modern-text-danger">*</span>
                            </label>
                            <input type="text" name="nama_barang" class="modern-form-control" required placeholder="Nama lengkap barang">
                            <div class="invalid-feedback">Nama barang harus diisi</div>
                        </div>
                    </div>

                    <div class="modern-grid modern-grid-cols-3 modern-gap-md">
                        <div class="modern-form-group">
                            <label class="modern-form-label">
                                <i class="fas fa-tags modern-text-primary"></i>
                                Kategori
                            </label>
                            <input type="text" name="kategori_barang" class="modern-form-control" placeholder="Kategori barang">
                        </div>
                        <div class="modern-form-group">
                            <label class="modern-form-label">
                                <i class="fas fa-balance-scale modern-text-primary"></i>
                                Satuan
                            </label>
                            <select name="satuan" class="modern-form-control">
                                <option value="pcs">📦 Pcs</option>
                                <option value="kg">⚖️ Kg</option>
                                <option value="liter">🥤 Liter</option>
                                <option value="meter">📏 Meter</option>
                                <option value="box">📦 Box</option>
                                <option value="pack">📦 Pack</option>
                                <option value="unit">🔢 Unit</option>
                            </select>
                        </div>
                        <div class="modern-form-group">
                            <label class="modern-form-label">
                                <i class="fas fa-truck modern-text-primary"></i>
                                Supplier
                            </label>
                            <select name="supplier_id" class="modern-form-control">
                                <option value="">Pilih Supplier</option>
                                <?php foreach ($suppliers as $supplier): ?>
                                <option value="<?= $supplier['id'] ?>">🏢 <?= htmlspecialchars($supplier['nama_supplier']) ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label class="form-label">Stok Saat Ini</label>
                                <input type="number" name="stok_saat_ini" class="form-control" min="0" value="0">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label class="form-label">Stok Minimum</label>
                                <input type="number" name="stok_minimum" class="form-control" min="0" value="0">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label class="form-label">Harga Beli</label>
                                <div class="input-group">
                                    <span class="input-group-text">Rp</span>
                                    <input type="text" name="harga_beli" class="form-control number-format" placeholder="0">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label class="form-label">Harga Jual</label>
                                <div class="input-group">
                                    <span class="input-group-text">Rp</span>
                                    <input type="text" name="harga_jual" class="form-control number-format" placeholder="0">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Lokasi Penyimpanan</label>
                                <input type="text" name="lokasi_penyimpanan" class="form-control" placeholder="Rak A1, Gudang B, dll">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Tanggal Masuk</label>
                                <input type="date" name="tanggal_masuk" class="form-control" value="<?= date('Y-m-d') ?>">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Tanggal Kadaluarsa</label>
                                <input type="date" name="tanggal_kadaluarsa" class="form-control">
                                <small class="text-muted">Opsional untuk barang yang tidak kadaluarsa</small>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Status</label>
                                <select name="status" class="form-select">
                                    <option value="aktif">Aktif</option>
                                    <option value="nonaktif">Non-Aktif</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Keterangan</label>
                        <textarea name="keterangan" class="form-control" rows="2" placeholder="Keterangan tambahan (opsional)"></textarea>
                    </div>
                </div>
                <div class="modal-footer border-0">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">Simpan Item</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Inventory Modal -->
<div class="modal fade" id="editInventoryModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header border-0">
                <h5 class="modal-title">Edit Item Inventory</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="" method="POST" class="needs-validation" novalidate>
                <div class="modal-body">
                    <input type="hidden" name="action" value="update">
                    <input type="hidden" name="id" id="edit_id">

                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Kode Barang</label>
                                <input type="text" id="edit_kode_barang" class="form-control" readonly>
                                <small class="text-muted">Kode barang tidak dapat diubah</small>
                            </div>
                        </div>
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label class="form-label">Nama Barang</label>
                                <input type="text" name="nama_barang" id="edit_nama_barang" class="form-control" required>
                                <div class="invalid-feedback">Nama barang harus diisi</div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Kategori</label>
                                <input type="text" name="kategori_barang" id="edit_kategori_barang" class="form-control">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Satuan</label>
                                <select name="satuan" id="edit_satuan" class="form-select">
                                    <option value="pcs">Pcs</option>
                                    <option value="kg">Kg</option>
                                    <option value="liter">Liter</option>
                                    <option value="meter">Meter</option>
                                    <option value="box">Box</option>
                                    <option value="pack">Pack</option>
                                    <option value="unit">Unit</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Supplier</label>
                                <select name="supplier_id" id="edit_supplier_id" class="form-select">
                                    <option value="">Pilih Supplier</option>
                                    <?php foreach ($suppliers as $supplier): ?>
                                    <option value="<?= $supplier['id'] ?>"><?= htmlspecialchars($supplier['nama_supplier']) ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label class="form-label">Stok Minimum</label>
                                <input type="number" name="stok_minimum" id="edit_stok_minimum" class="form-control" min="0">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label class="form-label">Harga Beli</label>
                                <div class="input-group">
                                    <span class="input-group-text">Rp</span>
                                    <input type="text" name="harga_beli" id="edit_harga_beli" class="form-control number-format">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label class="form-label">Harga Jual</label>
                                <div class="input-group">
                                    <span class="input-group-text">Rp</span>
                                    <input type="text" name="harga_jual" id="edit_harga_jual" class="form-control number-format">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label class="form-label">Status</label>
                                <select name="status" id="edit_status" class="form-select">
                                    <option value="aktif">Aktif</option>
                                    <option value="nonaktif">Non-Aktif</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Lokasi Penyimpanan</label>
                                <input type="text" name="lokasi_penyimpanan" id="edit_lokasi_penyimpanan" class="form-control">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Tanggal Masuk</label>
                                <input type="date" name="tanggal_masuk" id="edit_tanggal_masuk" class="form-control">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Tanggal Kadaluarsa</label>
                                <input type="date" name="tanggal_kadaluarsa" id="edit_tanggal_kadaluarsa" class="form-control">
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Keterangan</label>
                        <textarea name="keterangan" id="edit_keterangan" class="form-control" rows="2"></textarea>
                    </div>
                </div>
                <div class="modal-footer border-0">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">Update Item</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Stock Adjustment Modal -->
<div class="modal fade" id="stockAdjustmentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header border-0">
                <h5 class="modal-title">Adjustment Stok</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="" method="POST" class="needs-validation" novalidate>
                <div class="modal-body">
                    <input type="hidden" name="action" value="stock_adjustment">
                    <input type="hidden" name="inventory_id" id="adjust_inventory_id">

                    <div class="mb-3">
                        <label class="form-label">Item</label>
                        <input type="text" id="adjust_item_name" class="form-control" readonly>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Stok Saat Ini</label>
                                <input type="text" id="adjust_current_stock" class="form-control" readonly>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Stok Baru</label>
                                <input type="number" name="new_stock" id="adjust_new_stock" class="form-control" required min="0">
                                <div class="invalid-feedback">Stok baru harus diisi</div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Keterangan</label>
                        <textarea name="keterangan_adjustment" class="form-control" rows="2" placeholder="Alasan penyesuaian stok"></textarea>
                    </div>

                    <div class="alert alert-info">
                        <small><i class="fas fa-info-circle me-1"></i>Perubahan stok akan dicatat dalam riwayat stock movement</small>
                    </div>
                </div>
                <div class="modal-footer border-0">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-success">Sesuaikan Stok</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Bulk Import Modal -->
<div class="modal fade" id="bulkImportModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header border-0">
                <h5 class="modal-title">Import Inventory</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label class="form-label">File Excel/CSV</label>
                    <input type="file" class="form-control" accept=".xlsx,.xls,.csv">
                    <small class="text-muted">Format: Excel (.xlsx, .xls) atau CSV</small>
                </div>

                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle me-2"></i>Format File:</h6>
                    <p class="mb-2">File harus memiliki kolom berikut:</p>
                    <ul class="mb-0">
                        <li>Kode Barang</li>
                        <li>Nama Barang</li>
                        <li>Kategori</li>
                        <li>Satuan</li>
                        <li>Stok</li>
                        <li>Harga Beli</li>
                        <li>Harga Jual</li>
                    </ul>
                </div>

                <div class="text-center">
                    <a href="#" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-download me-2"></i>Download Template
                    </a>
                </div>
            </div>
            <div class="modal-footer border-0">
                <button type="button" class="btn btn-light" data-bs-dismiss="modal">Batal</button>
                <button type="button" class="btn btn-success">Import Data</button>
            </div>
        </div>
    </div>
</div>

<script>
function editInventory(item) {
    document.getElementById('edit_id').value = item.id;
    document.getElementById('edit_kode_barang').value = item.kode_barang;
    document.getElementById('edit_nama_barang').value = item.nama_barang;
    document.getElementById('edit_kategori_barang').value = item.kategori_barang || '';
    document.getElementById('edit_satuan').value = item.satuan;
    document.getElementById('edit_stok_minimum').value = item.stok_minimum;
    document.getElementById('edit_harga_beli').value = item.harga_beli ? formatNumber(item.harga_beli) : '';
    document.getElementById('edit_harga_jual').value = item.harga_jual ? formatNumber(item.harga_jual) : '';
    document.getElementById('edit_lokasi_penyimpanan').value = item.lokasi_penyimpanan || '';
    document.getElementById('edit_supplier_id').value = item.supplier_id || '';
    document.getElementById('edit_tanggal_masuk').value = item.tanggal_masuk || '';
    document.getElementById('edit_tanggal_kadaluarsa').value = item.tanggal_kadaluarsa || '';
    document.getElementById('edit_status').value = item.status;
    document.getElementById('edit_keterangan').value = item.keterangan || '';

    const modal = new bootstrap.Modal(document.getElementById('editInventoryModal'));
    modal.show();
}

function adjustStock(id, name, currentStock) {
    document.getElementById('adjust_inventory_id').value = id;
    document.getElementById('adjust_item_name').value = name;
    document.getElementById('adjust_current_stock').value = formatNumber(currentStock);
    document.getElementById('adjust_new_stock').value = currentStock;

    const modal = new bootstrap.Modal(document.getElementById('stockAdjustmentModal'));
    modal.show();
}

function deleteInventory(id) {
    if (confirm('Apakah Anda yakin ingin menghapus item ini? Semua riwayat stock movement akan ikut terhapus.')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="delete">
            <input type="hidden" name="id" value="${id}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

function viewHistory(id) {
    // Implement view stock movement history
    alert('Fitur riwayat stock movement akan segera tersedia');
}

function toggleView(view) {
    const tableView = document.getElementById('tableView');
    const gridView = document.getElementById('gridView');
    const buttons = document.querySelectorAll('.btn-group .btn');

    buttons.forEach(btn => btn.classList.remove('active'));

    if (view === 'table') {
        tableView.style.display = 'block';
        gridView.style.display = 'none';
        buttons[0].classList.add('active');
    } else {
        tableView.style.display = 'none';
        gridView.style.display = 'block';
        buttons[1].classList.add('active');
    }
}

function exportInventory() {
    alert('Fitur export akan segera tersedia');
}

function printBarcode() {
    alert('Fitur print barcode akan segera tersedia');
}

function formatNumber(num) {
    return new Intl.NumberFormat('id-ID').format(num);
}

// Format number inputs
document.addEventListener('DOMContentLoaded', function() {
    const numberInputs = document.querySelectorAll('.number-format');

    numberInputs.forEach(function(input) {
        input.addEventListener('input', function(e) {
            let value = e.target.value.replace(/[^\d]/g, '');
            if (value) {
                e.target.value = formatNumber(value);
            }
        });
    });
});
</script>

<style>
.bg-gradient-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
}

.bg-gradient-success {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
}

.bg-gradient-warning {
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
}

.bg-gradient-danger {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
}

.table-responsive {
    border-radius: 0.375rem;
}

.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

@media (max-width: 768px) {
    .card-body .row .col-md-2 {
        margin-bottom: 0.5rem;
    }

    .btn-group {
        flex-direction: column;
    }

    .btn-group .btn {
        border-radius: 0.375rem !important;
        margin-bottom: 0.25rem;
    }
}
</style>

<?php
// Helper function for short rupiah format
function formatRupiahShort($amount) {
    if ($amount >= 1000000000) {
        return 'Rp ' . number_format($amount / 1000000000, 1) . 'M';
    } elseif ($amount >= 1000000) {
        return 'Rp ' . number_format($amount / 1000000, 1) . 'jt';
    } elseif ($amount >= 1000) {
        return 'Rp ' . number_format($amount / 1000, 0) . 'rb';
    } else {
        return 'Rp ' . number_format($amount, 0);
    }
}
?>

        <!-- Modern Footer -->
        <div class="modern-footer">
            <div class="modern-footer-content">
                <div class="modern-footer-left">
                    <p class="modern-footer-text">
                        © <?= date('Y') ?> Sistem Keuangan Modern.
                        <span class="modern-text-primary">Inventory Management</span>
                    </p>
                </div>
                <div class="modern-footer-right">
                    <span class="modern-footer-version">v2.0.0</span>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/views/layouts/footer.php'; ?>
