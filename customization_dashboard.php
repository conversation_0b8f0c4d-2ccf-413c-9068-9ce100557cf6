<?php
require_once 'includes/config/database.php';
require_once 'includes/helpers/function_check.php';
require_once 'includes/helpers/functions.php';

// Check admin access
$currentUser = getCurrentUser();
if (!$currentUser || $currentUser['role'] !== 'admin') {
    setFlashMessage('danger', 'Aks<PERSON> ditolak! Anda tidak memiliki izin untuk mengakses halaman ini.');
    redirect('dashboard.php');
}

$pageTitle = 'Customization Dashboard';

// Get customization statistics
try {
    // Get settings count
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM system_settings");
    $settingsCount = $stmt->fetch()['total'];

    // Get themes count
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM custom_themes");
    $themesCount = $stmt->fetch()['total'];

    // Get menus count
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM custom_menus");
    $menusCount = $stmt->fetch()['total'];

    // Get widgets count
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM layout_components WHERE component_type = 'widget'");
    $widgetsCount = $stmt->fetch()['total'];

    // Get CSS files count
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM custom_css");
    $cssCount = $stmt->fetch()['total'];

    // Get recent activities
    $stmt = $pdo->query("
        SELECT 'theme' as type, name as title, created_at FROM custom_themes
        UNION ALL
        SELECT 'menu' as type, label as title, created_at FROM custom_menus
        UNION ALL
        SELECT 'css' as type, css_name as title, created_at FROM custom_css
        ORDER BY created_at DESC LIMIT 10
    ");
    $recentActivities = $stmt->fetchAll();

} catch (PDOException $e) {
    $settingsCount = $themesCount = $menusCount = $widgetsCount = $cssCount = 0;
    $recentActivities = [];
}

// Quick actions data
$quickActions = [
    [
        'title' => 'Theme Manager',
        'description' => 'Create and manage themes',
        'icon' => 'fas fa-swatchbook',
        'url' => 'theme_manager.php',
        'color' => 'primary',
        'count' => $themesCount
    ],
    [
        'title' => 'Menu Editor',
        'description' => 'Customize navigation menus',
        'icon' => 'fas fa-bars',
        'url' => 'menu_editor.php',
        'color' => 'success',
        'count' => $menusCount
    ],

];

include 'includes/views/layouts/header.php';
?>

<div class="container-fluid px-4 py-3">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="mb-1">🎨 Customization Dashboard</h2>
                    <p class="text-muted mb-0">Complete system customization control center</p>
                </div>
                <div class="btn-group">
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#quickSetupModal">
                        <i class="fas fa-magic me-1"></i>Quick Setup
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="exportAllSettings()">
                        <i class="fas fa-download me-1"></i>Export All
                    </button>
                    <button type="button" class="btn btn-outline-info" onclick="importSettings()">
                        <i class="fas fa-upload me-1"></i>Import
                    </button>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-2">
                    <div class="card bg-gradient-primary text-white">
                        <div class="card-body text-center">
                            <i class="fas fa-cog fa-2x mb-2"></i>
                            <h4><?= $settingsCount ?></h4>
                            <small>Settings</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card bg-gradient-success text-white">
                        <div class="card-body text-center">
                            <i class="fas fa-swatchbook fa-2x mb-2"></i>
                            <h4><?= $themesCount ?></h4>
                            <small>Themes</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card bg-gradient-info text-white">
                        <div class="card-body text-center">
                            <i class="fas fa-bars fa-2x mb-2"></i>
                            <h4><?= $menusCount ?></h4>
                            <small>Menus</small>
                        </div>
                    </div>
                </div>

                <div class="col-md-2">
                    <div class="card bg-gradient-secondary text-white">
                        <div class="card-body text-center">
                            <i class="fas fa-paint-brush fa-2x mb-2"></i>
                            <h4>100%</h4>
                            <small>Customized</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">🚀 Quick Actions</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <?php foreach ($quickActions as $action): ?>
                                    <div class="col-md-4 mb-3">
                                        <div class="card h-100 quick-action-card" onclick="location.href='<?= $action['url'] ?>'">
                                            <div class="card-body text-center">
                                                <div class="mb-3">
                                                    <i class="<?= $action['icon'] ?> fa-3x text-<?= $action['color'] ?>"></i>
                                                </div>
                                                <h6 class="card-title"><?= $action['title'] ?></h6>
                                                <p class="card-text text-muted small"><?= $action['description'] ?></p>
                                                <?php if ($action['count'] > 0): ?>
                                                    <span class="badge bg-<?= $action['color'] ?>"><?= $action['count'] ?> items</span>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Content Row -->
            <div class="row">
                <!-- Recent Activities -->
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">📋 Recent Activities</h5>
                        </div>
                        <div class="card-body">
                            <?php if (empty($recentActivities)): ?>
                                <div class="text-center py-3">
                                    <i class="fas fa-history fa-2x text-muted mb-2"></i>
                                    <p class="text-muted">No recent activities</p>
                                </div>
                            <?php else: ?>
                                <div class="timeline">
                                    <?php foreach ($recentActivities as $activity): ?>
                                        <div class="timeline-item">
                                            <div class="timeline-marker"></div>
                                            <div class="timeline-content">
                                                <h6 class="mb-1"><?= htmlspecialchars($activity['title']) ?></h6>
                                                <small class="text-muted">
                                                    <?= ucfirst($activity['type']) ?> •
                                                    <?= date('d/m/Y H:i', strtotime($activity['created_at'])) ?>
                                                </small>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- System Status -->
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">⚡ System Status</h5>
                        </div>
                        <div class="card-body">
                            <div class="status-item mb-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span>Theme System</span>
                                    <span class="badge bg-success">Active</span>
                                </div>
                                <div class="progress mt-1" style="height: 4px;">
                                    <div class="progress-bar bg-success" style="width: 100%"></div>
                                </div>
                            </div>

                            <div class="status-item mb-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span>Custom Menus</span>
                                    <span class="badge bg-<?= $menusCount > 0 ? 'success' : 'warning' ?>">
                                        <?= $menusCount > 0 ? 'Active' : 'Inactive' ?>
                                    </span>
                                </div>
                                <div class="progress mt-1" style="height: 4px;">
                                    <div class="progress-bar bg-<?= $menusCount > 0 ? 'success' : 'warning' ?>"
                                         style="width: <?= $menusCount > 0 ? '100' : '30' ?>%"></div>
                                </div>
                            </div>



                            <div class="mt-3">
                                <button type="button" class="btn btn-outline-primary btn-sm" onclick="refreshStatus()">
                                    <i class="fas fa-sync me-1"></i>Refresh Status
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Advanced Tools -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">🔧 Advanced Tools</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">

                                <div class="col-md-3">
                                    <div class="d-grid">
                                        <button type="button" class="btn btn-outline-success" onclick="backupCustomizations()">
                                            <i class="fas fa-save me-2"></i>Backup All
                                        </button>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="d-grid">
                                        <button type="button" class="btn btn-outline-warning" onclick="resetCustomizations()">
                                            <i class="fas fa-undo me-2"></i>Reset All
                                        </button>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="d-grid">
                                        <button type="button" class="btn btn-outline-info" onclick="openDocumentation()">
                                            <i class="fas fa-book me-2"></i>Documentation
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Setup Modal -->
<div class="modal fade" id="quickSetupModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">🚀 Quick Setup Wizard</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="setup-wizard">
                    <!-- Step 1: Theme Selection -->
                    <div class="setup-step active" id="step1">
                        <h6>Step 1: Choose Theme</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="theme-option" data-theme="modern">
                                    <div class="theme-preview">
                                        <div class="d-flex" style="height: 60px;">
                                            <div class="bg-primary" style="width: 30%; border-radius: 4px 0 0 4px;"></div>
                                            <div class="flex-grow-1">
                                                <div class="bg-primary" style="height: 20px; border-radius: 0 4px 0 0;"></div>
                                                <div class="bg-light border" style="height: 40px; border-radius: 0 0 4px 0;"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <h6 class="mt-2">Modern Theme</h6>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="theme-option" data-theme="dark">
                                    <div class="theme-preview">
                                        <div class="d-flex" style="height: 60px;">
                                            <div class="bg-dark" style="width: 30%; border-radius: 4px 0 0 4px;"></div>
                                            <div class="flex-grow-1">
                                                <div class="bg-dark" style="height: 20px; border-radius: 0 4px 0 0;"></div>
                                                <div class="bg-secondary" style="height: 40px; border-radius: 0 0 4px 0;"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <h6 class="mt-2">Dark Theme</h6>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Step 2: Menu Configuration -->
                    <div class="setup-step" id="step2">
                        <h6>Step 2: Configure Menus</h6>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="enableCustomMenus" checked>
                            <label class="form-check-label" for="enableCustomMenus">
                                Enable custom menu items
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="enableMenuIcons" checked>
                            <label class="form-check-label" for="enableMenuIcons">
                                Show menu icons
                            </label>
                        </div>
                    </div>

                    <!-- Step 3: Widget Setup -->
                    <div class="setup-step" id="step3">
                        <h6>Step 3: Widget Configuration</h6>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="enableDashboardWidgets" checked>
                            <label class="form-check-label" for="enableDashboardWidgets">
                                Enable dashboard widgets
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="enableSidebarWidgets">
                            <label class="form-check-label" for="enableSidebarWidgets">
                                Enable sidebar widgets
                            </label>
                        </div>
                    </div>

                    <!-- Step 4: Completion -->
                    <div class="setup-step" id="step4">
                        <h6>Setup Complete!</h6>
                        <div class="text-center">
                            <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                            <p>Your customization setup is ready!</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="prevStep" onclick="previousStep()" style="display: none;">Previous</button>
                <button type="button" class="btn btn-primary" id="nextStep" onclick="nextStep()">Next</button>
                <button type="button" class="btn btn-success" id="finishSetup" onclick="finishSetup()" style="display: none;">Finish</button>
            </div>
        </div>
    </div>
</div>

<style>
.quick-action-card {
    transition: all 0.3s ease;
    cursor: pointer;
    border: 1px solid #dee2e6;
}

.quick-action-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    border-color: #007bff;
}

.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -35px;
    top: 5px;
    width: 10px;
    height: 10px;
    background: #007bff;
    border-radius: 50%;
}

.timeline-marker::before {
    content: '';
    position: absolute;
    left: 4px;
    top: 15px;
    width: 2px;
    height: 20px;
    background: #dee2e6;
}

.timeline-item:last-child .timeline-marker::before {
    display: none;
}

.status-item {
    padding: 10px 0;
}

.bg-gradient-primary {
    background: linear-gradient(45deg, #007bff, #0056b3);
}

.bg-gradient-success {
    background: linear-gradient(45deg, #28a745, #1e7e34);
}

.bg-gradient-info {
    background: linear-gradient(45deg, #17a2b8, #117a8b);
}

.bg-gradient-warning {
    background: linear-gradient(45deg, #ffc107, #d39e00);
}

.bg-gradient-danger {
    background: linear-gradient(45deg, #dc3545, #bd2130);
}

.bg-gradient-secondary {
    background: linear-gradient(45deg, #6c757d, #545b62);
}

.setup-step {
    display: none;
}

.setup-step.active {
    display: block;
}

.theme-option {
    cursor: pointer;
    padding: 15px;
    border: 2px solid #dee2e6;
    border-radius: 8px;
    transition: all 0.3s ease;
    text-align: center;
}

.theme-option:hover,
.theme-option.selected {
    border-color: #007bff;
    background: rgba(0, 123, 255, 0.05);
}

.theme-preview {
    border: 1px solid #dee2e6;
    border-radius: 6px;
    overflow: hidden;
    margin-bottom: 10px;
}
</style>

<script>
let currentStep = 1;
const totalSteps = 4;

// Quick setup wizard functions
function nextStep() {
    if (currentStep < totalSteps) {
        document.getElementById(`step${currentStep}`).classList.remove('active');
        currentStep++;
        document.getElementById(`step${currentStep}`).classList.add('active');

        updateStepButtons();
    }
}

function previousStep() {
    if (currentStep > 1) {
        document.getElementById(`step${currentStep}`).classList.remove('active');
        currentStep--;
        document.getElementById(`step${currentStep}`).classList.add('active');

        updateStepButtons();
    }
}

function updateStepButtons() {
    const prevBtn = document.getElementById('prevStep');
    const nextBtn = document.getElementById('nextStep');
    const finishBtn = document.getElementById('finishSetup');

    prevBtn.style.display = currentStep > 1 ? 'block' : 'none';
    nextBtn.style.display = currentStep < totalSteps ? 'block' : 'none';
    finishBtn.style.display = currentStep === totalSteps ? 'block' : 'none';
}

function finishSetup() {
    // Apply setup configurations
    const selectedTheme = document.querySelector('.theme-option.selected')?.dataset.theme || 'modern';

    // Show success message
    showToast('Quick setup completed successfully!', 'success');

    // Close modal
    const modal = bootstrap.Modal.getInstance(document.getElementById('quickSetupModal'));
    modal.hide();

    // Refresh page
    setTimeout(() => {
        location.reload();
    }, 1000);
}

// Theme selection
document.addEventListener('DOMContentLoaded', function() {
    document.querySelectorAll('.theme-option').forEach(option => {
        option.addEventListener('click', function() {
            document.querySelectorAll('.theme-option').forEach(opt => opt.classList.remove('selected'));
            this.classList.add('selected');
        });
    });
});

// Advanced functions
function exportAllSettings() {
    // Create export data
    const exportData = {
        timestamp: new Date().toISOString(),
        settings: 'all_customization_settings',
        version: '1.0'
    };

    // Download as JSON
    const dataStr = JSON.stringify(exportData, null, 2);
    const dataBlob = new Blob([dataStr], {type: 'application/json'});
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `customization_backup_${new Date().toISOString().split('T')[0]}.json`;
    link.click();
    URL.revokeObjectURL(url);

    showToast('Settings exported successfully!', 'success');
}

function importSettings() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const data = JSON.parse(e.target.result);
                    // Process import data
                    showToast('Settings imported successfully!', 'success');
                    setTimeout(() => location.reload(), 1000);
                } catch (error) {
                    showToast('Invalid file format!', 'danger');
                }
            };
            reader.readAsText(file);
        }
    };
    input.click();
}

function refreshStatus() {
    // Simulate status refresh
    showToast('Status refreshed!', 'info');
    setTimeout(() => location.reload(), 500);
}



function backupCustomizations() {
    if (confirm('Create a backup of all customizations?')) {
        // Simulate backup process
        showToast('Backup created successfully!', 'success');
    }
}

function resetCustomizations() {
    if (confirm('Reset all customizations to default? This action cannot be undone.')) {
        // Simulate reset process
        showToast('Customizations reset to default!', 'warning');
        setTimeout(() => location.reload(), 1000);
    }
}

function openDocumentation() {
    window.open('customization_docs.php', '_blank');
}

// Toast notification function
function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    toast.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(toast);

    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 3000);
}
</script>

<?php include 'includes/views/layouts/footer.php'; ?>
