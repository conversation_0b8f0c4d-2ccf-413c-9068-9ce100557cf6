<?php
require_once 'includes/config/database.php';
require_once 'includes/helpers/function_check.php';
require_once 'includes/helpers/functions.php';
require_once 'includes/helpers/two_factor_auth.php';

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isLoggedIn()) {
    setFlashMessage('danger', 'Silakan login terlebih dahulu');
    redirect('login.php');
}

$currentUser = getCurrentUser();
$currentPage = 'two_factor_setup';

// Create 2FA table if not exists
create2FATable();

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'enable_2fa':
                $secret = $_POST['secret'] ?? '';
                $code = $_POST['verification_code'] ?? '';
                
                if ($secret && $code) {
                    if (verifyTOTP($secret, $code)) {
                        if (enable2FA($currentUser['id'], $secret)) {
                            setFlashMessage('success', '2FA berhasil diaktifkan! Simpan backup codes dengan aman.');
                            redirect('two_factor_setup.php?show_backup_codes=1');
                        } else {
                            setFlashMessage('danger', 'Gagal mengaktifkan 2FA. Silakan coba lagi.');
                        }
                    } else {
                        setFlashMessage('danger', 'Kode verifikasi tidak valid. Silakan coba lagi.');
                    }
                } else {
                    setFlashMessage('danger', 'Data tidak lengkap.');
                }
                break;
                
            case 'disable_2fa':
                $password = $_POST['password'] ?? '';
                
                if (password_verify($password, $currentUser['password'])) {
                    if (disable2FA($currentUser['id'])) {
                        setFlashMessage('success', '2FA berhasil dinonaktifkan.');
                    } else {
                        setFlashMessage('danger', 'Gagal menonaktifkan 2FA.');
                    }
                } else {
                    setFlashMessage('danger', 'Password tidak valid.');
                }
                break;
                
            case 'generate_backup_codes':
                $backupCodes = generate2FABackupCodes($currentUser['id']);
                if (!empty($backupCodes)) {
                    $_SESSION['backup_codes'] = $backupCodes;
                    setFlashMessage('success', 'Backup codes berhasil dibuat. Simpan dengan aman!');
                } else {
                    setFlashMessage('danger', 'Gagal membuat backup codes.');
                }
                break;
        }
        redirect('two_factor_setup.php');
    }
}

// Check if 2FA is enabled
$is2FAEnabled = is2FAEnabled($currentUser['id']);

// Generate new secret if not enabled
$secret = '';
$qrCodeUrl = '';
if (!$is2FAEnabled) {
    $secret = generate2FASecret();
    $qrCodeUrl = generate2FAQRCode($secret, $currentUser['email']);
}

// Get backup codes if requested
$showBackupCodes = isset($_GET['show_backup_codes']) && $is2FAEnabled;
$backupCodes = [];
if ($showBackupCodes && isset($_SESSION['backup_codes'])) {
    $backupCodes = $_SESSION['backup_codes'];
    unset($_SESSION['backup_codes']);
}

// Include header and sidebar
require_once 'includes/views/layouts/header.php';
require_once 'includes/views/layouts/sidebar.php';
?>

<div class="modern-layout">
    <div class="modern-container">
        <!-- Modern Page Header -->
        <div class="modern-page-header modern-flex modern-justify-between modern-items-center modern-mb-xl">
            <div>
                <h1 class="modern-page-title modern-mb-0">Two-Factor Authentication</h1>
                <p class="modern-page-subtitle">Tingkatkan keamanan akun Anda dengan 2FA</p>
            </div>
            <div class="modern-page-actions">
                <span class="modern-badge modern-badge-<?= $is2FAEnabled ? 'success' : 'warning' ?>">
                    <i class="fas fa-shield-alt"></i>
                    <?= $is2FAEnabled ? 'Aktif' : 'Nonaktif' ?>
                </span>
            </div>
        </div>

        <!-- Flash Messages -->
        <?php if ($flash = getFlashMessage()): ?>
        <div class="modern-alert modern-alert-<?= $flash['type'] ?> modern-mb-lg">
            <div class="modern-alert-content">
                <i class="fas fa-<?= $flash['type'] === 'success' ? 'check-circle' : 'exclamation-triangle' ?> modern-alert-icon"></i>
                <div class="modern-alert-message"><?= $flash['message'] ?></div>
            </div>
            <button type="button" class="modern-alert-close" onclick="this.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <?php endif; ?>

        <?php if ($showBackupCodes && !empty($backupCodes)): ?>
        <!-- Backup Codes Display -->
        <div class="modern-card modern-mb-lg">
            <div class="modern-card-header">
                <h5 class="modern-card-title">
                    <i class="fas fa-key modern-text-warning modern-mr-sm"></i>
                    Backup Codes
                </h5>
            </div>
            <div class="modern-card-body">
                <div class="modern-alert modern-alert-warning modern-mb-lg">
                    <div class="modern-alert-content">
                        <i class="fas fa-exclamation-triangle modern-alert-icon"></i>
                        <div class="modern-alert-message">
                            <strong>PENTING:</strong> Simpan backup codes ini dengan aman. Anda dapat menggunakan codes ini jika kehilangan akses ke aplikator authenticator.
                        </div>
                    </div>
                </div>
                
                <div class="modern-backup-codes">
                    <?php foreach ($backupCodes as $code): ?>
                    <div class="modern-backup-code">
                        <code><?= $code ?></code>
                    </div>
                    <?php endforeach; ?>
                </div>
                
                <div class="modern-mt-lg">
                    <button type="button" class="modern-btn modern-btn-primary" onclick="printBackupCodes()">
                        <i class="fas fa-print"></i>
                        Print Codes
                    </button>
                    <button type="button" class="modern-btn modern-btn-success" onclick="downloadBackupCodes()">
                        <i class="fas fa-download"></i>
                        Download
                    </button>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <div class="modern-grid modern-grid-cols-2 modern-gap-lg">
            <?php if (!$is2FAEnabled): ?>
            <!-- Setup 2FA -->
            <div class="modern-card">
                <div class="modern-card-header">
                    <h5 class="modern-card-title">
                        <i class="fas fa-mobile-alt modern-text-success modern-mr-sm"></i>
                        Aktifkan 2FA
                    </h5>
                </div>
                <div class="modern-card-body">
                    <div class="modern-2fa-setup">
                        <div class="modern-step modern-mb-lg">
                            <h6 class="modern-step-title">
                                <span class="modern-step-number">1</span>
                                Install Authenticator App
                            </h6>
                            <p class="modern-step-description">
                                Download aplikasi authenticator seperti Google Authenticator, Authy, atau Microsoft Authenticator.
                            </p>
                        </div>

                        <div class="modern-step modern-mb-lg">
                            <h6 class="modern-step-title">
                                <span class="modern-step-number">2</span>
                                Scan QR Code
                            </h6>
                            <div class="modern-qr-container modern-text-center modern-mb-sm">
                                <img src="<?= $qrCodeUrl ?>" alt="QR Code" class="modern-qr-code">
                            </div>
                            <div class="modern-secret-manual">
                                <p class="modern-text-sm modern-text-muted">Atau masukkan kode manual:</p>
                                <div class="modern-secret-code">
                                    <code><?= $secret ?></code>
                                    <button type="button" class="modern-btn modern-btn-sm modern-btn-outline-primary" onclick="copySecret()">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="modern-step">
                            <h6 class="modern-step-title">
                                <span class="modern-step-number">3</span>
                                Verifikasi
                            </h6>
                            <form method="POST" class="modern-form">
                                <input type="hidden" name="action" value="enable_2fa">
                                <input type="hidden" name="secret" value="<?= $secret ?>">
                                
                                <div class="modern-form-group modern-mb-sm">
                                    <label class="modern-form-label">Masukkan 6-digit code dari app</label>
                                    <input type="text" name="verification_code" class="modern-form-input modern-text-center" 
                                           placeholder="000000" maxlength="6" pattern="[0-9]{6}" required>
                                </div>
                                
                                <button type="submit" class="modern-btn modern-btn-success modern-w-full">
                                    <i class="fas fa-shield-alt"></i>
                                    Aktifkan 2FA
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
            <?php else: ?>
            <!-- 2FA Management -->
            <div class="modern-card">
                <div class="modern-card-header">
                    <h5 class="modern-card-title">
                        <i class="fas fa-shield-alt modern-text-success modern-mr-sm"></i>
                        2FA Aktif
                    </h5>
                </div>
                <div class="modern-card-body">
                    <div class="modern-alert modern-alert-success modern-mb-lg">
                        <div class="modern-alert-content">
                            <i class="fas fa-check-circle modern-alert-icon"></i>
                            <div class="modern-alert-message">
                                Two-Factor Authentication telah aktif untuk akun Anda.
                            </div>
                        </div>
                    </div>

                    <div class="modern-2fa-actions">
                        <form method="POST" class="modern-form modern-mb-lg">
                            <input type="hidden" name="action" value="generate_backup_codes">
                            <button type="submit" class="modern-btn modern-btn-warning modern-w-full modern-mb-sm">
                                <i class="fas fa-key"></i>
                                Generate New Backup Codes
                            </button>
                        </form>

                        <button type="button" class="modern-btn modern-btn-danger modern-w-full" onclick="showDisable2FA()">
                            <i class="fas fa-times"></i>
                            Nonaktifkan 2FA
                        </button>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- Security Information -->
            <div class="modern-card">
                <div class="modern-card-header">
                    <h5 class="modern-card-title">
                        <i class="fas fa-info-circle modern-text-info modern-mr-sm"></i>
                        Informasi Keamanan
                    </h5>
                </div>
                <div class="modern-card-body">
                    <div class="modern-security-info">
                        <div class="modern-security-item">
                            <div class="modern-security-icon">
                                <i class="fas fa-shield-alt modern-text-success"></i>
                            </div>
                            <div class="modern-security-content">
                                <h6>Perlindungan Ekstra</h6>
                                <p>2FA memberikan lapisan keamanan tambahan untuk akun Anda.</p>
                            </div>
                        </div>

                        <div class="modern-security-item">
                            <div class="modern-security-icon">
                                <i class="fas fa-mobile-alt modern-text-primary"></i>
                            </div>
                            <div class="modern-security-content">
                                <h6>Aplikasi Authenticator</h6>
                                <p>Gunakan aplikasi authenticator yang terpercaya seperti Google Authenticator.</p>
                            </div>
                        </div>

                        <div class="modern-security-item">
                            <div class="modern-security-icon">
                                <i class="fas fa-key modern-text-warning"></i>
                            </div>
                            <div class="modern-security-content">
                                <h6>Backup Codes</h6>
                                <p>Simpan backup codes dengan aman untuk akses darurat.</p>
                            </div>
                        </div>

                        <div class="modern-security-item">
                            <div class="modern-security-icon">
                                <i class="fas fa-clock modern-text-info"></i>
                            </div>
                            <div class="modern-security-content">
                                <h6>Time-based</h6>
                                <p>Kode berubah setiap 30 detik untuk keamanan maksimal.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Disable 2FA Modal -->
<div id="disable2FAModal" class="modern-modal" style="display: none;">
    <div class="modern-modal-content">
        <div class="modern-modal-header">
            <h5 class="modern-modal-title">Nonaktifkan 2FA</h5>
            <button type="button" class="modern-modal-close" onclick="hideDisable2FA()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <form method="POST">
            <div class="modern-modal-body">
                <input type="hidden" name="action" value="disable_2fa">
                
                <div class="modern-alert modern-alert-danger modern-mb-lg">
                    <div class="modern-alert-content">
                        <i class="fas fa-exclamation-triangle modern-alert-icon"></i>
                        <div class="modern-alert-message">
                            <strong>Peringatan:</strong> Menonaktifkan 2FA akan mengurangi keamanan akun Anda.
                        </div>
                    </div>
                </div>
                
                <div class="modern-form-group">
                    <label class="modern-form-label">Konfirmasi dengan password Anda</label>
                    <input type="password" name="password" class="modern-form-input" required>
                </div>
            </div>
            <div class="modern-modal-footer">
                <button type="button" class="modern-btn modern-btn-light" onclick="hideDisable2FA()">
                    Batal
                </button>
                <button type="submit" class="modern-btn modern-btn-danger">
                    <i class="fas fa-times"></i>
                    Nonaktifkan 2FA
                </button>
            </div>
        </form>
    </div>
</div>

<?php include 'includes/views/layouts/footer.php'; ?>

<script>
function copySecret() {
    const secretCode = '<?= $secret ?>';
    navigator.clipboard.writeText(secretCode).then(() => {
        alert('Secret code copied to clipboard!');
    });
}

function showDisable2FA() {
    document.getElementById('disable2FAModal').style.display = 'flex';
}

function hideDisable2FA() {
    document.getElementById('disable2FAModal').style.display = 'none';
}

function printBackupCodes() {
    const codes = <?= json_encode($backupCodes ?? []) ?>;
    let printContent = '<h2>Backup Codes - Sistem Keuangan</h2>';
    printContent += '<p>Simpan codes ini dengan aman:</p>';
    printContent += '<div style="font-family: monospace; font-size: 14px;">';
    codes.forEach(code => {
        printContent += '<div style="margin: 10px 0; padding: 5px; border: 1px solid #ccc;">' + code + '</div>';
    });
    printContent += '</div>';
    
    const printWindow = window.open('', '_blank');
    printWindow.document.write(printContent);
    printWindow.print();
}

function downloadBackupCodes() {
    const codes = <?= json_encode($backupCodes ?? []) ?>;
    const content = 'Backup Codes - Sistem Keuangan\n\n' + codes.join('\n');
    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'backup-codes.txt';
    a.click();
    URL.revokeObjectURL(url);
}
</script>

<style>
.modern-2fa-setup .modern-step {
    border-left: 3px solid #007bff;
    padding-left: 20px;
    position: relative;
}

.modern-step-number {
    position: absolute;
    left: -35px;
    top: 0;
    width: 25px;
    height: 25px;
    background: #007bff;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
}

.modern-qr-code {
    max-width: 200px;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 10px;
    background: white;
}

.modern-secret-code {
    display: flex;
    align-items: center;
    gap: 10px;
    background: #f8f9fa;
    padding: 10px;
    border-radius: 5px;
    border: 1px solid #dee2e6;
}

.modern-backup-codes {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 10px;
    margin: 20px 0;
}

.modern-backup-code {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    padding: 10px;
    text-align: center;
    font-family: monospace;
}

.modern-security-info .modern-security-item {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    margin-bottom: 20px;
}

.modern-security-icon {
    font-size: 24px;
    margin-top: 5px;
}

.modern-security-content h6 {
    margin: 0 0 5px 0;
    font-weight: 600;
}

.modern-security-content p {
    margin: 0;
    color: #6c757d;
    font-size: 14px;
}
</style>
