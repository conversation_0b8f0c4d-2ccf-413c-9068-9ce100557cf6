<!DOCTYPE html>
<html lang="id" data-bs-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Dropdown - KeuanganKu</title>

    <!-- CSS Libraries -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="assets/css/layout-manager.css?v=<?= time() ?>" rel="stylesheet">
    <link href="assets/css/control-sidebar.css?v=<?= time() ?>" rel="stylesheet">
</head>
<body>
    <!-- Debug Sidebar -->
    <aside class="modern-sidebar">
        <!-- Sidebar Header -->
        <div class="sidebar-header">
            <div class="brand-container">
                <div class="brand-logo">
                    <div class="logo-icon">
                        <i class="fas fa-bug"></i>
                    </div>
                </div>
                <div class="brand-content">
                    <h1 class="brand-title">Debug</h1>
                    <p class="brand-subtitle">Dropdown Test</p>
                </div>
            </div>
        </div>

        <!-- User Profile Section -->
        <div class="sidebar-user">
            <div class="user-profile">
                <div class="user-avatar">
                    <img src="https://ui-avatars.com/api/?name=Debug&background=dc3545&color=fff&size=40" alt="Debug" class="avatar-img">
                    <div class="status-indicator"></div>
                </div>
                <div class="user-details">
                    <h6 class="user-name">Debug Mode</h6>
                    <span class="user-role">Developer</span>
                </div>
            </div>
        </div>

        <!-- Navigation Menu -->
        <div class="sidebar-navigation">
            <nav class="nav-menu">
                <!-- Test Menu with Submenu -->
                <div class="menu-section">
                    <div class="menu-section-title">Test Menu</div>
                    
                    <button class="menu-item menu-toggle has-submenu" 
                            data-bs-toggle="collapse" 
                            data-bs-target="#testSubmenu" 
                            aria-expanded="false" 
                            data-tooltip="Test Menu"
                            data-submenu="test">
                        <div class="menu-icon">
                            <i class="fas fa-flask"></i>
                        </div>
                        <span class="menu-text">Test Menu</span>
                        <i class="fas fa-chevron-down menu-arrow"></i>
                    </button>
                    <div class="collapse submenu" id="testSubmenu">
                        <div class="submenu-container">
                            <a class="submenu-item" href="test1.php">
                                <div class="submenu-icon">
                                    <i class="fas fa-vial"></i>
                                </div>
                                <span class="submenu-text">Test Item 1</span>
                            </a>
                            <a class="submenu-item" href="test2.php">
                                <div class="submenu-icon">
                                    <i class="fas fa-microscope"></i>
                                </div>
                                <span class="submenu-text">Test Item 2</span>
                            </a>
                            <a class="submenu-item" href="test3.php">
                                <div class="submenu-icon">
                                    <i class="fas fa-atom"></i>
                                </div>
                                <span class="submenu-text">Test Item 3</span>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Another Test Menu -->
                <div class="menu-section">
                    <div class="menu-section-title">Another Test</div>
                    
                    <button class="menu-item menu-toggle has-submenu" 
                            data-bs-toggle="collapse" 
                            data-bs-target="#anotherSubmenu" 
                            aria-expanded="false" 
                            data-tooltip="Another Test"
                            data-submenu="another">
                        <div class="menu-icon">
                            <i class="fas fa-cog"></i>
                        </div>
                        <span class="menu-text">Another Test</span>
                        <i class="fas fa-chevron-down menu-arrow"></i>
                    </button>
                    <div class="collapse submenu" id="anotherSubmenu">
                        <div class="submenu-container">
                            <a class="submenu-item" href="another1.php">
                                <div class="submenu-icon">
                                    <i class="fas fa-wrench"></i>
                                </div>
                                <span class="submenu-text">Another Item 1</span>
                            </a>
                            <a class="submenu-item" href="another2.php">
                                <div class="submenu-icon">
                                    <i class="fas fa-screwdriver"></i>
                                </div>
                                <span class="submenu-text">Another Item 2</span>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Single Menu Item -->
                <div class="menu-section">
                    <div class="menu-section-title">Single Items</div>
                    
                    <a class="menu-item" href="single.php" data-tooltip="Single Item">
                        <div class="menu-icon">
                            <i class="fas fa-star"></i>
                        </div>
                        <span class="menu-text">Single Item</span>
                    </a>
                </div>
            </nav>
        </div>

        <!-- Sidebar Footer -->
        <div class="sidebar-footer">
            <div class="sidebar-footer-content">
                <div style="display: flex; gap: 0.5rem;">
                    <button class="theme-toggle-btn" onclick="toggleTheme()" style="flex: 1;">
                        <i class="fas fa-moon"></i>
                        <span class="theme-toggle-text">Dark Mode</span>
                    </button>
                    <button class="sidebar-toggle-btn" onclick="window.modernSidebar?.toggle()">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                </div>
            </div>
        </div>
    </aside>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Navbar -->
        <nav class="navbar navbar-expand-lg modern-navbar">
            <div class="container-fluid">
                <div class="navbar-brand">
                    <i class="fas fa-bug text-danger me-2"></i>
                    <span class="text-danger fw-bold">Debug Dropdown Test</span>
                </div>
                <div class="navbar-nav ms-auto">
                    <button class="nav-link btn btn-link" onclick="window.modernSidebar?.toggle()">
                        <i class="fas fa-cogs"></i>
                    </button>
                </div>
            </div>
        </nav>

        <!-- Content -->
        <div class="content-container">
            <div class="container-fluid">
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header bg-danger text-white">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-bug me-2"></i>Debug Information
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6>Test Steps:</h6>
                                        <ol>
                                            <li>Click "Toggle Sidebar" to collapse</li>
                                            <li>Click on "Test Menu" or "Another Test"</li>
                                            <li>Check if dropdown appears outside sidebar</li>
                                            <li>Check console for debug messages</li>
                                        </ol>
                                    </div>
                                    <div class="col-md-6">
                                        <h6>Current Status:</h6>
                                        <div id="debugInfo">
                                            <div>Sidebar State: <span id="sidebarState">Loading...</span></div>
                                            <div>Menu Items Found: <span id="menuCount">Loading...</span></div>
                                            <div>Dropdowns Created: <span id="dropdownCount">Loading...</span></div>
                                            <div>Is Mobile: <span id="isMobile">Loading...</span></div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mt-3">
                                    <h6>Console Log:</h6>
                                    <div id="consoleLog" style="background: #000; color: #0f0; padding: 1rem; border-radius: 4px; height: 200px; overflow-y: auto; font-family: monospace; font-size: 0.875rem;">
                                        Console messages will appear here...
                                    </div>
                                </div>

                                <div class="mt-3">
                                    <h6>Test Controls:</h6>
                                    <div class="btn-group" role="group">
                                        <button class="btn btn-primary" onclick="testCollapse()">Collapse & Test</button>
                                        <button class="btn btn-success" onclick="testExpand()">Expand</button>
                                        <button class="btn btn-warning" onclick="testDropdown()">Force Dropdown</button>
                                        <button class="btn btn-info" onclick="updateDebugInfo()">Refresh Info</button>
                                        <button class="btn btn-secondary" onclick="clearConsole()">Clear Log</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/modern-sidebar.js?v=<?= time() ?>"></script>
    <script src="assets/js/theme-manager.js?v=<?= time() ?>"></script>

    <script>
        // Debug functions
        function logToConsole(message) {
            const log = document.getElementById('consoleLog');
            const time = new Date().toLocaleTimeString();
            log.innerHTML += `<div>[${time}] ${message}</div>`;
            log.scrollTop = log.scrollHeight;
        }

        function clearConsole() {
            document.getElementById('consoleLog').innerHTML = 'Console cleared...<br>';
        }

        function updateDebugInfo() {
            const isCollapsed = document.body.classList.contains('sidebar-collapsed');
            const menuItems = document.querySelectorAll('.menu-item.has-submenu');
            const dropdowns = document.querySelectorAll('.collapsed-dropdown');
            const isMobile = window.innerWidth <= 1024;

            document.getElementById('sidebarState').textContent = isCollapsed ? 'Collapsed' : 'Expanded';
            document.getElementById('menuCount').textContent = menuItems.length;
            document.getElementById('dropdownCount').textContent = dropdowns.length;
            document.getElementById('isMobile').textContent = isMobile ? 'Yes' : 'No';

            logToConsole(`Status updated - Collapsed: ${isCollapsed}, Menus: ${menuItems.length}, Dropdowns: ${dropdowns.length}`);
        }

        function testCollapse() {
            logToConsole('Testing collapse...');
            window.modernSidebar?.collapse();
            setTimeout(updateDebugInfo, 500);
        }

        function testExpand() {
            logToConsole('Testing expand...');
            window.modernSidebar?.expand();
            setTimeout(updateDebugInfo, 500);
        }

        function testDropdown() {
            logToConsole('Testing dropdown...');
            const firstMenu = document.querySelector('.menu-item.has-submenu');
            if (firstMenu) {
                firstMenu.click();
                logToConsole('Clicked first submenu item');
            } else {
                logToConsole('No submenu items found!');
            }
        }

        // Override console.log to capture debug messages
        const originalLog = console.log;
        console.log = function(...args) {
            logToConsole(args.join(' '));
            originalLog.apply(console, args);
        };

        // Initial update
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(updateDebugInfo, 1000);
            logToConsole('Debug page loaded');
        });

        // Monitor changes
        const observer = new MutationObserver(function() {
            updateDebugInfo();
        });
        observer.observe(document.body, { 
            attributes: true, 
            attributeFilter: ['class'] 
        });
    </script>
</body>
</html>
