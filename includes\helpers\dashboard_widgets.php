<?php
/**
 * Dashboard Widgets Helper Functions
 * 
 * This file contains functions for dashboard widget management
 */

/**
 * Get available widgets
 * @return array List of available widgets
 */
function getAvailableWidgets() {
    return [
        'financial_summary' => [
            'name' => '<PERSON>kas<PERSON>',
            'description' => 'Total pemasukan, pengeluaran, dan saldo',
            'icon' => 'fas fa-chart-line',
            'size' => 'large',
            'category' => 'financial'
        ],
        'recent_transactions' => [
            'name' => 'Transaksi Terbaru',
            'description' => 'Daftar transaksi terbaru',
            'icon' => 'fas fa-list',
            'size' => 'medium',
            'category' => 'financial'
        ],
        'monthly_chart' => [
            'name' => 'Grafik Bulanan',
            'description' => 'Grafik pemasukan dan pengeluaran bulanan',
            'icon' => 'fas fa-chart-bar',
            'size' => 'large',
            'category' => 'analytics'
        ],
        'target_progress' => [
            'name' => 'Progress Target',
            'description' => 'Progress pencapaian target keuangan',
            'icon' => 'fas fa-bullseye',
            'size' => 'medium',
            'category' => 'goals'
        ],
        'inventory_status' => [
            'name' => 'Status Inventory',
            'description' => 'Ringkasan stok dan produk',
            'icon' => 'fas fa-boxes',
            'size' => 'medium',
            'category' => 'inventory'
        ],
        'supplier_summary' => [
            'name' => 'Ringkasan Supplier',
            'description' => 'Total supplier dan status',
            'icon' => 'fas fa-truck',
            'size' => 'small',
            'category' => 'supplier'
        ],
        'quick_actions' => [
            'name' => 'Quick Actions',
            'description' => 'Tombol aksi cepat',
            'icon' => 'fas fa-bolt',
            'size' => 'small',
            'category' => 'utility'
        ],
        'notifications_widget' => [
            'name' => 'Notifikasi',
            'description' => 'Notifikasi dan peringatan sistem',
            'icon' => 'fas fa-bell',
            'size' => 'medium',
            'category' => 'system'
        ],
        'calendar_widget' => [
            'name' => 'Kalender',
            'description' => 'Kalender dengan event penting',
            'icon' => 'fas fa-calendar',
            'size' => 'medium',
            'category' => 'utility'
        ],
        'weather_widget' => [
            'name' => 'Cuaca',
            'description' => 'Informasi cuaca terkini',
            'icon' => 'fas fa-cloud-sun',
            'size' => 'small',
            'category' => 'utility'
        ]
    ];
}

/**
 * Get user's dashboard layout
 * @param int $userId User ID
 * @return array Dashboard layout configuration
 */
function getUserDashboardLayout($userId) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("
            SELECT setting_value 
            FROM system_settings 
            WHERE setting_key = ? AND user_id = ?
        ");
        $stmt->execute(['dashboard_layout', $userId]);
        $layout = $stmt->fetchColumn();
        
        if ($layout) {
            return json_decode($layout, true);
        }
        
        // Return default layout if none exists
        return getDefaultDashboardLayout();
        
    } catch (Exception $e) {
        error_log("Get dashboard layout error: " . $e->getMessage());
        return getDefaultDashboardLayout();
    }
}

/**
 * Get default dashboard layout
 * @return array Default layout configuration
 */
function getDefaultDashboardLayout() {
    return [
        'widgets' => [
            'financial_summary',
            'recent_transactions',
            'monthly_chart',
            'target_progress',
            'inventory_status',
            'quick_actions'
        ],
        'layout' => [
            'row1' => ['financial_summary'],
            'row2' => ['recent_transactions', 'target_progress'],
            'row3' => ['monthly_chart'],
            'row4' => ['inventory_status', 'quick_actions']
        ]
    ];
}

/**
 * Save user's dashboard layout
 * @param int $userId User ID
 * @param array $layout Layout configuration
 * @return bool Success status
 */
function saveUserDashboardLayout($userId, $layout) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("
            INSERT INTO system_settings (setting_key, setting_value, user_id) 
            VALUES ('dashboard_layout', ?, ?)
            ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)
        ");
        
        return $stmt->execute([json_encode($layout), $userId]);
        
    } catch (Exception $e) {
        error_log("Save dashboard layout error: " . $e->getMessage());
        return false;
    }
}

/**
 * Render widget HTML
 * @param string $widgetId Widget identifier
 * @param int $userId User ID
 * @return string Widget HTML
 */
function renderWidget($widgetId, $userId) {
    $widgets = getAvailableWidgets();
    
    if (!isset($widgets[$widgetId])) {
        return '<div class="modern-widget-error">Widget not found</div>';
    }
    
    $widget = $widgets[$widgetId];
    
    switch ($widgetId) {
        case 'financial_summary':
            return renderFinancialSummaryWidget($userId);
        case 'recent_transactions':
            return renderRecentTransactionsWidget($userId);
        case 'monthly_chart':
            return renderMonthlyChartWidget($userId);
        case 'target_progress':
            return renderTargetProgressWidget($userId);
        case 'inventory_status':
            return renderInventoryStatusWidget($userId);
        case 'supplier_summary':
            return renderSupplierSummaryWidget($userId);
        case 'quick_actions':
            return renderQuickActionsWidget($userId);
        case 'notifications_widget':
            return renderNotificationsWidget($userId);
        case 'calendar_widget':
            return renderCalendarWidget($userId);
        case 'weather_widget':
            return renderWeatherWidget($userId);
        default:
            return '<div class="modern-widget-placeholder">Widget: ' . $widget['name'] . '</div>';
    }
}

/**
 * Render financial summary widget
 * @param int $userId User ID
 * @return string Widget HTML
 */
function renderFinancialSummaryWidget($userId) {
    global $pdo;
    
    try {
        // Get current month financial data
        $stmt = $pdo->prepare("
            SELECT 
                SUM(CASE WHEN jenis = 'pemasukan' THEN jumlah ELSE 0 END) as total_pemasukan,
                SUM(CASE WHEN jenis = 'pengeluaran' THEN jumlah ELSE 0 END) as total_pengeluaran
            FROM transaksi 
            WHERE user_id = ? AND MONTH(tanggal) = MONTH(CURDATE()) AND YEAR(tanggal) = YEAR(CURDATE())
        ");
        $stmt->execute([$userId]);
        $data = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $pemasukan = $data['total_pemasukan'] ?? 0;
        $pengeluaran = $data['total_pengeluaran'] ?? 0;
        $saldo = $pemasukan - $pengeluaran;
        
        return "
        <div class='modern-widget modern-widget-financial'>
            <div class='modern-widget-header'>
                <h6 class='modern-widget-title'>
                    <i class='fas fa-chart-line modern-text-primary'></i>
                    Ringkasan Keuangan Bulan Ini
                </h6>
            </div>
            <div class='modern-widget-body'>
                <div class='modern-financial-grid'>
                    <div class='modern-financial-item modern-financial-income'>
                        <div class='modern-financial-icon'>
                            <i class='fas fa-arrow-up'></i>
                        </div>
                        <div class='modern-financial-content'>
                            <div class='modern-financial-label'>Pemasukan</div>
                            <div class='modern-financial-value'>" . formatRupiah($pemasukan) . "</div>
                        </div>
                    </div>
                    <div class='modern-financial-item modern-financial-expense'>
                        <div class='modern-financial-icon'>
                            <i class='fas fa-arrow-down'></i>
                        </div>
                        <div class='modern-financial-content'>
                            <div class='modern-financial-label'>Pengeluaran</div>
                            <div class='modern-financial-value'>" . formatRupiah($pengeluaran) . "</div>
                        </div>
                    </div>
                    <div class='modern-financial-item modern-financial-balance " . ($saldo >= 0 ? 'positive' : 'negative') . "'>
                        <div class='modern-financial-icon'>
                            <i class='fas fa-wallet'></i>
                        </div>
                        <div class='modern-financial-content'>
                            <div class='modern-financial-label'>Saldo</div>
                            <div class='modern-financial-value'>" . formatRupiah($saldo) . "</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>";
        
    } catch (Exception $e) {
        return '<div class="modern-widget-error">Error loading financial data</div>';
    }
}

/**
 * Render recent transactions widget
 * @param int $userId User ID
 * @return string Widget HTML
 */
function renderRecentTransactionsWidget($userId) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("
            SELECT t.*, k.nama as kategori_nama
            FROM transaksi t
            LEFT JOIN kategori k ON t.kategori_id = k.id
            WHERE t.user_id = ?
            ORDER BY t.created_at DESC
            LIMIT 5
        ");
        $stmt->execute([$userId]);
        $transactions = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $html = "
        <div class='modern-widget modern-widget-transactions'>
            <div class='modern-widget-header'>
                <h6 class='modern-widget-title'>
                    <i class='fas fa-list modern-text-primary'></i>
                    Transaksi Terbaru
                </h6>
                <a href='transaksi.php' class='modern-widget-action'>
                    <i class='fas fa-external-link-alt'></i>
                </a>
            </div>
            <div class='modern-widget-body'>";
        
        if (empty($transactions)) {
            $html .= "
                <div class='modern-widget-empty'>
                    <i class='fas fa-receipt'></i>
                    <p>Belum ada transaksi</p>
                </div>";
        } else {
            $html .= "<div class='modern-transaction-list'>";
            foreach ($transactions as $transaction) {
                $typeClass = $transaction['jenis'] === 'pemasukan' ? 'income' : 'expense';
                $icon = $transaction['jenis'] === 'pemasukan' ? 'fa-plus' : 'fa-minus';
                
                $html .= "
                <div class='modern-transaction-item modern-transaction-{$typeClass}'>
                    <div class='modern-transaction-icon'>
                        <i class='fas {$icon}'></i>
                    </div>
                    <div class='modern-transaction-content'>
                        <div class='modern-transaction-title'>{$transaction['keterangan']}</div>
                        <div class='modern-transaction-meta'>
                            <span class='modern-transaction-category'>{$transaction['kategori_nama']}</span>
                            <span class='modern-transaction-date'>" . formatTanggal($transaction['tanggal']) . "</span>
                        </div>
                    </div>
                    <div class='modern-transaction-amount'>
                        " . formatRupiah($transaction['jumlah']) . "
                    </div>
                </div>";
            }
            $html .= "</div>";
        }
        
        $html .= "
            </div>
        </div>";
        
        return $html;
        
    } catch (Exception $e) {
        return '<div class="modern-widget-error">Error loading transactions</div>';
    }
}

/**
 * Render quick actions widget
 * @param int $userId User ID
 * @return string Widget HTML
 */
function renderQuickActionsWidget($userId) {
    return "
    <div class='modern-widget modern-widget-actions'>
        <div class='modern-widget-header'>
            <h6 class='modern-widget-title'>
                <i class='fas fa-bolt modern-text-primary'></i>
                Quick Actions
            </h6>
        </div>
        <div class='modern-widget-body'>
            <div class='modern-quick-actions'>
                <a href='transaksi.php' class='modern-quick-action modern-quick-action-primary'>
                    <i class='fas fa-plus'></i>
                    <span>Tambah Transaksi</span>
                </a>
                <a href='supplier.php' class='modern-quick-action modern-quick-action-success'>
                    <i class='fas fa-truck'></i>
                    <span>Kelola Supplier</span>
                </a>
                <a href='inventory.php' class='modern-quick-action modern-quick-action-info'>
                    <i class='fas fa-boxes'></i>
                    <span>Cek Inventory</span>
                </a>
                <a href='laporan.php' class='modern-quick-action modern-quick-action-warning'>
                    <i class='fas fa-chart-bar'></i>
                    <span>Lihat Laporan</span>
                </a>
            </div>
        </div>
    </div>";
}
?>
