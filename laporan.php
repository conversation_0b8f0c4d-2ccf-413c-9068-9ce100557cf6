<?php
// <PERSON><PERSON> session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Include file yang diperlukan
require_once 'includes/config/database.php';
require_once 'includes/helpers/function_check.php';
require_once 'includes/helpers/functions.php';
require_once 'includes/helpers/notifications.php';

// Cek apakah user sudah login
if (!isLoggedIn()) {
    setFlashMessage('danger', 'Silakan login terlebih dahulu');
    redirect('login.php');
}

// Dapatkan data user yang sedang login
$currentUser = getCurrentUser();
$currentPage = 'laporan';

// Get filter parameters
$bulan = $_GET['bulan'] ?? date('m');
$tahun = $_GET['tahun'] ?? date('Y');
$tipe = $_GET['tipe'] ?? 'semua';

// Get monthly income and expense
$stmt = $pdo->prepare("
    SELECT 
        COALESCE(SUM(CASE WHEN k.tipe = 'pemasukan' THEN t.jumlah ELSE 0 END), 0) as total_pemasukan,
        COALESCE(SUM(CASE WHEN k.tipe = 'pengeluaran' THEN t.jumlah ELSE 0 END), 0) as total_pengeluaran,
        COUNT(CASE WHEN k.tipe = 'pemasukan' THEN 1 END) as jumlah_pemasukan,
        COUNT(CASE WHEN k.tipe = 'pengeluaran' THEN 1 END) as jumlah_pengeluaran,
        COALESCE(MAX(CASE WHEN k.tipe = 'pemasukan' THEN t.jumlah ELSE 0 END), 0) as max_pemasukan,
        COALESCE(MAX(CASE WHEN k.tipe = 'pengeluaran' THEN t.jumlah ELSE 0 END), 0) as max_pengeluaran
    FROM transaksi t
    JOIN kategori k ON t.kategori_id = k.id
    WHERE t.user_id = ? 
    AND MONTH(t.tanggal) = ?
    AND YEAR(t.tanggal) = ?
");
$stmt->execute([$currentUser['id'], $bulan, $tahun]);
$ringkasan = $stmt->fetch();

// Siapkan data untuk grafik bulanan
$tahun = isset($_GET['tahun']) ? (int)$_GET['tahun'] : date('Y');
$bulan = isset($_GET['bulan']) ? (int)$_GET['bulan'] : date('n');

// Data untuk grafik bulanan
$monthlyLabels = [];
$monthlyPemasukan = [];
$monthlyPengeluaran = [];

for ($i = 1; $i <= 12; $i++) {
    $monthlyLabels[] = date('M Y', mktime(0, 0, 0, $i, 1, $tahun));
    
    // Query untuk pemasukan bulan ini
    $stmt = $pdo->prepare("
        SELECT COALESCE(SUM(jumlah), 0) as total
        FROM transaksi t
        JOIN kategori k ON t.kategori_id = k.id
        WHERE YEAR(tanggal) = ? 
        AND MONTH(tanggal) = ?
        AND k.tipe = 'pemasukan'
        AND (t.user_id = ? OR t.user_id IS NULL)
    ");
    $stmt->execute([$tahun, $i, $currentUser['id']]);
    $result = $stmt->fetch();
    $monthlyPemasukan[] = (float)$result['total'];
    
    // Query untuk pengeluaran bulan ini
    $stmt = $pdo->prepare("
        SELECT COALESCE(SUM(jumlah), 0) as total
        FROM transaksi t
        JOIN kategori k ON t.kategori_id = k.id
        WHERE YEAR(tanggal) = ? 
        AND MONTH(tanggal) = ?
        AND k.tipe = 'pengeluaran'
        AND (t.user_id = ? OR t.user_id IS NULL)
    ");
    $stmt->execute([$tahun, $i, $currentUser['id']]);
    $result = $stmt->fetch();
    $monthlyPengeluaran[] = (float)$result['total'];
}

// Get monthly chart data
$stmt = $pdo->prepare("
    SELECT 
        MONTH(t.tanggal) as bulan,
        COALESCE(SUM(CASE WHEN t.jenis = 'pemasukan' THEN t.jumlah ELSE 0 END), 0) as pemasukan,
        COALESCE(SUM(CASE WHEN t.jenis = 'pengeluaran' THEN t.jumlah ELSE 0 END), 0) as pengeluaran
    FROM transaksi t
    WHERE t.user_id = ? 
    AND YEAR(t.tanggal) = ?
    GROUP BY MONTH(t.tanggal)
    ORDER BY bulan ASC
");
$stmt->execute([$currentUser['id'], $tahun]);
$chartData = $stmt->fetchAll();

// Get transactions by category
$stmt = $pdo->prepare("
    SELECT 
        k.nama as kategori,
        k.tipe,
        COALESCE(SUM(t.jumlah), 0) as total,
        COUNT(t.id) as jumlah_transaksi
    FROM kategori k
    LEFT JOIN transaksi t ON t.kategori_id = k.id 
        AND t.user_id = ? 
        AND MONTH(t.tanggal) = ?
        AND YEAR(t.tanggal) = ?
    WHERE (k.user_id = ? OR k.user_id IS NULL)
    " . ($tipe != 'semua' ? "AND k.tipe = ?" : "") . "
    GROUP BY k.id, k.nama, k.tipe
    HAVING total > 0
    ORDER BY total DESC
");
$params = [$currentUser['id'], $bulan, $tahun, $currentUser['id']];
if ($tipe != 'semua') {
    $params[] = $tipe;
}
$stmt->execute($params);
$kategori = $stmt->fetchAll();

// Get daily transactions
$stmt = $pdo->prepare("
    SELECT 
        DATE(t.tanggal) as tanggal,
        k.nama as kategori,
        k.tipe,
        t.keterangan,
        COALESCE(SUM(CASE WHEN k.tipe = 'pemasukan' THEN t.jumlah ELSE 0 END), 0) as pemasukan,
        COALESCE(SUM(CASE WHEN k.tipe = 'pengeluaran' THEN t.jumlah ELSE 0 END), 0) as pengeluaran
    FROM transaksi t
    JOIN kategori k ON t.kategori_id = k.id
    WHERE t.user_id = ? 
    AND MONTH(t.tanggal) = ?
    AND YEAR(t.tanggal) = ?
    GROUP BY DATE(t.tanggal), k.id, k.nama, k.tipe, t.keterangan
    ORDER BY tanggal DESC, k.nama ASC
");
$stmt->execute([$currentUser['id'], $bulan, $tahun]);
$harian = $stmt->fetchAll();

// Data untuk grafik kategori
$stmt = $pdo->prepare("
    SELECT 
        k.nama as kategori,
        k.tipe,
        COALESCE(SUM(t.jumlah), 0) as total
    FROM kategori k
    LEFT JOIN transaksi t ON k.id = t.kategori_id 
        AND YEAR(t.tanggal) = ? 
        AND MONTH(t.tanggal) = ?
        AND (t.user_id = ? OR t.user_id IS NULL)
    WHERE k.user_id = ? OR k.user_id IS NULL
    GROUP BY k.id, k.nama, k.tipe
    HAVING total > 0
    ORDER BY total DESC
");
$stmt->execute([$tahun, $bulan, $currentUser['id'], $currentUser['id']]);
$kategoriData = $stmt->fetchAll();

$categoryLabels = [];
$categoryData = [];

foreach ($kategoriData as $item) {
    $categoryLabels[] = $item['kategori'] . ' (' . ucfirst($item['tipe']) . ')';
    $categoryData[] = (float)$item['total'];
}

// Include header
include 'includes/views/layouts/header.php';
?>

<!-- Main Content -->
<div class="container-fluid px-4 py-3">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-3">
        <div>
            <h1 class="h4 mb-1 text-gray-800">Laporan Keuangan</h1>
            <p class="text-muted small mb-0">Analisis dan ringkasan keuangan bulanan</p>
        </div>
        <div class="d-flex gap-2">
            <button class="btn btn-primary btn-sm d-flex align-items-center gap-2" onclick="window.print()">
                <i class="fas fa-print"></i>
                <span>Cetak Laporan</span>
            </button>
            <button class="btn btn-success btn-sm d-flex align-items-center gap-2" onclick="exportToExcel()">
                <i class="fas fa-file-excel"></i>
                <span>Export Excel</span>
            </button>
        </div>
    </div>

    <!-- Flash Messages -->
    <?php if ($flash = getFlashMessage()): ?>
    <div class="alert alert-<?= $flash['type'] ?> alert-dismissible fade show mb-3 py-2" role="alert">
        <small><?= $flash['message'] ?></small>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    <?php endif; ?>

    <!-- Filter Form -->
    <div class="card border-0 shadow-sm mb-3">
        <div class="card-body py-2">
            <form method="GET" class="row g-2">
                <div class="col-md-3">
                    <label class="form-label small mb-1">Bulan</label>
                    <select name="bulan" class="form-select form-select-sm">
                        <?php
                        $bulan_list = [
                            '01' => 'Januari', '02' => 'Februari', '03' => 'Maret',
                            '04' => 'April', '05' => 'Mei', '06' => 'Juni',
                            '07' => 'Juli', '08' => 'Agustus', '09' => 'September',
                            '10' => 'Oktober', '11' => 'November', '12' => 'Desember'
                        ];
                        foreach ($bulan_list as $key => $value) {
                            $selected = $key == $bulan ? 'selected' : '';
                            echo "<option value='$key' $selected>$value</option>";
                        }
                        ?>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label small mb-1">Tahun</label>
                    <select name="tahun" class="form-select form-select-sm">
                        <?php
                        $tahun_sekarang = date('Y');
                        for ($i = $tahun_sekarang; $i >= $tahun_sekarang - 5; $i--) {
                            $selected = $i == $tahun ? 'selected' : '';
                            echo "<option value='$i' $selected>$i</option>";
                        }
                        ?>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label small mb-1">Tipe Transaksi</label>
                    <select name="tipe" class="form-select form-select-sm">
                        <option value="semua" <?= $tipe == 'semua' ? 'selected' : '' ?>>Semua</option>
                        <option value="pemasukan" <?= $tipe == 'pemasukan' ? 'selected' : '' ?>>Pemasukan</option>
                        <option value="pengeluaran" <?= $tipe == 'pengeluaran' ? 'selected' : '' ?>>Pengeluaran</option>
                    </select>
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary btn-sm w-100 d-flex align-items-center justify-content-center gap-2">
                        <i class="fas fa-filter"></i>
                        <span>Filter</span>
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="row g-3 mb-3">
        <div class="col-xl-3 col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body py-3">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h6 class="text-success small fw-medium mb-1">Total Pemasukan</h6>
                            <h5 class="mb-0 fw-bold text-gray-800">
                                Rp <?= number_format($ringkasan['total_pemasukan'], 0, ',', '.') ?>
                            </h5>
                            <div class="text-muted small mt-1">
                                <?= $ringkasan['jumlah_pemasukan'] ?> transaksi
                            </div>
                            <div class="text-muted small">
                                Max: Rp <?= number_format($ringkasan['max_pemasukan'], 0, ',', '.') ?>
                            </div>
                        </div>
                        <div class="ms-3">
                            <i class="fas fa-arrow-up fa-2x text-success opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body py-3">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h6 class="text-danger small fw-medium mb-1">Total Pengeluaran</h6>
                            <h5 class="mb-0 fw-bold text-gray-800">
                                Rp <?= number_format($ringkasan['total_pengeluaran'], 0, ',', '.') ?>
                            </h5>
                            <div class="text-muted small mt-1">
                                <?= $ringkasan['jumlah_pengeluaran'] ?> transaksi
                            </div>
                            <div class="text-muted small">
                                Max: Rp <?= number_format($ringkasan['max_pengeluaran'], 0, ',', '.') ?>
                            </div>
                        </div>
                        <div class="ms-3">
                            <i class="fas fa-arrow-down fa-2x text-danger opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body py-3">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h6 class="text-primary small fw-medium mb-1">Saldo</h6>
                            <h5 class="mb-0 fw-bold text-gray-800">
                                Rp <?= number_format($ringkasan['total_pemasukan'] - $ringkasan['total_pengeluaran'], 0, ',', '.') ?>
                            </h5>
                            <div class="text-muted small mt-1">
                                Total <?= $ringkasan['jumlah_pemasukan'] + $ringkasan['jumlah_pengeluaran'] ?> transaksi
                            </div>
                        </div>
                        <div class="ms-3">
                            <i class="fas fa-wallet fa-2x text-primary opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body py-3">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h6 class="text-info small fw-medium mb-1">Persentase</h6>
                            <div class="mb-0 fw-bold text-gray-800">
                                <?php
                                $total = $ringkasan['total_pemasukan'] + $ringkasan['total_pengeluaran'];
                                $persentase_pemasukan = $total > 0 ? ($ringkasan['total_pemasukan'] / $total) * 100 : 0;
                                $persentase_pengeluaran = $total > 0 ? ($ringkasan['total_pengeluaran'] / $total) * 100 : 0;
                                ?>
                                <div class="d-flex align-items-center gap-2 mb-1">
                                    <span class="badge bg-success rounded-pill px-2 py-1">
                                        <?= round($persentase_pemasukan) ?>%
                                    </span>
                                    <span class="small">Pemasukan</span>
                                </div>
                                <div class="d-flex align-items-center gap-2">
                                    <span class="badge bg-danger rounded-pill px-2 py-1">
                                        <?= round($persentase_pengeluaran) ?>%
                                    </span>
                                    <span class="small">Pengeluaran</span>
                                </div>
                            </div>
                        </div>
                        <div class="ms-3">
                            <i class="fas fa-percentage fa-2x text-info opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Grafik -->
    <div class="row g-3 mb-3">
        <!-- Grafik Bulanan -->
        <div class="col-xl-8 col-lg-7">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-white py-2">
                    <h5 class="card-title mb-0 d-flex align-items-center gap-2">
                        <i class="fas fa-chart-line text-primary small"></i>
                        <span class="small fw-medium">Grafik Transaksi Bulanan</span>
                    </h5>
                </div>
                <div class="card-body">
                    <div style="position: relative; height: 300px;">
                        <canvas id="monthlyChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Grafik Kategori -->
        <div class="col-xl-4 col-lg-5">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-white py-2">
                    <h5 class="card-title mb-0 d-flex align-items-center gap-2">
                        <i class="fas fa-chart-pie text-primary small"></i>
                        <span class="small fw-medium">Grafik Transaksi per Kategori</span>
                    </h5>
                </div>
                <div class="card-body">
                    <div style="position: relative; height: 300px;">
                        <canvas id="categoryChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Transactions by Category -->
    <div class="card border-0 shadow-sm mb-3">
        <div class="card-header bg-white py-2 d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0 d-flex align-items-center gap-2">
                <i class="fas fa-tags text-primary small"></i>
                <span class="small fw-medium">Transaksi per Kategori</span>
            </h5>
            <button class="btn btn-sm btn-primary" type="button" data-bs-toggle="collapse" data-bs-target="#categoryTable">
                <i class="fas fa-chevron-down small"></i>
            </button>
        </div>
        <div class="collapse show" id="categoryTable">
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover align-middle mb-0 small">
                        <thead>
                            <tr>
                                <th class="border-0 px-3 fw-medium">Kategori</th>
                                <th class="border-0 px-3 fw-medium">Tipe</th>
                                <th class="border-0 px-3 fw-medium">Jumlah Transaksi</th>
                                <th class="border-0 px-3 fw-medium">Total</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($kategori as $k): ?>
                            <tr>
                                <td class="fw-medium px-3"><?= htmlspecialchars($k['kategori']) ?></td>
                                <td class="px-3">
                                    <span class="badge bg-<?= $k['tipe'] == 'pemasukan' ? 'success' : 'danger' ?> rounded-pill px-2 py-1">
                                        <?= ucfirst($k['tipe']) ?>
                                    </span>
                                </td>
                                <td class="px-3"><?= $k['jumlah_transaksi'] ?></td>
                                <td class="text-<?= $k['tipe'] == 'pemasukan' ? 'success' : 'danger' ?> px-3">
                                    Rp <?= number_format($k['total'], 0, ',', '.') ?>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Daily Transactions -->
    <div class="card border-0 shadow-sm mb-3">
        <div class="card-header bg-white py-2 d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0 d-flex align-items-center gap-2">
                <i class="fas fa-calendar-day text-primary small"></i>
                <span class="small fw-medium">Transaksi Harian</span>
            </h5>
            <button class="btn btn-sm btn-primary" type="button" data-bs-toggle="collapse" data-bs-target="#dailyTable">
                <i class="fas fa-chevron-down small"></i>
            </button>
        </div>
        <div class="collapse" id="dailyTable">
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover align-middle mb-0 small">
                        <thead>
                            <tr>
                                <th class="border-0 px-3 fw-medium">Tanggal</th>
                                <th class="border-0 px-3 fw-medium">Kategori</th>
                                <th class="border-0 px-3 fw-medium">Tipe</th>
                                <th class="border-0 px-3 fw-medium">Keterangan</th>
                                <th class="border-0 px-3 fw-medium">Pemasukan</th>
                                <th class="border-0 px-3 fw-medium">Pengeluaran</th>
                                <th class="border-0 px-3 fw-medium">Saldo</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($harian as $h): ?>
                            <tr>
                                <td class="px-3"><?= date('d/m/Y', strtotime($h['tanggal'])) ?></td>
                                <td class="fw-medium px-3"><?= htmlspecialchars($h['kategori']) ?></td>
                                <td class="px-3">
                                    <span class="badge bg-<?= $h['tipe'] == 'pemasukan' ? 'success' : 'danger' ?> rounded-pill px-2 py-1">
                                        <?= ucfirst($h['tipe']) ?>
                                    </span>
                                </td>
                                <td class="text-muted px-3"><?= htmlspecialchars($h['keterangan']) ?></td>
                                <td class="text-success px-3">
                                    Rp <?= number_format($h['pemasukan'], 0, ',', '.') ?>
                                </td>
                                <td class="text-danger px-3">
                                    Rp <?= number_format($h['pengeluaran'], 0, ',', '.') ?>
                                </td>
                                <td class="<?= ($h['pemasukan'] - $h['pengeluaran']) >= 0 ? 'text-success' : 'text-danger' ?> px-3">
                                    Rp <?= number_format($h['pemasukan'] - $h['pengeluaran'], 0, ',', '.') ?>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    transition: transform 0.2s ease-in-out;
    background: #fff;
}

.card:hover {
    transform: translateY(-5px);
}

.btn {
    padding: 0.5rem 1rem;
    border-radius: 8px;
    transition: all 0.2s ease-in-out;
}

.btn:hover {
    transform: translateY(-2px);
}

.modal-content {
    border-radius: 15px;
}

.form-control {
    border-radius: 8px;
    padding: 0.6rem 1rem;
    border: 1px solid #e0e0e0;
}

.form-control:focus {
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.15);
    border-color: #86b7fe;
}

.form-select {
    border-radius: 8px;
    padding: 0.6rem 1rem;
    border: 1px solid #e0e0e0;
}

.badge {
    font-weight: 500;
    padding: 0.5rem 1rem;
}

.bg-success {
    background-color: #198754 !important;
}

.bg-primary {
    background-color: #0d6efd !important;
}

.bg-danger {
    background-color: #dc3545 !important;
}

.btn-success {
    background-color: #198754;
    border-color: #198754;
}

.btn-primary {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.btn-danger {
    background-color: #dc3545;
    border-color: #dc3545;
}

.btn-light {
    background-color: #f8f9fa;
    border-color: #e0e0e0;
}

.text-muted {
    color: #6c757d !important;
}

.text-gray-800 {
    color: #212529 !important;
}

.shadow-sm {
    box-shadow: 0 .125rem .25rem rgba(0,0,0,.075) !important;
}

.shadow {
    box-shadow: 0 .5rem 1rem rgba(0,0,0,.15) !important;
}

.table > :not(caption) > * > * {
    padding: 1rem;
}

.pagination .page-link {
    border-radius: 8px;
    margin: 0 2px;
    border: none;
    color: #6c757d;
}

.pagination .page-item.active .page-link {
    background-color: #0d6efd;
    color: #fff;
}

.pagination .page-link:hover {
    background-color: #f8f9fa;
    color: #0d6efd;
}

@media print {
    .no-print {
        display: none !important;
    }
    
    .card {
        border: none !important;
        box-shadow: none !important;
    }
    
    .card-header {
        background-color: #f8f9fc !important;
        border-bottom: 1px solid #e3e6f0 !important;
    }
}

.table-responsive {
    max-height: 400px;
    overflow-y: auto;
}

.table thead th {
    position: sticky;
    top: 0;
    background-color: #f8f9fc;
    z-index: 1;
}

.collapse {
    transition: all 0.3s ease;
}

.collapse:not(.show) {
    display: none;
}

.collapsing {
    height: 0;
    overflow: hidden;
    transition: height 0.35s ease;
}

canvas {
    max-width: 100%;
}
</style>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Data untuk grafik bulanan
const monthlyData = {
    labels: <?= json_encode($monthlyLabels) ?>,
    datasets: [
        {
            label: 'Pemasukan',
            data: <?= json_encode($monthlyPemasukan) ?>,
            borderColor: 'rgb(40, 167, 69)',
            backgroundColor: 'rgba(40, 167, 69, 0.1)',
            tension: 0.4,
            fill: true
        },
        {
            label: 'Pengeluaran',
            data: <?= json_encode($monthlyPengeluaran) ?>,
            borderColor: 'rgb(220, 53, 69)',
            backgroundColor: 'rgba(220, 53, 69, 0.1)',
            tension: 0.4,
            fill: true
        }
    ]
};

// Data untuk grafik kategori
const categoryData = {
    labels: <?= json_encode($categoryLabels) ?>,
    datasets: [
        {
            label: 'Total Transaksi',
            data: <?= json_encode($categoryData) ?>,
            backgroundColor: [
                'rgba(40, 167, 69, 0.8)',
                'rgba(220, 53, 69, 0.8)',
                'rgba(255, 193, 7, 0.8)',
                'rgba(23, 162, 184, 0.8)',
                'rgba(111, 66, 193, 0.8)',
                'rgba(253, 126, 20, 0.8)',
                'rgba(13, 110, 253, 0.8)'
            ],
            borderWidth: 1
        }
    ]
};

// Konfigurasi grafik bulanan
const monthlyConfig = {
    type: 'line',
    data: monthlyData,
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'top',
            },
            tooltip: {
                callbacks: {
                    label: function(context) {
                        let label = context.dataset.label || '';
                        if (label) {
                            label += ': ';
                        }
                        if (context.parsed.y !== null) {
                            label += new Intl.NumberFormat('id-ID', {
                                style: 'currency',
                                currency: 'IDR'
                            }).format(context.parsed.y);
                        }
                        return label;
                    }
                }
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return new Intl.NumberFormat('id-ID', {
                            style: 'currency',
                            currency: 'IDR',
                            maximumSignificantDigits: 3
                        }).format(value);
                    }
                }
            }
        }
    }
};

// Konfigurasi grafik kategori
const categoryConfig = {
    type: 'pie',
    data: categoryData,
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'right',
                labels: {
                    boxWidth: 15,
                    padding: 15
                }
            },
            tooltip: {
                callbacks: {
                    label: function(context) {
                        let label = context.label || '';
                        if (label) {
                            label += ': ';
                        }
                        if (context.parsed !== null) {
                            label += new Intl.NumberFormat('id-ID', {
                                style: 'currency',
                                currency: 'IDR'
                            }).format(context.parsed);
                        }
                        return label;
                    }
                }
            }
        }
    }
};

// Inisialisasi grafik
document.addEventListener('DOMContentLoaded', function() {
    // Grafik Bulanan
    const monthlyCtx = document.getElementById('monthlyChart').getContext('2d');
    new Chart(monthlyCtx, monthlyConfig);

    // Grafik Kategori
    const categoryCtx = document.getElementById('categoryChart').getContext('2d');
    new Chart(categoryCtx, categoryConfig);

    // Tambahkan event listener untuk tombol collapse
    document.querySelectorAll('[data-bs-toggle="collapse"]').forEach(button => {
        button.addEventListener('click', function() {
            const icon = this.querySelector('i');
            if (icon.classList.contains('fa-chevron-down')) {
                icon.classList.replace('fa-chevron-down', 'fa-chevron-up');
            } else {
                icon.classList.replace('fa-chevron-up', 'fa-chevron-down');
            }
        });
    });
});

// Export to Excel function
function exportToExcel() {
    // Create a temporary table element
    const table = document.createElement('table');
    
    // Add header row
    const headerRow = table.insertRow();
    ['Tanggal', 'Kategori', 'Tipe', 'Keterangan', 'Pemasukan', 'Pengeluaran', 'Saldo'].forEach(text => {
        const cell = headerRow.insertCell();
        cell.textContent = text;
    });

    // Add data rows
    <?php foreach ($harian as $h): ?>
    const row = table.insertRow();
    row.insertCell().textContent = '<?= date('d/m/Y', strtotime($h['tanggal'])) ?>';
    row.insertCell().textContent = '<?= htmlspecialchars($h['kategori']) ?>';
    row.insertCell().textContent = '<?= ucfirst($h['tipe']) ?>';
    row.insertCell().textContent = '<?= htmlspecialchars($h['keterangan']) ?>';
    row.insertCell().textContent = 'Rp <?= number_format($h['pemasukan'], 0, ',', '.') ?>';
    row.insertCell().textContent = 'Rp <?= number_format($h['pengeluaran'], 0, ',', '.') ?>';
    row.insertCell().textContent = 'Rp <?= number_format($h['pemasukan'] - $h['pengeluaran'], 0, ',', '.') ?>';
    <?php endforeach; ?>

    // Convert table to CSV
    const csv = Array.from(table.rows).map(row => 
        Array.from(row.cells).map(cell => 
            '"' + cell.textContent.replace(/"/g, '""') + '"'
        ).join(',')
    ).join('\n');

    // Create download link
    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = 'laporan_keuangan_<?= date('Y-m') ?>.csv';
    link.click();
}
</script>

<?php
// Include footer
include 'includes/views/layouts/footer.php';
?> 