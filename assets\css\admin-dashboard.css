/* Admin Dashboard Styles */
:root {
    --bs-body-bg: #f8f9fa;
    --bs-body-color: #212529;
    --bs-card-bg: #ffffff;
    --bs-card-border-color: #dee2e6;
    --bs-table-color: #212529;
    --bs-table-bg: #ffffff;
    --bs-table-border-color: #dee2e6;
    --bs-dropdown-bg: #ffffff;
    --bs-dropdown-border-color: #dee2e6;
    --bs-dropdown-link-color: #212529;
    --bs-dropdown-link-hover-bg: #f8f9fa;
    --bs-navbar-bg: #ffffff;
    --bs-navbar-color: #212529;
    --bs-badge-bg: #6c757d;
    --bs-badge-color: #ffffff;
}

[data-bs-theme="dark"] {
    --bs-body-bg: #212529;
    --bs-body-color: #f8f9fa;
    --bs-card-bg: #2c3034;
    --bs-card-border-color: #373b3e;
    --bs-table-color: #f8f9fa;
    --bs-table-bg: #2c3034;
    --bs-table-border-color: #373b3e;
    --bs-dropdown-bg: #2c3034;
    --bs-dropdown-border-color: #373b3e;
    --bs-dropdown-link-color: #f8f9fa;
    --bs-dropdown-link-hover-bg: #373b3e;
    --bs-navbar-bg: #2c3034;
    --bs-navbar-color: #f8f9fa;
    --bs-badge-bg: #6c757d;
    --bs-badge-color: #ffffff;
}

/* Dark mode toggle */
.dark-mode-toggle {
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: all 0.3s ease;
    color: var(--bs-body-color);
}

.dark-mode-toggle:hover {
    background-color: rgba(0, 0, 0, 0.1);
}

[data-bs-theme="dark"] .dark-mode-toggle:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

/* Card styles */
.card {
    background-color: var(--bs-card-bg);
    border-color: var(--bs-card-border-color);
    transition: all 0.3s ease;
}

/* Table styles */
.table {
    color: var(--bs-table-color);
    background-color: var(--bs-table-bg);
    border-color: var(--bs-table-border-color);
}

/* Dropdown styles */
.dropdown-menu {
    background-color: var(--bs-dropdown-bg);
    border-color: var(--bs-dropdown-border-color);
}

.dropdown-item {
    color: var(--bs-dropdown-link-color);
}

.dropdown-item:hover {
    background-color: var(--bs-dropdown-link-hover-bg);
}

/* Navbar styles */
.navbar {
    background-color: var(--bs-navbar-bg) !important;
    color: var(--bs-navbar-color);
}

/* Modal styles */
.modal-content {
    background-color: var(--bs-card-bg);
    color: var(--bs-body-color);
}

/* Form styles */
.form-control,
.form-select {
    background-color: var(--bs-card-bg);
    border-color: var(--bs-card-border-color);
    color: var(--bs-body-color);
}

.form-control:focus,
.form-select:focus {
    background-color: var(--bs-card-bg);
    border-color: #0d6efd;
    color: var(--bs-body-color);
}

/* Text styles */
.text-muted {
    color: var(--bs-body-color) !important;
    opacity: 0.75;
}

/* Badge styles */
.badge {
    background-color: var(--bs-badge-bg);
    color: var(--bs-badge-color);
}

/* Notification badge */
.notification-badge {
    position: absolute;
    top: 0;
    right: 0;
    transform: translate(50%, -50%);
}

/* Profile dropdown */
.profile-dropdown {
    min-width: 200px;
}

.profile-dropdown .dropdown-item {
    padding: 0.5rem 1rem;
}

.profile-dropdown .dropdown-item i {
    width: 20px;
    margin-right: 10px;
}

.profile-dropdown .dropdown-divider {
    margin: 0.5rem 0;
} 