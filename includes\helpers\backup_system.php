<?php
/**
 * Backup System Helper Functions
 * 
 * This file contains functions for automated database backups
 */

/**
 * Create database backup
 * @param string $filename Optional custom filename
 * @return array Result with success status and message
 */
function createDatabaseBackup($filename = null) {
    global $pdo;
    
    try {
        // Create backups directory if not exists
        if (!is_dir('backups')) {
            mkdir('backups', 0755, true);
        }
        
        // Generate filename if not provided
        if (!$filename) {
            $filename = 'backup_' . date('Y-m-d_H-i-s') . '.sql';
        }
        
        $backupPath = 'backups/' . $filename;
        
        // Get database name from PDO
        $dbName = $pdo->query('SELECT DATABASE()')->fetchColumn();
        
        // Get all tables
        $tables = [];
        $result = $pdo->query('SHOW TABLES');
        while ($row = $result->fetch(PDO::FETCH_NUM)) {
            $tables[] = $row[0];
        }
        
        $sql = "-- Database Backup\n";
        $sql .= "-- Generated on: " . date('Y-m-d H:i:s') . "\n";
        $sql .= "-- Database: $dbName\n\n";
        
        $sql .= "SET FOREIGN_KEY_CHECKS = 0;\n\n";
        
        foreach ($tables as $table) {
            // Get table structure
            $result = $pdo->query("SHOW CREATE TABLE `$table`");
            $row = $result->fetch(PDO::FETCH_NUM);
            
            $sql .= "-- Table structure for `$table`\n";
            $sql .= "DROP TABLE IF EXISTS `$table`;\n";
            $sql .= $row[1] . ";\n\n";
            
            // Get table data
            $result = $pdo->query("SELECT * FROM `$table`");
            $numRows = $result->rowCount();
            
            if ($numRows > 0) {
                $sql .= "-- Data for table `$table`\n";
                
                while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
                    $sql .= "INSERT INTO `$table` (";
                    $sql .= implode(', ', array_map(function($col) { return "`$col`"; }, array_keys($row)));
                    $sql .= ") VALUES (";
                    
                    $values = [];
                    foreach ($row as $value) {
                        if ($value === null) {
                            $values[] = 'NULL';
                        } else {
                            $values[] = "'" . addslashes($value) . "'";
                        }
                    }
                    $sql .= implode(', ', $values);
                    $sql .= ");\n";
                }
                $sql .= "\n";
            }
        }
        
        $sql .= "SET FOREIGN_KEY_CHECKS = 1;\n";
        
        // Write to file
        if (file_put_contents($backupPath, $sql)) {
            // Log the backup
            logSystemEvent("Database backup created: $filename", 'info', [
                'filename' => $filename,
                'size' => formatBytes(filesize($backupPath)),
                'tables' => count($tables)
            ]);
            
            return [
                'success' => true,
                'message' => 'Backup created successfully',
                'filename' => $filename,
                'path' => $backupPath,
                'size' => filesize($backupPath)
            ];
        } else {
            return [
                'success' => false,
                'message' => 'Failed to write backup file'
            ];
        }
        
    } catch (Exception $e) {
        error_log("Backup error: " . $e->getMessage());
        return [
            'success' => false,
            'message' => 'Backup failed: ' . $e->getMessage()
        ];
    }
}

/**
 * Restore database from backup
 * @param string $filename Backup filename
 * @return array Result with success status and message
 */
function restoreDatabaseBackup($filename) {
    global $pdo;
    
    try {
        $backupPath = 'backups/' . $filename;
        
        if (!file_exists($backupPath)) {
            return [
                'success' => false,
                'message' => 'Backup file not found'
            ];
        }
        
        $sql = file_get_contents($backupPath);
        
        // Execute SQL statements
        $pdo->exec($sql);
        
        // Log the restore
        logSystemEvent("Database restored from backup: $filename", 'info', [
            'filename' => $filename,
            'size' => formatBytes(filesize($backupPath))
        ]);
        
        return [
            'success' => true,
            'message' => 'Database restored successfully'
        ];
        
    } catch (Exception $e) {
        error_log("Restore error: " . $e->getMessage());
        return [
            'success' => false,
            'message' => 'Restore failed: ' . $e->getMessage()
        ];
    }
}

/**
 * Get list of available backups
 * @return array List of backup files with details
 */
function getBackupList() {
    $backups = [];
    
    if (is_dir('backups')) {
        $files = scandir('backups');
        
        foreach ($files as $file) {
            if (pathinfo($file, PATHINFO_EXTENSION) === 'sql') {
                $filePath = 'backups/' . $file;
                $backups[] = [
                    'filename' => $file,
                    'size' => filesize($filePath),
                    'size_formatted' => formatBytes(filesize($filePath)),
                    'created' => filemtime($filePath),
                    'created_formatted' => date('d/m/Y H:i:s', filemtime($filePath))
                ];
            }
        }
        
        // Sort by creation time (newest first)
        usort($backups, function($a, $b) {
            return $b['created'] - $a['created'];
        });
    }
    
    return $backups;
}

/**
 * Delete backup file
 * @param string $filename Backup filename
 * @return array Result with success status and message
 */
function deleteBackup($filename) {
    try {
        $backupPath = 'backups/' . $filename;
        
        if (!file_exists($backupPath)) {
            return [
                'success' => false,
                'message' => 'Backup file not found'
            ];
        }
        
        if (unlink($backupPath)) {
            logSystemEvent("Backup deleted: $filename", 'info', [
                'filename' => $filename
            ]);
            
            return [
                'success' => true,
                'message' => 'Backup deleted successfully'
            ];
        } else {
            return [
                'success' => false,
                'message' => 'Failed to delete backup file'
            ];
        }
        
    } catch (Exception $e) {
        error_log("Delete backup error: " . $e->getMessage());
        return [
            'success' => false,
            'message' => 'Delete failed: ' . $e->getMessage()
        ];
    }
}

/**
 * Clean old backups (keep only specified number)
 * @param int $keepCount Number of backups to keep
 * @return array Result with success status and message
 */
function cleanOldBackups($keepCount = 10) {
    try {
        $backups = getBackupList();
        $deletedCount = 0;
        
        if (count($backups) > $keepCount) {
            $toDelete = array_slice($backups, $keepCount);
            
            foreach ($toDelete as $backup) {
                $result = deleteBackup($backup['filename']);
                if ($result['success']) {
                    $deletedCount++;
                }
            }
        }
        
        return [
            'success' => true,
            'message' => "Cleaned $deletedCount old backups",
            'deleted_count' => $deletedCount
        ];
        
    } catch (Exception $e) {
        error_log("Clean backups error: " . $e->getMessage());
        return [
            'success' => false,
            'message' => 'Clean failed: ' . $e->getMessage()
        ];
    }
}

/**
 * Schedule automatic backup
 * @return array Result with success status and message
 */
function scheduleAutomaticBackup() {
    try {
        // Check if backup is needed (daily backup)
        $lastBackup = null;
        $backups = getBackupList();
        
        if (!empty($backups)) {
            $lastBackup = $backups[0]['created'];
        }
        
        // Create backup if last backup is older than 24 hours
        if (!$lastBackup || (time() - $lastBackup) > 86400) {
            $result = createDatabaseBackup();
            
            if ($result['success']) {
                // Clean old backups
                cleanOldBackups(7); // Keep 7 days of backups
                
                return [
                    'success' => true,
                    'message' => 'Automatic backup created',
                    'backup_created' => true
                ];
            } else {
                return $result;
            }
        } else {
            return [
                'success' => true,
                'message' => 'Backup not needed yet',
                'backup_created' => false
            ];
        }
        
    } catch (Exception $e) {
        error_log("Automatic backup error: " . $e->getMessage());
        return [
            'success' => false,
            'message' => 'Automatic backup failed: ' . $e->getMessage()
        ];
    }
}
?>
