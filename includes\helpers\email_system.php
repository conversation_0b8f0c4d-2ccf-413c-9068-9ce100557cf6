<?php
/**
 * Email System Helper Functions
 * 
 * This file contains functions for sending email notifications
 */

require_once 'vendor/autoload.php';

use <PERSON><PERSON><PERSON>ailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\SMTP;
use <PERSON><PERSON><PERSON>ailer\PHPMailer\Exception;

/**
 * Send email notification
 * @param string $to Recipient email
 * @param string $subject Email subject
 * @param string $body Email body (HTML)
 * @param array $options Additional options
 * @return array Result with success status and message
 */
function sendEmail($to, $subject, $body, $options = []) {
    try {
        $mail = new PHPMailer(true);
        
        // Server settings
        $mail->isSMTP();
        $mail->Host = getSystemSetting('smtp_host', 'smtp.gmail.com');
        $mail->SMTPAuth = true;
        $mail->Username = getSystemSetting('smtp_username', '');
        $mail->Password = getSystemSetting('smtp_password', '');
        $mail->SMTPSecure = getSystemSetting('smtp_encryption', PHPMailer::ENCRYPTION_STARTTLS);
        $mail->Port = getSystemSetting('smtp_port', 587);
        
        // Recipients
        $mail->setFrom(
            getSystemSetting('smtp_from_email', '<EMAIL>'),
            getSystemSetting('smtp_from_name', 'Sistem Keuangan')
        );
        $mail->addAddress($to);
        
        // Add CC if specified
        if (!empty($options['cc'])) {
            if (is_array($options['cc'])) {
                foreach ($options['cc'] as $cc) {
                    $mail->addCC($cc);
                }
            } else {
                $mail->addCC($options['cc']);
            }
        }
        
        // Add BCC if specified
        if (!empty($options['bcc'])) {
            if (is_array($options['bcc'])) {
                foreach ($options['bcc'] as $bcc) {
                    $mail->addBCC($bcc);
                }
            } else {
                $mail->addBCC($options['bcc']);
            }
        }
        
        // Content
        $mail->isHTML(true);
        $mail->Subject = $subject;
        $mail->Body = $body;
        
        // Add plain text version if specified
        if (!empty($options['alt_body'])) {
            $mail->AltBody = $options['alt_body'];
        }
        
        // Add attachments if specified
        if (!empty($options['attachments'])) {
            foreach ($options['attachments'] as $attachment) {
                if (is_array($attachment)) {
                    $mail->addAttachment($attachment['path'], $attachment['name'] ?? '');
                } else {
                    $mail->addAttachment($attachment);
                }
            }
        }
        
        $mail->send();
        
        // Log email sent
        logSystemEvent("Email sent successfully", 'info', [
            'to' => $to,
            'subject' => $subject
        ]);
        
        return [
            'success' => true,
            'message' => 'Email sent successfully'
        ];
        
    } catch (Exception $e) {
        error_log("Email error: " . $e->getMessage());
        
        // Log email error
        logSystemEvent("Email failed to send", 'error', [
            'to' => $to,
            'subject' => $subject,
            'error' => $e->getMessage()
        ]);
        
        return [
            'success' => false,
            'message' => 'Email failed to send: ' . $e->getMessage()
        ];
    }
}

/**
 * Send welcome email to new user
 * @param array $user User data
 * @param string $tempPassword Temporary password
 * @return array Result
 */
function sendWelcomeEmail($user, $tempPassword = null) {
    $subject = 'Selamat Datang di Sistem Keuangan';
    
    $body = "
    <html>
    <head>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #007bff; color: white; padding: 20px; text-align: center; }
            .content { padding: 20px; background: #f8f9fa; }
            .footer { padding: 20px; text-align: center; color: #666; }
            .btn { background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; }
        </style>
    </head>
    <body>
        <div class='container'>
            <div class='header'>
                <h1>Selamat Datang!</h1>
            </div>
            <div class='content'>
                <h2>Halo {$user['nama']},</h2>
                <p>Selamat datang di Sistem Keuangan! Akun Anda telah berhasil dibuat.</p>
                
                <h3>Detail Akun:</h3>
                <ul>
                    <li><strong>Email:</strong> {$user['email']}</li>
                    <li><strong>Role:</strong> " . ucfirst($user['role']) . "</li>
                </ul>";
    
    if ($tempPassword) {
        $body .= "
                <h3>Password Sementara:</h3>
                <p><strong>$tempPassword</strong></p>
                <p><em>Silakan login dan ubah password Anda segera.</em></p>";
    }
    
    $body .= "
                <p>
                    <a href='" . getSystemSetting('app_url', 'http://localhost/keuangan') . "/login.php' class='btn'>
                        Login Sekarang
                    </a>
                </p>
            </div>
            <div class='footer'>
                <p>Terima kasih telah menggunakan Sistem Keuangan</p>
            </div>
        </div>
    </body>
    </html>";
    
    return sendEmail($user['email'], $subject, $body);
}

/**
 * Send password reset email
 * @param array $user User data
 * @param string $resetToken Reset token
 * @return array Result
 */
function sendPasswordResetEmail($user, $resetToken) {
    $subject = 'Reset Password - Sistem Keuangan';
    $resetUrl = getSystemSetting('app_url', 'http://localhost/keuangan') . "/reset-password.php?token=$resetToken";
    
    $body = "
    <html>
    <head>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #dc3545; color: white; padding: 20px; text-align: center; }
            .content { padding: 20px; background: #f8f9fa; }
            .footer { padding: 20px; text-align: center; color: #666; }
            .btn { background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; }
            .warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 15px 0; }
        </style>
    </head>
    <body>
        <div class='container'>
            <div class='header'>
                <h1>Reset Password</h1>
            </div>
            <div class='content'>
                <h2>Halo {$user['nama']},</h2>
                <p>Kami menerima permintaan untuk reset password akun Anda.</p>
                
                <div class='warning'>
                    <strong>Perhatian:</strong> Jika Anda tidak meminta reset password, abaikan email ini.
                </div>
                
                <p>Klik tombol di bawah untuk reset password Anda:</p>
                
                <p>
                    <a href='$resetUrl' class='btn'>Reset Password</a>
                </p>
                
                <p>Atau copy link berikut ke browser Anda:</p>
                <p><small>$resetUrl</small></p>
                
                <p><em>Link ini akan kedaluwarsa dalam 1 jam.</em></p>
            </div>
            <div class='footer'>
                <p>Sistem Keuangan - Keamanan Akun</p>
            </div>
        </div>
    </body>
    </html>";
    
    return sendEmail($user['email'], $subject, $body);
}

/**
 * Send notification email for important events
 * @param array $user User data
 * @param string $event Event type
 * @param array $data Event data
 * @return array Result
 */
function sendNotificationEmail($user, $event, $data = []) {
    $subjects = [
        'low_stock' => 'Peringatan: Stok Rendah',
        'target_achieved' => 'Selamat: Target Tercapai!',
        'monthly_report' => 'Laporan Bulanan',
        'backup_completed' => 'Backup Database Selesai',
        'security_alert' => 'Peringatan Keamanan'
    ];
    
    $subject = $subjects[$event] ?? 'Notifikasi Sistem';
    
    $body = generateNotificationEmailBody($event, $data, $user);
    
    return sendEmail($user['email'], $subject, $body);
}

/**
 * Generate email body for notifications
 * @param string $event Event type
 * @param array $data Event data
 * @param array $user User data
 * @return string HTML body
 */
function generateNotificationEmailBody($event, $data, $user) {
    $baseTemplate = "
    <html>
    <head>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #007bff; color: white; padding: 20px; text-align: center; }
            .content { padding: 20px; background: #f8f9fa; }
            .footer { padding: 20px; text-align: center; color: #666; }
        </style>
    </head>
    <body>
        <div class='container'>
            <div class='header'>
                <h1>{{TITLE}}</h1>
            </div>
            <div class='content'>
                <h2>Halo {$user['nama']},</h2>
                {{CONTENT}}
            </div>
            <div class='footer'>
                <p>Sistem Keuangan - " . date('d/m/Y H:i') . "</p>
            </div>
        </div>
    </body>
    </html>";
    
    switch ($event) {
        case 'low_stock':
            $title = 'Peringatan Stok Rendah';
            $content = "
                <p>Beberapa produk dalam inventory Anda memiliki stok yang rendah:</p>
                <ul>";
            foreach ($data['products'] as $product) {
                $content .= "<li><strong>{$product['nama']}</strong>: {$product['stok']} tersisa</li>";
            }
            $content .= "</ul>
                <p>Silakan lakukan restocking segera.</p>";
            break;
            
        case 'target_achieved':
            $title = 'Target Tercapai!';
            $content = "
                <p>Selamat! Target keuangan Anda telah tercapai:</p>
                <ul>
                    <li><strong>Target:</strong> {$data['target_name']}</li>
                    <li><strong>Jumlah Target:</strong> " . formatRupiah($data['target_amount']) . "</li>
                    <li><strong>Tercapai:</strong> " . formatRupiah($data['achieved_amount']) . "</li>
                </ul>
                <p>Pertahankan disiplin keuangan Anda!</p>";
            break;
            
        default:
            $title = 'Notifikasi Sistem';
            $content = "<p>Anda memiliki notifikasi baru dari sistem.</p>";
    }
    
    return str_replace(['{{TITLE}}', '{{CONTENT}}'], [$title, $content], $baseTemplate);
}

/**
 * Test email configuration
 * @return array Result
 */
function testEmailConfiguration() {
    $testEmail = getSystemSetting('admin_email', '<EMAIL>');
    
    $subject = 'Test Email - Sistem Keuangan';
    $body = "
    <h2>Test Email</h2>
    <p>Ini adalah email test untuk memverifikasi konfigurasi email sistem.</p>
    <p>Jika Anda menerima email ini, konfigurasi email sudah benar.</p>
    <p>Waktu: " . date('d/m/Y H:i:s') . "</p>";
    
    return sendEmail($testEmail, $subject, $body);
}
?>
