<?php
/**
 * Force Layout Apply - Debugging and Testing Tool
 * This script forces layout CSS to be applied for testing purposes
 */

require_once 'includes/config/database.php';
require_once 'includes/helpers/functions.php';
require_once 'includes/helpers/layout_helper.php';

// Check if user is logged in
$currentUser = getCurrentUser();
if (!$currentUser) {
    die('Please login first');
}

// Get layout preferences
$layoutPrefs = getUserLayoutPreferences($currentUser['id']);

// Generate CSS
$css = generateLayoutCSS($layoutPrefs);

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Force Layout Apply - Testing</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <!-- Force Layout CSS -->
    <?= $css ?>
    
    <style>
    /* Additional Force CSS */
    body .sidebar, body .modern-sidebar, body #sidebar {
        background: linear-gradient(135deg, #ff0080 0%, #00ff80 50%, #8000ff 100%) !important;
        border-radius: 0 15px 15px 0 !important;
    }
    
    body .navbar, body .modern-navbar, body #mainNavbar {
        background: linear-gradient(90deg, #ff0080 0%, #00ff80 50%, #8000ff 100%) !important;
        border-radius: 0 0 15px 15px !important;
    }
    
    body .sidebar a, body .sidebar .nav-link, body .sidebar .brand-title, body .sidebar span {
        color: #ffffff !important;
    }
    
    body .sidebar i {
        color: #ffffff !important;
    }
    
    body .card {
        border-radius: 15px !important;
        box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
    }
    
    /* Debug styles */
    .debug-info {
        position: fixed;
        top: 10px;
        right: 10px;
        background: rgba(0,0,0,0.8);
        color: white;
        padding: 15px;
        border-radius: 8px;
        z-index: 9999;
        max-width: 300px;
        font-size: 12px;
    }
    
    .test-sidebar {
        width: 250px;
        height: 400px;
        background: #343a40;
        color: white;
        padding: 20px;
        border-radius: 8px;
        margin: 20px;
    }
    
    .test-navbar {
        width: 100%;
        height: 60px;
        background: #007bff;
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        margin: 20px;
        display: flex;
        align-items: center;
    }
    
    .test-card {
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 20px;
        margin: 20px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    </style>
</head>
<body>
    <!-- Debug Info -->
    <div class="debug-info">
        <h6>Layout Debug Info</h6>
        <strong>User ID:</strong> <?= $currentUser['id'] ?><br>
        <strong>Layout Type:</strong> <?= $layoutPrefs['layout_type'] ?><br>
        <strong>Color Scheme:</strong> <?= $layoutPrefs['color_scheme'] ?><br>
        <strong>Border Radius:</strong> <?= $layoutPrefs['border_radius'] ?><br>
        <strong>CSS Generated:</strong> <?= strlen($css) ?> chars<br>
        <button onclick="toggleCSS()" class="btn btn-sm btn-warning mt-2">Toggle CSS</button>
        <button onclick="forceApply()" class="btn btn-sm btn-success mt-2">Force Apply</button>
    </div>

    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <h2 class="mb-4">
                    <i class="fas fa-bug me-2"></i>
                    Layout Force Apply Testing
                </h2>
                
                <!-- Test Elements -->
                <div class="row">
                    <div class="col-md-4">
                        <h5>Test Sidebar</h5>
                        <div class="test-sidebar sidebar modern-sidebar" id="testSidebar">
                            <div class="brand-title">
                                <i class="fas fa-chart-line me-2"></i>
                                Test App
                            </div>
                            <hr>
                            <div class="nav-link">
                                <i class="fas fa-home me-2"></i>
                                Dashboard
                            </div>
                            <div class="nav-link">
                                <i class="fas fa-users me-2"></i>
                                Users
                            </div>
                            <div class="nav-link">
                                <i class="fas fa-cog me-2"></i>
                                Settings
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-8">
                        <h5>Test Navbar</h5>
                        <div class="test-navbar navbar modern-navbar" id="testNavbar">
                            <span>Test Navigation Bar</span>
                            <div class="ms-auto">
                                <i class="fas fa-bell me-3"></i>
                                <i class="fas fa-user"></i>
                            </div>
                        </div>
                        
                        <h5>Test Cards</h5>
                        <div class="test-card card">
                            <h6>Test Card 1</h6>
                            <p>This is a test card to see if the layout CSS is being applied correctly.</p>
                        </div>
                        
                        <div class="test-card card">
                            <h6>Test Card 2</h6>
                            <p>Another test card with the same styling to verify consistency.</p>
                        </div>
                    </div>
                </div>
                
                <!-- CSS Output -->
                <div class="row mt-4">
                    <div class="col-12">
                        <h5>Generated CSS Output</h5>
                        <div class="card">
                            <div class="card-body">
                                <pre id="cssOutput" style="max-height: 300px; overflow-y: auto; font-size: 11px;"><?= htmlspecialchars($css) ?></pre>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Manual CSS Test -->
                <div class="row mt-4">
                    <div class="col-12">
                        <h5>Manual CSS Application</h5>
                        <div class="card">
                            <div class="card-body">
                                <button onclick="applyVibrant()" class="btn btn-primary me-2">Apply Vibrant</button>
                                <button onclick="applyPastel()" class="btn btn-success me-2">Apply Pastel</button>
                                <button onclick="applyNeon()" class="btn btn-warning me-2">Apply Neon</button>
                                <button onclick="applyMinimal()" class="btn btn-secondary me-2">Apply Minimal</button>
                                <button onclick="resetStyles()" class="btn btn-danger">Reset</button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Navigation -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-body text-center">
                                <a href="simple_layout_manager.php" class="btn btn-primary me-2">
                                    <i class="fas fa-paint-brush me-1"></i>Simple Layout Manager
                                </a>
                                <a href="advanced_layout_manager.php" class="btn btn-info me-2">
                                    <i class="fas fa-cogs me-1"></i>Advanced Layout Manager
                                </a>
                                <a href="/keuangan/dashboard.php" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left me-1"></i>Dashboard
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
    function toggleCSS() {
        const style = document.getElementById('layout-dynamic-css');
        if (style) {
            style.disabled = !style.disabled;
            console.log('CSS toggled:', style.disabled ? 'disabled' : 'enabled');
        }
    }
    
    function forceApply() {
        // Force apply current layout preferences
        const sidebar = document.getElementById('testSidebar');
        const navbar = document.getElementById('testNavbar');
        const cards = document.querySelectorAll('.test-card');
        
        if (sidebar) {
            sidebar.style.background = 'linear-gradient(135deg, #ff0080 0%, #00ff80 50%, #8000ff 100%)';
            sidebar.style.borderRadius = '0 15px 15px 0';
            sidebar.style.color = '#ffffff';
        }
        
        if (navbar) {
            navbar.style.background = 'linear-gradient(90deg, #ff0080 0%, #00ff80 50%, #8000ff 100%)';
            navbar.style.borderRadius = '0 0 15px 15px';
            navbar.style.color = '#ffffff';
        }
        
        cards.forEach(card => {
            card.style.borderRadius = '15px';
            card.style.boxShadow = '0 8px 25px rgba(0,0,0,0.15)';
        });
        
        console.log('Force apply completed');
    }
    
    function applyVibrant() {
        applyColorScheme({
            sidebar: 'linear-gradient(135deg, #ff0080 0%, #00ff80 50%, #8000ff 100%)',
            navbar: 'linear-gradient(90deg, #ff0080 0%, #00ff80 50%, #8000ff 100%)',
            text: '#ffffff'
        });
    }
    
    function applyPastel() {
        applyColorScheme({
            sidebar: 'linear-gradient(135deg, #ffb3ba 0%, #bae1ff 50%, #baffc9 100%)',
            navbar: 'linear-gradient(90deg, #ffb3ba 0%, #bae1ff 50%, #baffc9 100%)',
            text: '#2c3e50'
        });
    }
    
    function applyNeon() {
        applyColorScheme({
            sidebar: 'linear-gradient(135deg, #39ff14 0%, #ff073a 50%, #00ffff 100%)',
            navbar: 'linear-gradient(90deg, #39ff14 0%, #ff073a 50%, #00ffff 100%)',
            text: '#000000'
        });
    }
    
    function applyMinimal() {
        applyColorScheme({
            sidebar: '#f8f9fa',
            navbar: '#ffffff',
            text: '#2c3e50'
        });
    }
    
    function applyColorScheme(colors) {
        const sidebar = document.getElementById('testSidebar');
        const navbar = document.getElementById('testNavbar');
        
        if (sidebar) {
            sidebar.style.background = colors.sidebar;
            sidebar.style.color = colors.text;
            sidebar.style.borderRadius = '0 15px 15px 0';
        }
        
        if (navbar) {
            navbar.style.background = colors.navbar;
            navbar.style.color = colors.text;
            navbar.style.borderRadius = '0 0 15px 15px';
        }
        
        console.log('Color scheme applied:', colors);
    }
    
    function resetStyles() {
        const sidebar = document.getElementById('testSidebar');
        const navbar = document.getElementById('testNavbar');
        const cards = document.querySelectorAll('.test-card');
        
        if (sidebar) {
            sidebar.style.background = '#343a40';
            sidebar.style.color = '#ffffff';
            sidebar.style.borderRadius = '8px';
        }
        
        if (navbar) {
            navbar.style.background = '#007bff';
            navbar.style.color = '#ffffff';
            navbar.style.borderRadius = '8px';
        }
        
        cards.forEach(card => {
            card.style.borderRadius = '8px';
            card.style.boxShadow = '0 2px 4px rgba(0,0,0,0.1)';
        });
        
        console.log('Styles reset to default');
    }
    
    // Auto-apply on load
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Page loaded, applying force styles...');
        setTimeout(forceApply, 1000);
    });
    </script>
</body>
</html>
