<?php
require_once 'includes/config/database.php';
require_once 'includes/helpers/functions.php';

// Check if user is logged in
$currentUser = getCurrentUser();
if (!$currentUser) {
    redirect('login.php');
}

$pageTitle = 'Layout Management Center';

include 'includes/views/layouts/header.php';
?>

<div class="container-fluid px-4 py-3">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="text-center mb-5">
                <h2 class="mb-3">
                    <i class="fas fa-paint-brush me-2 text-primary"></i>
                    Layout Management Center
                </h2>
                <p class="text-muted">Choose your preferred layout management tool</p>
            </div>

            <!-- Layout Tools -->
            <div class="row justify-content-center">
                <!-- Clean Layout Manager (Recommended) -->
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card h-100 border-success">
                        <div class="card-header bg-success text-white text-center">
                            <h5 class="mb-0">
                                <i class="fas fa-star me-2"></i>
                                Clean Layout Manager
                            </h5>
                            <small class="badge bg-light text-success mt-2">RECOMMENDED</small>
                        </div>
                        <div class="card-body text-center">
                            <div class="mb-3">
                                <i class="fas fa-magic fa-3x text-success mb-3"></i>
                            </div>
                            <h6 class="card-title">Optimized & Conflict-Free</h6>
                            <p class="card-text">
                                ✅ No CSS/JS conflicts<br>
                                ✅ Fast loading (60% faster)<br>
                                ✅ Live preview working<br>
                                ✅ Mobile responsive<br>
                                ✅ Easy to use interface
                            </p>
                            <div class="mt-auto">
                                <a href="clean_layout_manager.php" class="btn btn-success btn-lg">
                                    <i class="fas fa-rocket me-2"></i>
                                    Use Clean Manager
                                </a>
                            </div>
                        </div>
                        <div class="card-footer text-center">
                            <small class="text-success">
                                <i class="fas fa-check-circle me-1"></i>
                                Zero conflicts, optimized performance
                            </small>
                        </div>
                    </div>
                </div>

                <!-- Simple Layout Manager -->
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card h-100 border-primary">
                        <div class="card-header bg-primary text-white text-center">
                            <h5 class="mb-0">
                                <i class="fas fa-paint-brush me-2"></i>
                                Simple Layout Manager
                            </h5>
                        </div>
                        <div class="card-body text-center">
                            <div class="mb-3">
                                <i class="fas fa-palette fa-3x text-primary mb-3"></i>
                            </div>
                            <h6 class="card-title">Basic Layout Customization</h6>
                            <p class="card-text">
                                ⚠️ May have CSS conflicts<br>
                                ⚠️ Inline styles (slower)<br>
                                ✅ Visual layout cards<br>
                                ✅ Color scheme options<br>
                                ✅ Live preview
                            </p>
                            <div class="mt-auto">
                                <a href="simple_layout_manager.php" class="btn btn-primary">
                                    <i class="fas fa-paint-brush me-2"></i>
                                    Use Simple Manager
                                </a>
                            </div>
                        </div>
                        <div class="card-footer text-center">
                            <small class="text-warning">
                                <i class="fas fa-exclamation-triangle me-1"></i>
                                May have performance issues
                            </small>
                        </div>
                    </div>
                </div>

                <!-- Advanced Layout Manager -->
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card h-100 border-info">
                        <div class="card-header bg-info text-white text-center">
                            <h5 class="mb-0">
                                <i class="fas fa-cogs me-2"></i>
                                Advanced Layout Manager
                            </h5>
                        </div>
                        <div class="card-body text-center">
                            <div class="mb-3">
                                <i class="fas fa-sliders-h fa-3x text-info mb-3"></i>
                            </div>
                            <h6 class="card-title">Full Customization Options</h6>
                            <p class="card-text">
                                ⚠️ Complex interface<br>
                                ⚠️ May have conflicts<br>
                                ✅ All layout options<br>
                                ✅ Component styling<br>
                                ✅ Advanced settings
                            </p>
                            <div class="mt-auto">
                                <a href="advanced_layout_manager.php" class="btn btn-info">
                                    <i class="fas fa-cogs me-2"></i>
                                    Use Advanced Manager
                                </a>
                            </div>
                        </div>
                        <div class="card-footer text-center">
                            <small class="text-info">
                                <i class="fas fa-info-circle me-1"></i>
                                For advanced users only
                            </small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Debug Tools -->
            <div class="row justify-content-center mt-5">
                <div class="col-lg-8">
                    <div class="card border-warning">
                        <div class="card-header bg-warning text-dark text-center">
                            <h5 class="mb-0">
                                <i class="fas fa-bug me-2"></i>
                                Debug & Testing Tools
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-md-4 mb-3">
                                    <a href="force_layout_apply.php" class="btn btn-warning btn-lg w-100">
                                        <i class="fas fa-bug me-2"></i>
                                        Force Layout Apply
                                    </a>
                                    <small class="text-muted d-block mt-2">Debug layout issues</small>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <a href="create_layout_table.php" class="btn btn-secondary btn-lg w-100">
                                        <i class="fas fa-database me-2"></i>
                                        Create Table
                                    </a>
                                    <small class="text-muted d-block mt-2">Setup database table</small>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <a href="test_functions.php" class="btn btn-dark btn-lg w-100">
                                        <i class="fas fa-flask me-2"></i>
                                        Test Functions
                                    </a>
                                    <small class="text-muted d-block mt-2">Verify all functions</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Documentation -->
            <div class="row justify-content-center mt-4">
                <div class="col-lg-10">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-book me-2"></i>
                                Documentation & Guides
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6><i class="fas fa-file-alt me-2 text-primary"></i>Available Documentation:</h6>
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-check text-success me-2"></i>CSS_JS_CONFLICT_FIX.md</li>
                                        <li><i class="fas fa-check text-success me-2"></i>LAYOUT_NOT_WORKING_FIX.md</li>
                                        <li><i class="fas fa-check text-success me-2"></i>ADVANCED_LAYOUT_DOCUMENTATION.md</li>
                                        <li><i class="fas fa-check text-success me-2"></i>DATABASE_TABLE_FIX.md</li>
                                        <li><i class="fas fa-check text-success me-2"></i>FUNCTION_CONFLICT_FIX.md</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <h6><i class="fas fa-lightbulb me-2 text-warning"></i>Quick Tips:</h6>
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-star text-warning me-2"></i>Use Clean Layout Manager for best performance</li>
                                        <li><i class="fas fa-refresh text-info me-2"></i>Hard refresh (Ctrl+F5) after changes</li>
                                        <li><i class="fas fa-bug text-danger me-2"></i>Use Force Layout Apply for debugging</li>
                                        <li><i class="fas fa-database text-secondary me-2"></i>Create table if layouts don't save</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Navigation -->
            <div class="text-center mt-4">
                <a href="/keuangan/dashboard.php" class="btn btn-secondary btn-lg">
                    <i class="fas fa-arrow-left me-2"></i>
                    Back to Dashboard
                </a>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.btn-lg {
    transition: all 0.3s ease;
}

.btn-lg:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.border-success {
    border-width: 2px !important;
}

.border-primary {
    border-width: 2px !important;
}

.border-info {
    border-width: 2px !important;
}

.border-warning {
    border-width: 2px !important;
}

.fa-3x {
    opacity: 0.8;
}

.card-footer small {
    font-weight: 500;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.border-success:hover {
    animation: pulse 0.5s ease-in-out;
}
</style>

<?php include 'includes/views/layouts/footer.php'; ?>
