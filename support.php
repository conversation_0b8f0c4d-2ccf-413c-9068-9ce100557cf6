<?php
require_once 'includes/config/database.php';
require_once 'includes/helpers/functions.php';
require_once 'includes/helpers/notifications.php';

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isLoggedIn()) {
    setFlashMessage('danger', 'Silakan login terlebih dahulu');
    redirect('/keuangan/login.php');
}

$currentUser = getCurrentUser();
$currentPage = 'support';

// Create support_tickets table if not exists
try {
    $pdo->exec("CREATE TABLE IF NOT EXISTS support_tickets (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        subject VARCHAR(255) NOT NULL,
        category ENUM('bug', 'feature', 'question', 'other') NOT NULL,
        priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
        message TEXT NOT NULL,
        status ENUM('open', 'in_progress', 'resolved', 'closed') DEFAULT 'open',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    )");
} catch (PDOException $e) {
    error_log("Error creating support_tickets table: " . $e->getMessage());
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'submit_ticket') {
    $errors = [];
    
    if (empty($_POST['subject'])) {
        $errors[] = 'Subject harus diisi';
    }
    
    if (empty($_POST['category'])) {
        $errors[] = 'Kategori harus dipilih';
    }
    
    if (empty($_POST['message'])) {
        $errors[] = 'Pesan harus diisi';
    }
    
    if (empty($errors)) {
        try {
            $stmt = $pdo->prepare("INSERT INTO support_tickets (user_id, subject, category, priority, message) VALUES (?, ?, ?, ?, ?)");
            $result = $stmt->execute([
                $currentUser['id'],
                $_POST['subject'],
                $_POST['category'],
                $_POST['priority'],
                $_POST['message']
            ]);
            
            if ($result) {
                setFlashMessage('success', 'Tiket support berhasil dikirim. Tim kami akan segera merespons.');
                redirect('support.php');
            } else {
                setFlashMessage('danger', 'Gagal mengirim tiket support.');
            }
        } catch (PDOException $e) {
            setFlashMessage('danger', 'Terjadi kesalahan. Silakan coba lagi.');
        }
    } else {
        setFlashMessage('danger', implode('<br>', $errors));
    }
}

// Get user's support tickets
$stmt = $pdo->prepare("SELECT * FROM support_tickets WHERE user_id = ? ORDER BY created_at DESC LIMIT 10");
$stmt->execute([$currentUser['id']]);
$tickets = $stmt->fetchAll();

// Include header
include 'includes/views/layouts/header.php';
?>

<div class="container-fluid py-4">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-1">Support Center</h1>
            <p class="text-muted mb-0">Dapatkan bantuan dan dukungan untuk KeuanganKu</p>
        </div>
    </div>

    <div class="row">
        <!-- Contact Options -->
        <div class="col-lg-4">
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-headset me-2"></i>Hubungi Kami</h5>
                </div>
                <div class="card-body">
                    <div class="contact-option mb-3">
                        <div class="d-flex align-items-center">
                            <div class="contact-icon bg-success bg-opacity-10 p-3 rounded me-3">
                                <i class="fas fa-envelope text-success"></i>
                            </div>
                            <div>
                                <h6 class="mb-1">Email Support</h6>
                                <small class="text-muted"><EMAIL></small>
                                <br><small class="text-success">Respon dalam 24 jam</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="contact-option mb-3">
                        <div class="d-flex align-items-center">
                            <div class="contact-icon bg-info bg-opacity-10 p-3 rounded me-3">
                                <i class="fas fa-comments text-info"></i>
                            </div>
                            <div>
                                <h6 class="mb-1">Live Chat</h6>
                                <small class="text-muted">Chat langsung dengan tim support</small>
                                <br><small class="text-info">Senin-Jumat 09:00-17:00</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="contact-option">
                        <div class="d-flex align-items-center">
                            <div class="contact-icon bg-warning bg-opacity-10 p-3 rounded me-3">
                                <i class="fas fa-phone text-warning"></i>
                            </div>
                            <div>
                                <h6 class="mb-1">Telepon</h6>
                                <small class="text-muted">+62 21 1234 5678</small>
                                <br><small class="text-warning">Senin-Jumat 09:00-17:00</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Links -->
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0"><i class="fas fa-link me-2"></i>Quick Links</h5>
                </div>
                <div class="list-group list-group-flush">
                    <a href="/keuangan/panduan.php" class="list-group-item list-group-item-action">
                        <i class="fas fa-book text-primary me-2"></i>Panduan Penggunaan
                    </a>
                    <a href="/keuangan/faq.php" class="list-group-item list-group-item-action">
                        <i class="fas fa-question-circle text-success me-2"></i>FAQ
                    </a>
                    <a href="/keuangan/tutorial.php" class="list-group-item list-group-item-action">
                        <i class="fas fa-graduation-cap text-warning me-2"></i>Tutorial Video
                    </a>
                </div>
            </div>
        </div>

        <!-- Support Form & Tickets -->
        <div class="col-lg-8">
            <!-- Submit Ticket Form -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0"><i class="fas fa-ticket-alt me-2"></i>Kirim Tiket Support</h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <input type="hidden" name="action" value="submit_ticket">
                        
                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label class="form-label">Subject</label>
                                    <input type="text" name="subject" class="form-control" required placeholder="Jelaskan masalah Anda secara singkat">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">Prioritas</label>
                                    <select name="priority" class="form-select">
                                        <option value="low">Rendah</option>
                                        <option value="medium" selected>Sedang</option>
                                        <option value="high">Tinggi</option>
                                        <option value="urgent">Mendesak</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Kategori</label>
                            <select name="category" class="form-select" required>
                                <option value="">Pilih Kategori</option>
                                <option value="bug">Bug/Error</option>
                                <option value="feature">Permintaan Fitur</option>
                                <option value="question">Pertanyaan</option>
                                <option value="other">Lainnya</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Pesan</label>
                            <textarea name="message" class="form-control" rows="5" required placeholder="Jelaskan masalah atau pertanyaan Anda secara detail..."></textarea>
                        </div>
                        
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-paper-plane me-2"></i>Kirim Tiket
                        </button>
                    </form>
                </div>
            </div>

            <!-- My Tickets -->
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0"><i class="fas fa-history me-2"></i>Tiket Saya</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($tickets)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-ticket-alt fa-3x text-muted mb-3"></i>
                            <h6 class="text-muted">Belum ada tiket support</h6>
                            <p class="text-muted">Tiket yang Anda kirim akan muncul di sini</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Subject</th>
                                        <th>Kategori</th>
                                        <th>Prioritas</th>
                                        <th>Status</th>
                                        <th>Tanggal</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($tickets as $ticket): ?>
                                    <tr>
                                        <td>
                                            <div class="fw-bold"><?= htmlspecialchars($ticket['subject']) ?></div>
                                            <small class="text-muted"><?= htmlspecialchars(substr($ticket['message'], 0, 100)) ?>...</small>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?= $ticket['category'] === 'bug' ? 'danger' : ($ticket['category'] === 'feature' ? 'primary' : ($ticket['category'] === 'question' ? 'info' : 'secondary')) ?>">
                                                <?= ucfirst($ticket['category']) ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?= $ticket['priority'] === 'urgent' ? 'danger' : ($ticket['priority'] === 'high' ? 'warning' : ($ticket['priority'] === 'medium' ? 'info' : 'secondary')) ?>">
                                                <?= ucfirst($ticket['priority']) ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?= $ticket['status'] === 'resolved' ? 'success' : ($ticket['status'] === 'in_progress' ? 'warning' : ($ticket['status'] === 'closed' ? 'secondary' : 'primary')) ?>">
                                                <?= str_replace('_', ' ', ucfirst($ticket['status'])) ?>
                                            </span>
                                        </td>
                                        <td>
                                            <small><?= date('d/m/Y H:i', strtotime($ticket['created_at'])) ?></small>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Knowledge Base -->
    <div class="row mt-5">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-dark text-white">
                    <h5 class="mb-0"><i class="fas fa-book-open me-2"></i>Knowledge Base</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="kb-item text-center p-3">
                                <i class="fas fa-play-circle fa-3x text-primary mb-3"></i>
                                <h6>Getting Started</h6>
                                <p class="text-muted small">Pelajari dasar-dasar penggunaan KeuanganKu</p>
                                <a href="panduan.php#getting-started" class="btn btn-sm btn-outline-primary">Baca Selengkapnya</a>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="kb-item text-center p-3">
                                <i class="fas fa-chart-line fa-3x text-success mb-3"></i>
                                <h6>Mengelola Investasi</h6>
                                <p class="text-muted small">Tips dan trik untuk tracking investasi</p>
                                <a href="panduan.php#investment" class="btn btn-sm btn-outline-success">Baca Selengkapnya</a>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="kb-item text-center p-3">
                                <i class="fas fa-chart-pie fa-3x text-warning mb-3"></i>
                                <h6>Budgeting Efektif</h6>
                                <p class="text-muted small">Cara membuat dan mengelola anggaran</p>
                                <a href="panduan.php#budget" class="btn btn-sm btn-outline-warning">Baca Selengkapnya</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.contact-option {
    border-bottom: 1px solid #eee;
    padding-bottom: 1rem;
}

.contact-option:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.contact-icon {
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.kb-item {
    border-right: 1px solid #eee;
}

.kb-item:last-child {
    border-right: none;
}

@media (max-width: 768px) {
    .kb-item {
        border-right: none;
        border-bottom: 1px solid #eee;
        margin-bottom: 1rem;
    }
    
    .kb-item:last-child {
        border-bottom: none;
        margin-bottom: 0;
    }
}
</style>

<?php include 'includes/views/layouts/footer.php'; ?>
