<?php

/**
 * This file is part of the Carbon package.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

/*
 * Authors: <AUTHORS>
 */
return array_replace_recursive(require __DIR__.'/en.php', [
    'formats' => [
        'L' => 'D-M-YY',
    ],
    'months' => ['जानेवारी', 'फेब्रुवारी', 'मार्च', 'एप्रिल', 'मे', 'जून', 'जुलै', 'ओगस्ट', 'सेप्टेंबर', 'ओक्टोबर', 'नोव्हेंबर', 'डिसेंबर'],
    'months_short' => ['जानेवारी', 'फेब्रुवारी', 'मार्च', 'एप्रिल', 'मे', 'जून', 'जुलै', 'ओगस्ट', 'सेप्टेंबर', 'ओक्टोबर', 'नोव्हेंबर', 'डिसेंबर'],
    'weekdays' => ['आयतार', 'सोमार', 'मंगळवार', 'बुधवार', 'बेरेसतार', 'शुकरार', 'शेनवार'],
    'weekdays_short' => ['आयतार', 'सोमार', 'मंगळवार', 'बुधवार', 'बेरेसतार', 'शुकरार', 'शेनवार'],
    'weekdays_min' => ['आयतार', 'सोमार', 'मंगळवार', 'बुधवार', 'बेरेसतार', 'शुकरार', 'शेनवार'],
    'first_day_of_week' => 0,
    'day_of_first_week_of_year' => 1,
    'meridiem' => ['म.पू.', 'म.नं.'],

    'year' => ':count वैशाकु', // less reliable
    'y' => ':count वैशाकु', // less reliable
    'a_year' => ':count वैशाकु', // less reliable

    'week' => ':count आदित्यवार', // less reliable
    'w' => ':count आदित्यवार', // less reliable
    'a_week' => ':count आदित्यवार', // less reliable

    'minute' => ':count नोंद', // less reliable
    'min' => ':count नोंद', // less reliable
    'a_minute' => ':count नोंद', // less reliable

    'second' => ':count तेंको', // less reliable
    's' => ':count तेंको', // less reliable
    'a_second' => ':count तेंको', // less reliable

    'month' => ':count मैनो',
    'm' => ':count मैनो',
    'a_month' => ':count मैनो',

    'day' => ':count दिवसु',
    'd' => ':count दिवसु',
    'a_day' => ':count दिवसु',

    'hour' => ':count घंते',
    'h' => ':count घंते',
    'a_hour' => ':count घंते',
]);
