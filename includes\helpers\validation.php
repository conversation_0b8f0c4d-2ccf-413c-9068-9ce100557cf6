<?php
/**
 * Validation Helper Functions
 * 
 * This file contains validation functions for form inputs and data.
 */

/**
 * Validate required field
 * @param mixed $value Value to validate
 * @return bool
 */
function validateRequired($value) {
    return !empty($value) || $value === '0';
}

/**
 * Validate email format
 * @param string $email Email to validate
 * @return bool
 */
function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * Validate numeric value
 * @param mixed $value Value to validate
 * @return bool
 */
function validateNumeric($value) {
    return is_numeric($value);
}

/**
 * Validate integer value
 * @param mixed $value Value to validate
 * @return bool
 */
function validateInteger($value) {
    return filter_var($value, FILTER_VALIDATE_INT) !== false;
}

/**
 * Validate minimum length
 * @param string $value Value to validate
 * @param int $min Minimum length
 * @return bool
 */
function validateMinLength($value, $min) {
    return strlen($value) >= $min;
}

/**
 * Validate maximum length
 * @param string $value Value to validate
 * @param int $max Maximum length
 * @return bool
 */
function validateMaxLength($value, $max) {
    return strlen($value) <= $max;
}

// validateDate function is available in functions.php

/**
 * Validate URL format
 * @param string $url URL to validate
 * @return bool
 */
function validateUrl($url) {
    return filter_var($url, FILTER_VALIDATE_URL) !== false;
}

/**
 * Validate phone number (Indonesian format)
 * @param string $phone Phone number
 * @return bool
 */
function validatePhone($phone) {
    // Remove all non-numeric characters
    $phone = preg_replace('/[^0-9]/', '', $phone);
    
    // Check if it's a valid Indonesian phone number
    return preg_match('/^(08|628|\+628)[0-9]{8,12}$/', $phone);
}

/**
 * Validate Indonesian ID number (NIK)
 * @param string $nik NIK to validate
 * @return bool
 */
function validateNIK($nik) {
    // Remove all non-numeric characters
    $nik = preg_replace('/[^0-9]/', '', $nik);
    
    // NIK must be exactly 16 digits
    return strlen($nik) === 16 && is_numeric($nik);
}

/**
 * Validate currency amount
 * @param mixed $amount Amount to validate
 * @param float $min Minimum amount
 * @param float $max Maximum amount
 * @return bool
 */
function validateAmount($amount, $min = 0, $max = null) {
    if (!is_numeric($amount)) {
        return false;
    }
    
    $amount = floatval($amount);
    
    if ($amount < $min) {
        return false;
    }
    
    if ($max !== null && $amount > $max) {
        return false;
    }
    
    return true;
}

/**
 * Validate file extension
 * @param string $filename Filename
 * @param array $allowedExtensions Allowed extensions
 * @return bool
 */
function validateFileExtension($filename, $allowedExtensions) {
    $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
    return in_array($extension, array_map('strtolower', $allowedExtensions));
}

/**
 * Validate password strength
 * @param string $password Password to validate
 * @param int $minLength Minimum length
 * @return array
 */
function validatePassword($password, $minLength = 8) {
    $errors = [];
    
    if (strlen($password) < $minLength) {
        $errors[] = "Password minimal {$minLength} karakter";
    }
    
    if (!preg_match('/[A-Z]/', $password)) {
        $errors[] = "Password harus mengandung huruf besar";
    }
    
    if (!preg_match('/[a-z]/', $password)) {
        $errors[] = "Password harus mengandung huruf kecil";
    }
    
    if (!preg_match('/[0-9]/', $password)) {
        $errors[] = "Password harus mengandung angka";
    }
    
    if (!preg_match('/[^A-Za-z0-9]/', $password)) {
        $errors[] = "Password harus mengandung karakter khusus";
    }
    
    return $errors;
}

/**
 * Validate form data against rules
 * @param array $data Form data
 * @param array $rules Validation rules
 * @return array
 */
function validateFormData($data, $rules) {
    $errors = [];
    
    foreach ($rules as $field => $fieldRules) {
        $value = $data[$field] ?? null;
        $fieldErrors = [];
        
        // Parse rules
        $rulesArray = is_string($fieldRules) ? explode('|', $fieldRules) : $fieldRules;
        
        foreach ($rulesArray as $rule) {
            $ruleParts = explode(':', $rule);
            $ruleName = $ruleParts[0];
            $ruleValue = $ruleParts[1] ?? null;
            
            switch ($ruleName) {
                case 'required':
                    if (!validateRequired($value)) {
                        $fieldErrors[] = "Field {$field} wajib diisi";
                    }
                    break;
                    
                case 'email':
                    if ($value && !validateEmail($value)) {
                        $fieldErrors[] = "Format email tidak valid";
                    }
                    break;
                    
                case 'numeric':
                    if ($value && !validateNumeric($value)) {
                        $fieldErrors[] = "Field {$field} harus berupa angka";
                    }
                    break;
                    
                case 'integer':
                    if ($value && !validateInteger($value)) {
                        $fieldErrors[] = "Field {$field} harus berupa bilangan bulat";
                    }
                    break;
                    
                case 'min':
                    if ($value && !validateMinLength($value, intval($ruleValue))) {
                        $fieldErrors[] = "Field {$field} minimal {$ruleValue} karakter";
                    }
                    break;
                    
                case 'max':
                    if ($value && !validateMaxLength($value, intval($ruleValue))) {
                        $fieldErrors[] = "Field {$field} maksimal {$ruleValue} karakter";
                    }
                    break;
                    
                case 'date':
                    if ($value && !validateDate($value)) {
                        $fieldErrors[] = "Format tanggal tidak valid";
                    }
                    break;
                    
                case 'url':
                    if ($value && !validateUrl($value)) {
                        $fieldErrors[] = "Format URL tidak valid";
                    }
                    break;
                    
                case 'phone':
                    if ($value && !validatePhone($value)) {
                        $fieldErrors[] = "Format nomor telepon tidak valid";
                    }
                    break;
                    
                case 'amount':
                    $min = 0;
                    $max = null;
                    if ($ruleValue) {
                        $range = explode(',', $ruleValue);
                        $min = floatval($range[0]);
                        $max = isset($range[1]) ? floatval($range[1]) : null;
                    }
                    if ($value && !validateAmount($value, $min, $max)) {
                        $fieldErrors[] = "Jumlah tidak valid";
                    }
                    break;
            }
        }
        
        if (!empty($fieldErrors)) {
            $errors[$field] = $fieldErrors;
        }
    }
    
    return $errors;
}

/**
 * Sanitize and validate input data
 * @param array $data Input data
 * @param array $rules Validation rules
 * @return array
 */
function sanitizeAndValidate($data, $rules) {
    // Sanitize data first
    $sanitizedData = [];
    foreach ($data as $key => $value) {
        if (is_string($value)) {
            $sanitizedData[$key] = trim(htmlspecialchars($value, ENT_QUOTES, 'UTF-8'));
        } else {
            $sanitizedData[$key] = $value;
        }
    }
    
    // Validate sanitized data
    $errors = validateFormData($sanitizedData, $rules);
    
    return [
        'data' => $sanitizedData,
        'errors' => $errors,
        'valid' => empty($errors)
    ];
}

/**
 * Check if value exists in database
 * @param string $table Table name
 * @param string $column Column name
 * @param mixed $value Value to check
 * @param int $excludeId ID to exclude from check
 * @return bool
 */
function valueExistsInDatabase($table, $column, $value, $excludeId = null) {
    global $pdo;
    
    try {
        $sql = "SELECT COUNT(*) FROM {$table} WHERE {$column} = ?";
        $params = [$value];
        
        if ($excludeId) {
            $sql .= " AND id != ?";
            $params[] = $excludeId;
        }
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        
        return $stmt->fetchColumn() > 0;
        
    } catch (PDOException $e) {
        error_log("Database validation error: " . $e->getMessage());
        return false;
    }
}

/**
 * Validate unique field in database
 * @param string $table Table name
 * @param string $column Column name
 * @param mixed $value Value to check
 * @param int $excludeId ID to exclude from check
 * @return bool
 */
function validateUnique($table, $column, $value, $excludeId = null) {
    return !valueExistsInDatabase($table, $column, $value, $excludeId);
}
