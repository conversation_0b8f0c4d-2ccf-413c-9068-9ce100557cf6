<?php

try {
require_once 'includes/config/database.php';
require_once 'includes/helpers/functions.php';
require_once 'includes/helpers/notifications.php';

// Start session with secure settings
if (session_status() === PHP_SESSION_NONE) {
    session_set_cookie_params([
        'lifetime' => 86400, // 24 jam
        'path' => '/',
        'secure' => false, // Set to false for local development
        'httponly' => true,
        'samesite' => 'Lax' // Changed from Strict to Lax for local development
    ]);
    session_start();
}

// Clear potentially corrupted session if redirect loop detected
if (isset($_SESSION['redirect_count'])) {
    if ($_SESSION['redirect_count'] > 3) {
        // Reset session to break the loop
        session_unset();
        session_destroy();
        session_start();
        $errors = ['Sesi telah direset karena terlalu banyak redirect'];
    } else {
        $_SESSION['redirect_count']++;
    }
} else {
    $_SESSION['redirect_count'] = 1;
}

// Check if user is already logged in
if (isset($_SESSION['user_id']) && !empty($_SESSION['user_id'])) {
    // Validate the session by checking if user exists in database
    try {
        $stmt = $pdo->prepare("SELECT role FROM users WHERE id = ?");
        $stmt->execute([$_SESSION['user_id']]);
        $user = $stmt->fetch();

        if ($user) {
            // User exists, redirect based on role
            if ($user['role'] === 'admin') {
                header('Location: admin-dashboard.php');
            } else {
                header('Location: dashboard.php');
            }
            exit;
        } else {
            // User not found in database, clear session
            session_unset();
            session_destroy();
            session_start();
            $errors = ['Sesi tidak valid. Silakan login kembali.'];
        }
    } catch (PDOException $e) {
        error_log("Login validation error: " . $e->getMessage());
        // Clear session on database error
        session_unset();
        session_destroy();
        session_start();
        $errors = ['Terjadi kesalahan sistem. Silakan login kembali.'];
    }
}

// Initialize errors array if not already set
if (!isset($errors)) {
    $errors = [];
}

// Handle login form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = cleanInput($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';

    // Validate input
    if (empty($email)) {
        $errors[] = 'Email harus diisi';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'Format email tidak valid';
    }

    if (empty($password)) {
        $errors[] = 'Password harus diisi';
    }

    if (empty($errors)) {
        try {
            // Check user credentials
            $stmt = executeQuery("SELECT * FROM users WHERE email = ?", [$email]);
            $user = $stmt->fetch();

            if ($user && password_verify($password, $user['password'])) {
                // Set session
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['user_role'] = $user['role'];

                // Log activity
                    try {
                executeQuery("
                    INSERT INTO aktivitas (user_id, aktivitas)
                    VALUES (?, 'User logged in')
                ", [$user['id']]);
                    } catch (Exception $e) {
                        error_log("Login Activity Log Error: " . $e->getMessage());
                        // Lanjutkan eksekusi meskipun logging gagal
                    }

                // Create notification
                    try {
                executeQuery("
                    INSERT INTO notifikasi (user_id, judul, pesan, tipe)
                    VALUES (?, 'Login Berhasil', ?, 'success')
                ", [$user['id'], 'Selamat datang kembali, ' . $user['nama']]);
                    } catch (Exception $e) {
                        error_log("Login Notification Error: " . $e->getMessage());
                        // Lanjutkan eksekusi meskipun pembuatan notifikasi gagal
                    }

                // Redirect to dashboard
                redirect('index.php');
            } else {
                $errors[] = 'Email atau password salah';
            }
        } catch (Exception $e) {
            error_log("Login Error: " . $e->getMessage());
            $errors[] = 'Terjadi kesalahan sistem. Silakan coba beberapa saat lagi.';
        }
    }
}

} catch (Exception $e) {
    error_log("Login General Error: " . $e->getMessage());
    $errors[] = 'Terjadi kesalahan umum. Silakan coba beberapa saat lagi.';
}

?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Sistem Keuangan</title>

    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@400;600;700&display=swap" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">

    <style>
        body {
            background-color: #f8f9fa;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Nunito', sans-serif;
        }
        .login-container {
            max-width: 400px;
            width: 100%;
            padding: 15px;
        }
        .login-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        .login-header {
            text-align: center;
            padding: 20px;
            border-bottom: 1px solid #eee;
        }
        .login-body {
            padding: 20px;
        }
        .login-footer {
            text-align: center;
            padding: 20px;
            border-top: 1px solid #eee;
        }
        .form-control:focus {
            border-color: #80bdff;
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
        }
        .btn-primary {
            background-color: #007bff;
            border-color: #007bff;
        }
        .btn-primary:hover {
            background-color: #0069d9;
            border-color: #0062cc;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container">
        <div class="row justify-content-center mt-5">
            <div class="col-md-6">
                <div class="card shadow">
                    <div class="card-body p-5">
                        <h2 class="text-center mb-4">Login</h2>

                        <?php if (!empty($errors)): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <ul class="mb-0">
                                <?php foreach ($errors as $error): ?>
                                <li><?= sanitizeOutput($error) ?></li>
                                <?php endforeach; ?>
                            </ul>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                        <?php endif; ?>

                        <?php
                        // Get and display flash message
                        $flash_message = getFlashMessage();
                        if ($flash_message):
                        ?>
                        <div class="alert alert-<?= $flash_message['type'] ?> alert-dismissible fade show" role="alert">
                            <?= htmlspecialchars($flash_message['message']) ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                        <?php endif; ?>

                        <form action="" method="POST" class="needs-validation" novalidate>
                            <div class="mb-3">
                                <label class="form-label">Email</label>
                                <input type="email" name="email" class="form-control"
                                       value="<?= sanitizeOutput($email ?? '') ?>" required>
                                <div class="invalid-feedback">
                                    Mohon masukkan email yang valid
                                </div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Password</label>
                                <div class="input-group">
                                    <input type="password" name="password" class="form-control" required>
                                    <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                                <div class="invalid-feedback">
                                    Mohon masukkan password
                                </div>
                            </div>

                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary">Login</button>
                                <a href="forgot-password.php" class="btn btn-link">Lupa Password?</a>
                            </div>

                            <div class="text-center mt-3">
                                <p>Belum punya akun? <a href="register.php">Daftar di sini</a></p>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
    // Toggle password visibility
    document.getElementById('togglePassword').addEventListener('click', function() {
        const password = document.querySelector('input[name="password"]');
        const icon = this.querySelector('i');

        if (password.type === 'password') {
            password.type = 'text';
            icon.classList.remove('fa-eye');
            icon.classList.add('fa-eye-slash');
        } else {
            password.type = 'password';
            icon.classList.remove('fa-eye-slash');
            icon.classList.add('fa-eye');
        }
    });

    // Form validation
    const form = document.querySelector('form');
    form.addEventListener('submit', function(event) {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        }
        form.classList.add('was-validated');
    });
    </script>
</body>
</html>
