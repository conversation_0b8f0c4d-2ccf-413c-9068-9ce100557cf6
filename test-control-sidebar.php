<?php
session_start();
require_once 'includes/config/database.php';
require_once 'includes/helpers/functions.php';

// Check if user is logged in
if (!isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$currentUser = getCurrentUser();
$pageTitle = 'Test Control Sidebar - KeuanganKu';

// Include header
include 'includes/views/layouts/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-cogs me-2"></i>Test Control Sidebar
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle me-2"></i>Test Instructions:</h6>
                        <ol>
                            <li><strong>Customize Layout:</strong> Click the <strong>gear icon (⚙️)</strong> in the navbar to open the Layout Customizer</li>
                            <li><strong>Dark Mode:</strong> Click the <strong>moon/sun icon (🌙/☀️)</strong> in the navbar to toggle dark mode</li>
                            <li><strong>Test Features:</strong>
                                <ul>
                                    <li>Dark Mode toggle (should change entire theme)</li>
                                    <li>Sidebar collapse/expand</li>
                                    <li>Navbar background colors (10 variants)</li>
                                    <li>Navbar icon colors (5 variants)</li>
                                    <li>Layout options (borders, shadows, etc.)</li>
                                    <li>Settings persistence (reload page to test)</li>
                                </ul>
                            </li>
                            <li><strong>Debug:</strong> Check the browser console (F12) for debugging information</li>
                        </ol>
                    </div>

                    <div class="alert alert-success">
                        <h6><i class="fas fa-check-circle me-2"></i>Expected Behavior:</h6>
                        <ul class="mb-0">
                            <li>✅ Gear icon should open Control Sidebar from the right</li>
                            <li>✅ Moon/Sun icon should toggle between light and dark themes</li>
                            <li>✅ All settings should be saved and restored on page reload</li>
                            <li>✅ Dark mode should affect navbar, sidebar, cards, and all UI elements</li>
                            <li>✅ Color changes should be applied immediately</li>
                        </ul>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6>Current Theme</h6>
                                    <p id="currentTheme">Loading...</p>

                                    <h6>Sidebar State</h6>
                                    <p id="sidebarState">Loading...</p>

                                    <h6>Control Sidebar State</h6>
                                    <p id="controlSidebarState">Loading...</p>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6>Test Buttons</h6>
                                    <button class="btn btn-primary me-2" onclick="testControlSidebar()">
                                        <i class="fas fa-cogs me-1"></i>Toggle Control Sidebar
                                    </button>
                                    <button class="btn btn-secondary me-2" onclick="testThemeToggle()">
                                        <i class="fas fa-moon me-1"></i>Toggle Theme
                                    </button>
                                    <button class="btn btn-success" onclick="refreshStatus()">
                                        <i class="fas fa-sync me-1"></i>Refresh Status
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0"><i class="fas fa-palette me-2"></i>Theme Test Card</h6>
                                </div>
                                <div class="card-body">
                                    <p>This card should change colors when you toggle dark mode.</p>
                                    <button class="btn btn-primary btn-sm">Primary Button</button>
                                    <button class="btn btn-outline-secondary btn-sm ms-2">Outline Button</button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0"><i class="fas fa-form me-2"></i>Form Test</h6>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label class="form-label">Test Input</label>
                                        <input type="text" class="form-control" placeholder="Type something...">
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="testCheck">
                                        <label class="form-check-label" for="testCheck">Test Checkbox</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0"><i class="fas fa-list me-2"></i>List Test</h6>
                                </div>
                                <div class="card-body">
                                    <ul class="list-group list-group-flush">
                                        <li class="list-group-item">List item 1</li>
                                        <li class="list-group-item">List item 2</li>
                                        <li class="list-group-item">List item 3</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mt-4">
                        <h6>Console Output</h6>
                        <div id="consoleOutput" class="bg-dark text-light p-3 rounded" style="height: 200px; overflow-y: auto; font-family: monospace; font-size: 0.875rem;">
                            <div class="text-success">Console ready...</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Override console.log to display in our custom console
const originalConsoleLog = console.log;
const originalConsoleWarn = console.warn;
const originalConsoleError = console.error;

const consoleOutput = document.getElementById('consoleOutput');

function addToConsole(message, type = 'log') {
    const timestamp = new Date().toLocaleTimeString();
    const colorClass = type === 'warn' ? 'text-warning' : type === 'error' ? 'text-danger' : 'text-light';
    const div = document.createElement('div');
    div.className = colorClass;
    div.innerHTML = `[${timestamp}] ${message}`;
    consoleOutput.appendChild(div);
    consoleOutput.scrollTop = consoleOutput.scrollHeight;
}

console.log = function(...args) {
    originalConsoleLog.apply(console, args);
    addToConsole(args.join(' '), 'log');
};

console.warn = function(...args) {
    originalConsoleWarn.apply(console, args);
    addToConsole(args.join(' '), 'warn');
};

console.error = function(...args) {
    originalConsoleError.apply(console, args);
    addToConsole(args.join(' '), 'error');
};

// Test functions
function testControlSidebar() {
    console.log('Test: Attempting to toggle control sidebar...');

    // Try using the global function first
    if (typeof window.toggleControlSidebarGlobal === 'function') {
        window.toggleControlSidebarGlobal();
        console.log('Test: Used global toggle function');
    } else {
        // Fallback to manual toggle
        const controlSidebar = document.getElementById('controlSidebar');
        const controlSidebarOverlay = document.getElementById('controlSidebarOverlay');

        if (controlSidebar && controlSidebarOverlay) {
            controlSidebar.classList.toggle('open');
            controlSidebarOverlay.classList.toggle('show');
            document.body.style.overflow = controlSidebar.classList.contains('open') ? 'hidden' : '';
            console.log('Test: Control sidebar toggled successfully (manual)');
        } else {
            console.error('Test: Control sidebar elements not found');
        }
    }
    refreshStatus();
}

function testThemeToggle() {
    console.log('Test: Attempting to toggle theme...');
    if (typeof toggleTheme === 'function') {
        toggleTheme();
        console.log('Test: Theme toggled successfully');
    } else {
        console.error('Test: toggleTheme function not found');
    }
    refreshStatus();
}

function refreshStatus() {
    // Update current theme
    const currentTheme = document.documentElement.getAttribute('data-bs-theme') || 'light';
    document.getElementById('currentTheme').textContent = currentTheme;

    // Update sidebar state
    const sidebarCollapsed = document.body.classList.contains('sidebar-collapsed');
    document.getElementById('sidebarState').textContent = sidebarCollapsed ? 'Collapsed' : 'Expanded';

    // Update control sidebar state
    const controlSidebar = document.getElementById('controlSidebar');
    const controlSidebarOpen = controlSidebar ? controlSidebar.classList.contains('open') : false;
    document.getElementById('controlSidebarState').textContent = controlSidebarOpen ? 'Open' : 'Closed';

    console.log('Status refreshed - Theme:', currentTheme, 'Sidebar:', sidebarCollapsed ? 'Collapsed' : 'Expanded', 'Control Sidebar:', controlSidebarOpen ? 'Open' : 'Closed');
}

// Initialize status on page load
document.addEventListener('DOMContentLoaded', function() {
    console.log('Test page loaded');
    refreshStatus();

    // Check if control sidebar elements exist
    const controlSidebar = document.getElementById('controlSidebar');
    const controlSidebarOverlay = document.getElementById('controlSidebarOverlay');
    const controlSidebarToggle = document.querySelector('[data-widget="control-sidebar"]');

    console.log('Control Sidebar Elements Check:');
    console.log('- Control Sidebar:', !!controlSidebar);
    console.log('- Control Sidebar Overlay:', !!controlSidebarOverlay);
    console.log('- Control Sidebar Toggle Button:', !!controlSidebarToggle);

    if (controlSidebarToggle) {
        console.log('- Toggle button found with selector:', controlSidebarToggle.tagName, controlSidebarToggle.className);

        // Add manual event listener as backup
        controlSidebarToggle.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('Manual event listener: Control sidebar toggle clicked');
            testControlSidebar();
        });
        console.log('- Manual event listener added to toggle button');
    }
});
</script>

<?php include 'includes/views/layouts/footer.php'; ?>
