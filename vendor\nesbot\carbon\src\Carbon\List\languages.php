<?php

declare(strict_types=1);

/**
 * This file is part of the Carbon package.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

return [
    /*
     * ISO 639-2
     */
    'ab' => [
        'isoName' => 'Abkhazian',
        'nativeName' => 'аҧсуа бызшәа, аҧсшәа',
    ],
    'aa' => [
        'isoName' => 'Afar',
        'nativeName' => 'Afaraf',
    ],
    'af' => [
        'isoName' => 'Afrikaans',
        'nativeName' => 'Afrikaans',
    ],
    'ak' => [
        'isoName' => 'Akan',
        'nativeName' => 'Akan',
    ],
    'sq' => [
        'isoName' => 'Albanian',
        'nativeName' => 'Shqip',
    ],
    'am' => [
        'isoName' => 'Amharic',
        'nativeName' => 'አማርኛ',
    ],
    'ar' => [
        'isoName' => 'Arabic',
        'nativeName' => 'العربية',
    ],
    'an' => [
        'isoName' => 'Aragonese',
        'nativeName' => 'aragonés',
    ],
    'hy' => [
        'isoName' => 'Armenian',
        'nativeName' => 'Հայերեն',
    ],
    'as' => [
        'isoName' => 'Assamese',
        'nativeName' => 'অসমীয়া',
    ],
    'av' => [
        'isoName' => 'Avaric',
        'nativeName' => 'авар мацӀ, магӀарул мацӀ',
    ],
    'ae' => [
        'isoName' => 'Avestan',
        'nativeName' => 'avesta',
    ],
    'ay' => [
        'isoName' => 'Aymara',
        'nativeName' => 'aymar aru',
    ],
    'az' => [
        'isoName' => 'Azerbaijani',
        'nativeName' => 'azərbaycan dili',
    ],
    'bm' => [
        'isoName' => 'Bambara',
        'nativeName' => 'bamanankan',
    ],
    'ba' => [
        'isoName' => 'Bashkir',
        'nativeName' => 'башҡорт теле',
    ],
    'eu' => [
        'isoName' => 'Basque',
        'nativeName' => 'euskara, euskera',
    ],
    'be' => [
        'isoName' => 'Belarusian',
        'nativeName' => 'беларуская мова',
    ],
    'bn' => [
        'isoName' => 'Bengali',
        'nativeName' => 'বাংলা',
    ],
    'bh' => [
        'isoName' => 'Bihari languages',
        'nativeName' => 'भोजपुरी',
    ],
    'bi' => [
        'isoName' => 'Bislama',
        'nativeName' => 'Bislama',
    ],
    'bs' => [
        'isoName' => 'Bosnian',
        'nativeName' => 'bosanski jezik',
    ],
    'br' => [
        'isoName' => 'Breton',
        'nativeName' => 'brezhoneg',
    ],
    'bg' => [
        'isoName' => 'Bulgarian',
        'nativeName' => 'български език',
    ],
    'my' => [
        'isoName' => 'Burmese',
        'nativeName' => 'ဗမာစာ',
    ],
    'ca' => [
        'isoName' => 'Catalan, Valencian',
        'nativeName' => 'català, valencià',
    ],
    'ch' => [
        'isoName' => 'Chamorro',
        'nativeName' => 'Chamoru',
    ],
    'ce' => [
        'isoName' => 'Chechen',
        'nativeName' => 'нохчийн мотт',
    ],
    'ny' => [
        'isoName' => 'Chichewa, Chewa, Nyanja',
        'nativeName' => 'chiCheŵa, chinyanja',
    ],
    'zh' => [
        'isoName' => 'Chinese',
        'nativeName' => '中文 (Zhōngwén), 汉语, 漢語',
    ],
    'cv' => [
        'isoName' => 'Chuvash',
        'nativeName' => 'чӑваш чӗлхи',
    ],
    'kw' => [
        'isoName' => 'Cornish',
        'nativeName' => 'Kernewek',
    ],
    'co' => [
        'isoName' => 'Corsican',
        'nativeName' => 'corsu, lingua corsa',
    ],
    'cr' => [
        'isoName' => 'Cree',
        'nativeName' => 'ᓀᐦᐃᔭᐍᐏᐣ',
    ],
    'hr' => [
        'isoName' => 'Croatian',
        'nativeName' => 'hrvatski jezik',
    ],
    'cs' => [
        'isoName' => 'Czech',
        'nativeName' => 'čeština, český jazyk',
    ],
    'da' => [
        'isoName' => 'Danish',
        'nativeName' => 'dansk',
    ],
    'dv' => [
        'isoName' => 'Divehi, Dhivehi, Maldivian',
        'nativeName' => 'ދިވެހި',
    ],
    'nl' => [
        'isoName' => 'Dutch, Flemish',
        'nativeName' => 'Nederlands, Vlaams',
    ],
    'dz' => [
        'isoName' => 'Dzongkha',
        'nativeName' => 'རྫོང་ཁ',
    ],
    'en' => [
        'isoName' => 'English',
        'nativeName' => 'English',
    ],
    'eo' => [
        'isoName' => 'Esperanto',
        'nativeName' => 'Esperanto',
    ],
    'et' => [
        'isoName' => 'Estonian',
        'nativeName' => 'eesti, eesti keel',
    ],
    'ee' => [
        'isoName' => 'Ewe',
        'nativeName' => 'Eʋegbe',
    ],
    'fo' => [
        'isoName' => 'Faroese',
        'nativeName' => 'føroyskt',
    ],
    'fj' => [
        'isoName' => 'Fijian',
        'nativeName' => 'vosa Vakaviti',
    ],
    'fi' => [
        'isoName' => 'Finnish',
        'nativeName' => 'suomi, suomen kieli',
    ],
    'fr' => [
        'isoName' => 'French',
        'nativeName' => 'français',
    ],
    'ff' => [
        'isoName' => 'Fulah',
        'nativeName' => 'Fulfulde, Pulaar, Pular',
    ],
    'gl' => [
        'isoName' => 'Galician',
        'nativeName' => 'Galego',
    ],
    'ka' => [
        'isoName' => 'Georgian',
        'nativeName' => 'ქართული',
    ],
    'de' => [
        'isoName' => 'German',
        'nativeName' => 'Deutsch',
    ],
    'el' => [
        'isoName' => 'Greek (modern)',
        'nativeName' => 'ελληνικά',
    ],
    'gn' => [
        'isoName' => 'Guaraní',
        'nativeName' => 'Avañe\'ẽ',
    ],
    'gu' => [
        'isoName' => 'Gujarati',
        'nativeName' => 'ગુજરાતી',
    ],
    'ht' => [
        'isoName' => 'Haitian, Haitian Creole',
        'nativeName' => 'Kreyòl ayisyen',
    ],
    'ha' => [
        'isoName' => 'Hausa',
        'nativeName' => '(Hausa) هَوُسَ',
    ],
    'he' => [
        'isoName' => 'Hebrew (modern)',
        'nativeName' => 'עברית',
    ],
    'hz' => [
        'isoName' => 'Herero',
        'nativeName' => 'Otjiherero',
    ],
    'hi' => [
        'isoName' => 'Hindi',
        'nativeName' => 'हिन्दी, हिंदी',
    ],
    'ho' => [
        'isoName' => 'Hiri Motu',
        'nativeName' => 'Hiri Motu',
    ],
    'hu' => [
        'isoName' => 'Hungarian',
        'nativeName' => 'magyar',
    ],
    'ia' => [
        'isoName' => 'Interlingua',
        'nativeName' => 'Interlingua',
    ],
    'id' => [
        'isoName' => 'Indonesian',
        'nativeName' => 'Bahasa Indonesia',
    ],
    'ie' => [
        'isoName' => 'Interlingue',
        'nativeName' => 'Originally called Occidental; then Interlingue after WWII',
    ],
    'ga' => [
        'isoName' => 'Irish',
        'nativeName' => 'Gaeilge',
    ],
    'ig' => [
        'isoName' => 'Igbo',
        'nativeName' => 'Asụsụ Igbo',
    ],
    'ik' => [
        'isoName' => 'Inupiaq',
        'nativeName' => 'Iñupiaq, Iñupiatun',
    ],
    'io' => [
        'isoName' => 'Ido',
        'nativeName' => 'Ido',
    ],
    'is' => [
        'isoName' => 'Icelandic',
        'nativeName' => 'Íslenska',
    ],
    'it' => [
        'isoName' => 'Italian',
        'nativeName' => 'Italiano',
    ],
    'iu' => [
        'isoName' => 'Inuktitut',
        'nativeName' => 'ᐃᓄᒃᑎᑐᑦ',
    ],
    'ja' => [
        'isoName' => 'Japanese',
        'nativeName' => '日本語 (にほんご)',
    ],
    'jv' => [
        'isoName' => 'Javanese',
        'nativeName' => 'ꦧꦱꦗꦮ, Basa Jawa',
    ],
    'kl' => [
        'isoName' => 'Kalaallisut, Greenlandic',
        'nativeName' => 'kalaallisut, kalaallit oqaasii',
    ],
    'kn' => [
        'isoName' => 'Kannada',
        'nativeName' => 'ಕನ್ನಡ',
    ],
    'kr' => [
        'isoName' => 'Kanuri',
        'nativeName' => 'Kanuri',
    ],
    'ks' => [
        'isoName' => 'Kashmiri',
        'nativeName' => 'कश्मीरी, كشميري‎',
    ],
    'kk' => [
        'isoName' => 'Kazakh',
        'nativeName' => 'қазақ тілі',
    ],
    'km' => [
        'isoName' => 'Central Khmer',
        'nativeName' => 'ខ្មែរ, ខេមរភាសា, ភាសាខ្មែរ',
    ],
    'ki' => [
        'isoName' => 'Kikuyu, Gikuyu',
        'nativeName' => 'Gĩkũyũ',
    ],
    'rw' => [
        'isoName' => 'Kinyarwanda',
        'nativeName' => 'Ikinyarwanda',
    ],
    'ky' => [
        'isoName' => 'Kirghiz, Kyrgyz',
        'nativeName' => 'Кыргызча, Кыргыз тили',
    ],
    'kv' => [
        'isoName' => 'Komi',
        'nativeName' => 'коми кыв',
    ],
    'kg' => [
        'isoName' => 'Kongo',
        'nativeName' => 'Kikongo',
    ],
    'ko' => [
        'isoName' => 'Korean',
        'nativeName' => '한국어',
    ],
    'ku' => [
        'isoName' => 'Kurdish',
        'nativeName' => 'Kurdî, کوردی‎',
    ],
    'kj' => [
        'isoName' => 'Kuanyama, Kwanyama',
        'nativeName' => 'Kuanyama',
    ],
    'la' => [
        'isoName' => 'Latin',
        'nativeName' => 'latine, lingua latina',
    ],
    'lb' => [
        'isoName' => 'Luxembourgish, Letzeburgesch',
        'nativeName' => 'Lëtzebuergesch',
    ],
    'lg' => [
        'isoName' => 'Ganda',
        'nativeName' => 'Luganda',
    ],
    'li' => [
        'isoName' => 'Limburgan, Limburger, Limburgish',
        'nativeName' => 'Limburgs',
    ],
    'ln' => [
        'isoName' => 'Lingala',
        'nativeName' => 'Lingála',
    ],
    'lo' => [
        'isoName' => 'Lao',
        'nativeName' => 'ພາສາລາວ',
    ],
    'lt' => [
        'isoName' => 'Lithuanian',
        'nativeName' => 'lietuvių kalba',
    ],
    'lu' => [
        'isoName' => 'Luba-Katanga',
        'nativeName' => 'Kiluba',
    ],
    'lv' => [
        'isoName' => 'Latvian',
        'nativeName' => 'latviešu valoda',
    ],
    'gv' => [
        'isoName' => 'Manx',
        'nativeName' => 'Gaelg, Gailck',
    ],
    'mk' => [
        'isoName' => 'Macedonian',
        'nativeName' => 'македонски јазик',
    ],
    'mg' => [
        'isoName' => 'Malagasy',
        'nativeName' => 'fiteny malagasy',
    ],
    'ms' => [
        'isoName' => 'Malay',
        'nativeName' => 'Bahasa Melayu, بهاس ملايو‎',
    ],
    'ml' => [
        'isoName' => 'Malayalam',
        'nativeName' => 'മലയാളം',
    ],
    'mt' => [
        'isoName' => 'Maltese',
        'nativeName' => 'Malti',
    ],
    'mi' => [
        'isoName' => 'Maori',
        'nativeName' => 'te reo Māori',
    ],
    'mr' => [
        'isoName' => 'Marathi',
        'nativeName' => 'मराठी',
    ],
    'mh' => [
        'isoName' => 'Marshallese',
        'nativeName' => 'Kajin M̧ajeļ',
    ],
    'mn' => [
        'isoName' => 'Mongolian',
        'nativeName' => 'Монгол хэл',
    ],
    'na' => [
        'isoName' => 'Nauru',
        'nativeName' => 'Dorerin Naoero',
    ],
    'nv' => [
        'isoName' => 'Navajo, Navaho',
        'nativeName' => 'Diné bizaad',
    ],
    'nd' => [
        'isoName' => 'North Ndebele',
        'nativeName' => 'isiNdebele',
    ],
    'ne' => [
        'isoName' => 'Nepali',
        'nativeName' => 'नेपाली',
    ],
    'ng' => [
        'isoName' => 'Ndonga',
        'nativeName' => 'Owambo',
    ],
    'nb' => [
        'isoName' => 'Norwegian Bokmål',
        'nativeName' => 'Norsk Bokmål',
    ],
    'nn' => [
        'isoName' => 'Norwegian Nynorsk',
        'nativeName' => 'Norsk Nynorsk',
    ],
    'no' => [
        'isoName' => 'Norwegian',
        'nativeName' => 'Norsk',
    ],
    'ii' => [
        'isoName' => 'Sichuan Yi, Nuosu',
        'nativeName' => 'ꆈꌠ꒿ Nuosuhxop',
    ],
    'nr' => [
        'isoName' => 'South Ndebele',
        'nativeName' => 'isiNdebele',
    ],
    'oc' => [
        'isoName' => 'Occitan',
        'nativeName' => 'occitan, lenga d\'òc',
    ],
    'oj' => [
        'isoName' => 'Ojibwa',
        'nativeName' => 'ᐊᓂᔑᓈᐯᒧᐎᓐ',
    ],
    'cu' => [
        'isoName' => 'Church Slavic, Church Slavonic, Old Church Slavonic, Old Slavonic, Old Bulgarian',
        'nativeName' => 'ѩзыкъ словѣньскъ',
    ],
    'om' => [
        'isoName' => 'Oromo',
        'nativeName' => 'Afaan Oromoo',
    ],
    'or' => [
        'isoName' => 'Oriya',
        'nativeName' => 'ଓଡ଼ିଆ',
    ],
    'os' => [
        'isoName' => 'Ossetian, Ossetic',
        'nativeName' => 'ирон æвзаг',
    ],
    'pa' => [
        'isoName' => 'Panjabi, Punjabi',
        'nativeName' => 'ਪੰਜਾਬੀ',
    ],
    'pi' => [
        'isoName' => 'Pali',
        'nativeName' => 'पाऴि',
    ],
    'fa' => [
        'isoName' => 'Persian',
        'nativeName' => 'فارسی',
    ],
    'pl' => [
        'isoName' => 'Polish',
        'nativeName' => 'język polski, polszczyzna',
    ],
    'ps' => [
        'isoName' => 'Pashto, Pushto',
        'nativeName' => 'پښتو',
    ],
    'pt' => [
        'isoName' => 'Portuguese',
        'nativeName' => 'Português',
    ],
    'qu' => [
        'isoName' => 'Quechua',
        'nativeName' => 'Runa Simi, Kichwa',
    ],
    'rm' => [
        'isoName' => 'Romansh',
        'nativeName' => 'Rumantsch Grischun',
    ],
    'rn' => [
        'isoName' => 'Rundi',
        'nativeName' => 'Ikirundi',
    ],
    'ro' => [
        'isoName' => 'Romanian, Moldavian, Moldovan',
        'nativeName' => 'Română',
    ],
    'ru' => [
        'isoName' => 'Russian',
        'nativeName' => 'русский',
    ],
    'sa' => [
        'isoName' => 'Sanskrit',
        'nativeName' => 'संस्कृतम्',
    ],
    'sc' => [
        'isoName' => 'Sardinian',
        'nativeName' => 'sardu',
    ],
    'sd' => [
        'isoName' => 'Sindhi',
        'nativeName' => 'सिन्धी, سنڌي، سندھی‎',
    ],
    'se' => [
        'isoName' => 'Northern Sami',
        'nativeName' => 'Davvisámegiella',
    ],
    'sm' => [
        'isoName' => 'Samoan',
        'nativeName' => 'gagana fa\'a Samoa',
    ],
    'sg' => [
        'isoName' => 'Sango',
        'nativeName' => 'yângâ tî sängö',
    ],
    'sr' => [
        'isoName' => 'Serbian',
        'nativeName' => 'српски језик',
    ],
    'gd' => [
        'isoName' => 'Gaelic, Scottish Gaelic',
        'nativeName' => 'Gàidhlig',
    ],
    'sn' => [
        'isoName' => 'Shona',
        'nativeName' => 'chiShona',
    ],
    'si' => [
        'isoName' => 'Sinhala, Sinhalese',
        'nativeName' => 'සිංහල',
    ],
    'sk' => [
        'isoName' => 'Slovak',
        'nativeName' => 'Slovenčina, Slovenský Jazyk',
    ],
    'sl' => [
        'isoName' => 'Slovene',
        'nativeName' => 'Slovenski Jezik, Slovenščina',
    ],
    'so' => [
        'isoName' => 'Somali',
        'nativeName' => 'Soomaaliga, af Soomaali',
    ],
    'st' => [
        'isoName' => 'Southern Sotho',
        'nativeName' => 'Sesotho',
    ],
    'es' => [
        'isoName' => 'Spanish, Castilian',
        'nativeName' => 'Español',
    ],
    'su' => [
        'isoName' => 'Sundanese',
        'nativeName' => 'Basa Sunda',
    ],
    'sw' => [
        'isoName' => 'Swahili',
        'nativeName' => 'Kiswahili',
    ],
    'ss' => [
        'isoName' => 'Swati',
        'nativeName' => 'SiSwati',
    ],
    'sv' => [
        'isoName' => 'Swedish',
        'nativeName' => 'Svenska',
    ],
    'ta' => [
        'isoName' => 'Tamil',
        'nativeName' => 'தமிழ்',
    ],
    'te' => [
        'isoName' => 'Telugu',
        'nativeName' => 'తెలుగు',
    ],
    'tg' => [
        'isoName' => 'Tajik',
        'nativeName' => 'тоҷикӣ, toçikī, تاجیکی‎',
    ],
    'th' => [
        'isoName' => 'Thai',
        'nativeName' => 'ไทย',
    ],
    'ti' => [
        'isoName' => 'Tigrinya',
        'nativeName' => 'ትግርኛ',
    ],
    'bo' => [
        'isoName' => 'Tibetan',
        'nativeName' => 'བོད་ཡིག',
    ],
    'tk' => [
        'isoName' => 'Turkmen',
        'nativeName' => 'Türkmen, Түркмен',
    ],
    'tl' => [
        'isoName' => 'Tagalog',
        'nativeName' => 'Wikang Tagalog',
    ],
    'tn' => [
        'isoName' => 'Tswana',
        'nativeName' => 'Setswana',
    ],
    'to' => [
        'isoName' => 'Tongan (Tonga Islands)',
        'nativeName' => 'Faka Tonga',
    ],
    'tr' => [
        'isoName' => 'Turkish',
        'nativeName' => 'Türkçe',
    ],
    'ts' => [
        'isoName' => 'Tsonga',
        'nativeName' => 'Xitsonga',
    ],
    'tt' => [
        'isoName' => 'Tatar',
        'nativeName' => 'татар теле, tatar tele',
    ],
    'tw' => [
        'isoName' => 'Twi',
        'nativeName' => 'Twi',
    ],
    'ty' => [
        'isoName' => 'Tahitian',
        'nativeName' => 'Reo Tahiti',
    ],
    'ug' => [
        'isoName' => 'Uighur, Uyghur',
        'nativeName' => 'Uyƣurqə, ‫ئۇيغۇرچ',
    ],
    'uk' => [
        'isoName' => 'Ukrainian',
        'nativeName' => 'Українська',
    ],
    'ur' => [
        'isoName' => 'Urdu',
        'nativeName' => 'اردو',
    ],
    'uz' => [
        'isoName' => 'Uzbek',
        'nativeName' => 'Oʻzbek, Ўзбек, أۇزبېك‎',
    ],
    've' => [
        'isoName' => 'Venda',
        'nativeName' => 'Tshivenḓa',
    ],
    'vi' => [
        'isoName' => 'Vietnamese',
        'nativeName' => 'Tiếng Việt',
    ],
    'vo' => [
        'isoName' => 'Volapük',
        'nativeName' => 'Volapük',
    ],
    'wa' => [
        'isoName' => 'Walloon',
        'nativeName' => 'Walon',
    ],
    'cy' => [
        'isoName' => 'Welsh',
        'nativeName' => 'Cymraeg',
    ],
    'wo' => [
        'isoName' => 'Wolof',
        'nativeName' => 'Wollof',
    ],
    'fy' => [
        'isoName' => 'Western Frisian',
        'nativeName' => 'Frysk',
    ],
    'xh' => [
        'isoName' => 'Xhosa',
        'nativeName' => 'isiXhosa',
    ],
    'yi' => [
        'isoName' => 'Yiddish',
        'nativeName' => 'ייִדיש',
    ],
    'yo' => [
        'isoName' => 'Yoruba',
        'nativeName' => 'Yorùbá',
    ],
    'za' => [
        'isoName' => 'Zhuang, Chuang',
        'nativeName' => 'Saɯ cueŋƅ, Saw cuengh',
    ],
    'zu' => [
        'isoName' => 'Zulu',
        'nativeName' => 'isiZulu',
    ],
    /*
     * Add ISO 639-3 languages available in Carbon
     */
    'agq' => [
        'isoName' => 'Aghem',
        'nativeName' => 'Aghem',
    ],
    'agr' => [
        'isoName' => 'Aguaruna',
        'nativeName' => 'Aguaruna',
    ],
    'anp' => [
        'isoName' => 'Angika',
        'nativeName' => 'Angika',
    ],
    'asa' => [
        'isoName' => 'Asu',
        'nativeName' => 'Asu',
    ],
    'ast' => [
        'isoName' => 'Asturian',
        'nativeName' => 'Asturian',
    ],
    'ayc' => [
        'isoName' => 'Southern Aymara',
        'nativeName' => 'Southern Aymara',
    ],
    'bas' => [
        'isoName' => 'Basaa',
        'nativeName' => 'Basaa',
    ],
    'bem' => [
        'isoName' => 'Bemba',
        'nativeName' => 'Bemba',
    ],
    'bez' => [
        'isoName' => 'Bena',
        'nativeName' => 'Bena',
    ],
    'bhb' => [
        'isoName' => 'Bhili',
        'nativeName' => 'Bhili',
    ],
    'bho' => [
        'isoName' => 'Bhojpuri',
        'nativeName' => 'Bhojpuri',
    ],
    'brx' => [
        'isoName' => 'Bodo',
        'nativeName' => 'Bodo',
    ],
    'byn' => [
        'isoName' => 'Bilin',
        'nativeName' => 'Bilin',
    ],
    'ccp' => [
        'isoName' => 'Chakma',
        'nativeName' => 'Chakma',
    ],
    'cgg' => [
        'isoName' => 'Chiga',
        'nativeName' => 'Chiga',
    ],
    'chr' => [
        'isoName' => 'Cherokee',
        'nativeName' => 'Cherokee',
    ],
    'cmn' => [
        'isoName' => 'Chinese',
        'nativeName' => 'Chinese',
    ],
    'crh' => [
        'isoName' => 'Crimean Turkish',
        'nativeName' => 'Crimean Turkish',
    ],
    'csb' => [
        'isoName' => 'Kashubian',
        'nativeName' => 'Kashubian',
    ],
    'dav' => [
        'isoName' => 'Taita',
        'nativeName' => 'Taita',
    ],
    'dje' => [
        'isoName' => 'Zarma',
        'nativeName' => 'Zarma',
    ],
    'doi' => [
        'isoName' => 'Dogri (macrolanguage)',
        'nativeName' => 'Dogri (macrolanguage)',
    ],
    'dsb' => [
        'isoName' => 'Lower Sorbian',
        'nativeName' => 'Lower Sorbian',
    ],
    'dua' => [
        'isoName' => 'Duala',
        'nativeName' => 'Duala',
    ],
    'dyo' => [
        'isoName' => 'Jola-Fonyi',
        'nativeName' => 'Jola-Fonyi',
    ],
    'ebu' => [
        'isoName' => 'Embu',
        'nativeName' => 'Embu',
    ],
    'ewo' => [
        'isoName' => 'Ewondo',
        'nativeName' => 'Ewondo',
    ],
    'fil' => [
        'isoName' => 'Filipino',
        'nativeName' => 'Filipino',
    ],
    'fur' => [
        'isoName' => 'Friulian',
        'nativeName' => 'Friulian',
    ],
    'gez' => [
        'isoName' => 'Geez',
        'nativeName' => 'Geez',
    ],
    'gom' => [
        'isoName' => 'Konkani, Goan',
        'nativeName' => 'ಕೊಂಕಣಿ',
    ],
    'gsw' => [
        'isoName' => 'Swiss German',
        'nativeName' => 'Swiss German',
    ],
    'guz' => [
        'isoName' => 'Gusii',
        'nativeName' => 'Gusii',
    ],
    'hak' => [
        'isoName' => 'Hakka Chinese',
        'nativeName' => 'Hakka Chinese',
    ],
    'haw' => [
        'isoName' => 'Hawaiian',
        'nativeName' => 'Hawaiian',
    ],
    'hif' => [
        'isoName' => 'Fiji Hindi',
        'nativeName' => 'Fiji Hindi',
    ],
    'hne' => [
        'isoName' => 'Chhattisgarhi',
        'nativeName' => 'Chhattisgarhi',
    ],
    'hsb' => [
        'isoName' => 'Upper Sorbian',
        'nativeName' => 'Upper Sorbian',
    ],
    'jgo' => [
        'isoName' => 'Ngomba',
        'nativeName' => 'Ngomba',
    ],
    'jmc' => [
        'isoName' => 'Machame',
        'nativeName' => 'Machame',
    ],
    'kab' => [
        'isoName' => 'Kabyle',
        'nativeName' => 'Kabyle',
    ],
    'kam' => [
        'isoName' => 'Kamba',
        'nativeName' => 'Kamba',
    ],
    'kde' => [
        'isoName' => 'Makonde',
        'nativeName' => 'Makonde',
    ],
    'kea' => [
        'isoName' => 'Kabuverdianu',
        'nativeName' => 'Kabuverdianu',
    ],
    'khq' => [
        'isoName' => 'Koyra Chiini',
        'nativeName' => 'Koyra Chiini',
    ],
    'kkj' => [
        'isoName' => 'Kako',
        'nativeName' => 'Kako',
    ],
    'kln' => [
        'isoName' => 'Kalenjin',
        'nativeName' => 'Kalenjin',
    ],
    'kok' => [
        'isoName' => 'Konkani',
        'nativeName' => 'Konkani',
    ],
    'ksb' => [
        'isoName' => 'Shambala',
        'nativeName' => 'Shambala',
    ],
    'ksf' => [
        'isoName' => 'Bafia',
        'nativeName' => 'Bafia',
    ],
    'ksh' => [
        'isoName' => 'Colognian',
        'nativeName' => 'Colognian',
    ],
    'lag' => [
        'isoName' => 'Langi',
        'nativeName' => 'Langi',
    ],
    'lij' => [
        'isoName' => 'Ligurian',
        'nativeName' => 'Ligurian',
    ],
    'lkt' => [
        'isoName' => 'Lakota',
        'nativeName' => 'Lakota',
    ],
    'lrc' => [
        'isoName' => 'Northern Luri',
        'nativeName' => 'Northern Luri',
    ],
    'luo' => [
        'isoName' => 'Luo',
        'nativeName' => 'Luo',
    ],
    'luy' => [
        'isoName' => 'Luyia',
        'nativeName' => 'Luyia',
    ],
    'lzh' => [
        'isoName' => 'Literary Chinese',
        'nativeName' => 'Literary Chinese',
    ],
    'mag' => [
        'isoName' => 'Magahi',
        'nativeName' => 'Magahi',
    ],
    'mai' => [
        'isoName' => 'Maithili',
        'nativeName' => 'Maithili',
    ],
    'mas' => [
        'isoName' => 'Masai',
        'nativeName' => 'Masai',
    ],
    'mer' => [
        'isoName' => 'Meru',
        'nativeName' => 'Meru',
    ],
    'mfe' => [
        'isoName' => 'Morisyen',
        'nativeName' => 'Morisyen',
    ],
    'mgh' => [
        'isoName' => 'Makhuwa-Meetto',
        'nativeName' => 'Makhuwa-Meetto',
    ],
    'mgo' => [
        'isoName' => 'Metaʼ',
        'nativeName' => 'Metaʼ',
    ],
    'mhr' => [
        'isoName' => 'Eastern Mari',
        'nativeName' => 'Eastern Mari',
    ],
    'miq' => [
        'isoName' => 'Mískito',
        'nativeName' => 'Mískito',
    ],
    'mjw' => [
        'isoName' => 'Karbi',
        'nativeName' => 'Karbi',
    ],
    'mni' => [
        'isoName' => 'Manipuri',
        'nativeName' => 'Manipuri',
    ],
    'mua' => [
        'isoName' => 'Mundang',
        'nativeName' => 'Mundang',
    ],
    'mzn' => [
        'isoName' => 'Mazanderani',
        'nativeName' => 'Mazanderani',
    ],
    'nan' => [
        'isoName' => 'Min Nan Chinese',
        'nativeName' => 'Min Nan Chinese',
    ],
    'naq' => [
        'isoName' => 'Nama',
        'nativeName' => 'Nama',
    ],
    'nds' => [
        'isoName' => 'Low German',
        'nativeName' => 'Low German',
    ],
    'nhn' => [
        'isoName' => 'Central Nahuatl',
        'nativeName' => 'Central Nahuatl',
    ],
    'niu' => [
        'isoName' => 'Niuean',
        'nativeName' => 'Niuean',
    ],
    'nmg' => [
        'isoName' => 'Kwasio',
        'nativeName' => 'Kwasio',
    ],
    'nnh' => [
        'isoName' => 'Ngiemboon',
        'nativeName' => 'Ngiemboon',
    ],
    'nso' => [
        'isoName' => 'Northern Sotho',
        'nativeName' => 'Northern Sotho',
    ],
    'nus' => [
        'isoName' => 'Nuer',
        'nativeName' => 'Nuer',
    ],
    'nyn' => [
        'isoName' => 'Nyankole',
        'nativeName' => 'Nyankole',
    ],
    'pap' => [
        'isoName' => 'Papiamento',
        'nativeName' => 'Papiamento',
    ],
    'prg' => [
        'isoName' => 'Prussian',
        'nativeName' => 'Prussian',
    ],
    'quz' => [
        'isoName' => 'Cusco Quechua',
        'nativeName' => 'Cusco Quechua',
    ],
    'raj' => [
        'isoName' => 'Rajasthani',
        'nativeName' => 'Rajasthani',
    ],
    'rof' => [
        'isoName' => 'Rombo',
        'nativeName' => 'Rombo',
    ],
    'rwk' => [
        'isoName' => 'Rwa',
        'nativeName' => 'Rwa',
    ],
    'sah' => [
        'isoName' => 'Sakha',
        'nativeName' => 'Sakha',
    ],
    'saq' => [
        'isoName' => 'Samburu',
        'nativeName' => 'Samburu',
    ],
    'sat' => [
        'isoName' => 'Santali',
        'nativeName' => 'Santali',
    ],
    'sbp' => [
        'isoName' => 'Sangu',
        'nativeName' => 'Sangu',
    ],
    'scr' => [
        'isoName' => 'Serbo Croatian',
        'nativeName' => 'Serbo Croatian',
    ],
    'seh' => [
        'isoName' => 'Sena',
        'nativeName' => 'Sena',
    ],
    'ses' => [
        'isoName' => 'Koyraboro Senni',
        'nativeName' => 'Koyraboro Senni',
    ],
    'sgs' => [
        'isoName' => 'Samogitian',
        'nativeName' => 'Samogitian',
    ],
    'shi' => [
        'isoName' => 'Tachelhit',
        'nativeName' => 'Tachelhit',
    ],
    'shn' => [
        'isoName' => 'Shan',
        'nativeName' => 'Shan',
    ],
    'shs' => [
        'isoName' => 'Shuswap',
        'nativeName' => 'Shuswap',
    ],
    'sid' => [
        'isoName' => 'Sidamo',
        'nativeName' => 'Sidamo',
    ],
    'smn' => [
        'isoName' => 'Inari Sami',
        'nativeName' => 'Inari Sami',
    ],
    'szl' => [
        'isoName' => 'Silesian',
        'nativeName' => 'Silesian',
    ],
    'tcy' => [
        'isoName' => 'Tulu',
        'nativeName' => 'Tulu',
    ],
    'teo' => [
        'isoName' => 'Teso',
        'nativeName' => 'Teso',
    ],
    'tet' => [
        'isoName' => 'Tetum',
        'nativeName' => 'Tetum',
    ],
    'the' => [
        'isoName' => 'Chitwania Tharu',
        'nativeName' => 'Chitwania Tharu',
    ],
    'tig' => [
        'isoName' => 'Tigre',
        'nativeName' => 'Tigre',
    ],
    'tlh' => [
        'isoName' => 'Klingon',
        'nativeName' => 'tlhIngan Hol',
    ],
    'tpi' => [
        'isoName' => 'Tok Pisin',
        'nativeName' => 'Tok Pisin',
    ],
    'twq' => [
        'isoName' => 'Tasawaq',
        'nativeName' => 'Tasawaq',
    ],
    'tzl' => [
        'isoName' => 'Talossan',
        'nativeName' => 'Talossan',
    ],
    'tzm' => [
        'isoName' => 'Tamazight, Central Atlas',
        'nativeName' => 'ⵜⵎⴰⵣⵉⵖⵜ',
    ],
    'unm' => [
        'isoName' => 'Unami',
        'nativeName' => 'Unami',
    ],
    'vai' => [
        'isoName' => 'Vai',
        'nativeName' => 'Vai',
    ],
    'vun' => [
        'isoName' => 'Vunjo',
        'nativeName' => 'Vunjo',
    ],
    'wae' => [
        'isoName' => 'Walser',
        'nativeName' => 'Walser',
    ],
    'wal' => [
        'isoName' => 'Wolaytta',
        'nativeName' => 'Wolaytta',
    ],
    'xog' => [
        'isoName' => 'Soga',
        'nativeName' => 'Soga',
    ],
    'yav' => [
        'isoName' => 'Yangben',
        'nativeName' => 'Yangben',
    ],
    'yue' => [
        'isoName' => 'Cantonese',
        'nativeName' => 'Cantonese',
    ],
    'yuw' => [
        'isoName' => 'Yau (Morobe Province)',
        'nativeName' => 'Yau (Morobe Province)',
    ],
    'zgh' => [
        'isoName' => 'Standard Moroccan Tamazight',
        'nativeName' => 'Standard Moroccan Tamazight',
    ],
];
