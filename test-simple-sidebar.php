<!DOCTYPE html>
<html lang="id" data-bs-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Clean Sidebar - KeuanganKu</title>

    <!-- CSS Libraries -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="assets/css/layout-manager.css?v=<?= time() ?>" rel="stylesheet">
    <link href="assets/css/control-sidebar.css?v=<?= time() ?>" rel="stylesheet">
</head>
<body>
    <?php 
    // Simulate current page for testing
    $currentPage = 'dashboard';
    $currentUser = [
        'id' => 1,
        'nama' => '<PERSON>e',
        'role' => 'admin'
    ];
    ?>

    <!-- Simple Clean Sidebar -->
    <aside class="modern-sidebar">
        <!-- Simple Header -->
        <div class="sidebar-header">
            <div class="brand-container">
                <div class="brand-logo">
                    <div class="logo-icon">
                        <i class="fas fa-wallet"></i>
                    </div>
                </div>
                <div class="brand-content">
                    <h1 class="brand-title">KeuanganKu</h1>
                    <p class="brand-subtitle">Financial Manager</p>
                </div>
            </div>
        </div>

        <!-- Simple User Profile -->
        <div class="sidebar-user">
            <div class="user-profile">
                <div class="user-avatar">
                    <img src="https://ui-avatars.com/api/?name=John+Doe&background=3b82f6&color=fff&size=40" alt="User Avatar" class="avatar-img">
                    <div class="status-indicator"></div>
                </div>
                <div class="user-details">
                    <h6 class="user-name">John Doe</h6>
                    <span class="user-role">Admin</span>
                </div>
            </div>
        </div>

        <!-- Navigation Menu -->
        <div class="sidebar-navigation">
            <nav class="nav-menu">
                <!-- Main Section -->
                <div class="menu-section">
                    <div class="menu-section-title">Main</div>
                    
                    <a class="menu-item active" href="/keuangan/dashboard.php" data-tooltip="Dashboard">
                        <div class="menu-icon">
                            <i class="fas fa-home"></i>
                        </div>
                        <span class="menu-text">Dashboard</span>
                    </a>

                    <a class="menu-item" href="analytics.php" data-tooltip="Analytics">
                        <div class="menu-icon">
                            <i class="fas fa-chart-bar"></i>
                        </div>
                        <span class="menu-text">Analytics</span>
                        <span class="menu-badge">5</span>
                    </a>
                </div>

                <!-- Keuangan Section -->
                <div class="menu-section">
                    <div class="menu-section-title">Keuangan</div>
                    
                    <button class="menu-item" data-bs-toggle="collapse" data-bs-target="#keuanganSubmenu" data-tooltip="Keuangan">
                        <div class="menu-icon">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                        <span class="menu-text">Keuangan</span>
                        <i class="fas fa-chevron-down menu-arrow"></i>
                    </button>
                    <div class="collapse submenu" id="keuanganSubmenu">
                        <a class="submenu-item" href="transaksi.php">
                            <div class="submenu-icon">
                                <i class="fas fa-exchange-alt"></i>
                            </div>
                            Transaksi
                        </a>
                        <a class="submenu-item" href="kategori.php">
                            <div class="submenu-icon">
                                <i class="fas fa-tags"></i>
                            </div>
                            Kategori
                        </a>
                        <a class="submenu-item" href="target.php">
                            <div class="submenu-icon">
                                <i class="fas fa-bullseye"></i>
                            </div>
                            Target
                        </a>
                    </div>
                </div>

                <!-- Laporan Section -->
                <div class="menu-section">
                    <div class="menu-section-title">Laporan</div>
                    
                    <button class="menu-item" data-bs-toggle="collapse" data-bs-target="#laporanSubmenu" data-tooltip="Laporan">
                        <div class="menu-icon">
                            <i class="fas fa-file-alt"></i>
                        </div>
                        <span class="menu-text">Laporan</span>
                        <i class="fas fa-chevron-down menu-arrow"></i>
                    </button>
                    <div class="collapse submenu" id="laporanSubmenu">
                        <a class="submenu-item" href="laporan.php">
                            <div class="submenu-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            Laporan Keuangan
                        </a>
                        <a class="submenu-item" href="analisis.php">
                            <div class="submenu-icon">
                                <i class="fas fa-analytics"></i>
                            </div>
                            Analisis
                        </a>
                    </div>
                </div>

                <!-- Settings Section -->
                <div class="menu-section">
                    <div class="menu-section-title">Settings</div>
                    
                    <a class="menu-item" href="profile.php" data-tooltip="Profile">
                        <div class="menu-icon">
                            <i class="fas fa-user"></i>
                        </div>
                        <span class="menu-text">Profile</span>
                    </a>

                    <a class="menu-item" href="settings.php" data-tooltip="Settings">
                        <div class="menu-icon">
                            <i class="fas fa-cog"></i>
                        </div>
                        <span class="menu-text">Settings</span>
                    </a>
                </div>
            </nav>
        </div>

        <!-- Sidebar Footer -->
        <div class="sidebar-footer">
            <div class="sidebar-footer-content">
                <button class="sidebar-toggle-btn" data-bs-toggle="tooltip" title="Toggle Sidebar">
                    <i class="fas fa-chevron-left"></i>
                </button>
                <button class="theme-toggle-btn" onclick="toggleTheme()" data-bs-toggle="tooltip" title="Toggle Dark Mode">
                    <i class="fas fa-moon"></i>
                </button>
            </div>
        </div>
    </aside>

    <!-- Sidebar Overlay for Mobile -->
    <div class="sidebar-overlay"></div>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Navbar -->
        <nav class="navbar navbar-expand-lg modern-navbar">
            <div class="container-fluid">
                <!-- Mobile Sidebar Toggle -->
                <button class="mobile-sidebar-toggle d-lg-none" type="button">
                    <i class="fas fa-bars"></i>
                </button>

                <!-- Brand -->
                <div class="navbar-brand">
                    <div class="brand-logo">
                        <i class="fas fa-sparkles" style="color: #10b981;"></i>
                    </div>
                    <span style="color: #10b981; font-weight: 600;">Simple & Clean Design</span>
                </div>

                <!-- Controls -->
                <div class="navbar-nav ms-auto">
                    <button class="nav-link btn btn-link me-2" onclick="toggleTheme()">
                        <i class="fas fa-moon"></i>
                    </button>
                    <button class="nav-link btn btn-link" onclick="window.modernSidebar?.toggle()">
                        <i class="fas fa-cogs"></i>
                    </button>
                </div>
            </div>
        </nav>

        <!-- Content -->
        <div class="content-container">
            <div class="container-fluid">
                <!-- Header -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h4 class="card-title mb-0">
                                    <i class="fas fa-sparkles me-2 text-success"></i>Simple & Clean Sidebar Design
                                </h4>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-success">
                                    <h6><i class="fas fa-check-circle me-2"></i>Design Improvements:</h6>
                                    <ul class="mb-0">
                                        <li><strong>Clean & Simple:</strong> Removed complex animations and effects</li>
                                        <li><strong>Better Readability:</strong> Clear text colors and proper contrast</li>
                                        <li><strong>Consistent Styling:</strong> Unified color scheme and spacing</li>
                                        <li><strong>Responsive Design:</strong> Works perfectly on all devices</li>
                                        <li><strong>Fast Performance:</strong> Lightweight without heavy effects</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Features Showcase -->
                <div class="row">
                    <div class="col-md-4 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="card-title mb-0">
                                    <i class="fas fa-eye me-2"></i>Visual Clarity
                                </h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled mb-0">
                                    <li><i class="fas fa-check text-success me-2"></i>Clear text colors</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Proper contrast ratios</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Readable font sizes</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Clean backgrounds</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="card-title mb-0">
                                    <i class="fas fa-mobile-alt me-2"></i>Responsive
                                </h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled mb-0">
                                    <li><i class="fas fa-check text-success me-2"></i>Mobile optimized</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Touch friendly</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Adaptive layout</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Smooth transitions</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="card-title mb-0">
                                    <i class="fas fa-tachometer-alt me-2"></i>Performance
                                </h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled mb-0">
                                    <li><i class="fas fa-check text-success me-2"></i>Lightweight CSS</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Fast loading</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Minimal animations</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Optimized code</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Test Controls -->
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="card-title mb-0">
                                    <i class="fas fa-gamepad me-2"></i>Test Controls
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row g-2">
                                    <div class="col-md-3">
                                        <button class="btn btn-primary w-100" onclick="window.modernSidebar?.toggle()">
                                            <i class="fas fa-arrows-alt-h me-1"></i>Toggle Sidebar
                                        </button>
                                    </div>
                                    <div class="col-md-3">
                                        <button class="btn btn-success w-100" onclick="window.modernSidebar?.expand()">
                                            <i class="fas fa-expand me-1"></i>Expand
                                        </button>
                                    </div>
                                    <div class="col-md-3">
                                        <button class="btn btn-warning w-100" onclick="window.modernSidebar?.collapse()">
                                            <i class="fas fa-compress me-1"></i>Collapse
                                        </button>
                                    </div>
                                    <div class="col-md-3">
                                        <button class="btn btn-info w-100" onclick="toggleTheme()">
                                            <i class="fas fa-palette me-1"></i>Dark Mode
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Navigation Test -->
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="card-title mb-0">
                                    <i class="fas fa-link me-2"></i>Navigation Test
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row g-2">
                                    <div class="col-md-2">
                                        <a href="/keuangan/dashboard.php" class="btn btn-outline-primary w-100">Dashboard</a>
                                    </div>
                                    <div class="col-md-2">
                                        <a href="transaksi.php" class="btn btn-outline-success w-100">Transaksi</a>
                                    </div>
                                    <div class="col-md-2">
                                        <a href="kategori.php" class="btn btn-outline-warning w-100">Kategori</a>
                                    </div>
                                    <div class="col-md-2">
                                        <a href="laporan.php" class="btn btn-outline-info w-100">Laporan</a>
                                    </div>
                                    <div class="col-md-2">
                                        <a href="profile.php" class="btn btn-outline-secondary w-100">Profile</a>
                                    </div>
                                    <div class="col-md-2">
                                        <a href="settings.php" class="btn btn-outline-dark w-100">Settings</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Active Menu Detection Script -->
    <script>
    // Pass current page info to JavaScript
    window.currentPageInfo = {
        page: '<?= $currentPage ?>',
        phpActiveSet: <?= json_encode(isset($currentPage)) ?>
    };

    // Ensure active menu is preserved after sidebar initialization
    document.addEventListener('DOMContentLoaded', function() {
        // Wait for modern sidebar to initialize
        setTimeout(function() {
            if (window.modernSidebar) {
                // Only refresh if no active menu is already set by PHP
                const existingActive = document.querySelector('.menu-item.active, .submenu-item.active');
                if (!existingActive) {
                    window.modernSidebar.refreshActiveMenu();
                    console.log('🔄 No active menu found, refreshing for current page');
                } else {
                    // Just ensure submenu is open if needed
                    window.modernSidebar.ensureSubmenuOpen(existingActive);
                    console.log('✅ Active menu preserved from PHP:', existingActive.textContent.trim());
                }
            }
        }, 150);
    });

    // Handle browser navigation (back/forward)
    window.addEventListener('popstate', function() {
        if (window.modernSidebar) {
            setTimeout(function() {
                window.modernSidebar.refreshActiveMenu();
            }, 50);
        }
    });
    </script>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/modern-sidebar.js?v=<?= time() ?>"></script>
    <script src="assets/js/theme-manager.js?v=<?= time() ?>"></script>

    <script>
        // Initialize tooltips
        document.addEventListener('DOMContentLoaded', function() {
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        });
    </script>
</body>
</html>
