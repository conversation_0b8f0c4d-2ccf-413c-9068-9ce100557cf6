<?php
require_once 'includes/config/database.php';
require_once 'includes/helpers/functions.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || getCurrentUser()['role'] !== 'admin') {
    setFlashMessage('danger', 'Aks<PERSON> ditolak! Anda tidak memiliki izin untuk mengakses halaman ini.');
    redirect('index.php');
}

$currentPage = 'settings';
$pageTitle = 'Pengaturan Sistem';

// Handle settings update
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $settings = [
            'site_name' => $_POST['site_name'] ?? 'KeuanganKu',
            'site_description' => $_POST['site_description'] ?? '',
            'currency' => $_POST['currency'] ?? 'IDR',
            'date_format' => $_POST['date_format'] ?? 'd/m/Y',
            'time_format' => $_POST['time_format'] ?? 'H:i:s',
            'maintenance_mode' => isset($_POST['maintenance_mode']) ? 1 : 0,
            'enable_registration' => isset($_POST['enable_registration']) ? 1 : 0,
            'default_role' => $_POST['default_role'] ?? 'user',
            'session_timeout' => (int)($_POST['session_timeout'] ?? 30),
            'max_login_attempts' => (int)($_POST['max_login_attempts'] ?? 5),
            'lockout_time' => (int)($_POST['lockout_time'] ?? 15)
        ];
        
        // Update settings in database
        $stmt = $pdo->prepare("UPDATE settings SET value = ? WHERE name = ?");
        foreach ($settings as $key => $value) {
            $stmt->execute([json_encode($value), $key]);
        }
        
        setFlashMessage('success', 'Pengaturan berhasil diperbarui!');
        redirect('settings.php');
    } catch (PDOException $e) {
        setFlashMessage('danger', 'Error: ' . $e->getMessage());
    }
}

// Get current settings
$stmt = $pdo->query("SELECT * FROM settings");
$settings = [];
while ($row = $stmt->fetch()) {
    $settings[$row['name']] = json_decode($row['value'], true);
}

include 'includes/views/layouts/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Pengaturan Sistem</h5>
                </div>
                <div class="card-body">
                    <?php showFlashMessages(); ?>
                    
                    <form method="POST" class="needs-validation" novalidate>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="card-title mb-0">Pengaturan Umum</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label class="form-label">Nama Situs</label>
                                            <input type="text" class="form-control" name="site_name" 
                                                   value="<?= htmlspecialchars($settings['site_name'] ?? '') ?>" required>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label class="form-label">Deskripsi Situs</label>
                                            <textarea class="form-control" name="site_description" rows="3"><?= 
                                                htmlspecialchars($settings['site_description'] ?? '') 
                                            ?></textarea>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label class="form-label">Mata Uang</label>
                                            <select class="form-select" name="currency">
                                                <option value="IDR" <?= ($settings['currency'] ?? '') === 'IDR' ? 'selected' : '' ?>>IDR</option>
                                                <option value="USD" <?= ($settings['currency'] ?? '') === 'USD' ? 'selected' : '' ?>>USD</option>
                                            </select>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label class="form-label">Format Tanggal</label>
                                            <select class="form-select" name="date_format">
                                                <option value="d/m/Y" <?= ($settings['date_format'] ?? '') === 'd/m/Y' ? 'selected' : '' ?>>DD/MM/YYYY</option>
                                                <option value="Y-m-d" <?= ($settings['date_format'] ?? '') === 'Y-m-d' ? 'selected' : '' ?>>YYYY-MM-DD</option>
                                                <option value="d-m-Y" <?= ($settings['date_format'] ?? '') === 'd-m-Y' ? 'selected' : '' ?>>DD-MM-YYYY</option>
                                            </select>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label class="form-label">Format Waktu</label>
                                            <select class="form-select" name="time_format">
                                                <option value="H:i:s" <?= ($settings['time_format'] ?? '') === 'H:i:s' ? 'selected' : '' ?>>24 Jam (HH:MM:SS)</option>
                                                <option value="h:i:s A" <?= ($settings['time_format'] ?? '') === 'h:i:s A' ? 'selected' : '' ?>>12 Jam (HH:MM:SS AM/PM)</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="card-title mb-0">Pengaturan Keamanan</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" name="maintenance_mode" 
                                                       <?= ($settings['maintenance_mode'] ?? 0) ? 'checked' : '' ?>>
                                                <label class="form-check-label">Mode Maintenance</label>
                                            </div>
                                            <small class="text-muted">Aktifkan untuk menutup akses ke sistem</small>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" name="enable_registration" 
                                                       <?= ($settings['enable_registration'] ?? 0) ? 'checked' : '' ?>>
                                                <label class="form-check-label">Aktifkan Registrasi</label>
                                            </div>
                                            <small class="text-muted">Izinkan pengguna baru untuk mendaftar</small>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label class="form-label">Role Default</label>
                                            <select class="form-select" name="default_role">
                                                <option value="user" <?= ($settings['default_role'] ?? '') === 'user' ? 'selected' : '' ?>>User</option>
                                                <option value="admin" <?= ($settings['default_role'] ?? '') === 'admin' ? 'selected' : '' ?>>Admin</option>
                                            </select>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label class="form-label">Timeout Sesi (menit)</label>
                                            <input type="number" class="form-control" name="session_timeout" 
                                                   value="<?= $settings['session_timeout'] ?? 30 ?>" min="1" max="1440">
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label class="form-label">Maksimal Percobaan Login</label>
                                            <input type="number" class="form-control" name="max_login_attempts" 
                                                   value="<?= $settings['max_login_attempts'] ?? 5 ?>" min="1" max="10">
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label class="form-label">Waktu Blokir (menit)</label>
                                            <input type="number" class="form-control" name="lockout_time" 
                                                   value="<?= $settings['lockout_time'] ?? 15 ?>" min="1" max="1440">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="text-end mt-3">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Simpan Pengaturan
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Form validation
(function() {
    'use strict';
    var forms = document.querySelectorAll('.needs-validation');
    Array.prototype.slice.call(forms).forEach(function(form) {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        }, false);
    });
})();
</script>

<?php include 'includes/views/layouts/footer.php'; ?> 