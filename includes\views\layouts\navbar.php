<?php
$currentUser = getCurrentUser();

// Get unread notifications count from system_notifications (same as notifications.php)
$unreadNotifications = 0;
try {
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM system_notifications WHERE is_read = FALSE OR is_read = 0");
    $stmt->execute();
    $unreadNotifications = $stmt->fetchColumn() ?: 0;
} catch (PDOException $e) {
    error_log("Error getting notifications count: " . $e->getMessage());
    $unreadNotifications = 0;
}
?>

<nav class="modern-navbar navbar navbar-expand-lg" id="mainNavbar">
    <div class="container-fluid">
        <!-- Modern Sidebar Toggle Button -->
        <button class="modern-sidebar-toggle-btn d-none d-lg-flex" type="button" id="sidebarToggleBtn"
                data-bs-toggle="tooltip" data-bs-placement="bottom" title="Toggle Sidebar">
            <div class="toggle-icon-container">
                <span class="modern-hamburger-line"></span>
                <span class="modern-hamburger-line"></span>
                <span class="modern-hamburger-line"></span>
            </div>
            <div class="toggle-ripple"></div>
        </button>

        <!-- Modern Mobile Sidebar Toggle -->
        <button class="modern-mobile-sidebar-toggle d-lg-none" type="button" id="mobileSidebarToggle"
                data-bs-toggle="tooltip" data-bs-placement="bottom" title="Open Menu">
            <div class="mobile-toggle-icon">
                <i class="fas fa-bars"></i>
            </div>
            <div class="mobile-toggle-ripple"></div>
        </button>

        <!-- Dynamic Greeting -->
        <div class="navbar-brand d-flex align-items-center">
            <div class="brand-logo">
                <i class="fas fa-sun" id="greetingIcon"></i>
            </div>
            <div class="brand-text d-none d-sm-block">
                <span class="brand-name" id="dynamicGreeting">Selamat Pagi</span>
                <small class="brand-subtitle"><?= htmlspecialchars($currentUser['nama'] ?? $currentUser['username'] ?? 'User') ?></small>
            </div>
        </div>

        <!-- Inline Script for Immediate Greeting Update -->
        <script>
        (function() {
            function immediateGreetingUpdate() {
                const now = new Date();
                const hour = now.getHours();
                const greetingElement = document.getElementById('dynamicGreeting');
                const greetingIcon = document.getElementById('greetingIcon');

                let greeting = '';
                let icon = '';
                let color = '';

                if (hour >= 5 && hour < 12) {
                    greeting = 'Selamat Pagi';
                    icon = 'fas fa-sun';
                    color = '#ff9800';
                } else if (hour >= 12 && hour < 15) {
                    greeting = 'Selamat Siang';
                    icon = 'fas fa-sun';
                    color = '#ffc107';
                } else if (hour >= 15 && hour < 18) {
                    greeting = 'Selamat Sore';
                    icon = 'fas fa-cloud-sun';
                    color = '#ff5722';
                } else {
                    greeting = 'Selamat Malam';
                    icon = 'fas fa-moon';
                    color = '#3f51b5';
                }

                if (greetingElement) {
                    greetingElement.textContent = greeting;
                    greetingElement.style.color = color;
                }

                if (greetingIcon) {
                    greetingIcon.className = icon;
                    greetingIcon.style.color = color;
                }

                console.log('Immediate greeting update:', greeting, 'at hour', hour);
            }

            // Run immediately
            immediateGreetingUpdate();

            // Run again after a short delay
            setTimeout(immediateGreetingUpdate, 100);
        })();
        </script>



        <!-- Search Bar (Desktop) -->
        <div class="navbar-search d-none d-lg-flex">
            <div class="search-container">
                <i class="fas fa-search search-icon"></i>
                <input type="text" class="search-input" placeholder="Search transactions, categories..." id="globalSearch">
                <div class="search-results" id="searchResults"></div>
            </div>
        </div>

        <!-- Mobile Menu Toggle Button -->
        <button class="navbar-toggler border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <i class="fas fa-ellipsis-v"></i>
        </button>

        <!-- Navbar Content -->
        <div class="collapse navbar-collapse" id="navbarNav">
            <!-- Mobile Search -->
            <div class="navbar-search-mobile d-lg-none mb-3">
                <div class="search-container">
                    <i class="fas fa-search search-icon"></i>
                    <input type="text" class="search-input" placeholder="Search..." id="mobileGlobalSearch">
                </div>
            </div>

            <ul class="navbar-nav ms-auto align-items-center">
                <!-- Quick Actions -->
                <li class="nav-item dropdown me-2">
                    <button class="nav-link btn btn-link" type="button" data-bs-toggle="dropdown"
                            data-bs-toggle="tooltip" title="Quick Actions">
                        <i class="fas fa-plus-circle"></i>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end quick-actions-dropdown">
                        <li><h6 class="dropdown-header">Quick Actions</h6></li>
                        <li><a class="dropdown-item" href="transaksi.php">
                            <i class="fas fa-plus me-2 text-success"></i>Add Transaction
                        </a></li>
                        <li><a class="dropdown-item" href="kategori.php">
                            <i class="fas fa-tag me-2 text-info"></i>Add Category
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="laporan.php">
                            <i class="fas fa-chart-bar me-2 text-primary"></i>View Reports
                        </a></li>
                    </ul>
                </li>

                <!-- Control Sidebar Toggle -->
                <li class="nav-item me-2">
                    <button class="nav-link btn btn-link" type="button" data-widget="control-sidebar"
                            id="controlSidebarToggleBtn"
                            data-bs-toggle="tooltip" title="Customize Layout">
                        <i class="fas fa-cogs"></i>
                    </button>
                </li>

                <!-- Dark Mode Toggle -->
                <li class="nav-item me-2">
                    <button class="nav-link btn btn-link" type="button" onclick="toggleTheme()"
                            data-theme-toggle data-bs-toggle="tooltip" title="Toggle Dark Mode">
                        <i class="fas fa-moon"></i>
                        <span class="d-none d-md-inline ms-1">Dark</span>
                    </button>
                </li>

                <!-- Notifications -->
                <li class="nav-item dropdown me-2">
                    <button class="nav-link btn btn-link position-relative" type="button"
                            id="notificationsDropdown" data-bs-toggle="dropdown"
                            data-bs-toggle="tooltip" title="Notifications">
                        <i class="fas fa-bell"></i>
                        <?php if ($unreadNotifications > 0): ?>
                            <span class="notification-badge"><?= $unreadNotifications ?></span>
                        <?php endif; ?>
                    </button>
                    <div class="dropdown-menu dropdown-menu-end notifications-dropdown" aria-labelledby="notificationsDropdown">
                        <div class="dropdown-header d-flex justify-content-between align-items-center">
                            <h6 class="mb-0">Notifikasi</h6>
                            <?php if ($unreadNotifications > 0): ?>
                                <a href="notifications.php" class="text-primary small">Tandai semua dibaca</a>
                            <?php endif; ?>
                        </div>
                        <div class="notifications-list" id="notificationsList">
                            <?php
                            try {
                                // Get notifications from system_notifications table (same as notifications.php)
                                $stmt = $pdo->prepare("
                                    SELECT * FROM system_notifications
                                    ORDER BY created_at DESC
                                    LIMIT 5
                                ");
                                $stmt->execute();
                                $navbarNotifications = $stmt->fetchAll();

                                if (empty($navbarNotifications)): ?>
                                    <div class="empty-notifications text-center py-4">
                                        <i class="fas fa-bell-slash text-muted mb-3" style="font-size: 2rem;"></i>
                                        <h6 class="text-muted">No notifications</h6>
                                        <p class="text-muted small mb-0">You're all caught up!</p>
                                    </div>
                                <?php else:
                                    foreach ($navbarNotifications as $notification):
                                        // Ensure all fields exist with defaults
                                        $type = $notification['type'] ?? 'info';
                                        $title = $notification['title'] ?? 'No Title';
                                        $message = $notification['message'] ?? 'No Message';
                                        $source = $notification['source'] ?? '';
                                        $is_read = $notification['is_read'] ?? false;
                                        $created_at = $notification['created_at'] ?? date('Y-m-d H:i:s');
                                        $id = $notification['id'] ?? 0;

                                        // Determine icon based on type
                                        $iconClass = 'fas fa-info-circle text-info';
                                        if ($type === 'error') {
                                            $iconClass = 'fas fa-exclamation-triangle text-danger';
                                        } elseif ($type === 'warning') {
                                            $iconClass = 'fas fa-exclamation-circle text-warning';
                                        } elseif ($type === 'success') {
                                            $iconClass = 'fas fa-check-circle text-success';
                                        }
                                        ?>
                                        <a class="dropdown-item notification-item <?= !$is_read ? 'unread' : '' ?>"
                                           href="notifications.php" data-id="<?= $id ?>" data-type="<?= $type ?>">
                                            <div class="d-flex align-items-start">
                                                <div class="notification-icon me-3">
                                                    <i class="<?= $iconClass ?>"></i>
                                                </div>
                                                <div class="notification-content flex-grow-1">
                                                    <p class="mb-1 fw-medium notification-title"><?= htmlspecialchars($title) ?></p>
                                                    <p class="mb-1 small text-muted notification-message"><?= htmlspecialchars(substr($message, 0, 60)) ?><?= strlen($message) > 60 ? '...' : '' ?></p>
                                                    <small class="text-muted">
                                                        <i class="fas fa-clock me-1"></i>
                                                        <?= date('M d, H:i', strtotime($created_at)) ?>
                                                        <?php if ($source): ?>
                                                            | <i class="fas fa-tag me-1"></i><?= htmlspecialchars($source) ?>
                                                        <?php endif; ?>
                                                    </small>
                                                </div>
                                                <?php if (!$is_read): ?>
                                                    <div class="unread-indicator"></div>
                                                <?php endif; ?>
                                            </div>
                                        </a>
                                    <?php endforeach;
                                endif;
                            } catch (PDOException $e) {
                                error_log("Error getting notifications: " . $e->getMessage());
                                ?>
                                <div class="text-center py-3">
                                    <i class="fas fa-exclamation-triangle text-warning mb-2"></i>
                                    <p class="text-muted mb-0">Error loading notifications</p>
                                    <small class="text-muted d-block">Check system logs for details</small>
                                </div>
                                <?php
                            }
                            ?>
                        </div>
                        <div class="dropdown-footer">
                            <a href="notifications.php" class="dropdown-item text-center fw-medium">
                                <i class="fas fa-eye me-1"></i>View All Notifications
                            </a>
                        </div>
                    </div>
                </li>

                <!-- User Profile -->
                <li class="nav-item dropdown">
                    <button class="nav-link modern-user-profile-btn d-flex align-items-center" type="button"
                            id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                        <div class="modern-user-avatar me-2">
                            <img src="https://ui-avatars.com/api/?name=<?= urlencode($currentUser['nama'] ?? 'User') ?>&background=2563eb&color=fff&size=36"
                                 alt="User Avatar" class="rounded-circle">
                            <div class="status-indicator"></div>
                        </div>
                        <div class="modern-user-info d-none d-lg-block">
                            <span class="modern-user-name"><?= htmlspecialchars($currentUser['nama'] ?? 'User') ?></span>
                            <small class="modern-user-role"><?= ucfirst($currentUser['role'] ?? 'User') ?></small>
                        </div>
                        <div class="dropdown-arrow-container ms-2">
                            <i class="fas fa-chevron-down dropdown-arrow"></i>
                        </div>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end ultra-modern-profile-dropdown" aria-labelledby="userDropdown">
                        <li class="modern-dropdown-header">
                            <div class="modern-profile-header d-flex align-items-center">
                                <div class="modern-profile-avatar me-3">
                                    <img src="https://ui-avatars.com/api/?name=<?= urlencode($currentUser['nama'] ?? 'User') ?>&background=2563eb&color=fff&size=52"
                                         alt="User Avatar" class="rounded-circle">
                                    <div class="modern-online-indicator"></div>
                                </div>
                                <div class="modern-profile-info">
                                    <h6 class="modern-profile-name mb-1"><?= htmlspecialchars($currentUser['nama'] ?? 'User') ?></h6>
                                    <small class="modern-profile-email text-muted"><?= htmlspecialchars($currentUser['email'] ?? '<EMAIL>') ?></small>
                                    <div class="modern-profile-role-badge mt-1">
                                        <i class="fas fa-crown me-1"></i>
                                        <?= ucfirst($currentUser['role'] ?? 'User') ?>
                                    </div>
                                </div>
                            </div>
                        </li>
                        <li><hr class="modern-dropdown-divider"></li>
                        <li>
                            <a class="dropdown-item ultra-modern-dropdown-item" href="profile.php">
                                <div class="modern-item-icon">
                                    <i class="fas fa-user-circle"></i>
                                </div>
                                <div class="modern-item-content">
                                    <span class="modern-item-title">My Profile</span>
                                    <small class="modern-item-subtitle">View and edit your profile</small>
                                </div>
                                <div class="modern-item-arrow">
                                    <i class="fas fa-chevron-right"></i>
                                </div>
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item ultra-modern-dropdown-item" href="settings.php">
                                <div class="modern-item-icon">
                                    <i class="fas fa-cog"></i>
                                </div>
                                <div class="modern-item-content">
                                    <span class="modern-item-title">Settings</span>
                                    <small class="modern-item-subtitle">Preferences & security</small>
                                </div>
                                <div class="modern-item-arrow">
                                    <i class="fas fa-chevron-right"></i>
                                </div>
                            </a>
                        </li>
                        <?php if ($currentUser['role'] === 'admin'): ?>
                        <li>
                            <a class="dropdown-item ultra-modern-dropdown-item admin-item" href="admin-dashboard.php">
                                <div class="modern-item-icon admin-icon">
                                    <i class="fas fa-shield-alt"></i>
                                </div>
                                <div class="modern-item-content">
                                    <span class="modern-item-title">Admin Dashboard</span>
                                    <small class="modern-item-subtitle">System management</small>
                                </div>
                                <div class="modern-item-arrow">
                                    <i class="fas fa-chevron-right"></i>
                                </div>
                            </a>
                        </li>
                        <?php endif; ?>
                        <li><hr class="modern-dropdown-divider"></li>
                        <li>
                            <a class="dropdown-item ultra-modern-dropdown-item logout-item" href="logout.php"
                               onclick="return confirm('Are you sure you want to logout?')">
                                <div class="modern-item-icon logout-icon">
                                    <i class="fas fa-sign-out-alt"></i>
                                </div>
                                <div class="modern-item-content">
                                    <span class="modern-item-title logout-title">Logout</span>
                                    <small class="modern-item-subtitle">Sign out of your account</small>
                                </div>
                                <div class="modern-item-arrow logout-arrow">
                                    <i class="fas fa-external-link-alt"></i>
                                </div>
                            </a>
                        </li>
                    </ul>
                </li>
            </ul>
        </div>
    </div>
</nav>

<style>
/* Modern Navbar Styles */
.modern-navbar {
    height: var(--navbar-height);
    background-color: var(--navbar-bg);
    border-bottom: 1px solid var(--navbar-border);
    box-shadow: var(--shadow-sm);
    position: fixed;
    top: 0;
    right: 0;
    left: var(--sidebar-width);
    z-index: 999;
    transition: all var(--transition-normal);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.sidebar-collapsed .modern-navbar {
    left: var(--sidebar-collapsed-width);
    width: calc(100% - var(--sidebar-collapsed-width));
}

.modern-navbar .container-fluid {
    padding: 0 1.5rem;
    height: 100%;
    display: flex;
    align-items: center;
}

/* Modern Sidebar Toggle Button */
.modern-sidebar-toggle-btn {
    width: 48px;
    height: 48px;
    border: none;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
    position: relative;
    overflow: hidden;
    margin-right: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.modern-sidebar-toggle-btn:hover {
    background: linear-gradient(135deg, #1d4ed8, #1e40af);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(59, 130, 246, 0.4);
}

.modern-sidebar-toggle-btn:active {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.modern-sidebar-toggle-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), transparent);
    border-radius: inherit;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.modern-sidebar-toggle-btn:hover::before {
    opacity: 1;
}

.toggle-icon-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 4px;
    z-index: 2;
    position: relative;
}

.modern-hamburger-line {
    width: 22px;
    height: 2.5px;
    background: #ffffff;
    border-radius: 2px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform-origin: center;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.toggle-ripple {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: all 0.3s ease;
    pointer-events: none;
}

.modern-sidebar-toggle-btn:active .toggle-ripple {
    width: 60px;
    height: 60px;
    opacity: 0;
}

/* Modern Animated hamburger when sidebar is collapsed */
.modern-sidebar-toggle-btn.active .modern-hamburger-line:nth-child(1) {
    transform: translateY(6.5px) rotate(45deg);
}

.modern-sidebar-toggle-btn.active .modern-hamburger-line:nth-child(2) {
    opacity: 0;
    transform: scaleX(0);
}

.modern-sidebar-toggle-btn.active .modern-hamburger-line:nth-child(3) {
    transform: translateY(-6.5px) rotate(-45deg);
}

/* Also support body class for backward compatibility */
.sidebar-collapsed .modern-sidebar-toggle-btn .modern-hamburger-line:nth-child(1) {
    transform: translateY(6.5px) rotate(45deg);
}

.sidebar-collapsed .modern-sidebar-toggle-btn .modern-hamburger-line:nth-child(2) {
    opacity: 0;
    transform: scaleX(0);
}

.sidebar-collapsed .modern-sidebar-toggle-btn .modern-hamburger-line:nth-child(3) {
    transform: translateY(-6.5px) rotate(-45deg);
}

/* Smooth rotation animation for the entire button */
.modern-sidebar-toggle-btn.active {
    background: linear-gradient(135deg, #1e40af, #1d4ed8);
}

.sidebar-collapsed .modern-sidebar-toggle-btn {
    background: linear-gradient(135deg, #1e40af, #1d4ed8);
}

/* Modern Mobile Sidebar Toggle */
.modern-mobile-sidebar-toggle {
    width: 44px;
    height: 44px;
    border: none;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 3px 8px rgba(59, 130, 246, 0.3);
    margin-right: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
    overflow: hidden;
}

.modern-mobile-sidebar-toggle:hover {
    background: linear-gradient(135deg, #1d4ed8, #1e40af);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.modern-mobile-sidebar-toggle:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(59, 130, 246, 0.3);
}

.mobile-toggle-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2;
    position: relative;
}

.mobile-toggle-icon i {
    font-size: 1.2rem;
    color: white;
    transition: all 0.3s ease;
}

.modern-mobile-sidebar-toggle:hover .mobile-toggle-icon i {
    transform: scale(1.1);
}

.mobile-toggle-ripple {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: all 0.3s ease;
    pointer-events: none;
}

.modern-mobile-sidebar-toggle:active .mobile-toggle-ripple {
    width: 50px;
    height: 50px;
    opacity: 0;
}

.modern-mobile-sidebar-toggle::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), transparent);
    border-radius: inherit;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.modern-mobile-sidebar-toggle:hover::before {
    opacity: 1;
}

/* Navbar Styles */
.navbar {
    height: 72px;
    box-shadow: 0 1px 3px rgba(0,0,0,.08);
    position: fixed;
    top: 0;
    right: 0;
    left: 280px; /* Sesuaikan dengan lebar sidebar */
    z-index: 999;
    background: linear-gradient(145deg, #ffffff, #e8f5e9);
    border-left: 4px solid #4caf50;
    padding: 0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    width: calc(100% - 280px);
}

/* Navbar when sidebar is collapsed */
.sidebar-collapsed .navbar {
    left: 70px;
    width: calc(100% - 70px);
}



.navbar .container-fluid {
    padding: 0 1.75rem;
    height: 100%;
    display: flex;
    align-items: center;
}

.navbar-brand {
    font-size: 1.5rem;
    font-weight: 600;
    color: #2e7d32;
    margin-right: 3rem;
    padding: 0;
    display: flex;
    align-items: center;
    height: 100%;
    text-decoration: none;
    cursor: default;
}

/* Dynamic Greeting Styles */
.brand-logo {
    margin-right: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: transparent !important;
    background-color: transparent !important;
    background-image: none !important;
    transition: all 0.3s ease;
}

.brand-logo i {
    font-size: 1.4rem;
    transition: all 0.3s ease;
}

/* Force transparent background for brand logo */
.navbar-brand .brand-logo,
.brand-logo,
div.brand-logo {
    background: transparent !important;
    background-color: transparent !important;
    background-image: none !important;
    box-shadow: none !important;
    border: none !important;
}

/* Ensure no background on any state */
.brand-logo:hover,
.brand-logo:focus,
.brand-logo:active {
    background: transparent !important;
    background-color: transparent !important;
    background-image: none !important;
}

.brand-text {
    display: flex;
    flex-direction: column;
    line-height: 1.2;
}

.brand-name {
    font-size: 1.1rem;
    font-weight: 600;
    transition: color 0.3s ease;
    margin-bottom: 2px;
}

.brand-subtitle {
    font-size: 0.8rem;
    color: var(--text-secondary, #6c757d);
    font-weight: 500;
    opacity: 0.8;
}

.navbar-brand i {
    font-size: 1.75rem;
    color: #4caf50;
}

.navbar-brand .brand-text {
    font-size: 1.5rem;
    font-weight: 600;
    line-height: 1.2;
    color: #2e7d32;
}

.navbar-brand .brand-text span {
    color: #1b5e20;
    font-weight: 700;
}

.navbar .nav-link {
    color: #2e7d32;
    padding: 0.625rem;
    transition: all 0.2s ease;
    font-size: 1rem;
    display: flex;
    align-items: center;
    height: 40px;
    border-radius: 0;
    margin: 0;
    position: relative;
}

.navbar .nav-link i {
    font-size: 1.1rem;
    width: 20px;
    text-align: center;
    line-height: 1;
}

.navbar .nav-link:hover {
    color: #1b5e20;
    background-color: rgba(76, 175, 80, 0.1);
}

.avatar-circle {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: linear-gradient(45deg, #4caf50, #8bc34a);
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.9rem;
    flex-shrink: 0;
    line-height: 1;
}

.profile-dropdown {
    min-width: 280px;
}

.profile-dropdown .dropdown-header {
    padding: 1.25rem;
    margin-bottom: 0.25rem;
}

.profile-dropdown .dropdown-header .avatar-circle {
    width: 36px;
    height: 36px;
    font-size: 1rem;
    margin-right: 0.75rem;
}

.profile-dropdown .dropdown-divider {
    margin: 0.5rem 0;
}

.dropdown-menu {
    border: 1px solid var(--bs-border-color);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    border-radius: 12px;
    padding: 0.625rem;
    margin-top: 0.5rem;
}

.dropdown-item {
    padding: 0.625rem 1rem;
    border-radius: 8px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
}

.dropdown-item:hover {
    background-color: rgba(var(--bs-primary-rgb), 0.08);
}

.dropdown-item i {
    width: 20px;
    text-align: center;
    margin-right: 0.75rem;
    font-size: 1rem;
    line-height: 1;
}

.dropdown-header {
    font-weight: 600;
    color: var(--bs-body-color);
}

.navbar-nav {
    display: flex;
    align-items: center;
    gap: 0;
    height: 100%;
    margin: 0;
    padding: 0;
}

.nav-item {
    display: flex;
    align-items: center;
    height: 100%;
}

.navbar-toggler {
    padding: 0.625rem;
    border: none;
    border-radius: 8px;
    transition: all 0.2s ease;
    color: #2e7d32;
}

.navbar-toggler:hover {
    background-color: rgba(76, 175, 80, 0.1);
}

.navbar-toggler i {
    font-size: 1.25rem;
    color: #2e7d32;
}

/* Mobile Sidebar Styles */
.sidebar.mobile-active {
    transform: translateX(0) !important;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.3);
}

.sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 998;
    display: none;
    transition: opacity 0.3s ease;
}

/* Responsive Styles */
@media (max-width: 991.98px) {
    .navbar {
        left: 0;
    }

    .sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
        z-index: 999;
    }

    .navbar-collapse {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: linear-gradient(145deg, #ffffff, #e8f5e9);
        padding: 1.25rem;
        box-shadow: 0 2px 4px rgba(0,0,0,.08);
        z-index: 1000;
        border-top: 1px solid rgba(76, 175, 80, 0.1);
    }

    .notifications-dropdown,
    .profile-dropdown {
        position: static !important;
        transform: none !important;
        width: 100%;
        margin-top: 0.25rem;
        box-shadow: none;
        border: 1px solid var(--bs-border-color);
    }

    .navbar-nav {
        gap: 0;
    }

    .navbar .nav-link {
        height: 44px;
        margin: 0.25rem 0;
        color: #2e7d32;
    }

    .navbar .nav-link:hover {
        color: #1b5e20;
        background-color: rgba(76, 175, 80, 0.1);
    }
}

/* Main Content Styles */
.main-content {
    padding: 90px 30px 30px 30px; /* Menambah padding top dan sisi */
    margin-left: 280px; /* Sesuaikan dengan lebar sidebar */
    transition: all 0.3s ease;
    min-height: calc(100vh - 72px);
    width: calc(100% - 280px); /* Lebar total dikurangi lebar sidebar */
    position: relative;
    overflow-x: hidden;
    background-color: #f8f9fa; /* Warna background yang lebih soft */
}

body.sidebar-collapsed .main-content {
    margin-left: 70px;
    width: calc(100% - 70px); /* Lebar total dikurangi lebar sidebar collapsed */
}

@media (max-width: 991.98px) {
    .main-content {
        margin-left: 0;
        width: 100%;
        padding: 90px 20px 20px 20px;
    }

    .modern-navbar {
        left: 0;
        width: 100%;
    }

    .sidebar-collapsed .modern-navbar {
        left: 0;
        width: 100%;
    }
}

/* Tooltip Customization */
.tooltip {
    font-size: 0.875rem;
}

.tooltip .tooltip-inner {
    background: var(--bs-body-bg);
    color: var(--bs-body-color);
    border: 1px solid var(--bs-border-color);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    padding: 0.5rem 0.75rem;
    border-radius: 6px;
}

.tooltip.bs-tooltip-top .tooltip-arrow::before {
    border-top-color: var(--bs-border-color);
}

.tooltip.bs-tooltip-bottom .tooltip-arrow::before {
    border-bottom-color: var(--bs-border-color);
}

.tooltip.bs-tooltip-start .tooltip-arrow::before {
    border-left-color: var(--bs-border-color);
}

.tooltip.bs-tooltip-end .tooltip-arrow::before {
    border-right-color: var(--bs-border-color);
}

[data-bs-theme="dark"] .tooltip .tooltip-inner {
    background: var(--bs-dark);
    border-color: var(--bs-border-color);
}

[data-bs-theme="dark"] .tooltip.bs-tooltip-top .tooltip-arrow::before {
    border-top-color: var(--bs-border-color);
}

[data-bs-theme="dark"] .tooltip.bs-tooltip-bottom .tooltip-arrow::before {
    border-bottom-color: var(--bs-border-color);
}

[data-bs-theme="dark"] .tooltip.bs-tooltip-start .tooltip-arrow::before {
    border-left-color: var(--bs-border-color);
}

[data-bs-theme="dark"] .tooltip.bs-tooltip-end .tooltip-arrow::before {
    border-right-color: var(--bs-border-color);
}

/* Notifications Styles */
.notifications-dropdown {
    width: 380px;
    padding: 0;
    max-height: 500px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    border: none;
    border-radius: 12px;
}

.notifications-dropdown .dropdown-header {
    padding: 1rem 1.25rem;
    border-bottom: 1px solid var(--bs-border-color);
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 12px 12px 0 0;
}

.notifications-list {
    max-height: 400px;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: #ccc transparent;
}

.notifications-list::-webkit-scrollbar {
    width: 6px;
}

.notifications-list::-webkit-scrollbar-track {
    background: transparent;
}

.notifications-list::-webkit-scrollbar-thumb {
    background: #ccc;
    border-radius: 3px;
}

.notification-item {
    padding: 1rem 1.25rem;
    border-bottom: 1px solid rgba(0,0,0,0.05);
    transition: all 0.2s ease;
    text-decoration: none;
    color: inherit;
    position: relative;
}

.notification-item:hover {
    background-color: rgba(76, 175, 80, 0.08);
    transform: translateX(2px);
    color: inherit;
    text-decoration: none;
}

.notification-item.unread {
    background-color: rgba(76, 175, 80, 0.05);
    border-left: 3px solid #4caf50;
}

.notification-item.unread:hover {
    background-color: rgba(76, 175, 80, 0.1);
}

.notification-icon {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background: rgba(76, 175, 80, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #4caf50;
    flex-shrink: 0;
}

.notification-content {
    flex-grow: 1;
    min-width: 0;
}

.notification-content .notification-title {
    font-size: 0.9rem;
    margin: 0;
    line-height: 1.4;
    font-weight: 600;
    color: var(--bs-body-color);
}

.notification-content .notification-message {
    font-size: 0.85rem;
    margin: 0;
    line-height: 1.3;
    color: var(--bs-secondary);
    word-wrap: break-word;
}

.notification-content small {
    font-size: 0.75rem;
    color: var(--bs-muted);
}

.unread-indicator {
    width: 8px;
    height: 8px;
    background: #4caf50;
    border-radius: 50%;
    position: absolute;
    top: 1rem;
    right: 1rem;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.dropdown-footer {
    padding: 0.75rem;
    border-top: 1px solid var(--bs-border-color);
    background: #f8f9fa;
    border-radius: 0 0 12px 12px;
}

.dropdown-footer .dropdown-item {
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-weight: 500;
    color: #4caf50;
}

.dropdown-footer .dropdown-item:hover {
    background-color: rgba(76, 175, 80, 0.1);
    color: #2e7d32;
}

/* Notification Badge */
.notification-badge {
    position: absolute;
    top: -2px;
    right: -2px;
    background: linear-gradient(135deg, #ff4444, #cc0000);
    color: white;
    border-radius: 10px;
    padding: 2px 6px;
    font-size: 0.7rem;
    font-weight: 600;
    min-width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 4px rgba(255, 68, 68, 0.3);
    animation: notificationBounce 0.5s ease-out;
}

@keyframes notificationBounce {
    0% { transform: scale(0); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

/* Empty notifications state */
.empty-notifications {
    padding: 2rem 1rem;
}

.empty-notifications i {
    opacity: 0.5;
}

/* Real-time notification updates */
.notification-item.new-notification {
    animation: slideInNotification 0.5s ease-out;
    background-color: rgba(76, 175, 80, 0.1);
}

@keyframes slideInNotification {
    0% {
        transform: translateX(-100%);
        opacity: 0;
    }
    100% {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Notification types styling */
.notification-item[data-type="error"] {
    border-left-color: #dc3545;
}

.notification-item[data-type="error"] .notification-icon {
    background: rgba(220, 53, 69, 0.1);
    color: #dc3545;
}

.notification-item[data-type="warning"] {
    border-left-color: #ffc107;
}

.notification-item[data-type="warning"] .notification-icon {
    background: rgba(255, 193, 7, 0.1);
    color: #ffc107;
}

.notification-item[data-type="success"] {
    border-left-color: #28a745;
}

.notification-item[data-type="success"] .notification-icon {
    background: rgba(40, 167, 69, 0.1);
    color: #28a745;
}

.notification-item[data-type="info"] {
    border-left-color: #17a2b8;
}

.notification-item[data-type="info"] .notification-icon {
    background: rgba(23, 162, 184, 0.1);
    color: #17a2b8;
}

/* Sidebar Styles */
.sidebar {
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    width: 280px;
    transition: all 0.3s ease;
    z-index: 1000;
    overflow-y: auto;
    overflow-x: hidden;
    background: #fff;
    box-shadow: 2px 0 5px rgba(0,0,0,.05);
}

body.sidebar-collapsed .sidebar {
    width: 70px;
}

@media (max-width: 991.98px) {
    .sidebar {
        transform: translateX(-100%);
    }

    body.sidebar-collapsed .sidebar {
        transform: translateX(0);
    }
}

/* Container Styles */
.container-fluid {
    padding: 0 20px;
    width: 100%;
    max-width: 100%;
}

/* Card Styles */
.card {
    margin-bottom: 25px; /* Menambah jarak antar card */
    border-radius: 12px;
    box-shadow: 0 2px 4px rgba(0,0,0,.05);
    border: none;
    background: #fff;
}

.card-header {
    padding: 1.25rem 1.5rem;
    background: #fff;
    border-bottom: 1px solid rgba(0,0,0,.05);
    border-radius: 12px 12px 0 0 !important;
}

.card-body {
    padding: 1.5rem;
}

/* Row and Column Spacing */
.row {
    margin: 0 -15px;
}

.col, .col-1, .col-2, .col-3, .col-4, .col-5, .col-6,
.col-7, .col-8, .col-9, .col-10, .col-11, .col-12,
.col-sm, .col-md, .col-lg, .col-xl {
    padding: 0 15px;
}

/* Table Styles */
.table {
    margin-bottom: 0;
}

.table th {
    font-weight: 600;
    background: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
}

.table td {
    vertical-align: middle;
}

/* Section Spacing */
.section {
    margin-bottom: 30px;
}

.section-title {
    margin-bottom: 20px;
    font-weight: 600;
    color: #2c3e50;
}

/* Quick Stats Cards */
.quick-stats .card {
    border: none;
    border-radius: 12px;
    transition: transform 0.2s ease;
}

.quick-stats .card:hover {
    transform: translateY(-5px);
}

.quick-stats .card-body {
    padding: 1.5rem;
}

.quick-stats .card-title {
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.quick-stats .card-value {
    font-size: 1.75rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
}

/* Fix for nested containers */
.container-fluid .container-fluid {
    padding: 0;
}

/* Modern Dropdown Styles */
.dropdown-menu {
    border: none;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    padding: 0.5rem;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    min-width: 280px;
}

.quick-actions-dropdown {
    min-width: 220px;
}

/* Ultra Modern User Profile Button */
.modern-user-profile-btn {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05)) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    border-radius: 12px !important;
    padding: 0.5rem 1rem !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    backdrop-filter: blur(10px) !important;
    -webkit-backdrop-filter: blur(10px) !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
    position: relative !important;
    overflow: hidden !important;
}

.modern-user-profile-btn:hover {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.08)) !important;
    border-color: rgba(59, 130, 246, 0.3) !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 16px rgba(59, 130, 246, 0.2) !important;
}

.modern-user-profile-btn:focus,
.modern-user-profile-btn:active {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1)) !important;
    border-color: rgba(59, 130, 246, 0.5) !important;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
}

.modern-user-profile-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.modern-user-profile-btn:hover::before {
    left: 100%;
}

/* Modern User Avatar */
.modern-user-avatar {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modern-user-avatar img {
    width: 36px;
    height: 36px;
    border: 2px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.modern-user-avatar:hover img {
    border-color: rgba(59, 130, 246, 0.5);
    transform: scale(1.05);
}

.status-indicator {
    position: absolute;
    bottom: 2px;
    right: 2px;
    width: 10px;
    height: 10px;
    background: linear-gradient(135deg, #10b981, #34d399);
    border: 2px solid white;
    border-radius: 50%;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

/* Modern User Info */
.modern-user-info {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    line-height: 1.2;
}

.modern-user-name {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-primary, #1f2937);
    margin-bottom: 2px;
    transition: color 0.3s ease;
}

.modern-user-role {
    font-size: 0.75rem;
    color: var(--text-muted, #6b7280);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Dropdown Arrow Container */
.dropdown-arrow-container {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    border-radius: 4px;
    background: rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.dropdown-arrow {
    font-size: 0.75rem;
    color: var(--text-muted, #6b7280);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modern-user-profile-btn[aria-expanded="true"] .dropdown-arrow {
    transform: rotate(180deg);
    color: var(--primary-color, #3b82f6);
}

.modern-user-profile-btn[aria-expanded="true"] .dropdown-arrow-container {
    background: rgba(59, 130, 246, 0.1);
}

/* Ultra Modern Profile Dropdown */
.ultra-modern-profile-dropdown {
    min-width: 340px;
    border: none !important;
    border-radius: 16px !important;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(255, 255, 255, 0.1) !important;
    backdrop-filter: blur(20px) !important;
    -webkit-backdrop-filter: blur(20px) !important;
    background: rgba(255, 255, 255, 0.95) !important;
    padding: 0 !important;
    margin-top: 0.75rem !important;
    overflow: hidden !important;
    animation: dropdownFadeIn 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

@keyframes dropdownFadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Modern Dropdown Header */
.modern-dropdown-header {
    padding: 1.5rem 1.25rem 1rem 1.25rem !important;
    margin: 0 !important;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.05), rgba(147, 197, 253, 0.05)) !important;
    border-bottom: 1px solid rgba(59, 130, 246, 0.1) !important;
}

.modern-profile-header {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.modern-profile-avatar {
    position: relative;
    flex-shrink: 0;
}

.modern-profile-avatar img {
    width: 52px;
    height: 52px;
    border: 3px solid rgba(59, 130, 246, 0.2);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
    transition: all 0.3s ease;
}

.modern-online-indicator {
    position: absolute;
    bottom: 2px;
    right: 2px;
    width: 14px;
    height: 14px;
    background: linear-gradient(135deg, #10b981, #34d399);
    border: 3px solid white;
    border-radius: 50%;
    box-shadow: 0 2px 6px rgba(16, 185, 129, 0.3);
    animation: pulse 2s infinite;
}

.modern-profile-info {
    flex: 1;
    min-width: 0;
}

.modern-profile-name {
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--text-primary, #1f2937);
    margin-bottom: 0.25rem;
    line-height: 1.3;
}

.modern-profile-email {
    font-size: 0.8rem;
    color: var(--text-muted, #6b7280);
    font-weight: 500;
    display: block;
    margin-bottom: 0.5rem;
    opacity: 0.8;
}

.modern-profile-role-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(147, 197, 253, 0.1));
    border: 1px solid rgba(59, 130, 246, 0.2);
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--primary-color, #3b82f6);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.modern-profile-role-badge i {
    font-size: 0.7rem;
    color: #f59e0b;
}

/* Modern Dropdown Divider */
.modern-dropdown-divider {
    margin: 0.75rem 1.25rem !important;
    border-color: rgba(59, 130, 246, 0.1) !important;
    opacity: 1 !important;
}

/* Ultra Modern Dropdown Items */
.ultra-modern-dropdown-item {
    display: flex !important;
    align-items: center !important;
    padding: 0.875rem 1.25rem !important;
    margin: 0.25rem 0.75rem !important;
    border-radius: 10px !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    text-decoration: none !important;
    color: var(--text-primary, #1f2937) !important;
    position: relative !important;
    overflow: hidden !important;
    border: 1px solid transparent !important;
}

.ultra-modern-dropdown-item:hover {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.08), rgba(147, 197, 253, 0.08)) !important;
    border-color: rgba(59, 130, 246, 0.15) !important;
    transform: translateX(4px) !important;
    color: var(--primary-color, #3b82f6) !important;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.1) !important;
}

.ultra-modern-dropdown-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
    transition: left 0.5s ease;
}

.ultra-modern-dropdown-item:hover::before {
    left: 100%;
}

.modern-item-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 10px;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(147, 197, 253, 0.1));
    margin-right: 1rem;
    flex-shrink: 0;
    transition: all 0.3s ease;
}

.modern-item-icon i {
    font-size: 1.1rem;
    color: var(--primary-color, #3b82f6);
    transition: all 0.3s ease;
}

.ultra-modern-dropdown-item:hover .modern-item-icon {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.15), rgba(147, 197, 253, 0.15));
    transform: scale(1.05);
}

.ultra-modern-dropdown-item:hover .modern-item-icon i {
    transform: scale(1.1);
}

.modern-item-content {
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
}

.modern-item-title {
    font-size: 0.9rem;
    font-weight: 600;
    color: inherit;
    margin-bottom: 0.125rem;
    line-height: 1.3;
}

.modern-item-subtitle {
    font-size: 0.75rem;
    color: var(--text-muted, #6b7280);
    font-weight: 500;
    opacity: 0.8;
    line-height: 1.2;
}

.modern-item-arrow {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    border-radius: 6px;
    background: rgba(59, 130, 246, 0.05);
    margin-left: 0.75rem;
    flex-shrink: 0;
    transition: all 0.3s ease;
    opacity: 0.6;
}

.modern-item-arrow i {
    font-size: 0.75rem;
    color: var(--text-muted, #6b7280);
    transition: all 0.3s ease;
}

.ultra-modern-dropdown-item:hover .modern-item-arrow {
    background: rgba(59, 130, 246, 0.1);
    transform: translateX(2px);
    opacity: 1;
}

.ultra-modern-dropdown-item:hover .modern-item-arrow i {
    color: var(--primary-color, #3b82f6);
}

/* Special Styling for Admin Item */
.admin-item {
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.05), rgba(251, 191, 36, 0.05)) !important;
    border-color: rgba(245, 158, 11, 0.1) !important;
}

.admin-item:hover {
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.1), rgba(251, 191, 36, 0.1)) !important;
    border-color: rgba(245, 158, 11, 0.2) !important;
    color: #d97706 !important;
}

.admin-icon {
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.1), rgba(251, 191, 36, 0.1)) !important;
}

.admin-icon i {
    color: #d97706 !important;
}

.admin-item:hover .admin-icon {
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.15), rgba(251, 191, 36, 0.15)) !important;
}

/* Special Styling for Logout Item */
.logout-item {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.05), rgba(248, 113, 113, 0.05)) !important;
    border-color: rgba(239, 68, 68, 0.1) !important;
    margin-bottom: 0.75rem !important;
}

.logout-item:hover {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(248, 113, 113, 0.1)) !important;
    border-color: rgba(239, 68, 68, 0.2) !important;
    color: #dc2626 !important;
}

.logout-icon {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(248, 113, 113, 0.1)) !important;
}

.logout-icon i {
    color: #dc2626 !important;
}

.logout-title {
    color: #dc2626 !important;
    font-weight: 700 !important;
}

.logout-arrow {
    background: rgba(239, 68, 68, 0.05) !important;
}

.logout-arrow i {
    color: #dc2626 !important;
}

.logout-item:hover .logout-icon {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.15), rgba(248, 113, 113, 0.15)) !important;
}

.logout-item:hover .logout-arrow {
    background: rgba(239, 68, 68, 0.1) !important;
}
    border-bottom: 1px solid var(--border-light);
    background: var(--bg-tertiary);
    border-radius: var(--radius-lg) var(--radius-lg) 0 0;
}

.profile-header {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.profile-avatar {
    position: relative;
    width: 48px;
    height: 48px;
    flex-shrink: 0;
}

.profile-avatar img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    border: 2px solid var(--primary-color);
}

.online-indicator {
    position: absolute;
    bottom: 2px;
    right: 2px;
    width: 12px;
    height: 12px;
    background: var(--success-color);
    border: 2px solid var(--bg-primary);
    border-radius: 50%;
}

.profile-info {
    flex: 1;
    min-width: 0;
}

.profile-name {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 0.25rem 0;
}

.profile-email {
    font-size: 0.8rem;
    color: var(--text-muted);
    margin-bottom: 0.25rem;
}

.profile-role-badge {
    display: inline-block;
    font-size: 0.7rem;
    font-weight: 600;
    color: var(--primary-color);
    background: rgba(37, 99, 235, 0.1);
    padding: 0.2rem 0.5rem;
    border-radius: var(--radius-sm);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.modern-dropdown-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.875rem 1.25rem;
    margin: 0.25rem 0;
    border-radius: var(--radius-md);
    color: var(--text-secondary);
    text-decoration: none;
    transition: all var(--transition-fast);
    border: none;
    background: transparent;
    width: 100%;
}

.modern-dropdown-item:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    transform: translateX(4px);
}

.item-icon {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.item-icon i {
    font-size: 1rem;
    color: var(--primary-color);
}

.item-content {
    flex: 1;
    text-align: left;
}

.item-title {
    display: block;
    font-weight: 500;
    font-size: 0.9rem;
    line-height: 1.2;
}

.item-subtitle {
    display: block;
    font-size: 0.75rem;
    color: var(--text-muted);
    margin-top: 0.1rem;
}

.logout-item:hover .item-icon i {
    color: var(--danger-color);
}

/* Notification Dropdown Styles */
.notifications-dropdown {
    min-width: 360px;
    max-height: 500px;
    overflow: hidden;
}

.notifications-list {
    max-height: 400px;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: var(--border-color) transparent;
}

.notifications-list::-webkit-scrollbar {
    width: 6px;
}

.notifications-list::-webkit-scrollbar-track {
    background: transparent;
}

.notifications-list::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 3px;
}

.notification-item {
    display: flex;
    align-items: flex-start;
    padding: 1rem 1.25rem;
    margin: 0;
    border-bottom: 1px solid var(--border-light);
    color: var(--text-secondary);
    text-decoration: none;
    transition: all var(--transition-fast);
    position: relative;
}

.notification-item:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.notification-item.unread {
    background: rgba(37, 99, 235, 0.05);
    border-left: 3px solid var(--primary-color);
}

.notification-icon {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background: rgba(37, 99, 235, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    margin-right: 1rem;
    flex-shrink: 0;
}

.notification-content {
    flex: 1;
    min-width: 0;
}

.notification-content .fw-medium {
    font-size: 0.9rem;
    line-height: 1.4;
    margin-bottom: 0.25rem;
}

.unread-indicator {
    position: absolute;
    top: 1rem;
    right: 1rem;
    width: 8px;
    height: 8px;
    background: var(--primary-color);
    border-radius: 50%;
}

.empty-notifications {
    padding: 2rem 1.25rem;
    text-align: center;
}

.empty-notifications i {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: var(--text-muted);
}

.dropdown-footer {
    padding: 0.75rem 1.25rem;
    border-top: 1px solid var(--border-light);
    background: var(--bg-tertiary);
    border-radius: 0 0 var(--radius-lg) var(--radius-lg);
}

.dropdown-footer .dropdown-item {
    padding: 0.5rem 0;
    text-align: center;
    font-weight: 500;
    color: var(--primary-color);
}

.dropdown-footer .dropdown-item:hover {
    background: transparent;
    color: var(--primary-hover);
}

/* User Profile Button */
.user-profile-btn {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.5rem 1rem;
    background: transparent;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-xl);
    color: var(--text-primary);
    text-decoration: none;
    transition: all var(--transition-fast);
    min-width: 0;
}

.user-profile-btn:hover {
    background: var(--bg-tertiary);
    border-color: var(--primary-color);
    color: var(--text-primary);
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

.user-avatar {
    width: 32px;
    height: 32px;
    flex-shrink: 0;
}

.user-avatar img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
}

.user-info {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    min-width: 0;
}

.user-name {
    font-size: 0.9rem;
    font-weight: 600;
    line-height: 1.2;
    margin: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 120px;
}

.user-role {
    font-size: 0.75rem;
    color: var(--text-muted);
    line-height: 1;
    margin: 0;
}

.dropdown-arrow {
    font-size: 0.75rem;
    color: var(--text-muted);
    transition: transform var(--transition-fast);
}

.user-profile-btn[aria-expanded="true"] .dropdown-arrow {
    transform: rotate(180deg);
}

/* Dark Mode Navbar Styles */
[data-bs-theme="dark"] .modern-navbar {
    background-color: var(--bg-primary) !important;
    border-bottom-color: var(--border-color) !important;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

[data-bs-theme="dark"] .navbar {
    background-color: var(--bg-primary) !important;
}

[data-bs-theme="dark"] .navbar-light {
    background-color: var(--bg-primary) !important;
}

[data-bs-theme="dark"] .bg-white {
    background-color: var(--bg-primary) !important;
}

[data-bs-theme="dark"] .bg-light {
    background-color: var(--bg-secondary) !important;
}

[data-bs-theme="dark"] .navbar-brand {
    color: var(--text-primary);
}

[data-bs-theme="dark"] .brand-name {
    color: var(--text-primary);
}

[data-bs-theme="dark"] .brand-subtitle {
    color: var(--text-muted);
}

[data-bs-theme="dark"] .search-input {
    background: var(--bg-tertiary);
    border-color: var(--border-color);
    color: var(--text-primary);
}

[data-bs-theme="dark"] .search-input:focus {
    background: var(--bg-primary);
    border-color: var(--primary-color);
    color: var(--text-primary);
}

[data-bs-theme="dark"] .search-icon {
    color: var(--text-muted);
}

[data-bs-theme="dark"] .nav-link {
    color: var(--text-secondary);
}

[data-bs-theme="dark"] .nav-link:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

[data-bs-theme="dark"] .user-profile-btn {
    border-color: var(--border-color);
    color: var(--text-primary);
}

[data-bs-theme="dark"] .user-profile-btn:hover {
    background: var(--bg-tertiary);
    border-color: var(--primary-color);
    color: var(--text-primary);
}

[data-bs-theme="dark"] .dropdown-menu {
    background: var(--bg-primary);
    border-color: var(--border-color);
}

[data-bs-theme="dark"] .dropdown-header {
    background: var(--bg-tertiary);
    border-bottom-color: var(--border-light);
}

[data-bs-theme="dark"] .modern-dropdown-item {
    color: var(--text-secondary);
}

[data-bs-theme="dark"] .modern-dropdown-item:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

[data-bs-theme="dark"] .notification-item {
    color: var(--text-secondary);
    border-bottom-color: var(--border-light);
}

[data-bs-theme="dark"] .notification-item:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

[data-bs-theme="dark"] .notification-item.unread {
    background: rgba(37, 99, 235, 0.1);
    border-left-color: var(--primary-color);
}

[data-bs-theme="dark"] .dropdown-footer {
    background: var(--bg-tertiary);
    border-top-color: var(--border-light);
}

[data-bs-theme="dark"] .mobile-sidebar-toggle {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

[data-bs-theme="dark"] .mobile-sidebar-toggle:hover {
    background: var(--primary-color);
    color: var(--text-inverse);
}

/* Responsive Dark Mode Adjustments */
@media (max-width: 991.98px) {
    [data-bs-theme="dark"] .modern-navbar {
        left: 0;
        width: 100%;
    }

    [data-bs-theme="dark"] .navbar-search-mobile .search-input {
        background: var(--bg-tertiary);
        border-color: var(--border-color);
        color: var(--text-primary);
    }
}

/* Theme Toggle Button Dark Mode */
[data-bs-theme="dark"] .nav-link[data-theme-toggle] {
    color: var(--text-secondary);
}

[data-bs-theme="dark"] .nav-link[data-theme-toggle]:hover {
    background: var(--bg-tertiary);
    color: var(--warning-color);
}

/* Notification Badge Dark Mode */
[data-bs-theme="dark"] .notification-badge {
    background: var(--danger-color);
    color: var(--text-inverse);
}

/* Search Results Dark Mode */
[data-bs-theme="dark"] .search-results {
    background: var(--bg-primary);
    border-color: var(--border-color);
}

/* Profile Avatar Dark Mode */
[data-bs-theme="dark"] .profile-avatar img,
[data-bs-theme="dark"] .user-avatar img {
    border-color: var(--primary-color);
}

/* Quick Actions Dropdown Dark Mode */
[data-bs-theme="dark"] .quick-actions-dropdown .dropdown-item {
    color: var(--text-secondary);
}

[data-bs-theme="dark"] .quick-actions-dropdown .dropdown-item:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

[data-bs-theme="dark"] .quick-actions-dropdown .dropdown-header {
    color: var(--text-primary);
}

/* Navbar Collapse Dark Mode */
[data-bs-theme="dark"] .navbar-collapse {
    background: var(--bg-primary);
    border-color: var(--border-color);
}

/* Navbar Toggler Dark Mode */
[data-bs-theme="dark"] .navbar-toggler {
    border-color: var(--border-color);
    color: var(--text-primary);
}

[data-bs-theme="dark"] .navbar-toggler:focus {
    box-shadow: 0 0 0 0.25rem rgba(37, 99, 235, 0.25);
}

/* Additional Dark Mode Fixes */
[data-bs-theme="dark"] .navbar-nav .nav-link {
    color: var(--text-secondary);
}

[data-bs-theme="dark"] .navbar-nav .nav-link:hover,
[data-bs-theme="dark"] .navbar-nav .nav-link:focus {
    color: var(--text-primary);
}

[data-bs-theme="dark"] .navbar-nav .nav-link.active {
    color: var(--primary-color);
}

/* Modern Sidebar Toggle Button Dark Mode */
[data-bs-theme="dark"] .modern-sidebar-toggle-btn {
    background: linear-gradient(135deg, #60a5fa, #3b82f6);
    border-color: rgba(255, 255, 255, 0.15);
    box-shadow: 0 4px 12px rgba(96, 165, 250, 0.4);
}

[data-bs-theme="dark"] .modern-sidebar-toggle-btn:hover {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    box-shadow: 0 8px 20px rgba(96, 165, 250, 0.5);
}

[data-bs-theme="dark"] .modern-sidebar-toggle-btn.active,
[data-bs-theme="dark"] .sidebar-collapsed .modern-sidebar-toggle-btn {
    background: linear-gradient(135deg, #2563eb, #1d4ed8);
}

[data-bs-theme="dark"] .modern-hamburger-line {
    background: #ffffff;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

[data-bs-theme="dark"] .modern-mobile-sidebar-toggle {
    background: linear-gradient(135deg, #60a5fa, #3b82f6);
    border-color: rgba(255, 255, 255, 0.15);
    box-shadow: 0 3px 8px rgba(96, 165, 250, 0.4);
}

[data-bs-theme="dark"] .modern-mobile-sidebar-toggle:hover {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    box-shadow: 0 4px 12px rgba(96, 165, 250, 0.5);
}
}

/* Additional Dark Mode Enhancements */
[data-bs-theme="dark"] .navbar-brand:hover {
    color: var(--text-primary);
}

[data-bs-theme="dark"] .search-input::placeholder {
    color: var(--text-muted);
}

[data-bs-theme="dark"] .navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28248, 250, 252, 0.75%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

[data-bs-theme="dark"] .dropdown-divider {
    border-color: var(--border-color);
}

[data-bs-theme="dark"] .text-muted {
    color: var(--text-muted) !important;
}

[data-bs-theme="dark"] .text-primary {
    color: var(--primary-color) !important;
}

[data-bs-theme="dark"] .text-success {
    color: var(--success-color) !important;
}

[data-bs-theme="dark"] .text-danger {
    color: var(--danger-color) !important;
}

[data-bs-theme="dark"] .text-warning {
    color: var(--warning-color) !important;
}

[data-bs-theme="dark"] .text-info {
    color: var(--info-color) !important;
}

/* Dark Mode Scrollbar */
[data-bs-theme="dark"] .notifications-list::-webkit-scrollbar-thumb {
    background: var(--border-dark);
}

[data-bs-theme="dark"] .notifications-list::-webkit-scrollbar-track {
    background: var(--bg-tertiary);
}

/* Dark Mode Focus States */
[data-bs-theme="dark"] .nav-link:focus {
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

[data-bs-theme="dark"] .user-profile-btn:focus {
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

[data-bs-theme="dark"] .modern-sidebar-toggle-btn:focus {
    box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.3);
    outline: none;
}

[data-bs-theme="dark"] .modern-mobile-sidebar-toggle:focus {
    box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.3);
    outline: none;
}

/* Light mode focus styles */
.modern-sidebar-toggle-btn:focus {
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
    outline: none;
}

.modern-mobile-sidebar-toggle:focus {
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
    outline: none;
}

/* Dark Mode Animation Enhancements */
[data-bs-theme="dark"] .nav-link,
[data-bs-theme="dark"] .user-profile-btn,
[data-bs-theme="dark"] .modern-sidebar-toggle-btn,
[data-bs-theme="dark"] .modern-mobile-sidebar-toggle {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1), box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Dark Mode Backdrop */
[data-bs-theme="dark"] .modern-navbar {
    backdrop-filter: blur(20px) saturate(180%);
    -webkit-backdrop-filter: blur(20px) saturate(180%);
}

/* Dark Mode Brand Logo Glow Effect */
[data-bs-theme="dark"] .brand-logo {
    box-shadow: var(--shadow-md), 0 0 20px rgba(59, 130, 246, 0.3);
}

/* Dark Mode Notification Improvements */
[data-bs-theme="dark"] .notification-icon {
    background: rgba(59, 130, 246, 0.15);
    color: var(--primary-color);
}

[data-bs-theme="dark"] .empty-notifications h6 {
    color: var(--text-primary);
}

[data-bs-theme="dark"] .empty-notifications p {
    color: var(--text-muted);
}

/* Dark Mode Profile Improvements */
[data-bs-theme="dark"] .profile-name {
    color: var(--text-primary);
}

[data-bs-theme="dark"] .profile-email {
    color: var(--text-muted);
}

[data-bs-theme="dark"] .profile-role-badge {
    background: rgba(59, 130, 246, 0.15);
    color: var(--primary-color);
}

/* Dark Mode Item Improvements */
[data-bs-theme="dark"] .item-title {
    color: var(--text-primary);
}

[data-bs-theme="dark"] .item-subtitle {
    color: var(--text-muted);
}

[data-bs-theme="dark"] .logout-item .item-title {
    color: var(--danger-color);
}

[data-bs-theme="dark"] .logout-item .item-subtitle {
    color: var(--text-muted);
}

/* Additional Dark Mode Fixes for All Navbar Elements */
[data-bs-theme="dark"] .container-fluid {
    background-color: transparent !important;
}

[data-bs-theme="dark"] .navbar-nav {
    background-color: transparent !important;
}

[data-bs-theme="dark"] .nav-item {
    background-color: transparent !important;
}

[data-bs-theme="dark"] .btn-link {
    color: var(--text-secondary) !important;
}

[data-bs-theme="dark"] .btn-link:hover {
    color: var(--text-primary) !important;
}

[data-bs-theme="dark"] .navbar-collapse {
    background-color: var(--bg-primary) !important;
    border-color: var(--border-color) !important;
}

[data-bs-theme="dark"] .navbar-toggler {
    border-color: var(--border-color) !important;
    color: var(--text-primary) !important;
}

[data-bs-theme="dark"] .navbar-toggler:focus {
    box-shadow: 0 0 0 0.25rem rgba(59, 130, 246, 0.25) !important;
}

[data-bs-theme="dark"] .navbar-toggler-icon {
    filter: invert(1);
}

/* Dark Mode for All Text Elements */
[data-bs-theme="dark"] .navbar * {
    color: var(--text-primary);
}

[data-bs-theme="dark"] .navbar .text-muted {
    color: var(--text-muted) !important;
}

[data-bs-theme="dark"] .navbar .small {
    color: var(--text-muted) !important;
}

/* Dark Mode for Buttons */
[data-bs-theme="dark"] .navbar button {
    background-color: transparent !important;
    border-color: var(--border-color) !important;
    color: var(--text-secondary) !important;
}

[data-bs-theme="dark"] .navbar button:hover {
    background-color: var(--bg-tertiary) !important;
    color: var(--text-primary) !important;
}

/* Dark Mode for Links */
[data-bs-theme="dark"] .navbar a {
    color: var(--text-secondary) !important;
}

[data-bs-theme="dark"] .navbar a:hover {
    color: var(--text-primary) !important;
}

/* Dark Mode for Dropdown Items */
[data-bs-theme="dark"] .dropdown-item {
    background-color: transparent !important;
    color: var(--text-secondary) !important;
}

[data-bs-theme="dark"] .dropdown-item:hover,
[data-bs-theme="dark"] .dropdown-item:focus {
    background-color: var(--bg-tertiary) !important;
    color: var(--text-primary) !important;
}

/* Dark Mode for Badges */
[data-bs-theme="dark"] .badge {
    background-color: var(--danger-color) !important;
    color: var(--text-inverse) !important;
}

/* Dark Mode for Icons */
[data-bs-theme="dark"] .fas,
[data-bs-theme="dark"] .far,
[data-bs-theme="dark"] .fab {
    color: inherit !important;
}

/* Force Dark Mode on Specific Classes */
[data-bs-theme="dark"] .navbar-light .navbar-brand {
    color: var(--text-primary) !important;
}

[data-bs-theme="dark"] .navbar-light .navbar-nav .nav-link {
    color: var(--text-secondary) !important;
}

[data-bs-theme="dark"] .navbar-light .navbar-nav .nav-link:hover,
[data-bs-theme="dark"] .navbar-light .navbar-nav .nav-link:focus {
    color: var(--text-primary) !important;
}

[data-bs-theme="dark"] .navbar-light .navbar-toggler {
    border-color: var(--border-color) !important;
}

/* Dark Mode for Custom Elements */
[data-bs-theme="dark"] .avatar-circle {
    background-color: var(--primary-color) !important;
    color: var(--text-inverse) !important;
}

[data-bs-theme="dark"] .position-absolute {
    color: inherit !important;
}

[data-bs-theme="dark"] .translate-middle {
    color: inherit !important;
}

/* Comprehensive Dark Mode Fixes for All Navbar Elements */
[data-bs-theme="dark"] .modern-navbar,
[data-bs-theme="dark"] .navbar,
[data-bs-theme="dark"] .navbar-expand-lg {
    background-color: var(--bg-primary) !important;
    background: var(--bg-primary) !important;
    border-bottom-color: var(--border-color) !important;
}

[data-bs-theme="dark"] .navbar-light {
    background-color: var(--bg-primary) !important;
    background: var(--bg-primary) !important;
}

[data-bs-theme="dark"] .navbar-light .navbar-brand,
[data-bs-theme="dark"] .navbar-brand {
    color: var(--text-primary) !important;
}

[data-bs-theme="dark"] .navbar-light .navbar-brand:hover,
[data-bs-theme="dark"] .navbar-light .navbar-brand:focus,
[data-bs-theme="dark"] .navbar-brand:hover,
[data-bs-theme="dark"] .navbar-brand:focus {
    color: var(--text-primary) !important;
}

[data-bs-theme="dark"] .navbar-light .navbar-nav .nav-link,
[data-bs-theme="dark"] .navbar-nav .nav-link {
    color: var(--text-secondary) !important;
}

[data-bs-theme="dark"] .navbar-light .navbar-nav .nav-link:hover,
[data-bs-theme="dark"] .navbar-light .navbar-nav .nav-link:focus,
[data-bs-theme="dark"] .navbar-nav .nav-link:hover,
[data-bs-theme="dark"] .navbar-nav .nav-link:focus {
    color: var(--text-primary) !important;
    background-color: var(--bg-tertiary) !important;
}

[data-bs-theme="dark"] .navbar-light .navbar-nav .nav-link.active,
[data-bs-theme="dark"] .navbar-light .navbar-nav .show > .nav-link,
[data-bs-theme="dark"] .navbar-nav .nav-link.active,
[data-bs-theme="dark"] .navbar-nav .show > .nav-link {
    color: var(--primary-color) !important;
}

[data-bs-theme="dark"] .navbar-light .navbar-toggler,
[data-bs-theme="dark"] .navbar-toggler {
    border-color: var(--border-color) !important;
    color: var(--text-primary) !important;
}

[data-bs-theme="dark"] .navbar-light .navbar-toggler:focus,
[data-bs-theme="dark"] .navbar-toggler:focus {
    box-shadow: 0 0 0 0.25rem rgba(59, 130, 246, 0.25) !important;
}

[data-bs-theme="dark"] .navbar-light .navbar-toggler-icon,
[data-bs-theme="dark"] .navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28248, 250, 252, 0.75%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e") !important;
}

/* Force Dark Mode on Container Elements */
[data-bs-theme="dark"] .navbar .container-fluid,
[data-bs-theme="dark"] .navbar .container,
[data-bs-theme="dark"] .navbar .container-sm,
[data-bs-theme="dark"] .navbar .container-md,
[data-bs-theme="dark"] .navbar .container-lg,
[data-bs-theme="dark"] .navbar .container-xl,
[data-bs-theme="dark"] .navbar .container-xxl {
    background-color: transparent !important;
    color: var(--text-primary) !important;
}

/* Force Dark Mode on All Navbar Children */
[data-bs-theme="dark"] .navbar *,
[data-bs-theme="dark"] .modern-navbar * {
    color: var(--text-primary) !important;
}

[data-bs-theme="dark"] .navbar .text-muted,
[data-bs-theme="dark"] .modern-navbar .text-muted {
    color: var(--text-muted) !important;
}

[data-bs-theme="dark"] .navbar .text-secondary,
[data-bs-theme="dark"] .modern-navbar .text-secondary {
    color: var(--text-secondary) !important;
}

[data-bs-theme="dark"] .navbar .text-primary,
[data-bs-theme="dark"] .modern-navbar .text-primary {
    color: var(--primary-color) !important;
}

/* Dark Mode for Navbar Buttons */
[data-bs-theme="dark"] .navbar .btn,
[data-bs-theme="dark"] .modern-navbar .btn {
    background-color: transparent !important;
    border-color: var(--border-color) !important;
    color: var(--text-secondary) !important;
}

[data-bs-theme="dark"] .navbar .btn:hover,
[data-bs-theme="dark"] .modern-navbar .btn:hover {
    background-color: var(--bg-tertiary) !important;
    color: var(--text-primary) !important;
}

[data-bs-theme="dark"] .navbar .btn-link,
[data-bs-theme="dark"] .modern-navbar .btn-link {
    color: var(--text-secondary) !important;
    border: none !important;
}

[data-bs-theme="dark"] .navbar .btn-link:hover,
[data-bs-theme="dark"] .modern-navbar .btn-link:hover {
    color: var(--text-primary) !important;
    background-color: var(--bg-tertiary) !important;
}

/* Dark Mode for Navbar Forms */
[data-bs-theme="dark"] .navbar .form-control,
[data-bs-theme="dark"] .modern-navbar .form-control {
    background-color: var(--bg-tertiary) !important;
    border-color: var(--border-color) !important;
    color: var(--text-primary) !important;
}

[data-bs-theme="dark"] .navbar .form-control:focus,
[data-bs-theme="dark"] .modern-navbar .form-control:focus {
    background-color: var(--bg-primary) !important;
    border-color: var(--primary-color) !important;
    color: var(--text-primary) !important;
    box-shadow: 0 0 0 0.25rem rgba(59, 130, 246, 0.25) !important;
}

[data-bs-theme="dark"] .navbar .form-control::placeholder,
[data-bs-theme="dark"] .modern-navbar .form-control::placeholder {
    color: var(--text-muted) !important;
}

/* Dark Mode for Navbar Icons */
[data-bs-theme="dark"] .navbar i,
[data-bs-theme="dark"] .modern-navbar i {
    color: inherit !important;
}

[data-bs-theme="dark"] .navbar .fas,
[data-bs-theme="dark"] .navbar .far,
[data-bs-theme="dark"] .navbar .fab,
[data-bs-theme="dark"] .modern-navbar .fas,
[data-bs-theme="dark"] .modern-navbar .far,
[data-bs-theme="dark"] .modern-navbar .fab {
    color: inherit !important;
}

/* Dark Mode for Navbar Collapse */
[data-bs-theme="dark"] .navbar-collapse {
    background-color: var(--bg-primary) !important;
    border-color: var(--border-color) !important;
}

/* Dark Mode for Navbar Text Elements */
[data-bs-theme="dark"] .navbar .navbar-text,
[data-bs-theme="dark"] .modern-navbar .navbar-text {
    color: var(--text-secondary) !important;
}

/* Dark Mode for Small Text */
[data-bs-theme="dark"] .navbar small,
[data-bs-theme="dark"] .modern-navbar small {
    color: var(--text-muted) !important;
}

/* Dark Mode for Spans */
[data-bs-theme="dark"] .navbar span,
[data-bs-theme="dark"] .modern-navbar span {
    color: inherit !important;
}

/* Dark Mode for Divs */
[data-bs-theme="dark"] .navbar div,
[data-bs-theme="dark"] .modern-navbar div {
    color: inherit !important;
}

/* Override any remaining light theme styles */
[data-bs-theme="dark"] .bg-white {
    background-color: var(--bg-primary) !important;
}

[data-bs-theme="dark"] .bg-light {
    background-color: var(--bg-secondary) !important;
}

[data-bs-theme="dark"] .text-dark {
    color: var(--text-primary) !important;
}

[data-bs-theme="dark"] .border-light {
    border-color: var(--border-color) !important;
}

/* Force Dark Mode on Specific Navbar Classes */
[data-bs-theme="dark"] .navbar-light .navbar-nav .nav-link.disabled {
    color: var(--text-muted) !important;
}

[data-bs-theme="dark"] .navbar-light .navbar-nav .nav-link.dropdown-toggle::after {
    border-top-color: var(--text-secondary) !important;
}

/* Dark Mode for List Items in Navbar */
[data-bs-theme="dark"] .navbar ul,
[data-bs-theme="dark"] .navbar li,
[data-bs-theme="dark"] .modern-navbar ul,
[data-bs-theme="dark"] .modern-navbar li {
    background-color: transparent !important;
    color: inherit !important;
}

/* Dark Mode for Navbar Links */
[data-bs-theme="dark"] .navbar a,
[data-bs-theme="dark"] .modern-navbar a {
    color: var(--text-secondary) !important;
    text-decoration: none !important;
}

[data-bs-theme="dark"] .navbar a:hover,
[data-bs-theme="dark"] .navbar a:focus,
[data-bs-theme="dark"] .modern-navbar a:hover,
[data-bs-theme="dark"] .modern-navbar a:focus {
    color: var(--text-primary) !important;
}

/* Dark Mode for Navbar Headings */
[data-bs-theme="dark"] .navbar h1,
[data-bs-theme="dark"] .navbar h2,
[data-bs-theme="dark"] .navbar h3,
[data-bs-theme="dark"] .navbar h4,
[data-bs-theme="dark"] .navbar h5,
[data-bs-theme="dark"] .navbar h6,
[data-bs-theme="dark"] .modern-navbar h1,
[data-bs-theme="dark"] .modern-navbar h2,
[data-bs-theme="dark"] .modern-navbar h3,
[data-bs-theme="dark"] .modern-navbar h4,
[data-bs-theme="dark"] .modern-navbar h5,
[data-bs-theme="dark"] .modern-navbar h6 {
    color: var(--text-primary) !important;
}

/* Dark Mode for Navbar Paragraphs */
[data-bs-theme="dark"] .navbar p,
[data-bs-theme="dark"] .modern-navbar p {
    color: var(--text-secondary) !important;
}

/* Dark Mode for Navbar Strong/Bold Text */
[data-bs-theme="dark"] .navbar strong,
[data-bs-theme="dark"] .navbar b,
[data-bs-theme="dark"] .modern-navbar strong,
[data-bs-theme="dark"] .modern-navbar b {
    color: var(--text-primary) !important;
}

/* Dark Mode for Navbar Images */
[data-bs-theme="dark"] .navbar img,
[data-bs-theme="dark"] .modern-navbar img {
    filter: brightness(0.9) contrast(1.1);
}

/* Dark Mode for Navbar Badges */
[data-bs-theme="dark"] .navbar .badge,
[data-bs-theme="dark"] .modern-navbar .badge {
    background-color: var(--danger-color) !important;
    color: var(--text-inverse) !important;
}

/* Dark Mode for Navbar Cards (if any) */
[data-bs-theme="dark"] .navbar .card,
[data-bs-theme="dark"] .modern-navbar .card {
    background-color: var(--bg-tertiary) !important;
    border-color: var(--border-color) !important;
    color: var(--text-primary) !important;
}

/* Dark Mode for Navbar Alerts (if any) */
[data-bs-theme="dark"] .navbar .alert,
[data-bs-theme="dark"] .modern-navbar .alert {
    background-color: var(--bg-tertiary) !important;
    border-color: var(--border-color) !important;
    color: var(--text-primary) !important;
}

/* Dark Mode for Navbar Tables (if any) */
[data-bs-theme="dark"] .navbar .table,
[data-bs-theme="dark"] .modern-navbar .table {
    background-color: var(--bg-primary) !important;
    color: var(--text-primary) !important;
}

/* Dark Mode for Navbar Progress Bars (if any) */
[data-bs-theme="dark"] .navbar .progress,
[data-bs-theme="dark"] .modern-navbar .progress {
    background-color: var(--bg-tertiary) !important;
}

/* Dark Mode for Navbar Spinners (if any) */
[data-bs-theme="dark"] .navbar .spinner-border,
[data-bs-theme="dark"] .navbar .spinner-grow,
[data-bs-theme="dark"] .modern-navbar .spinner-border,
[data-bs-theme="dark"] .modern-navbar .spinner-grow {
    color: var(--primary-color) !important;
}

/* Force Dark Mode on Any Remaining Elements */
[data-bs-theme="dark"] .navbar-expand-lg .navbar-nav,
[data-bs-theme="dark"] .navbar-expand-lg .navbar-collapse {
    background-color: transparent !important;
}

/* Dark Mode for Navbar Flex Items */
[data-bs-theme="dark"] .navbar .d-flex,
[data-bs-theme="dark"] .navbar .flex-row,
[data-bs-theme="dark"] .navbar .flex-column,
[data-bs-theme="dark"] .modern-navbar .d-flex,
[data-bs-theme="dark"] .modern-navbar .flex-row,
[data-bs-theme="dark"] .modern-navbar .flex-column {
    background-color: transparent !important;
    color: inherit !important;
}

/* Dark Mode for Navbar Grid Items */
[data-bs-theme="dark"] .navbar .row,
[data-bs-theme="dark"] .navbar .col,
[data-bs-theme="dark"] .navbar [class*="col-"],
[data-bs-theme="dark"] .modern-navbar .row,
[data-bs-theme="dark"] .modern-navbar .col,
[data-bs-theme="dark"] .modern-navbar [class*="col-"] {
    background-color: transparent !important;
    color: inherit !important;
}

/* Ensure all text elements inherit proper colors */
[data-bs-theme="dark"] .navbar *:not(.btn):not(.badge):not(.alert),
[data-bs-theme="dark"] .modern-navbar *:not(.btn):not(.badge):not(.alert) {
    color: inherit !important;
}

/* Search Results Styles */
.search-container {
    position: relative;
    width: 100%;
    max-width: 400px;
}

.search-input {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 2.5rem;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    background: var(--bg-primary);
    color: var(--text-primary);
    font-size: 0.9rem;
    transition: all var(--transition-fast);
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
    background: var(--bg-primary);
}

.search-icon {
    position: absolute;
    left: 0.875rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-muted);
    font-size: 0.9rem;
    pointer-events: none;
}

.search-results {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    z-index: 1050;
    max-height: 400px;
    overflow-y: auto;
    display: none;
    margin-top: 0.5rem;
}

.search-results-header {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid var(--border-light);
    background: var(--bg-tertiary);
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.85rem;
    font-weight: 600;
    color: var(--text-primary);
}

.search-close {
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: var(--radius-sm);
    transition: all var(--transition-fast);
}

.search-close:hover {
    background: var(--bg-secondary);
    color: var(--text-primary);
}

.search-result-item {
    display: flex;
    align-items: center;
    padding: 0.875rem 1rem;
    border-bottom: 1px solid var(--border-light);
    cursor: pointer;
    transition: all var(--transition-fast);
    text-decoration: none;
    color: var(--text-primary);
}

.search-result-item:hover,
.search-result-item.active {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.search-result-item:last-child {
    border-bottom: none;
}

.search-result-icon {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background: rgba(37, 99, 235, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    margin-right: 0.875rem;
    flex-shrink: 0;
}

.search-result-content {
    flex: 1;
    min-width: 0;
}

.search-result-title {
    font-weight: 500;
    font-size: 0.9rem;
    line-height: 1.3;
    margin-bottom: 0.25rem;
    color: var(--text-primary);
}

.search-result-title mark {
    background: rgba(37, 99, 235, 0.2);
    color: var(--primary-color);
    padding: 0.1rem 0.2rem;
    border-radius: 0.2rem;
}

.search-result-subtitle {
    font-size: 0.8rem;
    color: var(--text-muted);
    line-height: 1.2;
}

.search-result-date {
    font-size: 0.75rem;
    color: var(--text-muted);
    margin-top: 0.25rem;
}

.search-result-type {
    font-size: 0.7rem;
    font-weight: 600;
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-left: 0.5rem;
    flex-shrink: 0;
}

.search-loading,
.search-error,
.search-no-results {
    padding: 2rem 1rem;
    text-align: center;
    color: var(--text-muted);
}

.search-loading i {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    color: var(--primary-color);
}

.search-error {
    color: var(--danger-color);
}

.search-no-results i {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    color: var(--text-muted);
}

.search-no-results p {
    margin: 0;
    font-size: 0.9rem;
}

/* Mobile Search Styles */
.navbar-search-mobile .search-container {
    max-width: 100%;
}

.navbar-search-mobile .search-results {
    position: static;
    margin-top: 0.75rem;
    border-radius: var(--radius-md);
}

/* Dark Mode Search Styles */
[data-bs-theme="dark"] .search-input {
    background: var(--bg-tertiary);
    border-color: var(--border-color);
    color: var(--text-primary);
}

[data-bs-theme="dark"] .search-input:focus {
    background: var(--bg-primary);
    border-color: var(--primary-color);
}

[data-bs-theme="dark"] .search-results {
    background: var(--bg-primary);
    border-color: var(--border-color);
}

[data-bs-theme="dark"] .search-results-header {
    background: var(--bg-tertiary);
    border-bottom-color: var(--border-light);
    color: var(--text-primary);
}

[data-bs-theme="dark"] .search-result-item {
    color: var(--text-primary);
    border-bottom-color: var(--border-light);
}

[data-bs-theme="dark"] .search-result-item:hover,
[data-bs-theme="dark"] .search-result-item.active {
    background: var(--bg-tertiary);
}

[data-bs-theme="dark"] .search-result-icon {
    background: rgba(37, 99, 235, 0.15);
}

[data-bs-theme="dark"] .search-result-title mark {
    background: rgba(37, 99, 235, 0.3);
    color: var(--primary-color);
}

/* Search Results Scrollbar */
.search-results::-webkit-scrollbar {
    width: 6px;
}

.search-results::-webkit-scrollbar-track {
    background: transparent;
}

.search-results::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 3px;
}

[data-bs-theme="dark"] .search-results::-webkit-scrollbar-thumb {
    background: var(--border-dark);
}

/* Notification Badge Styles */
.notification-badge {
    position: absolute;
    top: -6px;
    right: -6px;
    background: var(--danger-color);
    color: white;
    font-size: 0.7rem;
    font-weight: 600;
    padding: 0.2rem 0.4rem;
    border-radius: 50px;
    min-width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 1;
    border: 2px solid var(--bg-primary);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7);
    }
    70% {
        box-shadow: 0 0 0 6px rgba(220, 53, 69, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(220, 53, 69, 0);
    }
}

/* Brand Logo Styles */
.brand-logo {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.75rem;
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-fast);
}

.brand-logo i {
    font-size: 1.25rem;
    color: white;
}

.brand-text {
    display: flex;
    flex-direction: column;
    line-height: 1.2;
}

.brand-name {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
}

.brand-subtitle {
    font-size: 0.75rem;
    color: var(--text-muted);
    margin: 0;
    font-weight: 500;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Bootstrap components
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.forEach(tooltipTriggerEl => {
        new bootstrap.Tooltip(tooltipTriggerEl, {
            trigger: 'hover',
            placement: 'bottom',
            delay: { show: 100, hide: 100 }
        });
    });

    // Initialize dropdowns
    const dropdownTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="dropdown"]'));
    dropdownTriggerList.forEach(dropdownTriggerEl => new bootstrap.Dropdown(dropdownTriggerEl));

    // Handle sidebar toggle - Desktop
    const sidebarToggleBtn = document.getElementById('sidebarToggleBtn');
    const mobileSidebarToggle = document.getElementById('mobileSidebarToggle');
    const sidebar = document.getElementById('sidebar');
    const overlay = document.querySelector('.sidebar-overlay') || createOverlay();

    // Create overlay for mobile
    function createOverlay() {
        const overlay = document.createElement('div');
        overlay.className = 'sidebar-overlay';
        overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 998;
            display: none;
            transition: opacity 0.3s ease;
        `;
        document.body.appendChild(overlay);
        return overlay;
    }

    // Desktop sidebar toggle
    if (sidebarToggleBtn) {
        sidebarToggleBtn.addEventListener('click', function(event) {
            event.preventDefault();
            event.stopPropagation();

            const isCollapsed = document.body.classList.contains('sidebar-collapsed');
            const mainContent = document.querySelector('.main-content');
            const navbar = document.querySelector('.modern-navbar');

            // Toggle collapsed state
            document.body.classList.toggle('sidebar-collapsed');

            // Update button state
            this.classList.toggle('active');

            // Add ripple effect
            createRipple(this, event);

            // Update layout immediately
            if (document.body.classList.contains('sidebar-collapsed')) {
                // Collapsed state
                if (sidebar) {
                    sidebar.style.width = var(--sidebar-collapsed-width) || '70px';
                }
                if (mainContent) {
                    mainContent.style.marginLeft = var(--sidebar-collapsed-width) || '70px';
                    mainContent.style.width = 'calc(100% - ' + (var(--sidebar-collapsed-width) || '70px') + ')';
                }
                if (navbar) {
                    navbar.style.left = var(--sidebar-collapsed-width) || '70px';
                    navbar.style.width = 'calc(100% - ' + (var(--sidebar-collapsed-width) || '70px') + ')';
                }
            } else {
                // Expanded state
                if (sidebar) {
                    sidebar.style.width = var(--sidebar-width) || '280px';
                }
                if (mainContent) {
                    mainContent.style.marginLeft = var(--sidebar-width) || '280px';
                    mainContent.style.width = 'calc(100% - ' + (var(--sidebar-width) || '280px') + ')';
                }
                if (navbar) {
                    navbar.style.left = var(--sidebar-width) || '280px';
                    navbar.style.width = 'calc(100% - ' + (var(--sidebar-width) || '280px') + ')';
                }
            }

            // Dispatch custom event for layout update
            document.dispatchEvent(new CustomEvent('sidebarToggled', {
                detail: { collapsed: document.body.classList.contains('sidebar-collapsed') }
            }));

            // Save state to localStorage
            localStorage.setItem('sidebarCollapsed', document.body.classList.contains('sidebar-collapsed'));
        });
    }

    // Mobile sidebar toggle
    if (mobileSidebarToggle && sidebar) {
        mobileSidebarToggle.addEventListener('click', function(event) {
            event.preventDefault();
            event.stopPropagation();

            sidebar.classList.toggle('mobile-active');

            if (sidebar.classList.contains('mobile-active')) {
                overlay.style.display = 'block';
                document.body.style.overflow = 'hidden'; // Prevent scrolling
            } else {
                overlay.style.display = 'none';
                document.body.style.overflow = '';
            }

            // Add ripple effect
            createRipple(this, event);
        });
    }

    // Close sidebar when clicking overlay
    if (overlay) {
        overlay.addEventListener('click', function() {
            sidebar.classList.remove('mobile-active');
            this.style.display = 'none';
            document.body.style.overflow = '';
        });
    }

    // Ripple effect function
    function createRipple(button, event) {
        const ripple = document.createElement('span');
        const rect = button.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = event.clientX - rect.left - size / 2;
        const y = event.clientY - rect.top - size / 2;

        ripple.style.cssText = `
            position: absolute;
            width: ${size}px;
            height: ${size}px;
            left: ${x}px;
            top: ${y}px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            transform: scale(0);
            animation: ripple 0.6s ease-out;
            pointer-events: none;
        `;

        button.appendChild(ripple);

        setTimeout(() => {
            ripple.remove();
        }, 600);
    }

    // Add ripple animation CSS
    const style = document.createElement('style');
    style.textContent = `
        @keyframes ripple {
            to {
                transform: scale(2);
                opacity: 0;
            }
        }
    `;
    document.head.appendChild(style);

    // Check localStorage for sidebar state on page load
    if (localStorage.getItem('sidebarCollapsed') === 'true') {
        document.body.classList.add('sidebar-collapsed');
        if (sidebar) {
            sidebar.style.width = '70px';
        }
    }

    // Handle profile menu clicks
    document.querySelector('.profile-dropdown').addEventListener('click', function(e) {
        const link = e.target.closest('a');
        if (link) {
            const href = link.getAttribute('href');
            if (href === 'logout.php') {
                e.preventDefault();
                if (confirm('Apakah Anda yakin ingin keluar?')) {
                    window.location.href = href;
                }
            }
        }
    });

    // Handle notification clicks
    document.querySelectorAll('.notification-item').forEach(item => {
        item.addEventListener('click', function(e) {
            if (!this.classList.contains('unread')) return;

            const notificationId = this.dataset.id;
            if (notificationId) {
                fetch('mark_notification_read.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ notification_id: notificationId })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        this.classList.remove('unread');
                        updateNotificationBadge();
                    }
                })
                .catch(error => console.error('Error:', error));
            }
        });
    });

    // Update notification badge
    function updateNotificationBadge() {
        const badge = document.querySelector('.badge');
        if (badge) {
            const count = parseInt(badge.textContent) - 1;
            if (count <= 0) {
                badge.remove();
            } else {
                badge.textContent = count;
            }
        }
    }

    // Handle window resize
    window.addEventListener('resize', function() {
        if (window.innerWidth <= 991.98) {
            document.body.classList.remove('sidebar-collapsed');
            if (sidebar) {
                sidebar.style.width = '280px';
            }
        }
    });

    // Initialize search functionality
    initializeSearch();

    // Initialize dark mode toggle functionality
    const darkModeToggle = document.querySelector('[data-theme-toggle]');
    if (darkModeToggle) {
        darkModeToggle.addEventListener('click', function() {
            const html = document.documentElement;
            const currentTheme = html.getAttribute('data-bs-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

            // Update theme
            html.setAttribute('data-bs-theme', newTheme);
            localStorage.setItem('theme', newTheme);

            // Update all theme toggle buttons
            document.querySelectorAll('[data-theme-toggle]').forEach(btn => {
                const icon = btn.querySelector('i');
                if (icon) {
                    icon.className = newTheme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
                }
                btn.setAttribute('title', newTheme === 'dark' ? 'Switch to Light Mode' : 'Switch to Dark Mode');
            });

            // Dispatch theme change event
            document.dispatchEvent(new CustomEvent('themeChanged', {
                detail: { theme: newTheme }
            }));
        });
    }

    // Apply saved theme on page load
    const savedTheme = localStorage.getItem('theme') || 'light';
    document.documentElement.setAttribute('data-bs-theme', savedTheme);

    // Update theme toggle buttons on page load
    document.querySelectorAll('[data-theme-toggle]').forEach(btn => {
        const icon = btn.querySelector('i');
        if (icon) {
            icon.className = savedTheme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
        }
        btn.setAttribute('title', savedTheme === 'dark' ? 'Switch to Light Mode' : 'Switch to Dark Mode');
    });

    // Search functionality
    function initializeSearch() {
        const searchInputs = document.querySelectorAll('#globalSearch, #mobileGlobalSearch');
        let searchTimeout;

        searchInputs.forEach(input => {
            if (input) {
                input.addEventListener('input', function() {
                    const query = this.value.trim();
                    clearTimeout(searchTimeout);

                    if (query.length >= 2) {
                        searchTimeout = setTimeout(() => {
                            performSearch(query, this);
                        }, 300);
                    } else {
                        hideSearchResults();
                    }
                });

                // Handle keyboard navigation
                input.addEventListener('keydown', function(e) {
                    const resultsContainer = document.getElementById('searchResults');
                    const items = resultsContainer.querySelectorAll('.search-result-item');

                    if (e.key === 'ArrowDown') {
                        e.preventDefault();
                        navigateResults(items, 1);
                    } else if (e.key === 'ArrowUp') {
                        e.preventDefault();
                        navigateResults(items, -1);
                    } else if (e.key === 'Enter') {
                        e.preventDefault();
                        const activeItem = resultsContainer.querySelector('.search-result-item.active');
                        if (activeItem) {
                            window.location.href = activeItem.dataset.url;
                        }
                    } else if (e.key === 'Escape') {
                        hideSearchResults();
                        this.blur();
                    }
                });

                // Hide results when input loses focus (with delay for clicks)
                input.addEventListener('blur', function() {
                    setTimeout(() => {
                        hideSearchResults();
                    }, 200);
                });
            }
        });

        // Close search results when clicking outside
        document.addEventListener('click', function(e) {
            if (!e.target.closest('.navbar-search') && !e.target.closest('.navbar-search-mobile')) {
                hideSearchResults();
            }
        });
    }

    function performSearch(query, inputElement) {
        const resultsContainer = document.getElementById('searchResults');
        if (!resultsContainer) return;

        // Show loading state
        resultsContainer.innerHTML = '<div class="search-loading"><i class="fas fa-spinner fa-spin"></i> Searching...</div>';
        resultsContainer.style.display = 'block';

        fetch(`search.php?q=${encodeURIComponent(query)}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displaySearchResults(data.results, query);
                } else {
                    resultsContainer.innerHTML = '<div class="search-error">Error: ' + data.message + '</div>';
                }
            })
            .catch(error => {
                console.error('Search error:', error);
                resultsContainer.innerHTML = '<div class="search-error">Search failed. Please try again.</div>';
            });
    }

    function displaySearchResults(results, query) {
        const resultsContainer = document.getElementById('searchResults');
        if (!resultsContainer) return;

        if (results.length === 0) {
            resultsContainer.innerHTML = `
                <div class="search-no-results">
                    <i class="fas fa-search"></i>
                    <p>No results found for "${query}"</p>
                </div>
            `;
            return;
        }

        let html = `<div class="search-results-header">
            <span>Search Results (${results.length})</span>
            <button class="search-close" onclick="hideSearchResults()">
                <i class="fas fa-times"></i>
            </button>
        </div>`;

        results.forEach((result, index) => {
            html += `
                <div class="search-result-item ${index === 0 ? 'active' : ''}"
                     data-url="${result.url}"
                     onclick="window.location.href='${result.url}'">
                    <div class="search-result-icon">
                        <i class="${result.icon}"></i>
                    </div>
                    <div class="search-result-content">
                        <div class="search-result-title">${highlightQuery(result.title, query)}</div>
                        <div class="search-result-subtitle">${result.subtitle}</div>
                        ${result.date ? `<div class="search-result-date">${formatDate(result.date)}</div>` : ''}
                    </div>
                    <div class="search-result-type">${result.type}</div>
                </div>
            `;
        });

        resultsContainer.innerHTML = html;
        resultsContainer.style.display = 'block';
    }

    function hideSearchResults() {
        const resultsContainer = document.getElementById('searchResults');
        if (resultsContainer) {
            resultsContainer.style.display = 'none';
            resultsContainer.innerHTML = '';
        }
    }

    function navigateResults(items, direction) {
        const currentActive = document.querySelector('.search-result-item.active');
        let newIndex = 0;

        if (currentActive) {
            const currentIndex = Array.from(items).indexOf(currentActive);
            newIndex = currentIndex + direction;
            currentActive.classList.remove('active');
        }

        if (newIndex < 0) newIndex = items.length - 1;
        if (newIndex >= items.length) newIndex = 0;

        if (items[newIndex]) {
            items[newIndex].classList.add('active');
            items[newIndex].scrollIntoView({ block: 'nearest' });
        }
    }

    function highlightQuery(text, query) {
        const regex = new RegExp(`(${query})`, 'gi');
        return text.replace(regex, '<mark>$1</mark>');
    }

    function formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('id-ID', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    }

    // Update notification badge count
    function updateNotificationBadge() {
        const badge = document.querySelector('.notification-badge');
        const unreadItems = document.querySelectorAll('.notification-item.unread');
        const count = unreadItems.length;

        if (count > 0) {
            if (badge) {
                badge.textContent = count;
            } else {
                // Create badge if it doesn't exist
                const notificationBtn = document.querySelector('#notificationsDropdown');
                if (notificationBtn) {
                    const newBadge = document.createElement('span');
                    newBadge.className = 'notification-badge';
                    newBadge.textContent = count;
                    notificationBtn.appendChild(newBadge);
                }
            }
        } else {
            if (badge) {
                badge.remove();
            }
        }
    }

    // Control sidebar toggle functionality - Removed duplicate implementation
    // The actual implementation is in control-sidebar.js

    // Dynamic Greeting System
    function updateDynamicGreeting() {
        const now = new Date();
        const hour = now.getHours();
        const greetingElement = document.getElementById('dynamicGreeting');
        const greetingIcon = document.getElementById('greetingIcon');

        let greeting = '';
        let icon = '';
        let color = '';

        if (hour >= 5 && hour < 12) {
            // Pagi (05:00 - 11:59)
            greeting = 'Selamat Pagi';
            icon = 'fas fa-sun';
            color = '#ff9800'; // Orange untuk pagi
        } else if (hour >= 12 && hour < 15) {
            // Siang (12:00 - 14:59)
            greeting = 'Selamat Siang';
            icon = 'fas fa-sun';
            color = '#ffc107'; // Kuning untuk siang
        } else if (hour >= 15 && hour < 18) {
            // Sore (15:00 - 17:59)
            greeting = 'Selamat Sore';
            icon = 'fas fa-cloud-sun';
            color = '#ff5722'; // Orange kemerahan untuk sore
        } else {
            // Malam (18:00 - 04:59)
            greeting = 'Selamat Malam';
            icon = 'fas fa-moon';
            color = '#3f51b5'; // Biru untuk malam
        }

        if (greetingElement) {
            // Add fade effect when changing greeting
            greetingElement.style.opacity = '0.5';
            setTimeout(() => {
                greetingElement.textContent = greeting;
                greetingElement.style.color = color;
                greetingElement.style.opacity = '1';
            }, 150);
        }

        if (greetingIcon) {
            // Add rotation effect when changing icon
            greetingIcon.style.transform = 'rotate(180deg)';
            setTimeout(() => {
                greetingIcon.className = icon;
                greetingIcon.style.color = color;
                greetingIcon.style.transform = 'rotate(0deg)';

                // Keep background transparent with stronger CSS
                const brandLogo = greetingIcon.parentElement;
                if (brandLogo) {
                    brandLogo.style.background = 'transparent';
                    brandLogo.style.backgroundColor = 'transparent';
                    brandLogo.style.backgroundImage = 'none';
                    brandLogo.style.setProperty('background', 'transparent', 'important');
                    brandLogo.style.setProperty('background-color', 'transparent', 'important');
                    brandLogo.style.setProperty('background-image', 'none', 'important');
                }
            }, 150);
        }
    }

    // Initialize dynamic greeting immediately
    updateDynamicGreeting();

    // Force update after a short delay to ensure DOM is ready
    setTimeout(updateDynamicGreeting, 100);

    // Update greeting every minute
    setInterval(updateDynamicGreeting, 60000);

    // Debug: Log current state
    console.log('Dynamic greeting initialized');
    console.log('Current hour:', new Date().getHours());
    console.log('Greeting element:', document.getElementById('dynamicGreeting'));
    console.log('Icon element:', document.getElementById('greetingIcon'));

    // Make updateDynamicGreeting available globally for debugging
    window.updateDynamicGreeting = updateDynamicGreeting;

    // Force update on window load as well
    window.addEventListener('load', function() {
        setTimeout(updateDynamicGreeting, 200);
    });
});

// Additional script to ensure greeting updates
document.addEventListener('DOMContentLoaded', function() {
    // Backup greeting update function
    function forceGreetingUpdate() {
        const now = new Date();
        const hour = now.getHours();
        const greetingElement = document.getElementById('dynamicGreeting');
        const greetingIcon = document.getElementById('greetingIcon');

        console.log('Force updating greeting for hour:', hour);

        let greeting = '';
        let icon = '';
        let color = '';

        if (hour >= 5 && hour < 12) {
            greeting = 'Selamat Pagi';
            icon = 'fas fa-sun';
            color = '#ff9800';
        } else if (hour >= 12 && hour < 15) {
            greeting = 'Selamat Siang';
            icon = 'fas fa-sun';
            color = '#ffc107';
        } else if (hour >= 15 && hour < 18) {
            greeting = 'Selamat Sore';
            icon = 'fas fa-cloud-sun';
            color = '#ff5722';
        } else {
            greeting = 'Selamat Malam';
            icon = 'fas fa-moon';
            color = '#3f51b5';
        }

        if (greetingElement) {
            greetingElement.textContent = greeting;
            greetingElement.style.color = color;
            console.log('Updated greeting text to:', greeting);
        } else {
            console.error('Greeting element not found!');
        }

        if (greetingIcon) {
            greetingIcon.className = icon;
            greetingIcon.style.color = color;
            console.log('Updated icon to:', icon, 'with color:', color);
        } else {
            console.error('Greeting icon not found!');
        }
    }

    // Run force update after DOM is ready
    setTimeout(forceGreetingUpdate, 500);

    // Make it available globally
    window.forceGreetingUpdate = forceGreetingUpdate;
});

// Real-time Notifications System
document.addEventListener('DOMContentLoaded', function() {
    let notificationCheckInterval;
    let lastNotificationCount = <?= $unreadNotifications ?>;

    // Function to fetch latest notifications
    async function fetchNotifications() {
        try {
            const response = await fetch('api/get_notifications.php');
            if (!response.ok) throw new Error('Network response was not ok');

            const data = await response.json();
            updateNotificationDisplay(data);
        } catch (error) {
            console.error('Error fetching notifications:', error);
        }
    }

    // Function to update notification display
    function updateNotificationDisplay(data) {
        const notificationsList = document.getElementById('notificationsList');
        const notificationBadge = document.querySelector('.notification-badge');
        const notificationButton = document.getElementById('notificationsDropdown');

        // Update unread count
        if (data.unread_count !== lastNotificationCount) {
            lastNotificationCount = data.unread_count;

            if (data.unread_count > 0) {
                if (notificationBadge) {
                    notificationBadge.textContent = data.unread_count;
                    notificationBadge.style.display = 'inline-block';
                } else {
                    // Create badge if it doesn't exist
                    const badge = document.createElement('span');
                    badge.className = 'notification-badge';
                    badge.textContent = data.unread_count;
                    notificationButton.appendChild(badge);
                }
            } else {
                if (notificationBadge) {
                    notificationBadge.style.display = 'none';
                }
            }
        }

        // Update notifications list
        if (data.notifications && notificationsList) {
            let notificationsHTML = '';

            if (data.notifications.length === 0) {
                notificationsHTML = `
                    <div class="empty-notifications text-center py-4">
                        <i class="fas fa-bell-slash text-muted mb-3" style="font-size: 2rem;"></i>
                        <h6 class="text-muted">No notifications</h6>
                        <p class="text-muted small mb-0">You're all caught up!</p>
                    </div>
                `;
            } else {
                data.notifications.forEach(notification => {
                    const type = notification.type || 'info';
                    const title = notification.title || 'No Title';
                    const message = notification.message || 'No Message';
                    const source = notification.source || '';
                    const isRead = notification.is_read;
                    const createdAt = notification.created_at;
                    const id = notification.id || 0;

                    // Determine icon based on type
                    let iconClass = 'fas fa-info-circle text-info';
                    if (type === 'error') {
                        iconClass = 'fas fa-exclamation-triangle text-danger';
                    } else if (type === 'warning') {
                        iconClass = 'fas fa-exclamation-circle text-warning';
                    } else if (type === 'success') {
                        iconClass = 'fas fa-check-circle text-success';
                    }

                    // Truncate message
                    const truncatedMessage = message.length > 60 ? message.substring(0, 60) + '...' : message;

                    // Format date
                    const date = new Date(createdAt);
                    const formattedDate = date.toLocaleDateString('en-US', {
                        month: 'short',
                        day: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit'
                    });

                    notificationsHTML += `
                        <a class="dropdown-item notification-item ${!isRead ? 'unread' : ''}"
                           href="notifications.php" data-id="${id}" data-type="${type}">
                            <div class="d-flex align-items-start">
                                <div class="notification-icon me-3">
                                    <i class="${iconClass}"></i>
                                </div>
                                <div class="notification-content flex-grow-1">
                                    <p class="mb-1 fw-medium notification-title">${escapeHtml(title)}</p>
                                    <p class="mb-1 small text-muted notification-message">${escapeHtml(truncatedMessage)}</p>
                                    <small class="text-muted">
                                        <i class="fas fa-clock me-1"></i>
                                        ${formattedDate}
                                        ${source ? `| <i class="fas fa-tag me-1"></i>${escapeHtml(source)}` : ''}
                                    </small>
                                </div>
                                ${!isRead ? '<div class="unread-indicator"></div>' : ''}
                            </div>
                        </a>
                    `;
                });
            }

            notificationsList.innerHTML = notificationsHTML;
        }
    }

    // Helper function to escape HTML
    function escapeHtml(text) {
        const map = {
            '&': '&amp;',
            '<': '&lt;',
            '>': '&gt;',
            '"': '&quot;',
            "'": '&#039;'
        };
        return text.replace(/[&<>"']/g, function(m) { return map[m]; });
    }

    // Start real-time notifications
    function startNotificationUpdates() {
        // Check for new notifications every 30 seconds
        notificationCheckInterval = setInterval(fetchNotifications, 30000);

        // Also check when page becomes visible
        document.addEventListener('visibilitychange', function() {
            if (!document.hidden) {
                fetchNotifications();
            }
        });
    }

    // Initialize real-time notifications
    startNotificationUpdates();

    // Manual refresh button (if needed)
    window.refreshNotifications = fetchNotifications;

    console.log('Real-time notifications system initialized');
});
</script>

<!-- All JavaScript moved to external files for better performance and no conflicts -->
<script>
// Initialize unread notifications count for external script
window.initialUnreadCount = <?= $unreadNotifications ?>;

// Initialize navbar theme toggle
document.addEventListener('DOMContentLoaded', function() {
    // Update navbar theme toggle button on page load
    const savedTheme = localStorage.getItem('theme') || 'light';
    updateNavbarThemeToggle(savedTheme);

    // Listen for theme changes
    document.addEventListener('themeChanged', function(e) {
        updateNavbarThemeToggle(e.detail.theme);
    });
});

function updateNavbarThemeToggle(theme) {
    const navbarToggle = document.querySelector('.navbar [data-theme-toggle]');
    if (navbarToggle) {
        const icon = navbarToggle.querySelector('i');
        const text = navbarToggle.querySelector('span');

        if (icon) {
            icon.className = theme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
        }

        if (text) {
            text.textContent = theme === 'dark' ? 'Light' : 'Dark';
        }

        navbarToggle.setAttribute('title', theme === 'dark' ? 'Switch to Light Mode' : 'Switch to Dark Mode');
        navbarToggle.setAttribute('data-bs-original-title', theme === 'dark' ? 'Switch to Light Mode' : 'Switch to Dark Mode');
    }
}
</script>
<script src="assets/js/navbar-functions.js?v=<?= time() ?>"></script>