<?php
require_once 'includes/config/database.php';
require_once 'includes/helpers/functions.php';
require_once 'includes/helpers/notifications.php';

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isLoggedIn()) {
    setFlashMessage('danger', 'Silakan login terlebih dahulu');
    redirect('/keuangan/login.php');
}

$currentUser = getCurrentUser();
$currentPage = 'faq';

// FAQ Data
$faqs = [
    [
        'category' => 'Umum',
        'questions' => [
            [
                'question' => 'Apa itu KeuanganKu?',
                'answer' => 'KeuanganKu adalah aplikasi manajemen keuangan personal yang membantu Anda mencatat, menganalisis, dan merencanakan keuangan dengan lebih baik. Aplikasi ini menyediakan fitur pencatatan transaksi, budgeting, tracking investasi, dan berbagai tools keuangan lainnya.'
            ],
            [
                'question' => 'Apakah data saya aman?',
                'answer' => 'Ya, keamanan data adalah prioritas utama kami. Semua data disimpan dengan enkripsi dan hanya Anda yang memiliki akses ke informasi keuangan pribadi Anda. Kami tidak membagikan data Anda kepada pihak ketiga.'
            ],
            [
                'question' => 'Apakah KeuanganKu gratis?',
                'answer' => 'Ya, KeuanganKu dapat digunakan secara gratis dengan fitur-fitur dasar yang lengkap untuk mengelola keuangan personal Anda.'
            ]
        ]
    ],
    [
        'category' => 'Transaksi',
        'questions' => [
            [
                'question' => 'Bagaimana cara menambah transaksi?',
                'answer' => 'Untuk menambah transaksi: 1) Buka halaman Transaksi, 2) Klik tombol "Tambah Transaksi", 3) Isi form dengan kategori, jenis, jumlah, tanggal, dan keterangan, 4) Klik "Simpan". Transaksi akan langsung tercatat dan mempengaruhi saldo Anda.'
            ],
            [
                'question' => 'Bisakah saya mengedit transaksi yang sudah dicatat?',
                'answer' => 'Ya, Anda dapat mengedit transaksi kapan saja. Klik ikon edit (pensil) pada transaksi yang ingin diubah, lakukan perubahan yang diperlukan, dan simpan. Perubahan akan langsung mempengaruhi laporan dan saldo.'
            ],
            [
                'question' => 'Bagaimana cara menghapus transaksi?',
                'answer' => 'Untuk menghapus transaksi, klik ikon hapus (tempat sampah) pada transaksi yang ingin dihapus, lalu konfirmasi penghapusan. Hati-hati karena tindakan ini tidak dapat dibatalkan.'
            ]
        ]
    ],
    [
        'category' => 'Kategori',
        'questions' => [
            [
                'question' => 'Apa itu kategori dan mengapa penting?',
                'answer' => 'Kategori adalah pengelompokan transaksi berdasarkan jenis atau tujuan, seperti "Makanan", "Transportasi", "Gaji", dll. Kategori penting untuk menganalisis pola pengeluaran dan membuat anggaran yang efektif.'
            ],
            [
                'question' => 'Bagaimana cara membuat kategori baru?',
                'answer' => 'Buka halaman Kategori, klik "Tambah Kategori", isi nama kategori dan pilih tipe (Pemasukan/Pengeluaran), lalu simpan. Kategori baru akan langsung tersedia saat menambah transaksi.'
            ],
            [
                'question' => 'Bisakah saya mengubah atau menghapus kategori?',
                'answer' => 'Ya, Anda dapat mengubah nama kategori kapan saja. Namun, berhati-hatilah saat menghapus kategori karena akan mempengaruhi transaksi yang sudah menggunakan kategori tersebut.'
            ]
        ]
    ],
    [
        'category' => 'Anggaran',
        'questions' => [
            [
                'question' => 'Bagaimana cara membuat anggaran?',
                'answer' => 'Buka halaman Anggaran, klik "Tambah Anggaran", pilih kategori, tentukan jumlah anggaran dan periode (bulanan/tahunan), set persentase peringatan, lalu simpan. Sistem akan memantau penggunaan anggaran secara otomatis.'
            ],
            [
                'question' => 'Apa yang terjadi jika anggaran terlampaui?',
                'answer' => 'Jika anggaran terlampaui, sistem akan menampilkan notifikasi peringatan. Anda dapat melihat status anggaran di dashboard dan halaman anggaran. Ini membantu Anda lebih aware terhadap pengeluaran.'
            ],
            [
                'question' => 'Bagaimana cara mengatur peringatan anggaran?',
                'answer' => 'Saat membuat atau mengedit anggaran, Anda dapat mengatur persentase peringatan (default 80%). Sistem akan memberikan notifikasi ketika pengeluaran mencapai persentase tersebut dari total anggaran.'
            ]
        ]
    ],
    [
        'category' => 'Investasi',
        'questions' => [
            [
                'question' => 'Jenis investasi apa saja yang bisa dicatat?',
                'answer' => 'KeuanganKu mendukung berbagai jenis investasi: Saham, Obligasi, Reksadana, Emas, Properti, Deposito, Cryptocurrency, dan lainnya. Anda dapat mencatat nilai beli dan memperbarui nilai terkini untuk tracking profit/loss.'
            ],
            [
                'question' => 'Bagaimana cara update nilai investasi?',
                'answer' => 'Buka halaman Investasi, klik edit pada investasi yang ingin diupdate, ubah nilai "Nilai Sekarang" sesuai harga pasar terkini, lalu simpan. Sistem akan otomatis menghitung profit/loss.'
            ],
            [
                'question' => 'Apakah ada fitur tracking otomatis harga investasi?',
                'answer' => 'Saat ini belum ada fitur tracking otomatis. Anda perlu memperbarui nilai investasi secara manual. Fitur ini mungkin akan ditambahkan di versi mendatang.'
            ]
        ]
    ],
    [
        'category' => 'Laporan',
        'questions' => [
            [
                'question' => 'Laporan apa saja yang tersedia?',
                'answer' => 'KeuanganKu menyediakan berbagai laporan: Laporan Keuangan (ringkasan pemasukan-pengeluaran), Laporan Anggaran (realisasi vs target), Laporan Investasi (performa portofolio), dan Laporan Tren (analisis perkembangan).'
            ],
            [
                'question' => 'Bisakah laporan diekspor?',
                'answer' => 'Ya, sebagian besar laporan dapat diekspor dalam format PDF atau Excel. Fitur ini berguna untuk dokumentasi, presentasi, atau analisis lebih lanjut menggunakan tools eksternal.'
            ],
            [
                'question' => 'Bagaimana cara melihat laporan periode tertentu?',
                'answer' => 'Setiap halaman laporan memiliki filter tanggal. Pilih tanggal mulai dan tanggal akhir sesuai periode yang ingin Anda analisis, lalu klik "Filter" untuk menampilkan data periode tersebut.'
            ]
        ]
    ]
];

// Include header
include 'includes/views/layouts/header.php';
?>

<div class="container-fluid py-4">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-1">Frequently Asked Questions (FAQ)</h1>
            <p class="text-muted mb-0">Temukan jawaban atas pertanyaan yang sering diajukan</p>
        </div>
    </div>

    <!-- Search Box -->
    <div class="row mb-4">
        <div class="col-lg-8 mx-auto">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="input-group">
                        <span class="input-group-text bg-light">
                            <i class="fas fa-search text-muted"></i>
                        </span>
                        <input type="text" id="searchFAQ" class="form-control" placeholder="Cari pertanyaan atau kata kunci...">
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- FAQ Categories -->
    <div class="row">
        <div class="col-lg-3">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h6 class="mb-0"><i class="fas fa-list me-2"></i>Kategori</h6>
                </div>
                <div class="list-group list-group-flush">
                    <a href="#" class="list-group-item list-group-item-action active" data-category="all">
                        <i class="fas fa-globe me-2"></i>Semua Kategori
                    </a>
                    <?php foreach ($faqs as $index => $category): ?>
                    <a href="#" class="list-group-item list-group-item-action" data-category="<?= $index ?>">
                        <i class="fas fa-<?= $index == 0 ? 'info-circle' : ($index == 1 ? 'exchange-alt' : ($index == 2 ? 'tags' : ($index == 3 ? 'chart-pie' : ($index == 4 ? 'chart-line' : 'chart-bar')))) ?> me-2"></i>
                        <?= $category['category'] ?>
                    </a>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>

        <div class="col-lg-9">
            <?php foreach ($faqs as $categoryIndex => $category): ?>
            <div class="faq-category" data-category="<?= $categoryIndex ?>">
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0 text-primary">
                            <i class="fas fa-<?= $categoryIndex == 0 ? 'info-circle' : ($categoryIndex == 1 ? 'exchange-alt' : ($categoryIndex == 2 ? 'tags' : ($categoryIndex == 3 ? 'chart-pie' : ($categoryIndex == 4 ? 'chart-line' : 'chart-bar')))) ?> me-2"></i>
                            <?= $category['category'] ?>
                        </h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="accordion" id="accordion<?= $categoryIndex ?>">
                            <?php foreach ($category['questions'] as $qIndex => $qa): ?>
                            <div class="accordion-item faq-item">
                                <h2 class="accordion-header" id="heading<?= $categoryIndex ?>_<?= $qIndex ?>">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse<?= $categoryIndex ?>_<?= $qIndex ?>" aria-expanded="false">
                                        <i class="fas fa-question-circle text-primary me-2"></i>
                                        <span class="faq-question"><?= $qa['question'] ?></span>
                                    </button>
                                </h2>
                                <div id="collapse<?= $categoryIndex ?>_<?= $qIndex ?>" class="accordion-collapse collapse" data-bs-parent="#accordion<?= $categoryIndex ?>">
                                    <div class="accordion-body">
                                        <div class="faq-answer">
                                            <i class="fas fa-lightbulb text-warning me-2"></i>
                                            <?= $qa['answer'] ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>

            <!-- No Results Message -->
            <div id="noResults" class="card border-0 shadow-sm" style="display: none;">
                <div class="card-body text-center py-5">
                    <i class="fas fa-search fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">Tidak ada hasil yang ditemukan</h5>
                    <p class="text-muted">Coba gunakan kata kunci yang berbeda atau lihat semua kategori</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Contact Support -->
    <div class="row mt-5">
        <div class="col-12">
            <div class="card border-0 shadow-sm bg-light">
                <div class="card-body text-center py-4">
                    <h5 class="mb-3">Tidak menemukan jawaban yang Anda cari?</h5>
                    <p class="text-muted mb-3">Tim support kami siap membantu Anda</p>
                    <a href="/keuangan/support.php" class="btn btn-primary">
                        <i class="fas fa-headset me-2"></i>Hubungi Support
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('searchFAQ');
    const categoryLinks = document.querySelectorAll('[data-category]');
    const faqCategories = document.querySelectorAll('.faq-category');
    const faqItems = document.querySelectorAll('.faq-item');
    const noResults = document.getElementById('noResults');

    // Search functionality
    searchInput.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        let hasResults = false;

        if (searchTerm === '') {
            // Show all items
            faqItems.forEach(item => {
                item.style.display = 'block';
            });
            faqCategories.forEach(category => {
                category.style.display = 'block';
            });
            noResults.style.display = 'none';
            return;
        }

        faqItems.forEach(item => {
            const question = item.querySelector('.faq-question').textContent.toLowerCase();
            const answer = item.querySelector('.faq-answer').textContent.toLowerCase();
            
            if (question.includes(searchTerm) || answer.includes(searchTerm)) {
                item.style.display = 'block';
                hasResults = true;
            } else {
                item.style.display = 'none';
            }
        });

        // Hide empty categories
        faqCategories.forEach(category => {
            const visibleItems = category.querySelectorAll('.faq-item[style="display: block;"], .faq-item:not([style])');
            if (visibleItems.length === 0) {
                category.style.display = 'none';
            } else {
                category.style.display = 'block';
            }
        });

        noResults.style.display = hasResults ? 'none' : 'block';
    });

    // Category filter
    categoryLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Update active state
            categoryLinks.forEach(l => l.classList.remove('active'));
            this.classList.add('active');
            
            const category = this.dataset.category;
            
            if (category === 'all') {
                faqCategories.forEach(cat => cat.style.display = 'block');
            } else {
                faqCategories.forEach((cat, index) => {
                    cat.style.display = index == category ? 'block' : 'none';
                });
            }
            
            // Clear search
            searchInput.value = '';
            faqItems.forEach(item => item.style.display = 'block');
            noResults.style.display = 'none';
        });
    });
});
</script>

<?php include 'includes/views/layouts/footer.php'; ?>
