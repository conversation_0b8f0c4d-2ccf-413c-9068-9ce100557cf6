<?php
// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

// Define logs directory path
define('LOGS_DIR', dirname(dirname(dirname(__FILE__))) . '/logs');

// Create logs directory if it doesn't exist
if (!is_dir(LOGS_DIR)) {
    mkdir(LOGS_DIR, 0755, true);
}

// Set error log path
ini_set('error_log', LOGS_DIR . '/error.log');

/**
 * Custom error handler
 */
function handleError($errno, $errstr, $errfile, $errline) {
    if (!(error_reporting() & $errno)) {
        return false;
    }

    $error = [
        'type' => $errno,
        'message' => $errstr,
        'file' => $errfile,
        'line' => $errline,
        'time' => date('Y-m-d H:i:s')
    ];

    // Log error
    error_log(json_encode($error) . "\n", 3, LOGS_DIR . '/error.log');

    // Show error in development
    if (ENVIRONMENT === 'development') {
        echo "<div style='background: #f8d7da; color: #721c24; padding: 10px; margin: 10px; border: 1px solid #f5c6cb; border-radius: 4px;'>";
        echo "<strong>Error:</strong> " . htmlspecialchars($errstr) . "<br>";
        echo "<strong>File:</strong> " . htmlspecialchars($errfile) . "<br>";
        echo "<strong>Line:</strong> " . $errline;
        echo "</div>";
    }

    return true;
}

/**
 * Custom exception handler
 */
function handleException($exception) {
    $error = [
        'type' => get_class($exception),
        'message' => $exception->getMessage(),
        'file' => $exception->getFile(),
        'line' => $exception->getLine(),
        'trace' => $exception->getTraceAsString(),
        'time' => date('Y-m-d H:i:s')
    ];

    // Log error
    error_log(json_encode($error) . "\n", 3, LOGS_DIR . '/error.log');

    // Show error in development
    if (ENVIRONMENT === 'development') {
        echo "<div style='background: #f8d7da; color: #721c24; padding: 10px; margin: 10px; border: 1px solid #f5c6cb; border-radius: 4px;'>";
        echo "<strong>Exception:</strong> " . htmlspecialchars($exception->getMessage()) . "<br>";
        echo "<strong>File:</strong> " . htmlspecialchars($exception->getFile()) . "<br>";
        echo "<strong>Line:</strong> " . $exception->getLine();
        echo "</div>";
    }
}

// Set error handlers
set_error_handler('handleError');
set_exception_handler('handleException');

// Define environment
define('ENVIRONMENT', 'development'); // Change to 'production' in production environment

/**
 * Log error to database
 * @param int $errno Error number
 * @param string $errstr Error message
 * @param string $errfile Error file
 * @param int $errline Error line
 * @return bool
 */
function logErrorToDatabase($errno, $errstr, $errfile, $errline) {
    global $pdo;

    if (!$pdo) return false;

    try {
        // Create error_logs table if not exists
        $pdo->exec("CREATE TABLE IF NOT EXISTS error_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            error_number INT NOT NULL,
            error_message TEXT NOT NULL,
            error_file VARCHAR(500) NOT NULL,
            error_line INT NOT NULL,
            user_id INT NULL,
            ip_address VARCHAR(45),
            user_agent TEXT,
            request_uri TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
        )");

        $currentUser = getCurrentUser();
        $stmt = $pdo->prepare("
            INSERT INTO error_logs
            (error_number, error_message, error_file, error_line, user_id, ip_address, user_agent, request_uri)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ");

        return $stmt->execute([
            $errno,
            $errstr,
            $errfile,
            $errline,
            $currentUser['id'] ?? null,
            $_SERVER['REMOTE_ADDR'] ?? null,
            $_SERVER['HTTP_USER_AGENT'] ?? null,
            $_SERVER['REQUEST_URI'] ?? null
        ]);

    } catch (PDOException $e) {
        error_log("Error logging to database: " . $e->getMessage());
        return false;
    }
}

/**
 * Get recent errors from database
 * @param int $limit Number of errors to retrieve
 * @return array
 */
function getRecentErrors($limit = 50) {
    global $pdo;

    try {
        $stmt = $pdo->prepare("
            SELECT el.*, u.nama as user_name
            FROM error_logs el
            LEFT JOIN users u ON el.user_id = u.id
            ORDER BY el.created_at DESC
            LIMIT ?
        ");
        $stmt->execute([$limit]);
        return $stmt->fetchAll();

    } catch (PDOException $e) {
        error_log("Get recent errors: " . $e->getMessage());
        return [];
    }
}

/**
 * Clean old error logs
 * @param int $days Keep errors newer than X days
 * @return bool
 */
function cleanOldErrorLogs($days = 30) {
    global $pdo;

    try {
        $stmt = $pdo->prepare("
            DELETE FROM error_logs
            WHERE created_at < DATE_SUB(NOW(), INTERVAL ? DAY)
        ");

        return $stmt->execute([$days]);

    } catch (PDOException $e) {
        error_log("Clean old error logs: " . $e->getMessage());
        return false;
    }
}