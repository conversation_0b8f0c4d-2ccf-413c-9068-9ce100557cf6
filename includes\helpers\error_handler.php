<?php
// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

// Define logs directory path
define('LOGS_DIR', dirname(dirname(dirname(__FILE__))) . '/logs');

// Create logs directory if it doesn't exist
if (!is_dir(LOGS_DIR)) {
    mkdir(LOGS_DIR, 0755, true);
}

// Set error log path
ini_set('error_log', LOGS_DIR . '/error.log');

/**
 * Custom error handler
 */
function handleError($errno, $errstr, $errfile, $errline) {
    if (!(error_reporting() & $errno)) {
        return false;
    }

    $error = [
        'type' => $errno,
        'message' => $errstr,
        'file' => $errfile,
        'line' => $errline,
        'time' => date('Y-m-d H:i:s')
    ];

    // Log error
    error_log(json_encode($error) . "\n", 3, LOGS_DIR . '/error.log');

    // Show error in development
    if (ENVIRONMENT === 'development') {
        echo "<div style='background: #f8d7da; color: #721c24; padding: 10px; margin: 10px; border: 1px solid #f5c6cb; border-radius: 4px;'>";
        echo "<strong>Error:</strong> " . htmlspecialchars($errstr) . "<br>";
        echo "<strong>File:</strong> " . htmlspecialchars($errfile) . "<br>";
        echo "<strong>Line:</strong> " . $errline;
        echo "</div>";
    }

    return true;
}

/**
 * Custom exception handler
 */
function handleException($exception) {
    $error = [
        'type' => get_class($exception),
        'message' => $exception->getMessage(),
        'file' => $exception->getFile(),
        'line' => $exception->getLine(),
        'trace' => $exception->getTraceAsString(),
        'time' => date('Y-m-d H:i:s')
    ];

    // Log error
    error_log(json_encode($error) . "\n", 3, LOGS_DIR . '/error.log');

    // Show error in development
    if (ENVIRONMENT === 'development') {
        echo "<div style='background: #f8d7da; color: #721c24; padding: 10px; margin: 10px; border: 1px solid #f5c6cb; border-radius: 4px;'>";
        echo "<strong>Exception:</strong> " . htmlspecialchars($exception->getMessage()) . "<br>";
        echo "<strong>File:</strong> " . htmlspecialchars($exception->getFile()) . "<br>";
        echo "<strong>Line:</strong> " . $exception->getLine();
        echo "</div>";
    }
}

// Set error handlers
set_error_handler('handleError');
set_exception_handler('handleException');

// Define environment
define('ENVIRONMENT', 'development'); // Change to 'production' in production environment 