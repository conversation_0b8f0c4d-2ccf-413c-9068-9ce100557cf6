<?php
/**
 * Check Theme Database Status
 * Script untuk memeriksa status database theme dan mendiagnosis masalah
 */

require_once 'includes/config/database.php';
require_once 'includes/helpers/functions.php';

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

echo "<h1>🔍 Theme Database Diagnostic</h1>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; }
.section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
.success { color: green; }
.error { color: red; }
.warning { color: orange; }
.info { color: blue; }
table { border-collapse: collapse; width: 100%; margin: 10px 0; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
</style>";

// Check 1: Database Connection
echo "<div class='section'>";
echo "<h2>1. Database Connection</h2>";
try {
    if ($pdo) {
        echo "<div class='success'>✅ Database connection successful</div>";
        echo "<div class='info'>Database: " . $pdo->getAttribute(PDO::ATTR_CONNECTION_STATUS) . "</div>";
    } else {
        echo "<div class='error'>❌ Database connection failed</div>";
    }
} catch (Exception $e) {
    echo "<div class='error'>❌ Database connection error: " . $e->getMessage() . "</div>";
}
echo "</div>";

// Check 2: Required Tables
echo "<div class='section'>";
echo "<h2>2. Required Tables Check</h2>";

$requiredTables = [
    'users' => 'User management table',
    'system_settings' => 'System settings for themes',
    'custom_themes' => 'Custom themes created by users',
    'user_theme_preferences' => 'User theme preferences'
];

foreach ($requiredTables as $table => $description) {
    try {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            echo "<div class='success'>✅ Table '$table' exists - $description</div>";
            
            // Check table structure
            $stmt = $pdo->query("DESCRIBE $table");
            $columns = $stmt->fetchAll();
            echo "<details><summary>View structure</summary>";
            echo "<table><tr><th>Column</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
            foreach ($columns as $column) {
                echo "<tr>";
                echo "<td>{$column['Field']}</td>";
                echo "<td>{$column['Type']}</td>";
                echo "<td>{$column['Null']}</td>";
                echo "<td>{$column['Key']}</td>";
                echo "<td>{$column['Default']}</td>";
                echo "</tr>";
            }
            echo "</table></details>";
            
        } else {
            echo "<div class='error'>❌ Table '$table' missing - $description</div>";
        }
    } catch (PDOException $e) {
        echo "<div class='error'>❌ Error checking table '$table': " . $e->getMessage() . "</div>";
    }
}
echo "</div>";

// Check 3: Current User
echo "<div class='section'>";
echo "<h2>3. Current User Check</h2>";
try {
    $currentUser = getCurrentUser();
    if ($currentUser) {
        echo "<div class='success'>✅ User logged in</div>";
        echo "<div class='info'>User ID: {$currentUser['id']}</div>";
        echo "<div class='info'>Username: {$currentUser['username']}</div>";
        echo "<div class='info'>Role: {$currentUser['role']}</div>";
    } else {
        echo "<div class='warning'>⚠️ No user logged in</div>";
    }
} catch (Exception $e) {
    echo "<div class='error'>❌ Error getting current user: " . $e->getMessage() . "</div>";
}
echo "</div>";

// Check 4: System Settings
echo "<div class='section'>";
echo "<h2>4. System Settings Check</h2>";
try {
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM system_settings");
    $count = $stmt->fetch()['count'];
    
    if ($count > 0) {
        echo "<div class='success'>✅ System settings table has $count records</div>";
        
        // Show theme-related settings
        $stmt = $pdo->query("SELECT * FROM system_settings WHERE setting_key LIKE '%theme%' OR setting_key LIKE '%color%'");
        $settings = $stmt->fetchAll();
        
        if ($settings) {
            echo "<table><tr><th>Setting Key</th><th>Value</th><th>Description</th></tr>";
            foreach ($settings as $setting) {
                echo "<tr>";
                echo "<td>{$setting['setting_key']}</td>";
                echo "<td>{$setting['setting_value']}</td>";
                echo "<td>{$setting['description']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    } else {
        echo "<div class='warning'>⚠️ System settings table is empty</div>";
    }
} catch (PDOException $e) {
    echo "<div class='error'>❌ Error checking system settings: " . $e->getMessage() . "</div>";
}
echo "</div>";

// Check 5: User Theme Preferences
echo "<div class='section'>";
echo "<h2>5. User Theme Preferences Check</h2>";
try {
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM user_theme_preferences");
    $count = $stmt->fetch()['count'];
    
    echo "<div class='info'>Total user theme preferences: $count</div>";
    
    if ($currentUser) {
        $stmt = $pdo->prepare("SELECT * FROM user_theme_preferences WHERE user_id = ?");
        $stmt->execute([$currentUser['id']]);
        $userTheme = $stmt->fetch();
        
        if ($userTheme) {
            echo "<div class='success'>✅ Current user has theme preferences</div>";
            echo "<table><tr><th>Property</th><th>Value</th></tr>";
            foreach ($userTheme as $key => $value) {
                if (!is_numeric($key)) {
                    echo "<tr><td>$key</td><td>$value</td></tr>";
                }
            }
            echo "</table>";
        } else {
            echo "<div class='warning'>⚠️ Current user has no theme preferences</div>";
        }
    }
} catch (PDOException $e) {
    echo "<div class='error'>❌ Error checking user theme preferences: " . $e->getMessage() . "</div>";
}
echo "</div>";

// Check 6: Custom Themes
echo "<div class='section'>";
echo "<h2>6. Custom Themes Check</h2>";
try {
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM custom_themes");
    $count = $stmt->fetch()['count'];
    
    echo "<div class='info'>Total custom themes: $count</div>";
    
    if ($count > 0) {
        $stmt = $pdo->query("SELECT * FROM custom_themes ORDER BY created_at DESC LIMIT 5");
        $themes = $stmt->fetchAll();
        
        echo "<table><tr><th>ID</th><th>Name</th><th>Created By</th><th>Active</th><th>Created At</th></tr>";
        foreach ($themes as $theme) {
            echo "<tr>";
            echo "<td>{$theme['id']}</td>";
            echo "<td>{$theme['name']}</td>";
            echo "<td>{$theme['created_by']}</td>";
            echo "<td>" . ($theme['is_active'] ? 'Yes' : 'No') . "</td>";
            echo "<td>{$theme['created_at']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
} catch (PDOException $e) {
    echo "<div class='error'>❌ Error checking custom themes: " . $e->getMessage() . "</div>";
}
echo "</div>";

// Check 7: Test Theme Functions
echo "<div class='section'>";
echo "<h2>7. Theme Functions Test</h2>";
try {
    require_once 'includes/helpers/theme_helper.php';
    
    // Test getUserThemePreferences
    if ($currentUser) {
        $userTheme = getUserThemePreferences($currentUser['id']);
        if ($userTheme) {
            echo "<div class='success'>✅ getUserThemePreferences() works</div>";
        } else {
            echo "<div class='warning'>⚠️ getUserThemePreferences() returned null</div>";
        }
    }
    
    // Test getPredefinedThemes
    $predefinedThemes = getPredefinedThemes();
    echo "<div class='success'>✅ getPredefinedThemes() works - " . count($predefinedThemes) . " themes available</div>";
    
    // Test generateThemeCSS
    $testTheme = [
        'theme_mode' => 'light',
        'primary_color' => '#2563eb',
        'secondary_color' => '#64748b',
        'sidebar_color' => 'dark',
        'navbar_color' => 'primary'
    ];
    
    $css = generateThemeCSS($testTheme);
    if ($css) {
        echo "<div class='success'>✅ generateThemeCSS() works</div>";
    } else {
        echo "<div class='error'>❌ generateThemeCSS() failed</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Theme functions error: " . $e->getMessage() . "</div>";
}
echo "</div>";

// Check 8: Recommendations
echo "<div class='section'>";
echo "<h2>8. Recommendations</h2>";

$recommendations = [];

// Check if tables exist
try {
    $stmt = $pdo->query("SHOW TABLES LIKE 'system_settings'");
    if ($stmt->rowCount() == 0) {
        $recommendations[] = "Run setup_theme_tables.php to create required tables";
    }
} catch (Exception $e) {
    $recommendations[] = "Database connection issues - check database configuration";
}

// Check if user is logged in
if (!$currentUser) {
    $recommendations[] = "Login as admin to access theme manager";
}

// Check if user has theme preferences
if ($currentUser) {
    try {
        $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM user_theme_preferences WHERE user_id = ?");
        $stmt->execute([$currentUser['id']]);
        $count = $stmt->fetch()['count'];
        
        if ($count == 0) {
            $recommendations[] = "Create default theme preferences for current user";
        }
    } catch (Exception $e) {
        $recommendations[] = "Error checking user theme preferences";
    }
}

if (empty($recommendations)) {
    echo "<div class='success'>✅ Everything looks good! No recommendations.</div>";
} else {
    echo "<div class='warning'>⚠️ Recommendations:</div>";
    foreach ($recommendations as $rec) {
        echo "<div class='info'>• $rec</div>";
    }
}
echo "</div>";

echo "<div class='section'>";
echo "<h2>🔧 Quick Actions</h2>";
echo "<a href='setup_theme_tables.php' style='margin-right: 10px; padding: 10px; background: #007bff; color: white; text-decoration: none; border-radius: 5px;'>Setup Tables</a>";
echo "<a href='theme_manager.php' style='margin-right: 10px; padding: 10px; background: #28a745; color: white; text-decoration: none; border-radius: 5px;'>Theme Manager</a>";
echo "<a href='test_theme_functions.php' style='margin-right: 10px; padding: 10px; background: #17a2b8; color: white; text-decoration: none; border-radius: 5px;'>Test Functions</a>";
echo "</div>";
?>
