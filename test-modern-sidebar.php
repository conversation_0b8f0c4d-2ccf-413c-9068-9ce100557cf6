<!DOCTYPE html>
<html lang="id" data-bs-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modern Responsive Sidebar Test - KeuanganKu</title>

    <!-- CSS Libraries -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="assets/css/layout-manager.css?v=<?= time() ?>" rel="stylesheet">
    <link href="assets/css/control-sidebar.css?v=<?= time() ?>" rel="stylesheet">
</head>
<body>
    <!-- Modern Responsive Sidebar -->
    <aside class="modern-sidebar">
        <!-- Sidebar Header -->
        <div class="sidebar-header">
            <div class="brand-container">
                <div class="brand-logo">
                    <div class="logo-icon">
                        <i class="fas fa-wallet"></i>
                    </div>
                </div>
                <div class="brand-content">
                    <h1 class="brand-title">KeuanganKu</h1>
                    <p class="brand-subtitle">Financial Manager</p>
                </div>
            </div>
        </div>

        <!-- User Profile Section -->
        <div class="sidebar-user">
            <div class="user-profile">
                <div class="user-avatar">
                    <img src="https://ui-avatars.com/api/?name=Test+User&background=2563eb&color=fff&size=40" alt="User Avatar" class="avatar-img">
                    <div class="status-indicator"></div>
                </div>
                <div class="user-details">
                    <h6 class="user-name">Test User</h6>
                    <span class="user-role">Admin</span>
                </div>
            </div>
        </div>

        <!-- Navigation Menu -->
        <div class="sidebar-navigation">
            <nav class="nav-menu">
                <!-- Main Section -->
                <div class="menu-section">
                    <div class="menu-section-title">Main</div>
                    
                    <a class="menu-item active" href="#" data-tooltip="Dashboard">
                        <div class="menu-icon">
                            <i class="fas fa-home"></i>
                        </div>
                        <span class="menu-text">Dashboard</span>
                    </a>

                    <a class="menu-item" href="#" data-tooltip="Analytics">
                        <div class="menu-icon">
                            <i class="fas fa-chart-bar"></i>
                        </div>
                        <span class="menu-text">Analytics</span>
                        <span class="menu-badge">5</span>
                    </a>

                    <button class="menu-item" data-bs-toggle="collapse" data-bs-target="#transactionSubmenu" data-tooltip="Transactions">
                        <div class="menu-icon">
                            <i class="fas fa-exchange-alt"></i>
                        </div>
                        <span class="menu-text">Transactions</span>
                        <i class="fas fa-chevron-down menu-arrow"></i>
                    </button>
                    <div class="collapse submenu" id="transactionSubmenu">
                        <a class="submenu-item" href="#">
                            <div class="submenu-icon">
                                <i class="fas fa-plus"></i>
                            </div>
                            Add Transaction
                        </a>
                        <a class="submenu-item active" href="#">
                            <div class="submenu-icon">
                                <i class="fas fa-list"></i>
                            </div>
                            View All
                        </a>
                        <a class="submenu-item" href="#">
                            <div class="submenu-icon">
                                <i class="fas fa-search"></i>
                            </div>
                            Search
                        </a>
                    </div>
                </div>

                <!-- Management Section -->
                <div class="menu-section">
                    <div class="menu-section-title">Management</div>
                    
                    <a class="menu-item" href="#" data-tooltip="Categories">
                        <div class="menu-icon">
                            <i class="fas fa-tags"></i>
                        </div>
                        <span class="menu-text">Categories</span>
                    </a>

                    <a class="menu-item" href="#" data-tooltip="Reports">
                        <div class="menu-icon">
                            <i class="fas fa-file-alt"></i>
                        </div>
                        <span class="menu-text">Reports</span>
                    </a>

                    <button class="menu-item" data-bs-toggle="collapse" data-bs-target="#settingsSubmenu" data-tooltip="Settings">
                        <div class="menu-icon">
                            <i class="fas fa-cog"></i>
                        </div>
                        <span class="menu-text">Settings</span>
                        <i class="fas fa-chevron-down menu-arrow"></i>
                    </button>
                    <div class="collapse submenu" id="settingsSubmenu">
                        <a class="submenu-item" href="#">
                            <div class="submenu-icon">
                                <i class="fas fa-user"></i>
                            </div>
                            Profile
                        </a>
                        <a class="submenu-item" href="#">
                            <div class="submenu-icon">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                            Security
                        </a>
                    </div>
                </div>
            </nav>
        </div>

        <!-- Sidebar Footer -->
        <div class="sidebar-footer">
            <div class="sidebar-footer-content">
                <button class="sidebar-toggle-btn" data-bs-toggle="tooltip" title="Toggle Sidebar">
                    <i class="fas fa-chevron-left"></i>
                </button>
                <button class="theme-toggle-btn" onclick="toggleTheme()" data-bs-toggle="tooltip" title="Toggle Dark Mode">
                    <i class="fas fa-moon"></i>
                </button>
            </div>
        </div>
    </aside>

    <!-- Sidebar Overlay for Mobile -->
    <div class="sidebar-overlay"></div>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Navbar -->
        <nav class="navbar navbar-expand-lg modern-navbar">
            <div class="container-fluid">
                <!-- Mobile Sidebar Toggle -->
                <button class="mobile-sidebar-toggle d-lg-none" type="button">
                    <i class="fas fa-bars"></i>
                </button>

                <!-- Brand -->
                <div class="navbar-brand">
                    <div class="brand-logo">
                        <i class="fas fa-sun" style="color: #ff9800;"></i>
                    </div>
                    <span id="dynamicGreeting" style="color: #ff9800;">Selamat Pagi</span>
                </div>

                <!-- Desktop Controls -->
                <div class="navbar-nav ms-auto d-none d-lg-flex">
                    <button class="nav-link btn btn-link me-2" onclick="toggleTheme()">
                        <i class="fas fa-moon"></i>
                    </button>
                    <button class="nav-link btn btn-link" onclick="window.modernSidebar?.toggle()">
                        <i class="fas fa-cogs"></i>
                    </button>
                </div>

                <!-- Mobile Menu Toggle -->
                <button class="navbar-toggler border-0 d-lg-none" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <i class="fas fa-ellipsis-v"></i>
                </button>

                <!-- Mobile Menu -->
                <div class="collapse navbar-collapse" id="navbarNav">
                    <div class="navbar-nav ms-auto">
                        <button class="nav-link btn btn-link" onclick="toggleTheme()">
                            <i class="fas fa-moon me-2"></i>Dark Mode
                        </button>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Content -->
        <div class="content-container">
            <div class="container-fluid">
                <!-- Header -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h4 class="card-title mb-0">
                                    <i class="fas fa-mobile-alt me-2"></i>Modern Responsive Sidebar Test
                                </h4>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-info">
                                    <h6><i class="fas fa-info-circle me-2"></i>Test Responsive Features:</h6>
                                    <ul class="mb-0">
                                        <li><strong>Desktop:</strong> Click the toggle button in sidebar footer to collapse/expand</li>
                                        <li><strong>Mobile:</strong> Use the hamburger menu in navbar to open sidebar</li>
                                        <li><strong>Tablet:</strong> Sidebar automatically hides and shows mobile behavior</li>
                                        <li><strong>Dark Mode:</strong> Toggle theme to see dark mode styling</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Feature Cards -->
                <div class="row">
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="card-title mb-0">
                                    <i class="fas fa-desktop me-2"></i>Desktop Features
                                </h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled mb-0">
                                    <li><i class="fas fa-check text-success me-2"></i>Collapsible sidebar</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Smooth animations</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Tooltip on hover (collapsed)</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Auto-adjusting content</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="card-title mb-0">
                                    <i class="fas fa-mobile-alt me-2"></i>Mobile Features
                                </h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled mb-0">
                                    <li><i class="fas fa-check text-success me-2"></i>Slide-in sidebar</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Overlay background</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Touch-friendly controls</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Auto-close on navigation</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Control Panel -->
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="card-title mb-0">
                                    <i class="fas fa-gamepad me-2"></i>Manual Controls
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row g-2">
                                    <div class="col-md-3">
                                        <button class="btn btn-primary w-100" onclick="window.modernSidebar?.toggle()">
                                            <i class="fas fa-arrows-alt-h me-1"></i>Toggle Sidebar
                                        </button>
                                    </div>
                                    <div class="col-md-3">
                                        <button class="btn btn-success w-100" onclick="window.modernSidebar?.expand()">
                                            <i class="fas fa-expand me-1"></i>Expand Sidebar
                                        </button>
                                    </div>
                                    <div class="col-md-3">
                                        <button class="btn btn-warning w-100" onclick="window.modernSidebar?.collapse()">
                                            <i class="fas fa-compress me-1"></i>Collapse Sidebar
                                        </button>
                                    </div>
                                    <div class="col-md-3">
                                        <button class="btn btn-info w-100" onclick="toggleTheme()">
                                            <i class="fas fa-palette me-1"></i>Toggle Theme
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/modern-sidebar.js?v=<?= time() ?>"></script>
    <script src="assets/js/theme-manager.js?v=<?= time() ?>"></script>

    <script>
        // Initialize tooltips
        document.addEventListener('DOMContentLoaded', function() {
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });

            // Update greeting
            updateGreeting();
        });

        function updateGreeting() {
            const now = new Date();
            const hour = now.getHours();
            const greetingElement = document.getElementById('dynamicGreeting');
            const greetingIcon = document.querySelector('.brand-logo i');

            let greeting = '';
            let icon = '';
            let color = '';

            if (hour >= 5 && hour < 12) {
                greeting = 'Selamat Pagi';
                icon = 'fas fa-sun';
                color = '#ff9800';
            } else if (hour >= 12 && hour < 15) {
                greeting = 'Selamat Siang';
                icon = 'fas fa-sun';
                color = '#ffc107';
            } else if (hour >= 15 && hour < 18) {
                greeting = 'Selamat Sore';
                icon = 'fas fa-cloud-sun';
                color = '#ff5722';
            } else {
                greeting = 'Selamat Malam';
                icon = 'fas fa-moon';
                color = '#3f51b5';
            }

            if (greetingElement) {
                greetingElement.textContent = greeting;
                greetingElement.style.color = color;
            }

            if (greetingIcon) {
                greetingIcon.className = icon;
                greetingIcon.style.color = color;
            }
        }
    </script>
</body>
</html>
