<?php
/**
 * Update Theme Database Schema
 * Script untuk menambahkan kolom layout_style ke tabel yang sudah ada
 */

require_once 'includes/config/database.php';
require_once 'includes/helpers/functions.php';

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

echo "<h1>🔄 Update Theme Database Schema</h1>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; }
.success { color: green; }
.error { color: red; }
.warning { color: orange; }
.info { color: blue; }
</style>";

// Check if user is logged in and is admin
$currentUser = getCurrentUser();
if (!$currentUser) {
    echo "<div class='error'>❌ Please login first</div>";
    echo "<a href='login.php'>Login</a>";
    exit;
}

if ($currentUser['role'] !== 'admin') {
    echo "<div class='error'>❌ Admin access required</div>";
    echo "<a href='dashboard.php'>Back to Dashboard</a>";
    exit;
}

echo "<div class='info'>Updating theme database schema...</div><br>";

try {
    // Check database connection
    if (!$pdo) {
        throw new Exception("Database connection failed");
    }
    
    echo "<div class='info'>Database connection successful</div>";
    
    // Check if layout_style column exists in user_theme_preferences
    echo "<div class='info'>Checking user_theme_preferences table structure...</div>";
    
    $stmt = $pdo->query("SHOW COLUMNS FROM user_theme_preferences LIKE 'layout_style'");
    if ($stmt->rowCount() == 0) {
        echo "<div class='warning'>⚠️ Column 'layout_style' not found - adding...</div>";
        
        $sql = "ALTER TABLE user_theme_preferences ADD COLUMN layout_style VARCHAR(20) DEFAULT 'standard' AFTER navbar_color";
        $pdo->exec($sql);
        
        echo "<div class='success'>✅ Column 'layout_style' added successfully</div>";
    } else {
        echo "<div class='success'>✅ Column 'layout_style' already exists</div>";
    }
    
    // Update existing records to have default layout_style
    echo "<div class='info'>Updating existing records...</div>";
    
    $stmt = $pdo->query("UPDATE user_theme_preferences SET layout_style = 'standard' WHERE layout_style IS NULL OR layout_style = ''");
    $updatedRows = $stmt->rowCount();
    
    if ($updatedRows > 0) {
        echo "<div class='success'>✅ Updated $updatedRows records with default layout_style</div>";
    } else {
        echo "<div class='success'>✅ All records already have layout_style values</div>";
    }
    
    // Check if custom_themes table needs layout_style column
    echo "<div class='info'>Checking custom_themes table structure...</div>";
    
    $stmt = $pdo->query("SHOW TABLES LIKE 'custom_themes'");
    if ($stmt->rowCount() > 0) {
        $stmt = $pdo->query("SHOW COLUMNS FROM custom_themes LIKE 'layout_style'");
        if ($stmt->rowCount() == 0) {
            echo "<div class='warning'>⚠️ Column 'layout_style' not found in custom_themes - adding...</div>";
            
            $sql = "ALTER TABLE custom_themes ADD COLUMN layout_style VARCHAR(20) DEFAULT 'standard' AFTER is_active";
            $pdo->exec($sql);
            
            echo "<div class='success'>✅ Column 'layout_style' added to custom_themes successfully</div>";
        } else {
            echo "<div class='success'>✅ Column 'layout_style' already exists in custom_themes</div>";
        }
    } else {
        echo "<div class='warning'>⚠️ Table 'custom_themes' not found</div>";
    }
    
    // Add new system settings for layout management
    echo "<div class='info'>Adding layout management system settings...</div>";
    
    $layoutSettings = [
        ['default_layout_style', 'standard', 'Default layout style for new users'],
        ['enable_layout_manager', '1', 'Enable layout manager functionality'],
        ['available_layouts', 'standard,modern,minimal,compact,corporate,futuristic', 'Available layout styles'],
        ['layout_customization', '1', 'Allow users to customize layouts']
    ];
    
    $stmt = $pdo->prepare("INSERT IGNORE INTO system_settings (setting_key, setting_value, description) VALUES (?, ?, ?)");
    $addedSettings = 0;
    
    foreach ($layoutSettings as $setting) {
        $result = $stmt->execute($setting);
        if ($stmt->rowCount() > 0) {
            $addedSettings++;
            echo "<div class='info'>+ Added setting: {$setting[0]}</div>";
        }
    }
    
    if ($addedSettings > 0) {
        echo "<div class='success'>✅ Added $addedSettings layout management settings</div>";
    } else {
        echo "<div class='success'>✅ All layout management settings already exist</div>";
    }
    
    // Verify schema updates
    echo "<div class='info'>Verifying schema updates...</div>";
    
    // Check user_theme_preferences structure
    $stmt = $pdo->query("DESCRIBE user_theme_preferences");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $requiredColumns = ['user_id', 'theme_mode', 'primary_color', 'secondary_color', 'sidebar_color', 'navbar_color', 'layout_style', 'custom_css'];
    $missingColumns = array_diff($requiredColumns, $columns);
    
    if (empty($missingColumns)) {
        echo "<div class='success'>✅ All required columns present in user_theme_preferences</div>";
    } else {
        echo "<div class='error'>❌ Missing columns in user_theme_preferences: " . implode(', ', $missingColumns) . "</div>";
    }
    
    // Test layout functionality
    echo "<div class='info'>Testing layout functionality...</div>";
    
    try {
        require_once 'includes/helpers/theme_helper.php';
        
        $layouts = getLayoutStyles();
        echo "<div class='success'>✅ Layout styles loaded successfully - " . count($layouts) . " layouts available</div>";
        
        $testLayout = generateLayoutCSS('modern');
        if (!empty($testLayout)) {
            echo "<div class='success'>✅ Layout CSS generation working</div>";
        } else {
            echo "<div class='error'>❌ Layout CSS generation failed</div>";
        }
        
    } catch (Exception $e) {
        echo "<div class='error'>❌ Layout functionality test failed: " . $e->getMessage() . "</div>";
    }
    
    echo "<br><div class='success'><strong>🎉 Schema update completed successfully!</strong></div>";
    echo "<div class='info'>New features available:</div>";
    echo "<ul>";
    echo "<li>✅ Layout style management</li>";
    echo "<li>✅ Enhanced theme system</li>";
    echo "<li>✅ Multiple layout options</li>";
    echo "<li>✅ Layout customization</li>";
    echo "</ul>";
    
    echo "<div style='margin-top: 20px;'>";
    echo "<a href='enhanced_theme_manager.php' style='margin-right: 10px; padding: 10px; background: #28a745; color: white; text-decoration: none; border-radius: 5px;'>Enhanced Theme Manager</a>";
    echo "<a href='layout_manager.php' style='margin-right: 10px; padding: 10px; background: #17a2b8; color: white; text-decoration: none; border-radius: 5px;'>Layout Manager</a>";
    echo "<a href='theme_manager.php' style='margin-right: 10px; padding: 10px; background: #6f42c1; color: white; text-decoration: none; border-radius: 5px;'>Classic Theme Manager</a>";
    echo "<a href='dashboard.php' style='padding: 10px; background: #6c757d; color: white; text-decoration: none; border-radius: 5px;'>Back to Dashboard</a>";
    echo "</div>";

} catch (PDOException $e) {
    echo "<div class='error'>❌ Database Error: " . $e->getMessage() . "</div>";
    echo "<div class='info'>Error Code: " . $e->getCode() . "</div>";
    error_log("Schema update error: " . $e->getMessage());
    
    echo "<div style='margin-top: 20px;'>";
    echo "<a href='check_theme_database.php' style='margin-right: 10px; padding: 10px; background: #dc3545; color: white; text-decoration: none; border-radius: 5px;'>Diagnose Problem</a>";
    echo "<a href='dashboard.php' style='padding: 10px; background: #6c757d; color: white; text-decoration: none; border-radius: 5px;'>Back to Dashboard</a>";
    echo "</div>";
} catch (Exception $e) {
    echo "<div class='error'>❌ General Error: " . $e->getMessage() . "</div>";
    error_log("Schema update general error: " . $e->getMessage());
}
?>

<!-- Schema Update Summary -->
<div style="margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 10px; border-left: 4px solid #007bff;">
    <h3>📋 Schema Update Summary</h3>
    <p><strong>What was updated:</strong></p>
    <ul>
        <li>Added <code>layout_style</code> column to <code>user_theme_preferences</code> table</li>
        <li>Added <code>layout_style</code> column to <code>custom_themes</code> table</li>
        <li>Added layout management system settings</li>
        <li>Updated existing records with default values</li>
        <li>Verified schema integrity</li>
    </ul>
    
    <p><strong>New Features Available:</strong></p>
    <ul>
        <li>🎨 Enhanced Theme Manager with 12 beautiful themes</li>
        <li>📐 Layout Manager with 6 different layout styles</li>
        <li>🔧 Advanced customization options</li>
        <li>📱 Responsive design support</li>
        <li>⚡ Real-time theme preview</li>
    </ul>
    
    <p><strong>Available Themes:</strong></p>
    <div style="display: flex; flex-wrap: wrap; gap: 10px; margin-top: 10px;">
        <span style="padding: 4px 8px; background: #2563eb; color: white; border-radius: 4px; font-size: 12px;">Default</span>
        <span style="padding: 4px 8px; background: #1f2937; color: white; border-radius: 4px; font-size: 12px;">Dark</span>
        <span style="padding: 4px 8px; background: #8b5cf6; color: white; border-radius: 4px; font-size: 12px;">Modern</span>
        <span style="padding: 4px 8px; background: #10b981; color: white; border-radius: 4px; font-size: 12px;">Minimal</span>
        <span style="padding: 4px 8px; background: #0ea5e9; color: white; border-radius: 4px; font-size: 12px;">Ocean</span>
        <span style="padding: 4px 8px; background: #059669; color: white; border-radius: 4px; font-size: 12px;">Forest</span>
        <span style="padding: 4px 8px; background: #f97316; color: white; border-radius: 4px; font-size: 12px;">Sunset</span>
        <span style="padding: 4px 8px; background: #1e40af; color: white; border-radius: 4px; font-size: 12px;">Midnight</span>
        <span style="padding: 4px 8px; background: #ec4899; color: white; border-radius: 4px; font-size: 12px;">Cherry</span>
        <span style="padding: 4px 8px; background: #374151; color: white; border-radius: 4px; font-size: 12px;">Corporate</span>
        <span style="padding: 4px 8px; background: #00ff88; color: black; border-radius: 4px; font-size: 12px;">Neon</span>
        <span style="padding: 4px 8px; background: #dc2626; color: white; border-radius: 4px; font-size: 12px;">Autumn</span>
    </div>
    
    <p><strong>Available Layouts:</strong></p>
    <div style="display: flex; flex-wrap: wrap; gap: 10px; margin-top: 10px;">
        <span style="padding: 4px 8px; background: #6c757d; color: white; border-radius: 4px; font-size: 12px;">Standard</span>
        <span style="padding: 4px 8px; background: #6c757d; color: white; border-radius: 4px; font-size: 12px;">Modern</span>
        <span style="padding: 4px 8px; background: #6c757d; color: white; border-radius: 4px; font-size: 12px;">Minimal</span>
        <span style="padding: 4px 8px; background: #6c757d; color: white; border-radius: 4px; font-size: 12px;">Compact</span>
        <span style="padding: 4px 8px; background: #6c757d; color: white; border-radius: 4px; font-size: 12px;">Corporate</span>
        <span style="padding: 4px 8px; background: #6c757d; color: white; border-radius: 4px; font-size: 12px;">Futuristic</span>
    </div>
</div>
