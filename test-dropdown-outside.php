<!DOCTYPE html>
<html lang="id" data-bs-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Dropdown Outside Sidebar - KeuanganKu</title>

    <!-- CSS Libraries -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="assets/css/layout-manager.css?v=<?= time() ?>" rel="stylesheet">
    <link href="assets/css/control-sidebar.css?v=<?= time() ?>" rel="stylesheet">
</head>
<body>
    <?php 
    // Simulate current page for testing
    $currentPage = 'transaksi';
    $currentUser = [
        'id' => 1,
        'nama' => 'Test User',
        'role' => 'admin'
    ];
    ?>

    <!-- Sidebar with Dropdown Outside -->
    <aside class="modern-sidebar">
        <!-- Sidebar Header -->
        <div class="sidebar-header">
            <div class="brand-container">
                <div class="brand-logo">
                    <div class="logo-icon">
                        <i class="fas fa-wallet"></i>
                    </div>
                </div>
                <div class="brand-content">
                    <h1 class="brand-title">KeuanganKu</h1>
                    <p class="brand-subtitle">Financial Manager</p>
                </div>
            </div>
        </div>

        <!-- User Profile Section -->
        <div class="sidebar-user">
            <div class="user-profile">
                <div class="user-avatar">
                    <img src="https://ui-avatars.com/api/?name=Test+User&background=3b82f6&color=fff&size=40" alt="User Avatar" class="avatar-img">
                    <div class="status-indicator"></div>
                </div>
                <div class="user-details">
                    <h6 class="user-name">Test User</h6>
                    <span class="user-role">Admin</span>
                </div>
            </div>
        </div>

        <!-- Navigation Menu -->
        <div class="sidebar-navigation">
            <nav class="nav-menu">
                <!-- Main Section -->
                <div class="menu-section">
                    <div class="menu-section-title">Main</div>
                    
                    <a class="menu-item" href="/keuangan/dashboard.php" data-tooltip="Dashboard">
                        <div class="menu-icon">
                            <i class="fas fa-home"></i>
                        </div>
                        <span class="menu-text">Dashboard</span>
                    </a>
                </div>

                <!-- Keuangan Section with Dropdown -->
                <div class="menu-section">
                    <div class="menu-section-title">Keuangan</div>
                    
                    <button class="menu-item menu-toggle has-submenu active" 
                            data-bs-toggle="collapse" 
                            data-bs-target="#keuanganSubmenu" 
                            aria-expanded="true" 
                            data-tooltip="Keuangan"
                            data-submenu="keuangan">
                        <div class="menu-icon">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                        <span class="menu-text">Keuangan</span>
                        <i class="fas fa-chevron-down menu-arrow"></i>
                    </button>
                    <div class="collapse submenu show" id="keuanganSubmenu">
                        <div class="submenu-container">
                            <a class="submenu-item active" href="transaksi.php">
                                <div class="submenu-icon">
                                    <i class="fas fa-exchange-alt"></i>
                                </div>
                                <span class="submenu-text">Transaksi</span>
                            </a>
                            <a class="submenu-item" href="kategori.php">
                                <div class="submenu-icon">
                                    <i class="fas fa-tags"></i>
                                </div>
                                <span class="submenu-text">Kategori</span>
                            </a>
                            <a class="submenu-item" href="target.php">
                                <div class="submenu-icon">
                                    <i class="fas fa-bullseye"></i>
                                </div>
                                <span class="submenu-text">Target</span>
                            </a>
                            <a class="submenu-item" href="anggaran.php">
                                <div class="submenu-icon">
                                    <i class="fas fa-chart-pie"></i>
                                </div>
                                <span class="submenu-text">Anggaran</span>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Laporan Section with Dropdown -->
                <div class="menu-section">
                    <div class="menu-section-title">Laporan</div>
                    
                    <button class="menu-item menu-toggle has-submenu" 
                            data-bs-toggle="collapse" 
                            data-bs-target="#laporanSubmenu" 
                            aria-expanded="false" 
                            data-tooltip="Laporan"
                            data-submenu="laporan">
                        <div class="menu-icon">
                            <i class="fas fa-chart-bar"></i>
                        </div>
                        <span class="menu-text">Laporan</span>
                        <i class="fas fa-chevron-down menu-arrow"></i>
                    </button>
                    <div class="collapse submenu" id="laporanSubmenu">
                        <div class="submenu-container">
                            <a class="submenu-item" href="laporan.php">
                                <div class="submenu-icon">
                                    <i class="fas fa-file-alt"></i>
                                </div>
                                <span class="submenu-text">Laporan Keuangan</span>
                            </a>
                            <a class="submenu-item" href="analisis.php">
                                <div class="submenu-icon">
                                    <i class="fas fa-analytics"></i>
                                </div>
                                <span class="submenu-text">Analisis</span>
                            </a>
                            <a class="submenu-item" href="grafik.php">
                                <div class="submenu-icon">
                                    <i class="fas fa-chart-area"></i>
                                </div>
                                <span class="submenu-text">Grafik</span>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Admin Section with Dropdown -->
                <div class="menu-section">
                    <div class="menu-section-title">Admin</div>
                    
                    <button class="menu-item menu-toggle has-submenu" 
                            data-bs-toggle="collapse" 
                            data-bs-target="#adminSubmenu" 
                            aria-expanded="false" 
                            data-tooltip="Admin Panel"
                            data-submenu="admin">
                        <div class="menu-icon">
                            <i class="fas fa-cogs"></i>
                        </div>
                        <span class="menu-text">Admin Panel</span>
                        <i class="fas fa-chevron-down menu-arrow"></i>
                    </button>
                    <div class="collapse submenu" id="adminSubmenu">
                        <div class="submenu-container">
                            <a class="submenu-item" href="users.php">
                                <div class="submenu-icon">
                                    <i class="fas fa-users"></i>
                                </div>
                                <span class="submenu-text">Kelola User</span>
                            </a>
                            <a class="submenu-item" href="permissions.php">
                                <div class="submenu-icon">
                                    <i class="fas fa-key"></i>
                                </div>
                                <span class="submenu-text">Permissions</span>
                            </a>
                        </div>
                    </div>
                </div>
            </nav>
        </div>

        <!-- Enhanced Sidebar Footer -->
        <div class="sidebar-footer">
            <div class="sidebar-footer-content">
                <!-- Control Buttons -->
                <div style="display: flex; gap: 0.5rem;">
                    <button class="theme-toggle-btn" onclick="toggleTheme()" data-theme-toggle
                            data-bs-toggle="tooltip" title="Toggle Dark Mode" style="flex: 1;">
                        <i class="fas fa-moon"></i>
                        <span class="theme-toggle-text">Dark Mode</span>
                    </button>
                    <button class="sidebar-toggle-btn" onclick="window.modernSidebar?.toggle()" 
                            data-bs-toggle="tooltip" title="Toggle Sidebar">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                </div>
            </div>
        </div>
    </aside>

    <!-- Sidebar Overlay for Mobile -->
    <div class="sidebar-overlay"></div>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Navbar -->
        <nav class="navbar navbar-expand-lg modern-navbar">
            <div class="container-fluid">
                <!-- Mobile Sidebar Toggle -->
                <button class="mobile-sidebar-toggle d-lg-none" type="button">
                    <i class="fas fa-bars"></i>
                </button>

                <!-- Brand -->
                <div class="navbar-brand">
                    <div class="brand-logo">
                        <i class="fas fa-external-link-alt" style="color: #10b981;"></i>
                    </div>
                    <span style="color: #10b981; font-weight: 600;">Dropdown Outside Test</span>
                </div>

                <!-- Controls -->
                <div class="navbar-nav ms-auto">
                    <button class="nav-link btn btn-link me-2" onclick="toggleTheme()">
                        <i class="fas fa-moon"></i>
                    </button>
                    <button class="nav-link btn btn-link" onclick="window.modernSidebar?.toggle()">
                        <i class="fas fa-cogs"></i>
                    </button>
                </div>
            </div>
        </nav>

        <!-- Content -->
        <div class="content-container">
            <div class="container-fluid">
                <!-- Header -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header bg-success text-white">
                                <h4 class="card-title mb-0">
                                    <i class="fas fa-external-link-alt me-2"></i>Dropdown Outside Sidebar Test
                                </h4>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-success">
                                    <h6><i class="fas fa-check-circle me-2"></i>Test Instructions:</h6>
                                    <ol class="mb-0">
                                        <li><strong>Collapse Sidebar:</strong> Click the toggle button to collapse sidebar</li>
                                        <li><strong>Click Menu with Submenu:</strong> Click on "Keuangan", "Laporan", or "Admin Panel"</li>
                                        <li><strong>Dropdown Should Appear:</strong> Outside the sidebar, not cut off</li>
                                        <li><strong>Check Position:</strong> Dropdown should be positioned to the right of sidebar</li>
                                        <li><strong>Test Interactions:</strong> Click items, click outside to close</li>
                                    </ol>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Test Controls -->
                <div class="row">
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="card-title mb-0">
                                    <i class="fas fa-gamepad me-2"></i>Quick Test Controls
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="d-grid gap-2">
                                    <button class="btn btn-primary" onclick="testCollapse()">
                                        <i class="fas fa-compress me-1"></i>Test: Collapse & Show Dropdown
                                    </button>
                                    <button class="btn btn-success" onclick="window.modernSidebar?.expand()">
                                        <i class="fas fa-expand me-1"></i>Expand Sidebar
                                    </button>
                                    <button class="btn btn-warning" onclick="window.modernSidebar?.hideAllDropdowns()">
                                        <i class="fas fa-times me-1"></i>Hide All Dropdowns
                                    </button>
                                    <button class="btn btn-info" onclick="toggleTheme()">
                                        <i class="fas fa-palette me-1"></i>Toggle Dark Mode
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="card-title mb-0">
                                    <i class="fas fa-info-circle me-2"></i>Expected Results
                                </h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled mb-0">
                                    <li><i class="fas fa-check text-success me-2"></i>Dropdown appears outside sidebar</li>
                                    <li><i class="fas fa-check text-success me-2"></i>No content is cut off</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Positioned to the right of sidebar</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Has proper header with menu title</li>
                                    <li><i class="fas fa-check text-success me-2"></i>All submenu items are clickable</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Active item is highlighted</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Closes when clicking outside</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Responsive positioning</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Debug Info -->
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="card-title mb-0">
                                    <i class="fas fa-bug me-2"></i>Debug Information
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <strong>Sidebar State:</strong>
                                        <span id="sidebarState" class="badge bg-success">Expanded</span>
                                    </div>
                                    <div class="col-md-3">
                                        <strong>Dropdown Count:</strong>
                                        <span id="dropdownCount" class="badge bg-info">0</span>
                                    </div>
                                    <div class="col-md-3">
                                        <strong>Sidebar Width:</strong>
                                        <span id="sidebarWidth" class="badge bg-warning">280px</span>
                                    </div>
                                    <div class="col-md-3">
                                        <strong>Viewport:</strong>
                                        <span id="viewport" class="badge bg-secondary">-</span>
                                    </div>
                                </div>
                                <div class="mt-3">
                                    <div id="debugLog" class="bg-dark text-light p-2 rounded" style="height: 150px; overflow-y: auto; font-family: monospace; font-size: 0.875rem;">
                                        <div>Debug log will appear here...</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/modern-sidebar.js?v=<?= time() ?>"></script>
    <script src="assets/js/theme-manager.js?v=<?= time() ?>"></script>

    <script>
        // Test function
        function testCollapse() {
            window.modernSidebar?.collapse();
            setTimeout(() => {
                // Try to trigger dropdown for first submenu
                const firstSubmenu = document.querySelector('.menu-item.has-submenu');
                if (firstSubmenu) {
                    firstSubmenu.click();
                    logDebug('Triggered dropdown for first submenu');
                }
            }, 500);
        }

        // Debug logging
        function logDebug(message) {
            const log = document.getElementById('debugLog');
            if (log) {
                const time = new Date().toLocaleTimeString();
                log.innerHTML += `<div>[${time}] ${message}</div>`;
                log.scrollTop = log.scrollHeight;
            }
        }

        // Update debug info
        function updateDebugInfo() {
            const isCollapsed = document.body.classList.contains('sidebar-collapsed');
            const dropdowns = document.querySelectorAll('.collapsed-dropdown');
            const sidebar = document.querySelector('.modern-sidebar');
            
            document.getElementById('sidebarState').textContent = isCollapsed ? 'Collapsed' : 'Expanded';
            document.getElementById('sidebarState').className = isCollapsed ? 'badge bg-warning' : 'badge bg-success';
            
            document.getElementById('dropdownCount').textContent = dropdowns.length;
            
            if (sidebar) {
                const rect = sidebar.getBoundingClientRect();
                document.getElementById('sidebarWidth').textContent = rect.width + 'px';
            }
            
            document.getElementById('viewport').textContent = `${window.innerWidth}x${window.innerHeight}`;
        }

        // Monitor changes
        const observer = new MutationObserver(() => {
            updateDebugInfo();
            logDebug('DOM mutation detected');
        });
        
        observer.observe(document.body, { 
            attributes: true, 
            attributeFilter: ['class'],
            childList: true,
            subtree: true
        });

        // Initial update
        document.addEventListener('DOMContentLoaded', () => {
            updateDebugInfo();
            logDebug('Page loaded');
            
            // Override console.log for dropdown positioning
            const originalLog = console.log;
            console.log = function(...args) {
                if (args[0] && args[0].includes('Dropdown positioned')) {
                    logDebug(args.join(' '));
                }
                originalLog.apply(console, args);
            };
        });

        // Window resize
        window.addEventListener('resize', () => {
            updateDebugInfo();
            logDebug('Window resized');
        });
    </script>
</body>
</html>
