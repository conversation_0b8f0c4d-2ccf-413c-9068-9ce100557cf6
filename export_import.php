<?php
require_once 'includes/config/database.php';
require_once 'includes/helpers/function_check.php';
require_once 'includes/helpers/functions.php';
require_once 'includes/helpers/export_import.php';

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isLoggedIn()) {
    setFlashMessage('danger', 'Silakan login terlebih dahulu');
    redirect('login.php');
}

$currentUser = getCurrentUser();
$currentPage = 'export_import';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'export_transactions':
                $filters = [
                    'start_date' => $_POST['start_date'] ?? '',
                    'end_date' => $_POST['end_date'] ?? '',
                    'kategori_id' => $_POST['kategori_id'] ?? ''
                ];
                $result = exportTransactions($currentUser['id'], $filters);
                if ($result['success']) {
                    setFlashMessage('success', 'Data transaksi berhasil diekspor: ' . $result['filename']);
                } else {
                    setFlashMessage('danger', $result['message']);
                }
                break;
                
            case 'export_suppliers':
                $result = exportSuppliers($currentUser['id']);
                if ($result['success']) {
                    setFlashMessage('success', 'Data supplier berhasil diekspor: ' . $result['filename']);
                } else {
                    setFlashMessage('danger', $result['message']);
                }
                break;
                
            case 'export_inventory':
                $result = exportInventory($currentUser['id']);
                if ($result['success']) {
                    setFlashMessage('success', 'Data inventory berhasil diekspor: ' . $result['filename']);
                } else {
                    setFlashMessage('danger', $result['message']);
                }
                break;
        }
        redirect('export_import.php');
    }
}

// Get categories for filter
$stmt = $pdo->prepare("SELECT id, nama FROM kategori WHERE user_id = ? ORDER BY nama");
$stmt->execute([$currentUser['id']]);
$categories = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get export files
$exports = getExportList();

// Include header and sidebar
require_once 'includes/views/layouts/header.php';
require_once 'includes/views/layouts/sidebar.php';
?>

<div class="modern-layout">
    <div class="modern-container">
        <!-- Modern Page Header -->
        <div class="modern-page-header modern-flex modern-justify-between modern-items-center modern-mb-xl">
            <div>
                <h1 class="modern-page-title modern-mb-0">Export & Import Data</h1>
                <p class="modern-page-subtitle">Ekspor dan impor data untuk backup atau migrasi</p>
            </div>
        </div>

        <!-- Flash Messages -->
        <?php if ($flash = getFlashMessage()): ?>
        <div class="modern-alert modern-alert-<?= $flash['type'] ?> modern-mb-lg">
            <div class="modern-alert-content">
                <i class="fas fa-<?= $flash['type'] === 'success' ? 'check-circle' : 'exclamation-triangle' ?> modern-alert-icon"></i>
                <div class="modern-alert-message"><?= $flash['message'] ?></div>
            </div>
            <button type="button" class="modern-alert-close" onclick="this.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <?php endif; ?>

        <div class="modern-grid modern-grid-cols-2 modern-gap-lg">
            <!-- Export Section -->
            <div class="modern-card">
                <div class="modern-card-header">
                    <h5 class="modern-card-title">
                        <i class="fas fa-download modern-text-success modern-mr-sm"></i>
                        Export Data
                    </h5>
                </div>
                <div class="modern-card-body">
                    <!-- Export Transactions -->
                    <div class="modern-form-section modern-mb-lg">
                        <h6 class="modern-form-section-title">
                            <i class="fas fa-exchange-alt modern-mr-xs"></i>
                            Export Transaksi
                        </h6>
                        <form method="POST" class="modern-form">
                            <input type="hidden" name="action" value="export_transactions">
                            
                            <div class="modern-grid modern-grid-cols-2 modern-gap-sm modern-mb-sm">
                                <div class="modern-form-group">
                                    <label class="modern-form-label">Tanggal Mulai</label>
                                    <input type="date" name="start_date" class="modern-form-input">
                                </div>
                                <div class="modern-form-group">
                                    <label class="modern-form-label">Tanggal Selesai</label>
                                    <input type="date" name="end_date" class="modern-form-input">
                                </div>
                            </div>
                            
                            <div class="modern-form-group modern-mb-sm">
                                <label class="modern-form-label">Kategori</label>
                                <select name="kategori_id" class="modern-form-select">
                                    <option value="">Semua Kategori</option>
                                    <?php foreach ($categories as $category): ?>
                                    <option value="<?= $category['id'] ?>"><?= htmlspecialchars($category['nama']) ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            
                            <button type="submit" class="modern-btn modern-btn-success modern-btn-sm modern-w-full">
                                <i class="fas fa-download"></i>
                                Export Transaksi
                            </button>
                        </form>
                    </div>

                    <!-- Export Suppliers -->
                    <div class="modern-form-section modern-mb-lg">
                        <h6 class="modern-form-section-title">
                            <i class="fas fa-truck modern-mr-xs"></i>
                            Export Supplier
                        </h6>
                        <form method="POST" class="modern-form">
                            <input type="hidden" name="action" value="export_suppliers">
                            <button type="submit" class="modern-btn modern-btn-success modern-btn-sm modern-w-full">
                                <i class="fas fa-download"></i>
                                Export Semua Supplier
                            </button>
                        </form>
                    </div>

                    <!-- Export Inventory -->
                    <div class="modern-form-section">
                        <h6 class="modern-form-section-title">
                            <i class="fas fa-boxes modern-mr-xs"></i>
                            Export Inventory
                        </h6>
                        <form method="POST" class="modern-form">
                            <input type="hidden" name="action" value="export_inventory">
                            <button type="submit" class="modern-btn modern-btn-success modern-btn-sm modern-w-full">
                                <i class="fas fa-download"></i>
                                Export Semua Inventory
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Import Section -->
            <div class="modern-card">
                <div class="modern-card-header">
                    <h5 class="modern-card-title">
                        <i class="fas fa-upload modern-text-primary modern-mr-sm"></i>
                        Import Data
                    </h5>
                </div>
                <div class="modern-card-body">
                    <div class="modern-alert modern-alert-info modern-mb-lg">
                        <div class="modern-alert-content">
                            <i class="fas fa-info-circle modern-alert-icon"></i>
                            <div class="modern-alert-message">
                                <strong>Format Import:</strong><br>
                                • File harus dalam format CSV<br>
                                • Gunakan template yang disediakan<br>
                                • Pastikan format data sesuai
                            </div>
                        </div>
                    </div>

                    <!-- Import Templates -->
                    <div class="modern-form-section modern-mb-lg">
                        <h6 class="modern-form-section-title">
                            <i class="fas fa-file-download modern-mr-xs"></i>
                            Download Template
                        </h6>
                        <div class="modern-grid modern-grid-cols-1 modern-gap-xs">
                            <a href="templates/template_transaksi.csv" class="modern-btn modern-btn-outline-primary modern-btn-sm" download>
                                <i class="fas fa-download"></i>
                                Template Transaksi
                            </a>
                            <a href="templates/template_supplier.csv" class="modern-btn modern-btn-outline-primary modern-btn-sm" download>
                                <i class="fas fa-download"></i>
                                Template Supplier
                            </a>
                            <a href="templates/template_inventory.csv" class="modern-btn modern-btn-outline-primary modern-btn-sm" download>
                                <i class="fas fa-download"></i>
                                Template Inventory
                            </a>
                        </div>
                    </div>

                    <!-- Import Form -->
                    <div class="modern-form-section">
                        <h6 class="modern-form-section-title">
                            <i class="fas fa-upload modern-mr-xs"></i>
                            Upload File Import
                        </h6>
                        <form method="POST" enctype="multipart/form-data" class="modern-form">
                            <div class="modern-form-group modern-mb-sm">
                                <label class="modern-form-label">Pilih File CSV</label>
                                <input type="file" name="import_file" accept=".csv" class="modern-form-input" required>
                            </div>
                            
                            <div class="modern-form-group modern-mb-sm">
                                <label class="modern-form-label">Jenis Data</label>
                                <select name="import_type" class="modern-form-select" required>
                                    <option value="">Pilih Jenis Data</option>
                                    <option value="transaksi">Transaksi</option>
                                    <option value="supplier">Supplier</option>
                                    <option value="inventory">Inventory</option>
                                </select>
                            </div>
                            
                            <button type="submit" class="modern-btn modern-btn-primary modern-btn-sm modern-w-full">
                                <i class="fas fa-upload"></i>
                                Import Data
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Export Files List -->
        <div class="modern-card modern-mt-lg">
            <div class="modern-card-header">
                <h5 class="modern-card-title">
                    <i class="fas fa-file-archive modern-text-info modern-mr-sm"></i>
                    File Export Tersedia
                </h5>
                <div class="modern-card-actions">
                    <span class="modern-badge modern-badge-info">
                        <?= count($exports) ?> file
                    </span>
                </div>
            </div>
            <div class="modern-card-body modern-p-0">
                <div class="modern-table-responsive">
                    <table class="modern-table">
                        <thead class="modern-table-header">
                            <tr>
                                <th class="modern-table-th">Filename</th>
                                <th class="modern-table-th">Size</th>
                                <th class="modern-table-th">Created</th>
                                <th class="modern-table-th modern-text-center">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="modern-table-body">
                            <?php if (empty($exports)): ?>
                            <tr>
                                <td colspan="4" class="modern-table-td">
                                    <div class="modern-empty-state">
                                        <div class="modern-empty-icon">
                                            <i class="fas fa-file-export"></i>
                                        </div>
                                        <div class="modern-empty-content">
                                            <h6 class="modern-empty-title">Belum Ada File Export</h6>
                                            <p class="modern-empty-text">Export data untuk melihat file di sini</p>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <?php else: ?>
                            <?php foreach ($exports as $export): ?>
                            <tr class="modern-table-row">
                                <td class="modern-table-td">
                                    <div class="modern-table-content">
                                        <div class="modern-table-title">
                                            <i class="fas fa-file-csv modern-text-success modern-mr-sm"></i>
                                            <?= htmlspecialchars($export['filename']) ?>
                                        </div>
                                    </div>
                                </td>
                                <td class="modern-table-td">
                                    <span class="modern-badge modern-badge-light">
                                        <?= $export['size_formatted'] ?>
                                    </span>
                                </td>
                                <td class="modern-table-td">
                                    <div class="modern-table-date">
                                        <i class="fas fa-calendar modern-text-muted modern-mr-xs"></i>
                                        <?= $export['created_formatted'] ?>
                                    </div>
                                </td>
                                <td class="modern-table-td modern-text-center">
                                    <a href="exports/<?= $export['filename'] ?>" 
                                       class="modern-btn modern-btn-success modern-btn-sm" 
                                       download title="Download">
                                        <i class="fas fa-download"></i>
                                    </a>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/views/layouts/footer.php'; ?>
