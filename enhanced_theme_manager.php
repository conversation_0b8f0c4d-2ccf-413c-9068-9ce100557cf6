<?php
require_once 'includes/config/database.php';
require_once 'includes/helpers/functions.php';
require_once 'includes/helpers/theme_helper.php';

// Check admin access
$currentUser = getCurrentUser();
if (!$currentUser || $currentUser['role'] !== 'admin') {
    setFlashMessage('danger', 'Akses ditolak! Anda tidak memiliki izin untuk mengakses halaman ini.');
    redirect('dashboard.php');
}

$pageTitle = 'Enhanced Theme Manager';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    try {
        switch ($_POST['action']) {
            case 'apply_theme':
                $themeId = $_POST['theme_id'] ?? '';
                if ($themeId) {
                    $predefinedThemes = getPredefinedThemes();
                    
                    if (isset($predefinedThemes[$themeId])) {
                        $theme = $predefinedThemes[$themeId];
                        
                        // Update user theme preferences
                        $result = updateUserThemePreferences($currentUser['id'], [
                            'theme_mode' => $theme['theme_mode'],
                            'primary_color' => $theme['primary_color'],
                            'secondary_color' => $theme['secondary_color'],
                            'sidebar_color' => $theme['sidebar_color'],
                            'navbar_color' => $theme['navbar_color'],
                            'layout_style' => $theme['layout_style'] ?? 'standard'
                        ]);
                        
                        if ($result) {
                            setFlashMessage('success', 'Theme "' . $theme['name'] . '" berhasil diterapkan');
                        } else {
                            setFlashMessage('danger', 'Gagal menerapkan theme');
                        }
                    } else {
                        setFlashMessage('danger', 'Theme tidak ditemukan');
                    }
                }
                break;
        }
    } catch (Exception $e) {
        setFlashMessage('danger', 'Error: ' . $e->getMessage());
        error_log("Enhanced theme manager error: " . $e->getMessage());
    }
    
    redirect('enhanced_theme_manager.php');
}

// Get current user theme preferences
$userTheme = getUserThemePreferences($currentUser['id']);

// Get predefined themes
$predefinedThemes = getPredefinedThemes();

include 'includes/views/layouts/header.php';
?>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">🎨 Enhanced Theme Manager</h1>
                    <p class="text-muted">Discover and apply beautiful themes with advanced layouts</p>
                </div>
                <div>
                    <a href="layout_manager.php" class="btn btn-outline-info me-2">
                        <i class="fas fa-th-large me-2"></i>Layout Manager
                    </a>
                    <a href="theme_manager.php" class="btn btn-outline-primary me-2">
                        <i class="fas fa-palette me-2"></i>Classic Theme Manager
                    </a>
                    <a href="/keuangan/dashboard.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Current Theme Status -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-primary">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-star me-2"></i>Current Active Theme</h5>
                </div>
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <?php
                            $currentThemeName = 'Custom Theme';
                            foreach ($predefinedThemes as $id => $theme) {
                                if ($userTheme && 
                                    $userTheme['primary_color'] === $theme['primary_color'] && 
                                    $userTheme['theme_mode'] === $theme['theme_mode']) {
                                    $currentThemeName = $theme['name'];
                                    break;
                                }
                            }
                            ?>
                            <h6 class="mb-1"><?= $currentThemeName ?></h6>
                            <p class="text-muted mb-2">Currently active theme configuration</p>
                            <div class="theme-details">
                                <span class="badge bg-primary me-2">
                                    <i class="fas fa-palette me-1"></i><?= $userTheme['primary_color'] ?? '#2563eb' ?>
                                </span>
                                <span class="badge bg-secondary me-2">
                                    <i class="fas fa-moon me-1"></i><?= ucfirst($userTheme['theme_mode'] ?? 'light') ?>
                                </span>
                                <span class="badge bg-info me-2">
                                    <i class="fas fa-th-large me-1"></i><?= ucfirst($userTheme['layout_style'] ?? 'standard') ?>
                                </span>
                            </div>
                        </div>
                        <div class="col-md-4 text-end">
                            <div class="current-theme-preview">
                                <div class="mini-preview">
                                    <div class="mini-sidebar" style="background: <?= $userTheme['primary_color'] ?? '#2563eb' ?>"></div>
                                    <div class="mini-content">
                                        <div class="mini-navbar" style="background: <?= $userTheme['primary_color'] ?? '#2563eb' ?>"></div>
                                        <div class="mini-main <?= ($userTheme['theme_mode'] ?? 'light') === 'dark' ? 'dark' : 'light' ?>"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Theme Categories -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Theme Categories</h5>
                </div>
                <div class="card-body">
                    <div class="theme-categories">
                        <button class="btn btn-outline-primary active" data-category="all">
                            <i class="fas fa-th me-2"></i>All Themes (<?= count($predefinedThemes) ?>)
                        </button>
                        <button class="btn btn-outline-success" data-category="light">
                            <i class="fas fa-sun me-2"></i>Light Themes
                        </button>
                        <button class="btn btn-outline-dark" data-category="dark">
                            <i class="fas fa-moon me-2"></i>Dark Themes
                        </button>
                        <button class="btn btn-outline-info" data-category="modern">
                            <i class="fas fa-rocket me-2"></i>Modern Layouts
                        </button>
                        <button class="btn btn-outline-warning" data-category="minimal">
                            <i class="fas fa-minimize me-2"></i>Minimal Designs
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Theme Grid -->
    <div class="row" id="themeGrid">
        <?php foreach ($predefinedThemes as $themeId => $theme): ?>
        <div class="col-md-6 col-lg-4 col-xl-3 mb-4 theme-item" 
             data-category="<?= $theme['theme_mode'] ?>" 
             data-layout="<?= $theme['layout_style'] ?>">
            <div class="card enhanced-theme-card <?= ($userTheme && $userTheme['primary_color'] === $theme['primary_color'] && $userTheme['theme_mode'] === $theme['theme_mode']) ? 'active-theme' : '' ?>">
                <div class="theme-preview-container">
                    <div class="enhanced-theme-preview">
                        <div class="preview-sidebar" style="background: <?= $theme['primary_color'] ?>"></div>
                        <div class="preview-content">
                            <div class="preview-navbar" style="background: <?= $theme['primary_color'] ?>"></div>
                            <div class="preview-main <?= $theme['theme_mode'] ?>">
                                <div class="preview-card"></div>
                                <div class="preview-card"></div>
                                <div class="preview-card"></div>
                            </div>
                        </div>
                    </div>
                    
                    <?php if ($userTheme && $userTheme['primary_color'] === $theme['primary_color'] && $userTheme['theme_mode'] === $theme['theme_mode']): ?>
                    <div class="active-badge">
                        <i class="fas fa-check"></i>
                    </div>
                    <?php endif; ?>
                </div>
                
                <div class="card-body">
                    <div class="theme-header mb-3">
                        <h6 class="theme-title"><?= htmlspecialchars($theme['name']) ?></h6>
                        <p class="theme-description"><?= htmlspecialchars($theme['description']) ?></p>
                    </div>
                    
                    <div class="theme-specs mb-3">
                        <div class="spec-row">
                            <span class="spec-label">Mode:</span>
                            <span class="spec-value">
                                <i class="fas fa-<?= $theme['theme_mode'] === 'dark' ? 'moon' : 'sun' ?> me-1"></i>
                                <?= ucfirst($theme['theme_mode']) ?>
                            </span>
                        </div>
                        <div class="spec-row">
                            <span class="spec-label">Layout:</span>
                            <span class="spec-value">
                                <i class="fas fa-th-large me-1"></i>
                                <?= ucfirst($theme['layout_style']) ?>
                            </span>
                        </div>
                        <div class="spec-row">
                            <span class="spec-label">Colors:</span>
                            <span class="spec-value">
                                <span class="color-preview" style="background: <?= $theme['primary_color'] ?>"></span>
                                <span class="color-preview" style="background: <?= $theme['secondary_color'] ?>"></span>
                            </span>
                        </div>
                    </div>
                    
                    <form method="POST" class="theme-apply-form">
                        <input type="hidden" name="action" value="apply_theme">
                        <input type="hidden" name="theme_id" value="<?= $themeId ?>">
                        <button type="submit" class="btn btn-primary w-100 apply-theme-btn">
                            <i class="fas fa-magic me-2"></i>Apply Theme
                        </button>
                    </form>
                </div>
            </div>
        </div>
        <?php endforeach; ?>
    </div>
</div>

<style>
/* Enhanced Theme Card Styles */
.enhanced-theme-card {
    border: 2px solid transparent;
    border-radius: 16px;
    transition: all 0.3s ease;
    overflow: hidden;
    height: 100%;
    position: relative;
}

.enhanced-theme-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 40px rgba(0,0,0,0.15);
    border-color: var(--primary-color);
}

.enhanced-theme-card.active-theme {
    border-color: #28a745;
    box-shadow: 0 8px 30px rgba(40, 167, 69, 0.3);
}

.theme-preview-container {
    position: relative;
    height: 180px;
    overflow: hidden;
}

.enhanced-theme-preview {
    display: flex;
    height: 100%;
    transform: scale(0.8);
    transform-origin: center;
    width: 125%;
    margin-left: -12.5%;
}

.preview-sidebar {
    width: 80px;
    background: #343a40;
    position: relative;
}

.preview-content {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.preview-navbar {
    height: 40px;
    background: #007bff;
}

.preview-main {
    flex: 1;
    padding: 15px;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.preview-main.light {
    background: #ffffff;
}

.preview-main.dark {
    background: #1a1a1a;
}

.preview-card {
    height: 25px;
    background: rgba(0,0,0,0.1);
    border-radius: 6px;
}

.preview-main.dark .preview-card {
    background: rgba(255,255,255,0.1);
}

.active-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 30px;
    height: 30px;
    background: #28a745;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 14px;
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.4);
}

.theme-header {
    text-align: center;
}

.theme-title {
    font-weight: 600;
    margin-bottom: 8px;
    color: var(--text-primary);
}

.theme-description {
    font-size: 0.85rem;
    color: var(--text-muted);
    margin-bottom: 0;
    line-height: 1.4;
}

.theme-specs {
    font-size: 0.8rem;
}

.spec-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 6px;
}

.spec-label {
    color: var(--text-muted);
    font-weight: 500;
}

.spec-value {
    display: flex;
    align-items: center;
    color: var(--text-primary);
}

.color-preview {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    border: 2px solid white;
    margin-left: 4px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.2);
}

.apply-theme-btn {
    border-radius: 10px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.apply-theme-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,123,255,0.4);
}

/* Current Theme Preview */
.mini-preview {
    display: flex;
    width: 120px;
    height: 80px;
    border-radius: 8px;
    overflow: hidden;
    border: 2px solid var(--border-color);
    margin-left: auto;
}

.mini-sidebar {
    width: 30px;
    background: #343a40;
}

.mini-content {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.mini-navbar {
    height: 20px;
    background: #007bff;
}

.mini-main {
    flex: 1;
}

.mini-main.light {
    background: #f8f9fa;
}

.mini-main.dark {
    background: #343a40;
}

/* Theme Categories */
.theme-categories {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.theme-categories .btn {
    border-radius: 25px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.theme-categories .btn.active {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

/* Responsive */
@media (max-width: 768px) {
    .enhanced-theme-preview {
        transform: scale(0.7);
        width: 143%;
        margin-left: -21.5%;
    }
    
    .theme-categories {
        justify-content: center;
    }
    
    .theme-categories .btn {
        font-size: 0.85rem;
        padding: 0.5rem 1rem;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Theme category filtering
    const categoryButtons = document.querySelectorAll('[data-category]');
    const themeItems = document.querySelectorAll('.theme-item');
    
    categoryButtons.forEach(button => {
        button.addEventListener('click', function() {
            const category = this.dataset.category;
            
            // Update active button
            categoryButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
            
            // Filter themes
            themeItems.forEach(item => {
                if (category === 'all') {
                    item.style.display = 'block';
                } else if (category === 'modern') {
                    item.style.display = item.dataset.layout === 'modern' ? 'block' : 'none';
                } else if (category === 'minimal') {
                    item.style.display = item.dataset.layout === 'minimal' ? 'block' : 'none';
                } else {
                    item.style.display = item.dataset.category === category ? 'block' : 'none';
                }
            });
        });
    });
    
    // Enhanced apply theme functionality
    const applyForms = document.querySelectorAll('.theme-apply-form');
    applyForms.forEach(form => {
        form.addEventListener('submit', function(e) {
            const button = this.querySelector('.apply-theme-btn');
            const originalText = button.innerHTML;
            
            button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Applying...';
            button.disabled = true;
            
            // Re-enable after 2 seconds (form will submit normally)
            setTimeout(() => {
                button.innerHTML = originalText;
                button.disabled = false;
            }, 2000);
        });
    });
});
</script>

<?php include 'includes/views/layouts/footer.php'; ?>
