/**
 * Navbar Functions - Consolidated JavaScript
 * All navbar-related functionality in one place
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 Navbar functions initialized');
    
    // Initialize all navbar functions
    initializeSearch();
    initializeDynamicGreeting();
    initializeNotifications();
    initializeTooltips();
});

/**
 * Search Functionality
 */
function initializeSearch() {
    const searchInput = document.getElementById('globalSearch');
    const searchResults = document.getElementById('searchResults');
    let searchTimeout;

    if (!searchInput) return;

    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        const query = this.value.trim();

        if (query.length < 2) {
            hideSearchResults();
            return;
        }

        searchTimeout = setTimeout(() => {
            performSearch(query);
        }, 300);
    });

    searchInput.addEventListener('keydown', function(e) {
        const items = document.querySelectorAll('.search-result-item');
        
        if (e.key === 'ArrowDown') {
            e.preventDefault();
            navigateResults(items, 1);
        } else if (e.key === 'ArrowUp') {
            e.preventDefault();
            navigateResults(items, -1);
        } else if (e.key === 'Enter') {
            e.preventDefault();
            const activeItem = document.querySelector('.search-result-item.active');
            if (activeItem) {
                activeItem.click();
            }
        } else if (e.key === 'Escape') {
            hideSearchResults();
            this.blur();
        }
    });

    // Hide results when clicking outside
    document.addEventListener('click', function(e) {
        if (!searchInput.contains(e.target) && !searchResults?.contains(e.target)) {
            hideSearchResults();
        }
    });
}

async function performSearch(query) {
    try {
        const response = await fetch(`api/search.php?q=${encodeURIComponent(query)}`);
        if (!response.ok) throw new Error('Search failed');
        
        const results = await response.json();
        displaySearchResults(results, query);
    } catch (error) {
        console.error('Search error:', error);
        showSearchError();
    }
}

function displaySearchResults(results, query) {
    const resultsContainer = document.getElementById('searchResults');
    if (!resultsContainer) return;

    if (!results || results.length === 0) {
        resultsContainer.innerHTML = `
            <div class="search-no-results">
                <i class="fas fa-search text-muted"></i>
                <p class="mb-0">No results found for "${query}"</p>
            </div>
        `;
        resultsContainer.style.display = 'block';
        return;
    }

    const html = results.map(result => {
        return `
            <div class="search-result-item" onclick="window.location.href='${result.url}'">
                <div class="search-result-icon">
                    <i class="${result.icon || 'fas fa-file'}"></i>
                </div>
                <div class="search-result-content">
                    <div class="search-result-title">${highlightQuery(result.title, query)}</div>
                    <div class="search-result-subtitle">${result.subtitle}</div>
                    ${result.date ? `<div class="search-result-date">${formatDate(result.date)}</div>` : ''}
                </div>
                <div class="search-result-type">${result.type}</div>
            </div>
        `;
    }).join('');

    resultsContainer.innerHTML = html;
    resultsContainer.style.display = 'block';
}

function hideSearchResults() {
    const resultsContainer = document.getElementById('searchResults');
    if (resultsContainer) {
        resultsContainer.style.display = 'none';
        resultsContainer.innerHTML = '';
    }
}

function navigateResults(items, direction) {
    const currentActive = document.querySelector('.search-result-item.active');
    let newIndex = 0;

    if (currentActive) {
        const currentIndex = Array.from(items).indexOf(currentActive);
        newIndex = currentIndex + direction;
        currentActive.classList.remove('active');
    }

    if (newIndex < 0) newIndex = items.length - 1;
    if (newIndex >= items.length) newIndex = 0;

    if (items[newIndex]) {
        items[newIndex].classList.add('active');
        items[newIndex].scrollIntoView({ block: 'nearest' });
    }
}

function highlightQuery(text, query) {
    const regex = new RegExp(`(${query})`, 'gi');
    return text.replace(regex, '<mark>$1</mark>');
}

function showSearchError() {
    const resultsContainer = document.getElementById('searchResults');
    if (resultsContainer) {
        resultsContainer.innerHTML = `
            <div class="search-error">
                <i class="fas fa-exclamation-triangle text-warning"></i>
                <p class="mb-0">Search temporarily unavailable</p>
            </div>
        `;
        resultsContainer.style.display = 'block';
    }
}

/**
 * Dynamic Greeting System
 */
function initializeDynamicGreeting() {
    updateDynamicGreeting();
    
    // Update every minute
    setInterval(updateDynamicGreeting, 60000);
    
    // Force update after short delay
    setTimeout(updateDynamicGreeting, 100);
    
    // Update on window load
    window.addEventListener('load', () => {
        setTimeout(updateDynamicGreeting, 200);
    });
    
    // Make globally available
    window.updateDynamicGreeting = updateDynamicGreeting;
}

function updateDynamicGreeting() {
    const now = new Date();
    const hour = now.getHours();
    const greetingElement = document.getElementById('dynamicGreeting');
    const greetingIcon = document.getElementById('greetingIcon');

    let greeting = '';
    let icon = '';
    let color = '';

    if (hour >= 5 && hour < 12) {
        greeting = 'Selamat Pagi';
        icon = 'fas fa-sun';
        color = '#ff9800';
    } else if (hour >= 12 && hour < 15) {
        greeting = 'Selamat Siang';
        icon = 'fas fa-sun';
        color = '#ffc107';
    } else if (hour >= 15 && hour < 18) {
        greeting = 'Selamat Sore';
        icon = 'fas fa-cloud-sun';
        color = '#ff5722';
    } else {
        greeting = 'Selamat Malam';
        icon = 'fas fa-moon';
        color = '#3f51b5';
    }

    if (greetingElement) {
        greetingElement.style.opacity = '0.5';
        setTimeout(() => {
            greetingElement.textContent = greeting;
            greetingElement.style.color = color;
            greetingElement.style.opacity = '1';
        }, 150);
    }

    if (greetingIcon) {
        greetingIcon.style.transform = 'rotate(180deg)';
        setTimeout(() => {
            greetingIcon.className = icon;
            greetingIcon.style.color = color;
            greetingIcon.style.transform = 'rotate(0deg)';

            // Keep background transparent
            const brandLogo = greetingIcon.parentElement;
            if (brandLogo) {
                brandLogo.style.setProperty('background', 'transparent', 'important');
                brandLogo.style.setProperty('background-color', 'transparent', 'important');
                brandLogo.style.setProperty('background-image', 'none', 'important');
            }
        }, 150);
    }
}

/**
 * Notifications System
 */
function initializeNotifications() {
    let notificationCheckInterval;
    let lastNotificationCount = parseInt(document.querySelector('.notification-badge')?.textContent || '0');

    // Start real-time updates
    startNotificationUpdates();

    function startNotificationUpdates() {
        // Check every 30 seconds
        notificationCheckInterval = setInterval(fetchNotifications, 30000);

        // Check when page becomes visible
        document.addEventListener('visibilitychange', function() {
            if (!document.hidden) {
                fetchNotifications();
            }
        });
    }

    async function fetchNotifications() {
        try {
            const response = await fetch('api/get_notifications.php');
            if (!response.ok) throw new Error('Network response was not ok');

            const data = await response.json();
            updateNotificationDisplay(data);
        } catch (error) {
            console.error('Error fetching notifications:', error);
        }
    }

    function updateNotificationDisplay(data) {
        const notificationsList = document.getElementById('notificationsList');
        const notificationBadge = document.querySelector('.notification-badge');
        const notificationButton = document.getElementById('notificationsDropdown');

        // Update badge count
        if (data.unread_count !== lastNotificationCount) {
            lastNotificationCount = data.unread_count;

            if (data.unread_count > 0) {
                if (notificationBadge) {
                    notificationBadge.textContent = data.unread_count;
                    notificationBadge.style.display = 'inline-block';
                } else {
                    const badge = document.createElement('span');
                    badge.className = 'notification-badge';
                    badge.textContent = data.unread_count;
                    notificationButton?.appendChild(badge);
                }
            } else {
                if (notificationBadge) {
                    notificationBadge.style.display = 'none';
                }
            }
        }

        // Update notifications list
        if (data.notifications && notificationsList) {
            updateNotificationsList(data.notifications);
        }
    }

    function updateNotificationsList(notifications) {
        const notificationsList = document.getElementById('notificationsList');
        if (!notificationsList) return;

        if (notifications.length === 0) {
            notificationsList.innerHTML = `
                <div class="empty-notifications text-center py-4">
                    <i class="fas fa-bell-slash text-muted mb-3" style="font-size: 2rem;"></i>
                    <h6 class="text-muted">No notifications</h6>
                    <p class="text-muted small mb-0">You're all caught up!</p>
                </div>
            `;
            return;
        }

        const html = notifications.map(notification => {
            const type = notification.type || 'info';
            const title = notification.title || 'No Title';
            const message = notification.message || 'No Message';
            const source = notification.source || '';
            const isRead = notification.is_read;
            const createdAt = notification.created_at;
            const id = notification.id || 0;

            let iconClass = 'fas fa-info-circle text-info';
            if (type === 'error') iconClass = 'fas fa-exclamation-triangle text-danger';
            else if (type === 'warning') iconClass = 'fas fa-exclamation-circle text-warning';
            else if (type === 'success') iconClass = 'fas fa-check-circle text-success';

            const truncatedMessage = message.length > 60 ? message.substring(0, 60) + '...' : message;
            const formattedDate = new Date(createdAt).toLocaleDateString('en-US', {
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });

            return `
                <a class="dropdown-item notification-item ${!isRead ? 'unread' : ''}"
                   href="notifications.php" data-id="${id}" data-type="${type}">
                    <div class="d-flex align-items-start">
                        <div class="notification-icon me-3">
                            <i class="${iconClass}"></i>
                        </div>
                        <div class="notification-content flex-grow-1">
                            <p class="mb-1 fw-medium notification-title">${escapeHtml(title)}</p>
                            <p class="mb-1 small text-muted notification-message">${escapeHtml(truncatedMessage)}</p>
                            <small class="text-muted">
                                <i class="fas fa-clock me-1"></i>
                                ${formattedDate}
                                ${source ? `| <i class="fas fa-tag me-1"></i>${escapeHtml(source)}` : ''}
                            </small>
                        </div>
                        ${!isRead ? '<div class="unread-indicator"></div>' : ''}
                    </div>
                </a>
            `;
        }).join('');

        notificationsList.innerHTML = html;
    }

    // Make refresh function globally available
    window.refreshNotifications = fetchNotifications;
}

/**
 * Initialize Tooltips
 */
function initializeTooltips() {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

/**
 * Utility Functions
 */
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('id-ID', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
}

function escapeHtml(text) {
    const map = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#039;'
    };
    return text.replace(/[&<>"']/g, function(m) { return map[m]; });
}

// Export functions for global access
window.NavbarFunctions = {
    updateDynamicGreeting,
    refreshNotifications: () => window.refreshNotifications?.(),
    hideSearchResults,
    performSearch
};

console.log('✅ Navbar functions loaded successfully');
