<?php
require_once 'includes/config/database.php';
require_once 'includes/helpers/functions.php';
require_once 'includes/helpers/notifications.php';

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isLoggedIn()) {
    setFlashMessage('danger', 'Silakan login terlebih dahulu');
    redirect('/keuangan/login.php');
}

$currentUser = getCurrentUser();
$currentPage = 'pembelian';

// Create pembelian table if not exists
try {
    $pdo->exec("CREATE TABLE IF NOT EXISTS pembelian (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        nomor_pembelian VARCHAR(50) UNIQUE NOT NULL,
        supplier_id INT,
        tanggal_pembelian DATE NOT NULL,
        total_pembelian DECIMAL(15,2) NOT NULL,
        status ENUM('pending', 'diterima', 'dibatalkan') DEFAULT 'pending',
        keterangan TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    )");

    $pdo->exec("CREATE TABLE IF NOT EXISTS detail_pembelian (
        id INT AUTO_INCREMENT PRIMARY KEY,
        pembelian_id INT NOT NULL,
        produk_id INT,
        nama_produk VARCHAR(255) NOT NULL,
        qty INT NOT NULL,
        harga_beli DECIMAL(15,2) NOT NULL,
        subtotal DECIMAL(15,2) NOT NULL,
        FOREIGN KEY (pembelian_id) REFERENCES pembelian(id) ON DELETE CASCADE
    )");

    $pdo->exec("CREATE TABLE IF NOT EXISTS supplier (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        nama_supplier VARCHAR(255) NOT NULL,
        kontak VARCHAR(100),
        alamat TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    )");
} catch (PDOException $e) {
    error_log("Error creating pembelian tables: " . $e->getMessage());
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        try {
            switch ($_POST['action']) {
                case 'add':
                    // Validasi input
                    $errors = [];
                    
                    if (empty($_POST['tanggal_pembelian'])) {
                        $errors[] = 'Tanggal pembelian harus diisi';
                    }
                    
                    if (empty($_POST['total_pembelian'])) {
                        $errors[] = 'Total pembelian harus diisi';
                    }
                    
                    if (empty($errors)) {
                        try {
                            // Generate nomor pembelian
                            $nomor_pembelian = 'PB' . date('Ymd') . sprintf('%04d', rand(1, 9999));
                            
                            // Format jumlah (hapus format angka)
                            $total = str_replace(['.', ','], '', $_POST['total_pembelian']);
                            
                            // Format tanggal ke format Y-m-d
                            $tanggal = date('Y-m-d', strtotime($_POST['tanggal_pembelian']));
                            
                            // Insert pembelian dengan prepared statement
                            $sql = "INSERT INTO pembelian (user_id, nomor_pembelian, supplier_id, tanggal_pembelian, total_pembelian, status, keterangan, created_at) 
                                   VALUES (:user_id, :nomor_pembelian, :supplier_id, :tanggal_pembelian, :total_pembelian, :status, :keterangan, NOW())";
                            
                            $stmt = $pdo->prepare($sql);
                            
                            $params = [
                                ':user_id' => $currentUser['id'],
                                ':nomor_pembelian' => $nomor_pembelian,
                                ':supplier_id' => !empty($_POST['supplier_id']) ? $_POST['supplier_id'] : null,
                                ':tanggal_pembelian' => $tanggal,
                                ':total_pembelian' => $total,
                                ':status' => $_POST['status'] ?? 'pending',
                                ':keterangan' => $_POST['keterangan']
                            ];
                            
                            $result = $stmt->execute($params);
                            
                            if ($result) {
                                // Log aktivitas
                                logActivity($currentUser['id'], sprintf(
                                    'Menambahkan pembelian %s sebesar %s',
                                    $nomor_pembelian,
                                    formatRupiah($total)
                                ));
                                
                                setFlashMessage('success', 'Pembelian berhasil ditambahkan');
                                redirect('/keuangan/pembelian.php');
                            } else {
                                throw new Exception('Gagal menyimpan pembelian');
                            }
                        } catch (Exception $e) {
                            setFlashMessage('danger', $e->getMessage());
                        }
                    } else {
                        setFlashMessage('danger', implode('<br>', $errors));
                    }
                    break;

                case 'update':
                    if (empty($_POST['id'])) {
                        setFlashMessage('danger', 'ID pembelian tidak valid');
                        break;
                    }

                    // Format jumlah (hapus format angka)
                    $total = str_replace(['.', ','], '', $_POST['total_pembelian']);
                    
                    // Format tanggal ke format Y-m-d
                    $tanggal = date('Y-m-d', strtotime($_POST['tanggal_pembelian']));
                    
                    $stmt = $pdo->prepare("
                        UPDATE pembelian 
                        SET supplier_id = ?, tanggal_pembelian = ?, total_pembelian = ?, status = ?, keterangan = ?
                        WHERE id = ? AND user_id = ?
                    ");
                    
                    $result = $stmt->execute([
                        !empty($_POST['supplier_id']) ? $_POST['supplier_id'] : null,
                        $tanggal,
                        $total,
                        $_POST['status'],
                        $_POST['keterangan'],
                        $_POST['id'],
                        $currentUser['id']
                    ]);

                    if ($result) {
                        setFlashMessage('success', 'Pembelian berhasil diperbarui');
                    } else {
                        setFlashMessage('danger', 'Gagal memperbarui pembelian');
                    }
                    break;

                case 'delete':
                    if (empty($_POST['id'])) {
                        setFlashMessage('danger', 'ID pembelian tidak valid');
                        break;
                    }

                    $stmt = $pdo->prepare("DELETE FROM pembelian WHERE id = ? AND user_id = ?");
                    $result = $stmt->execute([$_POST['id'], $currentUser['id']]);

                    if ($result) {
                        setFlashMessage('success', 'Pembelian berhasil dihapus');
                    } else {
                        setFlashMessage('danger', 'Gagal menghapus pembelian');
                    }
                    break;
            }
        } catch (PDOException $e) {
            setFlashMessage('danger', 'Terjadi kesalahan. Silakan coba lagi.');
        }
        redirect('/keuangan/pembelian.php');
    }
}

// Get purchases with pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$perPage = 10;
$offset = ($page - 1) * $perPage;

// Get total records with filters
$where = ["p.user_id = ?"];
$params = [$currentUser['id']];

if (!empty($_GET['status'])) {
    $where[] = "p.status = ?";
    $params[] = $_GET['status'];
}

if (!empty($_GET['supplier_id'])) {
    $where[] = "p.supplier_id = ?";
    $params[] = $_GET['supplier_id'];
}

if (!empty($_GET['start_date'])) {
    $where[] = "p.tanggal_pembelian >= ?";
    $params[] = $_GET['start_date'];
}

if (!empty($_GET['end_date'])) {
    $where[] = "p.tanggal_pembelian <= ?";
    $params[] = $_GET['end_date'];
}

$whereClause = implode(" AND ", $where);

// Get total records
$stmt = $pdo->prepare("
    SELECT COUNT(*) as total 
    FROM pembelian p
    WHERE $whereClause
");
$stmt->execute($params);
$totalRecords = $stmt->fetch()['total'];
$totalPages = ceil($totalRecords / $perPage);

// Get purchases with supplier information
$stmt = $pdo->prepare("
    SELECT 
        p.*,
        s.nama_supplier
    FROM pembelian p
    LEFT JOIN supplier s ON p.supplier_id = s.id
    WHERE $whereClause
    ORDER BY p.tanggal_pembelian DESC, p.created_at DESC
    LIMIT ? OFFSET ?
");

// Create new array for pagination parameters
$paginationParams = $params;
$paginationParams[] = $perPage;
$paginationParams[] = $offset;

$stmt->execute($paginationParams);
$pembelian = $stmt->fetchAll();

// Get suppliers for filter and form
$stmt = $pdo->prepare("
    SELECT * FROM supplier 
    WHERE user_id = ? 
    ORDER BY nama_supplier ASC
");
$stmt->execute([$currentUser['id']]);
$suppliers = $stmt->fetchAll();

// Get statistics
$stmt = $pdo->prepare("
    SELECT 
        SUM(CASE WHEN status = 'pending' THEN total_pembelian ELSE 0 END) as total_pending,
        SUM(CASE WHEN status = 'diterima' THEN total_pembelian ELSE 0 END) as total_diterima,
        COUNT(CASE WHEN status = 'pending' THEN 1 END) as count_pending,
        COUNT(CASE WHEN status = 'diterima' THEN 1 END) as count_diterima
    FROM pembelian
    WHERE user_id = ?
");
$stmt->execute([$currentUser['id']]);
$stats = $stmt->fetch();

// Include header and sidebar
require_once 'includes/views/layouts/header.php';
require_once 'includes/views/layouts/sidebar.php';
?>

<div class="modern-layout">
    <div class="modern-container">
        <!-- Modern Page Header -->
        <div class="modern-page-header modern-flex modern-justify-between modern-items-center modern-mb-xl">
            <div>
                <h1 class="modern-page-title modern-mb-0">Pembelian</h1>
                <p class="modern-page-subtitle">Kelola data pembelian barang dan supplier dengan mudah</p>
            </div>
            <div class="modern-page-actions">
                <button type="button" class="modern-btn modern-btn-primary" data-bs-toggle="modal" data-bs-target="#addPurchaseModal">
                    <i class="fas fa-plus"></i>
                    Tambah Pembelian
                </button>
            </div>
        </div>

        <!-- Modern Statistics Cards -->
        <div class="modern-grid modern-grid-cols-4 modern-gap-lg modern-mb-xl">
            <div class="modern-stats-card modern-stats-warning">
                <div class="modern-stats-content">
                    <div class="modern-stats-info">
                        <div class="modern-stats-label">Total Pending</div>
                        <div class="modern-stats-value"><?= formatRupiah($stats['total_pending'] ?? 0) ?></div>
                        <div class="modern-stats-meta"><?= $stats['count_pending'] ?? 0 ?> transaksi</div>
                    </div>
                    <div class="modern-stats-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                </div>
                <div class="modern-stats-glow"></div>
            </div>

            <div class="modern-stats-card modern-stats-success">
                <div class="modern-stats-content">
                    <div class="modern-stats-info">
                        <div class="modern-stats-label">Total Diterima</div>
                        <div class="modern-stats-value"><?= formatRupiah($stats['total_diterima'] ?? 0) ?></div>
                        <div class="modern-stats-meta"><?= $stats['count_diterima'] ?? 0 ?> transaksi</div>
                    </div>
                    <div class="modern-stats-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                </div>
                <div class="modern-stats-glow"></div>
            </div>

            <div class="modern-stats-card modern-stats-primary">
                <div class="modern-stats-content">
                    <div class="modern-stats-info">
                        <div class="modern-stats-label">Total Pembelian</div>
                        <div class="modern-stats-value"><?= formatRupiah(($stats['total_pending'] ?? 0) + ($stats['total_diterima'] ?? 0)) ?></div>
                        <div class="modern-stats-meta"><?= ($stats['count_pending'] ?? 0) + ($stats['count_diterima'] ?? 0) ?> transaksi</div>
                    </div>
                    <div class="modern-stats-icon">
                        <i class="fas fa-shopping-basket"></i>
                    </div>
                </div>
                <div class="modern-stats-glow"></div>
            </div>

            <div class="modern-stats-card modern-stats-info">
                <div class="modern-stats-content">
                    <div class="modern-stats-info">
                        <div class="modern-stats-label">Supplier</div>
                        <div class="modern-stats-value"><?= count($suppliers) ?></div>
                        <div class="modern-stats-meta">Total supplier</div>
                    </div>
                    <div class="modern-stats-icon">
                        <i class="fas fa-truck"></i>
                    </div>
                </div>
                <div class="modern-stats-glow"></div>
            </div>
        </div>

        <!-- Modern Filter Section -->
        <div class="modern-card modern-mb-lg">
            <div class="modern-card-header">
                <h5 class="modern-card-title">
                    <i class="fas fa-filter modern-text-primary modern-mr-sm"></i>
                    Filter Data Pembelian
                </h5>
            </div>
            <div class="modern-card-body">
                <form action="" method="GET" class="modern-grid modern-grid-cols-4 modern-gap-md modern-items-end">
                    <div class="modern-form-group">
                        <label class="modern-form-label">
                            <i class="fas fa-calendar-alt modern-text-primary"></i>
                            Tanggal Mulai
                        </label>
                        <input type="date" name="start_date" class="modern-form-control" value="<?= $_GET['start_date'] ?? '' ?>">
                    </div>

                    <div class="modern-form-group">
                        <label class="modern-form-label">
                            <i class="fas fa-calendar-check modern-text-primary"></i>
                            Tanggal Akhir
                        </label>
                        <input type="date" name="end_date" class="modern-form-control" value="<?= $_GET['end_date'] ?? '' ?>">
                    </div>

                    <div class="modern-form-group">
                        <label class="modern-form-label">
                            <i class="fas fa-truck modern-text-primary"></i>
                            Supplier
                        </label>
                        <select name="supplier_id" class="modern-form-control">
                            <option value="">🔍 Semua Supplier</option>
                            <?php foreach ($suppliers as $supplier): ?>
                            <option value="<?= $supplier['id'] ?>" <?= (isset($_GET['supplier_id']) && $_GET['supplier_id'] == $supplier['id']) ? 'selected' : '' ?>>
                                🏢 <?= htmlspecialchars($supplier['nama_supplier']) ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="modern-form-group">
                        <label class="modern-form-label">
                            <i class="fas fa-toggle-on modern-text-primary"></i>
                            Status
                        </label>
                        <select name="status" class="modern-form-control">
                            <option value="">🔍 Semua Status</option>
                            <option value="pending" <?= (isset($_GET['status']) && $_GET['status'] == 'pending') ? 'selected' : '' ?>>⏳ Pending</option>
                            <option value="diterima" <?= (isset($_GET['status']) && $_GET['status'] == 'diterima') ? 'selected' : '' ?>>✅ Diterima</option>
                            <option value="dibatalkan" <?= (isset($_GET['status']) && $_GET['status'] == 'dibatalkan') ? 'selected' : '' ?>>❌ Dibatalkan</option>
                        </select>
                    </div>

                    <div class="modern-form-group modern-flex modern-gap-sm modern-mb-0">
                        <button type="submit" class="modern-btn modern-btn-primary modern-flex-1">
                            <i class="fas fa-search"></i>
                            Filter
                        </button>
                        <a href="/keuangan/pembelian.php" class="modern-btn modern-btn-light">
                            <i class="fas fa-sync"></i>
                            Reset
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- Modern Purchases Table -->
        <div class="modern-card">
            <div class="modern-card-header">
                <h5 class="modern-card-title">
                    <i class="fas fa-shopping-basket modern-text-primary modern-mr-sm"></i>
                    Data Pembelian
                </h5>
                <div class="modern-card-actions">
                    <span class="modern-badge modern-badge-info">
                        Total: <?= count($pembelian) ?> data
                    </span>
                </div>
            </div>
            <div class="modern-card-body modern-p-0">
                <div class="modern-table-responsive">
                    <table class="modern-table">
                        <thead class="modern-table-header">
                            <tr>
                                <th class="modern-table-th">
                                    <i class="fas fa-receipt modern-mr-xs"></i>
                                    Nomor Pembelian
                                </th>
                                <th class="modern-table-th">
                                    <i class="fas fa-calendar modern-mr-xs"></i>
                                    Tanggal
                                </th>
                                <th class="modern-table-th">
                                    <i class="fas fa-truck modern-mr-xs"></i>
                                    Supplier
                                </th>
                                <th class="modern-table-th modern-text-right">
                                    <i class="fas fa-money-bill modern-mr-xs"></i>
                                    Total
                                </th>
                                <th class="modern-table-th modern-text-center">
                                    <i class="fas fa-toggle-on modern-mr-xs"></i>
                                    Status
                                </th>
                                <th class="modern-table-th modern-text-center">
                                    <i class="fas fa-cogs modern-mr-xs"></i>
                                    Aksi
                                </th>
                            </tr>
                        </thead>
                        <tbody class="modern-table-body">
                            <?php if (empty($pembelian)): ?>
                            <tr>
                                <td colspan="6" class="modern-table-td">
                                    <div class="modern-empty-state">
                                        <div class="modern-empty-icon">
                                            <i class="fas fa-shopping-basket"></i>
                                        </div>
                                        <div class="modern-empty-content">
                                            <h6 class="modern-empty-title">Belum Ada Data Pembelian</h6>
                                            <p class="modern-empty-text">Belum ada transaksi pembelian yang tercatat</p>
                                            <button type="button" class="modern-btn modern-btn-primary modern-btn-sm" data-bs-toggle="modal" data-bs-target="#addPurchaseModal">
                                                <i class="fas fa-plus"></i>
                                                Tambah Pembelian Pertama
                                            </button>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <?php else: ?>
                            <?php foreach ($pembelian as $item): ?>
                            <tr class="modern-table-row">
                                <td class="modern-table-td">
                                    <div class="modern-table-content">
                                        <div class="modern-table-title"><?= htmlspecialchars($item['nomor_pembelian']) ?></div>
                                        <?php if ($item['keterangan']): ?>
                                            <div class="modern-table-subtitle"><?= htmlspecialchars($item['keterangan']) ?></div>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td class="modern-table-td">
                                    <div class="modern-table-date">
                                        <i class="fas fa-calendar modern-text-muted modern-mr-xs"></i>
                                        <?= formatDate($item['tanggal_pembelian']) ?>
                                    </div>
                                </td>
                                <td class="modern-table-td">
                                    <?php if ($item['nama_supplier']): ?>
                                        <div class="modern-table-content">
                                            <div class="modern-table-title"><?= htmlspecialchars($item['nama_supplier']) ?></div>
                                        </div>
                                    <?php else: ?>
                                        <span class="modern-text-muted">-</span>
                                    <?php endif; ?>
                                </td>
                                <td class="modern-table-td modern-text-right">
                                    <div class="modern-table-amount modern-text-primary"><?= formatRupiah($item['total_pembelian']) ?></div>
                                </td>
                                <td class="modern-table-td modern-text-center">
                                    <div class="modern-badge modern-badge-<?= $item['status'] === 'diterima' ? 'success' : ($item['status'] === 'dibatalkan' ? 'danger' : 'warning') ?>">
                                        <?php if ($item['status'] === 'diterima'): ?>
                                            <i class="fas fa-check"></i>
                                        <?php elseif ($item['status'] === 'dibatalkan'): ?>
                                            <i class="fas fa-times"></i>
                                        <?php else: ?>
                                            <i class="fas fa-clock"></i>
                                        <?php endif; ?>
                                        <?= ucfirst($item['status']) ?>
                                    </div>
                                </td>
                                <td class="modern-table-td modern-text-center">
                                    <div class="modern-action-buttons">
                                        <button type="button" class="modern-btn modern-btn-light modern-btn-sm" onclick="editPurchase(
                                            '<?= $item['id'] ?>',
                                            '<?= $item['supplier_id'] ?>',
                                            '<?= date('Y-m-d', strtotime($item['tanggal_pembelian'])) ?>',
                                            '<?= $item['total_pembelian'] ?>',
                                            '<?= $item['status'] ?>',
                                            '<?= htmlspecialchars($item['keterangan']) ?>'
                                        )" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button type="button" class="modern-btn modern-btn-danger modern-btn-sm" onclick="deletePurchase(<?= $item['id'] ?>)" title="Hapus">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Modern Pagination -->
                <?php if ($totalPages > 1): ?>
                <div class="modern-card-footer">
                    <div class="modern-pagination-wrapper">
                        <div class="modern-pagination-info">
                            <span class="modern-text-muted">
                                Halaman <?= $page ?> dari <?= $totalPages ?>
                                (<?= $totalRecords ?> total data)
                            </span>
                        </div>
                        <nav class="modern-pagination">
                            <?php if ($page > 1): ?>
                            <a class="modern-pagination-btn modern-pagination-prev"
                               href="?page=<?= $page - 1 ?>&<?= http_build_query(array_filter($_GET, function($key) { return $key !== 'page'; }, ARRAY_FILTER_USE_KEY)) ?>">
                                <i class="fas fa-chevron-left"></i>
                                Sebelumnya
                            </a>
                            <?php endif; ?>

                            <div class="modern-pagination-numbers">
                                <?php
                                $start = max(1, $page - 2);
                                $end = min($totalPages, $page + 2);

                                if ($start > 1): ?>
                                    <a class="modern-pagination-number" href="?page=1&<?= http_build_query(array_filter($_GET, function($key) { return $key !== 'page'; }, ARRAY_FILTER_USE_KEY)) ?>">1</a>
                                    <?php if ($start > 2): ?>
                                        <span class="modern-pagination-dots">...</span>
                                    <?php endif; ?>
                                <?php endif; ?>

                                <?php for ($i = $start; $i <= $end; $i++): ?>
                                <a class="modern-pagination-number <?= $i === $page ? 'active' : '' ?>"
                                   href="?page=<?= $i ?>&<?= http_build_query(array_filter($_GET, function($key) { return $key !== 'page'; }, ARRAY_FILTER_USE_KEY)) ?>"><?= $i ?></a>
                                <?php endfor; ?>

                                <?php if ($end < $totalPages): ?>
                                    <?php if ($end < $totalPages - 1): ?>
                                        <span class="modern-pagination-dots">...</span>
                                    <?php endif; ?>
                                    <a class="modern-pagination-number" href="?page=<?= $totalPages ?>&<?= http_build_query(array_filter($_GET, function($key) { return $key !== 'page'; }, ARRAY_FILTER_USE_KEY)) ?>"><?= $totalPages ?></a>
                                <?php endif; ?>
                            </div>

                            <?php if ($page < $totalPages): ?>
                            <a class="modern-pagination-btn modern-pagination-next"
                               href="?page=<?= $page + 1 ?>&<?= http_build_query(array_filter($_GET, function($key) { return $key !== 'page'; }, ARRAY_FILTER_USE_KEY)) ?>">
                                Selanjutnya
                                <i class="fas fa-chevron-right"></i>
                            </a>
                            <?php endif; ?>
                        </nav>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
