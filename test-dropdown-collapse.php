<!DOCTYPE html>
<html lang="id" data-bs-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Dropdown Collapse - KeuanganKu</title>

    <!-- CSS Libraries -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="assets/css/layout-manager.css?v=<?= time() ?>" rel="stylesheet">
    <link href="assets/css/control-sidebar.css?v=<?= time() ?>" rel="stylesheet">
</head>
<body>
    <?php 
    // Simulate current page for testing
    $currentPage = 'transaksi';
    $currentUser = [
        'id' => 1,
        'nama' => 'Test User',
        'role' => 'admin'
    ];
    ?>

    <!-- Sidebar with Dropdown Functionality -->
    <aside class="modern-sidebar">
        <!-- Sidebar Header -->
        <div class="sidebar-header">
            <div class="brand-container">
                <div class="brand-logo">
                    <div class="logo-icon">
                        <i class="fas fa-wallet"></i>
                    </div>
                </div>
                <div class="brand-content">
                    <h1 class="brand-title">KeuanganKu</h1>
                    <p class="brand-subtitle">Financial Manager</p>
                </div>
            </div>
        </div>

        <!-- User Profile Section -->
        <div class="sidebar-user">
            <div class="user-profile">
                <div class="user-avatar">
                    <img src="https://ui-avatars.com/api/?name=Test+User&background=3b82f6&color=fff&size=40" alt="User Avatar" class="avatar-img">
                    <div class="status-indicator"></div>
                </div>
                <div class="user-details">
                    <h6 class="user-name">Test User</h6>
                    <span class="user-role">Admin</span>
                </div>
            </div>
        </div>

        <!-- Navigation Menu -->
        <div class="sidebar-navigation">
            <nav class="nav-menu">
                <!-- Main Section -->
                <div class="menu-section">
                    <div class="menu-section-title">Main</div>
                    
                    <a class="menu-item" href="/keuangan/dashboard.php" data-tooltip="Dashboard">
                        <div class="menu-icon">
                            <i class="fas fa-home"></i>
                        </div>
                        <span class="menu-text">Dashboard</span>
                    </a>
                </div>

                <!-- Keuangan Section with Dropdown -->
                <div class="menu-section">
                    <div class="menu-section-title">Keuangan</div>
                    
                    <button class="menu-item menu-toggle has-submenu active" 
                            data-bs-toggle="collapse" 
                            data-bs-target="#keuanganSubmenu" 
                            aria-expanded="true" 
                            data-tooltip="Keuangan"
                            data-submenu="keuangan">
                        <div class="menu-icon">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                        <span class="menu-text">Keuangan</span>
                        <i class="fas fa-chevron-down menu-arrow"></i>
                    </button>
                    <div class="collapse submenu show" id="keuanganSubmenu">
                        <div class="submenu-container">
                            <a class="submenu-item active" href="transaksi.php">
                                <div class="submenu-icon">
                                    <i class="fas fa-exchange-alt"></i>
                                </div>
                                <span class="submenu-text">Transaksi</span>
                            </a>
                            <a class="submenu-item" href="kategori.php">
                                <div class="submenu-icon">
                                    <i class="fas fa-tags"></i>
                                </div>
                                <span class="submenu-text">Kategori</span>
                            </a>
                            <a class="submenu-item" href="target.php">
                                <div class="submenu-icon">
                                    <i class="fas fa-bullseye"></i>
                                </div>
                                <span class="submenu-text">Target</span>
                            </a>
                            <a class="submenu-item" href="anggaran.php">
                                <div class="submenu-icon">
                                    <i class="fas fa-chart-pie"></i>
                                </div>
                                <span class="submenu-text">Anggaran</span>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Laporan Section with Dropdown -->
                <div class="menu-section">
                    <div class="menu-section-title">Laporan</div>
                    
                    <button class="menu-item menu-toggle has-submenu" 
                            data-bs-toggle="collapse" 
                            data-bs-target="#laporanSubmenu" 
                            aria-expanded="false" 
                            data-tooltip="Laporan"
                            data-submenu="laporan">
                        <div class="menu-icon">
                            <i class="fas fa-chart-bar"></i>
                        </div>
                        <span class="menu-text">Laporan</span>
                        <i class="fas fa-chevron-down menu-arrow"></i>
                    </button>
                    <div class="collapse submenu" id="laporanSubmenu">
                        <div class="submenu-container">
                            <a class="submenu-item" href="laporan.php">
                                <div class="submenu-icon">
                                    <i class="fas fa-file-alt"></i>
                                </div>
                                <span class="submenu-text">Laporan Keuangan</span>
                            </a>
                            <a class="submenu-item" href="analisis.php">
                                <div class="submenu-icon">
                                    <i class="fas fa-analytics"></i>
                                </div>
                                <span class="submenu-text">Analisis</span>
                            </a>
                            <a class="submenu-item" href="grafik.php">
                                <div class="submenu-icon">
                                    <i class="fas fa-chart-area"></i>
                                </div>
                                <span class="submenu-text">Grafik</span>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Admin Section with Dropdown -->
                <div class="menu-section">
                    <div class="menu-section-title">Admin</div>
                    
                    <button class="menu-item menu-toggle has-submenu" 
                            data-bs-toggle="collapse" 
                            data-bs-target="#adminSubmenu" 
                            aria-expanded="false" 
                            data-tooltip="Admin Panel"
                            data-submenu="admin">
                        <div class="menu-icon">
                            <i class="fas fa-cogs"></i>
                        </div>
                        <span class="menu-text">Admin Panel</span>
                        <i class="fas fa-chevron-down menu-arrow"></i>
                    </button>
                    <div class="collapse submenu" id="adminSubmenu">
                        <div class="submenu-container">
                            <a class="submenu-item" href="users.php">
                                <div class="submenu-icon">
                                    <i class="fas fa-users"></i>
                                </div>
                                <span class="submenu-text">Kelola User</span>
                            </a>
                            <a class="submenu-item" href="permissions.php">
                                <div class="submenu-icon">
                                    <i class="fas fa-key"></i>
                                </div>
                                <span class="submenu-text">Permissions</span>
                            </a>
                            <a class="submenu-item" href="settings.php">
                                <div class="submenu-icon">
                                    <i class="fas fa-cog"></i>
                                </div>
                                <span class="submenu-text">Settings</span>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Single Menu Items -->
                <div class="menu-section">
                    <div class="menu-section-title">Other</div>
                    
                    <a class="menu-item" href="profile.php" data-tooltip="Profile">
                        <div class="menu-icon">
                            <i class="fas fa-user"></i>
                        </div>
                        <span class="menu-text">Profile</span>
                    </a>
                </div>
            </nav>
        </div>

        <!-- Enhanced Sidebar Footer -->
        <div class="sidebar-footer">
            <div class="sidebar-footer-content">
                <!-- Control Buttons -->
                <div style="display: flex; gap: 0.5rem;">
                    <button class="theme-toggle-btn" onclick="toggleTheme()" data-theme-toggle
                            data-bs-toggle="tooltip" title="Toggle Dark Mode" style="flex: 1;">
                        <i class="fas fa-moon"></i>
                        <span class="theme-toggle-text">Dark Mode</span>
                    </button>
                    <button class="sidebar-toggle-btn" onclick="window.modernSidebar?.toggle()" 
                            data-bs-toggle="tooltip" title="Toggle Sidebar">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                </div>
            </div>
        </div>
    </aside>

    <!-- Sidebar Overlay for Mobile -->
    <div class="sidebar-overlay"></div>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Navbar -->
        <nav class="navbar navbar-expand-lg modern-navbar">
            <div class="container-fluid">
                <!-- Mobile Sidebar Toggle -->
                <button class="mobile-sidebar-toggle d-lg-none" type="button">
                    <i class="fas fa-bars"></i>
                </button>

                <!-- Brand -->
                <div class="navbar-brand">
                    <div class="brand-logo">
                        <i class="fas fa-mouse-pointer" style="color: #10b981;"></i>
                    </div>
                    <span style="color: #10b981; font-weight: 600;">Dropdown Collapse Test</span>
                </div>

                <!-- Controls -->
                <div class="navbar-nav ms-auto">
                    <button class="nav-link btn btn-link me-2" onclick="toggleTheme()">
                        <i class="fas fa-moon"></i>
                    </button>
                    <button class="nav-link btn btn-link" onclick="window.modernSidebar?.toggle()">
                        <i class="fas fa-cogs"></i>
                    </button>
                </div>
            </div>
        </nav>

        <!-- Content -->
        <div class="content-container">
            <div class="container-fluid">
                <!-- Header -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h4 class="card-title mb-0">
                                    <i class="fas fa-mouse-pointer me-2 text-success"></i>Dropdown Collapse Test
                                </h4>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-info">
                                    <h6><i class="fas fa-info-circle me-2"></i>How to Test:</h6>
                                    <ol class="mb-0">
                                        <li><strong>Click Toggle Button:</strong> Collapse the sidebar using the toggle button in footer</li>
                                        <li><strong>Click Menu Items:</strong> When collapsed, click on menu items with submenus</li>
                                        <li><strong>Dropdown Appears:</strong> A dropdown should appear next to the menu item</li>
                                        <li><strong>Click Outside:</strong> Click outside to close the dropdown</li>
                                        <li><strong>Expand Again:</strong> Click toggle to expand sidebar back to normal</li>
                                    </ol>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Test Controls -->
                <div class="row">
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="card-title mb-0">
                                    <i class="fas fa-gamepad me-2"></i>Test Controls
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="d-grid gap-2">
                                    <button class="btn btn-primary" onclick="window.modernSidebar?.collapse()">
                                        <i class="fas fa-compress me-1"></i>Collapse Sidebar
                                    </button>
                                    <button class="btn btn-success" onclick="window.modernSidebar?.expand()">
                                        <i class="fas fa-expand me-1"></i>Expand Sidebar
                                    </button>
                                    <button class="btn btn-warning" onclick="window.modernSidebar?.toggle()">
                                        <i class="fas fa-arrows-alt-h me-1"></i>Toggle Sidebar
                                    </button>
                                    <button class="btn btn-info" onclick="toggleTheme()">
                                        <i class="fas fa-palette me-1"></i>Toggle Dark Mode
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="card-title mb-0">
                                    <i class="fas fa-check-circle me-2"></i>Expected Behavior
                                </h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled mb-0">
                                    <li><i class="fas fa-check text-success me-2"></i>Sidebar collapses to 70px width</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Menu text and arrows hide</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Clicking submenu shows dropdown</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Dropdown has header with menu title</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Dropdown items are clickable</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Active item is highlighted</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Clicking outside closes dropdown</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Tooltips show on hover</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Status Display -->
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="card-title mb-0">
                                    <i class="fas fa-info me-2"></i>Current Status
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <strong>Sidebar State:</strong>
                                        <span id="sidebarState" class="badge bg-success">Expanded</span>
                                    </div>
                                    <div class="col-md-3">
                                        <strong>Current Page:</strong>
                                        <span class="badge bg-primary"><?= $currentPage ?></span>
                                    </div>
                                    <div class="col-md-3">
                                        <strong>Active Menu:</strong>
                                        <span class="badge bg-info">Keuangan > Transaksi</span>
                                    </div>
                                    <div class="col-md-3">
                                        <strong>Theme:</strong>
                                        <span id="currentTheme" class="badge bg-secondary">Light</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/modern-sidebar.js?v=<?= time() ?>"></script>
    <script src="assets/js/theme-manager.js?v=<?= time() ?>"></script>

    <script>
        // Update status display
        function updateStatus() {
            const isCollapsed = document.body.classList.contains('sidebar-collapsed');
            const sidebarState = document.getElementById('sidebarState');
            const currentTheme = document.getElementById('currentTheme');
            
            if (sidebarState) {
                sidebarState.textContent = isCollapsed ? 'Collapsed' : 'Expanded';
                sidebarState.className = isCollapsed ? 'badge bg-warning' : 'badge bg-success';
            }
            
            if (currentTheme) {
                const theme = document.documentElement.getAttribute('data-bs-theme') || 'light';
                currentTheme.textContent = theme.charAt(0).toUpperCase() + theme.slice(1);
                currentTheme.className = theme === 'dark' ? 'badge bg-dark' : 'badge bg-secondary';
            }
        }

        // Update status on load and theme change
        document.addEventListener('DOMContentLoaded', updateStatus);
        document.addEventListener('themeChanged', updateStatus);
        
        // Monitor sidebar state changes
        const observer = new MutationObserver(updateStatus);
        observer.observe(document.body, { 
            attributes: true, 
            attributeFilter: ['class'] 
        });

        // Initialize tooltips
        document.addEventListener('DOMContentLoaded', function() {
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        });
    </script>
</body>
</html>
