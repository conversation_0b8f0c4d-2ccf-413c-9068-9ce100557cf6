<?php
require_once 'includes/config/database.php';
require_once 'includes/helpers/function_check.php';
require_once 'includes/helpers/functions.php';
require_once 'includes/helpers/backup_system.php';

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in and is admin
if (!isLoggedIn()) {
    setFlashMessage('danger', 'Silakan login terlebih dahulu');
    redirect('login.php');
}

$currentUser = getCurrentUser();
if ($currentUser['role'] !== 'admin') {
    setFlashMessage('danger', 'Akses ditolak. Hanya admin yang dapat mengakses halaman ini.');
    redirect('dashboard.php');
}

$currentPage = 'backup_management';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'create_backup':
                $result = createDatabaseBackup();
                if ($result['success']) {
                    setFlashMessage('success', $result['message']);
                } else {
                    setFlashMessage('danger', $result['message']);
                }
                break;
                
            case 'restore_backup':
                if (!empty($_POST['filename'])) {
                    $result = restoreDatabaseBackup($_POST['filename']);
                    if ($result['success']) {
                        setFlashMessage('success', $result['message']);
                    } else {
                        setFlashMessage('danger', $result['message']);
                    }
                }
                break;
                
            case 'delete_backup':
                if (!empty($_POST['filename'])) {
                    $result = deleteBackup($_POST['filename']);
                    if ($result['success']) {
                        setFlashMessage('success', $result['message']);
                    } else {
                        setFlashMessage('danger', $result['message']);
                    }
                }
                break;
                
            case 'clean_old_backups':
                $keepCount = (int)($_POST['keep_count'] ?? 10);
                $result = cleanOldBackups($keepCount);
                if ($result['success']) {
                    setFlashMessage('success', $result['message']);
                } else {
                    setFlashMessage('danger', $result['message']);
                }
                break;
        }
        redirect('backup_management.php');
    }
}

// Get backup list
$backups = getBackupList();

// Include header and sidebar
require_once 'includes/views/layouts/header.php';
require_once 'includes/views/layouts/sidebar.php';
?>

<div class="modern-layout">
    <div class="modern-container">
        <!-- Modern Page Header -->
        <div class="modern-page-header modern-flex modern-justify-between modern-items-center modern-mb-xl">
            <div>
                <h1 class="modern-page-title modern-mb-0">Backup Management</h1>
                <p class="modern-page-subtitle">Kelola backup database sistem dengan aman dan mudah</p>
            </div>
            <div class="modern-page-actions">
                <button type="button" class="modern-btn modern-btn-success" onclick="createBackup()">
                    <i class="fas fa-plus"></i>
                    Buat Backup Baru
                </button>
            </div>
        </div>

        <!-- Flash Messages -->
        <?php if ($flash = getFlashMessage()): ?>
        <div class="modern-alert modern-alert-<?= $flash['type'] ?> modern-mb-lg">
            <div class="modern-alert-content">
                <i class="fas fa-<?= $flash['type'] === 'success' ? 'check-circle' : 'exclamation-triangle' ?> modern-alert-icon"></i>
                <div class="modern-alert-message"><?= $flash['message'] ?></div>
            </div>
            <button type="button" class="modern-alert-close" onclick="this.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <?php endif; ?>

        <!-- Backup Statistics -->
        <div class="modern-grid modern-grid-cols-4 modern-gap-lg modern-mb-xl">
            <div class="modern-stats-card modern-stats-primary">
                <div class="modern-stats-content">
                    <div class="modern-stats-info">
                        <div class="modern-stats-label">Total Backup</div>
                        <div class="modern-stats-value"><?= count($backups) ?></div>
                        <div class="modern-stats-meta">File backup</div>
                    </div>
                    <div class="modern-stats-icon">
                        <i class="fas fa-database"></i>
                    </div>
                </div>
                <div class="modern-stats-glow"></div>
            </div>

            <div class="modern-stats-card modern-stats-success">
                <div class="modern-stats-content">
                    <div class="modern-stats-info">
                        <div class="modern-stats-label">Backup Terbaru</div>
                        <div class="modern-stats-value">
                            <?= !empty($backups) ? date('d/m', $backups[0]['created']) : 'N/A' ?>
                        </div>
                        <div class="modern-stats-meta">
                            <?= !empty($backups) ? date('H:i', $backups[0]['created']) : 'Belum ada' ?>
                        </div>
                    </div>
                    <div class="modern-stats-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                </div>
                <div class="modern-stats-glow"></div>
            </div>

            <div class="modern-stats-card modern-stats-info">
                <div class="modern-stats-content">
                    <div class="modern-stats-info">
                        <div class="modern-stats-label">Total Size</div>
                        <div class="modern-stats-value">
                            <?php
                            $totalSize = 0;
                            foreach ($backups as $backup) {
                                $totalSize += $backup['size'];
                            }
                            echo formatBytes($totalSize);
                            ?>
                        </div>
                        <div class="modern-stats-meta">Semua backup</div>
                    </div>
                    <div class="modern-stats-icon">
                        <i class="fas fa-hdd"></i>
                    </div>
                </div>
                <div class="modern-stats-glow"></div>
            </div>

            <div class="modern-stats-card modern-stats-warning">
                <div class="modern-stats-content">
                    <div class="modern-stats-info">
                        <div class="modern-stats-label">Backup Otomatis</div>
                        <div class="modern-stats-value">
                            <?php
                            $autoBackup = getSystemSetting('auto_backup_enabled', '1');
                            echo $autoBackup ? 'Aktif' : 'Nonaktif';
                            ?>
                        </div>
                        <div class="modern-stats-meta">Status sistem</div>
                    </div>
                    <div class="modern-stats-icon">
                        <i class="fas fa-sync-alt"></i>
                    </div>
                </div>
                <div class="modern-stats-glow"></div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="modern-card modern-mb-lg">
            <div class="modern-card-header">
                <h5 class="modern-card-title">
                    <i class="fas fa-bolt modern-text-primary modern-mr-sm"></i>
                    Quick Actions
                </h5>
            </div>
            <div class="modern-card-body">
                <div class="modern-grid modern-grid-cols-4 modern-gap-sm">
                    <button type="button" class="modern-btn modern-btn-primary" onclick="createBackup()">
                        <i class="fas fa-plus"></i>
                        Buat Backup
                    </button>
                    <button type="button" class="modern-btn modern-btn-warning" onclick="scheduleBackup()">
                        <i class="fas fa-clock"></i>
                        Jadwal Backup
                    </button>
                    <button type="button" class="modern-btn modern-btn-danger" onclick="cleanOldBackups()">
                        <i class="fas fa-trash"></i>
                        Bersihkan Lama
                    </button>
                    <button type="button" class="modern-btn modern-btn-info" onclick="downloadBackup()">
                        <i class="fas fa-download"></i>
                        Download
                    </button>
                </div>
            </div>
        </div>

        <!-- Backup List -->
        <div class="modern-card">
            <div class="modern-card-header">
                <h5 class="modern-card-title">
                    <i class="fas fa-list modern-text-primary modern-mr-sm"></i>
                    Daftar Backup
                </h5>
                <div class="modern-card-actions">
                    <span class="modern-badge modern-badge-info">
                        <?= count($backups) ?> backup tersedia
                    </span>
                </div>
            </div>
            <div class="modern-card-body modern-p-0">
                <div class="modern-table-responsive">
                    <table class="modern-table">
                        <thead class="modern-table-header">
                            <tr>
                                <th class="modern-table-th">
                                    <i class="fas fa-file modern-mr-xs"></i>
                                    Filename
                                </th>
                                <th class="modern-table-th">
                                    <i class="fas fa-hdd modern-mr-xs"></i>
                                    Size
                                </th>
                                <th class="modern-table-th">
                                    <i class="fas fa-calendar modern-mr-xs"></i>
                                    Created
                                </th>
                                <th class="modern-table-th modern-text-center">
                                    <i class="fas fa-cogs modern-mr-xs"></i>
                                    Actions
                                </th>
                            </tr>
                        </thead>
                        <tbody class="modern-table-body">
                            <?php if (empty($backups)): ?>
                            <tr>
                                <td colspan="4" class="modern-table-td">
                                    <div class="modern-empty-state">
                                        <div class="modern-empty-icon">
                                            <i class="fas fa-database"></i>
                                        </div>
                                        <div class="modern-empty-content">
                                            <h6 class="modern-empty-title">Belum Ada Backup</h6>
                                            <p class="modern-empty-text">Buat backup pertama untuk melindungi data Anda</p>
                                            <button type="button" class="modern-btn modern-btn-primary modern-btn-sm" onclick="createBackup()">
                                                <i class="fas fa-plus"></i>
                                                Buat Backup Pertama
                                            </button>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <?php else: ?>
                            <?php foreach ($backups as $backup): ?>
                            <tr class="modern-table-row">
                                <td class="modern-table-td">
                                    <div class="modern-table-content">
                                        <div class="modern-table-title">
                                            <i class="fas fa-file-archive modern-text-primary modern-mr-sm"></i>
                                            <?= htmlspecialchars($backup['filename']) ?>
                                        </div>
                                    </div>
                                </td>
                                <td class="modern-table-td">
                                    <span class="modern-badge modern-badge-light">
                                        <?= $backup['size_formatted'] ?>
                                    </span>
                                </td>
                                <td class="modern-table-td">
                                    <div class="modern-table-date">
                                        <i class="fas fa-calendar modern-text-muted modern-mr-xs"></i>
                                        <?= $backup['created_formatted'] ?>
                                    </div>
                                </td>
                                <td class="modern-table-td modern-text-center">
                                    <div class="modern-action-buttons">
                                        <button type="button" class="modern-btn modern-btn-success modern-btn-sm" 
                                                onclick="downloadBackupFile('<?= $backup['filename'] ?>')" title="Download">
                                            <i class="fas fa-download"></i>
                                        </button>
                                        <button type="button" class="modern-btn modern-btn-warning modern-btn-sm" 
                                                onclick="restoreBackup('<?= $backup['filename'] ?>')" title="Restore">
                                            <i class="fas fa-undo"></i>
                                        </button>
                                        <button type="button" class="modern-btn modern-btn-danger modern-btn-sm" 
                                                onclick="deleteBackupFile('<?= $backup['filename'] ?>')" title="Delete">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Hidden Forms -->
<form id="backupForm" method="POST" style="display: none;">
    <input type="hidden" name="action" id="backupAction">
    <input type="hidden" name="filename" id="backupFilename">
    <input type="hidden" name="keep_count" id="keepCount">
</form>

<?php include 'includes/views/layouts/footer.php'; ?>

<script>
function createBackup() {
    if (confirm('Buat backup database sekarang?')) {
        document.getElementById('backupAction').value = 'create_backup';
        document.getElementById('backupForm').submit();
    }
}

function restoreBackup(filename) {
    if (confirm('PERINGATAN: Restore akan mengganti semua data saat ini dengan data dari backup. Lanjutkan?')) {
        document.getElementById('backupAction').value = 'restore_backup';
        document.getElementById('backupFilename').value = filename;
        document.getElementById('backupForm').submit();
    }
}

function deleteBackupFile(filename) {
    if (confirm('Hapus backup file: ' + filename + '?')) {
        document.getElementById('backupAction').value = 'delete_backup';
        document.getElementById('backupFilename').value = filename;
        document.getElementById('backupForm').submit();
    }
}

function cleanOldBackups() {
    const keepCount = prompt('Berapa backup yang ingin disimpan?', '10');
    if (keepCount && !isNaN(keepCount)) {
        document.getElementById('backupAction').value = 'clean_old_backups';
        document.getElementById('keepCount').value = keepCount;
        document.getElementById('backupForm').submit();
    }
}

function downloadBackupFile(filename) {
    window.open('backups/' + filename, '_blank');
}

function scheduleBackup() {
    alert('Fitur jadwal backup otomatis akan segera tersedia!');
}

function downloadBackup() {
    alert('Pilih backup yang ingin didownload dari tabel di bawah.');
}
</script>
