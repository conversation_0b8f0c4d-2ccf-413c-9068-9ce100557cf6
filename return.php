<?php
require_once 'includes/config/database.php';
require_once 'includes/helpers/functions.php';
require_once 'includes/helpers/notifications.php';

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isLoggedIn()) {
    setFlashMessage('danger', 'Silakan login terlebih dahulu');
    redirect('/keuangan/login.php');
}

$currentUser = getCurrentUser();
$currentPage = 'return';

// Create return tables if not exists
try {
    $pdo->exec("CREATE TABLE IF NOT EXISTS returns (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        nomor_return VARCHAR(50) UNIQUE NOT NULL,
        jenis_return ENUM('penjualan', 'pembelian') NOT NULL,
        referensi_id INT,
        referensi_nomor VARCHAR(50),
        tanggal_return DATE NOT NULL,
        total_return DECIMAL(15,2) NOT NULL DEFAULT 0,
        alasan_return TEXT,
        status ENUM('pending', 'disetujui', 'ditolak', 'selesai') DEFAULT 'pending',
        catatan TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    )");

    $pdo->exec("CREATE TABLE IF NOT EXISTS return_items (
        id INT AUTO_INCREMENT PRIMARY KEY,
        return_id INT NOT NULL,
        produk_id INT,
        nama_produk VARCHAR(255) NOT NULL,
        qty_return INT NOT NULL,
        harga_satuan DECIMAL(15,2) NOT NULL,
        subtotal DECIMAL(15,2) NOT NULL,
        kondisi_barang ENUM('baik', 'rusak', 'cacat') DEFAULT 'baik',
        keterangan TEXT,
        FOREIGN KEY (return_id) REFERENCES returns(id) ON DELETE CASCADE
    )");
} catch (PDOException $e) {
    error_log("Error creating return tables: " . $e->getMessage());
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        try {
            switch ($_POST['action']) {
                case 'add':
                    $errors = [];
                    
                    if (empty($_POST['jenis_return'])) {
                        $errors[] = 'Jenis return harus dipilih';
                    }
                    
                    if (empty($_POST['tanggal_return'])) {
                        $errors[] = 'Tanggal return harus diisi';
                    }
                    
                    if (empty($_POST['alasan_return'])) {
                        $errors[] = 'Alasan return harus diisi';
                    }
                    
                    if (empty($errors)) {
                        // Generate nomor return
                        $prefix = $_POST['jenis_return'] === 'penjualan' ? 'RTS' : 'RTB';
                        $nomor_return = $prefix . date('Ymd') . sprintf('%04d', rand(1, 9999));
                        
                        // Format total
                        $total = str_replace(['.', ','], '', $_POST['total_return'] ?? '0');
                        
                        $stmt = $pdo->prepare("INSERT INTO returns (user_id, nomor_return, jenis_return, referensi_nomor, tanggal_return, total_return, alasan_return, status, catatan) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)");
                        
                        $result = $stmt->execute([
                            $currentUser['id'],
                            $nomor_return,
                            $_POST['jenis_return'],
                            $_POST['referensi_nomor'],
                            $_POST['tanggal_return'],
                            $total,
                            $_POST['alasan_return'],
                            $_POST['status'] ?? 'pending',
                            $_POST['catatan']
                        ]);
                        
                        if ($result) {
                            setFlashMessage('success', 'Return berhasil ditambahkan');
                        } else {
                            setFlashMessage('danger', 'Gagal menambahkan return');
                        }
                    } else {
                        setFlashMessage('danger', implode('<br>', $errors));
                    }
                    break;

                case 'update':
                    if (!empty($_POST['id'])) {
                        $total = str_replace(['.', ','], '', $_POST['total_return'] ?? '0');
                        
                        $stmt = $pdo->prepare("UPDATE returns SET jenis_return = ?, referensi_nomor = ?, tanggal_return = ?, total_return = ?, alasan_return = ?, status = ?, catatan = ? WHERE id = ? AND user_id = ?");
                        
                        $result = $stmt->execute([
                            $_POST['jenis_return'],
                            $_POST['referensi_nomor'],
                            $_POST['tanggal_return'],
                            $total,
                            $_POST['alasan_return'],
                            $_POST['status'],
                            $_POST['catatan'],
                            $_POST['id'],
                            $currentUser['id']
                        ]);
                        
                        if ($result) {
                            setFlashMessage('success', 'Return berhasil diperbarui');
                        } else {
                            setFlashMessage('danger', 'Gagal memperbarui return');
                        }
                    }
                    break;

                case 'delete':
                    if (!empty($_POST['id'])) {
                        $stmt = $pdo->prepare("DELETE FROM returns WHERE id = ? AND user_id = ?");
                        $result = $stmt->execute([$_POST['id'], $currentUser['id']]);
                        
                        if ($result) {
                            setFlashMessage('success', 'Return berhasil dihapus');
                        } else {
                            setFlashMessage('danger', 'Gagal menghapus return');
                        }
                    }
                    break;

                case 'approve':
                    if (!empty($_POST['id'])) {
                        $stmt = $pdo->prepare("UPDATE returns SET status = 'disetujui' WHERE id = ? AND user_id = ?");
                        $result = $stmt->execute([$_POST['id'], $currentUser['id']]);
                        
                        if ($result) {
                            setFlashMessage('success', 'Return berhasil disetujui');
                        } else {
                            setFlashMessage('danger', 'Gagal menyetujui return');
                        }
                    }
                    break;

                case 'reject':
                    if (!empty($_POST['id'])) {
                        $stmt = $pdo->prepare("UPDATE returns SET status = 'ditolak' WHERE id = ? AND user_id = ?");
                        $result = $stmt->execute([$_POST['id'], $currentUser['id']]);
                        
                        if ($result) {
                            setFlashMessage('success', 'Return berhasil ditolak');
                        } else {
                            setFlashMessage('danger', 'Gagal menolak return');
                        }
                    }
                    break;
            }
        } catch (PDOException $e) {
            setFlashMessage('danger', 'Terjadi kesalahan. Silakan coba lagi.');
        }
        redirect('/keuangan/return.php');
    }
}

// Get returns with pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$perPage = 10;
$offset = ($page - 1) * $perPage;

$where = ["user_id = ?"];
$params = [$currentUser['id']];

if (!empty($_GET['jenis'])) {
    $where[] = "jenis_return = ?";
    $params[] = $_GET['jenis'];
}

if (!empty($_GET['status'])) {
    $where[] = "status = ?";
    $params[] = $_GET['status'];
}

if (!empty($_GET['start_date'])) {
    $where[] = "tanggal_return >= ?";
    $params[] = $_GET['start_date'];
}

if (!empty($_GET['end_date'])) {
    $where[] = "tanggal_return <= ?";
    $params[] = $_GET['end_date'];
}

$whereClause = implode(" AND ", $where);

// Get total records
$stmt = $pdo->prepare("SELECT COUNT(*) as total FROM returns WHERE $whereClause");
$stmt->execute($params);
$totalRecords = $stmt->fetch()['total'];
$totalPages = ceil($totalRecords / $perPage);

// Get returns
$stmt = $pdo->prepare("
    SELECT * FROM returns 
    WHERE $whereClause 
    ORDER BY tanggal_return DESC, created_at DESC 
    LIMIT ? OFFSET ?
");

$paginationParams = $params;
$paginationParams[] = $perPage;
$paginationParams[] = $offset;

$stmt->execute($paginationParams);
$returns = $stmt->fetchAll();

// Get statistics
$stmt = $pdo->prepare("
    SELECT 
        COUNT(*) as total_returns,
        COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending,
        COUNT(CASE WHEN status = 'disetujui' THEN 1 END) as disetujui,
        COUNT(CASE WHEN status = 'ditolak' THEN 1 END) as ditolak,
        COUNT(CASE WHEN status = 'selesai' THEN 1 END) as selesai,
        SUM(CASE WHEN status = 'disetujui' THEN total_return ELSE 0 END) as total_nilai_disetujui,
        COUNT(CASE WHEN jenis_return = 'penjualan' THEN 1 END) as return_penjualan,
        COUNT(CASE WHEN jenis_return = 'pembelian' THEN 1 END) as return_pembelian
    FROM returns 
    WHERE user_id = ?
");
$stmt->execute([$currentUser['id']]);
$stats = $stmt->fetch();

// Include header and sidebar
require_once 'includes/views/layouts/header.php';
require_once 'includes/views/layouts/sidebar.php';
?>

<div class="modern-layout">
    <div class="modern-container">
        <!-- Modern Page Header -->
        <div class="modern-page-header modern-flex modern-justify-between modern-items-center modern-mb-xl">
            <div>
                <h1 class="modern-page-title modern-mb-0">Return Management</h1>
                <p class="modern-page-subtitle">Kelola retur penjualan dan pembelian dengan sistem modern</p>
            </div>
            <div class="modern-page-actions">
                <button type="button" class="modern-btn modern-btn-primary" data-bs-toggle="modal" data-bs-target="#addReturnModal">
                    <i class="fas fa-plus"></i>
                    Tambah Return
                </button>
            </div>
        </div>

        <!-- Flash Messages -->
        <?php if ($flash = getFlashMessage()): ?>
        <div class="modern-alert modern-alert-<?= $flash['type'] ?> modern-mb-lg">
            <div class="modern-alert-content">
                <i class="fas fa-<?= $flash['type'] === 'success' ? 'check-circle' : 'exclamation-triangle' ?> modern-alert-icon"></i>
                <div class="modern-alert-message"><?= $flash['message'] ?></div>
            </div>
            <button type="button" class="modern-alert-close" onclick="this.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <?php endif; ?>

        <!-- Modern Statistics Cards -->
        <div class="modern-grid modern-grid-cols-4 modern-gap-lg modern-mb-xl">
            <div class="modern-stats-card modern-stats-primary">
                <div class="modern-stats-content">
                    <div class="modern-stats-info">
                        <div class="modern-stats-label">Total Returns</div>
                        <div class="modern-stats-value"><?= number_format($stats['total_returns'] ?? 0) ?></div>
                        <div class="modern-stats-meta">Semua retur</div>
                    </div>
                    <div class="modern-stats-icon">
                        <i class="fas fa-undo"></i>
                    </div>
                </div>
                <div class="modern-stats-glow"></div>
            </div>

            <div class="modern-stats-card modern-stats-warning">
                <div class="modern-stats-content">
                    <div class="modern-stats-info">
                        <div class="modern-stats-label">Pending</div>
                        <div class="modern-stats-value"><?= number_format($stats['pending'] ?? 0) ?></div>
                        <div class="modern-stats-meta">⏳ Menunggu persetujuan</div>
                    </div>
                    <div class="modern-stats-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                </div>
                <div class="modern-stats-glow"></div>
            </div>

            <div class="modern-stats-card modern-stats-success">
                <div class="modern-stats-content">
                    <div class="modern-stats-info">
                        <div class="modern-stats-label">Disetujui</div>
                        <div class="modern-stats-value"><?= number_format($stats['disetujui'] ?? 0) ?></div>
                        <div class="modern-stats-meta">✅ <?= formatRupiahShort($stats['total_nilai_disetujui'] ?? 0) ?></div>
                    </div>
                    <div class="modern-stats-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                </div>
                <div class="modern-stats-glow"></div>
            </div>

            <div class="modern-stats-card modern-stats-info">
                <div class="modern-stats-content">
                    <div class="modern-stats-info">
                        <div class="modern-stats-label">Return Ratio</div>
                        <div class="modern-stats-value">
                            <?php
                            $totalSales = $stats['return_penjualan'] ?? 0;
                            $totalPurchases = $stats['return_pembelian'] ?? 0;
                            $ratio = ($totalSales + $totalPurchases) > 0 ? round(($totalSales / ($totalSales + $totalPurchases)) * 100, 1) : 0;
                            echo $ratio . '%';
                            ?>
                        </div>
                        <div class="modern-stats-meta">📊 Penjualan vs Pembelian</div>
                    </div>
                    <div class="modern-stats-icon">
                        <i class="fas fa-chart-pie"></i>
                    </div>
                </div>
                <div class="modern-stats-glow"></div>
            </div>
        </div>

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="row g-2">
                        <div class="col-md-2">
                            <a href="?status=pending" class="btn btn-outline-warning w-100">
                                <i class="fas fa-clock me-2"></i>Pending
                            </a>
                        </div>
                        <div class="col-md-2">
                            <a href="?jenis=penjualan" class="btn btn-outline-primary w-100">
                                <i class="fas fa-shopping-cart me-2"></i>Return Penjualan
                            </a>
                        </div>
                        <div class="col-md-2">
                            <a href="?jenis=pembelian" class="btn btn-outline-success w-100">
                                <i class="fas fa-shopping-basket me-2"></i>Return Pembelian
                            </a>
                        </div>
                        <div class="col-md-2">
                            <button type="button" class="btn btn-outline-info w-100" onclick="exportReturns()">
                                <i class="fas fa-download me-2"></i>Export
                            </button>
                        </div>
                        <div class="col-md-2">
                            <button type="button" class="btn btn-outline-secondary w-100" onclick="printReturns()">
                                <i class="fas fa-print me-2"></i>Print
                            </button>
                        </div>
                        <div class="col-md-2">
                            <a href="/keuangan/return.php" class="btn btn-outline-dark w-100">
                                <i class="fas fa-sync me-2"></i>Refresh
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filter Section -->
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-header bg-light border-0">
            <h6 class="mb-0"><i class="fas fa-filter me-2"></i>Filter Return</h6>
        </div>
        <div class="card-body">
            <form action="" method="GET" class="row g-3">
                <div class="col-md-3">
                    <label class="form-label">Tanggal Mulai</label>
                    <input type="date" name="start_date" class="form-control" value="<?= $_GET['start_date'] ?? '' ?>">
                </div>
                <div class="col-md-3">
                    <label class="form-label">Tanggal Akhir</label>
                    <input type="date" name="end_date" class="form-control" value="<?= $_GET['end_date'] ?? '' ?>">
                </div>
                <div class="col-md-3">
                    <label class="form-label">Jenis Return</label>
                    <select name="jenis" class="form-select">
                        <option value="">Semua Jenis</option>
                        <option value="penjualan" <?= (isset($_GET['jenis']) && $_GET['jenis'] == 'penjualan') ? 'selected' : '' ?>>Return Penjualan</option>
                        <option value="pembelian" <?= (isset($_GET['jenis']) && $_GET['jenis'] == 'pembelian') ? 'selected' : '' ?>>Return Pembelian</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">Status</label>
                    <select name="status" class="form-select">
                        <option value="">Semua Status</option>
                        <option value="pending" <?= (isset($_GET['status']) && $_GET['status'] == 'pending') ? 'selected' : '' ?>>Pending</option>
                        <option value="disetujui" <?= (isset($_GET['status']) && $_GET['status'] == 'disetujui') ? 'selected' : '' ?>>Disetujui</option>
                        <option value="ditolak" <?= (isset($_GET['status']) && $_GET['status'] == 'ditolak') ? 'selected' : '' ?>>Ditolak</option>
                        <option value="selesai" <?= (isset($_GET['status']) && $_GET['status'] == 'selesai') ? 'selected' : '' ?>>Selesai</option>
                    </select>
                </div>
                <div class="col-12 text-end">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-filter me-2"></i>Filter
                    </button>
                    <a href="/keuangan/return.php" class="btn btn-light">
                        <i class="fas fa-sync me-2"></i>Reset
                    </a>
                </div>
            </form>
        </div>
    </div>

        <!-- Modern Returns Table -->
        <div class="modern-card">
            <div class="modern-card-header">
                <h5 class="modern-card-title">
                    <i class="fas fa-list modern-text-primary modern-mr-sm"></i>
                    Daftar Return
                </h5>
                <div class="modern-card-actions">
                    <span class="modern-badge modern-badge-info">
                        Total: <?= count($returns) ?> return
                    </span>
                </div>
            </div>
            <div class="modern-card-body modern-p-0">
                <div class="modern-table-responsive">
                    <table class="modern-table">
                        <thead class="modern-table-header">
                            <tr>
                                <th class="modern-table-th">
                                    <i class="fas fa-barcode modern-mr-xs"></i>
                                    Nomor Return
                                </th>
                                <th class="modern-table-th">
                                    <i class="fas fa-tags modern-mr-xs"></i>
                                    Jenis & Referensi
                                </th>
                                <th class="modern-table-th">
                                    <i class="fas fa-calendar modern-mr-xs"></i>
                                    Tanggal
                                </th>
                                <th class="modern-table-th modern-text-end">
                                    <i class="fas fa-money-bill modern-mr-xs"></i>
                                    Total
                                </th>
                                <th class="modern-table-th">
                                    <i class="fas fa-comment modern-mr-xs"></i>
                                    Alasan
                                </th>
                                <th class="modern-table-th modern-text-center">
                                    <i class="fas fa-toggle-on modern-mr-xs"></i>
                                    Status
                                </th>
                                <th class="modern-table-th modern-text-center">
                                    <i class="fas fa-cogs modern-mr-xs"></i>
                                    Aksi
                                </th>
                            </tr>
                        </thead>
                        <tbody class="modern-table-body">
                            <?php if (empty($returns)): ?>
                            <tr>
                                <td colspan="7" class="modern-table-td">
                                    <div class="modern-empty-state">
                                        <div class="modern-empty-icon">
                                            <i class="fas fa-undo"></i>
                                        </div>
                                        <div class="modern-empty-content">
                                            <h6 class="modern-empty-title">Belum Ada Data Return</h6>
                                            <p class="modern-empty-text">Return akan muncul di sini setelah Anda menambahkan data</p>
                                            <button type="button" class="modern-btn modern-btn-primary modern-btn-sm" data-bs-toggle="modal" data-bs-target="#addReturnModal">
                                                <i class="fas fa-plus"></i>
                                                Tambah Return Pertama
                                            </button>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <?php else: ?>
                            <?php foreach ($returns as $return): ?>
                            <tr class="modern-table-row">
                                <td class="modern-table-td">
                                    <div class="modern-return-item">
                                        <div class="modern-return-icon modern-return-<?= $return['jenis_return'] ?>">
                                            <i class="fas fa-<?= $return['jenis_return'] === 'penjualan' ? 'shopping-cart' : 'shopping-basket' ?>"></i>
                                        </div>
                                        <div class="modern-return-info">
                                            <div class="modern-return-number"><?= htmlspecialchars($return['nomor_return']) ?></div>
                                            <div class="modern-return-date">
                                                <i class="fas fa-clock"></i>
                                                <?= formatDate($return['created_at']) ?>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                            <td>
                                <span class="badge bg-<?= $return['jenis_return'] === 'penjualan' ? 'primary' : 'success' ?> mb-1">
                                    <?= ucfirst($return['jenis_return']) ?>
                                </span>
                                <?php if ($return['referensi_nomor']): ?>
                                    <br><small class="text-muted">Ref: <?= htmlspecialchars($return['referensi_nomor']) ?></small>
                                <?php endif; ?>
                            </td>
                            <td>
                                <div class="fw-bold"><?= formatTanggal($return['tanggal_return']) ?></div>
                                <small class="text-muted"><?= date('H:i', strtotime($return['created_at'])) ?></small>
                            </td>
                            <td class="text-end">
                                <span class="fw-bold text-danger"><?= formatRupiah($return['total_return']) ?></span>
                            </td>
                            <td>
                                <div class="text-truncate" style="max-width: 200px;" title="<?= htmlspecialchars($return['alasan_return']) ?>">
                                    <?= htmlspecialchars($return['alasan_return']) ?>
                                </div>
                            </td>
                            <td class="text-center">
                                <span class="badge bg-<?=
                                    $return['status'] === 'pending' ? 'warning' :
                                    ($return['status'] === 'disetujui' ? 'success' :
                                    ($return['status'] === 'ditolak' ? 'danger' : 'info'))
                                ?>">
                                    <?= ucfirst($return['status']) ?>
                                </span>
                            </td>
                            <td class="text-center">
                                <div class="btn-group btn-group-sm">
                                    <button type="button" class="btn btn-outline-info" onclick="viewReturn(<?= $return['id'] ?>)" title="Lihat Detail">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button type="button" class="btn btn-outline-primary" onclick="editReturn(<?= htmlspecialchars(json_encode($return)) ?>)" title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <?php if ($return['status'] === 'pending'): ?>
                                    <button type="button" class="btn btn-outline-success" onclick="approveReturn(<?= $return['id'] ?>)" title="Setujui">
                                        <i class="fas fa-check"></i>
                                    </button>
                                    <button type="button" class="btn btn-outline-danger" onclick="rejectReturn(<?= $return['id'] ?>)" title="Tolak">
                                        <i class="fas fa-times"></i>
                                    </button>
                                    <?php endif; ?>
                                    <button type="button" class="btn btn-outline-danger" onclick="deleteReturn(<?= $return['id'] ?>)" title="Hapus">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <?php if ($totalPages > 1): ?>
            <div class="border-top p-3">
                <nav>
                    <ul class="pagination justify-content-center mb-0">
                        <?php if ($page > 1): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?= $page - 1 ?><?= http_build_query(array_filter($_GET)) ?>">
                                <i class="fas fa-chevron-left"></i>
                            </a>
                        </li>
                        <?php endif; ?>

                        <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                        <li class="page-item <?= $i === $page ? 'active' : '' ?>">
                            <a class="page-link" href="?page=<?= $i ?><?= http_build_query(array_filter($_GET)) ?>"><?= $i ?></a>
                        </li>
                        <?php endfor; ?>

                        <?php if ($page < $totalPages): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?= $page + 1 ?><?= http_build_query(array_filter($_GET)) ?>">
                                <i class="fas fa-chevron-right"></i>
                            </a>
                        </li>
                        <?php endif; ?>
                    </ul>
                </nav>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Add Return Modal -->
<div class="modal fade" id="addReturnModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header border-0">
                <h5 class="modal-title">Tambah Return</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="" method="POST" class="needs-validation" novalidate>
                <div class="modal-body">
                    <input type="hidden" name="action" value="add">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Jenis Return</label>
                                <select name="jenis_return" class="form-select" required>
                                    <option value="">Pilih Jenis</option>
                                    <option value="penjualan">Return Penjualan</option>
                                    <option value="pembelian">Return Pembelian</option>
                                </select>
                                <div class="invalid-feedback">Jenis return harus dipilih</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Tanggal Return</label>
                                <input type="date" name="tanggal_return" class="form-control" required value="<?= date('Y-m-d') ?>">
                                <div class="invalid-feedback">Tanggal return harus diisi</div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Nomor Referensi</label>
                                <input type="text" name="referensi_nomor" class="form-control" placeholder="Nomor invoice/PO">
                                <small class="text-muted">Nomor invoice penjualan atau PO pembelian</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Total Return</label>
                                <div class="input-group">
                                    <span class="input-group-text">Rp</span>
                                    <input type="text" name="total_return" class="form-control number-format" placeholder="0">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Alasan Return</label>
                        <textarea name="alasan_return" class="form-control" rows="3" required placeholder="Jelaskan alasan return..."></textarea>
                        <div class="invalid-feedback">Alasan return harus diisi</div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Status</label>
                                <select name="status" class="form-select">
                                    <option value="pending">Pending</option>
                                    <option value="disetujui">Disetujui</option>
                                    <option value="ditolak">Ditolak</option>
                                    <option value="selesai">Selesai</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Catatan</label>
                        <textarea name="catatan" class="form-control" rows="2" placeholder="Catatan tambahan (opsional)"></textarea>
                    </div>
                </div>
                <div class="modal-footer border-0">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">Simpan Return</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Return Modal -->
<div class="modal fade" id="editReturnModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header border-0">
                <h5 class="modal-title">Edit Return</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="" method="POST" class="needs-validation" novalidate>
                <div class="modal-body">
                    <input type="hidden" name="action" value="update">
                    <input type="hidden" name="id" id="edit_id">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Jenis Return</label>
                                <select name="jenis_return" id="edit_jenis_return" class="form-select" required>
                                    <option value="penjualan">Return Penjualan</option>
                                    <option value="pembelian">Return Pembelian</option>
                                </select>
                                <div class="invalid-feedback">Jenis return harus dipilih</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Tanggal Return</label>
                                <input type="date" name="tanggal_return" id="edit_tanggal_return" class="form-control" required>
                                <div class="invalid-feedback">Tanggal return harus diisi</div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Nomor Referensi</label>
                                <input type="text" name="referensi_nomor" id="edit_referensi_nomor" class="form-control">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Total Return</label>
                                <div class="input-group">
                                    <span class="input-group-text">Rp</span>
                                    <input type="text" name="total_return" id="edit_total_return" class="form-control number-format">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Alasan Return</label>
                        <textarea name="alasan_return" id="edit_alasan_return" class="form-control" rows="3" required></textarea>
                        <div class="invalid-feedback">Alasan return harus diisi</div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Status</label>
                                <select name="status" id="edit_status" class="form-select">
                                    <option value="pending">Pending</option>
                                    <option value="disetujui">Disetujui</option>
                                    <option value="ditolak">Ditolak</option>
                                    <option value="selesai">Selesai</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Catatan</label>
                        <textarea name="catatan" id="edit_catatan" class="form-control" rows="2"></textarea>
                    </div>
                </div>
                <div class="modal-footer border-0">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">Update Return</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function editReturn(returnData) {
    document.getElementById('edit_id').value = returnData.id;
    document.getElementById('edit_jenis_return').value = returnData.jenis_return;
    document.getElementById('edit_tanggal_return').value = returnData.tanggal_return;
    document.getElementById('edit_referensi_nomor').value = returnData.referensi_nomor || '';
    document.getElementById('edit_total_return').value = returnData.total_return ? formatNumber(returnData.total_return) : '';
    document.getElementById('edit_alasan_return').value = returnData.alasan_return;
    document.getElementById('edit_status').value = returnData.status;
    document.getElementById('edit_catatan').value = returnData.catatan || '';

    const modal = new bootstrap.Modal(document.getElementById('editReturnModal'));
    modal.show();
}

function deleteReturn(id) {
    if (confirm('Apakah Anda yakin ingin menghapus return ini?')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="delete">
            <input type="hidden" name="id" value="${id}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

function approveReturn(id) {
    if (confirm('Apakah Anda yakin ingin menyetujui return ini?')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="approve">
            <input type="hidden" name="id" value="${id}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

function rejectReturn(id) {
    if (confirm('Apakah Anda yakin ingin menolak return ini?')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="reject">
            <input type="hidden" name="id" value="${id}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

function viewReturn(id) {
    // Implement view return details
    alert('Fitur view detail akan segera tersedia');
}

function exportReturns() {
    alert('Fitur export akan segera tersedia');
}

function printReturns() {
    window.print();
}

function formatNumber(num) {
    return new Intl.NumberFormat('id-ID').format(num);
}

// Format number inputs
document.addEventListener('DOMContentLoaded', function() {
    const numberInputs = document.querySelectorAll('.number-format');

    numberInputs.forEach(function(input) {
        input.addEventListener('input', function(e) {
            let value = e.target.value.replace(/[^\d]/g, '');
            if (value) {
                e.target.value = formatNumber(value);
            }
        });
    });
});
</script>

<style>
.bg-gradient-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
}

.bg-gradient-success {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
}

.bg-gradient-warning {
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
}

.bg-gradient-info {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
}

.text-truncate {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
</style>

<?php
// Helper function for short rupiah format
function formatRupiahShort($amount) {
    if ($amount >= 1000000000) {
        return 'Rp ' . number_format($amount / 1000000000, 1) . 'M';
    } elseif ($amount >= 1000000) {
        return 'Rp ' . number_format($amount / 1000000, 1) . 'jt';
    } elseif ($amount >= 1000) {
        return 'Rp ' . number_format($amount / 1000, 0) . 'rb';
    } else {
        return 'Rp ' . number_format($amount, 0);
    }
}

        <!-- Modern Footer -->
        <div class="modern-footer">
            <div class="modern-footer-content">
                <div class="modern-footer-left">
                    <p class="modern-footer-text">
                        © <?= date('Y') ?> Sistem Keuangan Modern.
                        <span class="modern-text-primary">Return Management</span>
                    </p>
                </div>
                <div class="modern-footer-right">
                    <span class="modern-footer-version">v2.0.0</span>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/views/layouts/footer.php'; ?>
