<?php
require_once 'includes/config/database.php';

$step = $_GET['step'] ?? 1;
$results = [];

try {
    switch ($step) {
        case 1:
            // Step 1: Check database connection
            $results[] = "✅ Database connection successful";
            $results[] = "✅ Database name: " . $pdo->query("SELECT DATABASE()")->fetchColumn();
            break;
            
        case 2:
            // Step 2: Check and create users table
            $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
            if ($stmt->rowCount() == 0) {
                $pdo->exec("CREATE TABLE users (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    username VARCHAR(50) UNIQUE NOT NULL,
                    email VARCHAR(100) UNIQUE NOT NULL,
                    password VARCHAR(255) NOT NULL,
                    role ENUM('admin', 'user') DEFAULT 'user',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )");
                $results[] = "✅ Users table created";
                
                // Insert default admin user
                $stmt = $pdo->prepare("INSERT INTO users (username, email, password, role) VALUES (?, ?, ?, ?)");
                $stmt->execute(['admin', '<EMAIL>', password_hash('admin123', PASSWORD_DEFAULT), 'admin']);
                $results[] = "✅ Default admin user created (admin/admin123)";
            } else {
                $results[] = "✅ Users table already exists";
            }
            break;
            
        case 3:
            // Step 3: Create supplier table
            $pdo->exec("DROP TABLE IF EXISTS supplier");
            $results[] = "✅ Dropped existing supplier table";
            
            $pdo->exec("CREATE TABLE supplier (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL DEFAULT 1,
                nama_supplier VARCHAR(255) NOT NULL,
                kontak VARCHAR(100),
                email VARCHAR(255),
                alamat TEXT,
                keterangan TEXT,
                status ENUM('aktif', 'nonaktif') DEFAULT 'aktif',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )");
            $results[] = "✅ Supplier table created successfully";
            break;
            
        case 4:
            // Step 4: Insert sample data
            $stmt = $pdo->prepare("INSERT INTO supplier (user_id, nama_supplier, kontak, email, alamat, keterangan, status) VALUES (?, ?, ?, ?, ?, ?, ?)");
            
            $sampleData = [
                [1, 'PT Supplier Utama', '021-12345678', '<EMAIL>', 'Jakarta', 'Supplier utama', 'aktif'],
                [1, 'CV Mitra Jaya', '081234567890', '<EMAIL>', 'Bandung', 'Supplier mitra', 'aktif'],
                [1, 'Toko Berkah', '022-87654321', '<EMAIL>', 'Surabaya', 'Supplier lokal', 'nonaktif']
            ];
            
            foreach ($sampleData as $data) {
                $stmt->execute($data);
            }
            $results[] = "✅ Sample data inserted (3 suppliers)";
            break;
            
        case 5:
            // Step 5: Test operations
            $stmt = $pdo->query("SELECT COUNT(*) FROM supplier");
            $count = $stmt->fetchColumn();
            $results[] = "✅ SELECT test: $count suppliers found";
            
            $stmt = $pdo->prepare("INSERT INTO supplier (user_id, nama_supplier, status) VALUES (?, ?, ?)");
            $testResult = $stmt->execute([1, 'Test Supplier ' . date('H:i:s'), 'aktif']);
            if ($testResult) {
                $testId = $pdo->lastInsertId();
                $results[] = "✅ INSERT test successful (ID: $testId)";
                
                $stmt = $pdo->prepare("DELETE FROM supplier WHERE id = ?");
                $stmt->execute([$testId]);
                $results[] = "✅ DELETE test successful";
            }
            break;
    }
    
} catch (PDOException $e) {
    $results[] = "❌ Error: " . $e->getMessage();
} catch (Exception $e) {
    $results[] = "❌ General error: " . $e->getMessage();
}

?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Database Fix - KeuanganKu</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">
                            <i class="fas fa-tools me-2"></i>Simple Database Fix - Step <?= $step ?>
                        </h4>
                    </div>
                    <div class="card-body">
                        
                        <!-- Progress -->
                        <div class="progress mb-4">
                            <div class="progress-bar" style="width: <?= ($step / 5) * 100 ?>%">
                                Step <?= $step ?> of 5
                            </div>
                        </div>
                        
                        <!-- Results -->
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>Step <?= $step ?> Results:</h6>
                            <ul class="mb-0">
                                <?php foreach ($results as $result): ?>
                                <li><?= $result ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                        
                        <!-- Navigation -->
                        <div class="text-center">
                            <?php if ($step < 5): ?>
                            <a href="?step=<?= $step + 1 ?>" class="btn btn-primary btn-lg">
                                <i class="fas fa-arrow-right me-2"></i>Next Step (<?= $step + 1 ?>)
                            </a>
                            <?php else: ?>
                            <div class="alert alert-success">
                                <h6><i class="fas fa-check-circle me-2"></i>All Steps Completed!</h6>
                                <p class="mb-0">Database setup is complete. You can now use the supplier features.</p>
                            </div>
                            <a href="/keuangan/supplier.php" class="btn btn-success btn-lg me-2">
                                <i class="fas fa-truck me-2"></i>Test Supplier Page
                            </a>
                            <?php endif; ?>
                            
                            <?php if ($step > 1): ?>
                            <a href="?step=<?= $step - 1 ?>" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Previous
                            </a>
                            <?php endif; ?>
                        </div>
                        
                        <!-- Step Description -->
                        <div class="mt-4">
                            <h6>What this step does:</h6>
                            <?php
                            $descriptions = [
                                1 => "Check database connection and basic settings",
                                2 => "Create users table and default admin user",
                                3 => "Create supplier table with proper structure",
                                4 => "Insert sample supplier data for testing",
                                5 => "Test all database operations (SELECT, INSERT, DELETE)"
                            ];
                            ?>
                            <p class="text-muted"><?= $descriptions[$step] ?></p>
                        </div>
                        
                        <!-- Quick Actions -->
                        <div class="mt-4">
                            <h6>Quick Actions:</h6>
                            <div class="btn-group-vertical w-100">
                                <a href="/keuangan/diagnose_database.php" class="btn btn-outline-info btn-sm">
                                    <i class="fas fa-stethoscope me-2"></i>Full Diagnostics
                                </a>
                                <a href="?step=1" class="btn btn-outline-warning btn-sm">
                                    <i class="fas fa-redo me-2"></i>Restart from Step 1
                                </a>
                                <a href="/keuangan/index.php" class="btn btn-outline-success btn-sm">
                                    <i class="fas fa-home me-2"></i>Go to Dashboard
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
