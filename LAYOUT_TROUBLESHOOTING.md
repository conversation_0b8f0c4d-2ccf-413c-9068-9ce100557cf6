# 🔧 LAYOUT TROUBLESHOOTING GUIDE

## 🚨 **MASALAH: LAYOUT TIDAK BERUBAH**

### **<PERSON>mungkinan Penyebab & Solusi:**

---

## 🔍 **1. VERIFIKASI DATABASE**

### **Check Layout Preferences Table:**
```sql
-- Check if table exists
SHOW TABLES LIKE 'layout_preferences';

-- Check user's layout preferences
SELECT * FROM layout_preferences WHERE user_id = YOUR_USER_ID;

-- Check table structure
DESCRIBE layout_preferences;
```

### **Manual Insert Test:**
```sql
-- Insert test layout preferences
INSERT INTO layout_preferences (user_id, layout_type, color_scheme) 
VALUES (1, 'colorful', 'vibrant') 
ON DUPLICATE KEY UPDATE layout_type = 'colorful', color_scheme = 'vibrant';
```

---

## 🔍 **2. VERIFIKASI CSS GENERATION**

### **Check Generated CSS:**
1. **View Page Source** (Ctrl+U)
2. **Look for Layout CSS** in `<head>` section:
```html
<style>
:root {
  --layout-primary: #ff0080;
  --layout-secondary: #00ff80;
  --layout-radius: 15px;
  --layout-shadow: 0 4px 15px rgba(0,0,0,0.12);
}

/* Colorful Layout */
.sidebar, .modern-sidebar, #sidebar { background: linear-gradient(135deg, #ff6b6b 0%, #4ecdc4 50%, #45b7d1 100%) !important; }
.navbar, .modern-navbar, #mainNavbar { background: linear-gradient(90deg, #ff6b6b 0%, #4ecdc4 50%, #45b7d1 100%) !important; }
</style>
```

### **If CSS Not Generated:**
- Check if `layout_helper.php` is included in `header.php`
- Check if user is logged in
- Check for PHP errors in error log

---

## 🔍 **3. VERIFIKASI CSS SPECIFICITY**

### **CSS Priority Issues:**
Layout CSS menggunakan `!important` untuk override existing styles, tapi mungkin ada CSS lain yang lebih spesifik.

### **Debug CSS in Browser:**
1. **Open Developer Tools** (F12)
2. **Inspect sidebar/navbar element**
3. **Check Computed Styles**
4. **Look for overridden styles**

### **Force CSS Override:**
```javascript
// Paste in browser console to test
document.querySelector('#sidebar').style.setProperty('background', 'linear-gradient(135deg, #ff6b6b 0%, #4ecdc4 100%)', 'important');
document.querySelector('#mainNavbar').style.setProperty('background', 'linear-gradient(90deg, #ff6b6b 0%, #4ecdc4 100%)', 'important');
```

---

## 🔍 **4. VERIFIKASI ELEMENT SELECTORS**

### **Check Element Classes:**
```javascript
// Check if elements exist
console.log('Sidebar:', document.querySelector('.sidebar, .modern-sidebar, #sidebar'));
console.log('Navbar:', document.querySelector('.navbar, .modern-navbar, #mainNavbar'));

// Check element classes
console.log('Body classes:', document.body.className);
```

### **Common Element Issues:**
- **Sidebar**: Should have class `sidebar` or `modern-sidebar` or ID `sidebar`
- **Navbar**: Should have class `navbar` or `modern-navbar` or ID `mainNavbar`

---

## 🔍 **5. CACHE ISSUES**

### **Clear All Caches:**
1. **Browser Cache**: Ctrl+F5 (hard refresh)
2. **PHP OpCache**: Restart web server
3. **CSS Cache**: Add version parameter to CSS

### **Disable Caching for Testing:**
```php
// Add to header.php for testing
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');
```

---

## 🔍 **6. TESTING STEPS**

### **Step 1: Test Layout Helper Directly**
```php
// Create test file: test_layout_helper.php
<?php
require_once 'includes/config/database.php';
require_once 'includes/helpers/functions.php';
require_once 'includes/helpers/layout_helper.php';

$currentUser = getCurrentUser();
if ($currentUser) {
    $layoutPrefs = getUserLayoutPreferences($currentUser['id']);
    echo "<pre>";
    print_r($layoutPrefs);
    echo "</pre>";
    
    echo generateLayoutCSS($layoutPrefs);
}
?>
```

### **Step 2: Test Database Connection**
```php
// Add to test file
try {
    $stmt = $pdo->prepare("SELECT * FROM layout_preferences WHERE user_id = ?");
    $stmt->execute([$currentUser['id']]);
    $result = $stmt->fetch();
    echo "Database result: ";
    var_dump($result);
} catch (Exception $e) {
    echo "Database error: " . $e->getMessage();
}
```

### **Step 3: Test CSS Application**
Use the provided `test_layout.php` file to test different layouts.

---

## 🔍 **7. COMMON FIXES**

### **Fix 1: Recreate Layout Preferences Table**
```sql
DROP TABLE IF EXISTS layout_preferences;
CREATE TABLE layout_preferences (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    layout_type ENUM('classic', 'modern', 'colorful', 'minimal', 'gradient', 'glassmorphism') DEFAULT 'classic',
    sidebar_style ENUM('classic', 'modern', 'floating', 'transparent', 'gradient', 'glassmorphism') DEFAULT 'classic',
    navbar_style ENUM('classic', 'modern', 'floating', 'transparent', 'gradient', 'glassmorphism') DEFAULT 'classic',
    footer_style ENUM('classic', 'modern', 'floating', 'transparent', 'gradient', 'minimal') DEFAULT 'classic',
    main_content_style ENUM('classic', 'modern', 'cards', 'floating', 'gradient', 'glassmorphism') DEFAULT 'classic',
    color_scheme ENUM('default', 'vibrant', 'pastel', 'neon', 'earth', 'ocean', 'sunset', 'forest') DEFAULT 'default',
    border_radius ENUM('none', 'small', 'medium', 'large', 'xl') DEFAULT 'medium',
    shadow_style ENUM('none', 'soft', 'medium', 'strong', 'colored') DEFAULT 'soft',
    animation_style ENUM('none', 'subtle', 'smooth', 'bouncy', 'elastic') DEFAULT 'subtle',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_user (user_id),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
```

### **Fix 2: Force CSS Inclusion**
```php
// Add to header.php after existing theme CSS
<?php
// Force layout CSS inclusion
if (file_exists('includes/helpers/layout_helper.php')) {
    require_once 'includes/helpers/layout_helper.php';
    $currentUser = getCurrentUser();
    if ($currentUser) {
        $layoutPrefs = getUserLayoutPreferences($currentUser['id']);
        echo "<!-- Layout CSS Start -->\n";
        echo generateLayoutCSS($layoutPrefs);
        echo "<!-- Layout CSS End -->\n";
    }
}
?>
```

### **Fix 3: Add Element Classes**
```php
// Ensure sidebar has correct class
// In sidebar.php, change:
<div class="modern-sidebar" id="sidebar">
// To:
<div class="modern-sidebar sidebar" id="sidebar">

// Ensure navbar has correct class  
// In navbar.php, change:
<nav class="modern-navbar navbar navbar-expand-lg" id="mainNavbar">
// To:
<nav class="modern-navbar navbar navbar-expand-lg" id="mainNavbar">
```

---

## 🔍 **8. DEBUGGING TOOLS**

### **Browser Console Commands:**
```javascript
// Check if layout CSS is loaded
console.log(document.querySelector('style[id*="layout"]'));

// Check computed styles
const sidebar = document.querySelector('#sidebar');
if (sidebar) {
    console.log('Sidebar background:', window.getComputedStyle(sidebar).background);
}

// Check body classes
console.log('Body classes:', document.body.className);

// Force layout application
document.body.className += ' layout-colorful color-vibrant';
```

### **PHP Debug Commands:**
```php
// Add to any page for debugging
<?php
if (isset($_GET['debug_layout'])) {
    echo "<pre>";
    echo "Current User: ";
    var_dump(getCurrentUser());
    
    echo "\nLayout Preferences: ";
    var_dump(getUserLayoutPreferences(getCurrentUser()['id']));
    
    echo "\nGenerated CSS: ";
    echo htmlspecialchars(generateLayoutCSS(getUserLayoutPreferences(getCurrentUser()['id'])));
    echo "</pre>";
}
?>
```

---

## 🔍 **9. VERIFICATION CHECKLIST**

### **Database ✅**
- [ ] `layout_preferences` table exists
- [ ] User has layout preferences record
- [ ] Values are correctly stored

### **PHP ✅**
- [ ] `layout_helper.php` exists and is included
- [ ] No PHP errors in error log
- [ ] Functions return expected values

### **CSS ✅**
- [ ] Layout CSS is generated in page source
- [ ] CSS uses `!important` declarations
- [ ] No CSS syntax errors

### **HTML ✅**
- [ ] Sidebar has correct class/ID
- [ ] Navbar has correct class/ID
- [ ] Body has layout classes

### **Browser ✅**
- [ ] Hard refresh performed (Ctrl+F5)
- [ ] No JavaScript errors in console
- [ ] CSS is not being overridden

---

## 🔍 **10. EMERGENCY RESET**

### **If Nothing Works:**
```sql
-- Reset user's layout preferences
DELETE FROM layout_preferences WHERE user_id = YOUR_USER_ID;

-- Insert working test layout
INSERT INTO layout_preferences (user_id, layout_type, color_scheme) 
VALUES (YOUR_USER_ID, 'colorful', 'vibrant');
```

### **Manual CSS Override:**
```html
<!-- Add to header.php as last resort -->
<style>
.sidebar, .modern-sidebar, #sidebar {
    background: linear-gradient(135deg, #ff6b6b 0%, #4ecdc4 50%, #45b7d1 100%) !important;
}
.navbar, .modern-navbar, #mainNavbar {
    background: linear-gradient(90deg, #ff6b6b 0%, #4ecdc4 50%, #45b7d1 100%) !important;
}
</style>
```

---

## 📞 **SUPPORT COMMANDS**

### **Quick Test Commands:**
```bash
# Check if files exist
ls -la includes/helpers/layout_helper.php
ls -la test_layout.php

# Check PHP syntax
php -l includes/helpers/layout_helper.php

# Check database connection
mysql -u username -p -e "USE database_name; SHOW TABLES LIKE 'layout_preferences';"
```

### **Log Debugging:**
```php
// Add to layout_helper.php for debugging
error_log("Layout Helper: User ID = " . $userId);
error_log("Layout Helper: Preferences = " . json_encode($layoutPrefs));
error_log("Layout Helper: Generated CSS length = " . strlen($css));
```

---

**Status: Comprehensive troubleshooting guide created!** 🔧✨

**Use this guide to diagnose and fix layout application issues step by step.**
