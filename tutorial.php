<?php
require_once 'includes/config/database.php';
require_once 'includes/helpers/functions.php';
require_once 'includes/helpers/notifications.php';

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isLoggedIn()) {
    setFlashMessage('danger', 'Silakan login terlebih dahulu');
    redirect('/keuangan/login.php');
}

$currentUser = getCurrentUser();
$currentPage = 'tutorial';

// Tutorial data
$tutorials = [
    [
        'id' => 1,
        'title' => 'Pengenalan KeuanganKu',
        'description' => 'Video pengenalan fitur-fitur utama KeuanganKu',
        'duration' => '5:30',
        'category' => 'Dasar',
        'thumbnail' => 'https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg',
        'video_url' => 'https://www.youtube.com/embed/dQw4w9WgXcQ',
        'topics' => ['Dashboard', 'Navigasi', 'Fitur Utama']
    ],
    [
        'id' => 2,
        'title' => 'Cara Mencatat Transaksi',
        'description' => 'Pelajari cara menambah, mengedit, dan menghapus transaksi',
        'duration' => '8:15',
        'category' => 'Transaksi',
        'thumbnail' => 'https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg',
        'video_url' => 'https://www.youtube.com/embed/dQw4w9WgXcQ',
        'topics' => ['Tambah Transaksi', 'Edit Transaksi', 'Filter Data']
    ],
    [
        'id' => 3,
        'title' => 'Mengelola Kategori',
        'description' => 'Cara membuat dan mengorganisir kategori pemasukan dan pengeluaran',
        'duration' => '6:45',
        'category' => 'Kategori',
        'thumbnail' => 'https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg',
        'video_url' => 'https://www.youtube.com/embed/dQw4w9WgXcQ',
        'topics' => ['Buat Kategori', 'Kategori Pemasukan', 'Kategori Pengeluaran']
    ],
    [
        'id' => 4,
        'title' => 'Membuat Anggaran Efektif',
        'description' => 'Tips dan cara membuat anggaran yang realistis dan mudah diikuti',
        'duration' => '12:20',
        'category' => 'Anggaran',
        'thumbnail' => 'https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg',
        'video_url' => 'https://www.youtube.com/embed/dQw4w9WgXcQ',
        'topics' => ['Perencanaan Anggaran', 'Monitoring', 'Tips Budgeting']
    ],
    [
        'id' => 5,
        'title' => 'Tracking Investasi',
        'description' => 'Cara mencatat dan memantau performa investasi Anda',
        'duration' => '10:30',
        'category' => 'Investasi',
        'thumbnail' => 'https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg',
        'video_url' => 'https://www.youtube.com/embed/dQw4w9WgXcQ',
        'topics' => ['Jenis Investasi', 'Update Nilai', 'Analisis Profit/Loss']
    ],
    [
        'id' => 6,
        'title' => 'Membaca Laporan Keuangan',
        'description' => 'Cara memahami dan menganalisis laporan keuangan',
        'duration' => '15:45',
        'category' => 'Laporan',
        'thumbnail' => 'https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg',
        'video_url' => 'https://www.youtube.com/embed/dQw4w9WgXcQ',
        'topics' => ['Laporan Keuangan', 'Analisis Tren', 'Export Data']
    ]
];

// Include header
include 'includes/views/layouts/header.php';
?>

<div class="container-fluid py-4">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-1">Tutorial Video</h1>
            <p class="text-muted mb-0">Pelajari KeuanganKu melalui video tutorial step-by-step</p>
        </div>
    </div>

    <!-- Filter Tabs -->
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-body">
            <ul class="nav nav-pills justify-content-center" id="categoryTabs">
                <li class="nav-item">
                    <a class="nav-link active" href="#" data-category="all">
                        <i class="fas fa-globe me-2"></i>Semua Tutorial
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#" data-category="Dasar">
                        <i class="fas fa-play-circle me-2"></i>Dasar
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#" data-category="Transaksi">
                        <i class="fas fa-exchange-alt me-2"></i>Transaksi
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#" data-category="Anggaran">
                        <i class="fas fa-chart-pie me-2"></i>Anggaran
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#" data-category="Investasi">
                        <i class="fas fa-chart-line me-2"></i>Investasi
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#" data-category="Laporan">
                        <i class="fas fa-chart-bar me-2"></i>Laporan
                    </a>
                </li>
            </ul>
        </div>
    </div>

    <!-- Tutorial Grid -->
    <div class="row" id="tutorialGrid">
        <?php foreach ($tutorials as $tutorial): ?>
        <div class="col-lg-4 col-md-6 mb-4 tutorial-item" data-category="<?= $tutorial['category'] ?>">
            <div class="card border-0 shadow-sm h-100 tutorial-card">
                <div class="position-relative">
                    <img src="<?= $tutorial['thumbnail'] ?>" class="card-img-top" alt="<?= $tutorial['title'] ?>" style="height: 200px; object-fit: cover;">
                    <div class="position-absolute top-50 start-50 translate-middle">
                        <button class="btn btn-primary btn-lg rounded-circle play-btn" data-video="<?= $tutorial['video_url'] ?>" data-title="<?= htmlspecialchars($tutorial['title']) ?>">
                            <i class="fas fa-play"></i>
                        </button>
                    </div>
                    <div class="position-absolute bottom-0 end-0 m-2">
                        <span class="badge bg-dark bg-opacity-75">
                            <i class="fas fa-clock me-1"></i><?= $tutorial['duration'] ?>
                        </span>
                    </div>
                    <div class="position-absolute top-0 start-0 m-2">
                        <span class="badge bg-primary"><?= $tutorial['category'] ?></span>
                    </div>
                </div>
                <div class="card-body">
                    <h5 class="card-title"><?= $tutorial['title'] ?></h5>
                    <p class="card-text text-muted"><?= $tutorial['description'] ?></p>
                    
                    <div class="mb-3">
                        <small class="text-muted">Yang akan dipelajari:</small>
                        <div class="mt-1">
                            <?php foreach ($tutorial['topics'] as $topic): ?>
                                <span class="badge bg-light text-dark me-1 mb-1"><?= $topic ?></span>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
                <div class="card-footer bg-transparent border-0">
                    <button class="btn btn-outline-primary w-100 play-btn" data-video="<?= $tutorial['video_url'] ?>" data-title="<?= htmlspecialchars($tutorial['title']) ?>">
                        <i class="fas fa-play me-2"></i>Tonton Tutorial
                    </button>
                </div>
            </div>
        </div>
        <?php endforeach; ?>
    </div>

    <!-- Learning Path -->
    <div class="row mt-5">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0"><i class="fas fa-route me-2"></i>Learning Path Rekomendasi</h5>
                </div>
                <div class="card-body">
                    <p class="mb-4">Ikuti urutan tutorial berikut untuk memaksimalkan pembelajaran Anda:</p>
                    
                    <div class="row">
                        <div class="col-md-2 text-center">
                            <div class="learning-step">
                                <div class="step-number bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-2" style="width: 40px; height: 40px;">1</div>
                                <h6>Pengenalan</h6>
                                <small class="text-muted">Mulai dengan dasar-dasar</small>
                            </div>
                        </div>
                        <div class="col-md-2 text-center">
                            <div class="learning-step">
                                <div class="step-number bg-info text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-2" style="width: 40px; height: 40px;">2</div>
                                <h6>Kategori</h6>
                                <small class="text-muted">Setup kategori</small>
                            </div>
                        </div>
                        <div class="col-md-2 text-center">
                            <div class="learning-step">
                                <div class="step-number bg-warning text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-2" style="width: 40px; height: 40px;">3</div>
                                <h6>Transaksi</h6>
                                <small class="text-muted">Catat transaksi</small>
                            </div>
                        </div>
                        <div class="col-md-2 text-center">
                            <div class="learning-step">
                                <div class="step-number bg-danger text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-2" style="width: 40px; height: 40px;">4</div>
                                <h6>Anggaran</h6>
                                <small class="text-muted">Buat anggaran</small>
                            </div>
                        </div>
                        <div class="col-md-2 text-center">
                            <div class="learning-step">
                                <div class="step-number bg-success text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-2" style="width: 40px; height: 40px;">5</div>
                                <h6>Investasi</h6>
                                <small class="text-muted">Track investasi</small>
                            </div>
                        </div>
                        <div class="col-md-2 text-center">
                            <div class="learning-step">
                                <div class="step-number bg-secondary text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-2" style="width: 40px; height: 40px;">6</div>
                                <h6>Laporan</h6>
                                <small class="text-muted">Analisis data</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Video Modal -->
<div class="modal fade" id="videoModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="videoModalTitle">Tutorial Video</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body p-0">
                <div class="ratio ratio-16x9">
                    <iframe id="videoFrame" src="" frameborder="0" allowfullscreen></iframe>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.tutorial-card {
    transition: transform 0.2s ease-in-out;
}

.tutorial-card:hover {
    transform: translateY(-5px);
}

.play-btn {
    transition: all 0.2s ease-in-out;
}

.play-btn:hover {
    transform: scale(1.1);
}

.learning-step {
    position: relative;
    padding: 1rem 0;
}

.learning-step:not(:last-child)::after {
    content: '';
    position: absolute;
    top: 20px;
    right: -50%;
    width: 100%;
    height: 2px;
    background: linear-gradient(to right, #007bff, #6c757d);
    z-index: -1;
}

@media (max-width: 768px) {
    .learning-step:not(:last-child)::after {
        display: none;
    }
}

.nav-pills .nav-link {
    border-radius: 25px;
    margin: 0 5px;
}

.nav-pills .nav-link.active {
    background-color: #007bff;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const categoryTabs = document.querySelectorAll('#categoryTabs .nav-link');
    const tutorialItems = document.querySelectorAll('.tutorial-item');
    const playButtons = document.querySelectorAll('.play-btn');
    const videoModal = new bootstrap.Modal(document.getElementById('videoModal'));
    const videoFrame = document.getElementById('videoFrame');
    const videoModalTitle = document.getElementById('videoModalTitle');

    // Category filter
    categoryTabs.forEach(tab => {
        tab.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Update active tab
            categoryTabs.forEach(t => t.classList.remove('active'));
            this.classList.add('active');
            
            const category = this.dataset.category;
            
            // Filter tutorials
            tutorialItems.forEach(item => {
                if (category === 'all' || item.dataset.category === category) {
                    item.style.display = 'block';
                } else {
                    item.style.display = 'none';
                }
            });
        });
    });

    // Video player
    playButtons.forEach(button => {
        button.addEventListener('click', function() {
            const videoUrl = this.dataset.video;
            const title = this.dataset.title;
            
            videoFrame.src = videoUrl + '?autoplay=1';
            videoModalTitle.textContent = title;
            videoModal.show();
        });
    });

    // Stop video when modal is closed
    document.getElementById('videoModal').addEventListener('hidden.bs.modal', function() {
        videoFrame.src = '';
    });
});
</script>

<?php include 'includes/views/layouts/footer.php'; ?>
