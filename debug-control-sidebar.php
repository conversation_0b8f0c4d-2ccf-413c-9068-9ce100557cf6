<!DOCTYPE html>
<html lang="id" data-bs-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Control Sidebar - KeuanganKu</title>

    <!-- CSS Libraries -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="assets/css/layout-manager.css?v=<?= time() ?>" rel="stylesheet">
    <link href="assets/css/control-sidebar.css?v=<?= time() ?>" rel="stylesheet">
    
    <!-- Initialize Theme Early -->
    <script>
        (function() {
            const savedTheme = localStorage.getItem('theme') || 'light';
            document.documentElement.setAttribute('data-bs-theme', savedTheme);
        })();
    </script>
</head>
<body>
    <!-- Debug Navbar -->
    <nav class="navbar navbar-expand-lg navbar-light bg-light fixed-top">
        <div class="container-fluid">
            <span class="navbar-brand">Debug Control Sidebar</span>
            
            <div class="d-flex">
                <!-- Control Sidebar Toggle Button -->
                <button class="btn btn-outline-primary me-2" type="button" 
                        id="controlSidebarToggleBtn" 
                        data-widget="control-sidebar"
                        onclick="debugOpenControlSidebar()">
                    <i class="fas fa-cogs me-1"></i>Customize Layout
                </button>
                
                <!-- Manual Test Button -->
                <button class="btn btn-success me-2" onclick="manualTest()">
                    <i class="fas fa-play me-1"></i>Manual Test
                </button>
                
                <!-- Dark Mode Toggle -->
                <button class="btn btn-secondary" onclick="toggleTheme()">
                    <i class="fas fa-moon me-1"></i>Dark Mode
                </button>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid" style="margin-top: 80px; padding: 2rem;">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-bug me-2"></i>Control Sidebar Debug Console
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>Debug Instructions:</h6>
                            <ol>
                                <li>Click the <strong>"Customize Layout"</strong> button to test control sidebar</li>
                                <li>Check the debug console below for detailed logs</li>
                                <li>Use "Manual Test" button for direct testing</li>
                                <li>All events and errors will be logged</li>
                            </ol>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <h6>Element Status</h6>
                                <div id="elementStatus" class="bg-light p-3 rounded">
                                    <div>Checking elements...</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6>Function Status</h6>
                                <div id="functionStatus" class="bg-light p-3 rounded">
                                    <div>Checking functions...</div>
                                </div>
                            </div>
                        </div>

                        <div class="mt-4">
                            <h6>Debug Console</h6>
                            <div id="debugConsole" class="bg-dark text-light p-3 rounded" style="height: 300px; overflow-y: auto; font-family: monospace; font-size: 0.875rem;">
                                <div class="text-success">[DEBUG] Console initialized...</div>
                            </div>
                        </div>

                        <div class="mt-3">
                            <button class="btn btn-warning btn-sm" onclick="clearDebugConsole()">
                                <i class="fas fa-trash me-1"></i>Clear Console
                            </button>
                            <button class="btn btn-info btn-sm" onclick="runDiagnostics()">
                                <i class="fas fa-stethoscope me-1"></i>Run Diagnostics
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Control Sidebar -->
    <div class="control-sidebar" id="controlSidebar">
        <div class="control-sidebar-content">
            <div class="control-sidebar-header">
                <h5><i class="fas fa-cogs me-2"></i>Layout Customizer</h5>
                <button class="control-sidebar-close" type="button" onclick="closeControlSidebar()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="control-sidebar-body">
                <div class="control-section">
                    <div class="control-section-title">
                        <i class="fas fa-bolt me-2"></i>Quick Settings
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="darkModeSwitch">
                        <label class="form-check-label" for="darkModeSwitch">Dark Mode</label>
                    </div>
                </div>

                <div class="control-section">
                    <div class="control-section-title">
                        <i class="fas fa-palette me-2"></i>Test Colors
                    </div>
                    <div class="color-variants">
                        <div class="color-option" data-color="primary">
                            <div class="color-preview" style="background: #007bff;"></div>
                            <span>Primary</span>
                        </div>
                        <div class="color-option" data-color="success">
                            <div class="color-preview" style="background: #28a745;"></div>
                            <span>Success</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Control Sidebar Overlay -->
    <div class="control-sidebar-overlay" id="controlSidebarOverlay" onclick="closeControlSidebar()"></div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/layout-manager.js?v=<?= time() ?>"></script>

    <script>
        // Debug console functions
        function debugLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const console = document.getElementById('debugConsole');
            const colorClass = type === 'error' ? 'text-danger' : type === 'warn' ? 'text-warning' : type === 'success' ? 'text-success' : 'text-light';
            
            const div = document.createElement('div');
            div.className = colorClass;
            div.innerHTML = `[${timestamp}] [${type.toUpperCase()}] ${message}`;
            console.appendChild(div);
            console.scrollTop = console.scrollHeight;
        }

        // Override console methods
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;

        console.log = function(...args) {
            originalLog.apply(console, args);
            debugLog(args.join(' '), 'info');
        };

        console.error = function(...args) {
            originalError.apply(console, args);
            debugLog(args.join(' '), 'error');
        };

        console.warn = function(...args) {
            originalWarn.apply(console, args);
            debugLog(args.join(' '), 'warn');
        };

        // Debug functions
        function debugOpenControlSidebar() {
            debugLog('Button clicked: debugOpenControlSidebar()', 'info');
            
            const controlSidebar = document.getElementById('controlSidebar');
            const controlSidebarOverlay = document.getElementById('controlSidebarOverlay');
            
            debugLog(`Elements found - Sidebar: ${!!controlSidebar}, Overlay: ${!!controlSidebarOverlay}`, 'info');
            
            if (controlSidebar && controlSidebarOverlay) {
                debugLog('Opening control sidebar...', 'success');
                controlSidebar.classList.add('open');
                controlSidebarOverlay.classList.add('show');
                document.body.style.overflow = 'hidden';
                debugLog('Control sidebar opened successfully!', 'success');
            } else {
                debugLog('Failed to open control sidebar - elements not found', 'error');
            }
        }

        function closeControlSidebar() {
            debugLog('Closing control sidebar...', 'info');
            
            const controlSidebar = document.getElementById('controlSidebar');
            const controlSidebarOverlay = document.getElementById('controlSidebarOverlay');
            
            if (controlSidebar && controlSidebarOverlay) {
                controlSidebar.classList.remove('open');
                controlSidebarOverlay.classList.remove('show');
                document.body.style.overflow = '';
                debugLog('Control sidebar closed successfully!', 'success');
            }
        }

        function manualTest() {
            debugLog('Running manual test...', 'info');
            debugOpenControlSidebar();
        }

        function clearDebugConsole() {
            document.getElementById('debugConsole').innerHTML = '<div class="text-success">[DEBUG] Console cleared...</div>';
        }

        function runDiagnostics() {
            debugLog('=== RUNNING DIAGNOSTICS ===', 'info');
            
            // Check elements
            const controlSidebar = document.getElementById('controlSidebar');
            const controlSidebarOverlay = document.getElementById('controlSidebarOverlay');
            const toggleButton = document.getElementById('controlSidebarToggleBtn');
            
            debugLog(`Control Sidebar Element: ${controlSidebar ? 'FOUND' : 'NOT FOUND'}`, controlSidebar ? 'success' : 'error');
            debugLog(`Control Sidebar Overlay: ${controlSidebarOverlay ? 'FOUND' : 'NOT FOUND'}`, controlSidebarOverlay ? 'success' : 'error');
            debugLog(`Toggle Button: ${toggleButton ? 'FOUND' : 'NOT FOUND'}`, toggleButton ? 'success' : 'error');
            
            // Check CSS
            const controlSidebarCSS = document.querySelector('link[href*="control-sidebar.css"]');
            debugLog(`Control Sidebar CSS: ${controlSidebarCSS ? 'LOADED' : 'NOT LOADED'}`, controlSidebarCSS ? 'success' : 'error');
            
            // Check functions
            debugLog(`debugOpenControlSidebar function: ${typeof debugOpenControlSidebar === 'function' ? 'AVAILABLE' : 'NOT AVAILABLE'}`, typeof debugOpenControlSidebar === 'function' ? 'success' : 'error');
            debugLog(`toggleTheme function: ${typeof toggleTheme === 'function' ? 'AVAILABLE' : 'NOT AVAILABLE'}`, typeof toggleTheme === 'function' ? 'success' : 'error');
            
            debugLog('=== DIAGNOSTICS COMPLETE ===', 'info');
        }

        function updateStatus() {
            // Update element status
            const controlSidebar = document.getElementById('controlSidebar');
            const controlSidebarOverlay = document.getElementById('controlSidebarOverlay');
            const toggleButton = document.getElementById('controlSidebarToggleBtn');
            
            document.getElementById('elementStatus').innerHTML = `
                <div><strong>Elements Status:</strong></div>
                <div>✅ Control Sidebar: ${controlSidebar ? 'Found' : 'Not Found'}</div>
                <div>✅ Overlay: ${controlSidebarOverlay ? 'Found' : 'Not Found'}</div>
                <div>✅ Toggle Button: ${toggleButton ? 'Found' : 'Not Found'}</div>
            `;
            
            // Update function status
            document.getElementById('functionStatus').innerHTML = `
                <div><strong>Functions Status:</strong></div>
                <div>✅ debugOpenControlSidebar: ${typeof debugOpenControlSidebar === 'function' ? 'Available' : 'Not Available'}</div>
                <div>✅ toggleTheme: ${typeof toggleTheme === 'function' ? 'Available' : 'Not Available'}</div>
                <div>✅ closeControlSidebar: ${typeof closeControlSidebar === 'function' ? 'Available' : 'Not Available'}</div>
            `;
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            debugLog('DOM Content Loaded', 'success');
            updateStatus();
            runDiagnostics();
            
            // Add event listener to toggle button
            const toggleButton = document.getElementById('controlSidebarToggleBtn');
            if (toggleButton) {
                toggleButton.addEventListener('click', function(e) {
                    debugLog('Toggle button clicked via event listener', 'info');
                });
            }
        });
    </script>
</body>
</html>
