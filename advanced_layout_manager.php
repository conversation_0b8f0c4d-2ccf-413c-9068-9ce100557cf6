<?php
require_once 'includes/config/database.php';
require_once 'includes/helpers/functions.php';
require_once 'includes/helpers/layout_helper.php';

// Check if user is logged in
$currentUser = getCurrentUser();
if (!$currentUser) {
    redirect('login.php');
}

$pageTitle = 'Advanced Layout Manager';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['save_layout'])) {
    try {
        // Check if table exists, create if not
        $stmt = $pdo->query("SHOW TABLES LIKE 'layout_preferences'");
        if ($stmt->rowCount() === 0) {
            // Redirect to create table page
            setFlashMessage('warning', 'Layout preferences table not found. Please create it first.');
            redirect('create_layout_table.php');
        }

        $stmt = $pdo->prepare("
            INSERT INTO layout_preferences (user_id, layout_type, sidebar_style, navbar_style, footer_style, main_content_style, color_scheme, border_radius, shadow_style, animation_style)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ON DUPLICATE KEY UPDATE
            layout_type = VALUES(layout_type),
            sidebar_style = VALUES(sidebar_style),
            navbar_style = VALUES(navbar_style),
            footer_style = VALUES(footer_style),
            main_content_style = VALUES(main_content_style),
            color_scheme = VALUES(color_scheme),
            border_radius = VALUES(border_radius),
            shadow_style = VALUES(shadow_style),
            animation_style = VALUES(animation_style),
            updated_at = CURRENT_TIMESTAMP
        ");

        $result = $stmt->execute([
            $currentUser['id'],
            $_POST['layout_type'],
            $_POST['sidebar_style'],
            $_POST['navbar_style'],
            $_POST['footer_style'],
            $_POST['main_content_style'],
            $_POST['color_scheme'],
            $_POST['border_radius'],
            $_POST['shadow_style'],
            $_POST['animation_style']
        ]);

        if ($result) {
            setFlashMessage('success', 'Layout preferences saved successfully!');
        } else {
            setFlashMessage('danger', 'Failed to save layout preferences');
        }
        redirect('advanced_layout_manager.php');
    } catch (Exception $e) {
        setFlashMessage('danger', 'Error: ' . $e->getMessage());
    }
}

// Get current preferences
$currentPrefs = getUserLayoutPreferences($currentUser['id']);

include 'includes/views/layouts/header.php';
?>

<div class="container-fluid px-4 py-3">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h3 class="mb-1">
                        <i class="fas fa-paint-brush me-2 text-primary"></i>
                        Advanced Layout Manager
                    </h3>
                    <p class="text-muted mb-0">Customize your application layout with advanced options</p>
                </div>
                <div class="btn-group">
                    <a href="test_layout.php" class="btn btn-info">
                        <i class="fas fa-flask me-1"></i>Test Layouts
                    </a>
                    <a href="theme_manager.php" class="btn btn-outline-secondary">
                        <i class="fas fa-palette me-1"></i>Theme Manager
                    </a>
                    <a href="/keuangan/dashboard.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i>Dashboard
                    </a>
                </div>
            </div>

            <!-- Current Layout Info -->
            <div class="card mb-4">
                <div class="card-header bg-gradient-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        Current Layout Configuration
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <strong>Layout Type:</strong><br>
                            <span class="badge bg-primary"><?= ucfirst($currentPrefs['layout_type']) ?></span>
                        </div>
                        <div class="col-md-3 mb-3">
                            <strong>Color Scheme:</strong><br>
                            <span class="badge bg-success"><?= ucfirst($currentPrefs['color_scheme']) ?></span>
                        </div>
                        <div class="col-md-3 mb-3">
                            <strong>Border Radius:</strong><br>
                            <span class="badge bg-warning"><?= ucfirst($currentPrefs['border_radius']) ?></span>
                        </div>
                        <div class="col-md-3 mb-3">
                            <strong>Animation:</strong><br>
                            <span class="badge bg-info"><?= ucfirst($currentPrefs['animation_style']) ?></span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Layout Configuration Form -->
            <form method="POST" id="layoutForm">
                <div class="row">
                    <!-- Layout Types -->
                    <div class="col-lg-6 mb-4">
                        <div class="card h-100">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-th-large me-2"></i>
                                    Layout Types
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="layout-options">
                                    <div class="layout-option">
                                        <input type="radio" id="layout_classic" name="layout_type" value="classic" <?= $currentPrefs['layout_type'] === 'classic' ? 'checked' : '' ?>>
                                        <label for="layout_classic">
                                            <i class="fas fa-th-large"></i>
                                            <span>Classic</span>
                                            <small>Traditional corporate design</small>
                                        </label>
                                    </div>
                                    <div class="layout-option">
                                        <input type="radio" id="layout_modern" name="layout_type" value="modern" <?= $currentPrefs['layout_type'] === 'modern' ? 'checked' : '' ?>>
                                        <label for="layout_modern">
                                            <i class="fas fa-layer-group"></i>
                                            <span>Modern</span>
                                            <small>Contemporary with rounded corners</small>
                                        </label>
                                    </div>
                                    <div class="layout-option">
                                        <input type="radio" id="layout_colorful" name="layout_type" value="colorful" <?= $currentPrefs['layout_type'] === 'colorful' ? 'checked' : '' ?>>
                                        <label for="layout_colorful">
                                            <i class="fas fa-rainbow"></i>
                                            <span>Colorful</span>
                                            <small>Vibrant multi-color gradients</small>
                                        </label>
                                    </div>
                                    <div class="layout-option">
                                        <input type="radio" id="layout_minimal" name="layout_type" value="minimal" <?= $currentPrefs['layout_type'] === 'minimal' ? 'checked' : '' ?>>
                                        <label for="layout_minimal">
                                            <i class="fas fa-minus"></i>
                                            <span>Minimal</span>
                                            <small>Clean, minimalist design</small>
                                        </label>
                                    </div>
                                    <div class="layout-option">
                                        <input type="radio" id="layout_gradient" name="layout_type" value="gradient" <?= $currentPrefs['layout_type'] === 'gradient' ? 'checked' : '' ?>>
                                        <label for="layout_gradient">
                                            <i class="fas fa-fill-drip"></i>
                                            <span>Gradient</span>
                                            <small>Smooth gradient transitions</small>
                                        </label>
                                    </div>
                                    <div class="layout-option">
                                        <input type="radio" id="layout_glassmorphism" name="layout_type" value="glassmorphism" <?= $currentPrefs['layout_type'] === 'glassmorphism' ? 'checked' : '' ?>>
                                        <label for="layout_glassmorphism">
                                            <i class="fas fa-gem"></i>
                                            <span>Glassmorphism</span>
                                            <small>Modern glass effect with blur</small>
                                        </label>
                                    </div>
                                    <div class="layout-option">
                                        <input type="radio" id="layout_neon" name="layout_type" value="neon" <?= $currentPrefs['layout_type'] === 'neon' ? 'checked' : '' ?>>
                                        <label for="layout_neon">
                                            <i class="fas fa-bolt"></i>
                                            <span>Neon</span>
                                            <small>Glowing neon effects</small>
                                        </label>
                                    </div>
                                    <div class="layout-option">
                                        <input type="radio" id="layout_corporate" name="layout_type" value="corporate" <?= $currentPrefs['layout_type'] === 'corporate' ? 'checked' : '' ?>>
                                        <label for="layout_corporate">
                                            <i class="fas fa-building"></i>
                                            <span>Corporate</span>
                                            <small>Professional business style</small>
                                        </label>
                                    </div>
                                    <div class="layout-option">
                                        <input type="radio" id="layout_retro" name="layout_type" value="retro" <?= $currentPrefs['layout_type'] === 'retro' ? 'checked' : '' ?>>
                                        <label for="layout_retro">
                                            <i class="fas fa-tv"></i>
                                            <span>Retro</span>
                                            <small>Vintage 80s/90s style</small>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Color Schemes -->
                    <div class="col-lg-6 mb-4">
                        <div class="card h-100">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-palette me-2"></i>
                                    Color Schemes
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="color-schemes">
                                    <div class="color-option">
                                        <input type="radio" id="color_default" name="color_scheme" value="default" <?= $currentPrefs['color_scheme'] === 'default' ? 'checked' : '' ?>>
                                        <label for="color_default">
                                            <div class="color-preview" style="background: linear-gradient(45deg, #007bff, #6c757d);"></div>
                                            <span>Default</span>
                                            <small>Professional blue tones</small>
                                        </label>
                                    </div>
                                    <div class="color-option">
                                        <input type="radio" id="color_vibrant" name="color_scheme" value="vibrant" <?= $currentPrefs['color_scheme'] === 'vibrant' ? 'checked' : '' ?>>
                                        <label for="color_vibrant">
                                            <div class="color-preview" style="background: linear-gradient(45deg, #ff0080, #00ff80, #8000ff);"></div>
                                            <span>Vibrant</span>
                                            <small>Hot pink, neon green, purple</small>
                                        </label>
                                    </div>
                                    <div class="color-option">
                                        <input type="radio" id="color_pastel" name="color_scheme" value="pastel" <?= $currentPrefs['color_scheme'] === 'pastel' ? 'checked' : '' ?>>
                                        <label for="color_pastel">
                                            <div class="color-preview" style="background: linear-gradient(45deg, #ffb3ba, #bae1ff, #baffc9);"></div>
                                            <span>Pastel</span>
                                            <small>Soft pink, light blue, mint</small>
                                        </label>
                                    </div>
                                    <div class="color-option">
                                        <input type="radio" id="color_neon" name="color_scheme" value="neon" <?= $currentPrefs['color_scheme'] === 'neon' ? 'checked' : '' ?>>
                                        <label for="color_neon">
                                            <div class="color-preview" style="background: linear-gradient(45deg, #39ff14, #ff073a, #00ffff);"></div>
                                            <span>Neon</span>
                                            <small>Electric green, bright red, cyan</small>
                                        </label>
                                    </div>
                                    <div class="color-option">
                                        <input type="radio" id="color_earth" name="color_scheme" value="earth" <?= $currentPrefs['color_scheme'] === 'earth' ? 'checked' : '' ?>>
                                        <label for="color_earth">
                                            <div class="color-preview" style="background: linear-gradient(45deg, #8b4513, #228b22, #daa520);"></div>
                                            <span>Earth</span>
                                            <small>Brown, forest green, gold</small>
                                        </label>
                                    </div>
                                    <div class="color-option">
                                        <input type="radio" id="color_ocean" name="color_scheme" value="ocean" <?= $currentPrefs['color_scheme'] === 'ocean' ? 'checked' : '' ?>>
                                        <label for="color_ocean">
                                            <div class="color-preview" style="background: linear-gradient(45deg, #006994, #0099cc, #66ccff);"></div>
                                            <span>Ocean</span>
                                            <small>Deep blue variations</small>
                                        </label>
                                    </div>
                                    <div class="color-option">
                                        <input type="radio" id="color_sunset" name="color_scheme" value="sunset" <?= $currentPrefs['color_scheme'] === 'sunset' ? 'checked' : '' ?>>
                                        <label for="color_sunset">
                                            <div class="color-preview" style="background: linear-gradient(45deg, #ff4500, #ff6347, #ffd700);"></div>
                                            <span>Sunset</span>
                                            <small>Orange red, tomato, gold</small>
                                        </label>
                                    </div>
                                    <div class="color-option">
                                        <input type="radio" id="color_forest" name="color_scheme" value="forest" <?= $currentPrefs['color_scheme'] === 'forest' ? 'checked' : '' ?>>
                                        <label for="color_forest">
                                            <div class="color-preview" style="background: linear-gradient(45deg, #228b22, #32cd32, #90ee90);"></div>
                                            <span>Forest</span>
                                            <small>Green variations</small>
                                        </label>
                                    </div>
                                    <div class="color-option">
                                        <input type="radio" id="color_midnight" name="color_scheme" value="midnight" <?= $currentPrefs['color_scheme'] === 'midnight' ? 'checked' : '' ?>>
                                        <label for="color_midnight">
                                            <div class="color-preview" style="background: linear-gradient(45deg, #2c3e50, #34495e, #1a252f);"></div>
                                            <span>Midnight</span>
                                            <small>Dark elegant tones</small>
                                        </label>
                                    </div>
                                    <div class="color-option">
                                        <input type="radio" id="color_royal" name="color_scheme" value="royal" <?= $currentPrefs['color_scheme'] === 'royal' ? 'checked' : '' ?>>
                                        <label for="color_royal">
                                            <div class="color-preview" style="background: linear-gradient(45deg, #663399, #9966cc, #cc99ff);"></div>
                                            <span>Royal</span>
                                            <small>Purple with gold accents</small>
                                        </label>
                                    </div>
                                    <div class="color-option">
                                        <input type="radio" id="color_cyberpunk" name="color_scheme" value="cyberpunk" <?= $currentPrefs['color_scheme'] === 'cyberpunk' ? 'checked' : '' ?>>
                                        <label for="color_cyberpunk">
                                            <div class="color-preview" style="background: linear-gradient(45deg, #ff00ff, #00ffff, #ffff00);"></div>
                                            <span>Cyberpunk</span>
                                            <small>Futuristic neon colors</small>
                                        </label>
                                    </div>
                                    <div class="color-option">
                                        <input type="radio" id="color_autumn" name="color_scheme" value="autumn" <?= $currentPrefs['color_scheme'] === 'autumn' ? 'checked' : '' ?>>
                                        <label for="color_autumn">
                                            <div class="color-preview" style="background: linear-gradient(45deg, #d2691e, #cd853f, #daa520);"></div>
                                            <span>Autumn</span>
                                            <small>Warm fall colors</small>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Component Styles Row -->
                <div class="row">
                    <!-- Sidebar Styles -->
                    <div class="col-lg-3 mb-4">
                        <div class="card h-100">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-bars me-2"></i>
                                    Sidebar Style
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="component-options">
                                    <div class="component-option">
                                        <input type="radio" id="sidebar_classic" name="sidebar_style" value="classic" <?= $currentPrefs['sidebar_style'] === 'classic' ? 'checked' : '' ?>>
                                        <label for="sidebar_classic">Classic</label>
                                    </div>
                                    <div class="component-option">
                                        <input type="radio" id="sidebar_modern" name="sidebar_style" value="modern" <?= $currentPrefs['sidebar_style'] === 'modern' ? 'checked' : '' ?>>
                                        <label for="sidebar_modern">Modern</label>
                                    </div>
                                    <div class="component-option">
                                        <input type="radio" id="sidebar_floating" name="sidebar_style" value="floating" <?= $currentPrefs['sidebar_style'] === 'floating' ? 'checked' : '' ?>>
                                        <label for="sidebar_floating">Floating</label>
                                    </div>
                                    <div class="component-option">
                                        <input type="radio" id="sidebar_transparent" name="sidebar_style" value="transparent" <?= $currentPrefs['sidebar_style'] === 'transparent' ? 'checked' : '' ?>>
                                        <label for="sidebar_transparent">Transparent</label>
                                    </div>
                                    <div class="component-option">
                                        <input type="radio" id="sidebar_gradient" name="sidebar_style" value="gradient" <?= $currentPrefs['sidebar_style'] === 'gradient' ? 'checked' : '' ?>>
                                        <label for="sidebar_gradient">Gradient</label>
                                    </div>
                                    <div class="component-option">
                                        <input type="radio" id="sidebar_glassmorphism" name="sidebar_style" value="glassmorphism" <?= $currentPrefs['sidebar_style'] === 'glassmorphism' ? 'checked' : '' ?>>
                                        <label for="sidebar_glassmorphism">Glassmorphism</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Navbar Styles -->
                    <div class="col-lg-3 mb-4">
                        <div class="card h-100">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-window-maximize me-2"></i>
                                    Navbar Style
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="component-options">
                                    <div class="component-option">
                                        <input type="radio" id="navbar_classic" name="navbar_style" value="classic" <?= $currentPrefs['navbar_style'] === 'classic' ? 'checked' : '' ?>>
                                        <label for="navbar_classic">Classic</label>
                                    </div>
                                    <div class="component-option">
                                        <input type="radio" id="navbar_modern" name="navbar_style" value="modern" <?= $currentPrefs['navbar_style'] === 'modern' ? 'checked' : '' ?>>
                                        <label for="navbar_modern">Modern</label>
                                    </div>
                                    <div class="component-option">
                                        <input type="radio" id="navbar_floating" name="navbar_style" value="floating" <?= $currentPrefs['navbar_style'] === 'floating' ? 'checked' : '' ?>>
                                        <label for="navbar_floating">Floating</label>
                                    </div>
                                    <div class="component-option">
                                        <input type="radio" id="navbar_transparent" name="navbar_style" value="transparent" <?= $currentPrefs['navbar_style'] === 'transparent' ? 'checked' : '' ?>>
                                        <label for="navbar_transparent">Transparent</label>
                                    </div>
                                    <div class="component-option">
                                        <input type="radio" id="navbar_gradient" name="navbar_style" value="gradient" <?= $currentPrefs['navbar_style'] === 'gradient' ? 'checked' : '' ?>>
                                        <label for="navbar_gradient">Gradient</label>
                                    </div>
                                    <div class="component-option">
                                        <input type="radio" id="navbar_glassmorphism" name="navbar_style" value="glassmorphism" <?= $currentPrefs['navbar_style'] === 'glassmorphism' ? 'checked' : '' ?>>
                                        <label for="navbar_glassmorphism">Glassmorphism</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Footer Styles -->
                    <div class="col-lg-3 mb-4">
                        <div class="card h-100">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-window-minimize me-2"></i>
                                    Footer Style
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="component-options">
                                    <div class="component-option">
                                        <input type="radio" id="footer_classic" name="footer_style" value="classic" <?= $currentPrefs['footer_style'] === 'classic' ? 'checked' : '' ?>>
                                        <label for="footer_classic">Classic</label>
                                    </div>
                                    <div class="component-option">
                                        <input type="radio" id="footer_modern" name="footer_style" value="modern" <?= $currentPrefs['footer_style'] === 'modern' ? 'checked' : '' ?>>
                                        <label for="footer_modern">Modern</label>
                                    </div>
                                    <div class="component-option">
                                        <input type="radio" id="footer_floating" name="footer_style" value="floating" <?= $currentPrefs['footer_style'] === 'floating' ? 'checked' : '' ?>>
                                        <label for="footer_floating">Floating</label>
                                    </div>
                                    <div class="component-option">
                                        <input type="radio" id="footer_transparent" name="footer_style" value="transparent" <?= $currentPrefs['footer_style'] === 'transparent' ? 'checked' : '' ?>>
                                        <label for="footer_transparent">Transparent</label>
                                    </div>
                                    <div class="component-option">
                                        <input type="radio" id="footer_gradient" name="footer_style" value="gradient" <?= $currentPrefs['footer_style'] === 'gradient' ? 'checked' : '' ?>>
                                        <label for="footer_gradient">Gradient</label>
                                    </div>
                                    <div class="component-option">
                                        <input type="radio" id="footer_minimal" name="footer_style" value="minimal" <?= $currentPrefs['footer_style'] === 'minimal' ? 'checked' : '' ?>>
                                        <label for="footer_minimal">Minimal</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Main Content Styles -->
                    <div class="col-lg-3 mb-4">
                        <div class="card h-100">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-th me-2"></i>
                                    Content Style
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="component-options">
                                    <div class="component-option">
                                        <input type="radio" id="content_classic" name="main_content_style" value="classic" <?= $currentPrefs['main_content_style'] === 'classic' ? 'checked' : '' ?>>
                                        <label for="content_classic">Classic</label>
                                    </div>
                                    <div class="component-option">
                                        <input type="radio" id="content_modern" name="main_content_style" value="modern" <?= $currentPrefs['main_content_style'] === 'modern' ? 'checked' : '' ?>>
                                        <label for="content_modern">Modern</label>
                                    </div>
                                    <div class="component-option">
                                        <input type="radio" id="content_cards" name="main_content_style" value="cards" <?= $currentPrefs['main_content_style'] === 'cards' ? 'checked' : '' ?>>
                                        <label for="content_cards">Cards</label>
                                    </div>
                                    <div class="component-option">
                                        <input type="radio" id="content_floating" name="main_content_style" value="floating" <?= $currentPrefs['main_content_style'] === 'floating' ? 'checked' : '' ?>>
                                        <label for="content_floating">Floating</label>
                                    </div>
                                    <div class="component-option">
                                        <input type="radio" id="content_gradient" name="main_content_style" value="gradient" <?= $currentPrefs['main_content_style'] === 'gradient' ? 'checked' : '' ?>>
                                        <label for="content_gradient">Gradient</label>
                                    </div>
                                    <div class="component-option">
                                        <input type="radio" id="content_glassmorphism" name="main_content_style" value="glassmorphism" <?= $currentPrefs['main_content_style'] === 'glassmorphism' ? 'checked' : '' ?>>
                                        <label for="content_glassmorphism">Glassmorphism</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Advanced Options Row -->
                <div class="row">
                    <!-- Border Radius -->
                    <div class="col-lg-3 mb-4">
                        <div class="card h-100">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-circle me-2"></i>
                                    Border Radius
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="component-options">
                                    <div class="component-option">
                                        <input type="radio" id="radius_none" name="border_radius" value="none" <?= $currentPrefs['border_radius'] === 'none' ? 'checked' : '' ?>>
                                        <label for="radius_none">None (0px)</label>
                                    </div>
                                    <div class="component-option">
                                        <input type="radio" id="radius_small" name="border_radius" value="small" <?= $currentPrefs['border_radius'] === 'small' ? 'checked' : '' ?>>
                                        <label for="radius_small">Small (4px)</label>
                                    </div>
                                    <div class="component-option">
                                        <input type="radio" id="radius_medium" name="border_radius" value="medium" <?= $currentPrefs['border_radius'] === 'medium' ? 'checked' : '' ?>>
                                        <label for="radius_medium">Medium (8px)</label>
                                    </div>
                                    <div class="component-option">
                                        <input type="radio" id="radius_large" name="border_radius" value="large" <?= $currentPrefs['border_radius'] === 'large' ? 'checked' : '' ?>>
                                        <label for="radius_large">Large (15px)</label>
                                    </div>
                                    <div class="component-option">
                                        <input type="radio" id="radius_xl" name="border_radius" value="xl" <?= $currentPrefs['border_radius'] === 'xl' ? 'checked' : '' ?>>
                                        <label for="radius_xl">Extra Large (25px)</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Shadow Style -->
                    <div class="col-lg-3 mb-4">
                        <div class="card h-100">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-cloud me-2"></i>
                                    Shadow Style
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="component-options">
                                    <div class="component-option">
                                        <input type="radio" id="shadow_none" name="shadow_style" value="none" <?= $currentPrefs['shadow_style'] === 'none' ? 'checked' : '' ?>>
                                        <label for="shadow_none">None</label>
                                    </div>
                                    <div class="component-option">
                                        <input type="radio" id="shadow_soft" name="shadow_style" value="soft" <?= $currentPrefs['shadow_style'] === 'soft' ? 'checked' : '' ?>>
                                        <label for="shadow_soft">Soft</label>
                                    </div>
                                    <div class="component-option">
                                        <input type="radio" id="shadow_medium" name="shadow_style" value="medium" <?= $currentPrefs['shadow_style'] === 'medium' ? 'checked' : '' ?>>
                                        <label for="shadow_medium">Medium</label>
                                    </div>
                                    <div class="component-option">
                                        <input type="radio" id="shadow_strong" name="shadow_style" value="strong" <?= $currentPrefs['shadow_style'] === 'strong' ? 'checked' : '' ?>>
                                        <label for="shadow_strong">Strong</label>
                                    </div>
                                    <div class="component-option">
                                        <input type="radio" id="shadow_colored" name="shadow_style" value="colored" <?= $currentPrefs['shadow_style'] === 'colored' ? 'checked' : '' ?>>
                                        <label for="shadow_colored">Colored</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Animation Style -->
                    <div class="col-lg-3 mb-4">
                        <div class="card h-100">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-magic me-2"></i>
                                    Animation Style
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="component-options">
                                    <div class="component-option">
                                        <input type="radio" id="animation_none" name="animation_style" value="none" <?= $currentPrefs['animation_style'] === 'none' ? 'checked' : '' ?>>
                                        <label for="animation_none">None</label>
                                    </div>
                                    <div class="component-option">
                                        <input type="radio" id="animation_subtle" name="animation_style" value="subtle" <?= $currentPrefs['animation_style'] === 'subtle' ? 'checked' : '' ?>>
                                        <label for="animation_subtle">Subtle</label>
                                    </div>
                                    <div class="component-option">
                                        <input type="radio" id="animation_smooth" name="animation_style" value="smooth" <?= $currentPrefs['animation_style'] === 'smooth' ? 'checked' : '' ?>>
                                        <label for="animation_smooth">Smooth</label>
                                    </div>
                                    <div class="component-option">
                                        <input type="radio" id="animation_bouncy" name="animation_style" value="bouncy" <?= $currentPrefs['animation_style'] === 'bouncy' ? 'checked' : '' ?>>
                                        <label for="animation_bouncy">Bouncy</label>
                                    </div>
                                    <div class="component-option">
                                        <input type="radio" id="animation_elastic" name="animation_style" value="elastic" <?= $currentPrefs['animation_style'] === 'elastic' ? 'checked' : '' ?>>
                                        <label for="animation_elastic">Elastic</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Live Preview -->
                    <div class="col-lg-3 mb-4">
                        <div class="card h-100">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-eye me-2"></i>
                                    Live Preview
                                </h6>
                            </div>
                            <div class="card-body">
                                <div id="livePreview" class="layout-preview-mini">
                                    <div class="preview-sidebar-mini"></div>
                                    <div class="preview-main-mini">
                                        <div class="preview-navbar-mini"></div>
                                        <div class="preview-content-mini">
                                            <div class="preview-card-mini"></div>
                                            <div class="preview-card-mini"></div>
                                        </div>
                                        <div class="preview-footer-mini"></div>
                                    </div>
                                </div>
                                <div class="text-center mt-3">
                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="updatePreview()">
                                        <i class="fas fa-sync me-1"></i>Update Preview
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Save Button -->
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-body text-center">
                                <button type="submit" name="save_layout" class="btn btn-primary btn-lg me-3">
                                    <i class="fas fa-save me-2"></i>Save Layout Preferences
                                </button>
                                <button type="button" class="btn btn-info btn-lg me-3" onclick="previewLayout()">
                                    <i class="fas fa-eye me-2"></i>Preview Changes
                                </button>
                                <button type="button" class="btn btn-secondary btn-lg" onclick="resetForm()">
                                    <i class="fas fa-undo me-2"></i>Reset to Default
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
/* Layout Options Styling */
.layout-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.layout-option {
    position: relative;
}

.layout-option input[type="radio"] {
    display: none;
}

.layout-option label {
    display: block;
    padding: 15px;
    border: 2px solid #dee2e6;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    background: #fff;
}

.layout-option label:hover {
    border-color: #007bff;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,123,255,0.2);
}

.layout-option input[type="radio"]:checked + label {
    border-color: #007bff;
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,123,255,0.3);
}

.layout-option label i {
    font-size: 24px;
    margin-bottom: 8px;
    display: block;
}

.layout-option label span {
    font-weight: 600;
    font-size: 14px;
    display: block;
    margin-bottom: 5px;
}

.layout-option label small {
    font-size: 11px;
    opacity: 0.8;
    display: block;
}

/* Color Schemes Styling */
.color-schemes {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 12px;
}

.color-option {
    position: relative;
}

.color-option input[type="radio"] {
    display: none;
}

.color-option label {
    display: block;
    padding: 12px;
    border: 2px solid #dee2e6;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    background: #fff;
}

.color-option label:hover {
    border-color: #007bff;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,123,255,0.2);
}

.color-option input[type="radio"]:checked + label {
    border-color: #007bff;
    background: #f8f9fa;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,123,255,0.3);
}

.color-preview {
    width: 100%;
    height: 40px;
    border-radius: 6px;
    margin-bottom: 8px;
    border: 1px solid rgba(0,0,0,0.1);
}

.color-option label span {
    font-weight: 600;
    font-size: 13px;
    display: block;
    margin-bottom: 3px;
}

.color-option label small {
    font-size: 10px;
    color: #6c757d;
    display: block;
}

/* Component Options Styling */
.component-options {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.component-option {
    position: relative;
}

.component-option input[type="radio"] {
    display: none;
}

.component-option label {
    display: block;
    padding: 10px 15px;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    background: #fff;
    font-size: 13px;
}

.component-option label:hover {
    border-color: #007bff;
    background: #f8f9fa;
}

.component-option input[type="radio"]:checked + label {
    border-color: #007bff;
    background: #007bff;
    color: white;
}

/* Live Preview Styling */
.layout-preview-mini {
    width: 100%;
    height: 120px;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    overflow: hidden;
    display: flex;
    background: #f8f9fa;
    position: relative;
}

.preview-sidebar-mini {
    width: 30%;
    background: #343a40;
    position: relative;
}

.preview-main-mini {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.preview-navbar-mini {
    height: 25%;
    background: #007bff;
}

.preview-content-mini {
    flex: 1;
    padding: 5px;
    display: flex;
    flex-direction: column;
    gap: 3px;
}

.preview-card-mini {
    flex: 1;
    background: #e9ecef;
    border-radius: 3px;
}

.preview-footer-mini {
    height: 15%;
    background: #f8f9fa;
    border-top: 1px solid #dee2e6;
}

/* Responsive Design */
@media (max-width: 768px) {
    .layout-options {
        grid-template-columns: 1fr;
    }

    .color-schemes {
        grid-template-columns: repeat(2, 1fr);
    }

    .component-options {
        gap: 6px;
    }

    .component-option label {
        padding: 8px 12px;
        font-size: 12px;
    }
}

/* Animation Effects */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.layout-option input[type="radio"]:checked + label {
    animation: pulse 0.3s ease-in-out;
}

/* Card Hover Effects */
.card {
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

/* Button Styling */
.btn-lg {
    padding: 12px 30px;
    font-size: 16px;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.btn-lg:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.2);
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize preview
    updatePreview();

    // Add event listeners to all radio buttons
    const radioButtons = document.querySelectorAll('input[type="radio"]');
    radioButtons.forEach(radio => {
        radio.addEventListener('change', function() {
            updatePreview();
        });
    });
});

function updatePreview() {
    const preview = document.getElementById('livePreview');
    if (!preview) return;

    // Get selected values
    const layoutType = document.querySelector('input[name="layout_type"]:checked')?.value || 'classic';
    const colorScheme = document.querySelector('input[name="color_scheme"]:checked')?.value || 'default';
    const sidebarStyle = document.querySelector('input[name="sidebar_style"]:checked')?.value || 'classic';
    const navbarStyle = document.querySelector('input[name="navbar_style"]:checked')?.value || 'classic';
    const footerStyle = document.querySelector('input[name="footer_style"]:checked')?.value || 'classic';
    const borderRadius = document.querySelector('input[name="border_radius"]:checked')?.value || 'medium';

    // Remove all existing classes
    preview.className = 'layout-preview-mini';

    // Add new classes
    preview.classList.add(`layout-${layoutType}`);
    preview.classList.add(`preview-${colorScheme}`);
    preview.classList.add(`sidebar-${sidebarStyle}`);
    preview.classList.add(`navbar-${navbarStyle}`);
    preview.classList.add(`footer-${footerStyle}`);
    preview.classList.add(`radius-${borderRadius}`);

    // Apply color scheme to preview
    const sidebar = preview.querySelector('.preview-sidebar-mini');
    const navbar = preview.querySelector('.preview-navbar-mini');

    // Color scheme backgrounds
    const colorSchemes = {
        'default': { sidebar: '#343a40', navbar: '#007bff' },
        'vibrant': { sidebar: 'linear-gradient(135deg, #ff0080 0%, #00ff80 50%, #8000ff 100%)', navbar: 'linear-gradient(90deg, #ff0080 0%, #00ff80 50%, #8000ff 100%)' },
        'pastel': { sidebar: 'linear-gradient(135deg, #ffb3ba 0%, #bae1ff 50%, #baffc9 100%)', navbar: 'linear-gradient(90deg, #ffb3ba 0%, #bae1ff 50%, #baffc9 100%)' },
        'neon': { sidebar: 'linear-gradient(135deg, #39ff14 0%, #ff073a 50%, #00ffff 100%)', navbar: 'linear-gradient(90deg, #39ff14 0%, #ff073a 50%, #00ffff 100%)' },
        'earth': { sidebar: 'linear-gradient(135deg, #8b4513 0%, #228b22 50%, #daa520 100%)', navbar: 'linear-gradient(90deg, #8b4513 0%, #228b22 50%, #daa520 100%)' },
        'ocean': { sidebar: 'linear-gradient(135deg, #006994 0%, #0099cc 50%, #66ccff 100%)', navbar: 'linear-gradient(90deg, #006994 0%, #0099cc 50%, #66ccff 100%)' },
        'sunset': { sidebar: 'linear-gradient(135deg, #ff4500 0%, #ff6347 50%, #ffd700 100%)', navbar: 'linear-gradient(90deg, #ff4500 0%, #ff6347 50%, #ffd700 100%)' },
        'forest': { sidebar: 'linear-gradient(135deg, #228b22 0%, #32cd32 50%, #90ee90 100%)', navbar: 'linear-gradient(90deg, #228b22 0%, #32cd32 50%, #90ee90 100%)' },
        'midnight': { sidebar: 'linear-gradient(135deg, #2c3e50 0%, #34495e 50%, #1a252f 100%)', navbar: 'linear-gradient(90deg, #2c3e50 0%, #34495e 50%, #1a252f 100%)' },
        'royal': { sidebar: 'linear-gradient(135deg, #663399 0%, #9966cc 50%, #cc99ff 100%)', navbar: 'linear-gradient(90deg, #663399 0%, #9966cc 50%, #cc99ff 100%)' },
        'cyberpunk': { sidebar: 'linear-gradient(135deg, #ff00ff 0%, #00ffff 50%, #ffff00 100%)', navbar: 'linear-gradient(90deg, #ff00ff 0%, #00ffff 50%, #ffff00 100%)' },
        'autumn': { sidebar: 'linear-gradient(135deg, #d2691e 0%, #cd853f 50%, #daa520 100%)', navbar: 'linear-gradient(90deg, #d2691e 0%, #cd853f 50%, #daa520 100%)' }
    };

    const colors = colorSchemes[colorScheme] || colorSchemes['default'];
    if (sidebar) sidebar.style.background = colors.sidebar;
    if (navbar) navbar.style.background = colors.navbar;

    // Apply border radius
    const radiusValues = {
        'none': '0px',
        'small': '4px',
        'medium': '8px',
        'large': '15px',
        'xl': '25px'
    };

    const radiusValue = radiusValues[borderRadius] || '8px';
    const footer = preview.querySelector('.preview-footer-mini');
    const cards = preview.querySelectorAll('.preview-card-mini');

    if (sidebar && layoutType !== 'minimal') {
        sidebar.style.borderRadius = `0 ${radiusValue} ${radiusValue} 0`;
    }
    if (navbar && layoutType !== 'minimal') {
        navbar.style.borderRadius = `0 0 ${radiusValue} ${radiusValue}`;
    }
    if (footer && layoutType !== 'minimal') {
        footer.style.borderRadius = `${radiusValue} ${radiusValue} 0 0`;
    }
    cards.forEach(card => {
        card.style.borderRadius = radiusValue;
    });

    // Apply special effects for certain layouts
    if (layoutType === 'neon' || colorScheme === 'neon') {
        if (sidebar) sidebar.style.boxShadow = '0 0 10px rgba(57, 255, 20, 0.3)';
        if (navbar) navbar.style.boxShadow = '0 0 10px rgba(57, 255, 20, 0.3)';
    } else if (layoutType === 'glassmorphism') {
        if (sidebar) {
            sidebar.style.background = 'rgba(255, 255, 255, 0.25)';
            sidebar.style.backdropFilter = 'blur(10px)';
            sidebar.style.border = '1px solid rgba(255, 255, 255, 0.18)';
        }
        if (navbar) {
            navbar.style.background = 'rgba(255, 255, 255, 0.25)';
            navbar.style.backdropFilter = 'blur(10px)';
            navbar.style.border = '1px solid rgba(255, 255, 255, 0.18)';
        }
    }
}

function previewLayout() {
    // Create a temporary preview effect
    const previewBtn = document.querySelector('button[onclick="previewLayout()"]');
    const originalText = previewBtn.innerHTML;
    previewBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Previewing...';
    previewBtn.disabled = true;

    // Scale animation effect
    document.body.style.transform = 'scale(0.98)';
    document.body.style.transition = 'transform 0.3s ease';

    setTimeout(() => {
        document.body.style.transform = 'scale(1)';

        setTimeout(() => {
            document.body.style.transform = '';
            document.body.style.transition = '';
            previewBtn.innerHTML = originalText;
            previewBtn.disabled = false;

            // Show success message
            showNotification('Preview applied! Changes are temporary until saved.', 'info');
        }, 300);
    }, 1000);
}

function resetForm() {
    if (confirm('Are you sure you want to reset all layout preferences to default?')) {
        // Reset all radio buttons to default values
        document.getElementById('layout_classic').checked = true;
        document.getElementById('color_default').checked = true;
        document.getElementById('sidebar_classic').checked = true;
        document.getElementById('navbar_classic').checked = true;
        document.getElementById('footer_classic').checked = true;
        document.getElementById('content_classic').checked = true;
        document.getElementById('radius_medium').checked = true;
        document.getElementById('shadow_soft').checked = true;
        document.getElementById('animation_subtle').checked = true;

        // Update preview
        updatePreview();

        showNotification('Layout preferences reset to default values.', 'success');
    }
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(notification);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

// Form validation
document.getElementById('layoutForm').addEventListener('submit', function(e) {
    const submitBtn = document.querySelector('button[name="save_layout"]');
    const originalText = submitBtn.innerHTML;

    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Saving...';
    submitBtn.disabled = true;

    // Re-enable button after form submission (in case of errors)
    setTimeout(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }, 3000);
});
</script>

<?php include 'includes/views/layouts/footer.php'; ?>
