<?php
// <PERSON>ript to fix all redirect URLs to include /keuangan/ path

$files = [
    'kalkulator.php',
    'panduan.php', 
    'faq.php',
    'support.php',
    'tutorial.php',
    'konverter.php',
    'kalender.php',
    'laporan_bisnis.php',
    'laporan_pajak.php'
];

$redirectPatterns = [
    "redirect('/login.php')" => "redirect('/keuangan/login.php')",
    "redirect('/kalkulator.php')" => "redirect('/keuangan/kalkulator.php')",
    "redirect('/panduan.php')" => "redirect('/keuangan/panduan.php')",
    "redirect('/faq.php')" => "redirect('/keuangan/faq.php')",
    "redirect('/support.php')" => "redirect('/keuangan/support.php')",
    "redirect('/tutorial.php')" => "redirect('/keuangan/tutorial.php')",
    "redirect('/konverter.php')" => "redirect('/keuangan/konverter.php')",
    "redirect('/kalender.php')" => "redirect('/keuangan/kalender.php')",
    "redirect('/laporan_bisnis.php')" => "redirect('/keuangan/laporan_bisnis.php')",
    "redirect('/laporan_pajak.php')" => "redirect('/keuangan/laporan_pajak.php')"
];

$results = [];

foreach ($files as $file) {
    if (file_exists($file)) {
        $content = file_get_contents($file);
        $originalContent = $content;
        
        foreach ($redirectPatterns as $old => $new) {
            $content = str_replace($old, $new, $content);
        }
        
        if ($content !== $originalContent) {
            file_put_contents($file, $content);
            $results[] = "✅ Fixed redirects in: $file";
        } else {
            $results[] = "ℹ️ No redirects to fix in: $file";
        }
    } else {
        $results[] = "❌ File not found: $file";
    }
}

// Also fix href links in HTML
$hrefPatterns = [
    'href="supplier.php"' => 'href="/keuangan/supplier.php"',
    'href="inventory.php"' => 'href="/keuangan/inventory.php"',
    'href="return.php"' => 'href="/keuangan/return.php"',
    'href="pembelian.php"' => 'href="/keuangan/pembelian.php"',
    'href="pengingat.php"' => 'href="/keuangan/pengingat.php"',
    'href="backup_restore.php"' => 'href="/keuangan/backup_restore.php"',
    'href="kalkulator.php"' => 'href="/keuangan/kalkulator.php"',
    'href="panduan.php"' => 'href="/keuangan/panduan.php"',
    'href="faq.php"' => 'href="/keuangan/faq.php"',
    'href="support.php"' => 'href="/keuangan/support.php"',
    'href="tutorial.php"' => 'href="/keuangan/tutorial.php"',
    'href="konverter.php"' => 'href="/keuangan/konverter.php"',
    'href="kalender.php"' => 'href="/keuangan/kalender.php"',
    'href="laporan_bisnis.php"' => 'href="/keuangan/laporan_bisnis.php"',
    'href="laporan_pajak.php"' => 'href="/keuangan/laporan_pajak.php"'
];

foreach ($files as $file) {
    if (file_exists($file)) {
        $content = file_get_contents($file);
        $originalContent = $content;
        
        foreach ($hrefPatterns as $old => $new) {
            $content = str_replace($old, $new, $content);
        }
        
        if ($content !== $originalContent) {
            file_put_contents($file, $content);
            $results[] = "✅ Fixed href links in: $file";
        }
    }
}

// Output results
echo "<!DOCTYPE html>\n";
echo "<html><head><title>Fix Redirects Results</title></head><body>\n";
echo "<h2>Redirect Fix Results</h2>\n";
echo "<ul>\n";
foreach ($results as $result) {
    echo "<li>$result</li>\n";
}
echo "</ul>\n";
echo "<p><a href='index.php'>Go to Dashboard</a></p>\n";
echo "</body></html>\n";
?>
