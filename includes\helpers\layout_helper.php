<?php
/**
 * Layout Helper Functions
 * Handles layout preferences and applies them to the application
 */

/**
 * Get user layout preferences
 */
function getUserLayoutPreferences($userId) {
    global $pdo;

    // Default preferences
    $defaultPreferences = [
        'layout_type' => 'classic',
        'sidebar_style' => 'classic',
        'navbar_style' => 'classic',
        'footer_style' => 'classic',
        'main_content_style' => 'classic',
        'color_scheme' => 'default',
        'border_radius' => 'medium',
        'shadow_style' => 'soft',
        'animation_style' => 'subtle'
    ];

    try {
        // Check if table exists first
        $stmt = $pdo->query("SHOW TABLES LIKE 'layout_preferences'");
        if ($stmt->rowCount() === 0) {
            error_log("Layout preferences table does not exist. Using default preferences.");
            return $defaultPreferences;
        }

        $stmt = $pdo->prepare("SELECT * FROM layout_preferences WHERE user_id = ?");
        $stmt->execute([$userId]);
        $preferences = $stmt->fetch(PDO::FETCH_ASSOC);

        // Return default preferences if none found
        if (!$preferences) {
            return $defaultPreferences;
        }

        return $preferences;
    } catch (PDOException $e) {
        error_log("Error getting layout preferences: " . $e->getMessage());
        return $defaultPreferences;
    }
}

/**
 * Generate CSS classes based on layout preferences
 */
function getLayoutClasses($layoutPrefs) {
    $classes = [
        'layout' => 'layout-' . ($layoutPrefs['layout_type'] ?? 'classic'),
        'sidebar' => 'sidebar-' . ($layoutPrefs['sidebar_style'] ?? 'classic'),
        'navbar' => 'navbar-' . ($layoutPrefs['navbar_style'] ?? 'classic'),
        'footer' => 'footer-' . ($layoutPrefs['footer_style'] ?? 'classic'),
        'content' => 'content-' . ($layoutPrefs['main_content_style'] ?? 'classic'),
        'color' => 'color-' . ($layoutPrefs['color_scheme'] ?? 'default'),
        'radius' => 'radius-' . ($layoutPrefs['border_radius'] ?? 'medium'),
        'shadow' => 'shadow-' . ($layoutPrefs['shadow_style'] ?? 'soft'),
        'animation' => 'animation-' . ($layoutPrefs['animation_style'] ?? 'subtle')
    ];

    return $classes;
}

/**
 * Generate layout CSS based on preferences
 */
function generateLayoutCSS($layoutPrefs) {
    $css = '';

    // Color scheme variables
    $colorSchemes = [
        'default' => [
            'primary' => '#007bff',
            'secondary' => '#6c757d',
            'sidebar_bg' => '#343a40',
            'navbar_bg' => '#007bff',
            'text_color' => '#ffffff',
            'logo_color' => '#ffffff',
            'accent' => '#17a2b8'
        ],
        'vibrant' => [
            'primary' => '#ff0080',
            'secondary' => '#00ff80',
            'sidebar_bg' => 'linear-gradient(135deg, #ff0080 0%, #00ff80 50%, #8000ff 100%)',
            'navbar_bg' => 'linear-gradient(90deg, #ff0080 0%, #00ff80 50%, #8000ff 100%)',
            'text_color' => '#ffffff',
            'logo_color' => '#ffffff',
            'accent' => '#ffff00'
        ],
        'pastel' => [
            'primary' => '#ffb3ba',
            'secondary' => '#bae1ff',
            'sidebar_bg' => 'linear-gradient(135deg, #ffb3ba 0%, #bae1ff 50%, #baffc9 100%)',
            'navbar_bg' => 'linear-gradient(90deg, #ffb3ba 0%, #bae1ff 50%, #baffc9 100%)',
            'text_color' => '#2c3e50',
            'logo_color' => '#2c3e50',
            'accent' => '#e74c3c'
        ],
        'neon' => [
            'primary' => '#39ff14',
            'secondary' => '#ff073a',
            'sidebar_bg' => 'linear-gradient(135deg, #39ff14 0%, #ff073a 50%, #00ffff 100%)',
            'navbar_bg' => 'linear-gradient(90deg, #39ff14 0%, #ff073a 50%, #00ffff 100%)',
            'text_color' => '#000000',
            'logo_color' => '#000000',
            'accent' => '#ffff00'
        ],
        'earth' => [
            'primary' => '#8b4513',
            'secondary' => '#228b22',
            'sidebar_bg' => 'linear-gradient(135deg, #8b4513 0%, #228b22 50%, #daa520 100%)',
            'navbar_bg' => 'linear-gradient(90deg, #8b4513 0%, #228b22 50%, #daa520 100%)',
            'text_color' => '#ffffff',
            'logo_color' => '#ffffff',
            'accent' => '#cd853f'
        ],
        'ocean' => [
            'primary' => '#006994',
            'secondary' => '#0099cc',
            'sidebar_bg' => 'linear-gradient(135deg, #006994 0%, #0099cc 50%, #66ccff 100%)',
            'navbar_bg' => 'linear-gradient(90deg, #006994 0%, #0099cc 50%, #66ccff 100%)',
            'text_color' => '#ffffff',
            'logo_color' => '#ffffff',
            'accent' => '#20b2aa'
        ],
        'sunset' => [
            'primary' => '#ff4500',
            'secondary' => '#ff6347',
            'sidebar_bg' => 'linear-gradient(135deg, #ff4500 0%, #ff6347 50%, #ffd700 100%)',
            'navbar_bg' => 'linear-gradient(90deg, #ff4500 0%, #ff6347 50%, #ffd700 100%)',
            'text_color' => '#ffffff',
            'logo_color' => '#ffffff',
            'accent' => '#ff8c00'
        ],
        'forest' => [
            'primary' => '#228b22',
            'secondary' => '#32cd32',
            'sidebar_bg' => 'linear-gradient(135deg, #228b22 0%, #32cd32 50%, #90ee90 100%)',
            'navbar_bg' => 'linear-gradient(90deg, #228b22 0%, #32cd32 50%, #90ee90 100%)',
            'text_color' => '#ffffff',
            'logo_color' => '#ffffff',
            'accent' => '#9acd32'
        ],
        'midnight' => [
            'primary' => '#2c3e50',
            'secondary' => '#34495e',
            'sidebar_bg' => 'linear-gradient(135deg, #2c3e50 0%, #34495e 50%, #1a252f 100%)',
            'navbar_bg' => 'linear-gradient(90deg, #2c3e50 0%, #34495e 50%, #1a252f 100%)',
            'text_color' => '#ecf0f1',
            'logo_color' => '#3498db',
            'accent' => '#e74c3c'
        ],
        'royal' => [
            'primary' => '#663399',
            'secondary' => '#9966cc',
            'sidebar_bg' => 'linear-gradient(135deg, #663399 0%, #9966cc 50%, #cc99ff 100%)',
            'navbar_bg' => 'linear-gradient(90deg, #663399 0%, #9966cc 50%, #cc99ff 100%)',
            'text_color' => '#ffffff',
            'logo_color' => '#ffd700',
            'accent' => '#ffd700'
        ],
        'cyberpunk' => [
            'primary' => '#ff00ff',
            'secondary' => '#00ffff',
            'sidebar_bg' => 'linear-gradient(135deg, #ff00ff 0%, #00ffff 50%, #ffff00 100%)',
            'navbar_bg' => 'linear-gradient(90deg, #ff00ff 0%, #00ffff 50%, #ffff00 100%)',
            'text_color' => '#000000',
            'logo_color' => '#000000',
            'accent' => '#ff0080'
        ],
        'autumn' => [
            'primary' => '#d2691e',
            'secondary' => '#cd853f',
            'sidebar_bg' => 'linear-gradient(135deg, #d2691e 0%, #cd853f 50%, #daa520 100%)',
            'navbar_bg' => 'linear-gradient(90deg, #d2691e 0%, #cd853f 50%, #daa520 100%)',
            'text_color' => '#ffffff',
            'logo_color' => '#ffffff',
            'accent' => '#ff6347'
        ]
    ];

    $colorScheme = $layoutPrefs['color_scheme'] ?? 'default';
    $colors = $colorSchemes[$colorScheme] ?? $colorSchemes['default'];

    // Border radius values
    $radiusValues = [
        'none' => '0px',
        'small' => '4px',
        'medium' => '8px',
        'large' => '15px',
        'xl' => '25px'
    ];

    $radius = $radiusValues[$layoutPrefs['border_radius'] ?? 'medium'];

    // Shadow values
    $shadowValues = [
        'none' => 'none',
        'soft' => '0 2px 10px rgba(0,0,0,0.08)',
        'medium' => '0 4px 15px rgba(0,0,0,0.12)',
        'strong' => '0 8px 25px rgba(0,0,0,0.18)',
        'colored' => '0 8px 25px rgba(0, 123, 255, 0.3)'
    ];

    $shadow = $shadowValues[$layoutPrefs['shadow_style'] ?? 'soft'];

    // Generate CSS with higher specificity
    $css .= "<style id='layout-dynamic-css'>\n";
    $css .= "/* Dynamic Layout CSS - Generated */\n";
    $css .= ":root {\n";
    $css .= "  --layout-primary: {$colors['primary']};\n";
    $css .= "  --layout-secondary: {$colors['secondary']};\n";
    $css .= "  --layout-text-color: {$colors['text_color']};\n";
    $css .= "  --layout-logo-color: {$colors['logo_color']};\n";
    $css .= "  --layout-accent: {$colors['accent']};\n";
    $css .= "  --layout-radius: {$radius};\n";
    $css .= "  --layout-shadow: {$shadow};\n";
    $css .= "}\n\n";

    // Layout type specific styles
    $layoutType = $layoutPrefs['layout_type'] ?? 'classic';

    switch ($layoutType) {
        case 'modern':
            $css .= "/* Modern Layout */\n";
            $css .= "body .sidebar, body .modern-sidebar, body #sidebar { background: {$colors['sidebar_bg']} !important; border-radius: 0 {$radius} {$radius} 0 !important; }\n";
            $css .= "body .navbar, body .modern-navbar, body #mainNavbar { background: {$colors['navbar_bg']} !important; border-radius: 0 0 {$radius} {$radius} !important; }\n";
            $css .= "body .sidebar .brand-title, body .sidebar .user-name, body .sidebar .menu-text, body .sidebar a, body .sidebar .nav-link { color: {$colors['text_color']} !important; }\n";
            $css .= "body .sidebar .logo-icon, body .navbar .brand-logo i, body .sidebar i { color: {$colors['logo_color']} !important; }\n";
            $css .= "body .card { border-radius: {$radius} !important; box-shadow: {$shadow} !important; }\n";
            break;

        case 'colorful':
            $css .= "/* Colorful Layout */\n";
            $css .= "body .sidebar, body .modern-sidebar, body #sidebar { background: {$colors['sidebar_bg']} !important; }\n";
            $css .= "body .navbar, body .modern-navbar, body #mainNavbar { background: {$colors['navbar_bg']} !important; }\n";
            $css .= "body .sidebar .brand-title, body .sidebar .user-name, body .sidebar .menu-text, body .sidebar a, body .sidebar .nav-link { color: {$colors['text_color']} !important; }\n";
            $css .= "body .sidebar .logo-icon, body .navbar .brand-logo i, body .sidebar i { color: {$colors['logo_color']} !important; }\n";
            $css .= "body .card { border-radius: {$radius} !important; box-shadow: {$shadow} !important; background: linear-gradient(135deg, rgba(255,255,255,0.9), rgba(255,255,255,1)) !important; }\n";
            break;

        case 'minimal':
            $css .= "/* Minimal Layout */\n";
            $css .= "body .sidebar, body .modern-sidebar, body #sidebar { background: #f8f9fa !important; border-right: 1px solid #dee2e6 !important; }\n";
            $css .= "body .navbar, body .modern-navbar, body #mainNavbar { background: #fff !important; border-bottom: 1px solid #dee2e6 !important; }\n";
            $css .= "body .sidebar .brand-title, body .sidebar .user-name, body .sidebar .menu-text, body .sidebar a, body .sidebar .nav-link { color: #2c3e50 !important; }\n";
            $css .= "body .sidebar .logo-icon, body .navbar .brand-logo i, body .sidebar i { color: #007bff !important; }\n";
            $css .= "body .card { border: 1px solid #dee2e6 !important; box-shadow: none !important; }\n";
            break;

        case 'gradient':
            $css .= "/* Gradient Layout */\n";
            $css .= ".sidebar, .modern-sidebar, #sidebar { background: {$colors['sidebar_bg']} !important; }\n";
            $css .= ".navbar, .modern-navbar, #mainNavbar { background: {$colors['navbar_bg']} !important; }\n";
            $css .= ".sidebar .brand-title, .sidebar .user-name, .sidebar .menu-text { color: {$colors['text_color']} !important; }\n";
            $css .= ".sidebar .logo-icon, .navbar .brand-logo i { color: {$colors['logo_color']} !important; }\n";
            $css .= ".card { background: linear-gradient(135deg, rgba(255,255,255,0.95), rgba(255,255,255,1)) !important; border-radius: {$radius} !important; box-shadow: {$shadow} !important; }\n";
            break;

        case 'glassmorphism':
            $css .= "/* Glassmorphism Layout */\n";
            $css .= ".sidebar, .modern-sidebar, #sidebar { background: rgba(255, 255, 255, 0.25) !important; backdrop-filter: blur(10px) !important; border: 1px solid rgba(255, 255, 255, 0.18) !important; }\n";
            $css .= ".navbar, .modern-navbar, #mainNavbar { background: rgba(255, 255, 255, 0.25) !important; backdrop-filter: blur(10px) !important; border: 1px solid rgba(255, 255, 255, 0.18) !important; }\n";
            $css .= ".sidebar .brand-title, .sidebar .user-name, .sidebar .menu-text { color: {$colors['text_color']} !important; text-shadow: 0 0 10px rgba(0,0,0,0.5) !important; }\n";
            $css .= ".sidebar .logo-icon, .navbar .brand-logo i { color: {$colors['logo_color']} !important; filter: drop-shadow(0 0 5px rgba(0,0,0,0.5)) !important; }\n";
            $css .= ".card { background: rgba(255, 255, 255, 0.25) !important; backdrop-filter: blur(10px) !important; border: 1px solid rgba(255, 255, 255, 0.18) !important; border-radius: {$radius} !important; }\n";
            break;

        case 'neon':
            $css .= "/* Neon Layout */\n";
            $css .= ".sidebar, .modern-sidebar, #sidebar { background: {$colors['sidebar_bg']} !important; box-shadow: 0 0 20px {$colors['accent']} !important; }\n";
            $css .= ".navbar, .modern-navbar, #mainNavbar { background: {$colors['navbar_bg']} !important; box-shadow: 0 0 15px {$colors['accent']} !important; }\n";
            $css .= ".sidebar .brand-title, .sidebar .user-name, .sidebar .menu-text { color: {$colors['text_color']} !important; text-shadow: 0 0 10px {$colors['accent']} !important; }\n";
            $css .= ".sidebar .logo-icon, .navbar .brand-logo i { color: {$colors['logo_color']} !important; filter: drop-shadow(0 0 10px {$colors['accent']}) !important; }\n";
            $css .= ".card { border-radius: {$radius} !important; box-shadow: 0 0 15px {$colors['accent']} !important; }\n";
            break;

        case 'corporate':
            $css .= "/* Corporate Layout */\n";
            $css .= ".sidebar, .modern-sidebar, #sidebar { background: {$colors['sidebar_bg']} !important; border-right: 3px solid {$colors['accent']} !important; }\n";
            $css .= ".navbar, .modern-navbar, #mainNavbar { background: {$colors['navbar_bg']} !important; border-bottom: 3px solid {$colors['accent']} !important; }\n";
            $css .= ".sidebar .brand-title, .sidebar .user-name, .sidebar .menu-text { color: {$colors['text_color']} !important; font-weight: 600 !important; }\n";
            $css .= ".sidebar .logo-icon, .navbar .brand-logo i { color: {$colors['logo_color']} !important; }\n";
            $css .= ".card { border-radius: {$radius} !important; box-shadow: {$shadow} !important; border-left: 4px solid {$colors['accent']} !important; }\n";
            break;

        case 'retro':
            $css .= "/* Retro Layout */\n";
            $css .= ".sidebar, .modern-sidebar, #sidebar { background: {$colors['sidebar_bg']} !important; border: 2px solid {$colors['accent']} !important; }\n";
            $css .= ".navbar, .modern-navbar, #mainNavbar { background: {$colors['navbar_bg']} !important; border: 2px solid {$colors['accent']} !important; }\n";
            $css .= ".sidebar .brand-title, .sidebar .user-name, .sidebar .menu-text { color: {$colors['text_color']} !important; font-family: 'Courier New', monospace !important; }\n";
            $css .= ".sidebar .logo-icon, .navbar .brand-logo i { color: {$colors['logo_color']} !important; }\n";
            $css .= ".card { border-radius: 0 !important; border: 2px solid {$colors['accent']} !important; box-shadow: 4px 4px 0 {$colors['accent']} !important; }\n";
            break;

        default: // classic
            $css .= "/* Classic Layout */\n";
            $css .= ".sidebar, .modern-sidebar, #sidebar { background: {$colors['sidebar_bg']} !important; }\n";
            $css .= ".navbar, .modern-navbar, #mainNavbar { background: {$colors['navbar_bg']} !important; }\n";
            $css .= ".sidebar .brand-title, .sidebar .user-name, .sidebar .menu-text { color: {$colors['text_color']} !important; }\n";
            $css .= ".sidebar .logo-icon, .navbar .brand-logo i { color: {$colors['logo_color']} !important; }\n";
            $css .= ".card { border-radius: {$radius} !important; box-shadow: {$shadow} !important; }\n";
            break;
    }

    // Component specific styles
    $sidebarStyle = $layoutPrefs['sidebar_style'] ?? 'classic';
    if ($sidebarStyle === 'floating') {
        $css .= ".sidebar, .modern-sidebar, #sidebar { margin: 10px !important; border-radius: {$radius} !important; box-shadow: 0 10px 30px rgba(0,0,0,0.2) !important; }\n";
    }

    $navbarStyle = $layoutPrefs['navbar_style'] ?? 'classic';
    if ($navbarStyle === 'floating') {
        $css .= ".navbar, .modern-navbar, #mainNavbar { margin: 10px 10px 0 10px !important; border-radius: {$radius} !important; box-shadow: 0 5px 15px rgba(0,0,0,0.1) !important; }\n";
    }

    $footerStyle = $layoutPrefs['footer_style'] ?? 'classic';
    if ($footerStyle === 'floating') {
        $css .= ".footer { margin: 0 10px 10px 10px !important; border-radius: {$radius} !important; box-shadow: 0 5px 15px rgba(0,0,0,0.1) !important; }\n";
    }

    $contentStyle = $layoutPrefs['main_content_style'] ?? 'classic';
    if ($contentStyle === 'floating') {
        $css .= ".main-content .card { transform: translateY(-5px) !important; box-shadow: 0 10px 25px rgba(0,0,0,0.15) !important; }\n";
    }

    // Animation styles
    $animationStyle = $layoutPrefs['animation_style'] ?? 'subtle';
    switch ($animationStyle) {
        case 'smooth':
            $css .= "* { transition: all 0.3s ease; }\n";
            break;
        case 'bouncy':
            $css .= "* { transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55); }\n";
            break;
        case 'elastic':
            $css .= "* { transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275); }\n";
            break;
        case 'subtle':
            $css .= "* { transition: all 0.2s ease; }\n";
            break;
    }

    $css .= "</style>\n";

    return $css;
}

/**
 * Apply layout preferences to current page
 */
function applyLayoutPreferences($userId) {
    $layoutPrefs = getUserLayoutPreferences($userId);
    $css = generateLayoutCSS($layoutPrefs);

    // Output CSS
    echo $css;

    // Add body classes
    $classes = getLayoutClasses($layoutPrefs);
    $bodyClasses = implode(' ', $classes);

    echo "<script>\n";
    echo "document.addEventListener('DOMContentLoaded', function() {\n";
    echo "  document.body.className += ' {$bodyClasses}';\n";
    echo "});\n";
    echo "</script>\n";
}

/**
 * Get layout body classes as string
 */
function getLayoutBodyClasses($userId) {
    $layoutPrefs = getUserLayoutPreferences($userId);
    $classes = getLayoutClasses($layoutPrefs);
    return implode(' ', $classes);
}
?>
