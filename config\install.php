<?php
// Konfigurasi Database
$db_host = 'localhost';
$db_user = 'root';
$db_pass = '';

try {
    // Buat koneksi ke MySQL tanpa memilih database
    $pdo = new PDO("mysql:host=$db_host", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Buat database jika belum ada
    $pdo->exec("CREATE DATABASE IF NOT EXISTS keuangan CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "Database 'keuangan' berhasil dibuat atau sudah ada.<br>";
    
    // Pilih database
    $pdo->exec("USE keuangan");
    
    // Buat tabel users
    $pdo->exec("CREATE TABLE IF NOT EXISTS users (
        id INT PRIMARY KEY AUTO_INCREMENT,
        nama VARCHAR(100) NOT NULL,
        email VARCHAR(100) NOT NULL UNIQUE,
        password VARCHAR(255) NOT NULL,
        role ENUM('admin', 'user') DEFAULT 'user',
        email_verified TINYINT(1) DEFAULT 0,
        verification_token VARCHAR(100) NULL,
        status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
        last_login TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB");
    echo "Tabel 'users' berhasil dibuat.<br>";
    
    // Buat tabel kategori
    $pdo->exec("CREATE TABLE IF NOT EXISTS kategori (
        id INT PRIMARY KEY AUTO_INCREMENT,
        nama VARCHAR(100) NOT NULL,
        tipe ENUM('pemasukan', 'pengeluaran') NOT NULL,
        user_id INT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    ) ENGINE=InnoDB");
    echo "Tabel 'kategori' berhasil dibuat.<br>";
    
    // Buat tabel transaksi
    $pdo->exec("CREATE TABLE IF NOT EXISTS transaksi (
        id INT PRIMARY KEY AUTO_INCREMENT,
        user_id INT NOT NULL,
        kategori_id INT NOT NULL,
        jumlah DECIMAL(15,2) NOT NULL,
        tanggal DATE NOT NULL,
        keterangan TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (kategori_id) REFERENCES kategori(id) ON DELETE RESTRICT
    ) ENGINE=InnoDB");
    echo "Tabel 'transaksi' berhasil dibuat.<br>";
    
    // Buat tabel anggaran
    $pdo->exec("CREATE TABLE IF NOT EXISTS anggaran (
        id INT PRIMARY KEY AUTO_INCREMENT,
        user_id INT NOT NULL,
        kategori_id INT NOT NULL,
        jumlah DECIMAL(15,2) NOT NULL,
        tanggal_mulai DATE NOT NULL,
        tanggal_selesai DATE NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (kategori_id) REFERENCES kategori(id) ON DELETE RESTRICT
    ) ENGINE=InnoDB");
    echo "Tabel 'anggaran' berhasil dibuat.<br>";
    
    // Buat tabel target
    $pdo->exec("CREATE TABLE IF NOT EXISTS target (
        id INT PRIMARY KEY AUTO_INCREMENT,
        user_id INT NOT NULL,
        nama VARCHAR(100) NOT NULL,
        jumlah_target DECIMAL(15,2) NOT NULL,
        jumlah_terkumpul DECIMAL(15,2) DEFAULT 0,
        tanggal_mulai DATE NOT NULL,
        tanggal_selesai DATE NOT NULL,
        status ENUM('aktif', 'selesai', 'batal') DEFAULT 'aktif',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    ) ENGINE=InnoDB");
    echo "Tabel 'target' berhasil dibuat.<br>";
    
    // Buat tabel activity_logs
    $pdo->exec("CREATE TABLE IF NOT EXISTS activity_logs (
        id INT PRIMARY KEY AUTO_INCREMENT,
        user_id INT NOT NULL,
        aktivitas TEXT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    ) ENGINE=InnoDB");
    echo "Tabel 'activity_logs' berhasil dibuat.<br>";
    
    // Buat tabel notifikasi
    $pdo->exec("CREATE TABLE IF NOT EXISTS notifikasi (
        id INT PRIMARY KEY AUTO_INCREMENT,
        user_id INT NOT NULL,
        judul VARCHAR(100) NOT NULL,
        pesan TEXT NOT NULL,
        tipe ENUM('info', 'warning', 'success', 'danger') DEFAULT 'info',
        dibaca TINYINT(1) DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    ) ENGINE=InnoDB");
    echo "Tabel 'notifikasi' berhasil dibuat.<br>";
    
    // Buat tabel password_resets
    $pdo->exec("CREATE TABLE IF NOT EXISTS password_resets (
        id INT PRIMARY KEY AUTO_INCREMENT,
        email VARCHAR(100) NOT NULL,
        token VARCHAR(100) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        expires_at TIMESTAMP NULL DEFAULT NULL,
        used TINYINT(1) DEFAULT 0,
        INDEX (email),
        INDEX (token)
    ) ENGINE=InnoDB");
    echo "Tabel 'password_resets' berhasil dibuat.<br>";
    
    // Insert default admin user
    $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
    $stmt = $pdo->prepare("INSERT IGNORE INTO users (nama, email, password, role, email_verified, status) 
                          VALUES (?, ?, ?, 'admin', 1, 'active')");
    $stmt->execute(['Administrator', '<EMAIL>', $admin_password]);
    echo "User admin default berhasil dibuat.<br>";
    
    // Insert default categories
    $categories = [
        ['Gaji', 'pemasukan'],
        ['Bonus', 'pemasukan'],
        ['Investasi', 'pemasukan'],
        ['Makanan & Minuman', 'pengeluaran'],
        ['Transportasi', 'pengeluaran'],
        ['Belanja', 'pengeluaran'],
        ['Tagihan', 'pengeluaran'],
        ['Hiburan', 'pengeluaran'],
        ['Kesehatan', 'pengeluaran'],
        ['Pendidikan', 'pengeluaran']
    ];
    
    $stmt = $pdo->prepare("INSERT IGNORE INTO kategori (nama, tipe) VALUES (?, ?)");
    foreach ($categories as $category) {
        $stmt->execute($category);
    }
    echo "Kategori default berhasil dibuat.<br>";
    
    echo "<br>Instalasi database berhasil!<br>";
    echo "Silakan hapus file install.php setelah selesai untuk keamanan.<br>";
    
} catch(PDOException $e) {
    die("Error: " . $e->getMessage());
}
?> 