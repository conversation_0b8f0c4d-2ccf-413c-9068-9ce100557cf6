            </div> <!-- End of content-container -->
        </div> <!-- End of main-content -->
    </div> <!-- End of wrapper -->

<!-- Control Sidebar -->
<aside class="control-sidebar" id="controlSidebar">
    <div class="control-sidebar-content">
        <div class="control-sidebar-header">
            <h5><i class="fas fa-cogs me-2"></i>Layout Customizer</h5>
            <button type="button" class="control-sidebar-close" data-widget="control-sidebar">
                <i class="fas fa-times"></i>
            </button>
        </div>

        <div class="control-sidebar-body">
            <!-- KeuanganKu Brand -->
            <div class="control-section brand-section">
                <h6 class="control-section-title">
                    <i class="fas fa-wallet me-2 text-primary"></i>KeuanganKu Layout Customizer
                </h6>
                <p class="text-muted small mb-0">Customize your dashboard appearance</p>
            </div>

            <!-- Quick Settings -->
            <div class="control-section">
                <h6 class="control-section-title">
                    <i class="fas fa-bolt me-2"></i>Quick Settings
                </h6>
                <div class="row g-2">
                    <div class="col-6">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="darkModeSwitch">
                            <label class="form-check-label small" for="darkModeSwitch">Dark Mode</label>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="sidebarCollapsed">
                            <label class="form-check-label small" for="sidebarCollapsed">Collapse Sidebar</label>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="headerFixed" checked>
                            <label class="form-check-label small" for="headerFixed">Fixed Header</label>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="footerFixed">
                            <label class="form-check-label small" for="footerFixed">Fixed Footer</label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Header/Navbar Customization -->
            <div class="control-section">
                <h6 class="control-section-title">
                    <i class="fas fa-window-maximize me-2"></i>Header/Navbar Customization
                </h6>

                <!-- Header Layout Options -->
                <div class="mb-3">
                    <label class="form-label small fw-bold">Layout Options</label>
                    <div class="row g-2">
                        <div class="col-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="dropdownLegacy">
                                <label class="form-check-label small" for="dropdownLegacy">Legacy Dropdown</label>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="noBorder">
                                <label class="form-check-label small" for="noBorder">No Border</label>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="navbarSmall">
                                <label class="form-check-label small" for="navbarSmall">Small Text</label>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="navbarShadow" checked>
                                <label class="form-check-label small" for="navbarShadow">Shadow</label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Navbar Background Colors -->
                <div class="mb-3">
                    <label class="form-label small fw-bold">Background Colors</label>
                    <div class="color-variants">
                        <div class="color-option active" data-color="white" data-target="navbar">
                            <div class="color-preview bg-white border"></div>
                            <span>White</span>
                        </div>
                        <div class="color-option" data-color="primary" data-target="navbar">
                            <div class="color-preview bg-primary"></div>
                            <span>Primary</span>
                        </div>
                        <div class="color-option" data-color="secondary" data-target="navbar">
                            <div class="color-preview bg-secondary"></div>
                            <span>Secondary</span>
                        </div>
                        <div class="color-option" data-color="success" data-target="navbar">
                            <div class="color-preview bg-success"></div>
                            <span>Success</span>
                        </div>
                        <div class="color-option" data-color="info" data-target="navbar">
                            <div class="color-preview bg-info"></div>
                            <span>Info</span>
                        </div>
                        <div class="color-option" data-color="warning" data-target="navbar">
                            <div class="color-preview bg-warning"></div>
                            <span>Warning</span>
                        </div>
                        <div class="color-option" data-color="danger" data-target="navbar">
                            <div class="color-preview bg-danger"></div>
                            <span>Danger</span>
                        </div>
                        <div class="color-option" data-color="dark" data-target="navbar">
                            <div class="color-preview bg-dark"></div>
                            <span>Dark</span>
                        </div>
                        <div class="color-option" data-color="gradient-primary" data-target="navbar">
                            <div class="color-preview" style="background: linear-gradient(45deg, #007bff, #6610f2);"></div>
                            <span>Gradient</span>
                        </div>
                        <div class="color-option" data-color="transparent" data-target="navbar">
                            <div class="color-preview border" style="background: linear-gradient(45deg, transparent 49%, #ccc 49%, #ccc 51%, transparent 51%);"></div>
                            <span>Glass</span>
                        </div>
                    </div>
                </div>

                <!-- Navbar Icon Colors -->
                <div class="mb-3">
                    <label class="form-label small fw-bold">Icon Colors</label>
                    <div class="icon-variants">
                        <div class="icon-option active" data-icon-color="default" data-target="navbar-icons">
                            <div class="icon-preview">
                                <i class="fas fa-cogs" style="color: var(--text-secondary);"></i>
                            </div>
                            <span>Default</span>
                        </div>
                        <div class="icon-option" data-icon-color="primary" data-target="navbar-icons">
                            <div class="icon-preview">
                                <i class="fas fa-cogs" style="color: var(--primary-color);"></i>
                            </div>
                            <span>Primary</span>
                        </div>
                        <div class="icon-option" data-icon-color="success" data-target="navbar-icons">
                            <div class="icon-preview">
                                <i class="fas fa-cogs" style="color: var(--success-color);"></i>
                            </div>
                            <span>Success</span>
                        </div>
                        <div class="icon-option" data-icon-color="white" data-target="navbar-icons">
                            <div class="icon-preview">
                                <i class="fas fa-cogs" style="color: #ffffff;"></i>
                            </div>
                            <span>White</span>
                        </div>
                        <div class="icon-option" data-icon-color="dark" data-target="navbar-icons">
                            <div class="icon-preview">
                                <i class="fas fa-cogs" style="color: #1a1a1a;"></i>
                            </div>
                            <span>Dark</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar Customization -->
            <div class="control-section">
                <h6 class="control-section-title">
                    <i class="fas fa-bars me-2"></i>Sidebar Customization
                </h6>

                <!-- Sidebar Background Colors -->
                <div class="mb-3">
                    <label class="form-label small fw-bold">Sidebar Colors</label>
                    <div class="color-variants">
                        <div class="color-option active" data-color="default" data-target="sidebar">
                            <div class="color-preview" style="background: linear-gradient(135deg, #ffffff, #f8f9fa); border: 1px solid #dee2e6;"></div>
                            <span>Default</span>
                        </div>
                        <div class="color-option" data-color="dark" data-target="sidebar">
                            <div class="color-preview bg-dark"></div>
                            <span>Dark</span>
                        </div>
                        <div class="color-option" data-color="primary" data-target="sidebar">
                            <div class="color-preview bg-primary"></div>
                            <span>Primary</span>
                        </div>
                        <div class="color-option" data-color="success" data-target="sidebar">
                            <div class="color-preview bg-success"></div>
                            <span>Success</span>
                        </div>
                        <div class="color-option" data-color="info" data-target="sidebar">
                            <div class="color-preview bg-info"></div>
                            <span>Info</span>
                        </div>
                        <div class="color-option" data-color="gradient-blue" data-target="sidebar">
                            <div class="color-preview" style="background: linear-gradient(135deg, #667eea, #764ba2);"></div>
                            <span>Blue Gradient</span>
                        </div>
                        <div class="color-option" data-color="gradient-green" data-target="sidebar">
                            <div class="color-preview" style="background: linear-gradient(135deg, #11998e, #38ef7d);"></div>
                            <span>Green Gradient</span>
                        </div>
                        <div class="color-option" data-color="gradient-purple" data-target="sidebar">
                            <div class="color-preview" style="background: linear-gradient(135deg, #667eea, #764ba2);"></div>
                            <span>Purple Gradient</span>
                        </div>
                    </div>
                </div>

                <!-- Sidebar Layout Options -->
                <div class="mb-3">
                    <label class="form-label small fw-bold">Layout Options</label>
                    <div class="row g-2">
                        <div class="col-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="sidebarFixed" checked>
                                <label class="form-check-label small" for="sidebarFixed">Fixed Position</label>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="sidebarMini">
                                <label class="form-check-label small" for="sidebarMini">Mini Mode</label>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="sidebarMiniMD">
                                <label class="form-check-label small" for="sidebarMiniMD">Mini on Tablet</label>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="sidebarMiniXS">
                                <label class="form-check-label small" for="sidebarMiniXS">Mini on Mobile</label>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="sidebarNavSmall">
                                <label class="form-check-label small" for="sidebarNavSmall">Small Text</label>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="sidebarShadow" checked>
                                <label class="form-check-label small" for="sidebarShadow">Shadow</label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Navigation Style Options -->
                <div class="mb-3">
                    <label class="form-label small fw-bold">Navigation Style</label>
                    <div class="row g-2">
                        <div class="col-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="navFlat">
                                <label class="form-check-label small" for="navFlat">Flat Style</label>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="navLegacy">
                                <label class="form-check-label small" for="navLegacy">Legacy Style</label>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="navCompact">
                                <label class="form-check-label small" for="navCompact">Compact</label>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="navChildIndent">
                                <label class="form-check-label small" for="navChildIndent">Child Indent</label>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="navChildHide">
                                <label class="form-check-label small" for="navChildHide">Hide on Collapse</label>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="disableHover">
                                <label class="form-check-label small" for="disableHover">No Hover</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Content Area Customization -->
            <div class="control-section">
                <h6 class="control-section-title">
                    <i class="fas fa-desktop me-2"></i>Content Area
                </h6>

                <!-- Content Background -->
                <div class="mb-3">
                    <label class="form-label small fw-bold">Background Style</label>
                    <div class="color-variants">
                        <div class="color-option active" data-color="default" data-target="content">
                            <div class="color-preview bg-light border"></div>
                            <span>Default</span>
                        </div>
                        <div class="color-option" data-color="white" data-target="content">
                            <div class="color-preview bg-white border"></div>
                            <span>White</span>
                        </div>
                        <div class="color-option" data-color="gray" data-target="content">
                            <div class="color-preview bg-secondary"></div>
                            <span>Gray</span>
                        </div>
                        <div class="color-option" data-color="pattern" data-target="content">
                            <div class="color-preview" style="background: repeating-linear-gradient(45deg, #f8f9fa, #f8f9fa 10px, #ffffff 10px, #ffffff 20px);"></div>
                            <span>Pattern</span>
                        </div>
                    </div>
                </div>

                <!-- Content Layout Options -->
                <div class="mb-3">
                    <label class="form-label small fw-bold">Layout Options</label>
                    <div class="row g-2">
                        <div class="col-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="contentBoxed">
                                <label class="form-check-label small" for="contentBoxed">Boxed Layout</label>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="contentPadding" checked>
                                <label class="form-check-label small" for="contentPadding">Content Padding</label>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="cardShadows" checked>
                                <label class="form-check-label small" for="cardShadows">Card Shadows</label>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="roundedCorners" checked>
                                <label class="form-check-label small" for="roundedCorners">Rounded Corners</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Typography & Fonts -->
            <div class="control-section">
                <h6 class="control-section-title">
                    <i class="fas fa-font me-2"></i>Typography
                </h6>

                <!-- Font Family -->
                <div class="mb-3">
                    <label class="form-label small fw-bold">Font Family</label>
                    <select class="form-select form-select-sm" id="fontFamily">
                        <option value="default">Default (System)</option>
                        <option value="inter">Inter</option>
                        <option value="roboto">Roboto</option>
                        <option value="opensans">Open Sans</option>
                        <option value="lato">Lato</option>
                        <option value="poppins">Poppins</option>
                        <option value="nunito">Nunito</option>
                        <option value="montserrat">Montserrat</option>
                    </select>
                </div>

                <!-- Font Size -->
                <div class="mb-3">
                    <label class="form-label small fw-bold">Base Font Size</label>
                    <select class="form-select form-select-sm" id="fontSize">
                        <option value="small">Small (14px)</option>
                        <option value="default" selected>Default (16px)</option>
                        <option value="large">Large (18px)</option>
                        <option value="xlarge">Extra Large (20px)</option>
                    </select>
                </div>

                <!-- Typography Options -->
                <div class="mb-3">
                    <label class="form-label small fw-bold">Typography Options</label>
                    <div class="row g-2">
                        <div class="col-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="fontSmoothing" checked>
                                <label class="form-check-label small" for="fontSmoothing">Font Smoothing</label>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="textShadow">
                                <label class="form-check-label small" for="textShadow">Text Shadow</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Animation & Effects -->
            <div class="control-section">
                <h6 class="control-section-title">
                    <i class="fas fa-magic me-2"></i>Animation & Effects
                </h6>

                <!-- Animation Speed -->
                <div class="mb-3">
                    <label class="form-label small fw-bold">Animation Speed</label>
                    <select class="form-select form-select-sm" id="animationSpeed">
                        <option value="slow">Slow (0.5s)</option>
                        <option value="normal" selected>Normal (0.3s)</option>
                        <option value="fast">Fast (0.15s)</option>
                        <option value="none">No Animation</option>
                    </select>
                </div>

                <!-- Effect Options -->
                <div class="mb-3">
                    <label class="form-label small fw-bold">Visual Effects</label>
                    <div class="row g-2">
                        <div class="col-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="hoverEffects" checked>
                                <label class="form-check-label small" for="hoverEffects">Hover Effects</label>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="fadeTransitions" checked>
                                <label class="form-check-label small" for="fadeTransitions">Fade Transitions</label>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="slideAnimations" checked>
                                <label class="form-check-label small" for="slideAnimations">Slide Animations</label>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="parallaxEffect">
                                <label class="form-check-label small" for="parallaxEffect">Parallax Effect</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Accessibility -->
            <div class="control-section">
                <h6 class="control-section-title">
                    <i class="fas fa-universal-access me-2"></i>Accessibility
                </h6>

                <!-- Accessibility Options -->
                <div class="mb-3">
                    <label class="form-label small fw-bold">Accessibility Features</label>
                    <div class="row g-2">
                        <div class="col-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="highContrast">
                                <label class="form-check-label small" for="highContrast">High Contrast</label>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="focusIndicators" checked>
                                <label class="form-check-label small" for="focusIndicators">Focus Indicators</label>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="reducedMotion">
                                <label class="form-check-label small" for="reducedMotion">Reduced Motion</label>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="screenReaderOptimized">
                                <label class="form-check-label small" for="screenReaderOptimized">Screen Reader</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Layout Presets -->
            <div class="control-section">
                <h6 class="control-section-title">
                    <i class="fas fa-layer-group me-2"></i>Layout Presets
                </h6>

                <!-- Preset Options -->
                <div class="mb-3">
                    <div class="d-grid gap-2">
                        <button class="btn btn-outline-primary btn-sm" onclick="applyPreset('modern')">
                            <i class="fas fa-rocket me-1"></i>Modern Layout
                        </button>
                        <button class="btn btn-outline-success btn-sm" onclick="applyPreset('classic')">
                            <i class="fas fa-building me-1"></i>Classic Layout
                        </button>
                        <button class="btn btn-outline-info btn-sm" onclick="applyPreset('minimal')">
                            <i class="fas fa-circle me-1"></i>Minimal Layout
                        </button>
                        <button class="btn btn-outline-warning btn-sm" onclick="applyPreset('dark')">
                            <i class="fas fa-moon me-1"></i>Dark Theme
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" onclick="applyPreset('compact')">
                            <i class="fas fa-compress me-1"></i>Compact Layout
                        </button>
                    </div>
                </div>
            </div>

            <!-- Settings Management -->
            <div class="control-section">
                <h6 class="control-section-title">
                    <i class="fas fa-tools me-2"></i>Settings Management
                </h6>
                <div class="d-grid gap-2">
                    <button class="btn btn-outline-primary btn-sm" onclick="exportSettings()">
                        <i class="fas fa-download me-1"></i>Export Settings
                    </button>
                    <button class="btn btn-outline-success btn-sm" onclick="importSettings()">
                        <i class="fas fa-upload me-1"></i>Import Settings
                    </button>
                    <button class="btn btn-outline-warning btn-sm" onclick="resetAllSettings()">
                        <i class="fas fa-undo me-1"></i>Reset to Default
                    </button>
                </div>
            </div>

            <!-- Footer Info -->
            <div class="control-section">
                <div class="text-center">
                    <small class="text-muted">
                        <i class="fas fa-info-circle me-1"></i>
                        Changes are saved automatically
                    </small>
                </div>
            </div>
        </div>
    </div>
</aside>

<!-- Control Sidebar Overlay -->
<div class="control-sidebar-overlay" id="controlSidebarOverlay"></div>

<!-- Modern Footer -->
<footer class="modern-footer">
    <div class="footer-container">
        <div class="footer-content">
            <div class="footer-section">
                <div class="footer-brand">
                    <div class="footer-logo">
                        <i class="fas fa-wallet"></i>
                    </div>
                    <div class="footer-brand-text">
                        <h6 class="footer-title">KeuanganKu</h6>
                        <small class="footer-subtitle">Financial Management System</small>
                    </div>
                </div>
            </div>

            <div class="footer-section">
                <div class="footer-links">
                    <a href="#" class="footer-link">Privacy Policy</a>
                    <a href="#" class="footer-link">Terms of Service</a>
                    <a href="#" class="footer-link">Help Center</a>
                </div>
            </div>

            <div class="footer-section">
                <div class="footer-info">
                    <div class="footer-version">
                        <i class="fas fa-code-branch me-1"></i>
                        <span>Version 2.0.0</span>
                    </div>
                    <div class="footer-copyright">
                        <i class="fas fa-copyright me-1"></i>
                        <span><?= date('Y') ?> KeuanganKu. All rights reserved.</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="footer-bottom">
            <div class="footer-stats">
                <div class="stat-item">
                    <i class="fas fa-users text-primary"></i>
                    <span>Active Users</span>
                </div>
                <div class="stat-item">
                    <i class="fas fa-shield-alt text-success"></i>
                    <span>Secure</span>
                </div>
                <div class="stat-item">
                    <i class="fas fa-clock text-info"></i>
                    <span>24/7 Support</span>
                </div>
            </div>
        </div>
    </div>
</footer>

<!-- Modern Scripts -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"
        integrity="sha384-C6RzsynM9kWDrMNeT87bh95OGNyZPhcTNXj1NW7RuBCsyN/o0jlpcV8Qyq46cDfL"
        crossorigin="anonymous"></script>

<!-- Custom Scripts -->
<script src="assets/js/navbar.js"></script>
<script src="assets/js/admin-dashboard.js"></script>
<script src="assets/js/main.js"></script>

<!-- Modern UI Framework JavaScript -->
<script src="assets/js/modern-ui.js?v=<?= time() ?>"></script>

<!-- Layout Manager - Consolidated JavaScript -->
<script src="assets/js/layout-manager.js?v=<?= time() ?>"></script>

<!-- Control Sidebar functionality moved to separate file -->
<script src="assets/js/control-sidebar.js?v=<?= time() ?>"></script>

    </body>
</html>
