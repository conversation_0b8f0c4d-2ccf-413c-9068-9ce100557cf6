<?php
require_once 'includes/config/database.php';
require_once 'includes/helpers/functions.php';
require_once 'includes/helpers/notifications.php';

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isLoggedIn()) {
    setFlashMessage('danger', 'Silakan login terlebih dahulu');
    redirect('/keuangan/login.php');
}

$currentUser = getCurrentUser();

// Check if ID is provided
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    setFlashMessage('danger', 'ID kategori tidak valid');
    redirect('/kategori.php');
}

$id = (int)$_GET['id'];

// Get category data
try {
    $stmt = $pdo->prepare("
        SELECT * FROM kategori 
        WHERE id = ? AND user_id = ?
    ");
    $stmt->execute([$id, $currentUser['id']]);
    $kategori = $stmt->fetch();
    
    if (!$kategori) {
        setFlashMessage('danger', 'Kategori tidak ditemukan');
        redirect('/kategori.php');
    }
} catch (PDOException $e) {
    setFlashMessage('danger', 'Terjadi kesalahan: ' . $e->getMessage());
    redirect('/kategori.php');
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $nama = cleanInput($_POST['nama']);
    $tipe = cleanInput($_POST['tipe']);
    
    // Validate input
    $errors = [];
    
    if (empty($nama)) {
        $errors[] = 'Nama kategori harus diisi';
    }
    
    if (empty($tipe)) {
        $errors[] = 'Tipe kategori harus dipilih';
    }
    
    if (empty($errors)) {
        try {
            // Check if category name already exists (excluding current category)
            $stmt = $pdo->prepare("
                SELECT COUNT(*) as count 
                FROM kategori 
                WHERE nama = ? AND user_id = ? AND id != ?
            ");
            $stmt->execute([$nama, $currentUser['id'], $id]);
            
            if ($stmt->fetch()['count'] > 0) {
                $errors[] = 'Nama kategori sudah ada';
            } else {
                // Update category
                $stmt = $pdo->prepare("
                    UPDATE kategori 
                    SET nama = ?, tipe = ? 
                    WHERE id = ? AND user_id = ?
                ");
                $stmt->execute([$nama, $tipe, $id, $currentUser['id']]);
                
                // Create notification
                $judul = 'Kategori Diperbarui';
                $pesan = sprintf(
                    'Kategori %s telah diperbarui menjadi %s (%s)',
                    $kategori['nama'],
                    $nama,
                    $tipe === 'pemasukan' ? 'Pemasukan' : 'Pengeluaran'
                );
                createNotification($currentUser['id'], $judul, $pesan, 'info');
                
                // Log activity
                logActivity($currentUser['id'], sprintf(
                    'Memperbarui kategori %s menjadi %s (%s)',
                    $kategori['nama'],
                    $nama,
                    $tipe === 'pemasukan' ? 'Pemasukan' : 'Pengeluaran'
                ));
                
                setFlashMessage('success', 'Kategori berhasil diperbarui');
                redirect('/kategori.php');
            }
        } catch (PDOException $e) {
            $errors[] = 'Terjadi kesalahan: ' . $e->getMessage();
        }
    }
}
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit Kategori - Sistem Keuangan</title>
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@400;600;700&display=swap" rel="stylesheet">
    <link href="/assets/css/style.css" rel="stylesheet">
</head>
<body>
    <div class="wrapper">
        <!-- Sidebar -->
        <?php include 'includes/views/layouts/sidebar.php'; ?>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Top Navigation -->
            <?php include 'includes/views/layouts/navbar.php'; ?>

            <div class="container-fluid">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h3 mb-0">Edit Kategori</h1>
                    <a href="/kategori.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Kembali
                    </a>
                </div>

                <?php if (!empty($errors)): ?>
                <div class="alert alert-danger">
                    <ul class="mb-0">
                        <?php foreach ($errors as $error): ?>
                        <li><?= $error ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
                <?php endif; ?>

                <div class="card">
                    <div class="card-body">
                        <form action="" method="POST" class="needs-validation" novalidate>
                            <div class="mb-3">
                                <label class="form-label">Nama Kategori</label>
                                <input type="text" name="nama" class="form-control" 
                                       value="<?= htmlspecialchars($kategori['nama']) ?>" required>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">Tipe</label>
                                <select name="tipe" class="form-select" required>
                                    <option value="">Pilih Tipe</option>
                                    <option value="pemasukan" <?= $kategori['tipe'] === 'pemasukan' ? 'selected' : '' ?>>
                                        Pemasukan
                                    </option>
                                    <option value="pengeluaran" <?= $kategori['tipe'] === 'pengeluaran' ? 'selected' : '' ?>>
                                        Pengeluaran
                                    </option>
                                </select>
                            </div>
                            
                            <div class="text-end">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>Simpan Perubahan
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/assets/js/main.js"></script>
</body>
</html> 