# ✅ Checklist Migrasi Server Keuangan<PERSON>u ke PC Baru

## 📋 Pre-Migration (Server Lama)

### Backup Data
- [ ] **Export Database**
  - [ ] Via phpMyAdmin: Export → Custom → SQL format
  - [ ] Include structure and data
  - [ ] Save as `keuanganku_backup.sql`
  - [ ] Verify file size > 0 KB

- [ ] **Backup Project Files**
  - [ ] Copy entire project folder
  - [ ] Include all subfolders (assets, uploads, etc.)
  - [ ] Backup .htaccess files
  - [ ] Backup configuration files

- [ ] **Document Current Settings**
  - [ ] Database name: `________________`
  - [ ] Database user: `________________`
  - [ ] PHP version: `________________`
  - [ ] Special configurations: `________________`

### Backup Verification
- [ ] Database backup file exists and not empty
- [ ] All project files copied successfully
- [ ] Configuration files included
- [ ] Upload/media files included

---

## 🖥️ PC Baru - Software Installation

### Essential Software
- [ ] **XAMPP** (PHP 8.1+)
  - [ ] Downloaded from official site
  - [ ] Installed with default settings
  - [ ] Apache and MySQL services start successfully

- [ ] **Visual Studio Code**
  - [ ] Downloaded and installed
  - [ ] Extensions installed (see extension list)

- [ ] **Git for Windows**
  - [ ] Downloaded and installed
  - [ ] Username and email configured

### Optional Software
- [ ] **HeidiSQL** (advanced database management)
- [ ] **GitHub Desktop** (if using Git GUI)
- [ ] **Notepad++** (quick text editing)

---

## 🔧 XAMPP Configuration

### Service Check
- [ ] **Apache**
  - [ ] Port 80 available (no conflicts)
  - [ ] Service starts without errors
  - [ ] http://localhost/ shows XAMPP dashboard

- [ ] **MySQL**
  - [ ] Port 3306 available
  - [ ] Service starts without errors
  - [ ] phpMyAdmin accessible

### PHP Configuration
- [ ] **Extensions Enabled**
  - [ ] mysqli
  - [ ] pdo_mysql
  - [ ] mbstring
  - [ ] openssl
  - [ ] curl
  - [ ] gd

- [ ] **Settings Updated**
  - [ ] memory_limit = 512M
  - [ ] upload_max_filesize = 64M
  - [ ] post_max_size = 64M
  - [ ] timezone = Asia/Jakarta

---

## 📁 Project Setup

### File Transfer
- [ ] **Project Files Copied**
  - [ ] Location: `C:\xampp\htdocs\keuanganku\`
  - [ ] All files and folders present
  - [ ] Permissions set correctly

- [ ] **Directory Structure**
  ```
  C:\xampp\htdocs\keuanganku\
  ├── assets/
  ├── uploads/
  ├── config.php (or database.php)
  ├── index.php
  └── [other project files]
  ```

### Configuration Update
- [ ] **Database Configuration**
  - [ ] config.php updated with new credentials
  - [ ] Host: localhost
  - [ ] Username: root
  - [ ] Password: (empty)
  - [ ] Database name: keuanganku_db

- [ ] **URL Configuration**
  - [ ] BASE_URL updated to http://localhost/keuanganku/
  - [ ] Asset paths updated
  - [ ] Upload paths updated

---

## 🗄️ Database Setup

### Database Creation
- [ ] **phpMyAdmin Access**
  - [ ] http://localhost/phpmyadmin/ accessible
  - [ ] Can login with root (no password)

- [ ] **Database Import**
  - [ ] New database created: `keuanganku_db`
  - [ ] Character set: utf8mb4
  - [ ] Collation: utf8mb4_unicode_ci
  - [ ] Backup file imported successfully
  - [ ] All tables present
  - [ ] Data imported correctly

### Database Verification
- [ ] **Table Count**: `____` tables (same as original)
- [ ] **User Data**: Login accounts exist
- [ ] **Application Data**: Transactions, categories, etc. present
- [ ] **No Import Errors**: Check for any SQL errors

---

## 🌐 Website Testing

### Basic Access
- [ ] **Homepage Loads**
  - [ ] http://localhost/keuanganku/ accessible
  - [ ] No PHP errors displayed
  - [ ] CSS and JS files loading

- [ ] **Login System**
  - [ ] Login page accessible
  - [ ] Can login with existing account
  - [ ] Session management working
  - [ ] Logout functionality working

### Core Features
- [ ] **Dashboard**
  - [ ] Dashboard loads without errors
  - [ ] Data displays correctly
  - [ ] Charts/graphs working (if any)

- [ ] **CRUD Operations**
  - [ ] Can create new records
  - [ ] Can read/view existing data
  - [ ] Can update records
  - [ ] Can delete records

- [ ] **File Uploads**
  - [ ] Upload functionality working
  - [ ] Files saved to correct location
  - [ ] File permissions correct

### Advanced Features
- [ ] **Reports/Exports**
  - [ ] Report generation working
  - [ ] Export functions working
  - [ ] PDF generation (if applicable)

- [ ] **Email Functions** (if applicable)
  - [ ] Email sending configured
  - [ ] Test email sent successfully

---

## 🛠️ Development Environment

### VS Code Setup
- [ ] **Extensions Installed**
  - [ ] PHP Intelephense
  - [ ] PHP Debug
  - [ ] MySQL
  - [ ] GitLens
  - [ ] Auto Rename Tag
  - [ ] Live Server

- [ ] **Project Opened**
  - [ ] Project folder opened in VS Code
  - [ ] IntelliSense working
  - [ ] Syntax highlighting active

### Automation Scripts
- [ ] **Scripts Created**
  - [ ] Backup script: `C:\Tools\scripts\backup-keuanganku.bat`
  - [ ] Start dev script: `C:\Tools\scripts\start-dev-keuanganku.bat`
  - [ ] Database import script: `C:\Tools\scripts\import-database.bat`
  - [ ] Quick menu: `C:\Tools\scripts\keuanganku-menu.bat`

- [ ] **Desktop Shortcuts**
  - [ ] XAMPP Control
  - [ ] KeuanganKu Website
  - [ ] Start KeuanganKu Dev
  - [ ] KeuanganKu Menu

---

## 🔍 Troubleshooting Checklist

### Common Issues
- [ ] **Port Conflicts**
  - [ ] Apache port 80 free (stop IIS/Skype if needed)
  - [ ] MySQL port 3306 free

- [ ] **Permission Issues**
  - [ ] XAMPP running as Administrator
  - [ ] Project folder permissions correct
  - [ ] Upload folder writable

- [ ] **PHP Errors**
  - [ ] Check error logs: `C:\xampp\apache\logs\error.log`
  - [ ] PHP extensions loaded
  - [ ] Memory limits sufficient

### Error Log Locations
- [ ] **Apache Errors**: `C:\xampp\apache\logs\error.log`
- [ ] **PHP Errors**: `C:\xampp\php\logs\php_error_log`
- [ ] **MySQL Errors**: `C:\xampp\mysql\data\*.err`

---

## 🎯 Performance Optimization

### XAMPP Optimization
- [ ] **MySQL Configuration**
  - [ ] innodb_buffer_pool_size increased
  - [ ] max_allowed_packet increased
  - [ ] query_cache enabled

- [ ] **Apache Configuration**
  - [ ] mod_rewrite enabled
  - [ ] compression enabled
  - [ ] caching configured

### Application Optimization
- [ ] **File Permissions**
  - [ ] Correct file permissions set
  - [ ] Upload directories writable
  - [ ] Cache directories writable

- [ ] **Database Optimization**
  - [ ] Indexes present on key columns
  - [ ] Query optimization checked
  - [ ] Database maintenance scheduled

---

## 🔒 Security Checklist

### Basic Security
- [ ] **Default Passwords Changed**
  - [ ] MySQL root password set (for production)
  - [ ] Application admin passwords updated
  - [ ] Default accounts removed/disabled

- [ ] **File Security**
  - [ ] Sensitive files protected
  - [ ] .htaccess files in place
  - [ ] Directory browsing disabled

### Development Security
- [ ] **Debug Mode**
  - [ ] Debug mode enabled for development
  - [ ] Error reporting configured
  - [ ] Logging enabled

- [ ] **Backup Security**
  - [ ] Backup files stored securely
  - [ ] Regular backup schedule planned
  - [ ] Backup restoration tested

---

## ✅ Final Verification

### Complete Testing
- [ ] **Full Application Test**
  - [ ] All major features tested
  - [ ] No critical errors found
  - [ ] Performance acceptable
  - [ ] User experience maintained

- [ ] **Data Integrity**
  - [ ] All data migrated correctly
  - [ ] No data loss detected
  - [ ] Relationships maintained
  - [ ] File uploads accessible

### Documentation
- [ ] **Setup Documented**
  - [ ] New server details recorded
  - [ ] Configuration changes documented
  - [ ] Troubleshooting notes created
  - [ ] Backup procedures established

### Handover
- [ ] **Knowledge Transfer**
  - [ ] Team informed of new setup
  - [ ] Access credentials shared securely
  - [ ] Maintenance procedures documented
  - [ ] Support contacts updated

---

## 📞 Emergency Contacts & Resources

### Quick Reference
- **XAMPP Control Panel**: `C:\xampp\xampp-control.exe`
- **Project URL**: http://localhost/keuanganku/
- **phpMyAdmin**: http://localhost/phpmyadmin/
- **Quick Menu**: Desktop → "KeuanganKu Menu"

### Support Resources
- **XAMPP Documentation**: https://www.apachefriends.org/docs/
- **PHP Manual**: https://www.php.net/manual/
- **MySQL Documentation**: https://dev.mysql.com/doc/

---

**🎉 Migration Complete!**

Date: `________________`
Completed by: `________________`
Notes: `________________`
