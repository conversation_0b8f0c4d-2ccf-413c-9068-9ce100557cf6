on:
  workflow_call:
    inputs:
      releaseName:
        required: true
        type: string
      stable:
        required: false
        type: boolean
        default: false

name: "Release"

permissions:
  contents: read

jobs:
  create:
    name: Create Release

    runs-on: ubuntu-latest

    permissions:
      contents: write

    steps:
      - name: Harden Runner
        uses: step-security/harden-runner@cb605e52c26070c328afc4562f0b4ada7618a84e # v2.10.4
        with:
          egress-policy: audit

      - name: Create prerelease
        if: ${{ !inputs.stable }}
        env:
          GITHUB_TOKEN: ${{ github.token }}
        run: |
          gh release create \
            --repo ${{ github.repository }} \
            --title ${{ inputs.releaseName }} \
            --prerelease \
            --generate-notes \
            ${{ inputs.releaseName }}

      - name: Create release
        if: ${{ inputs.stable }}
        env:
          GITHUB_TOKEN: ${{ github.token }}
        run: |
          gh release create \
            --repo ${{ github.repository }} \
            --title ${{ inputs.releaseName }} \
            --generate-notes \
            ${{ inputs.releaseName }}

  upload_release:
    name: "Upload"

    needs: ["create"]

    runs-on: ubuntu-latest

    permissions:
      id-token: write
      contents: write
      attestations: write

    steps:
      - name: Harden Runner
        uses: step-security/harden-runner@cb605e52c26070c328afc4562f0b4ada7618a84e # v2.10.4
        with:
          egress-policy: audit

      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - uses: actions/download-artifact@fa0a91b85d4f404e444e00e005971372dc801d16 # v4.1.8
        with:
          name: docs
          path: docs
      - run: |
          tar -czvf docs.tar.gz docs
      - name: "Attest Documentation"
        id: attestation
        uses: actions/attest-build-provenance@520d128f165991a6c774bcb264f323e3d70747f4 # v2.2.0
        with:
          subject-path: "docs.tar.gz"
      - name: Copy Attestation
        run: cp "$ATTESTATION" docs.tar.gz.sigstore
        env:
          ATTESTATION: "${{ steps.attestation.outputs.bundle-path }}"
      - name: Upload
        env:
          GITHUB_TOKEN: ${{ github.token }}
        run: |
          gh release upload --clobber "${{ github.ref_name }}" \
            docs.tar.gz docs.tar.gz.sigstore
