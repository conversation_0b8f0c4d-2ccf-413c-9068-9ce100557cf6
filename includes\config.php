<?php
/**
 * Application Configuration
 * 
 * This file contains all configuration settings for the application.
 * Make sure to update these settings according to your environment.
 */

// Database configuration
define('DB_HOST', 'localhost');
define('DB_NAME', 'keuangan');
define('DB_USER', 'root');
define('DB_PASS', '');

// Application configuration
define('APP_NAME', 'Siste<PERSON>uang<PERSON>');
define('APP_VERSION', '1.0.0');
define('APP_URL', 'http://localhost/keuangan');
define('APP_TIMEZONE', 'Asia/Jakarta');

// Session configuration
define('SESSION_LIFETIME', 86400); // 24 hours
define('SESSION_PATH', '/');
define('SESSION_SECURE', false); // Set to true in production
define('SESSION_HTTPONLY', true);
define('SESSION_SAMESITE', 'Lax');

// Security configuration
define('HASH_COST', 12); // For password_hash()
define('TOKEN_LIFETIME', 3600); // 1 hour
define('MAX_LOGIN_ATTEMPTS', 5);
define('LOGIN_TIMEOUT', 900); // 15 minutes

// File upload configuration
define('UPLOAD_MAX_SIZE', 5 * 1024 * 1024); // 5MB
define('ALLOWED_EXTENSIONS', ['jpg', 'jpeg', 'png', 'pdf', 'doc', 'docx', 'xls', 'xlsx']);
define('UPLOAD_PATH', ROOT_PATH . '/uploads');

// Email configuration
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'your-app-password');
define('SMTP_FROM_EMAIL', '<EMAIL>');
define('SMTP_FROM_NAME', APP_NAME);

// Notification configuration
define('NOTIFICATION_LIFETIME', 30 * 24 * 60 * 60); // 30 days
define('MAX_NOTIFICATIONS', 100);

// Cache configuration
define('CACHE_ENABLED', true);
define('CACHE_PATH', ROOT_PATH . '/cache');
define('CACHE_LIFETIME', 3600); // 1 hour

// Logging configuration
define('LOG_ENABLED', true);
define('LOG_PATH', ROOT_PATH . '/logs');
define('LOG_LEVEL', 'debug'); // debug, info, warning, error, critical

// API configuration
define('API_KEY', 'your-api-key');
define('API_SECRET', 'your-api-secret');
define('API_TIMEOUT', 30); // seconds

// Theme configuration
define('DEFAULT_THEME', 'light');
define('AVAILABLE_THEMES', ['light', 'dark']);

// Language configuration
define('DEFAULT_LANGUAGE', 'id');
define('AVAILABLE_LANGUAGES', ['id', 'en']);

// Currency configuration
define('DEFAULT_CURRENCY', 'IDR');
define('CURRENCY_SYMBOL', 'Rp');
define('CURRENCY_DECIMAL_SEPARATOR', ',');
define('CURRENCY_THOUSAND_SEPARATOR', '.');
define('CURRENCY_DECIMAL_PLACES', 2);

// Date and time configuration
define('DATE_FORMAT', 'd/m/Y');
define('TIME_FORMAT', 'H:i:s');
define('DATETIME_FORMAT', 'd/m/Y H:i:s');

// Pagination configuration
define('ITEMS_PER_PAGE', 10);
define('PAGINATION_LINKS', 5);

// Export configuration
define('EXPORT_PATH', ROOT_PATH . '/exports');
define('ALLOWED_EXPORT_FORMATS', ['csv', 'xlsx', 'pdf']);

// Backup configuration
define('BACKUP_ENABLED', true);
define('BACKUP_PATH', ROOT_PATH . '/backups');
define('BACKUP_RETENTION_DAYS', 30);

// Maintenance mode
define('MAINTENANCE_MODE', false);
define('MAINTENANCE_MESSAGE', 'Sistem sedang dalam pemeliharaan. Silakan coba beberapa saat lagi.');

// Debug mode
define('DEBUG_MODE', true);

// Error reporting
if (DEBUG_MODE) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}

// Set timezone
date_default_timezone_set(APP_TIMEZONE);

// Set session parameters before session starts
if (session_status() === PHP_SESSION_NONE) {
    ini_set('session.gc_maxlifetime', SESSION_LIFETIME);
    ini_set('session.cookie_lifetime', SESSION_LIFETIME);
    ini_set('session.cookie_path', SESSION_PATH);
    ini_set('session.cookie_secure', SESSION_SECURE);
    ini_set('session.cookie_httponly', SESSION_HTTPONLY);
    ini_set('session.cookie_samesite', SESSION_SAMESITE);
}

// Create required directories if they don't exist
$directories = [
    UPLOAD_PATH,
    CACHE_PATH,
    LOG_PATH,
    EXPORT_PATH,
    BACKUP_PATH
];

foreach ($directories as $directory) {
    if (!file_exists($directory)) {
        mkdir($directory, 0755, true);
    }
}

// Load environment-specific configuration
$env_file = ROOT_PATH . '/.env';
if (file_exists($env_file)) {
    $env = parse_ini_file($env_file);
    foreach ($env as $key => $value) {
        if (!defined($key)) {
            define($key, $value);
        }
    }
} 