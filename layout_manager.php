<?php
require_once 'includes/config/database.php';
require_once 'includes/helpers/functions.php';
require_once 'includes/helpers/theme_helper.php';

// Check admin access
$currentUser = getCurrentUser();
if (!$currentUser || $currentUser['role'] !== 'admin') {
    setFlashMessage('danger', 'Akses ditolak! Anda tidak memiliki izin untuk mengakses halaman ini.');
    redirect('dashboard.php');
}

$pageTitle = 'Layout Manager';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    try {
        switch ($_POST['action']) {
            case 'apply_layout':
                $layoutStyle = $_POST['layout_style'] ?? 'standard';
                $layouts = getLayoutStyles();

                if (isset($layouts[$layoutStyle])) {
                    // Update user theme preferences with new layout
                    $userTheme = getUserThemePreferences($currentUser['id']);
                    $userTheme['layout_style'] = $layoutStyle;

                    $result = updateUserThemePreferences($currentUser['id'], $userTheme);

                    if ($result) {
                        setFlashMessage('success', 'Layout "' . $layouts[$layoutStyle]['name'] . '" berhasil diterapkan');
                    } else {
                        setFlashMessage('danger', 'Gagal menerapkan layout');
                    }
                } else {
                    setFlashMessage('danger', 'Layout tidak ditemukan');
                }
                break;

            case 'reset_layout':
                $userTheme = getUserThemePreferences($currentUser['id']);
                $userTheme['layout_style'] = 'standard';

                $result = updateUserThemePreferences($currentUser['id'], $userTheme);

                if ($result) {
                    setFlashMessage('success', 'Layout berhasil direset ke standard');
                } else {
                    setFlashMessage('danger', 'Gagal mereset layout');
                }
                break;
        }
    } catch (Exception $e) {
        setFlashMessage('danger', 'Error: ' . $e->getMessage());
        error_log("Layout manager error: " . $e->getMessage());
    }

    redirect('layout_manager.php');
}

// Get current user layout preferences
$userTheme = getUserThemePreferences($currentUser['id']);
$currentLayout = $userTheme['layout_style'] ?? 'standard';

// Get available layouts
$layouts = getLayoutStyles();

include 'includes/views/layouts/header.php';
?>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">🎨 Layout Manager</h1>
                    <p class="text-muted">Customize your application layout and design</p>
                </div>
                <div>
                    <a href="theme_manager.php" class="btn btn-outline-primary me-2">
                        <i class="fas fa-palette me-2"></i>Theme Manager
                    </a>
                    <a href="/keuangan/dashboard.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Current Layout -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Current Layout</h5>
                </div>
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h6 class="mb-1"><?= $layouts[$currentLayout]['name'] ?></h6>
                            <p class="text-muted mb-0"><?= $layouts[$currentLayout]['description'] ?></p>
                            <div class="mt-2">
                                <span class="badge bg-primary me-2">Sidebar: <?= $layouts[$currentLayout]['sidebar_width'] ?></span>
                                <span class="badge bg-info me-2">Navbar: <?= $layouts[$currentLayout]['navbar_height'] ?></span>
                                <span class="badge bg-success">Padding: <?= $layouts[$currentLayout]['content_padding'] ?></span>
                            </div>
                        </div>
                        <div class="col-md-4 text-end">
                            <form method="POST" class="d-inline">
                                <input type="hidden" name="action" value="reset_layout">
                                <button type="submit" class="btn btn-outline-secondary">
                                    <i class="fas fa-undo me-2"></i>Reset to Standard
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Available Layouts -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Available Layouts</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <?php foreach ($layouts as $layoutId => $layout): ?>
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="card layout-card <?= $currentLayout === $layoutId ? 'border-primary' : '' ?>">
                                <div class="card-body">
                                    <div class="layout-preview mb-3">
                                        <div class="layout-demo" data-layout="<?= $layoutId ?>">
                                            <div class="demo-sidebar" style="width: <?= $layout['sidebar_width'] ?>"></div>
                                            <div class="demo-content">
                                                <div class="demo-navbar" style="height: <?= $layout['navbar_height'] ?>"></div>
                                                <div class="demo-main" style="padding: <?= $layout['content_padding'] ?>">
                                                    <div class="demo-card"></div>
                                                    <div class="demo-card"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <h6 class="card-title"><?= htmlspecialchars($layout['name']) ?></h6>
                                    <p class="card-text text-muted small"><?= htmlspecialchars($layout['description']) ?></p>

                                    <div class="layout-specs mb-3">
                                        <small class="text-muted">
                                            <div>Sidebar: <?= $layout['sidebar_width'] ?></div>
                                            <div>Navbar: <?= $layout['navbar_height'] ?></div>
                                            <div>Padding: <?= $layout['content_padding'] ?></div>
                                        </small>
                                    </div>

                                    <form method="POST" class="d-inline">
                                        <input type="hidden" name="action" value="apply_layout">
                                        <input type="hidden" name="layout_style" value="<?= $layoutId ?>">
                                        <button type="submit" class="btn btn-primary btn-sm w-100">
                                            <i class="fas fa-check me-1"></i>Apply Layout
                                        </button>
                                    </form>

                                    <?php if ($currentLayout === $layoutId): ?>
                                        <div class="text-center mt-2">
                                            <span class="badge bg-success">
                                                <i class="fas fa-check me-1"></i>Currently Active
                                            </span>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Layout Customization -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Layout Customization</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Layout Features</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-2"></i>Responsive design</li>
                                <li><i class="fas fa-check text-success me-2"></i>Mobile-friendly</li>
                                <li><i class="fas fa-check text-success me-2"></i>Customizable dimensions</li>
                                <li><i class="fas fa-check text-success me-2"></i>Multiple layout styles</li>
                                <li><i class="fas fa-check text-success me-2"></i>Real-time preview</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>Coming Soon</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-clock text-warning me-2"></i>Custom layout builder</li>
                                <li><i class="fas fa-clock text-warning me-2"></i>Right sidebar option</li>
                                <li><i class="fas fa-clock text-warning me-2"></i>Top navbar option</li>
                                <li><i class="fas fa-clock text-warning me-2"></i>Floating sidebar</li>
                                <li><i class="fas fa-clock text-warning me-2"></i>Grid layout system</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.layout-card {
    transition: all 0.3s ease;
    height: 100%;
}

.layout-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.layout-preview {
    height: 120px;
    background: #f8f9fa;
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid #dee2e6;
}

.layout-demo {
    display: flex;
    height: 100%;
    transform: scale(0.3);
    transform-origin: top left;
    width: 333%;
    height: 333%;
}

.demo-sidebar {
    background: #343a40;
    min-width: 80px;
    position: relative;
}

.demo-content {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.demo-navbar {
    background: #007bff;
    min-height: 20px;
}

.demo-main {
    flex: 1;
    background: #ffffff;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.demo-card {
    background: #e9ecef;
    height: 30px;
    border-radius: 4px;
}

.layout-specs {
    font-size: 0.8rem;
    line-height: 1.2;
}

/* Layout-specific demo styles */
.layout-demo[data-layout="modern"] .demo-sidebar {
    border-radius: 0 15px 15px 0;
}

.layout-demo[data-layout="modern"] .demo-navbar {
    border-radius: 0 0 15px 15px;
}

.layout-demo[data-layout="minimal"] .demo-sidebar {
    background: #f8f9fa;
    border-right: 1px solid #dee2e6;
}

.layout-demo[data-layout="minimal"] .demo-navbar {
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.layout-demo[data-layout="corporate"] .demo-sidebar {
    background: linear-gradient(180deg, #343a40, #495057);
    border-right: 3px solid #007bff;
}

.layout-demo[data-layout="corporate"] .demo-navbar {
    background: linear-gradient(90deg, #343a40, #495057);
    border-bottom: 3px solid #007bff;
}

.layout-demo[data-layout="futuristic"] .demo-sidebar {
    background: linear-gradient(135deg, #000000, #00ff88);
    box-shadow: 0 0 10px rgba(0,255,136,0.3);
}

.layout-demo[data-layout="futuristic"] .demo-navbar {
    background: linear-gradient(90deg, #000000, #00ff88);
    box-shadow: 0 0 10px rgba(0,255,136,0.3);
}

/* Layout Preview Styles */
.layout-preview-container {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    border-radius: 15px;
    padding: 20px;
    margin: 20px 0;
}

.layout-preview {
    width: 100%;
    height: 300px;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    display: flex;
    background: #fff;
}

/* Sidebar Preview */
.preview-sidebar {
    width: 200px;
    padding: 15px;
    display: flex;
    flex-direction: column;
    transition: all 0.3s ease;
}

.preview-logo {
    text-align: center;
    padding: 10px 0;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    margin-bottom: 15px;
}

.preview-logo i {
    font-size: 24px;
    color: #fff;
}

.preview-menu-item {
    padding: 8px 12px;
    margin: 2px 0;
    border-radius: 6px;
    display: flex;
    align-items: center;
    color: rgba(255,255,255,0.8);
    font-size: 14px;
    transition: all 0.2s ease;
}

.preview-menu-item.active {
    background: rgba(255,255,255,0.2);
    color: #fff;
}

.preview-menu-item i {
    margin-right: 8px;
    width: 16px;
}

/* Main Content Preview */
.preview-main {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.preview-navbar {
    height: 60px;
    padding: 0 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: all 0.3s ease;
}

.preview-navbar-brand {
    display: flex;
    align-items: center;
    color: #fff;
    font-weight: 600;
}

.preview-navbar-brand i {
    margin-right: 8px;
    font-size: 18px;
}

.preview-navbar-actions {
    display: flex;
    gap: 15px;
}

.preview-navbar-actions i {
    color: #fff;
    font-size: 16px;
}

.preview-content {
    flex: 1;
    padding: 20px;
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.preview-card {
    flex: 1;
    min-width: 200px;
    background: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.preview-card-header {
    padding: 12px 15px;
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.preview-card-header h6 {
    margin: 0;
    font-size: 14px;
    color: #495057;
}

.preview-card-body {
    padding: 15px;
}

.preview-chart {
    height: 60px;
    background: linear-gradient(45deg, #007bff, #0056b3);
    border-radius: 4px;
    opacity: 0.8;
}

.preview-stats {
    display: flex;
    gap: 20px;
}

.preview-stat {
    text-align: center;
}

.preview-stat-value {
    font-size: 18px;
    font-weight: 600;
    color: #007bff;
}

.preview-stat-label {
    font-size: 12px;
    color: #6c757d;
}

.preview-footer {
    height: 40px;
    padding: 0 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: #6c757d;
    transition: all 0.3s ease;
}

/* Layout Type Styles */
.layout-classic .preview-sidebar {
    background: #343a40;
}

.layout-classic .preview-navbar {
    background: #007bff;
}

.layout-classic .preview-footer {
    background: #f8f9fa;
    border-top: 1px solid #dee2e6;
}

.layout-modern .preview-sidebar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 0 15px 15px 0;
}

.layout-modern .preview-navbar {
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    border-radius: 0 0 15px 15px;
}

.layout-modern .preview-footer {
    background: linear-gradient(90deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 15px 15px 0 0;
}

.layout-colorful .preview-sidebar {
    background: linear-gradient(135deg, #ff6b6b 0%, #4ecdc4 50%, #45b7d1 100%);
}

.layout-colorful .preview-navbar {
    background: linear-gradient(90deg, #ff6b6b 0%, #4ecdc4 50%, #45b7d1 100%);
}

.layout-colorful .preview-footer {
    background: linear-gradient(90deg, #ff6b6b 0%, #4ecdc4 100%);
}

.layout-minimal .preview-sidebar {
    background: #f8f9fa;
    border-right: 1px solid #dee2e6;
}

.layout-minimal .preview-menu-item {
    color: #495057;
}

.layout-minimal .preview-navbar {
    background: #fff;
    border-bottom: 1px solid #dee2e6;
}

.layout-minimal .preview-navbar-brand,
.layout-minimal .preview-navbar-actions i {
    color: #495057;
}

.layout-minimal .preview-footer {
    background: #f8f9fa;
    border-top: 1px solid #dee2e6;
}

.layout-gradient .preview-sidebar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.layout-gradient .preview-navbar {
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}

.layout-gradient .preview-footer {
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}

.layout-glassmorphism .preview-sidebar {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.18);
}

.layout-glassmorphism .preview-navbar {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.18);
}

.layout-glassmorphism .preview-footer {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.18);
}

/* Color Scheme Styles */
.color-vibrant .preview-sidebar {
    background: linear-gradient(135deg, #ff0080 0%, #00ff80 50%, #8000ff 100%);
}

.color-pastel .preview-sidebar {
    background: linear-gradient(135deg, #ffb3ba 0%, #bae1ff 50%, #baffc9 100%);
}

.color-neon .preview-sidebar {
    background: linear-gradient(135deg, #39ff14 0%, #ff073a 50%, #00ffff 100%);
    box-shadow: 0 0 20px rgba(57, 255, 20, 0.3);
}

.color-earth .preview-sidebar {
    background: linear-gradient(135deg, #8b4513 0%, #228b22 50%, #daa520 100%);
}

.color-ocean .preview-sidebar {
    background: linear-gradient(135deg, #006994 0%, #0099cc 50%, #66ccff 100%);
}

.color-sunset .preview-sidebar {
    background: linear-gradient(135deg, #ff4500 0%, #ff6347 50%, #ffd700 100%);
}

.color-forest .preview-sidebar {
    background: linear-gradient(135deg, #228b22 0%, #32cd32 50%, #90ee90 100%);
}

/* Component Style Variations */
.sidebar-floating .preview-sidebar {
    margin: 10px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.navbar-floating .preview-navbar {
    margin: 10px 10px 0 10px;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.footer-floating .preview-footer {
    margin: 0 10px 10px 10px;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.content-floating .preview-card {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

/* Border Radius Variations */
.radius-none .preview-sidebar,
.radius-none .preview-navbar,
.radius-none .preview-footer,
.radius-none .preview-card {
    border-radius: 0 !important;
}

.radius-small .preview-sidebar,
.radius-small .preview-navbar,
.radius-small .preview-footer,
.radius-small .preview-card {
    border-radius: 4px !important;
}

.radius-large .preview-sidebar,
.radius-large .preview-navbar,
.radius-large .preview-footer,
.radius-large .preview-card {
    border-radius: 15px !important;
}

.radius-xl .preview-sidebar,
.radius-xl .preview-navbar,
.radius-xl .preview-footer,
.radius-xl .preview-card {
    border-radius: 25px !important;
}

/* Shadow Variations */
.shadow-none .preview-sidebar,
.shadow-none .preview-navbar,
.shadow-none .preview-footer,
.shadow-none .preview-card {
    box-shadow: none !important;
}

.shadow-soft .preview-card {
    box-shadow: 0 2px 10px rgba(0,0,0,0.08) !important;
}

.shadow-medium .preview-card {
    box-shadow: 0 4px 15px rgba(0,0,0,0.12) !important;
}

.shadow-strong .preview-card {
    box-shadow: 0 8px 25px rgba(0,0,0,0.18) !important;
}

.shadow-colored .preview-card {
    box-shadow: 0 8px 25px rgba(0, 123, 255, 0.3) !important;
}

/* Color Preview Dots */
.color-preview {
    display: flex;
    justify-content: center;
    gap: 4px;
}

.color-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 0 1px 3px rgba(0,0,0,0.2);
}

/* Responsive Design */
@media (max-width: 768px) {
    .layout-preview {
        height: 200px;
    }

    .preview-sidebar {
        width: 60px;
    }

    .preview-menu-item span {
        display: none;
    }

    .preview-content {
        flex-direction: column;
    }

    .preview-card {
        min-width: auto;
    }
}

/* Animation Styles */
.layout-preview * {
    transition: all 0.3s ease;
}

.preview-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 20px rgba(0,0,0,0.15);
}

.preview-menu-item:hover {
    background: rgba(255,255,255,0.1);
}
</style>

<script>
// Real-time preview updates
document.addEventListener('DOMContentLoaded', function() {
    const layoutForm = document.getElementById('layoutForm');
    const layoutPreview = document.getElementById('layoutPreview');

    if (layoutForm && layoutPreview) {
        // Update preview when form changes
        layoutForm.addEventListener('change', function(e) {
            updatePreview();
        });

        function updatePreview() {
            const formData = new FormData(layoutForm);

            // Get all form values
            const layoutType = formData.get('layout_type') || 'classic';
            const sidebarStyle = formData.get('sidebar_style') || 'classic';
            const navbarStyle = formData.get('navbar_style') || 'classic';
            const footerStyle = formData.get('footer_style') || 'classic';
            const mainContentStyle = formData.get('main_content_style') || 'classic';
            const colorScheme = formData.get('color_scheme') || 'default';
            const borderRadius = formData.get('border_radius') || 'medium';
            const shadowStyle = formData.get('shadow_style') || 'soft';
            const animationStyle = formData.get('animation_style') || 'subtle';

            // Update preview classes
            const sidebar = layoutPreview.querySelector('.preview-sidebar');
            const navbar = layoutPreview.querySelector('.preview-navbar');
            const footer = layoutPreview.querySelector('.preview-footer');
            const content = layoutPreview.querySelector('.preview-content');

            // Clear existing classes and add new ones
            const elements = [sidebar, navbar, footer, content];
            elements.forEach(element => {
                if (element) {
                    element.className = element.className.replace(/layout-\w+|sidebar-\w+|navbar-\w+|footer-\w+|content-\w+|color-\w+|radius-\w+|shadow-\w+|animation-\w+/g, '');
                }
            });

            // Add new classes
            if (sidebar) {
                sidebar.classList.add(
                    `layout-${layoutType}`,
                    `sidebar-${sidebarStyle}`,
                    `color-${colorScheme}`,
                    `radius-${borderRadius}`,
                    `shadow-${shadowStyle}`
                );
            }

            if (navbar) {
                navbar.classList.add(
                    `layout-${layoutType}`,
                    `navbar-${navbarStyle}`,
                    `color-${colorScheme}`,
                    `radius-${borderRadius}`,
                    `shadow-${shadowStyle}`
                );
            }

            if (footer) {
                footer.classList.add(
                    `layout-${layoutType}`,
                    `footer-${footerStyle}`,
                    `color-${colorScheme}`,
                    `radius-${borderRadius}`,
                    `shadow-${shadowStyle}`
                );
            }

            if (content) {
                content.classList.add(
                    `layout-${layoutType}`,
                    `content-${mainContentStyle}`,
                    `color-${colorScheme}`,
                    `radius-${borderRadius}`,
                    `shadow-${shadowStyle}`
                );
            }
        }

        // Initial preview update
        updatePreview();
    }
});

// Preview function for button
function previewLayout() {
    const layoutPreview = document.getElementById('layoutPreview');
    if (layoutPreview) {
        layoutPreview.style.transform = 'scale(1.05)';
        layoutPreview.style.transition = 'transform 0.3s ease';

        setTimeout(() => {
            layoutPreview.style.transform = 'scale(1)';
        }, 300);
    }
}
</script>

<?php include 'includes/views/layouts/footer.php'; ?>
