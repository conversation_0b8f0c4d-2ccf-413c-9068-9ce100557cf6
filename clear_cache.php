<?php
require_once 'includes/config/database.php';
require_once 'includes/helpers/functions.php';

// Check admin access
$currentUser = getCurrentUser();
if (!$currentUser || $currentUser['role'] !== 'admin') {
    setFlashMessage('danger', 'Aks<PERSON> ditolak! Anda tidak memiliki izin untuk mengakses halaman ini.');
    redirect('dashboard.php');
}

$currentPage = 'clear_cache';
$pageTitle = 'Clear Cache & Temporary Files';

// Function to add notification
function addNotification($type, $title, $message, $source = 'clear_cache') {
    global $pdo, $currentUser;
    try {
        $stmt = $pdo->prepare("INSERT INTO system_notifications (type, title, message, source, user_id) VALUES (?, ?, ?, ?, ?)");
        $stmt->execute([$type, $title, $message, $source, $currentUser['id']]);
    } catch (Exception $e) {
        error_log("Error adding notification: " . $e->getMessage());
    }
}

// formatBytes function is available in functions.php

// Function to delete directory contents
function deleteDirectoryContents($dir) {
    if (!is_dir($dir)) return 0;
    
    $deleted = 0;
    $files = glob($dir . '/*');
    foreach ($files as $file) {
        if (is_file($file)) {
            if (unlink($file)) {
                $deleted++;
            }
        } elseif (is_dir($file)) {
            $deleted += deleteDirectoryContents($file);
            rmdir($file);
        }
    }
    return $deleted;
}

// Cache directories and files to check
$cacheLocations = [
    'session' => [
        'name' => 'PHP Sessions',
        'path' => session_save_path() ?: sys_get_temp_dir(),
        'pattern' => 'sess_*',
        'description' => 'PHP session files'
    ],
    'temp' => [
        'name' => 'Temporary Files',
        'path' => sys_get_temp_dir(),
        'pattern' => 'tmp*',
        'description' => 'System temporary files'
    ],
    'logs' => [
        'name' => 'Log Files',
        'path' => 'logs',
        'pattern' => '*.log',
        'description' => 'Application log files'
    ],
    'uploads_temp' => [
        'name' => 'Upload Temp',
        'path' => 'uploads/temp',
        'pattern' => '*',
        'description' => 'Temporary upload files'
    ]
];

// Handle cache clearing actions
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    try {
        switch ($_POST['action']) {
            case 'clear_php_sessions':
                $sessionPath = session_save_path() ?: sys_get_temp_dir();
                $sessionFiles = glob($sessionPath . '/sess_*');
                $deleted = 0;
                
                foreach ($sessionFiles as $file) {
                    if (is_file($file) && unlink($file)) {
                        $deleted++;
                    }
                }
                
                addNotification('success', 'PHP Sessions Cleared', "Cleared $deleted session files");
                setFlashMessage('success', "Berhasil menghapus $deleted file session");
                break;
                
            case 'clear_temp_files':
                $tempPath = sys_get_temp_dir();
                $tempFiles = glob($tempPath . '/tmp*');
                $deleted = 0;
                
                foreach ($tempFiles as $file) {
                    if (is_file($file) && is_writable($file)) {
                        // Only delete files older than 1 hour
                        if (filemtime($file) < time() - 3600) {
                            if (unlink($file)) {
                                $deleted++;
                            }
                        }
                    }
                }
                
                addNotification('success', 'Temp Files Cleared', "Cleared $deleted temporary files");
                setFlashMessage('success', "Berhasil menghapus $deleted file temporary");
                break;
                
            case 'clear_application_logs':
                $logPath = 'logs';
                $deleted = 0;
                
                if (is_dir($logPath)) {
                    $logFiles = glob($logPath . '/*.log');
                    foreach ($logFiles as $file) {
                        if (is_file($file) && unlink($file)) {
                            $deleted++;
                        }
                    }
                }
                
                addNotification('success', 'Application Logs Cleared', "Cleared $deleted log files");
                setFlashMessage('success', "Berhasil menghapus $deleted file log");
                break;
                
            case 'clear_upload_temp':
                $uploadTempPath = 'uploads/temp';
                $deleted = 0;
                
                if (is_dir($uploadTempPath)) {
                    $deleted = deleteDirectoryContents($uploadTempPath);
                }
                
                addNotification('success', 'Upload Temp Cleared', "Cleared $deleted temporary upload files");
                setFlashMessage('success', "Berhasil menghapus $deleted file upload temporary");
                break;
                
            case 'clear_database_cache':
                // Clear old notifications (older than 30 days)
                $stmt = $pdo->prepare("DELETE FROM system_notifications WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY)");
                $stmt->execute();
                $deletedNotifications = $stmt->rowCount();
                
                // Clear old logs if table exists
                try {
                    $stmt = $pdo->prepare("DELETE FROM system_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL 7 DAY)");
                    $stmt->execute();
                    $deletedLogs = $stmt->rowCount();
                } catch (Exception $e) {
                    $deletedLogs = 0;
                }
                
                $totalDeleted = $deletedNotifications + $deletedLogs;
                addNotification('success', 'Database Cache Cleared', "Cleared $totalDeleted old database records");
                setFlashMessage('success', "Berhasil menghapus $totalDeleted record database lama");
                break;
                
            case 'clear_all_cache':
                $totalDeleted = 0;
                
                // Clear sessions
                $sessionPath = session_save_path() ?: sys_get_temp_dir();
                $sessionFiles = glob($sessionPath . '/sess_*');
                foreach ($sessionFiles as $file) {
                    if (is_file($file) && unlink($file)) {
                        $totalDeleted++;
                    }
                }
                
                // Clear temp files (older than 1 hour)
                $tempPath = sys_get_temp_dir();
                $tempFiles = glob($tempPath . '/tmp*');
                foreach ($tempFiles as $file) {
                    if (is_file($file) && is_writable($file) && filemtime($file) < time() - 3600) {
                        if (unlink($file)) {
                            $totalDeleted++;
                        }
                    }
                }
                
                // Clear application logs
                $logPath = 'logs';
                if (is_dir($logPath)) {
                    $logFiles = glob($logPath . '/*.log');
                    foreach ($logFiles as $file) {
                        if (is_file($file) && unlink($file)) {
                            $totalDeleted++;
                        }
                    }
                }
                
                // Clear database cache
                $stmt = $pdo->prepare("DELETE FROM system_notifications WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY)");
                $stmt->execute();
                $totalDeleted += $stmt->rowCount();
                
                addNotification('success', 'All Cache Cleared', "Comprehensive cache clear completed. Removed $totalDeleted items");
                setFlashMessage('success', "Berhasil menghapus semua cache ($totalDeleted items)");
                break;
                
            case 'restart_session':
                // Regenerate session ID
                session_regenerate_id(true);
                addNotification('info', 'Session Restarted', 'User session has been regenerated');
                setFlashMessage('success', 'Session berhasil di-restart');
                break;
        }
        redirect('clear_cache.php');
    } catch (Exception $e) {
        addNotification('error', 'Cache Clear Error', 'Error during cache clearing: ' . $e->getMessage());
        setFlashMessage('danger', 'Error: ' . $e->getMessage());
    }
}

// Get cache information
$cacheInfo = [];

foreach ($cacheLocations as $key => $location) {
    $info = [
        'name' => $location['name'],
        'description' => $location['description'],
        'files' => 0,
        'size' => 0,
        'status' => 'unknown'
    ];
    
    if (is_dir($location['path'])) {
        $files = glob($location['path'] . '/' . $location['pattern']);
        $info['files'] = count($files);
        
        $totalSize = 0;
        foreach ($files as $file) {
            if (is_file($file)) {
                $totalSize += filesize($file);
            }
        }
        $info['size'] = $totalSize;
        
        if ($info['files'] == 0) {
            $info['status'] = 'clean';
        } elseif ($info['files'] < 10) {
            $info['status'] = 'low';
        } elseif ($info['files'] < 50) {
            $info['status'] = 'medium';
        } else {
            $info['status'] = 'high';
        }
    } else {
        $info['status'] = 'not_found';
    }
    
    $cacheInfo[$key] = $info;
}

// Get database cache info
try {
    $stmt = $pdo->query("SELECT COUNT(*) FROM system_notifications");
    $notificationCount = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM system_notifications WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY)");
    $oldNotificationCount = $stmt->fetchColumn();
    
    $cacheInfo['database'] = [
        'name' => 'Database Cache',
        'description' => 'Old notifications and logs',
        'files' => $oldNotificationCount,
        'size' => 0,
        'status' => $oldNotificationCount > 100 ? 'high' : ($oldNotificationCount > 10 ? 'medium' : 'low')
    ];
} catch (Exception $e) {
    $cacheInfo['database'] = [
        'name' => 'Database Cache',
        'description' => 'Cannot access database',
        'files' => 0,
        'size' => 0,
        'status' => 'error'
    ];
}

include 'includes/views/layouts/header.php';
?>

<div class="container-fluid px-4 py-3">
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-gradient-secondary text-white py-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h5 class="card-title mb-0">
                                <i class="fas fa-broom me-2"></i>
                                Clear Cache & Temporary Files
                            </h5>
                            <p class="mb-0 small opacity-75">Bersihkan cache dan file temporary untuk optimasi performa</p>
                        </div>
                        <div class="d-flex gap-2">
                            <a href="notifications.php" class="btn btn-light btn-sm">
                                <i class="fas fa-bell me-1"></i>Notifications
                            </a>
                            <a href="system_check.php" class="btn btn-light btn-sm">
                                <i class="fas fa-check-circle me-1"></i>Health Check
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body p-4">
                    
                    <!-- Cache Overview -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="mb-3">
                                <i class="fas fa-chart-pie me-2"></i>Cache Overview
                            </h6>
                            <div class="row">
                                <?php foreach ($cacheInfo as $key => $info): ?>
                                    <div class="col-md-6 col-lg-4 mb-3">
                                        <div class="card border-<?= $info['status'] === 'clean' ? 'success' : ($info['status'] === 'high' ? 'danger' : ($info['status'] === 'medium' ? 'warning' : 'secondary')) ?> h-100">
                                            <div class="card-body">
                                                <div class="d-flex justify-content-between align-items-start mb-2">
                                                    <h6 class="card-title mb-0"><?= $info['name'] ?></h6>
                                                    <span class="badge bg-<?= $info['status'] === 'clean' ? 'success' : ($info['status'] === 'high' ? 'danger' : ($info['status'] === 'medium' ? 'warning' : 'secondary')) ?>">
                                                        <?= ucfirst($info['status']) ?>
                                                    </span>
                                                </div>
                                                <p class="small text-muted mb-2"><?= $info['description'] ?></p>
                                                <div class="d-flex justify-content-between">
                                                    <small><strong><?= $info['files'] ?></strong> files</small>
                                                    <?php if ($info['size'] > 0): ?>
                                                        <small><strong><?= formatBytes($info['size']) ?></strong></small>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Cache Clearing Actions -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card border-warning">
                                <div class="card-header bg-warning text-dark">
                                    <h6 class="mb-0">
                                        <i class="fas fa-trash-alt me-2"></i>Individual Cache Clear
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <form method="POST" class="d-grid gap-2">
                                        <button type="submit" name="action" value="clear_php_sessions" class="btn btn-outline-primary btn-sm">
                                            <i class="fas fa-user-clock me-2"></i>Clear PHP Sessions
                                        </button>
                                        <button type="submit" name="action" value="clear_temp_files" class="btn btn-outline-info btn-sm">
                                            <i class="fas fa-file-alt me-2"></i>Clear Temp Files
                                        </button>
                                        <button type="submit" name="action" value="clear_application_logs" class="btn btn-outline-warning btn-sm">
                                            <i class="fas fa-list-alt me-2"></i>Clear Application Logs
                                        </button>
                                        <button type="submit" name="action" value="clear_upload_temp" class="btn btn-outline-secondary btn-sm">
                                            <i class="fas fa-upload me-2"></i>Clear Upload Temp
                                        </button>
                                        <button type="submit" name="action" value="clear_database_cache" class="btn btn-outline-success btn-sm">
                                            <i class="fas fa-database me-2"></i>Clear Database Cache
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border-danger">
                                <div class="card-header bg-danger text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-magic me-2"></i>Comprehensive Actions
                                    </h6>
                                </div>
                                <div class="card-body text-center">
                                    <p class="mb-3">Bersihkan semua cache sekaligus</p>
                                    <form method="POST" class="d-grid gap-2" onsubmit="return confirm('Yakin ingin menghapus semua cache?')">
                                        <button type="submit" name="action" value="clear_all_cache" class="btn btn-danger">
                                            <i class="fas fa-broom me-2"></i>Clear All Cache
                                        </button>
                                        <button type="submit" name="action" value="restart_session" class="btn btn-outline-info">
                                            <i class="fas fa-sync me-2"></i>Restart Session
                                        </button>
                                    </form>
                                    <small class="text-muted d-block mt-2">
                                        Ini akan menghapus semua cache dan file temporary
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Cache Information -->
                    <div class="row">
                        <div class="col-12">
                            <h6 class="mb-3">
                                <i class="fas fa-info-circle me-2"></i>Cache Information
                            </h6>
                            <div class="accordion" id="cacheInfoAccordion">
                                <div class="accordion-item">
                                    <h2 class="accordion-header">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#phpSessions">
                                            PHP Sessions
                                        </button>
                                    </h2>
                                    <div id="phpSessions" class="accordion-collapse collapse" data-bs-parent="#cacheInfoAccordion">
                                        <div class="accordion-body">
                                            <strong>Purpose:</strong> Store user session data<br>
                                            <strong>Safe to clear:</strong> Yes, but will log out all users<br>
                                            <strong>Location:</strong> <?= session_save_path() ?: sys_get_temp_dir() ?>
                                        </div>
                                    </div>
                                </div>
                                <div class="accordion-item">
                                    <h2 class="accordion-header">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#tempFiles">
                                            Temporary Files
                                        </button>
                                    </h2>
                                    <div id="tempFiles" class="accordion-collapse collapse" data-bs-parent="#cacheInfoAccordion">
                                        <div class="accordion-body">
                                            <strong>Purpose:</strong> System temporary files<br>
                                            <strong>Safe to clear:</strong> Yes, only files older than 1 hour<br>
                                            <strong>Location:</strong> <?= sys_get_temp_dir() ?>
                                        </div>
                                    </div>
                                </div>
                                <div class="accordion-item">
                                    <h2 class="accordion-header">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#databaseCache">
                                            Database Cache
                                        </button>
                                    </h2>
                                    <div id="databaseCache" class="accordion-collapse collapse" data-bs-parent="#cacheInfoAccordion">
                                        <div class="accordion-body">
                                            <strong>Purpose:</strong> Old notifications and logs<br>
                                            <strong>Safe to clear:</strong> Yes, removes data older than 30 days<br>
                                            <strong>Impact:</strong> Improves database performance
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Back to Notifications -->
                    <div class="mt-4 text-center">
                        <a href="notifications.php" class="btn btn-outline-primary">
                            <i class="fas fa-arrow-left me-2"></i>Back to Notifications
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/views/layouts/footer.php'; ?>
