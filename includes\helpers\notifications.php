<?php
require_once __DIR__ . '/../config/database.php';

/**
 * Membuat notifikasi baru
 * @param int $user_id ID pengguna
 * @param string $title Judul notifikasi
 * @param string $message Pesan notifikasi
 * @param string $type Tipe notifikasi (success, warning, error, info)
 * @return bool
 */
function createNotification($user_id, $title, $message, $type = 'info') {
    global $pdo;
    try {
        $stmt = $pdo->prepare("
            INSERT INTO notifications (user_id, title, message, type, created_at)
            VALUES (?, ?, ?, ?, NOW())
        ");
        return $stmt->execute([$user_id, $title, $message, $type]);
    } catch (PDOException $e) {
        error_log("Error creating notification: " . $e->getMessage());
        return false;
    }
}

/**
 * Mendapatkan semua notifikasi untuk pengguna tertentu
 * @param int $user_id ID pengguna
 * @param int $limit Jumlah notifikasi yang akan diambil
 * @return array
 */
function getNotifications($user_id, $limit = 10) {
    global $pdo;
    try {
        $stmt = $pdo->prepare("
            SELECT * FROM notifications 
            WHERE user_id = ? 
            ORDER BY created_at DESC 
            LIMIT ?
        ");
        $stmt->execute([$user_id, $limit]);
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        error_log("Error getting notifications: " . $e->getMessage());
        return [];
    }
}

/**
 * Mendapatkan notifikasi yang belum dibaca
 * @param int $user_id ID pengguna
 * @return array
 */
function getUnreadNotifications($user_id) {
    global $pdo;
    try {
        $stmt = $pdo->prepare("
            SELECT * FROM notifications 
            WHERE user_id = ? AND is_read = 0 
            ORDER BY created_at DESC
        ");
        $stmt->execute([$user_id]);
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        error_log("Error getting unread notifications: " . $e->getMessage());
        return [];
    }
}

/**
 * Menandai notifikasi sebagai telah dibaca
 * @param int $notification_id ID notifikasi
 * @return bool
 */
function markNotificationAsRead($notification_id) {
    global $pdo;
    try {
        $stmt = $pdo->prepare("
            UPDATE notifications 
            SET is_read = 1, read_at = NOW() 
            WHERE id = ?
        ");
        return $stmt->execute([$notification_id]);
    } catch (PDOException $e) {
        error_log("Error marking notification as read: " . $e->getMessage());
        return false;
    }
}

/**
 * Menandai semua notifikasi pengguna sebagai telah dibaca
 * @param int $user_id ID pengguna
 * @return bool
 */
function markAllNotificationsAsRead($user_id) {
    global $pdo;
    try {
        $stmt = $pdo->prepare("
            UPDATE notifications 
            SET is_read = 1, read_at = NOW() 
            WHERE user_id = ? AND is_read = 0
        ");
        return $stmt->execute([$user_id]);
    } catch (PDOException $e) {
        error_log("Error marking all notifications as read: " . $e->getMessage());
        return false;
    }
}

/**
 * Menghapus notifikasi
 * @param int $notification_id ID notifikasi
 * @return bool
 */
function deleteNotification($notification_id) {
    global $pdo;
    try {
        $stmt = $pdo->prepare("DELETE FROM notifications WHERE id = ?");
        return $stmt->execute([$notification_id]);
    } catch (PDOException $e) {
        error_log("Error deleting notification: " . $e->getMessage());
        return false;
    }
}

/**
 * Menghapus semua notifikasi pengguna
 * @param int $user_id ID pengguna
 * @return bool
 */
function deleteAllNotifications($user_id) {
    global $pdo;
    try {
        $stmt = $pdo->prepare("DELETE FROM notifications WHERE user_id = ?");
        return $stmt->execute([$user_id]);
    } catch (PDOException $e) {
        error_log("Error deleting all notifications: " . $e->getMessage());
        return false;
    }
}

/**
 * Set flash message
 * @param string $type Message type (success, danger, warning, info)
 * @param string $message Message content
 */
function setFlashMessage($type, $message) {
    if (!isset($_SESSION)) {
        session_start();
    }
    $_SESSION['flash_message'] = [
        'type' => $type,
        'message' => $message
    ];
}

/**
 * Get and clear flash message
 * @return array|null
 */
function getFlashMessage() {
    if (!isset($_SESSION)) {
        session_start();
    }

    if (isset($_SESSION['flash_message'])) {
        $message = $_SESSION['flash_message'];
        unset($_SESSION['flash_message']);
        return $message;
    }

    return null;
}

/**
 * Get unread notification count
 * @param int $user_id User ID
 * @return int
 */
function getUnreadNotificationCount($user_id) {
    global $pdo;

    try {
        $stmt = $pdo->prepare("
            SELECT COUNT(*) FROM notifications
            WHERE user_id = ? AND is_read = 0
        ");
        $stmt->execute([$user_id]);
        return $stmt->fetchColumn();

    } catch (PDOException $e) {
        error_log("Error getting unread notification count: " . $e->getMessage());
        return 0;
    }
}

/**
 * Create system notification for all users
 * @param string $title Notification title
 * @param string $message Notification message
 * @param string $type Notification type
 * @param string $role Target role (optional, 'all' for everyone)
 * @return bool
 */
function createSystemNotification($title, $message, $type = 'info', $role = 'all') {
    global $pdo;

    try {
        $sql = "SELECT id FROM users";
        $params = [];

        if ($role !== 'all') {
            $sql .= " WHERE role = ?";
            $params[] = $role;
        }

        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        $users = $stmt->fetchAll();

        $success = true;
        foreach ($users as $user) {
            if (!createNotification($user['id'], $title, $message, $type)) {
                $success = false;
            }
        }

        return $success;

    } catch (PDOException $e) {
        error_log("Error creating system notification: " . $e->getMessage());
        return false;
    }
}