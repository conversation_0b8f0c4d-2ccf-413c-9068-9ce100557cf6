<?php
/**
 * Export/Import Helper Functions
 * 
 * This file contains functions for data export and import
 */

// Note: PhpSpreadsheet requires composer install
// For now, we'll use basic CSV export/import functionality

/**
 * Export data to CSV
 * @param string $table Table name
 * @param array $data Data to export
 * @param array $headers Column headers
 * @param string $filename Optional filename
 * @return array Result with success status and file path
 */
function exportToCSV($table, $data, $headers, $filename = null) {
    try {
        // Create exports directory if not exists
        if (!is_dir('exports')) {
            mkdir('exports', 0755, true);
        }

        // Generate filename if not provided
        if (!$filename) {
            $filename = $table . '_export_' . date('Y-m-d_H-i-s') . '.csv';
        }

        $filePath = 'exports/' . $filename;

        // Open file for writing
        $file = fopen($filePath, 'w');

        // Add BOM for UTF-8
        fwrite($file, "\xEF\xBB\xBF");

        // Write headers
        fputcsv($file, $headers);

        // Write data
        foreach ($data as $record) {
            fputcsv($file, $record);
        }

        fclose($file);

        // Log export
        logSystemEvent("Data exported to CSV: $filename", 'info', [
            'table' => $table,
            'filename' => $filename,
            'records' => count($data)
        ]);

        return [
            'success' => true,
            'message' => 'Data exported successfully',
            'filename' => $filename,
            'path' => $filePath,
            'records' => count($data)
        ];

    } catch (Exception $e) {
        error_log("Export error: " . $e->getMessage());
        return [
            'success' => false,
            'message' => 'Export failed: ' . $e->getMessage()
        ];
    }
}

/**
 * Export data to Excel (alias for CSV for now)
 * @param string $table Table name
 * @param array $data Data to export
 * @param array $headers Column headers
 * @param string $filename Optional filename
 * @return array Result with success status and file path
 */
function exportToExcel($table, $data, $headers, $filename = null) {
    // For now, use CSV export
    if (!$filename) {
        $filename = $table . '_export_' . date('Y-m-d_H-i-s') . '.csv';
    }
    return exportToCSV($table, $data, $headers, $filename);
}
    try {
        // Create exports directory if not exists
        if (!is_dir('exports')) {
            mkdir('exports', 0755, true);
        }
        
        // Generate filename if not provided
        if (!$filename) {
            $filename = $table . '_export_' . date('Y-m-d_H-i-s') . '.xlsx';
        }
        
        $filePath = 'exports/' . $filename;
        
        // Create new spreadsheet
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        
        // Set headers
        $col = 'A';
        foreach ($headers as $header) {
            $sheet->setCellValue($col . '1', $header);
            $sheet->getStyle($col . '1')->getFont()->setBold(true);
            $sheet->getColumnDimension($col)->setAutoSize(true);
            $col++;
        }
        
        // Add data
        $row = 2;
        foreach ($data as $record) {
            $col = 'A';
            foreach ($record as $value) {
                $sheet->setCellValue($col . $row, $value);
                $col++;
            }
            $row++;
        }
        
        // Save file
        $writer = new Xlsx($spreadsheet);
        $writer->save($filePath);
        
        // Log export
        logSystemEvent("Data exported to Excel: $filename", 'info', [
            'table' => $table,
            'filename' => $filename,
            'records' => count($data)
        ]);
        
        return [
            'success' => true,
            'message' => 'Data exported successfully',
            'filename' => $filename,
            'path' => $filePath,
            'records' => count($data)
        ];
        
    } catch (Exception $e) {
        error_log("Export error: " . $e->getMessage());
        return [
            'success' => false,
            'message' => 'Export failed: ' . $e->getMessage()
        ];
    }
}

/**
 * Export transactions to Excel
 * @param int $userId User ID
 * @param array $filters Optional filters
 * @return array Result
 */
function exportTransactions($userId, $filters = []) {
    global $pdo;
    
    try {
        $where = ["t.user_id = ?"];
        $params = [$userId];
        
        // Apply filters
        if (!empty($filters['start_date'])) {
            $where[] = "t.tanggal >= ?";
            $params[] = $filters['start_date'];
        }
        
        if (!empty($filters['end_date'])) {
            $where[] = "t.tanggal <= ?";
            $params[] = $filters['end_date'];
        }
        
        if (!empty($filters['kategori_id'])) {
            $where[] = "t.kategori_id = ?";
            $params[] = $filters['kategori_id'];
        }
        
        $whereClause = implode(" AND ", $where);
        
        $stmt = $pdo->prepare("
            SELECT 
                t.tanggal,
                k.nama as kategori,
                t.jenis,
                t.jumlah,
                t.keterangan,
                t.created_at
            FROM transaksi t
            LEFT JOIN kategori k ON t.kategori_id = k.id
            WHERE $whereClause
            ORDER BY t.tanggal DESC, t.created_at DESC
        ");
        
        $stmt->execute($params);
        $data = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Format data for export
        $exportData = [];
        foreach ($data as $row) {
            $exportData[] = [
                date('d/m/Y', strtotime($row['tanggal'])),
                $row['kategori'],
                ucfirst($row['jenis']),
                number_format($row['jumlah'], 0, ',', '.'),
                $row['keterangan'],
                date('d/m/Y H:i', strtotime($row['created_at']))
            ];
        }
        
        $headers = ['Tanggal', 'Kategori', 'Jenis', 'Jumlah', 'Keterangan', 'Dibuat'];
        
        return exportToCSV('transaksi', $exportData, $headers);
        
    } catch (Exception $e) {
        error_log("Export transactions error: " . $e->getMessage());
        return [
            'success' => false,
            'message' => 'Export failed: ' . $e->getMessage()
        ];
    }
}

/**
 * Export suppliers to Excel
 * @param int $userId User ID
 * @return array Result
 */
function exportSuppliers($userId) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("
            SELECT 
                nama_supplier,
                kontak,
                email,
                alamat,
                status,
                created_at
            FROM supplier
            WHERE user_id = ?
            ORDER BY nama_supplier ASC
        ");
        
        $stmt->execute([$userId]);
        $data = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Format data for export
        $exportData = [];
        foreach ($data as $row) {
            $exportData[] = [
                $row['nama_supplier'],
                $row['kontak'],
                $row['email'],
                $row['alamat'],
                ucfirst($row['status']),
                date('d/m/Y H:i', strtotime($row['created_at']))
            ];
        }
        
        $headers = ['Nama Supplier', 'Kontak', 'Email', 'Alamat', 'Status', 'Dibuat'];
        
        return exportToCSV('supplier', $exportData, $headers);
        
    } catch (Exception $e) {
        error_log("Export suppliers error: " . $e->getMessage());
        return [
            'success' => false,
            'message' => 'Export failed: ' . $e->getMessage()
        ];
    }
}

/**
 * Export inventory to Excel
 * @param int $userId User ID
 * @return array Result
 */
function exportInventory($userId) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("
            SELECT 
                i.nama_produk,
                i.kategori,
                i.stok_awal,
                i.stok_masuk,
                i.stok_keluar,
                i.stok_saat_ini,
                i.harga_beli,
                i.harga_jual,
                s.nama_supplier,
                i.status,
                i.created_at
            FROM inventory i
            LEFT JOIN supplier s ON i.supplier_id = s.id
            WHERE i.user_id = ?
            ORDER BY i.nama_produk ASC
        ");
        
        $stmt->execute([$userId]);
        $data = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Format data for export
        $exportData = [];
        foreach ($data as $row) {
            $exportData[] = [
                $row['nama_produk'],
                $row['kategori'],
                $row['stok_awal'],
                $row['stok_masuk'],
                $row['stok_keluar'],
                $row['stok_saat_ini'],
                number_format($row['harga_beli'], 0, ',', '.'),
                number_format($row['harga_jual'], 0, ',', '.'),
                $row['nama_supplier'],
                ucfirst($row['status']),
                date('d/m/Y H:i', strtotime($row['created_at']))
            ];
        }
        
        $headers = [
            'Nama Produk', 'Kategori', 'Stok Awal', 'Stok Masuk', 
            'Stok Keluar', 'Stok Saat Ini', 'Harga Beli', 'Harga Jual',
            'Supplier', 'Status', 'Dibuat'
        ];
        
        return exportToCSV('inventory', $exportData, $headers);
        
    } catch (Exception $e) {
        error_log("Export inventory error: " . $e->getMessage());
        return [
            'success' => false,
            'message' => 'Export failed: ' . $e->getMessage()
        ];
    }
}

/**
 * Import data from Excel
 * @param string $filePath Path to Excel file
 * @param string $table Target table
 * @param array $mapping Column mapping
 * @param int $userId User ID
 * @return array Result
 */
function importFromExcel($filePath, $table, $mapping, $userId) {
    global $pdo;
    
    try {
        if (!file_exists($filePath)) {
            return [
                'success' => false,
                'message' => 'File not found'
            ];
        }
        
        $spreadsheet = IOFactory::load($filePath);
        $worksheet = $spreadsheet->getActiveSheet();
        $rows = $worksheet->toArray();
        
        // Skip header row
        array_shift($rows);
        
        $successCount = 0;
        $errorCount = 0;
        $errors = [];
        
        foreach ($rows as $index => $row) {
            if (empty(array_filter($row))) continue; // Skip empty rows
            
            try {
                // Map row data to database columns
                $data = ['user_id' => $userId];
                foreach ($mapping as $excelCol => $dbCol) {
                    $data[$dbCol] = $row[$excelCol] ?? '';
                }
                
                // Insert data
                $columns = implode(', ', array_keys($data));
                $placeholders = ':' . implode(', :', array_keys($data));
                
                $stmt = $pdo->prepare("INSERT INTO $table ($columns) VALUES ($placeholders)");
                $stmt->execute($data);
                
                $successCount++;
                
            } catch (Exception $e) {
                $errorCount++;
                $errors[] = "Row " . ($index + 2) . ": " . $e->getMessage();
            }
        }
        
        // Log import
        logSystemEvent("Data imported from Excel", 'info', [
            'table' => $table,
            'file' => basename($filePath),
            'success_count' => $successCount,
            'error_count' => $errorCount
        ]);
        
        return [
            'success' => true,
            'message' => "Import completed: $successCount success, $errorCount errors",
            'success_count' => $successCount,
            'error_count' => $errorCount,
            'errors' => $errors
        ];
        
    } catch (Exception $e) {
        error_log("Import error: " . $e->getMessage());
        return [
            'success' => false,
            'message' => 'Import failed: ' . $e->getMessage()
        ];
    }
}

/**
 * Get list of export files
 * @return array List of export files
 */
function getExportList() {
    $exports = [];
    
    if (is_dir('exports')) {
        $files = scandir('exports');
        
        foreach ($files as $file) {
            if (pathinfo($file, PATHINFO_EXTENSION) === 'xlsx') {
                $filePath = 'exports/' . $file;
                $exports[] = [
                    'filename' => $file,
                    'size' => filesize($filePath),
                    'size_formatted' => formatBytes(filesize($filePath)),
                    'created' => filemtime($filePath),
                    'created_formatted' => date('d/m/Y H:i:s', filemtime($filePath))
                ];
            }
        }
        
        // Sort by creation time (newest first)
        usort($exports, function($a, $b) {
            return $b['created'] - $a['created'];
        });
    }
    
    return $exports;
}
?>
