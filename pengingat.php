<?php
require_once 'includes/config/database.php';
require_once 'includes/helpers/functions.php';
require_once 'includes/helpers/notifications.php';

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isLoggedIn()) {
    setFlashMessage('danger', 'Silakan login terlebih dahulu');
    redirect('/keuangan/login.php');
}

$currentUser = getCurrentUser();
$currentPage = 'pengingat';

// Create reminders table if not exists
try {
    $pdo->exec("CREATE TABLE IF NOT EXISTS pengingat (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        judul VARCHAR(255) NOT NULL,
        deskripsi TEXT,
        tanggal_pengingat DATE NOT NULL,
        waktu_pengingat TIME,
        jenis <PERSON>('tagihan', 'cicilan', 'target', 'investasi', 'lainnya') NOT NULL,
        jumlah DECIMAL(15,2),
        status ENUM('aktif', 'selesai', 'terlewat') DEFAULT 'aktif',
        pengulangan ENUM('sekali', 'harian', 'mingguan', 'bulanan', 'tahunan') DEFAULT 'sekali',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    )");
} catch (PDOException $e) {
    error_log("Error creating pengingat table: " . $e->getMessage());
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        try {
            switch ($_POST['action']) {
                case 'add':
                    $errors = [];
                    
                    if (empty($_POST['judul'])) {
                        $errors[] = 'Judul harus diisi';
                    }
                    
                    if (empty($_POST['tanggal_pengingat'])) {
                        $errors[] = 'Tanggal pengingat harus diisi';
                    }
                    
                    if (empty($_POST['jenis'])) {
                        $errors[] = 'Jenis pengingat harus dipilih';
                    }
                    
                    if (empty($errors)) {
                        $jumlah = !empty($_POST['jumlah']) ? str_replace(['.', ','], '', $_POST['jumlah']) : null;
                        
                        $stmt = $pdo->prepare("INSERT INTO pengingat (user_id, judul, deskripsi, tanggal_pengingat, waktu_pengingat, jenis, jumlah, pengulangan) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
                        $result = $stmt->execute([
                            $currentUser['id'],
                            $_POST['judul'],
                            $_POST['deskripsi'],
                            $_POST['tanggal_pengingat'],
                            $_POST['waktu_pengingat'] ?: null,
                            $_POST['jenis'],
                            $jumlah,
                            $_POST['pengulangan']
                        ]);
                        
                        if ($result) {
                            setFlashMessage('success', 'Pengingat berhasil ditambahkan');
                        } else {
                            setFlashMessage('danger', 'Gagal menambahkan pengingat');
                        }
                    } else {
                        setFlashMessage('danger', implode('<br>', $errors));
                    }
                    break;

                case 'update':
                    if (!empty($_POST['id'])) {
                        $jumlah = !empty($_POST['jumlah']) ? str_replace(['.', ','], '', $_POST['jumlah']) : null;
                        
                        $stmt = $pdo->prepare("UPDATE pengingat SET judul = ?, deskripsi = ?, tanggal_pengingat = ?, waktu_pengingat = ?, jenis = ?, jumlah = ?, pengulangan = ? WHERE id = ? AND user_id = ?");
                        $result = $stmt->execute([
                            $_POST['judul'],
                            $_POST['deskripsi'],
                            $_POST['tanggal_pengingat'],
                            $_POST['waktu_pengingat'] ?: null,
                            $_POST['jenis'],
                            $jumlah,
                            $_POST['pengulangan'],
                            $_POST['id'],
                            $currentUser['id']
                        ]);
                        
                        if ($result) {
                            setFlashMessage('success', 'Pengingat berhasil diperbarui');
                        } else {
                            setFlashMessage('danger', 'Gagal memperbarui pengingat');
                        }
                    }
                    break;

                case 'delete':
                    if (!empty($_POST['id'])) {
                        $stmt = $pdo->prepare("DELETE FROM pengingat WHERE id = ? AND user_id = ?");
                        $result = $stmt->execute([$_POST['id'], $currentUser['id']]);
                        
                        if ($result) {
                            setFlashMessage('success', 'Pengingat berhasil dihapus');
                        } else {
                            setFlashMessage('danger', 'Gagal menghapus pengingat');
                        }
                    }
                    break;

                case 'mark_done':
                    if (!empty($_POST['id'])) {
                        $stmt = $pdo->prepare("UPDATE pengingat SET status = 'selesai' WHERE id = ? AND user_id = ?");
                        $result = $stmt->execute([$_POST['id'], $currentUser['id']]);
                        
                        if ($result) {
                            setFlashMessage('success', 'Pengingat ditandai sebagai selesai');
                        } else {
                            setFlashMessage('danger', 'Gagal memperbarui status pengingat');
                        }
                    }
                    break;
            }
        } catch (PDOException $e) {
            setFlashMessage('danger', 'Terjadi kesalahan. Silakan coba lagi.');
        }
        redirect('/keuangan/pengingat.php');
    }
}

// Get reminders with filters
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$perPage = 10;
$offset = ($page - 1) * $perPage;

$where = ["user_id = ?"];
$params = [$currentUser['id']];

if (!empty($_GET['status'])) {
    $where[] = "status = ?";
    $params[] = $_GET['status'];
}

if (!empty($_GET['jenis'])) {
    $where[] = "jenis = ?";
    $params[] = $_GET['jenis'];
}

$whereClause = implode(" AND ", $where);

// Get total records
$stmt = $pdo->prepare("SELECT COUNT(*) as total FROM pengingat WHERE $whereClause");
$stmt->execute($params);
$totalRecords = $stmt->fetch()['total'];
$totalPages = ceil($totalRecords / $perPage);

// Get reminders
$stmt = $pdo->prepare("
    SELECT * FROM pengingat 
    WHERE $whereClause 
    ORDER BY tanggal_pengingat ASC, waktu_pengingat ASC 
    LIMIT ? OFFSET ?
");

$paginationParams = $params;
$paginationParams[] = $perPage;
$paginationParams[] = $offset;

$stmt->execute($paginationParams);
$reminders = $stmt->fetchAll();

// Update overdue reminders
$stmt = $pdo->prepare("UPDATE pengingat SET status = 'terlewat' WHERE tanggal_pengingat < CURDATE() AND status = 'aktif' AND user_id = ?");
$stmt->execute([$currentUser['id']]);

// Get upcoming reminders (next 7 days)
$stmt = $pdo->prepare("
    SELECT * FROM pengingat 
    WHERE user_id = ? AND status = 'aktif' AND tanggal_pengingat BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 7 DAY)
    ORDER BY tanggal_pengingat ASC, waktu_pengingat ASC
    LIMIT 5
");
$stmt->execute([$currentUser['id']]);
$upcomingReminders = $stmt->fetchAll();

// Get statistics
$stmt = $pdo->prepare("
    SELECT 
        COUNT(CASE WHEN status = 'aktif' THEN 1 END) as aktif,
        COUNT(CASE WHEN status = 'selesai' THEN 1 END) as selesai,
        COUNT(CASE WHEN status = 'terlewat' THEN 1 END) as terlewat,
        COUNT(CASE WHEN tanggal_pengingat = CURDATE() AND status = 'aktif' THEN 1 END) as hari_ini
    FROM pengingat 
    WHERE user_id = ?
");
$stmt->execute([$currentUser['id']]);
$stats = $stmt->fetch();

// Include header
include 'includes/views/layouts/header.php';
?>

<div class="container-fluid py-4">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-1">Pengingat Keuangan</h1>
            <p class="text-muted mb-0">Kelola pengingat untuk tagihan, cicilan, dan target keuangan</p>
        </div>
        <button type="button" class="btn btn-primary d-flex align-items-center" data-bs-toggle="modal" data-bs-target="#addReminderModal">
            <i class="fas fa-plus me-2"></i>Tambah Pengingat
        </button>
    </div>

    <!-- Statistics Cards -->
    <div class="row g-3 mb-4">
        <div class="col-md-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted mb-1">Aktif</h6>
                            <h3 class="mb-0 text-primary"><?= $stats['aktif'] ?? 0 ?></h3>
                        </div>
                        <div class="bg-primary bg-opacity-10 p-3 rounded">
                            <i class="fas fa-bell text-primary"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted mb-1">Hari Ini</h6>
                            <h3 class="mb-0 text-warning"><?= $stats['hari_ini'] ?? 0 ?></h3>
                        </div>
                        <div class="bg-warning bg-opacity-10 p-3 rounded">
                            <i class="fas fa-calendar-day text-warning"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted mb-1">Selesai</h6>
                            <h3 class="mb-0 text-success"><?= $stats['selesai'] ?? 0 ?></h3>
                        </div>
                        <div class="bg-success bg-opacity-10 p-3 rounded">
                            <i class="fas fa-check-circle text-success"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted mb-1">Terlewat</h6>
                            <h3 class="mb-0 text-danger"><?= $stats['terlewat'] ?? 0 ?></h3>
                        </div>
                        <div class="bg-danger bg-opacity-10 p-3 rounded">
                            <i class="fas fa-exclamation-triangle text-danger"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Upcoming Reminders -->
        <div class="col-lg-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0"><i class="fas fa-clock me-2"></i>Pengingat Mendatang</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($upcomingReminders)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-calendar-check fa-3x text-muted mb-3"></i>
                            <p class="text-muted mb-0">Tidak ada pengingat dalam 7 hari ke depan</p>
                        </div>
                    <?php else: ?>
                        <?php foreach ($upcomingReminders as $reminder): ?>
                        <div class="reminder-item border-bottom pb-3 mb-3">
                            <div class="d-flex justify-content-between align-items-start">
                                <div class="flex-grow-1">
                                    <h6 class="mb-1"><?= htmlspecialchars($reminder['judul']) ?></h6>
                                    <div class="d-flex align-items-center mb-1">
                                        <span class="badge bg-<?= $reminder['jenis'] === 'tagihan' ? 'danger' : ($reminder['jenis'] === 'cicilan' ? 'warning' : ($reminder['jenis'] === 'target' ? 'success' : 'info')) ?> me-2">
                                            <?= ucfirst($reminder['jenis']) ?>
                                        </span>
                                        <small class="text-muted">
                                            <?= formatTanggal($reminder['tanggal_pengingat']) ?>
                                            <?php if ($reminder['waktu_pengingat']): ?>
                                                <?= date('H:i', strtotime($reminder['waktu_pengingat'])) ?>
                                            <?php endif; ?>
                                        </small>
                                    </div>
                                    <?php if ($reminder['jumlah']): ?>
                                        <div class="fw-bold text-primary"><?= formatRupiah($reminder['jumlah']) ?></div>
                                    <?php endif; ?>
                                </div>
                                <button type="button" class="btn btn-sm btn-success" onclick="markDone(<?= $reminder['id'] ?>)" title="Tandai Selesai">
                                    <i class="fas fa-check"></i>
                                </button>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- All Reminders -->
        <div class="col-lg-8">
            <!-- Filter Section -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-body">
                    <form action="" method="GET" class="row g-3">
                        <div class="col-md-4">
                            <label class="form-label">Status</label>
                            <select name="status" class="form-select">
                                <option value="">Semua Status</option>
                                <option value="aktif" <?= (isset($_GET['status']) && $_GET['status'] == 'aktif') ? 'selected' : '' ?>>Aktif</option>
                                <option value="selesai" <?= (isset($_GET['status']) && $_GET['status'] == 'selesai') ? 'selected' : '' ?>>Selesai</option>
                                <option value="terlewat" <?= (isset($_GET['status']) && $_GET['status'] == 'terlewat') ? 'selected' : '' ?>>Terlewat</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">Jenis</label>
                            <select name="jenis" class="form-select">
                                <option value="">Semua Jenis</option>
                                <option value="tagihan" <?= (isset($_GET['jenis']) && $_GET['jenis'] == 'tagihan') ? 'selected' : '' ?>>Tagihan</option>
                                <option value="cicilan" <?= (isset($_GET['jenis']) && $_GET['jenis'] == 'cicilan') ? 'selected' : '' ?>>Cicilan</option>
                                <option value="target" <?= (isset($_GET['jenis']) && $_GET['jenis'] == 'target') ? 'selected' : '' ?>>Target</option>
                                <option value="investasi" <?= (isset($_GET['jenis']) && $_GET['jenis'] == 'investasi') ? 'selected' : '' ?>>Investasi</option>
                                <option value="lainnya" <?= (isset($_GET['jenis']) && $_GET['jenis'] == 'lainnya') ? 'selected' : '' ?>>Lainnya</option>
                            </select>
                        </div>
                        <div class="col-12 text-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-filter me-2"></i>Filter
                            </button>
                            <a href="/keuangan/pengingat.php" class="btn btn-light">
                                <i class="fas fa-sync me-2"></i>Reset
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Reminders Table -->
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover align-middle">
                            <thead>
                                <tr>
                                    <th>Pengingat</th>
                                    <th>Tanggal & Waktu</th>
                                    <th class="text-end">Jumlah</th>
                                    <th class="text-center">Status</th>
                                    <th class="text-center">Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($reminders)): ?>
                                <tr>
                                    <td colspan="5" class="text-center py-5">
                                        <div class="text-muted">
                                            <i class="fas fa-bell fa-3x mb-3"></i>
                                            <p class="mb-0">Belum ada pengingat</p>
                                        </div>
                                    </td>
                                </tr>
                                <?php else: ?>
                                <?php foreach ($reminders as $reminder): ?>
                                <tr>
                                    <td>
                                        <div>
                                            <div class="fw-bold"><?= htmlspecialchars($reminder['judul']) ?></div>
                                            <div class="d-flex align-items-center mt-1">
                                                <span class="badge bg-<?= $reminder['jenis'] === 'tagihan' ? 'danger' : ($reminder['jenis'] === 'cicilan' ? 'warning' : ($reminder['jenis'] === 'target' ? 'success' : 'info')) ?> me-2">
                                                    <?= ucfirst($reminder['jenis']) ?>
                                                </span>
                                                <?php if ($reminder['pengulangan'] !== 'sekali'): ?>
                                                    <span class="badge bg-secondary"><?= ucfirst($reminder['pengulangan']) ?></span>
                                                <?php endif; ?>
                                            </div>
                                            <?php if ($reminder['deskripsi']): ?>
                                                <small class="text-muted"><?= htmlspecialchars($reminder['deskripsi']) ?></small>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td>
                                        <div><?= formatTanggal($reminder['tanggal_pengingat']) ?></div>
                                        <?php if ($reminder['waktu_pengingat']): ?>
                                            <small class="text-muted"><?= date('H:i', strtotime($reminder['waktu_pengingat'])) ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td class="text-end">
                                        <?php if ($reminder['jumlah']): ?>
                                            <span class="fw-bold"><?= formatRupiah($reminder['jumlah']) ?></span>
                                        <?php else: ?>
                                            <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="text-center">
                                        <span class="badge bg-<?= $reminder['status'] === 'aktif' ? 'primary' : ($reminder['status'] === 'selesai' ? 'success' : 'danger') ?>">
                                            <?= ucfirst($reminder['status']) ?>
                                        </span>
                                    </td>
                                    <td class="text-center">
                                        <div class="btn-group">
                                            <?php if ($reminder['status'] === 'aktif'): ?>
                                            <button type="button" class="btn btn-sm btn-success" onclick="markDone(<?= $reminder['id'] ?>)" title="Tandai Selesai">
                                                <i class="fas fa-check"></i>
                                            </button>
                                            <?php endif; ?>
                                            <button type="button" class="btn btn-sm btn-light" onclick="editReminder(
                                                '<?= $reminder['id'] ?>',
                                                '<?= htmlspecialchars($reminder['judul']) ?>',
                                                '<?= htmlspecialchars($reminder['deskripsi']) ?>',
                                                '<?= $reminder['tanggal_pengingat'] ?>',
                                                '<?= $reminder['waktu_pengingat'] ?>',
                                                '<?= $reminder['jenis'] ?>',
                                                '<?= $reminder['jumlah'] ?>',
                                                '<?= $reminder['pengulangan'] ?>'
                                            )">
                                                <i class="fas fa-edit text-primary"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-light" onclick="deleteReminder(<?= $reminder['id'] ?>)">
                                                <i class="fas fa-trash text-danger"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <?php if ($totalPages > 1): ?>
                    <nav class="mt-4">
                        <ul class="pagination justify-content-center">
                            <?php if ($page > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?= $page - 1 ?><?= isset($_GET['status']) ? '&status='.$_GET['status'] : '' ?><?= isset($_GET['jenis']) ? '&jenis='.$_GET['jenis'] : '' ?>">
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            </li>
                            <?php endif; ?>
                            
                            <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                            <li class="page-item <?= $i === $page ? 'active' : '' ?>">
                                <a class="page-link" href="?page=<?= $i ?><?= isset($_GET['status']) ? '&status='.$_GET['status'] : '' ?><?= isset($_GET['jenis']) ? '&jenis='.$_GET['jenis'] : '' ?>"><?= $i ?></a>
                            </li>
                            <?php endfor; ?>
                            
                            <?php if ($page < $totalPages): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?= $page + 1 ?><?= isset($_GET['status']) ? '&status='.$_GET['status'] : '' ?><?= isset($_GET['jenis']) ? '&jenis='.$_GET['jenis'] : '' ?>">
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Reminder Modal -->
<div class="modal fade" id="addReminderModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header border-0">
                <h5 class="modal-title">Tambah Pengingat</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="" method="POST" class="needs-validation" novalidate>
                <div class="modal-body">
                    <input type="hidden" name="action" value="add">

                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label class="form-label">Judul Pengingat</label>
                                <input type="text" name="judul" class="form-control" required placeholder="Contoh: Bayar Tagihan Listrik">
                                <div class="invalid-feedback">Judul harus diisi</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Jenis</label>
                                <select name="jenis" class="form-select" required>
                                    <option value="">Pilih Jenis</option>
                                    <option value="tagihan">Tagihan</option>
                                    <option value="cicilan">Cicilan</option>
                                    <option value="target">Target</option>
                                    <option value="investasi">Investasi</option>
                                    <option value="lainnya">Lainnya</option>
                                </select>
                                <div class="invalid-feedback">Jenis harus dipilih</div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Deskripsi</label>
                        <textarea name="deskripsi" class="form-control" rows="2" placeholder="Deskripsi tambahan (opsional)"></textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Tanggal Pengingat</label>
                                <input type="date" name="tanggal_pengingat" class="form-control" required>
                                <div class="invalid-feedback">Tanggal pengingat harus diisi</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Waktu Pengingat</label>
                                <input type="time" name="waktu_pengingat" class="form-control">
                                <small class="text-muted">Opsional</small>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Jumlah</label>
                                <div class="input-group">
                                    <span class="input-group-text">Rp</span>
                                    <input type="text" name="jumlah" class="form-control number-format" placeholder="Opsional">
                                </div>
                                <small class="text-muted">Kosongkan jika tidak ada nominal</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Pengulangan</label>
                                <select name="pengulangan" class="form-select">
                                    <option value="sekali">Sekali</option>
                                    <option value="harian">Harian</option>
                                    <option value="mingguan">Mingguan</option>
                                    <option value="bulanan">Bulanan</option>
                                    <option value="tahunan">Tahunan</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer border-0">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">Simpan Pengingat</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Reminder Modal -->
<div class="modal fade" id="editReminderModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header border-0">
                <h5 class="modal-title">Edit Pengingat</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="" method="POST" class="needs-validation" novalidate>
                <div class="modal-body">
                    <input type="hidden" name="action" value="update">
                    <input type="hidden" name="id" id="edit_id">

                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label class="form-label">Judul Pengingat</label>
                                <input type="text" name="judul" id="edit_judul" class="form-control" required>
                                <div class="invalid-feedback">Judul harus diisi</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Jenis</label>
                                <select name="jenis" id="edit_jenis" class="form-select" required>
                                    <option value="tagihan">Tagihan</option>
                                    <option value="cicilan">Cicilan</option>
                                    <option value="target">Target</option>
                                    <option value="investasi">Investasi</option>
                                    <option value="lainnya">Lainnya</option>
                                </select>
                                <div class="invalid-feedback">Jenis harus dipilih</div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Deskripsi</label>
                        <textarea name="deskripsi" id="edit_deskripsi" class="form-control" rows="2"></textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Tanggal Pengingat</label>
                                <input type="date" name="tanggal_pengingat" id="edit_tanggal_pengingat" class="form-control" required>
                                <div class="invalid-feedback">Tanggal pengingat harus diisi</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Waktu Pengingat</label>
                                <input type="time" name="waktu_pengingat" id="edit_waktu_pengingat" class="form-control">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Jumlah</label>
                                <div class="input-group">
                                    <span class="input-group-text">Rp</span>
                                    <input type="text" name="jumlah" id="edit_jumlah" class="form-control number-format">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Pengulangan</label>
                                <select name="pengulangan" id="edit_pengulangan" class="form-select">
                                    <option value="sekali">Sekali</option>
                                    <option value="harian">Harian</option>
                                    <option value="mingguan">Mingguan</option>
                                    <option value="bulanan">Bulanan</option>
                                    <option value="tahunan">Tahunan</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer border-0">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">Update Pengingat</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function editReminder(id, judul, deskripsi, tanggal, waktu, jenis, jumlah, pengulangan) {
    document.getElementById('edit_id').value = id;
    document.getElementById('edit_judul').value = judul;
    document.getElementById('edit_deskripsi').value = deskripsi;
    document.getElementById('edit_tanggal_pengingat').value = tanggal;
    document.getElementById('edit_waktu_pengingat').value = waktu || '';
    document.getElementById('edit_jenis').value = jenis;
    document.getElementById('edit_jumlah').value = jumlah ? formatNumber(jumlah) : '';
    document.getElementById('edit_pengulangan').value = pengulangan;

    const modal = new bootstrap.Modal(document.getElementById('editReminderModal'));
    modal.show();
}

function deleteReminder(id) {
    if (confirm('Apakah Anda yakin ingin menghapus pengingat ini?')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="delete">
            <input type="hidden" name="id" value="${id}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

function markDone(id) {
    if (confirm('Tandai pengingat ini sebagai selesai?')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="mark_done">
            <input type="hidden" name="id" value="${id}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

function formatNumber(num) {
    return new Intl.NumberFormat('id-ID').format(num);
}

// Format number inputs
document.addEventListener('DOMContentLoaded', function() {
    const numberInputs = document.querySelectorAll('.number-format');

    numberInputs.forEach(function(input) {
        input.addEventListener('input', function(e) {
            let value = e.target.value.replace(/[^\d]/g, '');
            if (value) {
                e.target.value = formatNumber(value);
            }
        });
    });
});
</script>

<style>
.reminder-item:last-child {
    border-bottom: none !important;
    padding-bottom: 0 !important;
    margin-bottom: 0 !important;
}
</style>

<?php include 'includes/views/layouts/footer.php'; ?>
