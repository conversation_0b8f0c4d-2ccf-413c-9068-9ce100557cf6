<?php
require_once 'includes/config/database.php';
require_once 'includes/helpers/functions.php';
require_once 'includes/helpers/notifications.php';

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isLoggedIn()) {
    setFlashMessage('danger', 'Silakan login terlebih dahulu');
    redirect('/keuangan/login.php');
}

$currentUser = getCurrentUser();
$currentPage = 'backup';

// Handle backup/restore actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'backup':
                try {
                    $backupData = createBackup($currentUser['id'], $pdo);
                    $filename = 'keuanganku_backup_' . $currentUser['id'] . '_' . date('Y-m-d_H-i-s') . '.json';
                    
                    header('Content-Type: application/json');
                    header('Content-Disposition: attachment; filename="' . $filename . '"');
                    header('Content-Length: ' . strlen($backupData));
                    
                    echo $backupData;
                    exit;
                } catch (Exception $e) {
                    setFlashMessage('danger', 'Gagal membuat backup: ' . $e->getMessage());
                }
                break;

            case 'restore':
                if (isset($_FILES['backup_file']) && $_FILES['backup_file']['error'] === UPLOAD_ERR_OK) {
                    try {
                        $backupContent = file_get_contents($_FILES['backup_file']['tmp_name']);
                        $backupData = json_decode($backupContent, true);
                        
                        if ($backupData === null) {
                            throw new Exception('File backup tidak valid');
                        }
                        
                        $result = restoreBackup($currentUser['id'], $backupData, $pdo);
                        
                        if ($result) {
                            setFlashMessage('success', 'Data berhasil dipulihkan dari backup');
                        } else {
                            setFlashMessage('danger', 'Gagal memulihkan data dari backup');
                        }
                    } catch (Exception $e) {
                        setFlashMessage('danger', 'Gagal memulihkan backup: ' . $e->getMessage());
                    }
                } else {
                    setFlashMessage('danger', 'File backup tidak valid');
                }
                break;

            case 'clear_data':
                if (isset($_POST['confirm']) && $_POST['confirm'] === 'HAPUS') {
                    try {
                        clearUserData($currentUser['id'], $pdo);
                        setFlashMessage('success', 'Semua data berhasil dihapus');
                    } catch (Exception $e) {
                        setFlashMessage('danger', 'Gagal menghapus data: ' . $e->getMessage());
                    }
                } else {
                    setFlashMessage('danger', 'Konfirmasi tidak valid');
                }
                break;
        }
        redirect('/keuangan/backup_restore.php');
    }
}

// Get user data statistics
try {
    $stats = getUserDataStats($currentUser['id'], $pdo);
} catch (Exception $e) {
    $stats = [
        'total_transaksi' => 0,
        'total_kategori' => 0,
        'total_anggaran' => 0,
        'total_investasi' => 0,
        'total_penjualan' => 0,
        'total_pembelian' => 0,
        'total_produk' => 0,
        'total_supplier' => 0,
        'total_pengingat' => 0
    ];
}

// Backup functions
function createBackup($userId, $pdo) {
    $backup = [
        'version' => '1.0',
        'created_at' => date('Y-m-d H:i:s'),
        'user_id' => $userId,
        'data' => []
    ];

    // Tables to backup
    $tables = [
        'kategori',
        'transaksi',
        'anggaran',
        'investasi',
        'penjualan',
        'detail_penjualan',
        'pembelian',
        'detail_pembelian',
        'produk',
        'supplier',
        'pengingat'
    ];

    foreach ($tables as $table) {
        try {
            $stmt = $pdo->prepare("SELECT * FROM $table WHERE user_id = ?");
            $stmt->execute([$userId]);
            $backup['data'][$table] = $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            // Table might not exist, skip it
            $backup['data'][$table] = [];
        }
    }

    return json_encode($backup, JSON_PRETTY_PRINT);
}

function restoreBackup($userId, $backupData, $pdo) {
    if (!isset($backupData['data']) || !is_array($backupData['data'])) {
        throw new Exception('Format backup tidak valid');
    }

    $pdo->beginTransaction();

    try {
        // Clear existing data first
        clearUserData($userId, $pdo, false);

        // Restore data
        foreach ($backupData['data'] as $table => $rows) {
            if (empty($rows)) continue;

            foreach ($rows as $row) {
                // Update user_id to current user
                $row['user_id'] = $userId;
                
                // Remove id to let auto increment work
                unset($row['id']);

                $columns = array_keys($row);
                $placeholders = ':' . implode(', :', $columns);
                
                $sql = "INSERT INTO $table (" . implode(', ', $columns) . ") VALUES ($placeholders)";
                $stmt = $pdo->prepare($sql);
                
                foreach ($row as $key => $value) {
                    $stmt->bindValue(":$key", $value);
                }
                
                $stmt->execute();
            }
        }

        $pdo->commit();
        return true;
    } catch (Exception $e) {
        $pdo->rollback();
        throw $e;
    }
}

function clearUserData($userId, $pdo, $commit = true) {
    if ($commit) {
        $pdo->beginTransaction();
    }

    try {
        $tables = [
            'detail_penjualan' => 'penjualan_id IN (SELECT id FROM penjualan WHERE user_id = ?)',
            'detail_pembelian' => 'pembelian_id IN (SELECT id FROM pembelian WHERE user_id = ?)',
            'penjualan' => 'user_id = ?',
            'pembelian' => 'user_id = ?',
            'transaksi' => 'user_id = ?',
            'anggaran' => 'user_id = ?',
            'investasi' => 'user_id = ?',
            'produk' => 'user_id = ?',
            'supplier' => 'user_id = ?',
            'pengingat' => 'user_id = ?',
            'kategori' => 'user_id = ?'
        ];

        foreach ($tables as $table => $condition) {
            try {
                $stmt = $pdo->prepare("DELETE FROM $table WHERE $condition");
                $stmt->execute([$userId]);
            } catch (PDOException $e) {
                // Table might not exist, skip it
            }
        }

        if ($commit) {
            $pdo->commit();
        }
        return true;
    } catch (Exception $e) {
        if ($commit) {
            $pdo->rollback();
        }
        throw $e;
    }
}

function getUserDataStats($userId, $pdo) {
    $stats = [];
    
    $queries = [
        'total_transaksi' => "SELECT COUNT(*) FROM transaksi WHERE user_id = ?",
        'total_kategori' => "SELECT COUNT(*) FROM kategori WHERE user_id = ?",
        'total_anggaran' => "SELECT COUNT(*) FROM anggaran WHERE user_id = ?",
        'total_investasi' => "SELECT COUNT(*) FROM investasi WHERE user_id = ?",
        'total_penjualan' => "SELECT COUNT(*) FROM penjualan WHERE user_id = ?",
        'total_pembelian' => "SELECT COUNT(*) FROM pembelian WHERE user_id = ?",
        'total_produk' => "SELECT COUNT(*) FROM produk WHERE user_id = ?",
        'total_supplier' => "SELECT COUNT(*) FROM supplier WHERE user_id = ?",
        'total_pengingat' => "SELECT COUNT(*) FROM pengingat WHERE user_id = ?"
    ];

    foreach ($queries as $key => $query) {
        try {
            $stmt = $pdo->prepare($query);
            $stmt->execute([$userId]);
            $stats[$key] = $stmt->fetchColumn();
        } catch (PDOException $e) {
            $stats[$key] = 0;
        }
    }

    return $stats;
}

// Include header
include 'includes/views/layouts/header.php';
?>

<div class="container-fluid py-4">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-1">Backup & Restore</h1>
            <p class="text-muted mb-0">Kelola backup data untuk keamanan informasi Anda</p>
        </div>
    </div>

    <!-- Data Statistics -->
    <div class="row g-3 mb-4">
        <div class="col-lg-3 col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted mb-1">Total Transaksi</h6>
                            <h3 class="mb-0 text-primary"><?= number_format($stats['total_transaksi']) ?></h3>
                        </div>
                        <div class="bg-primary bg-opacity-10 p-3 rounded">
                            <i class="fas fa-exchange-alt text-primary"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted mb-1">Total Kategori</h6>
                            <h3 class="mb-0 text-success"><?= number_format($stats['total_kategori']) ?></h3>
                        </div>
                        <div class="bg-success bg-opacity-10 p-3 rounded">
                            <i class="fas fa-tags text-success"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted mb-1">Total Investasi</h6>
                            <h3 class="mb-0 text-warning"><?= number_format($stats['total_investasi']) ?></h3>
                        </div>
                        <div class="bg-warning bg-opacity-10 p-3 rounded">
                            <i class="fas fa-chart-line text-warning"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted mb-1">Total Anggaran</h6>
                            <h3 class="mb-0 text-info"><?= number_format($stats['total_anggaran']) ?></h3>
                        </div>
                        <div class="bg-info bg-opacity-10 p-3 rounded">
                            <i class="fas fa-chart-pie text-info"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Backup Section -->
        <div class="col-lg-6">
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0"><i class="fas fa-download me-2"></i>Backup Data</h5>
                </div>
                <div class="card-body">
                    <p class="text-muted">Buat backup semua data Anda untuk keamanan. File backup akan diunduh dalam format JSON.</p>
                    
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle me-2"></i>Data yang akan di-backup:</h6>
                        <ul class="mb-0">
                            <li>Transaksi (<?= number_format($stats['total_transaksi']) ?>)</li>
                            <li>Kategori (<?= number_format($stats['total_kategori']) ?>)</li>
                            <li>Anggaran (<?= number_format($stats['total_anggaran']) ?>)</li>
                            <li>Investasi (<?= number_format($stats['total_investasi']) ?>)</li>
                            <li>Penjualan (<?= number_format($stats['total_penjualan']) ?>)</li>
                            <li>Pembelian (<?= number_format($stats['total_pembelian']) ?>)</li>
                            <li>Produk (<?= number_format($stats['total_produk']) ?>)</li>
                            <li>Supplier (<?= number_format($stats['total_supplier']) ?>)</li>
                            <li>Pengingat (<?= number_format($stats['total_pengingat']) ?>)</li>
                        </ul>
                    </div>
                    
                    <form method="POST">
                        <input type="hidden" name="action" value="backup">
                        <button type="submit" class="btn btn-success w-100">
                            <i class="fas fa-download me-2"></i>Buat Backup Sekarang
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Restore Section -->
        <div class="col-lg-6">
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-upload me-2"></i>Restore Data</h5>
                </div>
                <div class="card-body">
                    <p class="text-muted">Pulihkan data dari file backup yang telah dibuat sebelumnya.</p>
                    
                    <div class="alert alert-warning">
                        <h6><i class="fas fa-exclamation-triangle me-2"></i>Peringatan:</h6>
                        <p class="mb-0">Proses restore akan menghapus semua data yang ada dan menggantinya dengan data dari backup. Pastikan Anda telah membuat backup terbaru sebelum melakukan restore.</p>
                    </div>
                    
                    <form method="POST" enctype="multipart/form-data">
                        <input type="hidden" name="action" value="restore">
                        
                        <div class="mb-3">
                            <label class="form-label">File Backup</label>
                            <input type="file" name="backup_file" class="form-control" accept=".json" required>
                            <small class="text-muted">Pilih file backup dengan format .json</small>
                        </div>
                        
                        <button type="submit" class="btn btn-primary w-100" onclick="return confirm('Apakah Anda yakin ingin melakukan restore? Semua data saat ini akan diganti dengan data dari backup.')">
                            <i class="fas fa-upload me-2"></i>Restore Data
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Danger Zone -->
    <div class="row">
        <div class="col-12">
            <div class="card border-danger shadow-sm">
                <div class="card-header bg-danger text-white">
                    <h5 class="mb-0"><i class="fas fa-exclamation-triangle me-2"></i>Danger Zone</h5>
                </div>
                <div class="card-body">
                    <h6 class="text-danger">Hapus Semua Data</h6>
                    <p class="text-muted">Tindakan ini akan menghapus semua data Anda secara permanen dan tidak dapat dibatalkan. Pastikan Anda telah membuat backup sebelum melakukan tindakan ini.</p>
                    
                    <button type="button" class="btn btn-outline-danger" data-bs-toggle="modal" data-bs-target="#clearDataModal">
                        <i class="fas fa-trash me-2"></i>Hapus Semua Data
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Clear Data Modal -->
<div class="modal fade" id="clearDataModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header border-0">
                <h5 class="modal-title text-danger">Konfirmasi Hapus Data</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="clear_data">

                    <div class="alert alert-danger">
                        <h6><i class="fas fa-exclamation-triangle me-2"></i>PERINGATAN!</h6>
                        <p class="mb-0">Tindakan ini akan menghapus SEMUA data Anda secara permanen dan tidak dapat dibatalkan.</p>
                    </div>

                    <p>Untuk melanjutkan, ketik <strong>HAPUS</strong> pada kolom di bawah ini:</p>

                    <div class="mb-3">
                        <input type="text" name="confirm" class="form-control" placeholder="Ketik HAPUS untuk konfirmasi" required>
                    </div>
                </div>
                <div class="modal-footer border-0">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-danger">Hapus Semua Data</button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.card-header {
    border-bottom: 1px solid rgba(0,0,0,.125);
}

.alert ul {
    padding-left: 1.5rem;
}

.alert ul li {
    margin-bottom: 0.25rem;
}
</style>

<?php include 'includes/views/layouts/footer.php'; ?>
