<?php
// Start session with secure settings
if (session_status() === PHP_SESSION_NONE) {
    session_set_cookie_params([
        'lifetime' => 86400,
        'path' => '/',
        'secure' => false, // Set to false for local development
        'httponly' => true,
        'samesite' => 'Lax' // Changed from Strict to Lax for better compatibility
    ]);
    session_start();
}

// Include required files
require_once 'includes/config/database.php';
require_once 'includes/helpers/functions.php';
require_once 'includes/helpers/notifications.php';

// Check if user is logged in
if (!isLoggedIn()) {
    setFlashMessage('danger', 'Silakan login terlebih dahulu');
    redirect('login.php');
}

$currentUser = getCurrentUser();
if (!$currentUser) {
    session_destroy();
    setFlashMessage('danger', 'Sesi tidak valid. Silakan login kembali.');
    redirect('login.php');
}

$currentPage = 'kategori';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        try {
            switch ($_POST['action']) {
                case 'add':
                    // Validasi input
                    $errors = [];

                    if (empty($_POST['nama_kategori'])) {
                        $errors[] = 'Nama kategori harus diisi';
                    } elseif (strlen($_POST['nama_kategori']) < 3 || strlen($_POST['nama_kategori']) > 50) {
                        $errors[] = 'Nama kategori harus antara 3-50 karakter';
                    }

                    if (empty($_POST['tipe'])) {
                        $errors[] = 'Tipe kategori harus dipilih';
                    } elseif (!in_array($_POST['tipe'], ['pemasukan', 'pengeluaran'])) {
                        $errors[] = 'Tipe kategori tidak valid';
                    }

                    if (empty($errors)) {
                        try {
                            // Insert kategori
                            $stmt = executeQuery("
                                INSERT INTO kategori (user_id, nama_kategori, tipe, created_at)
                                VALUES (?, ?, ?, NOW())
                            ", [
                                $currentUser['id'],
                                $_POST['nama_kategori'],
                                $_POST['tipe']
                            ]);

                            if ($stmt->rowCount() > 0) {
                                // Log aktivitas
                                executeQuery("
                                    INSERT INTO aktivitas (user_id, aktivitas)
                                    VALUES (?, ?)
                                ", [
                                    $currentUser['id'],
                                    sprintf('Menambahkan kategori %s (%s)',
                                        $_POST['nama_kategori'],
                                        $_POST['tipe']
                                    )
                                ]);

                                setFlashMessage('success', 'Kategori berhasil ditambahkan');
                                redirect('kategori.php');
                            } else {
                                throw new Exception('Gagal menyimpan kategori');
                            }
                        } catch (Exception $e) {
                            error_log("Error adding category: " . $e->getMessage());
                            setFlashMessage('danger', $e->getMessage());
                        }
                    } else {
                        setFlashMessage('danger', implode('<br>', $errors));
                    }
                    break;

                case 'update':
                    if (empty($_POST['id'])) {
                        setFlashMessage('danger', 'ID kategori tidak valid');
                        break;
                    }

                    // Validasi input
                    $errors = [];

                    if (empty($_POST['nama_kategori'])) {
                        $errors[] = 'Nama kategori harus diisi';
                    } elseif (strlen($_POST['nama_kategori']) < 3 || strlen($_POST['nama_kategori']) > 50) {
                        $errors[] = 'Nama kategori harus antara 3-50 karakter';
                    }

                    if (empty($_POST['tipe'])) {
                        $errors[] = 'Tipe kategori harus dipilih';
                    } elseif (!in_array($_POST['tipe'], ['pemasukan', 'pengeluaran'])) {
                        $errors[] = 'Tipe kategori tidak valid';
                    }

                    if (empty($errors)) {
                        $stmt = executeQuery("
                            UPDATE kategori
                            SET nama_kategori = ?, tipe = ?
                            WHERE id = ? AND (user_id = ? OR user_id IS NULL)
                        ", [
                            $_POST['nama_kategori'],
                            $_POST['tipe'],
                            $_POST['id'],
                            $currentUser['id']
                        ]);

                        if ($stmt->rowCount() > 0) {
                            // Log aktivitas
                            executeQuery("
                                INSERT INTO aktivitas (user_id, aktivitas)
                                VALUES (?, ?)
                            ", [
                                $currentUser['id'],
                                sprintf('Memperbarui kategori ID %s', $_POST['id'])
                            ]);

                            setFlashMessage('success', 'Kategori berhasil diperbarui');
                        } else {
                            setFlashMessage('danger', 'Gagal memperbarui kategori');
                        }
                    } else {
                        setFlashMessage('danger', implode('<br>', $errors));
                    }
                    break;
            }
        } catch (Exception $e) {
            error_log("Category Error: " . $e->getMessage());
            setFlashMessage('danger', 'Terjadi kesalahan. Silakan coba lagi.');
        }
        redirect('kategori.php');
    }
}

// Get categories with pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$perPage = 10;
$offset = ($page - 1) * $perPage;

// Get total records with filters
$where = ["(user_id = ? OR user_id IS NULL)"];
$params = [$currentUser['id']];

if (!empty($_GET['tipe'])) {
    $where[] = "tipe = ?";
    $params[] = $_GET['tipe'];
}

if (!empty($_GET['search'])) {
    $where[] = "nama_kategori LIKE ?";
    $params[] = "%{$_GET['search']}%";
}

$whereClause = implode(" AND ", $where);

// Get total records
$stmt = executeQuery("
    SELECT COUNT(*) as total
    FROM kategori
    WHERE $whereClause
", $params);
$totalRecords = $stmt->fetch()['total'];
$totalPages = ceil($totalRecords / $perPage);

// Get categories
$stmt = executeQuery("
    SELECT * FROM kategori
    WHERE $whereClause
    ORDER BY nama_kategori ASC
    LIMIT ? OFFSET ?
", array_merge($params, [$perPage, $offset]));
$categories = $stmt->fetchAll();

// Set page title
$page_title = 'Kategori';

// Include header
include 'includes/views/layouts/header.php';
?>

<!-- Main Content -->
<div class="container-fluid px-4 py-3">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-3">
        <div>
            <h1 class="h4 mb-1 text-gray-800">Kategori</h1>
            <p class="text-muted small mb-0">Kelola kategori pemasukan dan pengeluaran</p>
        </div>
        <button type="button" class="btn btn-primary btn-sm d-flex align-items-center gap-2" data-bs-toggle="modal" data-bs-target="#addCategoryModal">
            <i class="fas fa-plus"></i>
            <span>Tambah Kategori</span>
        </button>
    </div>

    <!-- Flash Messages -->
    <?php if ($flash = getFlashMessage()): ?>
    <div class="alert alert-<?= $flash['type'] ?> alert-dismissible fade show mb-3 py-2" role="alert">
        <small><?= $flash['message'] ?></small>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    <?php endif; ?>

    <!-- Filter Form -->
    <div class="card border-0 shadow-sm mb-3">
        <div class="card-body py-2">
            <form action="" method="GET" class="row g-2">
                <div class="col-md-4">
                    <label class="form-label small mb-1">Cari Kategori</label>
                    <div class="input-group input-group-sm">
                        <span class="input-group-text bg-light">
                            <i class="fas fa-search text-muted small"></i>
                        </span>
                        <input type="text" name="search" class="form-control form-control-sm" value="<?= $_GET['search'] ?? '' ?>" placeholder="Cari nama kategori...">
                    </div>
                </div>
                <div class="col-md-3">
                    <label class="form-label small mb-1">Tipe</label>
                    <select name="tipe" class="form-select form-select-sm">
                        <option value="">Semua Tipe</option>
                        <option value="pemasukan" <?= (isset($_GET['tipe']) && $_GET['tipe'] == 'pemasukan') ? 'selected' : '' ?>>Pemasukan</option>
                        <option value="pengeluaran" <?= (isset($_GET['tipe']) && $_GET['tipe'] == 'pengeluaran') ? 'selected' : '' ?>>Pengeluaran</option>
                    </select>
                </div>
                <div class="col-12 text-end">
                    <button type="submit" class="btn btn-primary btn-sm d-inline-flex align-items-center gap-1">
                        <i class="fas fa-filter small"></i>
                        <span>Filter</span>
                    </button>
                    <a href="kategori.php" class="btn btn-light btn-sm d-inline-flex align-items-center gap-1">
                        <i class="fas fa-sync small"></i>
                        <span>Reset</span>
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Categories Table -->
    <div class="card border-0 shadow-sm">
        <div class="card-header bg-white py-2">
            <h5 class="card-title mb-0 d-flex align-items-center gap-2">
                <i class="fas fa-tags text-primary small"></i>
                <span class="small fw-medium">Daftar Kategori</span>
            </h5>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover align-middle mb-0 small">
                    <thead>
                        <tr>
                            <th class="border-0 px-3 fw-medium">Nama Kategori</th>
                            <th class="border-0 px-3 fw-medium">Tipe</th>
                            <th class="border-0 px-3 fw-medium">Tanggal Dibuat</th>
                            <th class="border-0 px-3 fw-medium text-center">Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($categories)): ?>
                        <tr>
                            <td colspan="4" class="text-center py-4">
                                <div class="text-muted">
                                    <i class="fas fa-tags fa-2x mb-2"></i>
                                    <p class="mb-0 small">Belum ada kategori</p>
                                </div>
                            </td>
                        </tr>
                        <?php else: ?>
                        <?php foreach ($categories as $c): ?>
                        <tr>
                            <td class="fw-medium px-3"><?= htmlspecialchars($c['nama_kategori']) ?></td>
                            <td class="px-3">
                                <span class="badge bg-<?= $c['tipe'] === 'pemasukan' ? 'success' : 'danger' ?> rounded-pill px-2 py-1 small">
                                    <?= ucfirst($c['tipe']) ?>
                                </span>
                            </td>
                            <td class="text-muted px-3"><?= date('d/m/Y H:i', strtotime($c['created_at'])) ?></td>
                            <td class="text-center px-3">
                                <div class="btn-group">
                                    <button type="button" class="btn btn-sm btn-primary" onclick="editCategory(
                                        '<?= $c['id'] ?>',
                                        '<?= htmlspecialchars($c['nama_kategori']) ?>',
                                        '<?= $c['tipe'] ?>'
                                    )">
                                        <i class="fas fa-edit small"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteCategory(<?= $c['id'] ?>)">
                                        <i class="fas fa-trash small"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <?php if ($totalPages > 1): ?>
            <div class="px-3 py-2">
                <nav>
                    <ul class="pagination pagination-sm justify-content-center mb-0">
                        <?php if ($page > 1): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?= $page - 1 ?><?= !empty($_GET) ? '&' . http_build_query($_GET) : '' ?>">
                                <i class="fas fa-chevron-left small"></i>
                            </a>
                        </li>
                        <?php endif; ?>

                        <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                        <li class="page-item <?= $i === $page ? 'active' : '' ?>">
                            <a class="page-link" href="?page=<?= $i ?><?= !empty($_GET) ? '&' . http_build_query($_GET) : '' ?>">
                                <?= $i ?>
                            </a>
                        </li>
                        <?php endfor; ?>

                        <?php if ($page < $totalPages): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?= $page + 1 ?><?= !empty($_GET) ? '&' . http_build_query($_GET) : '' ?>">
                                <i class="fas fa-chevron-right small"></i>
                            </a>
                        </li>
                        <?php endif; ?>
                    </ul>
                </nav>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Add Category Modal -->
<div class="modal fade" id="addCategoryModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content border-0 shadow">
            <div class="modal-header border-0 py-2">
                <h5 class="modal-title small fw-medium">Tambah Kategori</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="" method="POST" class="needs-validation" novalidate>
                <div class="modal-body py-2">
                    <input type="hidden" name="action" value="add">

                    <div class="mb-2">
                        <label class="form-label small mb-1">Nama Kategori</label>
                        <input type="text" name="nama_kategori" class="form-control form-control-sm" required
                               minlength="3" maxlength="50" pattern="[A-Za-z0-9\s]+">
                        <div class="invalid-feedback small">
                            Nama kategori harus diisi (3-50 karakter, hanya huruf, angka, dan spasi)
                        </div>
                    </div>

                    <div class="mb-2">
                        <label class="form-label small mb-1">Tipe</label>
                        <select name="tipe" class="form-select form-select-sm" required>
                            <option value="">Pilih Tipe</option>
                            <option value="pemasukan">Pemasukan</option>
                            <option value="pengeluaran">Pengeluaran</option>
                        </select>
                        <div class="invalid-feedback small">Tipe kategori harus dipilih</div>
                    </div>
                </div>
                <div class="modal-footer border-0 py-2">
                    <button type="button" class="btn btn-light btn-sm" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary btn-sm">Simpan</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Category Modal -->
<div class="modal fade" id="editCategoryModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content border-0 shadow">
            <div class="modal-header border-0 py-2">
                <h5 class="modal-title small fw-medium">Edit Kategori</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="" method="POST" class="needs-validation" novalidate>
                <div class="modal-body py-2">
                    <input type="hidden" name="action" value="update">
                    <input type="hidden" name="id" id="edit_id">

                    <div class="mb-2">
                        <label class="form-label small mb-1">Nama Kategori</label>
                        <input type="text" name="nama_kategori" id="edit_nama_kategori" class="form-control form-control-sm" required
                               minlength="3" maxlength="50" pattern="[A-Za-z0-9\s]+">
                        <div class="invalid-feedback small">
                            Nama kategori harus diisi (3-50 karakter, hanya huruf, angka, dan spasi)
                        </div>
                    </div>

                    <div class="mb-2">
                        <label class="form-label small mb-1">Tipe</label>
                        <select name="tipe" id="edit_tipe" class="form-select form-select-sm" required>
                            <option value="pemasukan">Pemasukan</option>
                            <option value="pengeluaran">Pengeluaran</option>
                        </select>
                        <div class="invalid-feedback small">Tipe kategori harus dipilih</div>
                    </div>
                </div>
                <div class="modal-footer border-0 py-2">
                    <button type="button" class="btn btn-light btn-sm" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary btn-sm">Simpan</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php include 'includes/views/layouts/footer.php'; ?>

<style>
.card {
    transition: transform 0.2s ease-in-out;
    background: #fff;
}

.card:hover {
    transform: translateY(-5px);
}

.btn {
    padding: 0.5rem 1rem;
    border-radius: 8px;
    transition: all 0.2s ease-in-out;
}

.btn:hover {
    transform: translateY(-2px);
}

.modal-content {
    border-radius: 15px;
}

.form-control {
    border-radius: 8px;
    padding: 0.6rem 1rem;
    border: 1px solid #e0e0e0;
}

.form-control:focus {
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.15);
    border-color: #86b7fe;
}

.form-select {
    border-radius: 8px;
    padding: 0.6rem 1rem;
    border: 1px solid #e0e0e0;
}

.input-group-text {
    border-radius: 8px 0 0 8px;
    background-color: #f8f9fa;
    border: 1px solid #e0e0e0;
}

.badge {
    font-weight: 500;
    padding: 0.5rem 1rem;
}

.bg-success {
    background-color: #198754 !important;
}

.bg-primary {
    background-color: #0d6efd !important;
}

.bg-danger {
    background-color: #dc3545 !important;
}

.btn-success {
    background-color: #198754;
    border-color: #198754;
}

.btn-primary {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.btn-danger {
    background-color: #dc3545;
    border-color: #dc3545;
}

.btn-light {
    background-color: #f8f9fa;
    border-color: #e0e0e0;
}

.btn-outline-danger {
    color: #dc3545;
    border-color: #dc3545;
}

.btn-outline-danger:hover {
    background-color: #dc3545;
    color: #fff;
}

.text-muted {
    color: #6c757d !important;
}

.text-gray-800 {
    color: #212529 !important;
}

.shadow-sm {
    box-shadow: 0 .125rem .25rem rgba(0,0,0,.075) !important;
}

.shadow {
    box-shadow: 0 .5rem 1rem rgba(0,0,0,.15) !important;
}

.table > :not(caption) > * > * {
    padding: 1rem;
}

.pagination .page-link {
    border-radius: 8px;
    margin: 0 2px;
    border: none;
    color: #6c757d;
}

.pagination .page-item.active .page-link {
    background-color: #0d6efd;
    color: #fff;
}

.pagination .page-link:hover {
    background-color: #f8f9fa;
    color: #0d6efd;
}
</style>

<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>
// Edit category
function editCategory(id, nama, tipe) {
    document.getElementById('edit_id').value = id;
    document.getElementById('edit_nama_kategori').value = nama;
    document.getElementById('edit_tipe').value = tipe;

    new bootstrap.Modal(document.getElementById('editCategoryModal')).show();
}

// Delete category
function deleteCategory(id) {
    Swal.fire({
        title: 'Apakah Anda yakin?',
        text: "Data kategori yang dihapus tidak dapat dikembalikan!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'Ya, hapus!',
        cancelButtonText: 'Batal'
    }).then((result) => {
        if (result.isConfirmed) {
            window.location.href = `hapus-kategori.php?id=${id}`;
        }
    });
}

// Form validation
const forms = document.querySelectorAll('.needs-validation');
Array.from(forms).forEach(form => {
    form.addEventListener('submit', event => {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        }
        form.classList.add('was-validated');
    }, false);
});
</script>