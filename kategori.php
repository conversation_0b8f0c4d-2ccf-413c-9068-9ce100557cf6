<?php
// Start session with secure settings
if (session_status() === PHP_SESSION_NONE) {
    session_set_cookie_params([
        'lifetime' => 86400,
        'path' => '/',
        'secure' => false, // Set to false for local development
        'httponly' => true,
        'samesite' => 'Lax' // Changed from Strict to Lax for better compatibility
    ]);
    session_start();
}

// Include required files
require_once 'includes/config/database.php';
require_once 'includes/helpers/functions.php';
require_once 'includes/helpers/notifications.php';

// Check if user is logged in
if (!isLoggedIn()) {
    setFlashMessage('danger', 'Silakan login terlebih dahulu');
    redirect('login.php');
}

$currentUser = getCurrentUser();
if (!$currentUser) {
    session_destroy();
    setFlashMessage('danger', 'Sesi tidak valid. Silakan login kembali.');
    redirect('login.php');
}

$currentPage = 'kategori';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        try {
            switch ($_POST['action']) {
                case 'add':
                    // Validasi input
                    $errors = [];

                    if (empty($_POST['nama_kategori'])) {
                        $errors[] = 'Nama kategori harus diisi';
                    } elseif (strlen($_POST['nama_kategori']) < 3 || strlen($_POST['nama_kategori']) > 50) {
                        $errors[] = 'Nama kategori harus antara 3-50 karakter';
                    }

                    if (empty($_POST['tipe'])) {
                        $errors[] = 'Tipe kategori harus dipilih';
                    } elseif (!in_array($_POST['tipe'], ['pemasukan', 'pengeluaran'])) {
                        $errors[] = 'Tipe kategori tidak valid';
                    }

                    if (empty($errors)) {
                        try {
                            // Insert kategori
                            $stmt = executeQuery("
                                INSERT INTO kategori (user_id, nama_kategori, tipe, created_at)
                                VALUES (?, ?, ?, NOW())
                            ", [
                                $currentUser['id'],
                                $_POST['nama_kategori'],
                                $_POST['tipe']
                            ]);

                            if ($stmt->rowCount() > 0) {
                                // Log aktivitas
                                executeQuery("
                                    INSERT INTO aktivitas (user_id, aktivitas)
                                    VALUES (?, ?)
                                ", [
                                    $currentUser['id'],
                                    sprintf('Menambahkan kategori %s (%s)',
                                        $_POST['nama_kategori'],
                                        $_POST['tipe']
                                    )
                                ]);

                                setFlashMessage('success', 'Kategori berhasil ditambahkan');
                                redirect('kategori.php');
                            } else {
                                throw new Exception('Gagal menyimpan kategori');
                            }
                        } catch (Exception $e) {
                            error_log("Error adding category: " . $e->getMessage());
                            setFlashMessage('danger', $e->getMessage());
                        }
                    } else {
                        setFlashMessage('danger', implode('<br>', $errors));
                    }
                    break;

                case 'update':
                    if (empty($_POST['id'])) {
                        setFlashMessage('danger', 'ID kategori tidak valid');
                        break;
                    }

                    // Validasi input
                    $errors = [];

                    if (empty($_POST['nama_kategori'])) {
                        $errors[] = 'Nama kategori harus diisi';
                    } elseif (strlen($_POST['nama_kategori']) < 3 || strlen($_POST['nama_kategori']) > 50) {
                        $errors[] = 'Nama kategori harus antara 3-50 karakter';
                    }

                    if (empty($_POST['tipe'])) {
                        $errors[] = 'Tipe kategori harus dipilih';
                    } elseif (!in_array($_POST['tipe'], ['pemasukan', 'pengeluaran'])) {
                        $errors[] = 'Tipe kategori tidak valid';
                    }

                    if (empty($errors)) {
                        $stmt = executeQuery("
                            UPDATE kategori
                            SET nama_kategori = ?, tipe = ?
                            WHERE id = ? AND (user_id = ? OR user_id IS NULL)
                        ", [
                            $_POST['nama_kategori'],
                            $_POST['tipe'],
                            $_POST['id'],
                            $currentUser['id']
                        ]);

                        if ($stmt->rowCount() > 0) {
                            // Log aktivitas
                            executeQuery("
                                INSERT INTO aktivitas (user_id, aktivitas)
                                VALUES (?, ?)
                            ", [
                                $currentUser['id'],
                                sprintf('Memperbarui kategori ID %s', $_POST['id'])
                            ]);

                            setFlashMessage('success', 'Kategori berhasil diperbarui');
                        } else {
                            setFlashMessage('danger', 'Gagal memperbarui kategori');
                        }
                    } else {
                        setFlashMessage('danger', implode('<br>', $errors));
                    }
                    break;
            }
        } catch (Exception $e) {
            error_log("Category Error: " . $e->getMessage());
            setFlashMessage('danger', 'Terjadi kesalahan. Silakan coba lagi.');
        }
        redirect('kategori.php');
    }
}

// Get categories with pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$perPage = 10;
$offset = ($page - 1) * $perPage;

// Get total records with filters
$where = ["(user_id = ? OR user_id IS NULL)"];
$params = [$currentUser['id']];

if (!empty($_GET['tipe'])) {
    $where[] = "tipe = ?";
    $params[] = $_GET['tipe'];
}

if (!empty($_GET['search'])) {
    $where[] = "nama_kategori LIKE ?";
    $params[] = "%{$_GET['search']}%";
}

$whereClause = implode(" AND ", $where);

// Get total records
$stmt = executeQuery("
    SELECT COUNT(*) as total
    FROM kategori
    WHERE $whereClause
", $params);
$totalRecords = $stmt->fetch()['total'];
$totalPages = ceil($totalRecords / $perPage);

// Get categories
$stmt = executeQuery("
    SELECT * FROM kategori
    WHERE $whereClause
    ORDER BY nama_kategori ASC
    LIMIT ? OFFSET ?
", array_merge($params, [$perPage, $offset]));
$categories = $stmt->fetchAll();

// Set page title
$page_title = 'Kategori';

// Include header and sidebar
require_once 'includes/views/layouts/header.php';
require_once 'includes/views/layouts/sidebar.php';
?>

<div class="modern-layout">
    <div class="modern-container">
        <!-- Modern Page Header -->
        <div class="modern-page-header modern-flex modern-justify-between modern-items-center modern-mb-xl">
            <div>
                <h1 class="modern-page-title modern-mb-0">Kategori</h1>
                <p class="modern-page-subtitle">Kelola kategori pemasukan dan pengeluaran dengan mudah</p>
            </div>
            <div class="modern-page-actions">
                <button type="button" class="modern-btn modern-btn-primary" data-bs-toggle="modal" data-bs-target="#addCategoryModal">
                    <i class="fas fa-plus"></i>
                    Tambah Kategori
                </button>
            </div>
        </div>

        <!-- Flash Messages -->
        <?php if ($flash = getFlashMessage()): ?>
        <div class="modern-alert modern-alert-<?= $flash['type'] ?> modern-mb-lg">
            <div class="modern-alert-content">
                <i class="fas fa-<?= $flash['type'] === 'success' ? 'check-circle' : 'exclamation-triangle' ?> modern-alert-icon"></i>
                <div class="modern-alert-message"><?= $flash['message'] ?></div>
            </div>
            <button type="button" class="modern-alert-close" onclick="this.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <?php endif; ?>

        <!-- Modern Filter Section -->
        <div class="modern-card modern-mb-lg">
            <div class="modern-card-header">
                <h5 class="modern-card-title">
                    <i class="fas fa-filter modern-text-primary modern-mr-sm"></i>
                    Filter Kategori
                </h5>
            </div>
            <div class="modern-card-body">
                <form action="" method="GET" class="modern-grid modern-grid-cols-3 modern-gap-md modern-items-end">
                    <div class="modern-form-group">
                        <label class="modern-form-label">
                            <i class="fas fa-search modern-text-primary"></i>
                            Cari Kategori
                        </label>
                        <input type="text" name="search" class="modern-form-control" value="<?= $_GET['search'] ?? '' ?>" placeholder="Cari nama kategori...">
                    </div>

                    <div class="modern-form-group">
                        <label class="modern-form-label">
                            <i class="fas fa-tags modern-text-primary"></i>
                            Tipe
                        </label>
                        <select name="tipe" class="modern-form-control">
                            <option value="">🔍 Semua Tipe</option>
                            <option value="pemasukan" <?= (isset($_GET['tipe']) && $_GET['tipe'] == 'pemasukan') ? 'selected' : '' ?>>📈 Pemasukan</option>
                            <option value="pengeluaran" <?= (isset($_GET['tipe']) && $_GET['tipe'] == 'pengeluaran') ? 'selected' : '' ?>>📉 Pengeluaran</option>
                        </select>
                    </div>

                    <div class="modern-form-group modern-flex modern-gap-sm modern-mb-0">
                        <button type="submit" class="modern-btn modern-btn-primary modern-flex-1">
                            <i class="fas fa-search"></i>
                            Filter
                        </button>
                        <a href="kategori.php" class="modern-btn modern-btn-light">
                            <i class="fas fa-sync"></i>
                            Reset
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- Modern Categories Table -->
        <div class="modern-card">
            <div class="modern-card-header">
                <h5 class="modern-card-title">
                    <i class="fas fa-tags modern-text-primary modern-mr-sm"></i>
                    Daftar Kategori
                </h5>
                <div class="modern-card-actions">
                    <span class="modern-badge modern-badge-info">
                        Total: <?= count($categories) ?> kategori
                    </span>
                </div>
            </div>
            <div class="modern-card-body modern-p-0">
                <div class="modern-table-responsive">
                    <table class="modern-table">
                        <thead class="modern-table-header">
                            <tr>
                                <th class="modern-table-th">
                                    <i class="fas fa-tag modern-mr-xs"></i>
                                    Nama Kategori
                                </th>
                                <th class="modern-table-th">
                                    <i class="fas fa-exchange-alt modern-mr-xs"></i>
                                    Tipe
                                </th>
                                <th class="modern-table-th">
                                    <i class="fas fa-calendar modern-mr-xs"></i>
                                    Tanggal Dibuat
                                </th>
                                <th class="modern-table-th modern-text-center">
                                    <i class="fas fa-cogs modern-mr-xs"></i>
                                    Aksi
                                </th>
                            </tr>
                        </thead>
                        <tbody class="modern-table-body">
                            <?php if (empty($categories)): ?>
                            <tr>
                                <td colspan="4" class="modern-table-td">
                                    <div class="modern-empty-state">
                                        <div class="modern-empty-icon">
                                            <i class="fas fa-tags"></i>
                                        </div>
                                        <div class="modern-empty-content">
                                            <h6 class="modern-empty-title">Belum Ada Kategori</h6>
                                            <p class="modern-empty-text">Belum ada kategori yang dibuat</p>
                                            <button type="button" class="modern-btn modern-btn-primary modern-btn-sm" data-bs-toggle="modal" data-bs-target="#addCategoryModal">
                                                <i class="fas fa-plus"></i>
                                                Tambah Kategori Pertama
                                            </button>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <?php else: ?>
                            <?php foreach ($categories as $c): ?>
                            <tr class="modern-table-row">
                                <td class="modern-table-td">
                                    <div class="modern-table-content">
                                        <div class="modern-table-title"><?= htmlspecialchars($c['nama_kategori']) ?></div>
                                    </div>
                                </td>
                                <td class="modern-table-td">
                                    <div class="modern-badge modern-badge-<?= $c['tipe'] === 'pemasukan' ? 'success' : 'danger' ?>">
                                        <?php if ($c['tipe'] === 'pemasukan'): ?>
                                            <i class="fas fa-arrow-up"></i>
                                        <?php else: ?>
                                            <i class="fas fa-arrow-down"></i>
                                        <?php endif; ?>
                                        <?= ucfirst($c['tipe']) ?>
                                    </div>
                                </td>
                                <td class="modern-table-td">
                                    <div class="modern-table-date">
                                        <i class="fas fa-calendar modern-text-muted modern-mr-xs"></i>
                                        <?= formatDate($c['created_at']) ?>
                                    </div>
                                </td>
                                <td class="modern-table-td modern-text-center">
                                    <div class="modern-action-buttons">
                                        <button type="button" class="modern-btn modern-btn-light modern-btn-sm" onclick="editCategory(
                                            '<?= $c['id'] ?>',
                                            '<?= htmlspecialchars($c['nama_kategori']) ?>',
                                            '<?= $c['tipe'] ?>'
                                        )" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button type="button" class="modern-btn modern-btn-danger modern-btn-sm" onclick="deleteCategory(<?= $c['id'] ?>)" title="Hapus">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Modern Pagination -->
                <?php if ($totalPages > 1): ?>
                <div class="modern-card-footer">
                    <div class="modern-pagination-wrapper">
                        <div class="modern-pagination-info">
                            <span class="modern-text-muted">
                                Halaman <?= $page ?> dari <?= $totalPages ?>
                                (<?= $totalRecords ?> total kategori)
                            </span>
                        </div>
                        <nav class="modern-pagination">
                            <?php if ($page > 1): ?>
                            <a class="modern-pagination-btn modern-pagination-prev"
                               href="?page=<?= $page - 1 ?>&<?= http_build_query(array_filter($_GET, function($key) { return $key !== 'page'; }, ARRAY_FILTER_USE_KEY)) ?>">
                                <i class="fas fa-chevron-left"></i>
                                Sebelumnya
                            </a>
                            <?php endif; ?>

                            <div class="modern-pagination-numbers">
                                <?php
                                $start = max(1, $page - 2);
                                $end = min($totalPages, $page + 2);

                                if ($start > 1): ?>
                                    <a class="modern-pagination-number" href="?page=1&<?= http_build_query(array_filter($_GET, function($key) { return $key !== 'page'; }, ARRAY_FILTER_USE_KEY)) ?>">1</a>
                                    <?php if ($start > 2): ?>
                                        <span class="modern-pagination-dots">...</span>
                                    <?php endif; ?>
                                <?php endif; ?>

                                <?php for ($i = $start; $i <= $end; $i++): ?>
                                <a class="modern-pagination-number <?= $i === $page ? 'active' : '' ?>"
                                   href="?page=<?= $i ?>&<?= http_build_query(array_filter($_GET, function($key) { return $key !== 'page'; }, ARRAY_FILTER_USE_KEY)) ?>"><?= $i ?></a>
                                <?php endfor; ?>

                                <?php if ($end < $totalPages): ?>
                                    <?php if ($end < $totalPages - 1): ?>
                                        <span class="modern-pagination-dots">...</span>
                                    <?php endif; ?>
                                    <a class="modern-pagination-number" href="?page=<?= $totalPages ?>&<?= http_build_query(array_filter($_GET, function($key) { return $key !== 'page'; }, ARRAY_FILTER_USE_KEY)) ?>"><?= $totalPages ?></a>
                                <?php endif; ?>
                            </div>

                            <?php if ($page < $totalPages): ?>
                            <a class="modern-pagination-btn modern-pagination-next"
                               href="?page=<?= $page + 1 ?>&<?= http_build_query(array_filter($_GET, function($key) { return $key !== 'page'; }, ARRAY_FILTER_USE_KEY)) ?>">
                                Selanjutnya
                                <i class="fas fa-chevron-right"></i>
                            </a>
                            <?php endif; ?>
                        </nav>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Modern Add Category Modal -->
<div class="modal fade" id="addCategoryModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modern-modal-content">
            <div class="modern-modal-header">
                <h5 class="modern-modal-title">
                    <i class="fas fa-plus modern-text-primary modern-mr-sm"></i>
                    Tambah Kategori
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="" method="POST" class="needs-validation" novalidate>
                <div class="modern-modal-body">
                    <input type="hidden" name="action" value="add">

                    <div class="modern-form-group">
                        <label class="modern-form-label">
                            <i class="fas fa-tag modern-text-primary"></i>
                            Nama Kategori <span class="modern-text-danger">*</span>
                        </label>
                        <input type="text" name="nama_kategori" class="modern-form-control" required
                               minlength="3" maxlength="50" placeholder="Masukkan nama kategori">
                        <div class="invalid-feedback">
                            Nama kategori harus diisi (3-50 karakter)
                        </div>
                    </div>

                    <div class="modern-form-group modern-mb-0">
                        <label class="modern-form-label">
                            <i class="fas fa-exchange-alt modern-text-primary"></i>
                            Tipe <span class="modern-text-danger">*</span>
                        </label>
                        <select name="tipe" class="modern-form-control" required>
                            <option value="">Pilih Tipe</option>
                            <option value="pemasukan">📈 Pemasukan</option>
                            <option value="pengeluaran">📉 Pengeluaran</option>
                        </select>
                        <div class="invalid-feedback">Tipe kategori harus dipilih</div>
                    </div>
                </div>
                <div class="modern-modal-footer">
                    <button type="button" class="modern-btn modern-btn-light" data-bs-dismiss="modal">
                        <i class="fas fa-times"></i>
                        Batal
                    </button>
                    <button type="submit" class="modern-btn modern-btn-primary">
                        <i class="fas fa-save"></i>
                        Simpan Kategori
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modern Edit Category Modal -->
<div class="modal fade" id="editCategoryModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modern-modal-content">
            <div class="modern-modal-header">
                <h5 class="modern-modal-title">
                    <i class="fas fa-edit modern-text-primary modern-mr-sm"></i>
                    Edit Kategori
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="" method="POST" class="needs-validation" novalidate>
                <div class="modern-modal-body">
                    <input type="hidden" name="action" value="update">
                    <input type="hidden" name="id" id="edit_id">

                    <div class="modern-form-group">
                        <label class="modern-form-label">
                            <i class="fas fa-tag modern-text-primary"></i>
                            Nama Kategori <span class="modern-text-danger">*</span>
                        </label>
                        <input type="text" name="nama_kategori" id="edit_nama_kategori" class="modern-form-control" required
                               minlength="3" maxlength="50">
                        <div class="invalid-feedback">
                            Nama kategori harus diisi (3-50 karakter)
                        </div>
                    </div>

                    <div class="modern-form-group modern-mb-0">
                        <label class="modern-form-label">
                            <i class="fas fa-exchange-alt modern-text-primary"></i>
                            Tipe <span class="modern-text-danger">*</span>
                        </label>
                        <select name="tipe" id="edit_tipe" class="modern-form-control" required>
                            <option value="pemasukan">📈 Pemasukan</option>
                            <option value="pengeluaran">📉 Pengeluaran</option>
                        </select>
                        <div class="invalid-feedback">Tipe kategori harus dipilih</div>
                    </div>
                </div>
                <div class="modern-modal-footer">
                    <button type="button" class="modern-btn modern-btn-light" data-bs-dismiss="modal">
                        <i class="fas fa-times"></i>
                        Batal
                    </button>
                    <button type="submit" class="modern-btn modern-btn-primary">
                        <i class="fas fa-save"></i>
                        Update Kategori
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php include 'includes/views/layouts/footer.php'; ?>

<style>
.card {
    transition: transform 0.2s ease-in-out;
    background: #fff;
}

.card:hover {
    transform: translateY(-5px);
}

.btn {
    padding: 0.5rem 1rem;
    border-radius: 8px;
    transition: all 0.2s ease-in-out;
}

.btn:hover {
    transform: translateY(-2px);
}

.modal-content {
    border-radius: 15px;
}

.form-control {
    border-radius: 8px;
    padding: 0.6rem 1rem;
    border: 1px solid #e0e0e0;
}

.form-control:focus {
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.15);
    border-color: #86b7fe;
}

.form-select {
    border-radius: 8px;
    padding: 0.6rem 1rem;
    border: 1px solid #e0e0e0;
}

.input-group-text {
    border-radius: 8px 0 0 8px;
    background-color: #f8f9fa;
    border: 1px solid #e0e0e0;
}

.badge {
    font-weight: 500;
    padding: 0.5rem 1rem;
}

.bg-success {
    background-color: #198754 !important;
}

.bg-primary {
    background-color: #0d6efd !important;
}

.bg-danger {
    background-color: #dc3545 !important;
}

.btn-success {
    background-color: #198754;
    border-color: #198754;
}

.btn-primary {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.btn-danger {
    background-color: #dc3545;
    border-color: #dc3545;
}

.btn-light {
    background-color: #f8f9fa;
    border-color: #e0e0e0;
}

.btn-outline-danger {
    color: #dc3545;
    border-color: #dc3545;
}

.btn-outline-danger:hover {
    background-color: #dc3545;
    color: #fff;
}

.text-muted {
    color: #6c757d !important;
}

.text-gray-800 {
    color: #212529 !important;
}

.shadow-sm {
    box-shadow: 0 .125rem .25rem rgba(0,0,0,.075) !important;
}

.shadow {
    box-shadow: 0 .5rem 1rem rgba(0,0,0,.15) !important;
}

.table > :not(caption) > * > * {
    padding: 1rem;
}

.pagination .page-link {
    border-radius: 8px;
    margin: 0 2px;
    border: none;
    color: #6c757d;
}

.pagination .page-item.active .page-link {
    background-color: #0d6efd;
    color: #fff;
}

.pagination .page-link:hover {
    background-color: #f8f9fa;
    color: #0d6efd;
}
</style>

<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>
// Edit category
function editCategory(id, nama, tipe) {
    document.getElementById('edit_id').value = id;
    document.getElementById('edit_nama_kategori').value = nama;
    document.getElementById('edit_tipe').value = tipe;

    new bootstrap.Modal(document.getElementById('editCategoryModal')).show();
}

// Delete category
function deleteCategory(id) {
    Swal.fire({
        title: 'Apakah Anda yakin?',
        text: "Data kategori yang dihapus tidak dapat dikembalikan!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'Ya, hapus!',
        cancelButtonText: 'Batal'
    }).then((result) => {
        if (result.isConfirmed) {
            window.location.href = `hapus-kategori.php?id=${id}`;
        }
    });
}

// Form validation
const forms = document.querySelectorAll('.needs-validation');
Array.from(forms).forEach(form => {
    form.addEventListener('submit', event => {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        }
        form.classList.add('was-validated');
    }, false);
});
</script>