<?php
/**
 * Function Conflict Prevention
 * Include this file to ensure core functions are available without conflicts
 */

// Core functions that should only exist in functions.php
$core_functions = [
    'setFlashMessage',
    'getFlashMessage', 
    'displayFlashMessage',
    'formatRupiah',
    'formatTanggal',
    'formatBytes',
    'formatFileSize',
    'getCurrentUser',
    'isLoggedIn',
    'validateInput',
    'sanitizeOutput',
    'redirect',
    'formatRupiahShort',
    'getSystemStats',
    'getDiskUsage',
    'getMemoryUsage',
    'hasPermission',
    'logSystemEvent'
];

// Check if functions exist, if not include functions.php
foreach ($core_functions as $function) {
    if (!function_exists($function)) {
        require_once __DIR__ . '/functions.php';
        break; // Only need to include once
    }
}

// Prevent redeclaration by checking if functions exist before declaring
function checkFunctionExists($functionName) {
    return function_exists($functionName);
}

// Helper to safely declare functions
function safeDeclareFunction($functionName, $callback) {
    if (!function_exists($functionName)) {
        return $callback();
    }
    return false; // Function already exists
}
