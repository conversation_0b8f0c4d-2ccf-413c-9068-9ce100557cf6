/* Layout Manager CSS - Consolidated and Optimized */

/* CSS Variables for Theme Support */
:root {
    /* Light Theme Colors */
    --primary-color: #2563eb;
    --primary-hover: #1d4ed8;
    --secondary-color: #64748b;
    --success-color: #059669;
    --danger-color: #dc2626;
    --warning-color: #d97706;
    --info-color: #0891b2;

    /* Background Colors */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --bg-accent: #e2e8f0;

    /* Text Colors */
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --text-muted: #94a3b8;
    --text-inverse: #ffffff;

    /* Border Colors */
    --border-color: #e2e8f0;
    --border-light: #f1f5f9;
    --border-dark: #cbd5e1;

    /* Shadow Colors */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

    /* Sidebar */
    --sidebar-width: 280px;
    --sidebar-collapsed-width: 70px;
    --sidebar-bg: var(--bg-primary);
    --sidebar-border: var(--border-color);

    /* Navbar */
    --navbar-height: 72px;
    --navbar-bg: var(--bg-primary);
    --navbar-border: var(--border-color);

    /* Fonts */
    --font-family-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-family-secondary: 'Poppins', sans-serif;

    /* Transitions */
    --transition-fast: 0.15s ease;
    --transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: 0.5s cubic-bezier(0.4, 0, 0.2, 1);

    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
}

/* Dark Theme Colors */
[data-bs-theme="dark"] {
    --primary-color: #3b82f6;
    --primary-hover: #2563eb;
    --secondary-color: #6b7280;
    --success-color: #10b981;
    --danger-color: #ef4444;
    --warning-color: #f59e0b;
    --info-color: #06b6d4;

    --bg-primary: #1e293b;
    --bg-secondary: #0f172a;
    --bg-tertiary: #334155;
    --bg-accent: #475569;

    --text-primary: #f8fafc;
    --text-secondary: #cbd5e1;
    --text-muted: #94a3b8;
    --text-inverse: #1e293b;

    --border-color: #334155;
    --border-light: #475569;
    --border-dark: #1e293b;

    --sidebar-bg: var(--bg-primary);
    --sidebar-border: var(--border-color);
    --navbar-bg: var(--bg-primary);
    --navbar-border: var(--border-color);
}

/* Footer Styles */
.modern-footer {
    background: linear-gradient(135deg, var(--bg-primary), var(--bg-tertiary));
    border-top: 1px solid var(--border-color);
    margin-top: auto;
    padding: 2rem 0 1rem;
    color: var(--text-secondary);
}

.footer-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1.5rem;
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 2rem;
    margin-bottom: 1.5rem;
}

.footer-section {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.footer-brand {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.footer-logo {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-inverse);
    font-size: 1.25rem;
}

.footer-brand-text {
    display: flex;
    flex-direction: column;
}

.footer-title {
    font-family: var(--font-family-secondary);
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
    font-size: 1.1rem;
}

.footer-subtitle {
    color: var(--text-muted);
    font-size: 0.75rem;
    margin: 0;
}

.footer-links {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.footer-link {
    color: var(--text-secondary);
    text-decoration: none;
    font-size: 0.9rem;
    transition: color var(--transition-fast);
}

.footer-link:hover {
    color: var(--primary-color);
}

.footer-info {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    text-align: right;
}

.footer-version, .footer-copyright {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    font-size: 0.85rem;
    color: var(--text-muted);
}

.footer-bottom {
    padding-top: 1.5rem;
    border-top: 1px solid var(--border-light);
}

.footer-stats {
    display: flex;
    justify-content: center;
    gap: 2rem;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.85rem;
    color: var(--text-muted);
}

.stat-item i {
    font-size: 1rem;
}

/* Mobile Responsive for Footer */
@media (max-width: 768px) {
    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 1.5rem;
    }

    .footer-info {
        text-align: center;
    }

    .footer-version, .footer-copyright {
        justify-content: center;
    }

    .footer-stats {
        flex-direction: column;
        gap: 1rem;
    }
}

/* Layout Cards */
.layout-card {
    position: relative;
}

.layout-card input[type="radio"] {
    display: none;
}

.layout-label {
    display: block;
    border: 2px solid #dee2e6;
    border-radius: 12px;
    padding: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #fff;
    height: 100%;
}

.layout-label:hover {
    border-color: #007bff;
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,123,255,0.15);
}

.layout-card input[type="radio"]:checked + .layout-label {
    border-color: #007bff;
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(0,123,255,0.3);
}

.layout-preview {
    width: 100%;
    height: 80px;
    border-radius: 8px;
    overflow: hidden;
    display: flex;
    margin-bottom: 12px;
    background: #f8f9fa;
}

.preview-sidebar {
    width: 30%;
    position: relative;
}

.preview-main {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.preview-navbar {
    height: 25%;
}

.preview-content {
    flex: 1;
    background: #e9ecef;
}

/* Layout Specific Styles */
.classic-sidebar { background: #343a40; }
.classic-navbar { background: #007bff; }

.modern-sidebar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 0 8px 8px 0;
}
.modern-navbar {
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    border-radius: 0 0 8px 8px;
}

.colorful-sidebar { background: linear-gradient(135deg, #ff6b6b 0%, #4ecdc4 50%, #45b7d1 100%); }
.colorful-navbar { background: linear-gradient(90deg, #ff6b6b 0%, #4ecdc4 50%, #45b7d1 100%); }

.minimal-sidebar {
    background: #f8f9fa;
    border-right: 1px solid #dee2e6;
}
.minimal-navbar {
    background: #fff;
    border-bottom: 1px solid #dee2e6;
}

.glass-sidebar {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.18);
}
.glass-navbar {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.18);
}

.neon-sidebar {
    background: linear-gradient(135deg, #39ff14 0%, #ff073a 50%, #00ffff 100%);
    box-shadow: 0 0 10px rgba(57, 255, 20, 0.3);
}
.neon-navbar {
    background: linear-gradient(90deg, #39ff14 0%, #ff073a 50%, #00ffff 100%);
    box-shadow: 0 0 10px rgba(57, 255, 20, 0.3);
}

.layout-info h6 {
    margin: 0 0 5px 0;
    font-weight: 600;
}

.layout-info small {
    opacity: 0.8;
}

/* Color Cards */
.color-card {
    position: relative;
}

.color-card input[type="radio"] {
    display: none;
}

.color-label {
    display: block;
    border: 2px solid #dee2e6;
    border-radius: 8px;
    padding: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #fff;
    text-align: center;
}

.color-label:hover {
    border-color: #007bff;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,123,255,0.15);
}

.color-card input[type="radio"]:checked + .color-label {
    border-color: #007bff;
    background: #f8f9fa;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,123,255,0.2);
}

.color-preview {
    width: 100%;
    height: 40px;
    border-radius: 6px;
    margin-bottom: 8px;
    border: 1px solid rgba(0,0,0,0.1);
}

/* Color Scheme Previews */
.default-colors { background: linear-gradient(45deg, #007bff, #6c757d); }
.vibrant-colors { background: linear-gradient(45deg, #ff0080, #00ff80, #8000ff); }
.pastel-colors { background: linear-gradient(45deg, #ffb3ba, #bae1ff, #baffc9); }
.neon-colors { background: linear-gradient(45deg, #39ff14, #ff073a, #00ffff); }
.ocean-colors { background: linear-gradient(45deg, #006994, #0099cc, #66ccff); }
.sunset-colors { background: linear-gradient(45deg, #ff4500, #ff6347, #ffd700); }
.midnight-colors { background: linear-gradient(45deg, #2c3e50, #34495e, #1a252f); }
.royal-colors { background: linear-gradient(45deg, #663399, #9966cc, #cc99ff); }

/* Live Preview */
.live-preview-container {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    overflow: hidden;
    background: #f8f9fa;
}

.preview-app {
    display: flex;
    height: 300px;
}

.preview-sidebar-large {
    width: 35%;
    background: #343a40;
    color: white;
    padding: 15px;
    display: flex;
    flex-direction: column;
}

.preview-logo {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    font-weight: bold;
    font-size: 16px;
}

.preview-logo i {
    margin-right: 8px;
    font-size: 18px;
}

.preview-menu-item {
    display: flex;
    align-items: center;
    padding: 8px 0;
    margin-bottom: 5px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.preview-menu-item:hover {
    background: rgba(255,255,255,0.1);
}

.preview-menu-item.active {
    background: rgba(255,255,255,0.2);
}

.preview-menu-item i {
    margin-right: 8px;
    width: 16px;
}

.preview-main-large {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.preview-navbar-large {
    background: #007bff;
    color: white;
    padding: 12px 15px;
    display: flex;
    align-items: center;
}

.preview-nav-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

.preview-nav-actions {
    display: flex;
    gap: 10px;
}

.preview-content-large {
    flex: 1;
    padding: 15px;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.preview-card-large {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.preview-card-large h6 {
    margin: 0 0 8px 0;
    color: #333;
}

.preview-card-large p {
    margin: 0;
    color: #666;
    font-size: 14px;
}

/* Advanced Layout Options */
.layout-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.layout-option {
    position: relative;
}

.layout-option input[type="radio"] {
    display: none;
}

.layout-option label {
    display: block;
    padding: 15px;
    border: 2px solid #dee2e6;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    background: #fff;
}

.layout-option label:hover {
    border-color: #007bff;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,123,255,0.2);
}

.layout-option input[type="radio"]:checked + label {
    border-color: #007bff;
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,123,255,0.3);
}

.layout-option label i {
    font-size: 24px;
    margin-bottom: 8px;
    display: block;
}

.layout-option label span {
    font-weight: 600;
    font-size: 14px;
    display: block;
    margin-bottom: 5px;
}

.layout-option label small {
    font-size: 11px;
    opacity: 0.8;
    display: block;
}

/* Color Schemes */
.color-schemes {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 12px;
}

.color-option {
    position: relative;
}

.color-option input[type="radio"] {
    display: none;
}

.color-option label {
    display: block;
    padding: 12px;
    border: 2px solid #dee2e6;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    background: #fff;
}

.color-option label:hover {
    border-color: #007bff;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,123,255,0.2);
}

.color-option input[type="radio"]:checked + label {
    border-color: #007bff;
    background: #f8f9fa;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,123,255,0.3);
}

.color-option label span {
    font-weight: 600;
    font-size: 13px;
    display: block;
    margin-bottom: 3px;
}

.color-option label small {
    font-size: 10px;
    color: #6c757d;
    display: block;
}

/* Component Options */
.component-options {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.component-option {
    position: relative;
}

.component-option input[type="radio"] {
    display: none;
}

.component-option label {
    display: block;
    padding: 10px 15px;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    background: #fff;
    font-size: 13px;
}

.component-option label:hover {
    border-color: #007bff;
    background: #f8f9fa;
}

.component-option input[type="radio"]:checked + label {
    border-color: #007bff;
    background: #007bff;
    color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
    .layout-options {
        grid-template-columns: 1fr;
    }

    .color-schemes {
        grid-template-columns: repeat(2, 1fr);
    }

    .component-options {
        gap: 6px;
    }

    .component-option label {
        padding: 8px 12px;
        font-size: 12px;
    }

    .preview-app {
        height: 200px;
    }

    .preview-sidebar-large {
        width: 40%;
        padding: 10px;
    }

    .preview-logo {
        font-size: 14px;
        margin-bottom: 15px;
    }

    .preview-menu-item {
        padding: 6px 0;
        font-size: 12px;
    }

    .preview-navbar-large {
        padding: 8px 10px;
        font-size: 14px;
    }

    .preview-content-large {
        padding: 10px;
    }

    .preview-card-large {
        padding: 10px;
    }
}

/* Animation Effects */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.layout-label, .color-label {
    animation: fadeIn 0.3s ease;
}

.layout-option input[type="radio"]:checked + label {
    animation: pulse 0.3s ease-in-out;
}

/* Card Hover Effects */
.card {
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

/* Button Styling */
.btn-lg {
    padding: 12px 30px;
    font-size: 16px;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.btn-lg:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.sticky-top {
    top: 20px;
}
