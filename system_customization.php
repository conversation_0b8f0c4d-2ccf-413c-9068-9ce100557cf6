<?php
require_once 'includes/config/database.php';
require_once 'includes/helpers/functions.php';

// Check admin access
$currentUser = getCurrentUser();
if (!$currentUser || $currentUser['role'] !== 'admin') {
    setFlashMessage('danger', 'Akses ditolak! Anda tidak memiliki izin untuk mengakses halaman ini.');
    redirect('dashboard.php');
}

$pageTitle = 'System Customization';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    try {
        switch ($_POST['action']) {
            case 'update_sidebar':
                $sidebarSettings = [
                    'sidebar_style' => $_POST['sidebar_style'] ?? 'default',
                    'sidebar_color' => $_POST['sidebar_color'] ?? 'dark',
                    'sidebar_position' => $_POST['sidebar_position'] ?? 'left',
                    'sidebar_collapsed' => isset($_POST['sidebar_collapsed']) ? 1 : 0,
                    'sidebar_animation' => $_POST['sidebar_animation'] ?? 'slide'
                ];

                foreach ($sidebarSettings as $key => $value) {
                    $stmt = $pdo->prepare("INSERT INTO system_settings (setting_key, setting_value) VALUES (?, ?)
                                         ON DUPLICATE KEY UPDATE setting_value = ?");
                    $stmt->execute([$key, $value, $value]);
                }

                setFlashMessage('success', 'Pengaturan sidebar berhasil disimpan');
                break;

            case 'update_navbar':
                $navbarSettings = [
                    'navbar_style' => $_POST['navbar_style'] ?? 'default',
                    'navbar_color' => $_POST['navbar_color'] ?? 'primary',
                    'navbar_position' => $_POST['navbar_position'] ?? 'top',
                    'navbar_brand_show' => isset($_POST['navbar_brand_show']) ? 1 : 0,
                    'navbar_search_show' => isset($_POST['navbar_search_show']) ? 1 : 0
                ];

                foreach ($navbarSettings as $key => $value) {
                    $stmt = $pdo->prepare("INSERT INTO system_settings (setting_key, setting_value) VALUES (?, ?)
                                         ON DUPLICATE KEY UPDATE setting_value = ?");
                    $stmt->execute([$key, $value, $value]);
                }

                setFlashMessage('success', 'Pengaturan navbar berhasil disimpan');
                break;

            case 'update_effects':
                $effectSettings = [
                    'page_transition' => $_POST['page_transition'] ?? 'none',
                    'hover_effects' => isset($_POST['hover_effects']) ? 1 : 0,
                    'loading_animation' => $_POST['loading_animation'] ?? 'spinner',
                    'card_animations' => isset($_POST['card_animations']) ? 1 : 0,
                    'smooth_scrolling' => isset($_POST['smooth_scrolling']) ? 1 : 0
                ];

                foreach ($effectSettings as $key => $value) {
                    $stmt = $pdo->prepare("INSERT INTO system_settings (setting_key, setting_value) VALUES (?, ?)
                                         ON DUPLICATE KEY UPDATE setting_value = ?");
                    $stmt->execute([$key, $value, $value]);
                }

                setFlashMessage('success', 'Pengaturan efek berhasil disimpan');
                break;

            case 'update_general':
                $generalSettings = [
                    'site_title' => $_POST['site_title'] ?? 'Sistem Keuangan',
                    'site_description' => $_POST['site_description'] ?? '',
                    'theme_mode' => $_POST['theme_mode'] ?? 'light',
                    'language' => $_POST['language'] ?? 'id',
                    'timezone' => $_POST['timezone'] ?? 'Asia/Jakarta'
                ];

                foreach ($generalSettings as $key => $value) {
                    $stmt = $pdo->prepare("INSERT INTO system_settings (setting_key, setting_value) VALUES (?, ?)
                                         ON DUPLICATE KEY UPDATE setting_value = ?");
                    $stmt->execute([$key, $value, $value]);
                }

                setFlashMessage('success', 'Pengaturan umum berhasil disimpan');
                break;

            case 'reset_settings':
                $stmt = $pdo->prepare("DELETE FROM system_settings WHERE setting_key LIKE 'sidebar_%'
                                     OR setting_key LIKE 'navbar_%'
                                     OR setting_key IN ('page_transition', 'hover_effects', 'loading_animation', 'card_animations', 'smooth_scrolling')");
                $stmt->execute();

                setFlashMessage('success', 'Semua pengaturan berhasil direset ke default');
                break;
        }
        redirect('system_customization.php');
    } catch (Exception $e) {
        setFlashMessage('danger', 'Error: ' . $e->getMessage());
    }
}

// Get current settings
try {
    $stmt = $pdo->query("SELECT setting_key, setting_value FROM system_settings");
    $settings = [];
    while ($row = $stmt->fetch()) {
        $settings[$row['setting_key']] = $row['setting_value'];
    }
} catch (PDOException $e) {
    error_log("Error fetching settings: " . $e->getMessage());
    $settings = [];
}

// Helper function to get setting value
function getSetting($key, $default = '') {
    global $settings;
    return $settings[$key] ?? $default;
}

include 'includes/views/layouts/header.php';
?>

<div class="container-fluid px-4 py-3">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h3 class="mb-1">🎨 System Customization</h3>
                    <p class="text-muted mb-0">Complete system customization control center</p>
                </div>
                <div class="btn-group">
                    <a href="/keuangan/dashboard.php" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
                    </a>
                    <a href="customization_dashboard.php" class="btn btn-primary">
                        <i class="fas fa-tachometer-alt me-1"></i>Customization Dashboard
                    </a>
                    <form method="POST" class="d-inline" onsubmit="return confirm('Reset all customization settings to default?')">
                        <input type="hidden" name="action" value="reset_settings">
                        <button type="submit" class="btn btn-outline-danger">
                            <i class="fas fa-undo me-1"></i>Reset All
                        </button>
                    </form>
                </div>
            </div>

            <!-- Quick Stats -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body text-center">
                            <i class="fas fa-palette fa-2x mb-2"></i>
                            <h5>11</h5>
                            <small>Customization Tools</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <i class="fas fa-cogs fa-2x mb-2"></i>
                            <h5>25+</h5>
                            <small>Settings Available</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body text-center">
                            <i class="fas fa-swatchbook fa-2x mb-2"></i>
                            <h5>4</h5>
                            <small>Built-in Themes</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body text-center">
                            <i class="fas fa-magic fa-2x mb-2"></i>
                            <h5>Live</h5>
                            <small>Preview Mode</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Customization Tools Grid -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header bg-gradient-primary text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-tools me-2"></i>Advanced Customization Tools
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-lg-4 col-md-6 mb-3">
                                    <div class="card h-100 border-primary">
                                        <div class="card-body text-center">
                                            <i class="fas fa-tachometer-alt fa-3x text-primary mb-3"></i>
                                            <h6>Customization Dashboard</h6>
                                            <p class="text-muted small">Central control center</p>
                                            <a href="customization_dashboard.php" class="btn btn-primary btn-sm">
                                                <i class="fas fa-external-link-alt me-1"></i>Open
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-4 col-md-6 mb-3">
                                    <div class="card h-100 border-success">
                                        <div class="card-body text-center">
                                            <i class="fas fa-swatchbook fa-3x text-success mb-3"></i>
                                            <h6>Theme Manager</h6>
                                            <p class="text-muted small">Create & manage themes</p>
                                            <a href="theme_manager.php" class="btn btn-success btn-sm">
                                                <i class="fas fa-external-link-alt me-1"></i>Open
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-4 col-md-6 mb-3">
                                    <div class="card h-100 border-info">
                                        <div class="card-body text-center">
                                            <i class="fas fa-edit fa-3x text-info mb-3"></i>
                                            <h6>Menu Editor</h6>
                                            <p class="text-muted small">Drag & drop menu builder</p>
                                            <a href="menu_editor.php" class="btn btn-info btn-sm">
                                                <i class="fas fa-external-link-alt me-1"></i>Open
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-4 col-md-6 mb-3">
                                    <div class="card h-100 border-warning">
                                        <div class="card-body text-center">
                                            <i class="fas fa-th-large fa-3x text-warning mb-3"></i>
                                            <h6>Layout Builder</h6>
                                            <p class="text-muted small">Visual layout designer</p>
                                            <a href="layout_builder.php" class="btn btn-warning btn-sm">
                                                <i class="fas fa-external-link-alt me-1"></i>Open
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-4 col-md-6 mb-3">
                                    <div class="card h-100 border-danger">
                                        <div class="card-body text-center">
                                            <i class="fas fa-puzzle-piece fa-3x text-danger mb-3"></i>
                                            <h6>Widget Manager</h6>
                                            <p class="text-muted small">Manage custom widgets</p>
                                            <a href="widget_manager.php" class="btn btn-danger btn-sm">
                                                <i class="fas fa-external-link-alt me-1"></i>Open
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-4 col-md-6 mb-3">
                                    <div class="card h-100 border-secondary">
                                        <div class="card-body text-center">
                                            <i class="fas fa-file-alt fa-3x text-secondary mb-3"></i>
                                            <h6>Template Manager</h6>
                                            <p class="text-muted small">Advanced template system</p>
                                            <a href="template_manager.php" class="btn btn-secondary btn-sm">
                                                <i class="fas fa-external-link-alt me-1"></i>Open
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="text-center mt-3">
                                <a href="customization_dashboard.php" class="btn btn-lg btn-primary">
                                    <i class="fas fa-rocket me-2"></i>Go to Full Customization Dashboard
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Customization Tabs -->
            <div class="card">
                <div class="card-header">
                    <ul class="nav nav-tabs card-header-tabs" id="customizationTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="sidebar-tab" data-bs-toggle="tab" data-bs-target="#sidebar" type="button" role="tab">
                                <i class="fas fa-bars me-2"></i>Sidebar
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="navbar-tab" data-bs-toggle="tab" data-bs-target="#navbar" type="button" role="tab">
                                <i class="fas fa-window-maximize me-2"></i>Navbar
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="effects-tab" data-bs-toggle="tab" data-bs-target="#effects" type="button" role="tab">
                                <i class="fas fa-magic me-2"></i>Effects
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="general-tab" data-bs-toggle="tab" data-bs-target="#general" type="button" role="tab">
                                <i class="fas fa-cog me-2"></i>General
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="tools-tab" data-bs-toggle="tab" data-bs-target="#tools" type="button" role="tab">
                                <i class="fas fa-tools me-2"></i>Tools
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="preview-tab" data-bs-toggle="tab" data-bs-target="#preview" type="button" role="tab">
                                <i class="fas fa-eye me-2"></i>Preview
                            </button>
                        </li>
                    </ul>
                </div>
                <div class="card-body">
                    <div class="tab-content" id="customizationTabsContent">
                        <!-- Sidebar Settings -->
                        <div class="tab-pane fade show active" id="sidebar" role="tabpanel">
                            <form method="POST">
                                <input type="hidden" name="action" value="update_sidebar">

                                <div class="row">
                                    <div class="col-md-6">
                                        <h5 class="mb-3">Sidebar Configuration</h5>

                                        <div class="mb-3">
                                            <label class="form-label">Sidebar Style</label>
                                            <select class="form-select" name="sidebar_style">
                                                <option value="default" <?= getSetting('sidebar_style', 'default') === 'default' ? 'selected' : '' ?>>Default</option>
                                                <option value="modern" <?= getSetting('sidebar_style') === 'modern' ? 'selected' : '' ?>>Modern</option>
                                                <option value="minimal" <?= getSetting('sidebar_style') === 'minimal' ? 'selected' : '' ?>>Minimal</option>
                                                <option value="compact" <?= getSetting('sidebar_style') === 'compact' ? 'selected' : '' ?>>Compact</option>
                                            </select>
                                        </div>

                                        <div class="mb-3">
                                            <label class="form-label">Sidebar Color</label>
                                            <select class="form-select" name="sidebar_color">
                                                <option value="dark" <?= getSetting('sidebar_color', 'dark') === 'dark' ? 'selected' : '' ?>>Dark</option>
                                                <option value="light" <?= getSetting('sidebar_color') === 'light' ? 'selected' : '' ?>>Light</option>
                                                <option value="primary" <?= getSetting('sidebar_color') === 'primary' ? 'selected' : '' ?>>Primary</option>
                                                <option value="secondary" <?= getSetting('sidebar_color') === 'secondary' ? 'selected' : '' ?>>Secondary</option>
                                                <option value="success" <?= getSetting('sidebar_color') === 'success' ? 'selected' : '' ?>>Success</option>
                                            </select>
                                        </div>

                                        <div class="mb-3">
                                            <label class="form-label">Sidebar Position</label>
                                            <select class="form-select" name="sidebar_position">
                                                <option value="left" <?= getSetting('sidebar_position', 'left') === 'left' ? 'selected' : '' ?>>Left</option>
                                                <option value="right" <?= getSetting('sidebar_position') === 'right' ? 'selected' : '' ?>>Right</option>
                                            </select>
                                        </div>

                                        <div class="mb-3">
                                            <label class="form-label">Animation</label>
                                            <select class="form-select" name="sidebar_animation">
                                                <option value="slide" <?= getSetting('sidebar_animation', 'slide') === 'slide' ? 'selected' : '' ?>>Slide</option>
                                                <option value="fade" <?= getSetting('sidebar_animation') === 'fade' ? 'selected' : '' ?>>Fade</option>
                                                <option value="push" <?= getSetting('sidebar_animation') === 'push' ? 'selected' : '' ?>>Push</option>
                                                <option value="overlay" <?= getSetting('sidebar_animation') === 'overlay' ? 'selected' : '' ?>>Overlay</option>
                                            </select>
                                        </div>

                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="sidebar_collapsed" id="sidebar_collapsed"
                                                   <?= getSetting('sidebar_collapsed') ? 'checked' : '' ?>>
                                            <label class="form-check-label" for="sidebar_collapsed">
                                                Start Collapsed
                                            </label>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <h5 class="mb-3">Preview</h5>
                                        <div class="border rounded p-3" style="height: 300px; background: #f8f9fa;">
                                            <div id="sidebar-preview" class="d-flex h-100">
                                                <div class="sidebar-demo bg-dark text-white p-2" style="width: 200px;">
                                                    <div class="mb-2"><strong>Menu</strong></div>
                                                    <div class="small">Dashboard</div>
                                                    <div class="small">Notifications</div>
                                                    <div class="small">Settings</div>
                                                </div>
                                                <div class="flex-grow-1 p-2">
                                                    <div class="text-muted">Main Content Area</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="mt-4">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>Save Sidebar Settings
                                    </button>
                                </div>
                            </form>
                        </div>

                        <!-- Navbar Settings -->
                        <div class="tab-pane fade" id="navbar" role="tabpanel">
                            <form method="POST">
                                <input type="hidden" name="action" value="update_navbar">

                                <div class="row">
                                    <div class="col-md-6">
                                        <h5 class="mb-3">Navbar Configuration</h5>

                                        <div class="mb-3">
                                            <label class="form-label">Navbar Style</label>
                                            <select class="form-select" name="navbar_style">
                                                <option value="default" <?= getSetting('navbar_style', 'default') === 'default' ? 'selected' : '' ?>>Default</option>
                                                <option value="modern" <?= getSetting('navbar_style') === 'modern' ? 'selected' : '' ?>>Modern</option>
                                                <option value="minimal" <?= getSetting('navbar_style') === 'minimal' ? 'selected' : '' ?>>Minimal</option>
                                                <option value="glass" <?= getSetting('navbar_style') === 'glass' ? 'selected' : '' ?>>Glass Effect</option>
                                            </select>
                                        </div>

                                        <div class="mb-3">
                                            <label class="form-label">Navbar Color</label>
                                            <select class="form-select" name="navbar_color">
                                                <option value="primary" <?= getSetting('navbar_color', 'primary') === 'primary' ? 'selected' : '' ?>>Primary</option>
                                                <option value="dark" <?= getSetting('navbar_color') === 'dark' ? 'selected' : '' ?>>Dark</option>
                                                <option value="light" <?= getSetting('navbar_color') === 'light' ? 'selected' : '' ?>>Light</option>
                                                <option value="success" <?= getSetting('navbar_color') === 'success' ? 'selected' : '' ?>>Success</option>
                                                <option value="info" <?= getSetting('navbar_color') === 'info' ? 'selected' : '' ?>>Info</option>
                                                <option value="warning" <?= getSetting('navbar_color') === 'warning' ? 'selected' : '' ?>>Warning</option>
                                            </select>
                                        </div>

                                        <div class="mb-3">
                                            <label class="form-label">Navbar Position</label>
                                            <select class="form-select" name="navbar_position">
                                                <option value="top" <?= getSetting('navbar_position', 'top') === 'top' ? 'selected' : '' ?>>Top</option>
                                                <option value="fixed-top" <?= getSetting('navbar_position') === 'fixed-top' ? 'selected' : '' ?>>Fixed Top</option>
                                                <option value="sticky-top" <?= getSetting('navbar_position') === 'sticky-top' ? 'selected' : '' ?>>Sticky Top</option>
                                            </select>
                                        </div>

                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" name="navbar_brand_show" id="navbar_brand_show"
                                                   <?= getSetting('navbar_brand_show', '1') ? 'checked' : '' ?>>
                                            <label class="form-check-label" for="navbar_brand_show">
                                                Show Brand/Logo
                                            </label>
                                        </div>

                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="navbar_search_show" id="navbar_search_show"
                                                   <?= getSetting('navbar_search_show', '1') ? 'checked' : '' ?>>
                                            <label class="form-check-label" for="navbar_search_show">
                                                Show Search Box
                                            </label>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <h5 class="mb-3">Preview</h5>
                                        <div class="border rounded p-3" style="height: 300px; background: #f8f9fa;">
                                            <div id="navbar-preview">
                                                <div class="navbar-demo bg-primary text-white p-2 mb-3 rounded">
                                                    <div class="d-flex justify-content-between align-items-center">
                                                        <div><strong>Brand</strong></div>
                                                        <div class="d-flex gap-2">
                                                            <input type="text" class="form-control form-control-sm" placeholder="Search..." style="width: 150px;">
                                                            <div class="text-white">User</div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="text-muted">Page Content</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="mt-4">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>Save Navbar Settings
                                    </button>
                                </div>
                            </form>
                        </div>

                        <!-- Effects Settings -->
                        <div class="tab-pane fade" id="effects" role="tabpanel">
                            <form method="POST">
                                <input type="hidden" name="action" value="update_effects">

                                <div class="row">
                                    <div class="col-md-6">
                                        <h5 class="mb-3">Animation & Effects</h5>

                                        <div class="mb-3">
                                            <label class="form-label">Page Transition</label>
                                            <select class="form-select" name="page_transition">
                                                <option value="none" <?= getSetting('page_transition', 'none') === 'none' ? 'selected' : '' ?>>None</option>
                                                <option value="fade" <?= getSetting('page_transition') === 'fade' ? 'selected' : '' ?>>Fade</option>
                                                <option value="slide" <?= getSetting('page_transition') === 'slide' ? 'selected' : '' ?>>Slide</option>
                                                <option value="zoom" <?= getSetting('page_transition') === 'zoom' ? 'selected' : '' ?>>Zoom</option>
                                            </select>
                                        </div>

                                        <div class="mb-3">
                                            <label class="form-label">Loading Animation</label>
                                            <select class="form-select" name="loading_animation">
                                                <option value="spinner" <?= getSetting('loading_animation', 'spinner') === 'spinner' ? 'selected' : '' ?>>Spinner</option>
                                                <option value="dots" <?= getSetting('loading_animation') === 'dots' ? 'selected' : '' ?>>Dots</option>
                                                <option value="pulse" <?= getSetting('loading_animation') === 'pulse' ? 'selected' : '' ?>>Pulse</option>
                                                <option value="bars" <?= getSetting('loading_animation') === 'bars' ? 'selected' : '' ?>>Bars</option>
                                            </select>
                                        </div>

                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" name="hover_effects" id="hover_effects"
                                                   <?= getSetting('hover_effects', '1') ? 'checked' : '' ?>>
                                            <label class="form-check-label" for="hover_effects">
                                                Enable Hover Effects
                                            </label>
                                        </div>

                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" name="card_animations" id="card_animations"
                                                   <?= getSetting('card_animations') ? 'checked' : '' ?>>
                                            <label class="form-check-label" for="card_animations">
                                                Enable Card Animations
                                            </label>
                                        </div>

                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="smooth_scrolling" id="smooth_scrolling"
                                                   <?= getSetting('smooth_scrolling') ? 'checked' : '' ?>>
                                            <label class="form-check-label" for="smooth_scrolling">
                                                Enable Smooth Scrolling
                                            </label>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <h5 class="mb-3">Effect Demos</h5>
                                        <div class="border rounded p-3" style="height: 300px; background: #f8f9fa;">
                                            <div class="mb-3">
                                                <button class="btn btn-primary btn-sm demo-hover" onclick="demoHover(this)">Hover Effect Demo</button>
                                            </div>
                                            <div class="mb-3">
                                                <div class="card demo-card" style="width: 200px;">
                                                    <div class="card-body">
                                                        <h6 class="card-title">Card Animation</h6>
                                                        <p class="card-text small">Demo card with animation</p>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="mb-3">
                                                <button class="btn btn-info btn-sm" onclick="demoLoading()">Loading Demo</button>
                                                <div id="loading-demo" class="mt-2" style="display: none;">
                                                    <div class="spinner-border spinner-border-sm" role="status"></div>
                                                    <span class="ms-2">Loading...</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="mt-4">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>Save Effects Settings
                                    </button>
                                </div>
                            </form>
                        </div>

                        <!-- General Settings -->
                        <div class="tab-pane fade" id="general" role="tabpanel">
                            <form method="POST">
                                <input type="hidden" name="action" value="update_general">

                                <div class="row">
                                    <div class="col-md-6">
                                        <h5 class="mb-3">General Configuration</h5>

                                        <div class="mb-3">
                                            <label class="form-label">Site Title</label>
                                            <input type="text" class="form-control" name="site_title"
                                                   value="<?= htmlspecialchars(getSetting('site_title', 'Sistem Keuangan')) ?>"
                                                   placeholder="Enter site title">
                                        </div>

                                        <div class="mb-3">
                                            <label class="form-label">Site Description</label>
                                            <textarea class="form-control" name="site_description" rows="3"
                                                      placeholder="Enter site description"><?= htmlspecialchars(getSetting('site_description')) ?></textarea>
                                        </div>

                                        <div class="mb-3">
                                            <label class="form-label">Theme Mode</label>
                                            <select class="form-select" name="theme_mode">
                                                <option value="light" <?= getSetting('theme_mode', 'light') === 'light' ? 'selected' : '' ?>>Light</option>
                                                <option value="dark" <?= getSetting('theme_mode') === 'dark' ? 'selected' : '' ?>>Dark</option>
                                                <option value="auto" <?= getSetting('theme_mode') === 'auto' ? 'selected' : '' ?>>Auto (System)</option>
                                            </select>
                                        </div>

                                        <div class="mb-3">
                                            <label class="form-label">Language</label>
                                            <select class="form-select" name="language">
                                                <option value="id" <?= getSetting('language', 'id') === 'id' ? 'selected' : '' ?>>Bahasa Indonesia</option>
                                                <option value="en" <?= getSetting('language') === 'en' ? 'selected' : '' ?>>English</option>
                                            </select>
                                        </div>

                                        <div class="mb-3">
                                            <label class="form-label">Timezone</label>
                                            <select class="form-select" name="timezone">
                                                <option value="Asia/Jakarta" <?= getSetting('timezone', 'Asia/Jakarta') === 'Asia/Jakarta' ? 'selected' : '' ?>>Asia/Jakarta (WIB)</option>
                                                <option value="Asia/Makassar" <?= getSetting('timezone') === 'Asia/Makassar' ? 'selected' : '' ?>>Asia/Makassar (WITA)</option>
                                                <option value="Asia/Jayapura" <?= getSetting('timezone') === 'Asia/Jayapura' ? 'selected' : '' ?>>Asia/Jayapura (WIT)</option>
                                                <option value="UTC" <?= getSetting('timezone') === 'UTC' ? 'selected' : '' ?>>UTC</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <h5 class="mb-3">System Information</h5>
                                        <div class="border rounded p-3" style="height: 300px; background: #f8f9fa;">
                                            <div class="mb-2">
                                                <strong>Current Settings:</strong>
                                            </div>
                                            <div class="small">
                                                <div>Title: <?= htmlspecialchars(getSetting('site_title', 'Sistem Keuangan')) ?></div>
                                                <div>Theme: <?= ucfirst(getSetting('theme_mode', 'light')) ?></div>
                                                <div>Language: <?= getSetting('language', 'id') === 'id' ? 'Bahasa Indonesia' : 'English' ?></div>
                                                <div>Timezone: <?= getSetting('timezone', 'Asia/Jakarta') ?></div>
                                                <div class="mt-3">
                                                    <strong>System Stats:</strong>
                                                </div>
                                                <div>PHP Version: <?= PHP_VERSION ?></div>
                                                <div>Server: <?= $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown' ?></div>
                                                <div>Current Time: <?= date('Y-m-d H:i:s') ?></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="mt-4">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>Save General Settings
                                    </button>
                                </div>
                            </form>
                        </div>

                        <!-- Customization Tools Tab -->
                        <div class="tab-pane fade" id="tools" role="tabpanel">
                            <div class="row">
                                <div class="col-12">
                                    <h5 class="mb-3">🎨 Complete Customization Suite</h5>
                                    <p class="text-muted mb-4">Access all advanced customization tools from here. Each tool provides specialized functionality for different aspects of system customization.</p>

                                    <div class="row">
                                        <!-- Customization Dashboard -->
                                        <div class="col-lg-6 col-xl-4 mb-4">
                                            <div class="card h-100 border-primary">
                                                <div class="card-header bg-primary text-white">
                                                    <h6 class="mb-0">
                                                        <i class="fas fa-tachometer-alt me-2"></i>Customization Dashboard
                                                    </h6>
                                                </div>
                                                <div class="card-body">
                                                    <p class="card-text small">Central control center for all customization features with statistics and quick actions.</p>
                                                    <ul class="list-unstyled small">
                                                        <li><i class="fas fa-check text-success me-1"></i>Statistics Overview</li>
                                                        <li><i class="fas fa-check text-success me-1"></i>Quick Actions</li>
                                                        <li><i class="fas fa-check text-success me-1"></i>Recent Activities</li>
                                                        <li><i class="fas fa-check text-success me-1"></i>System Status</li>
                                                    </ul>
                                                </div>
                                                <div class="card-footer">
                                                    <a href="customization_dashboard.php" class="btn btn-primary btn-sm w-100">
                                                        <i class="fas fa-external-link-alt me-1"></i>Open Dashboard
                                                    </a>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Theme Manager -->
                                        <div class="col-lg-6 col-xl-4 mb-4">
                                            <div class="card h-100 border-success">
                                                <div class="card-header bg-success text-white">
                                                    <h6 class="mb-0">
                                                        <i class="fas fa-swatchbook me-2"></i>Theme Manager
                                                    </h6>
                                                </div>
                                                <div class="card-body">
                                                    <p class="card-text small">Create and manage custom themes with live preview and color customization.</p>
                                                    <ul class="list-unstyled small">
                                                        <li><i class="fas fa-check text-success me-1"></i>4 Predefined Themes</li>
                                                        <li><i class="fas fa-check text-success me-1"></i>Custom Theme Creator</li>
                                                        <li><i class="fas fa-check text-success me-1"></i>Live Preview</li>
                                                        <li><i class="fas fa-check text-success me-1"></i>Export/Import</li>
                                                    </ul>
                                                </div>
                                                <div class="card-footer">
                                                    <a href="theme_manager.php" class="btn btn-success btn-sm w-100">
                                                        <i class="fas fa-external-link-alt me-1"></i>Open Theme Manager
                                                    </a>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Menu Editor -->
                                        <div class="col-lg-6 col-xl-4 mb-4">
                                            <div class="card h-100 border-info">
                                                <div class="card-header bg-info text-white">
                                                    <h6 class="mb-0">
                                                        <i class="fas fa-edit me-2"></i>Menu Editor
                                                    </h6>
                                                </div>
                                                <div class="card-body">
                                                    <p class="card-text small">Advanced menu customization with drag & drop interface and icon selection.</p>
                                                    <ul class="list-unstyled small">
                                                        <li><i class="fas fa-check text-success me-1"></i>Drag & Drop Interface</li>
                                                        <li><i class="fas fa-check text-success me-1"></i>35+ FontAwesome Icons</li>
                                                        <li><i class="fas fa-check text-success me-1"></i>Live Preview</li>
                                                        <li><i class="fas fa-check text-success me-1"></i>Export/Import</li>
                                                    </ul>
                                                </div>
                                                <div class="card-footer">
                                                    <a href="menu_editor.php" class="btn btn-info btn-sm w-100">
                                                        <i class="fas fa-external-link-alt me-1"></i>Open Menu Editor
                                                    </a>
                                                </div>
                                            </div>
                                        </div>












                                    </div>

                                    <!-- Quick Access Section -->
                                    <div class="mt-4 p-4 bg-light rounded">
                                        <h6 class="mb-3">🚀 Quick Access</h6>
                                        <div class="row">
                                            <div class="col-md-4 mb-2">
                                                <a href="customization_dashboard.php" class="btn btn-outline-primary w-100">
                                                    <i class="fas fa-tachometer-alt me-2"></i>Main Dashboard
                                                </a>
                                            </div>

                                            <div class="col-md-4 mb-2">
                                                <a href="database_management.php" class="btn btn-outline-secondary w-100">
                                                    <i class="fas fa-database me-2"></i>Database Management
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Preview Tab -->
                        <div class="tab-pane fade" id="preview" role="tabpanel">
                            <div class="row">
                                <div class="col-12">
                                    <h5 class="mb-3">Live Preview</h5>
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle me-2"></i>
                                        This preview shows how your customizations will look. Save settings to apply changes system-wide.
                                    </div>

                                    <div class="border rounded p-3" style="min-height: 400px; background: #f8f9fa;">
                                        <div id="live-preview">
                                            <!-- Navbar Preview -->
                                            <div class="navbar-preview bg-primary text-white p-2 mb-3 rounded">
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <div><strong><?= htmlspecialchars(getSetting('site_title', 'Sistem Keuangan')) ?></strong></div>
                                                    <div class="d-flex gap-2 align-items-center">
                                                        <input type="text" class="form-control form-control-sm" placeholder="Search..." style="width: 150px;">
                                                        <div class="dropdown">
                                                            <button class="btn btn-sm btn-outline-light dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                                User
                                                            </button>
                                                            <ul class="dropdown-menu">
                                                                <li><a class="dropdown-item" href="#">Profile</a></li>
                                                                <li><a class="dropdown-item" href="#">Settings</a></li>
                                                                <li><hr class="dropdown-divider"></li>
                                                                <li><a class="dropdown-item" href="#">Logout</a></li>
                                                            </ul>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Content Area with Sidebar -->
                                            <div class="d-flex">
                                                <!-- Sidebar Preview -->
                                                <div class="sidebar-preview bg-dark text-white p-3 me-3 rounded" style="width: 200px; min-height: 300px;">
                                                    <div class="mb-3"><strong>Navigation</strong></div>
                                                    <div class="nav flex-column">
                                                        <a href="#" class="nav-link text-white-50 py-1">
                                                            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                                                        </a>
                                                        <a href="#" class="nav-link text-white py-1">
                                                            <i class="fas fa-bell me-2"></i>Notifications
                                                        </a>
                                                        <a href="#" class="nav-link text-white-50 py-1">
                                                            <i class="fas fa-users me-2"></i>Users
                                                        </a>
                                                        <a href="#" class="nav-link text-white-50 py-1">
                                                            <i class="fas fa-cog me-2"></i>Settings
                                                        </a>
                                                    </div>
                                                </div>

                                                <!-- Main Content -->
                                                <div class="flex-grow-1">
                                                    <div class="card">
                                                        <div class="card-header">
                                                            <h6 class="mb-0">Sample Content</h6>
                                                        </div>
                                                        <div class="card-body">
                                                            <p>This is how your customized interface will look.</p>
                                                            <div class="row">
                                                                <div class="col-md-4">
                                                                    <div class="card bg-primary text-white">
                                                                        <div class="card-body text-center">
                                                                            <h4>25</h4>
                                                                            <small>Total Items</small>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                                <div class="col-md-4">
                                                                    <div class="card bg-success text-white">
                                                                        <div class="card-body text-center">
                                                                            <h4>12</h4>
                                                                            <small>Active</small>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                                <div class="col-md-4">
                                                                    <div class="card bg-warning text-white">
                                                                        <div class="card-body text-center">
                                                                            <h4>3</h4>
                                                                            <small>Pending</small>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mt-3">
                                        <button class="btn btn-success" onclick="refreshPreview()">
                                            <i class="fas fa-sync me-2"></i>Refresh Preview
                                        </button>
                                        <button class="btn btn-info" onclick="exportSettings()">
                                            <i class="fas fa-download me-2"></i>Export Settings
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Custom Styles for Customization Page */
.demo-hover {
    transition: all 0.3s ease;
}

.demo-hover:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.demo-card {
    transition: all 0.3s ease;
    cursor: pointer;
}

.demo-card:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.sidebar-demo {
    transition: all 0.3s ease;
}

.navbar-demo {
    transition: all 0.3s ease;
}

.preview-container {
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    background: repeating-linear-gradient(
        45deg,
        transparent,
        transparent 10px,
        rgba(0,0,0,.05) 10px,
        rgba(0,0,0,.05) 20px
    );
}

.settings-preview {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 8px;
}

.nav-tabs .nav-link {
    border-radius: 8px 8px 0 0;
    margin-right: 4px;
}

.nav-tabs .nav-link.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-color: transparent;
}

.card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in {
    animation: slideIn 0.5s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

.zoom-in {
    animation: zoomIn 0.3s ease-out;
}

@keyframes zoomIn {
    from { transform: scale(0.8); opacity: 0; }
    to { transform: scale(1); opacity: 1; }
}
</style>

<script>
// Demo Functions
function demoHover(element) {
    element.classList.add('zoom-in');
    setTimeout(() => {
        element.classList.remove('zoom-in');
    }, 300);
}

function demoLoading() {
    const loadingDemo = document.getElementById('loading-demo');
    loadingDemo.style.display = 'block';
    setTimeout(() => {
        loadingDemo.style.display = 'none';
    }, 2000);
}

// Preview Functions
function refreshPreview() {
    const preview = document.getElementById('live-preview');
    preview.classList.add('fade-in');
    setTimeout(() => {
        preview.classList.remove('fade-in');
    }, 500);

    // Show success message
    showToast('Preview refreshed successfully!', 'success');
}

function exportSettings() {
    // Collect all current settings
    const settings = {
        sidebar: {
            style: document.querySelector('[name="sidebar_style"]')?.value || 'default',
            color: document.querySelector('[name="sidebar_color"]')?.value || 'dark',
            position: document.querySelector('[name="sidebar_position"]')?.value || 'left',
            collapsed: document.querySelector('[name="sidebar_collapsed"]')?.checked || false,
            animation: document.querySelector('[name="sidebar_animation"]')?.value || 'slide'
        },
        navbar: {
            style: document.querySelector('[name="navbar_style"]')?.value || 'default',
            color: document.querySelector('[name="navbar_color"]')?.value || 'primary',
            position: document.querySelector('[name="navbar_position"]')?.value || 'top',
            brand_show: document.querySelector('[name="navbar_brand_show"]')?.checked || true,
            search_show: document.querySelector('[name="navbar_search_show"]')?.checked || true
        },
        effects: {
            page_transition: document.querySelector('[name="page_transition"]')?.value || 'none',
            hover_effects: document.querySelector('[name="hover_effects"]')?.checked || true,
            loading_animation: document.querySelector('[name="loading_animation"]')?.value || 'spinner',
            card_animations: document.querySelector('[name="card_animations"]')?.checked || false,
            smooth_scrolling: document.querySelector('[name="smooth_scrolling"]')?.checked || false
        },
        general: {
            site_title: document.querySelector('[name="site_title"]')?.value || 'Sistem Keuangan',
            site_description: document.querySelector('[name="site_description"]')?.value || '',
            theme_mode: document.querySelector('[name="theme_mode"]')?.value || 'light',
            language: document.querySelector('[name="language"]')?.value || 'id',
            timezone: document.querySelector('[name="timezone"]')?.value || 'Asia/Jakarta'
        }
    };

    // Create and download JSON file
    const dataStr = JSON.stringify(settings, null, 2);
    const dataBlob = new Blob([dataStr], {type: 'application/json'});
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'system_customization_settings.json';
    link.click();
    URL.revokeObjectURL(url);

    showToast('Settings exported successfully!', 'success');
}

// Real-time preview updates
function updatePreview() {
    // Update sidebar preview
    const sidebarStyle = document.querySelector('[name="sidebar_style"]')?.value;
    const sidebarColor = document.querySelector('[name="sidebar_color"]')?.value;
    const sidebarPreview = document.querySelector('.sidebar-demo');

    if (sidebarPreview) {
        // Update sidebar styling based on selections
        sidebarPreview.className = `sidebar-demo bg-${sidebarColor} text-white p-2`;

        if (sidebarStyle === 'minimal') {
            sidebarPreview.style.width = '150px';
        } else if (sidebarStyle === 'compact') {
            sidebarPreview.style.width = '60px';
        } else {
            sidebarPreview.style.width = '200px';
        }
    }

    // Update navbar preview
    const navbarColor = document.querySelector('[name="navbar_color"]')?.value;
    const navbarPreview = document.querySelector('.navbar-demo');

    if (navbarPreview) {
        navbarPreview.className = `navbar-demo bg-${navbarColor} text-white p-2 mb-3 rounded`;
    }
}

// Toast notification function
function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    toast.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(toast);

    // Auto remove after 3 seconds
    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 3000);
}

// Initialize event listeners
document.addEventListener('DOMContentLoaded', function() {
    // Add change listeners to form elements for real-time preview
    const formElements = document.querySelectorAll('select, input[type="checkbox"]');
    formElements.forEach(element => {
        element.addEventListener('change', updatePreview);
    });

    // Initialize preview
    updatePreview();

    // Add animation to cards
    const cards = document.querySelectorAll('.card');
    cards.forEach((card, index) => {
        setTimeout(() => {
            card.classList.add('fade-in');
        }, index * 100);
    });
});

// Tab change handler
document.addEventListener('shown.bs.tab', function (e) {
    const targetTab = e.target.getAttribute('data-bs-target');

    // Add animation to tab content
    const tabContent = document.querySelector(targetTab);
    if (tabContent) {
        tabContent.classList.add('fade-in');
        setTimeout(() => {
            tabContent.classList.remove('fade-in');
        }, 500);
    }

    // Update preview when switching to preview tab
    if (targetTab === '#preview') {
        setTimeout(updatePreview, 100);
    }
});
</script>

<?php include 'includes/views/layouts/footer.php'; ?>
