<?php
/**
 * Multi-Currency Helper Functions
 * 
 * This file contains functions for multi-currency support
 */

/**
 * Get supported currencies
 * @return array List of supported currencies
 */
function getSupportedCurrencies() {
    return [
        'IDR' => [
            'name' => 'Indonesian Rupiah',
            'symbol' => 'Rp',
            'code' => 'IDR',
            'decimal_places' => 0,
            'is_default' => true
        ],
        'USD' => [
            'name' => 'US Dollar',
            'symbol' => '$',
            'code' => 'USD',
            'decimal_places' => 2,
            'is_default' => false
        ],
        'EUR' => [
            'name' => 'Euro',
            'symbol' => '€',
            'code' => 'EUR',
            'decimal_places' => 2,
            'is_default' => false
        ],
        'GBP' => [
            'name' => 'British Pound',
            'symbol' => '£',
            'code' => 'GBP',
            'decimal_places' => 2,
            'is_default' => false
        ],
        'JPY' => [
            'name' => 'Japanese Yen',
            'symbol' => '¥',
            'code' => 'JPY',
            'decimal_places' => 0,
            'is_default' => false
        ],
        'SGD' => [
            'name' => 'Singapore Dollar',
            'symbol' => 'S$',
            'code' => 'SGD',
            'decimal_places' => 2,
            'is_default' => false
        ],
        'MYR' => [
            'name' => 'Malaysian Ringgit',
            'symbol' => 'RM',
            'code' => 'MYR',
            'decimal_places' => 2,
            'is_default' => false
        ],
        'THB' => [
            'name' => 'Thai Baht',
            'symbol' => '฿',
            'code' => 'THB',
            'decimal_places' => 2,
            'is_default' => false
        ]
    ];
}

/**
 * Get default currency
 * @return string Default currency code
 */
function getDefaultCurrency() {
    return getSystemSetting('default_currency', 'IDR');
}

/**
 * Get user's preferred currency
 * @param int $userId User ID
 * @return string Currency code
 */
function getUserCurrency($userId) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("
            SELECT preferred_currency 
            FROM users 
            WHERE id = ?
        ");
        $stmt->execute([$userId]);
        
        $currency = $stmt->fetchColumn();
        return $currency ?: getDefaultCurrency();
        
    } catch (Exception $e) {
        error_log("Get user currency error: " . $e->getMessage());
        return getDefaultCurrency();
    }
}

/**
 * Set user's preferred currency
 * @param int $userId User ID
 * @param string $currencyCode Currency code
 * @return bool Success status
 */
function setUserCurrency($userId, $currencyCode) {
    global $pdo;
    
    try {
        $supportedCurrencies = getSupportedCurrencies();
        if (!isset($supportedCurrencies[$currencyCode])) {
            return false;
        }
        
        $stmt = $pdo->prepare("
            UPDATE users 
            SET preferred_currency = ? 
            WHERE id = ?
        ");
        
        return $stmt->execute([$currencyCode, $userId]);
        
    } catch (Exception $e) {
        error_log("Set user currency error: " . $e->getMessage());
        return false;
    }
}

/**
 * Get current exchange rates
 * @param string $baseCurrency Base currency code
 * @return array Exchange rates
 */
function getExchangeRates($baseCurrency = 'IDR') {
    global $pdo;
    
    try {
        // Try to get cached rates first
        $stmt = $pdo->prepare("
            SELECT rates_data, updated_at 
            FROM currency_rates 
            WHERE base_currency = ? 
            ORDER BY updated_at DESC 
            LIMIT 1
        ");
        $stmt->execute([$baseCurrency]);
        $cached = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // Check if cache is still valid (1 hour)
        if ($cached && (time() - strtotime($cached['updated_at'])) < 3600) {
            return json_decode($cached['rates_data'], true);
        }
        
        // Fetch new rates from API
        $rates = fetchExchangeRatesFromAPI($baseCurrency);
        
        if ($rates) {
            // Cache the rates
            cacheExchangeRates($baseCurrency, $rates);
            return $rates;
        }
        
        // Return cached rates if API fails
        if ($cached) {
            return json_decode($cached['rates_data'], true);
        }
        
        // Return default rates if nothing available
        return getDefaultExchangeRates($baseCurrency);
        
    } catch (Exception $e) {
        error_log("Get exchange rates error: " . $e->getMessage());
        return getDefaultExchangeRates($baseCurrency);
    }
}

/**
 * Fetch exchange rates from external API
 * @param string $baseCurrency Base currency code
 * @return array|false Exchange rates or false on failure
 */
function fetchExchangeRatesFromAPI($baseCurrency) {
    try {
        // Using exchangerate-api.com (free tier)
        $apiKey = getSystemSetting('exchange_api_key', '');
        
        if ($apiKey) {
            $url = "https://v6.exchangerate-api.com/v6/{$apiKey}/latest/{$baseCurrency}";
        } else {
            // Fallback to free service (limited requests)
            $url = "https://api.exchangerate-api.com/v4/latest/{$baseCurrency}";
        }
        
        $context = stream_context_create([
            'http' => [
                'timeout' => 10,
                'user_agent' => 'Sistem Keuangan/1.0'
            ]
        ]);
        
        $response = file_get_contents($url, false, $context);
        
        if ($response === false) {
            return false;
        }
        
        $data = json_decode($response, true);
        
        if (isset($data['rates'])) {
            return $data['rates'];
        }
        
        return false;
        
    } catch (Exception $e) {
        error_log("Fetch exchange rates API error: " . $e->getMessage());
        return false;
    }
}

/**
 * Cache exchange rates
 * @param string $baseCurrency Base currency code
 * @param array $rates Exchange rates
 * @return bool Success status
 */
function cacheExchangeRates($baseCurrency, $rates) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("
            INSERT INTO currency_rates (base_currency, rates_data, updated_at) 
            VALUES (?, ?, NOW())
            ON DUPLICATE KEY UPDATE 
            rates_data = VALUES(rates_data), 
            updated_at = VALUES(updated_at)
        ");
        
        return $stmt->execute([$baseCurrency, json_encode($rates)]);
        
    } catch (Exception $e) {
        error_log("Cache exchange rates error: " . $e->getMessage());
        return false;
    }
}

/**
 * Get default exchange rates (fallback)
 * @param string $baseCurrency Base currency code
 * @return array Default exchange rates
 */
function getDefaultExchangeRates($baseCurrency) {
    // Static fallback rates (should be updated regularly)
    $defaultRates = [
        'IDR' => [
            'USD' => 0.000067,
            'EUR' => 0.000061,
            'GBP' => 0.000053,
            'JPY' => 0.0097,
            'SGD' => 0.000090,
            'MYR' => 0.00031,
            'THB' => 0.0024,
            'IDR' => 1
        ],
        'USD' => [
            'IDR' => 15000,
            'EUR' => 0.91,
            'GBP' => 0.79,
            'JPY' => 145,
            'SGD' => 1.35,
            'MYR' => 4.65,
            'THB' => 36,
            'USD' => 1
        ]
    ];
    
    return $defaultRates[$baseCurrency] ?? $defaultRates['IDR'];
}

/**
 * Convert amount between currencies
 * @param float $amount Amount to convert
 * @param string $fromCurrency Source currency code
 * @param string $toCurrency Target currency code
 * @return float Converted amount
 */
function convertCurrency($amount, $fromCurrency, $toCurrency) {
    if ($fromCurrency === $toCurrency) {
        return $amount;
    }
    
    try {
        $rates = getExchangeRates($fromCurrency);
        
        if (isset($rates[$toCurrency])) {
            return $amount * $rates[$toCurrency];
        }
        
        // Try reverse conversion
        $reverseRates = getExchangeRates($toCurrency);
        if (isset($reverseRates[$fromCurrency])) {
            return $amount / $reverseRates[$fromCurrency];
        }
        
        return $amount; // Return original if conversion fails
        
    } catch (Exception $e) {
        error_log("Currency conversion error: " . $e->getMessage());
        return $amount;
    }
}

/**
 * Format amount with currency
 * @param float $amount Amount to format
 * @param string $currencyCode Currency code
 * @param bool $showCode Show currency code
 * @return string Formatted amount
 */
function formatCurrency($amount, $currencyCode = null, $showCode = false) {
    if (!$currencyCode) {
        $currencyCode = getDefaultCurrency();
    }
    
    $currencies = getSupportedCurrencies();
    $currency = $currencies[$currencyCode] ?? $currencies['IDR'];
    
    $decimals = $currency['decimal_places'];
    $symbol = $currency['symbol'];
    
    $formattedAmount = number_format($amount, $decimals, ',', '.');
    
    if ($showCode) {
        return $symbol . ' ' . $formattedAmount . ' ' . $currencyCode;
    }
    
    return $symbol . ' ' . $formattedAmount;
}

/**
 * Get currency conversion for display
 * @param float $amount Amount in base currency
 * @param string $baseCurrency Base currency code
 * @param string $targetCurrency Target currency code
 * @return array Conversion data
 */
function getCurrencyConversion($amount, $baseCurrency, $targetCurrency) {
    $convertedAmount = convertCurrency($amount, $baseCurrency, $targetCurrency);
    
    return [
        'original_amount' => $amount,
        'original_currency' => $baseCurrency,
        'converted_amount' => $convertedAmount,
        'target_currency' => $targetCurrency,
        'original_formatted' => formatCurrency($amount, $baseCurrency),
        'converted_formatted' => formatCurrency($convertedAmount, $targetCurrency),
        'exchange_rate' => $amount > 0 ? ($convertedAmount / $amount) : 0
    ];
}

/**
 * Create currency tables if not exist
 */
function createCurrencyTables() {
    global $pdo;
    
    try {
        // Create currency_rates table
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS currency_rates (
                id INT AUTO_INCREMENT PRIMARY KEY,
                base_currency VARCHAR(3) NOT NULL,
                rates_data JSON NOT NULL,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE KEY unique_base (base_currency)
            )
        ");
        
        // Add preferred_currency column to users table
        $pdo->exec("
            ALTER TABLE users 
            ADD COLUMN IF NOT EXISTS preferred_currency VARCHAR(3) DEFAULT 'IDR'
        ");
        
        // Add currency columns to transaction tables
        try {
            $pdo->exec("
                ALTER TABLE transaksi
                ADD COLUMN currency VARCHAR(3) DEFAULT 'IDR'
            ");
        } catch (Exception $e) {
            // Column might already exist
        }

        try {
            $pdo->exec("
                ALTER TABLE transaksi
                ADD COLUMN original_amount DECIMAL(15,2) NULL
            ");
        } catch (Exception $e) {
            // Column might already exist
        }

        try {
            $pdo->exec("
                ALTER TABLE transaksi
                ADD COLUMN original_currency VARCHAR(3) NULL
            ");
        } catch (Exception $e) {
            // Column might already exist
        }
        
        return true;
        
    } catch (Exception $e) {
        error_log("Create currency tables error: " . $e->getMessage());
        return false;
    }
}

/**
 * Update exchange rates (for cron job)
 */
function updateExchangeRates() {
    $supportedCurrencies = getSupportedCurrencies();
    $updated = 0;
    
    foreach ($supportedCurrencies as $code => $currency) {
        $rates = fetchExchangeRatesFromAPI($code);
        if ($rates) {
            cacheExchangeRates($code, $rates);
            $updated++;
        }
        
        // Add delay to avoid rate limiting
        sleep(1);
    }
    
    logSystemEvent("Exchange rates updated", 'info', [
        'currencies_updated' => $updated,
        'total_currencies' => count($supportedCurrencies)
    ]);
    
    return $updated;
}
?>
