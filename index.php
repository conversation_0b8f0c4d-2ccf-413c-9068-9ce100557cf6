<?php
require_once 'includes/config/database.php';
require_once 'includes/helpers/functions.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Reset redirect counter to prevent loops
if (isset($_SESSION['redirect_count'])) {
    unset($_SESSION['redirect_count']);
}

// Check if user is logged in with strict checking
if (isset($_SESSION['user_id']) && !empty($_SESSION['user_id'])) {
    // Validate the session by checking if user exists in database
    try {
        $stmt = $pdo->prepare("SELECT role FROM users WHERE id = ?");
        $stmt->execute([$_SESSION['user_id']]);
        $user = $stmt->fetch();

        if ($user) {
            // User exists, redirect based on role
            if ($user['role'] === 'admin') {
                header('Location: admin-dashboard.php');
            } else {
                header('Location: dashboard.php');
            }
        } else {
            // User not found in database, clear session and redirect to login
            session_unset();
            session_destroy();
            header('Location: login.php');
        }
    } catch (PDOException $e) {
        error_log("Index validation error: " . $e->getMessage());
        // Clear session on database error and redirect to login
        session_unset();
        session_destroy();
        header('Location: login.php');
    }
} else {
    // Redirect to login if not logged in
    header('Location: login.php');
}
exit;
?>

