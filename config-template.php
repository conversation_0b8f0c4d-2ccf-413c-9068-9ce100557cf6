<?php
/**
 * KeuanganKu - Database Configuration Template
 * Copy this file to your project and rename to config.php or database.php
 * Update the values according to your new PC setup
 */

// ===========================================
// DATABASE CONFIGURATION - PC BARU
// ===========================================

// Database connection settings for new PC
define('DB_HOST', 'localhost');           // Usually 'localhost' for XAMPP
define('DB_USERNAME', 'root');            // Default XAMPP username
define('DB_PASSWORD', '');                // Default XAMPP password (empty)
define('DB_NAME', 'keuanganku_db');      // Your database name
define('DB_CHARSET', 'utf8mb4');         // Character set
define('DB_COLLATE', 'utf8mb4_unicode_ci'); // Collation

// Alternative configuration array format
$database_config = [
    'host' => 'localhost',
    'username' => 'root',
    'password' => '',
    'database' => 'keuanganku_db',
    'charset' => 'utf8mb4',
    'collation' => 'utf8mb4_unicode_ci',
    'port' => 3306,
    'socket' => '',
    'prefix' => '',
    'strict' => true,
    'engine' => null,
];

// ===========================================
// APPLICATION SETTINGS
// ===========================================

// Base URL for new PC
define('BASE_URL', 'http://localhost/keuanganku/');
define('SITE_URL', 'http://localhost/keuanganku/');

// File paths
define('ROOT_PATH', $_SERVER['DOCUMENT_ROOT'] . '/keuanganku/');
define('UPLOAD_PATH', ROOT_PATH . 'uploads/');
define('ASSETS_PATH', ROOT_PATH . 'assets/');

// Application settings
define('APP_NAME', 'KeuanganKu');
define('APP_VERSION', '2.0');
define('APP_ENV', 'development'); // development, production

// ===========================================
// SECURITY SETTINGS
// ===========================================

// Session settings
define('SESSION_NAME', 'keuanganku_session');
define('SESSION_LIFETIME', 3600); // 1 hour

// Security keys (generate new ones for security)
define('ENCRYPTION_KEY', 'your-new-encryption-key-here');
define('HASH_SALT', 'your-new-hash-salt-here');

// ===========================================
// EMAIL SETTINGS (if used)
// ===========================================

define('MAIL_HOST', 'localhost');
define('MAIL_PORT', 587);
define('MAIL_USERNAME', '');
define('MAIL_PASSWORD', '');
define('MAIL_FROM_EMAIL', 'noreply@localhost');
define('MAIL_FROM_NAME', 'KeuanganKu System');

// ===========================================
// DEVELOPMENT SETTINGS
// ===========================================

if (APP_ENV === 'development') {
    // Show all errors in development
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
    ini_set('display_startup_errors', 1);
    
    // Debug mode
    define('DEBUG_MODE', true);
    define('LOG_ERRORS', true);
} else {
    // Hide errors in production
    error_reporting(0);
    ini_set('display_errors', 0);
    
    // Production mode
    define('DEBUG_MODE', false);
    define('LOG_ERRORS', true);
}

// ===========================================
// DATABASE CONNECTION FUNCTION
// ===========================================

/**
 * Create database connection using PDO
 */
function createDatabaseConnection() {
    try {
        $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
        $options = [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
        ];
        
        $pdo = new PDO($dsn, DB_USERNAME, DB_PASSWORD, $options);
        
        if (DEBUG_MODE) {
            echo "<!-- Database connected successfully -->\n";
        }
        
        return $pdo;
    } catch (PDOException $e) {
        if (DEBUG_MODE) {
            die("Database connection failed: " . $e->getMessage());
        } else {
            die("Database connection failed. Please check your configuration.");
        }
    }
}

/**
 * Create database connection using MySQLi
 */
function createMySQLiConnection() {
    $connection = new mysqli(DB_HOST, DB_USERNAME, DB_PASSWORD, DB_NAME);
    
    if ($connection->connect_error) {
        if (DEBUG_MODE) {
            die("Database connection failed: " . $connection->connect_error);
        } else {
            die("Database connection failed. Please check your configuration.");
        }
    }
    
    $connection->set_charset(DB_CHARSET);
    
    if (DEBUG_MODE) {
        echo "<!-- Database connected successfully -->\n";
    }
    
    return $connection;
}

// ===========================================
// UTILITY FUNCTIONS
// ===========================================

/**
 * Get base URL
 */
function getBaseUrl() {
    return BASE_URL;
}

/**
 * Get asset URL
 */
function getAssetUrl($path = '') {
    return BASE_URL . 'assets/' . ltrim($path, '/');
}

/**
 * Get upload URL
 */
function getUploadUrl($path = '') {
    return BASE_URL . 'uploads/' . ltrim($path, '/');
}

/**
 * Redirect to URL
 */
function redirect($url) {
    if (!headers_sent()) {
        header('Location: ' . $url);
        exit;
    } else {
        echo '<script>window.location.href="' . $url . '";</script>';
        exit;
    }
}

/**
 * Check if running on localhost
 */
function isLocalhost() {
    return in_array($_SERVER['HTTP_HOST'], ['localhost', '127.0.0.1', '::1']);
}

// ===========================================
// TIMEZONE SETTING
// ===========================================

// Set timezone for Indonesia
date_default_timezone_set('Asia/Jakarta');

// ===========================================
// AUTO-LOAD CONFIGURATION
// ===========================================

// Initialize session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_name(SESSION_NAME);
    session_start();
}

// Set memory limit for large operations
ini_set('memory_limit', '256M');

// Set maximum execution time
ini_set('max_execution_time', 300); // 5 minutes

// ===========================================
// ENVIRONMENT CHECK
// ===========================================

/**
 * Check if all required extensions are loaded
 */
function checkRequiredExtensions() {
    $required = ['mysqli', 'pdo', 'pdo_mysql', 'mbstring', 'openssl', 'curl', 'gd'];
    $missing = [];
    
    foreach ($required as $ext) {
        if (!extension_loaded($ext)) {
            $missing[] = $ext;
        }
    }
    
    if (!empty($missing)) {
        die("Missing required PHP extensions: " . implode(', ', $missing));
    }
    
    return true;
}

// Check extensions on load
if (DEBUG_MODE) {
    checkRequiredExtensions();
}

// ===========================================
// CONSTANTS FOR COMPATIBILITY
// ===========================================

// If your old code uses different constant names, add them here
if (!defined('DATABASE_HOST')) define('DATABASE_HOST', DB_HOST);
if (!defined('DATABASE_USER')) define('DATABASE_USER', DB_USERNAME);
if (!defined('DATABASE_PASS')) define('DATABASE_PASS', DB_PASSWORD);
if (!defined('DATABASE_NAME')) define('DATABASE_NAME', DB_NAME);

// ===========================================
// FINAL NOTES
// ===========================================

/*
SETUP INSTRUCTIONS:

1. Copy this file to your project root
2. Rename to config.php or database.php (whatever your project uses)
3. Update the database credentials above
4. Update BASE_URL to match your local setup
5. Generate new security keys for ENCRYPTION_KEY and HASH_SALT
6. Test the connection by accessing your website

SECURITY NOTES:

- Change ENCRYPTION_KEY and HASH_SALT to random strings
- Set APP_ENV to 'production' when deploying to live server
- Never commit real credentials to version control
- Use environment variables for sensitive data in production

TROUBLESHOOTING:

- If connection fails, check XAMPP is running
- Verify database name exists in phpMyAdmin
- Check PHP extensions are enabled
- Review error logs in C:\xampp\apache\logs\error.log
*/

?>
