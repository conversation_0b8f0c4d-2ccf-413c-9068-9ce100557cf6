/* ===== MODERN UI FRAMEWORK ===== */
/* Modern CSS Framework untuk KeuanganKu */

:root {
    /* Modern Color Palette */
    --modern-primary: #2563eb;
    --modern-primary-light: #3b82f6;
    --modern-primary-dark: #1d4ed8;
    --modern-secondary: #64748b;
    --modern-success: #10b981;
    --modern-danger: #ef4444;
    --modern-warning: #f59e0b;
    --modern-info: #06b6d4;
    
    /* Neutral Colors */
    --modern-white: #ffffff;
    --modern-gray-50: #f8fafc;
    --modern-gray-100: #f1f5f9;
    --modern-gray-200: #e2e8f0;
    --modern-gray-300: #cbd5e1;
    --modern-gray-400: #94a3b8;
    --modern-gray-500: #64748b;
    --modern-gray-600: #475569;
    --modern-gray-700: #334155;
    --modern-gray-800: #1e293b;
    --modern-gray-900: #0f172a;
    
    /* Spacing */
    --modern-space-xs: 0.25rem;
    --modern-space-sm: 0.5rem;
    --modern-space-md: 1rem;
    --modern-space-lg: 1.5rem;
    --modern-space-xl: 2rem;
    --modern-space-2xl: 3rem;
    
    /* Border Radius */
    --modern-radius-sm: 0.375rem;
    --modern-radius-md: 0.5rem;
    --modern-radius-lg: 0.75rem;
    --modern-radius-xl: 1rem;
    
    /* Shadows */
    --modern-shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --modern-shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --modern-shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --modern-shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    
    /* Typography */
    --modern-font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --modern-font-size-xs: 0.75rem;
    --modern-font-size-sm: 0.875rem;
    --modern-font-size-base: 1rem;
    --modern-font-size-lg: 1.125rem;
    --modern-font-size-xl: 1.25rem;
    --modern-font-size-2xl: 1.5rem;
    --modern-font-size-3xl: 1.875rem;
    
    /* Transitions */
    --modern-transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    --modern-transition-fast: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
}

/* ===== LAYOUT SYSTEM ===== */
.modern-layout {
    min-height: 100vh;
    background: var(--modern-gray-50);
    font-family: var(--modern-font-family);
}

.modern-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: var(--modern-space-lg);
}

/* ===== GRID SYSTEM ===== */
.modern-grid {
    display: grid;
    gap: var(--modern-space-md);
}

.modern-grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.modern-grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.modern-grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.modern-grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }

/* ===== FLEXBOX UTILITIES ===== */
.modern-flex { display: flex; }
.modern-flex-col { flex-direction: column; }
.modern-flex-wrap { flex-wrap: wrap; }
.modern-flex-1 { flex: 1 1 0%; }
.modern-justify-start { justify-content: flex-start; }
.modern-justify-center { justify-content: center; }
.modern-justify-between { justify-content: space-between; }
.modern-justify-end { justify-content: flex-end; }
.modern-items-start { align-items: flex-start; }
.modern-items-center { align-items: center; }
.modern-items-end { align-items: flex-end; }

/* ===== SPACING UTILITIES ===== */
.modern-gap-xs { gap: var(--modern-space-xs); }
.modern-gap-sm { gap: var(--modern-space-sm); }
.modern-gap-md { gap: var(--modern-space-md); }
.modern-gap-lg { gap: var(--modern-space-lg); }
.modern-gap-xl { gap: var(--modern-space-xl); }

.modern-p-0 { padding: 0; }
.modern-p-xs { padding: var(--modern-space-xs); }
.modern-p-sm { padding: var(--modern-space-sm); }
.modern-p-md { padding: var(--modern-space-md); }
.modern-p-lg { padding: var(--modern-space-lg); }

.modern-m-0 { margin: 0; }
.modern-mb-0 { margin-bottom: 0; }
.modern-mb-xs { margin-bottom: var(--modern-space-xs); }
.modern-mb-sm { margin-bottom: var(--modern-space-sm); }
.modern-mb-md { margin-bottom: var(--modern-space-md); }
.modern-mb-lg { margin-bottom: var(--modern-space-lg); }
.modern-mb-xl { margin-bottom: var(--modern-space-xl); }

.modern-mr-xs { margin-right: var(--modern-space-xs); }
.modern-mr-sm { margin-right: var(--modern-space-sm); }
.modern-mt-xs { margin-top: var(--modern-space-xs); }

/* ===== TEXT UTILITIES ===== */
.modern-text-left { text-align: left; }
.modern-text-center { text-align: center; }
.modern-text-right { text-align: right; }

.modern-text-xs { font-size: var(--modern-font-size-xs); }
.modern-text-sm { font-size: var(--modern-font-size-sm); }
.modern-text-base { font-size: var(--modern-font-size-base); }
.modern-text-lg { font-size: var(--modern-font-size-lg); }
.modern-text-xl { font-size: var(--modern-font-size-xl); }

.modern-font-medium { font-weight: 500; }
.modern-font-semibold { font-weight: 600; }
.modern-font-bold { font-weight: 700; }

.modern-text-primary { color: var(--modern-primary); }
.modern-text-secondary { color: var(--modern-secondary); }
.modern-text-success { color: var(--modern-success); }
.modern-text-danger { color: var(--modern-danger); }
.modern-text-warning { color: var(--modern-warning); }
.modern-text-info { color: var(--modern-info); }
.modern-text-muted { color: var(--modern-gray-500); }

/* ===== PAGE HEADER ===== */
.modern-page-header {
    margin-bottom: var(--modern-space-xl);
    padding-bottom: var(--modern-space-lg);
    border-bottom: 1px solid var(--modern-gray-200);
}

.modern-page-title {
    font-size: var(--modern-font-size-3xl);
    font-weight: 700;
    color: var(--modern-gray-900);
    margin: 0;
}

.modern-page-subtitle {
    font-size: var(--modern-font-size-lg);
    color: var(--modern-gray-600);
    margin-top: var(--modern-space-xs);
    margin-bottom: 0;
}

.modern-page-actions {
    display: flex;
    gap: var(--modern-space-sm);
    align-items: center;
}

/* ===== CARDS ===== */
.modern-card {
    background: var(--modern-white);
    border-radius: var(--modern-radius-lg);
    box-shadow: var(--modern-shadow-sm);
    border: 1px solid var(--modern-gray-200);
    overflow: hidden;
    transition: var(--modern-transition);
}

.modern-card:hover {
    box-shadow: var(--modern-shadow-md);
}

.modern-card-header {
    padding: var(--modern-space-lg);
    border-bottom: 1px solid var(--modern-gray-200);
    background: var(--modern-gray-50);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modern-card-title {
    font-size: var(--modern-font-size-lg);
    font-weight: 600;
    color: var(--modern-gray-900);
    margin: 0;
    display: flex;
    align-items: center;
    gap: var(--modern-space-sm);
}

.modern-card-actions {
    display: flex;
    gap: var(--modern-space-sm);
    align-items: center;
}

.modern-card-body {
    padding: var(--modern-space-lg);
}

.modern-card-footer {
    padding: var(--modern-space-lg);
    border-top: 1px solid var(--modern-gray-200);
    background: var(--modern-gray-50);
}

/* ===== BUTTONS ===== */
.modern-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--modern-space-sm);
    padding: var(--modern-space-sm) var(--modern-space-lg);
    font-size: var(--modern-font-size-sm);
    font-weight: 500;
    border-radius: var(--modern-radius-md);
    border: 1px solid transparent;
    cursor: pointer;
    transition: var(--modern-transition);
    text-decoration: none;
    white-space: nowrap;
}

.modern-btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--modern-shadow-md);
}

.modern-btn:active {
    transform: translateY(0);
}

.modern-btn-sm {
    padding: var(--modern-space-xs) var(--modern-space-sm);
    font-size: var(--modern-font-size-xs);
    gap: var(--modern-space-xs);
}

.modern-btn-primary {
    background: var(--modern-primary);
    color: var(--modern-white);
    border-color: var(--modern-primary);
}

.modern-btn-primary:hover {
    background: var(--modern-primary-dark);
    border-color: var(--modern-primary-dark);
    color: var(--modern-white);
}

.modern-btn-success {
    background: var(--modern-success);
    color: var(--modern-white);
    border-color: var(--modern-success);
}

.modern-btn-danger {
    background: var(--modern-danger);
    color: var(--modern-white);
    border-color: var(--modern-danger);
}

.modern-btn-light {
    background: var(--modern-white);
    color: var(--modern-gray-700);
    border-color: var(--modern-gray-300);
}

.modern-btn-light:hover {
    background: var(--modern-gray-50);
    color: var(--modern-gray-900);
}

/* ===== BADGES ===== */
.modern-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--modern-space-xs);
    padding: var(--modern-space-xs) var(--modern-space-sm);
    font-size: var(--modern-font-size-xs);
    font-weight: 500;
    border-radius: var(--modern-radius-sm);
    white-space: nowrap;
}

.modern-badge-sm {
    padding: 2px var(--modern-space-xs);
    font-size: 0.6875rem;
}

.modern-badge-primary {
    background: rgba(37, 99, 235, 0.1);
    color: var(--modern-primary);
}

.modern-badge-success {
    background: rgba(16, 185, 129, 0.1);
    color: var(--modern-success);
}

.modern-badge-danger {
    background: rgba(239, 68, 68, 0.1);
    color: var(--modern-danger);
}

.modern-badge-warning {
    background: rgba(245, 158, 11, 0.1);
    color: var(--modern-warning);
}

.modern-badge-info {
    background: rgba(6, 182, 212, 0.1);
    color: var(--modern-info);
}

/* ===== FORMS ===== */
.modern-form-group {
    margin-bottom: var(--modern-space-lg);
}

.modern-form-label {
    display: flex;
    align-items: center;
    gap: var(--modern-space-sm);
    font-size: var(--modern-font-size-sm);
    font-weight: 500;
    color: var(--modern-gray-700);
    margin-bottom: var(--modern-space-sm);
}

.modern-form-control {
    width: 100%;
    padding: var(--modern-space-sm) var(--modern-space-md);
    font-size: var(--modern-font-size-sm);
    border: 1px solid var(--modern-gray-300);
    border-radius: var(--modern-radius-md);
    background: var(--modern-white);
    transition: var(--modern-transition);
}

.modern-form-control:focus {
    outline: none;
    border-color: var(--modern-primary);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.modern-form-help {
    font-size: var(--modern-font-size-xs);
    color: var(--modern-gray-500);
    margin-top: var(--modern-space-xs);
}

.modern-input-group {
    display: flex;
    align-items: stretch;
}

.modern-input-group-text {
    display: flex;
    align-items: center;
    padding: var(--modern-space-sm) var(--modern-space-md);
    font-size: var(--modern-font-size-sm);
    background: var(--modern-gray-100);
    border: 1px solid var(--modern-gray-300);
    border-right: 0;
    border-radius: var(--modern-radius-md) 0 0 var(--modern-radius-md);
    color: var(--modern-gray-600);
}

.modern-input-group .modern-form-control {
    border-radius: 0 var(--modern-radius-md) var(--modern-radius-md) 0;
    border-left: 0;
}

/* ===== TABLES ===== */
.modern-table-responsive {
    overflow-x: auto;
    border-radius: var(--modern-radius-lg);
}

.modern-table {
    width: 100%;
    border-collapse: collapse;
    font-size: var(--modern-font-size-sm);
}

.modern-table-header {
    background: var(--modern-gray-50);
}

.modern-table-th {
    padding: var(--modern-space-md) var(--modern-space-lg);
    text-align: left;
    font-weight: 600;
    color: var(--modern-gray-700);
    border-bottom: 1px solid var(--modern-gray-200);
    white-space: nowrap;
}

.modern-table-body {
    background: var(--modern-white);
}

.modern-table-row {
    transition: var(--modern-transition);
}

.modern-table-row:hover {
    background: var(--modern-gray-50);
}

.modern-table-td {
    padding: var(--modern-space-md) var(--modern-space-lg);
    border-bottom: 1px solid var(--modern-gray-100);
    vertical-align: middle;
}

.modern-table-content {
    display: flex;
    flex-direction: column;
    gap: var(--modern-space-xs);
}

.modern-table-title {
    font-weight: 500;
    color: var(--modern-gray-900);
}

.modern-table-subtitle {
    font-size: var(--modern-font-size-xs);
    color: var(--modern-gray-500);
}

.modern-table-amount {
    font-weight: 600;
    font-family: 'JetBrains Mono', monospace;
}

.modern-table-date {
    display: flex;
    align-items: center;
    gap: var(--modern-space-xs);
    color: var(--modern-gray-600);
    font-size: var(--modern-font-size-xs);
}

/* ===== ACTION BUTTONS ===== */
.modern-action-buttons {
    display: flex;
    gap: var(--modern-space-xs);
    justify-content: center;
}

/* ===== EMPTY STATE ===== */
.modern-empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--modern-space-2xl);
    text-align: center;
}

.modern-empty-icon {
    width: 64px;
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--modern-gray-100);
    border-radius: 50%;
    margin-bottom: var(--modern-space-lg);
    color: var(--modern-gray-400);
    font-size: var(--modern-font-size-2xl);
}

.modern-empty-content {
    max-width: 400px;
}

.modern-empty-title {
    font-size: var(--modern-font-size-lg);
    font-weight: 600;
    color: var(--modern-gray-900);
    margin-bottom: var(--modern-space-sm);
}

.modern-empty-text {
    color: var(--modern-gray-600);
    margin-bottom: var(--modern-space-lg);
}

/* ===== STATISTICS CARDS ===== */
.modern-stats-card {
    position: relative;
    background: var(--modern-white);
    border-radius: var(--modern-radius-xl);
    padding: var(--modern-space-xl);
    border: 1px solid var(--modern-gray-200);
    overflow: hidden;
    transition: var(--modern-transition);
}

.modern-stats-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--modern-shadow-lg);
}

.modern-stats-content {
    position: relative;
    z-index: 2;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modern-stats-info {
    flex: 1;
}

.modern-stats-label {
    font-size: var(--modern-font-size-sm);
    font-weight: 500;
    color: var(--modern-gray-600);
    margin-bottom: var(--modern-space-xs);
}

.modern-stats-value {
    font-size: var(--modern-font-size-2xl);
    font-weight: 700;
    margin-bottom: var(--modern-space-xs);
}

.modern-stats-meta {
    font-size: var(--modern-font-size-xs);
    color: var(--modern-gray-500);
}

.modern-stats-icon {
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--modern-radius-lg);
    font-size: var(--modern-font-size-xl);
    opacity: 0.8;
}

.modern-stats-glow {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0.05;
    border-radius: var(--modern-radius-xl);
    transition: var(--modern-transition);
}

.modern-stats-primary .modern-stats-value { color: var(--modern-primary); }
.modern-stats-primary .modern-stats-icon { background: rgba(37, 99, 235, 0.1); color: var(--modern-primary); }
.modern-stats-primary .modern-stats-glow { background: var(--modern-primary); }

.modern-stats-success .modern-stats-value { color: var(--modern-success); }
.modern-stats-success .modern-stats-icon { background: rgba(16, 185, 129, 0.1); color: var(--modern-success); }
.modern-stats-success .modern-stats-glow { background: var(--modern-success); }

.modern-stats-danger .modern-stats-value { color: var(--modern-danger); }
.modern-stats-danger .modern-stats-icon { background: rgba(239, 68, 68, 0.1); color: var(--modern-danger); }
.modern-stats-danger .modern-stats-glow { background: var(--modern-danger); }

.modern-stats-warning .modern-stats-value { color: var(--modern-warning); }
.modern-stats-warning .modern-stats-icon { background: rgba(245, 158, 11, 0.1); color: var(--modern-warning); }
.modern-stats-warning .modern-stats-glow { background: var(--modern-warning); }

.modern-stats-info .modern-stats-value { color: var(--modern-info); }
.modern-stats-info .modern-stats-icon { background: rgba(6, 182, 212, 0.1); color: var(--modern-info); }
.modern-stats-info .modern-stats-glow { background: var(--modern-info); }

/* ===== PROGRESS BARS ===== */
.modern-progress {
    width: 100%;
    height: 4px;
    background: var(--modern-gray-200);
    border-radius: var(--modern-radius-sm);
    overflow: hidden;
}

.modern-progress-bar {
    height: 100%;
    border-radius: var(--modern-radius-sm);
    transition: var(--modern-transition);
}

.modern-progress-primary { background: var(--modern-primary); }
.modern-progress-success { background: var(--modern-success); }
.modern-progress-danger { background: var(--modern-danger); }
.modern-progress-warning { background: var(--modern-warning); }

/* ===== MODALS ===== */
.modern-modal-content {
    background: var(--modern-white);
    border-radius: var(--modern-radius-xl);
    border: none;
    box-shadow: var(--modern-shadow-xl);
    overflow: hidden;
}

.modern-modal-header {
    padding: var(--modern-space-xl);
    border-bottom: 1px solid var(--modern-gray-200);
    background: var(--modern-gray-50);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modern-modal-title {
    font-size: var(--modern-font-size-xl);
    font-weight: 600;
    color: var(--modern-gray-900);
    margin: 0;
    display: flex;
    align-items: center;
    gap: var(--modern-space-sm);
}

.modern-modal-body {
    padding: var(--modern-space-xl);
}

.modern-modal-footer {
    padding: var(--modern-space-xl);
    border-top: 1px solid var(--modern-gray-200);
    background: var(--modern-gray-50);
    display: flex;
    justify-content: flex-end;
    gap: var(--modern-space-sm);
}

/* ===== PAGINATION ===== */
.modern-pagination-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: var(--modern-space-md);
}

.modern-pagination-info {
    font-size: var(--modern-font-size-sm);
    color: var(--modern-gray-600);
}

.modern-pagination {
    display: flex;
    align-items: center;
    gap: var(--modern-space-sm);
}

.modern-pagination-btn {
    display: flex;
    align-items: center;
    gap: var(--modern-space-xs);
    padding: var(--modern-space-sm) var(--modern-space-md);
    font-size: var(--modern-font-size-sm);
    font-weight: 500;
    color: var(--modern-gray-700);
    background: var(--modern-white);
    border: 1px solid var(--modern-gray-300);
    border-radius: var(--modern-radius-md);
    text-decoration: none;
    transition: var(--modern-transition);
}

.modern-pagination-btn:hover {
    background: var(--modern-gray-50);
    color: var(--modern-gray-900);
    text-decoration: none;
}

.modern-pagination-numbers {
    display: flex;
    align-items: center;
    gap: var(--modern-space-xs);
}

.modern-pagination-number {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    font-size: var(--modern-font-size-sm);
    font-weight: 500;
    color: var(--modern-gray-700);
    background: var(--modern-white);
    border: 1px solid var(--modern-gray-300);
    border-radius: var(--modern-radius-md);
    text-decoration: none;
    transition: var(--modern-transition);
}

.modern-pagination-number:hover {
    background: var(--modern-gray-50);
    color: var(--modern-gray-900);
    text-decoration: none;
}

.modern-pagination-number.active {
    background: var(--modern-primary);
    color: var(--modern-white);
    border-color: var(--modern-primary);
}

.modern-pagination-dots {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    color: var(--modern-gray-400);
    font-weight: 500;
}

/* ===== ALERTS ===== */
.modern-alert {
    display: flex;
    align-items: flex-start;
    gap: var(--modern-space-md);
    padding: var(--modern-space-lg);
    border-radius: var(--modern-radius-lg);
    border: 1px solid;
    margin-bottom: var(--modern-space-lg);
}

.modern-alert-content {
    display: flex;
    align-items: center;
    gap: var(--modern-space-sm);
    flex: 1;
}

.modern-alert-icon {
    font-size: var(--modern-font-size-lg);
}

.modern-alert-message {
    flex: 1;
    font-size: var(--modern-font-size-sm);
    font-weight: 500;
}

.modern-alert-close {
    background: none;
    border: none;
    padding: var(--modern-space-xs);
    cursor: pointer;
    border-radius: var(--modern-radius-sm);
    transition: var(--modern-transition);
}

.modern-alert-close:hover {
    background: rgba(0, 0, 0, 0.1);
}

.modern-alert-success {
    background: rgba(16, 185, 129, 0.1);
    border-color: var(--modern-success);
    color: var(--modern-success);
}

.modern-alert-danger {
    background: rgba(239, 68, 68, 0.1);
    border-color: var(--modern-danger);
    color: var(--modern-danger);
}

.modern-alert-warning {
    background: rgba(245, 158, 11, 0.1);
    border-color: var(--modern-warning);
    color: var(--modern-warning);
}

.modern-alert-info {
    background: rgba(6, 182, 212, 0.1);
    border-color: var(--modern-info);
    color: var(--modern-info);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .modern-container {
        padding: var(--modern-space-md);
    }

    .modern-grid-cols-2,
    .modern-grid-cols-3,
    .modern-grid-cols-4 {
        grid-template-columns: 1fr;
    }

    .modern-page-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--modern-space-md);
    }

    .modern-page-actions {
        width: 100%;
        justify-content: stretch;
    }

    .modern-page-actions .modern-btn {
        flex: 1;
        justify-content: center;
    }

    .modern-card-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--modern-space-sm);
    }

    .modern-stats-card {
        padding: var(--modern-space-lg);
    }

    .modern-stats-content {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--modern-space-md);
    }

    .modern-table-responsive {
        font-size: var(--modern-font-size-xs);
    }

    .modern-table-th,
    .modern-table-td {
        padding: var(--modern-space-sm);
    }

    .modern-pagination-wrapper {
        flex-direction: column;
        align-items: stretch;
        text-align: center;
    }

    .modern-pagination {
        justify-content: center;
        flex-wrap: wrap;
    }
}

/* ===== DARK MODE SUPPORT ===== */
[data-bs-theme="dark"] {
    --modern-white: #1e293b;
    --modern-gray-50: #0f172a;
    --modern-gray-100: #1e293b;
    --modern-gray-200: #334155;
    --modern-gray-300: #475569;
    --modern-gray-400: #64748b;
    --modern-gray-500: #94a3b8;
    --modern-gray-600: #cbd5e1;
    --modern-gray-700: #e2e8f0;
    --modern-gray-800: #f1f5f9;
    --modern-gray-900: #f8fafc;
}

/* ===== ANIMATIONS ===== */
@keyframes modern-fade-in {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modern-fade-in {
    animation: modern-fade-in 0.3s ease-out;
}

@keyframes modern-slide-up {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modern-slide-up {
    animation: modern-slide-up 0.4s ease-out;
}

/* ===== MODERN CALCULATOR STYLES ===== */
.modern-calculator {
    background: var(--modern-white);
    border-radius: var(--modern-radius-xl);
    padding: var(--modern-space-xl);
    border: 1px solid var(--modern-gray-200);
    box-shadow: var(--modern-shadow-lg);
}

.modern-calculator-display {
    margin-bottom: var(--modern-space-xl);
}

.modern-calculator-screen {
    width: 100%;
    height: 4rem;
    background: var(--modern-gray-900);
    border: 2px solid var(--modern-gray-300);
    border-radius: var(--modern-radius-lg);
    padding: 0 var(--modern-space-lg);
    font-size: 1.75rem;
    font-weight: 700;
    text-align: right;
    color: var(--modern-white);
    font-family: 'JetBrains Mono', 'Courier New', monospace;
    box-shadow: inset 0 2px 8px rgba(0, 0, 0, 0.2);
    letter-spacing: 0.05em;
}

.modern-calculator-buttons {
    display: flex;
    flex-direction: column;
    gap: var(--modern-space-md);
}

.modern-calculator-row {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: var(--modern-space-md);
}

.modern-calc-btn {
    height: 3.5rem;
    border: none;
    border-radius: var(--modern-radius-lg);
    font-size: 1.25rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.modern-calc-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.modern-calc-btn:hover::before {
    left: 100%;
}

.modern-calc-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
}

.modern-calc-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.modern-calc-btn-number {
    background: linear-gradient(135deg, var(--modern-white) 0%, var(--modern-gray-50) 100%);
    color: var(--modern-gray-900);
    border: 1px solid var(--modern-gray-200);
}

.modern-calc-btn-number:hover {
    background: linear-gradient(135deg, var(--modern-gray-50) 0%, var(--modern-gray-100) 100%);
    border-color: var(--modern-primary);
}

.modern-calc-btn-operator {
    background: linear-gradient(135deg, var(--modern-primary) 0%, var(--modern-primary-dark) 100%);
    color: var(--modern-white);
    box-shadow: 0 4px 6px rgba(37, 99, 235, 0.3);
}

.modern-calc-btn-operator:hover {
    background: linear-gradient(135deg, var(--modern-primary-dark) 0%, var(--modern-primary) 100%);
    box-shadow: 0 8px 15px rgba(37, 99, 235, 0.4);
}

.modern-calc-btn-clear {
    background: linear-gradient(135deg, var(--modern-danger) 0%, #dc2626 100%);
    color: var(--modern-white);
    box-shadow: 0 4px 6px rgba(239, 68, 68, 0.3);
}

.modern-calc-btn-clear:hover {
    background: linear-gradient(135deg, #dc2626 0%, var(--modern-danger) 100%);
    box-shadow: 0 8px 15px rgba(239, 68, 68, 0.4);
}

.modern-calc-btn-equals {
    background: linear-gradient(135deg, var(--modern-success) 0%, #059669 100%);
    color: var(--modern-white);
    box-shadow: 0 4px 6px rgba(16, 185, 129, 0.3);
}

.modern-calc-btn-equals:hover {
    background: linear-gradient(135deg, #059669 0%, var(--modern-success) 100%);
    box-shadow: 0 8px 15px rgba(16, 185, 129, 0.4);
}

.modern-calc-btn-wide {
    grid-column: span 2;
}

.modern-calc-btn-tall {
    grid-row: span 2;
    height: 7.75rem;
}

/* ===== MODERN RESULT CARDS ===== */
.modern-result-card {
    background: var(--modern-white);
    border-radius: var(--modern-radius-xl);
    border: 1px solid var(--modern-gray-200);
    overflow: hidden;
    box-shadow: var(--modern-shadow-lg);
    margin-top: var(--modern-space-lg);
}

.modern-result-header {
    padding: var(--modern-space-lg) var(--modern-space-xl);
    font-weight: 600;
    font-size: var(--modern-font-size-lg);
    display: flex;
    align-items: center;
    gap: var(--modern-space-sm);
    border-bottom: 1px solid var(--modern-gray-200);
}

.modern-result-body {
    padding: var(--modern-space-xl);
}

.modern-result-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--modern-space-lg);
    margin-bottom: var(--modern-space-lg);
}

.modern-result-item {
    text-align: center;
    padding: var(--modern-space-lg);
    background: var(--modern-gray-50);
    border-radius: var(--modern-radius-lg);
    border: 1px solid var(--modern-gray-200);
}

.modern-result-label {
    font-size: var(--modern-font-size-sm);
    color: var(--modern-gray-600);
    margin-bottom: var(--modern-space-sm);
    font-weight: 500;
}

.modern-result-value {
    font-size: var(--modern-font-size-xl);
    font-weight: 700;
    color: var(--modern-gray-900);
    font-family: 'JetBrains Mono', monospace;
}

.modern-result-highlight {
    grid-column: span 2;
    background: var(--modern-primary);
    color: var(--modern-white);
    border-color: var(--modern-primary);
}

.modern-result-highlight .modern-result-label {
    color: rgba(255, 255, 255, 0.8);
}

.modern-result-highlight .modern-result-value {
    color: var(--modern-white);
}

.modern-result-success .modern-result-header {
    background: var(--modern-success);
    color: var(--modern-white);
}

.modern-result-info .modern-result-header {
    background: var(--modern-info);
    color: var(--modern-white);
}

.modern-result-warning .modern-result-header {
    background: var(--modern-warning);
    color: var(--modern-white);
}

/* ===== MODERN TARGET CARDS ===== */
.modern-target-card {
    background: var(--modern-white);
    border-radius: var(--modern-radius-xl);
    padding: var(--modern-space-xl);
    border: 1px solid var(--modern-gray-200);
    box-shadow: var(--modern-shadow-lg);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.modern-target-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--modern-primary), var(--modern-success));
}

.modern-target-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--modern-shadow-xl);
}

.modern-target-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--modern-space-lg);
}

.modern-target-title {
    font-size: var(--modern-font-size-lg);
    font-weight: 700;
    color: var(--modern-gray-900);
    margin: 0;
    line-height: 1.3;
}

.modern-target-progress {
    margin-bottom: var(--modern-space-lg);
}

.modern-target-amounts {
    display: flex;
    justify-content: space-between;
    margin-bottom: var(--modern-space-md);
}

.modern-target-amount {
    display: flex;
    flex-direction: column;
    gap: var(--modern-space-xs);
}

.modern-target-label {
    font-size: var(--modern-font-size-sm);
    color: var(--modern-gray-600);
    font-weight: 500;
}

.modern-target-value {
    font-size: var(--modern-font-size-lg);
    font-weight: 700;
    color: var(--modern-gray-900);
    font-family: 'JetBrains Mono', monospace;
}

.modern-progress-container {
    display: flex;
    align-items: center;
    gap: var(--modern-space-md);
}

.modern-progress-bar {
    flex: 1;
    height: 12px;
    background: var(--modern-gray-200);
    border-radius: 6px;
    overflow: hidden;
    position: relative;
}

.modern-progress-fill {
    height: 100%;
    border-radius: 6px;
    transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.modern-progress-active {
    background: linear-gradient(90deg, var(--modern-primary), var(--modern-info));
}

.modern-progress-complete {
    background: linear-gradient(90deg, var(--modern-success), #059669);
}

.modern-progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: progress-shine 2s infinite;
}

@keyframes progress-shine {
    0% { left: -100%; }
    100% { left: 100%; }
}

.modern-progress-text {
    font-size: var(--modern-font-size-sm);
    font-weight: 600;
    color: var(--modern-gray-700);
    min-width: 45px;
    text-align: right;
}

.modern-target-period {
    margin-bottom: var(--modern-space-lg);
    padding: var(--modern-space-md);
    background: var(--modern-gray-50);
    border-radius: var(--modern-radius-lg);
    border: 1px solid var(--modern-gray-200);
}

.modern-target-period-header {
    display: flex;
    align-items: center;
    gap: var(--modern-space-sm);
    margin-bottom: var(--modern-space-xs);
    font-size: var(--modern-font-size-sm);
    font-weight: 600;
    color: var(--modern-gray-700);
}

.modern-target-period-dates {
    font-size: var(--modern-font-size-sm);
    color: var(--modern-gray-600);
    font-family: 'JetBrains Mono', monospace;
}

.modern-target-actions {
    display: flex;
    gap: var(--modern-space-sm);
    margin-top: auto;
}

.modern-flex-1 {
    flex: 1;
}

/* ===== MODERN CURRENCY CONVERTER ===== */
.modern-converter-card {
    background: var(--modern-white);
    border-radius: var(--modern-radius-xl);
    padding: var(--modern-space-xl);
    border: 1px solid var(--modern-gray-200);
    box-shadow: var(--modern-shadow-lg);
    position: relative;
    overflow: hidden;
}

.modern-converter-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--modern-primary), var(--modern-success));
}

.modern-converter-header {
    margin-bottom: var(--modern-space-xl);
}

.modern-converter-title {
    font-size: var(--modern-font-size-xl);
    font-weight: 700;
    color: var(--modern-gray-900);
    margin: 0;
}

.modern-converter-form {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: var(--modern-space-lg);
    align-items: center;
    margin-bottom: var(--modern-space-xl);
}

.modern-converter-section {
    display: flex;
    flex-direction: column;
    gap: var(--modern-space-md);
}

.modern-converter-swap {
    display: flex;
    justify-content: center;
    align-items: center;
}

.modern-swap-btn {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--modern-primary), var(--modern-info));
    border: none;
    color: var(--modern-white);
    font-size: var(--modern-font-size-lg);
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: var(--modern-shadow-md);
    position: relative;
    overflow: hidden;
}

.modern-swap-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s;
}

.modern-swap-btn:hover {
    transform: rotate(180deg) scale(1.1);
    box-shadow: var(--modern-shadow-xl);
}

.modern-swap-btn:hover::before {
    left: 100%;
}

.modern-result-input {
    background: linear-gradient(135deg, #f8fffe, #e6fffa);
    border: 2px solid var(--modern-success);
    font-weight: 700;
    font-size: var(--modern-font-size-lg);
    color: var(--modern-success);
}

.modern-rate-info {
    background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
    border: 1px solid var(--modern-info);
    border-radius: var(--modern-radius-lg);
    padding: var(--modern-space-lg);
}

.modern-rate-display {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modern-rate-text strong {
    font-size: var(--modern-font-size-lg);
    color: var(--modern-gray-900);
    display: block;
    margin-bottom: var(--modern-space-xs);
}

.modern-rate-subtitle {
    color: var(--modern-gray-600);
    font-size: var(--modern-font-size-sm);
}

/* ===== MODERN CURRENCY LIST ===== */
.modern-currency-list {
    background: var(--modern-white);
    border-radius: var(--modern-radius-xl);
    border: 1px solid var(--modern-gray-200);
    box-shadow: var(--modern-shadow-lg);
    overflow: hidden;
}

.modern-currency-header {
    background: linear-gradient(135deg, var(--modern-info), #0ea5e9);
    color: var(--modern-white);
    padding: var(--modern-space-lg);
}

.modern-currency-title {
    font-size: var(--modern-font-size-lg);
    font-weight: 700;
    margin: 0;
}

.modern-currency-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--modern-space-md) var(--modern-space-lg);
    border-bottom: 1px solid var(--modern-gray-100);
    transition: background-color 0.2s ease;
}

.modern-currency-item:hover {
    background: var(--modern-gray-50);
}

.modern-currency-item:last-child {
    border-bottom: none;
}

.modern-currency-info {
    display: flex;
    align-items: center;
    gap: var(--modern-space-md);
}

.modern-currency-flag {
    width: 40px;
    height: 30px;
    background: var(--modern-gray-100);
    border-radius: var(--modern-radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: var(--modern-font-size-sm);
    color: var(--modern-gray-700);
}

.modern-currency-details {
    display: flex;
    flex-direction: column;
}

.modern-currency-code {
    font-weight: 700;
    color: var(--modern-gray-900);
    font-size: var(--modern-font-size-md);
}

.modern-currency-name {
    font-size: var(--modern-font-size-sm);
    color: var(--modern-gray-600);
}

.modern-currency-rate {
    font-weight: 700;
    color: var(--modern-gray-900);
    font-family: 'JetBrains Mono', monospace;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .modern-converter-form {
        grid-template-columns: 1fr;
        gap: var(--modern-space-md);
    }

    .modern-converter-swap {
        order: 2;
        margin: var(--modern-space-md) 0;
    }

    .modern-swap-btn {
        width: 50px;
        height: 50px;
        font-size: var(--modern-font-size-md);
    }

    .modern-rate-display {
        flex-direction: column;
        gap: var(--modern-space-md);
        text-align: center;
    }
}

/* ===== UTILITY CLASSES ===== */
.modern-w-full { width: 100%; }
.modern-mt-lg { margin-top: var(--modern-space-lg); }
