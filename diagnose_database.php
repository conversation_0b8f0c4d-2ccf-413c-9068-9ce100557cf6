<?php
require_once 'includes/config/database.php';

$diagnostics = [];
$errors = [];

try {
    // 1. Test database connection
    $diagnostics[] = "✅ Database connection successful";
    
    // 2. Check if users table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
    if ($stmt->rowCount() > 0) {
        $diagnostics[] = "✅ Users table exists";
        
        // Check users table structure
        $stmt = $pdo->query("DESCRIBE users");
        $userColumns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        $diagnostics[] = "✅ Users table columns: " . implode(', ', $userColumns);
    } else {
        $errors[] = "❌ Users table does not exist";
    }
    
    // 3. Check if supplier table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'supplier'");
    if ($stmt->rowCount() > 0) {
        $diagnostics[] = "✅ Supplier table exists";
        
        // Check supplier table structure
        $stmt = $pdo->query("DESCRIBE supplier");
        $supplierColumns = $stmt->fetchAll();
        $diagnostics[] = "✅ Supplier table structure:";
        foreach ($supplierColumns as $col) {
            $diagnostics[] = "   - {$col['Field']} ({$col['Type']}) {$col['Null']} {$col['Key']} {$col['Default']}";
        }
        
        // Check for foreign key constraints
        $stmt = $pdo->query("SELECT * FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE WHERE TABLE_NAME = 'supplier' AND CONSTRAINT_NAME != 'PRIMARY'");
        $constraints = $stmt->fetchAll();
        if (!empty($constraints)) {
            $diagnostics[] = "✅ Foreign key constraints found:";
            foreach ($constraints as $constraint) {
                $diagnostics[] = "   - {$constraint['COLUMN_NAME']} -> {$constraint['REFERENCED_TABLE_NAME']}.{$constraint['REFERENCED_COLUMN_NAME']}";
            }
        } else {
            $diagnostics[] = "⚠️ No foreign key constraints found";
        }
        
    } else {
        $errors[] = "❌ Supplier table does not exist";
    }
    
    // 4. Test basic operations
    if (empty($errors)) {
        // Test SELECT
        $stmt = $pdo->query("SELECT COUNT(*) FROM supplier");
        $count = $stmt->fetchColumn();
        $diagnostics[] = "✅ SELECT test successful - {$count} suppliers found";
        
        // Test INSERT (dry run)
        $stmt = $pdo->prepare("SELECT 1 FROM users LIMIT 1");
        $stmt->execute();
        $user = $stmt->fetch();
        if ($user) {
            $diagnostics[] = "✅ Users table has data for testing";
        } else {
            $errors[] = "❌ No users found for testing";
        }
    }
    
    // 5. Check MySQL version and settings
    $stmt = $pdo->query("SELECT VERSION() as version");
    $version = $stmt->fetch();
    $diagnostics[] = "✅ MySQL version: " . $version['version'];
    
    $stmt = $pdo->query("SELECT @@sql_mode as sql_mode");
    $sqlMode = $stmt->fetch();
    $diagnostics[] = "✅ SQL Mode: " . $sqlMode['sql_mode'];
    
    // 6. Check charset and collation
    $stmt = $pdo->query("SELECT DEFAULT_CHARACTER_SET_NAME, DEFAULT_COLLATION_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = DATABASE()");
    $charset = $stmt->fetch();
    $diagnostics[] = "✅ Database charset: " . $charset['DEFAULT_CHARACTER_SET_NAME'];
    $diagnostics[] = "✅ Database collation: " . $charset['DEFAULT_COLLATION_NAME'];
    
} catch (PDOException $e) {
    $errors[] = "❌ Database error: " . $e->getMessage();
    $errors[] = "❌ Error code: " . $e->getCode();
    $errors[] = "❌ SQL State: " . $e->errorInfo[0] ?? 'Unknown';
} catch (Exception $e) {
    $errors[] = "❌ General error: " . $e->getMessage();
}

?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Diagnostics - KeuanganKu</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="card shadow">
                    <div class="card-header bg-info text-white">
                        <h4 class="mb-0">
                            <i class="fas fa-stethoscope me-2"></i>Database Diagnostics
                        </h4>
                    </div>
                    <div class="card-body">
                        
                        <?php if (!empty($errors)): ?>
                        <div class="alert alert-danger">
                            <h6><i class="fas fa-exclamation-triangle me-2"></i>Errors Found:</h6>
                            <ul class="mb-0">
                                <?php foreach ($errors as $error): ?>
                                <li><?= htmlspecialchars($error) ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                        <?php endif; ?>

                        <?php if (!empty($diagnostics)): ?>
                        <div class="alert alert-success">
                            <h6><i class="fas fa-check-circle me-2"></i>Diagnostic Results:</h6>
                            <ul class="mb-0">
                                <?php foreach ($diagnostics as $diagnostic): ?>
                                <li><?= htmlspecialchars($diagnostic) ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                        <?php endif; ?>

                        <!-- Quick Fixes -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h6><i class="fas fa-wrench me-2"></i>Quick Fixes</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="d-grid gap-2">
                                            <a href="/keuangan/fix_supplier_table.php" class="btn btn-warning">
                                                <i class="fas fa-table me-2"></i>Fix Supplier Table
                                            </a>
                                            <a href="/keuangan/setup_database.php" class="btn btn-primary">
                                                <i class="fas fa-database me-2"></i>Setup All Tables
                                            </a>
                                            <button class="btn btn-danger" onclick="recreateSupplierTable()">
                                                <i class="fas fa-redo me-2"></i>Recreate Supplier Table
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h6><i class="fas fa-test-tube me-2"></i>Tests</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="d-grid gap-2">
                                            <a href="/keuangan/test_supplier_insert.php" class="btn btn-info">
                                                <i class="fas fa-flask me-2"></i>Test Insert
                                            </a>
                                            <a href="/keuangan/debug_supplier.php" class="btn btn-secondary">
                                                <i class="fas fa-bug me-2"></i>Debug Supplier
                                            </a>
                                            <a href="/keuangan/supplier.php" class="btn btn-success">
                                                <i class="fas fa-truck me-2"></i>Go to Supplier
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Manual SQL Execution -->
                        <div class="card mt-4">
                            <div class="card-header">
                                <h6><i class="fas fa-terminal me-2"></i>Manual SQL Execution</h6>
                            </div>
                            <div class="card-body">
                                <form method="POST">
                                    <div class="mb-3">
                                        <label class="form-label">SQL Query:</label>
                                        <textarea name="sql_query" class="form-control" rows="4" placeholder="Enter SQL query here..."><?= htmlspecialchars($_POST['sql_query'] ?? '') ?></textarea>
                                    </div>
                                    <button type="submit" name="execute_sql" class="btn btn-warning">
                                        <i class="fas fa-play me-2"></i>Execute Query
                                    </button>
                                </form>

                                <?php
                                if (isset($_POST['execute_sql']) && !empty($_POST['sql_query'])) {
                                    echo "<hr>";
                                    try {
                                        $query = trim($_POST['sql_query']);
                                        if (stripos($query, 'SELECT') === 0 || stripos($query, 'SHOW') === 0 || stripos($query, 'DESCRIBE') === 0) {
                                            $stmt = $pdo->query($query);
                                            $results = $stmt->fetchAll();
                                            echo "<div class='alert alert-success'>";
                                            echo "<h6>Query Results:</h6>";
                                            if (empty($results)) {
                                                echo "<p>No results returned.</p>";
                                            } else {
                                                echo "<div class='table-responsive'>";
                                                echo "<table class='table table-sm table-bordered'>";
                                                echo "<thead><tr>";
                                                foreach (array_keys($results[0]) as $column) {
                                                    if (!is_numeric($column)) {
                                                        echo "<th>" . htmlspecialchars($column) . "</th>";
                                                    }
                                                }
                                                echo "</tr></thead><tbody>";
                                                foreach ($results as $row) {
                                                    echo "<tr>";
                                                    foreach ($row as $key => $value) {
                                                        if (!is_numeric($key)) {
                                                            echo "<td>" . htmlspecialchars($value) . "</td>";
                                                        }
                                                    }
                                                    echo "</tr>";
                                                }
                                                echo "</tbody></table></div>";
                                            }
                                            echo "</div>";
                                        } else {
                                            echo "<div class='alert alert-warning'>Only SELECT, SHOW, and DESCRIBE queries are allowed for safety.</div>";
                                        }
                                    } catch (PDOException $e) {
                                        echo "<div class='alert alert-danger'>";
                                        echo "<h6>SQL Error:</h6>";
                                        echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
                                        echo "</div>";
                                    }
                                }
                                ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
    function recreateSupplierTable() {
        if (confirm('⚠️ This will DELETE all supplier data and recreate the table. Are you sure?')) {
            window.location.href = '/keuangan/recreate_supplier_table.php';
        }
    }
    </script>
</body>
</html>
