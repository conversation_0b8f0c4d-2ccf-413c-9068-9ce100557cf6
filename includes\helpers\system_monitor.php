<?php
/**
 * System Monitor Helper Functions
 * 
 * This file contains functions for monitoring system health and performance.
 */

/**
 * Get system information
 * @return array
 */
function getSystemInfo() {
    $info = [];
    
    // Basic system info
    $info['os'] = PHP_OS;
    $info['php_version'] = PHP_VERSION;
    $info['server_software'] = $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown';
    $info['server_name'] = $_SERVER['SERVER_NAME'] ?? 'localhost';
    $info['server_port'] = $_SERVER['SERVER_PORT'] ?? '80';
    $info['document_root'] = $_SERVER['DOCUMENT_ROOT'] ?? '';
    
    // Memory info
    $info['memory_limit'] = ini_get('memory_limit');
    $info['memory_usage'] = memory_get_usage(true);
    $info['memory_peak'] = memory_get_peak_usage(true);
    
    // Disk info
    $info['disk_total'] = disk_total_space('.');
    $info['disk_free'] = disk_free_space('.');
    $info['disk_used'] = $info['disk_total'] - $info['disk_free'];
    
    // Load average (Unix only)
    if (function_exists('sys_getloadavg')) {
        $info['load'] = sys_getloadavg();
    } else {
        $info['load'] = 'N/A (Windows)';
    }
    
    // PHP extensions
    $requiredExtensions = ['pdo', 'pdo_mysql', 'json', 'session', 'mbstring', 'openssl'];
    $info['extensions'] = [];
    foreach ($requiredExtensions as $ext) {
        $info['extensions'][$ext] = extension_loaded($ext);
    }
    
    // Database info
    try {
        global $pdo;
        $stmt = $pdo->query("SELECT VERSION() as version");
        $result = $stmt->fetch();
        $info['mysql_version'] = $result['version'] ?? 'Unknown';
    } catch (Exception $e) {
        $info['mysql_version'] = 'Connection Error';
    }
    
    return $info;
}

/**
 * Get database statistics
 * @return array
 */
function getDatabaseStats() {
    global $pdo;
    $stats = [];
    
    try {
        // Database size
        $stmt = $pdo->query("
            SELECT 
                ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS size_mb,
                SUM(table_rows) AS total_rows
            FROM information_schema.tables 
            WHERE table_schema = DATABASE()
        ");
        $result = $stmt->fetch();
        $stats['total_size_mb'] = $result['size_mb'] ?? 0;
        $stats['total_rows'] = $result['total_rows'] ?? 0;
        
        // Table information
        $stmt = $pdo->query("
            SELECT 
                table_name,
                table_rows,
                ROUND((data_length + index_length) / 1024 / 1024, 2) AS size_mb
            FROM information_schema.tables 
            WHERE table_schema = DATABASE()
            ORDER BY (data_length + index_length) DESC
        ");
        $stats['tables'] = $stmt->fetchAll();
        
        // Connection count
        $stmt = $pdo->query("SHOW STATUS LIKE 'Threads_connected'");
        $result = $stmt->fetch();
        $stats['connections'] = $result['Value'] ?? 0;
        
    } catch (PDOException $e) {
        error_log("Database stats error: " . $e->getMessage());
        $stats = [
            'total_size_mb' => 0,
            'total_rows' => 0,
            'tables' => [],
            'connections' => 0
        ];
    }
    
    return $stats;
}

/**
 * Check system health
 * @return array
 */
function checkSystemHealth() {
    $health = [];
    
    // Memory usage check
    $memoryLimit = convertToBytes(ini_get('memory_limit'));
    $memoryUsage = memory_get_usage(true);
    $memoryPercent = ($memoryUsage / $memoryLimit) * 100;
    
    $health['memory'] = [
        'status' => $memoryPercent < 80 ? 'good' : ($memoryPercent < 90 ? 'warning' : 'critical'),
        'usage_percent' => round($memoryPercent, 2),
        'usage_bytes' => $memoryUsage,
        'limit_bytes' => $memoryLimit
    ];
    
    // Disk usage check
    $diskTotal = disk_total_space('.');
    $diskFree = disk_free_space('.');
    $diskUsed = $diskTotal - $diskFree;
    $diskPercent = ($diskUsed / $diskTotal) * 100;
    
    $health['disk'] = [
        'status' => $diskPercent < 80 ? 'good' : ($diskPercent < 90 ? 'warning' : 'critical'),
        'usage_percent' => round($diskPercent, 2),
        'used_bytes' => $diskUsed,
        'total_bytes' => $diskTotal,
        'free_bytes' => $diskFree
    ];
    
    // Database connection check
    try {
        global $pdo;
        $pdo->query("SELECT 1");
        $health['database'] = ['status' => 'good', 'message' => 'Connected'];
    } catch (Exception $e) {
        $health['database'] = ['status' => 'critical', 'message' => 'Connection failed'];
    }
    
    // File permissions check
    $criticalFiles = [
        'includes/config/database.php',
        'includes/helpers/functions.php',
        'assets/css/modern-ui.css'
    ];
    
    $permissionIssues = [];
    foreach ($criticalFiles as $file) {
        if (!file_exists($file)) {
            $permissionIssues[] = "Missing: $file";
        } elseif (!is_readable($file)) {
            $permissionIssues[] = "Not readable: $file";
        }
    }
    
    $health['permissions'] = [
        'status' => empty($permissionIssues) ? 'good' : 'warning',
        'issues' => $permissionIssues
    ];
    
    return $health;
}

/**
 * Get security status
 * @return array
 */
function getSecurityStatus() {
    $security = [];
    
    // PHP security settings
    $security['display_errors'] = ini_get('display_errors') == '0';
    $security['expose_php'] = ini_get('expose_php') == '0';
    $security['allow_url_fopen'] = ini_get('allow_url_fopen') == '0';
    $security['allow_url_include'] = ini_get('allow_url_include') == '0';
    
    // Session security
    $security['session_cookie_httponly'] = ini_get('session.cookie_httponly') == '1';
    $security['session_cookie_secure'] = ini_get('session.cookie_secure') == '1';
    $security['session_use_strict_mode'] = ini_get('session.use_strict_mode') == '1';
    
    // HTTPS check
    $security['https_enabled'] = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on';
    
    return $security;
}

/**
 * Get performance metrics
 * @return array
 */
function getPerformanceMetrics() {
    $metrics = [];
    
    // Execution time
    $metrics['execution_time'] = microtime(true) - $_SERVER['REQUEST_TIME_FLOAT'];
    
    // Memory usage
    $metrics['memory_usage'] = memory_get_usage(true);
    $metrics['memory_peak'] = memory_get_peak_usage(true);
    
    // Database query count (if tracking is enabled)
    global $queryCount;
    $metrics['query_count'] = $queryCount ?? 0;
    
    // File operations (basic)
    $metrics['included_files'] = count(get_included_files());
    
    return $metrics;
}

/**
 * Log system event
 * @param string $event Event description
 * @param string $level Log level
 * @param array $context Additional context
 * @return bool
 */
function logSystemEvent($event, $level = 'info', $context = []) {
    global $pdo;
    
    try {
        // Create system_logs table if not exists
        $pdo->exec("CREATE TABLE IF NOT EXISTS system_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            event TEXT NOT NULL,
            level ENUM('debug', 'info', 'warning', 'error', 'critical') DEFAULT 'info',
            context JSON,
            ip_address VARCHAR(45),
            user_agent TEXT,
            user_id INT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
        )");
        
        $currentUser = getCurrentUser();
        $stmt = $pdo->prepare("
            INSERT INTO system_logs (event, level, context, ip_address, user_agent, user_id)
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        
        return $stmt->execute([
            $event,
            $level,
            json_encode($context),
            $_SERVER['REMOTE_ADDR'] ?? null,
            $_SERVER['HTTP_USER_AGENT'] ?? null,
            $currentUser['id'] ?? null
        ]);
        
    } catch (PDOException $e) {
        error_log("System log error: " . $e->getMessage());
        return false;
    }
}

/**
 * Get recent system logs
 * @param int $limit Number of logs to retrieve
 * @param string $level Filter by log level
 * @return array
 */
function getRecentSystemLogs($limit = 50, $level = null) {
    global $pdo;
    
    try {
        $sql = "SELECT * FROM system_logs";
        $params = [];
        
        if ($level) {
            $sql .= " WHERE level = ?";
            $params[] = $level;
        }
        
        $sql .= " ORDER BY created_at DESC LIMIT ?";
        $params[] = $limit;
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll();
        
    } catch (PDOException $e) {
        error_log("Get system logs error: " . $e->getMessage());
        return [];
    }
}

/**
 * Clean old system logs
 * @param int $days Keep logs newer than X days
 * @return bool
 */
function cleanOldSystemLogs($days = 30) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("
            DELETE FROM system_logs 
            WHERE created_at < DATE_SUB(NOW(), INTERVAL ? DAY)
        ");
        
        return $stmt->execute([$days]);
        
    } catch (PDOException $e) {
        error_log("Clean system logs error: " . $e->getMessage());
        return false;
    }
}

/**
 * Get system uptime (approximation based on session)
 * @return string
 */
function getSystemUptime() {
    $sessionFile = session_save_path() . '/sess_' . session_id();
    if (file_exists($sessionFile)) {
        $uptime = time() - filemtime($sessionFile);
        return formatDuration($uptime);
    }
    return 'Unknown';
}

/**
 * Format duration in human readable format
 * @param int $seconds Duration in seconds
 * @return string
 */
function formatDuration($seconds) {
    $units = [
        'day' => 86400,
        'hour' => 3600,
        'minute' => 60,
        'second' => 1
    ];
    
    $result = [];
    foreach ($units as $unit => $value) {
        if ($seconds >= $value) {
            $count = floor($seconds / $value);
            $result[] = $count . ' ' . $unit . ($count > 1 ? 's' : '');
            $seconds %= $value;
        }
    }
    
    return empty($result) ? '0 seconds' : implode(', ', array_slice($result, 0, 2));
}
