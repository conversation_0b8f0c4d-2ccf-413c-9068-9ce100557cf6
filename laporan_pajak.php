<?php
require_once 'includes/config/database.php';
require_once 'includes/helpers/functions.php';
require_once 'includes/helpers/notifications.php';

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isLoggedIn()) {
    setFlashMessage('danger', 'Silakan login terlebih dahulu');
    redirect('/keuangan/login.php');
}

$currentUser = getCurrentUser();
$currentPage = 'laporan_pajak';

// Get tax year from request
$taxYear = isset($_GET['tax_year']) ? (int)$_GET['tax_year'] : date('Y');

// Validate tax year
if ($taxYear < 2020 || $taxYear > date('Y')) {
    $taxYear = date('Y');
}

$startDate = $taxYear . '-01-01';
$endDate = $taxYear . '-12-31';

try {
    // Get income summary (taxable income)
    $stmt = $pdo->prepare("
        SELECT 
            k.nama_kategori,
            SUM(t.jumlah) as total_amount
        FROM transaksi t
        JOIN kategori k ON t.kategori_id = k.id
        WHERE t.user_id = ? AND k.tipe = 'pemasukan' 
        AND t.tanggal_transaksi BETWEEN ? AND ?
        GROUP BY k.id, k.nama_kategori
        ORDER BY total_amount DESC
    ");
    $stmt->execute([$currentUser['id'], $startDate, $endDate]);
    $incomeByCategory = $stmt->fetchAll();

    // Get deductible expenses
    $stmt = $pdo->prepare("
        SELECT 
            k.nama_kategori,
            SUM(t.jumlah) as total_amount
        FROM transaksi t
        JOIN kategori k ON t.kategori_id = k.id
        WHERE t.user_id = ? AND k.tipe = 'pengeluaran' 
        AND t.tanggal_transaksi BETWEEN ? AND ?
        AND k.nama_kategori IN ('Biaya Operasional', 'Biaya Kantor', 'Transportasi Bisnis', 'Pelatihan', 'Asuransi')
        GROUP BY k.id, k.nama_kategori
        ORDER BY total_amount DESC
    ");
    $stmt->execute([$currentUser['id'], $startDate, $endDate]);
    $deductibleExpenses = $stmt->fetchAll();

    // Get business income from sales
    $stmt = $pdo->prepare("
        SELECT 
            SUM(total_penjualan) as total_business_income
        FROM penjualan 
        WHERE user_id = ? AND tanggal_penjualan BETWEEN ? AND ?
    ");
    $stmt->execute([$currentUser['id'], $startDate, $endDate]);
    $businessIncome = $stmt->fetch()['total_business_income'] ?? 0;

    // Get business expenses from purchases
    $stmt = $pdo->prepare("
        SELECT 
            SUM(total_pembelian) as total_business_expenses
        FROM pembelian 
        WHERE user_id = ? AND tanggal_pembelian BETWEEN ? AND ?
    ");
    $stmt->execute([$currentUser['id'], $startDate, $endDate]);
    $businessExpenses = $stmt->fetch()['total_business_expenses'] ?? 0;

    // Get monthly income breakdown
    $stmt = $pdo->prepare("
        SELECT 
            MONTH(t.tanggal_transaksi) as bulan,
            SUM(CASE WHEN k.tipe = 'pemasukan' THEN t.jumlah ELSE 0 END) as pemasukan,
            SUM(CASE WHEN k.tipe = 'pengeluaran' THEN t.jumlah ELSE 0 END) as pengeluaran
        FROM transaksi t
        JOIN kategori k ON t.kategori_id = k.id
        WHERE t.user_id = ? AND t.tanggal_transaksi BETWEEN ? AND ?
        GROUP BY MONTH(t.tanggal_transaksi)
        ORDER BY bulan ASC
    ");
    $stmt->execute([$currentUser['id'], $startDate, $endDate]);
    $monthlyBreakdown = $stmt->fetchAll();

} catch (PDOException $e) {
    $incomeByCategory = [];
    $deductibleExpenses = [];
    $businessIncome = 0;
    $businessExpenses = 0;
    $monthlyBreakdown = [];
}

// Calculate totals
$totalIncome = array_sum(array_column($incomeByCategory, 'total_amount')) + $businessIncome;
$totalDeductibleExpenses = array_sum(array_column($deductibleExpenses, 'total_amount')) + $businessExpenses;
$netIncome = $totalIncome - $totalDeductibleExpenses;

// Tax calculation (simplified Indonesian tax brackets for individuals)
$taxBrackets = [
    ['min' => 0, 'max' => 60000000, 'rate' => 0.05],
    ['min' => 60000000, 'max' => *********, 'rate' => 0.15],
    ['min' => *********, 'max' => *********, 'rate' => 0.25],
    ['min' => *********, 'max' => *********0, 'rate' => 0.30],
    ['min' => *********0, 'max' => PHP_INT_MAX, 'rate' => 0.35]
];

$ptkp = 54000000; // PTKP for single person (2023)
$taxableIncome = max(0, $netIncome - $ptkp);
$estimatedTax = 0;

foreach ($taxBrackets as $bracket) {
    if ($taxableIncome > $bracket['min']) {
        $taxableAmount = min($taxableIncome, $bracket['max']) - $bracket['min'];
        $estimatedTax += $taxableAmount * $bracket['rate'];
    }
}

// Include header
include 'includes/views/layouts/header.php';
?>

<div class="container-fluid py-4">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-1">Laporan Pajak</h1>
            <p class="text-muted mb-0">Estimasi perhitungan pajak penghasilan tahunan</p>
        </div>
        <div class="d-flex gap-2">
            <button type="button" class="btn btn-success" onclick="exportTaxReport('excel')">
                <i class="fas fa-file-excel me-2"></i>Export Excel
            </button>
            <button type="button" class="btn btn-danger" onclick="exportTaxReport('pdf')">
                <i class="fas fa-file-pdf me-2"></i>Export PDF
            </button>
        </div>
    </div>

    <!-- Year Filter -->
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-body">
            <form action="" method="GET" class="row g-3 align-items-end">
                <div class="col-md-4">
                    <label class="form-label">Tahun Pajak</label>
                    <select name="tax_year" class="form-select">
                        <?php for ($year = date('Y'); $year >= 2020; $year--): ?>
                        <option value="<?= $year ?>" <?= $year == $taxYear ? 'selected' : '' ?>><?= $year ?></option>
                        <?php endfor; ?>
                    </select>
                </div>
                <div class="col-md-4">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-filter me-2"></i>Filter Tahun
                    </button>
                </div>
                <div class="col-md-4">
                    <div class="alert alert-warning mb-0">
                        <small><i class="fas fa-exclamation-triangle me-1"></i>Ini adalah estimasi. Konsultasikan dengan konsultan pajak untuk perhitungan yang akurat.</small>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Tax Summary Cards -->
    <div class="row g-3 mb-4">
        <div class="col-lg-3 col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted mb-1">Total Penghasilan</h6>
                            <h3 class="mb-0 text-success"><?= formatRupiah($totalIncome) ?></h3>
                        </div>
                        <div class="bg-success bg-opacity-10 p-3 rounded">
                            <i class="fas fa-money-bill-wave text-success"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted mb-1">Biaya yang Dapat Dikurangkan</h6>
                            <h3 class="mb-0 text-warning"><?= formatRupiah($totalDeductibleExpenses) ?></h3>
                        </div>
                        <div class="bg-warning bg-opacity-10 p-3 rounded">
                            <i class="fas fa-receipt text-warning"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted mb-1">Penghasilan Kena Pajak</h6>
                            <h3 class="mb-0 text-primary"><?= formatRupiah($taxableIncome) ?></h3>
                            <small class="text-muted">Setelah PTKP</small>
                        </div>
                        <div class="bg-primary bg-opacity-10 p-3 rounded">
                            <i class="fas fa-calculator text-primary"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted mb-1">Estimasi Pajak</h6>
                            <h3 class="mb-0 text-danger"><?= formatRupiah($estimatedTax) ?></h3>
                            <small class="text-muted">PPh Terutang</small>
                        </div>
                        <div class="bg-danger bg-opacity-10 p-3 rounded">
                            <i class="fas fa-file-invoice-dollar text-danger"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Income Breakdown -->
        <div class="col-lg-6">
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0"><i class="fas fa-arrow-up me-2"></i>Rincian Penghasilan</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Kategori</th>
                                    <th class="text-end">Jumlah</th>
                                    <th class="text-end">%</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if ($businessIncome > 0): ?>
                                <tr>
                                    <td><strong>Penghasilan Bisnis</strong></td>
                                    <td class="text-end fw-bold"><?= formatRupiah($businessIncome) ?></td>
                                    <td class="text-end"><?= $totalIncome > 0 ? number_format(($businessIncome / $totalIncome) * 100, 1) : 0 ?>%</td>
                                </tr>
                                <?php endif; ?>
                                <?php foreach ($incomeByCategory as $income): ?>
                                <tr>
                                    <td><?= htmlspecialchars($income['nama_kategori']) ?></td>
                                    <td class="text-end"><?= formatRupiah($income['total_amount']) ?></td>
                                    <td class="text-end"><?= $totalIncome > 0 ? number_format(($income['total_amount'] / $totalIncome) * 100, 1) : 0 ?>%</td>
                                </tr>
                                <?php endforeach; ?>
                                <tr class="table-success">
                                    <td><strong>Total Penghasilan</strong></td>
                                    <td class="text-end fw-bold"><?= formatRupiah($totalIncome) ?></td>
                                    <td class="text-end fw-bold">100%</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Deductible Expenses -->
        <div class="col-lg-6">
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0"><i class="fas fa-arrow-down me-2"></i>Biaya yang Dapat Dikurangkan</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Kategori</th>
                                    <th class="text-end">Jumlah</th>
                                    <th class="text-end">%</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if ($businessExpenses > 0): ?>
                                <tr>
                                    <td><strong>Biaya Bisnis</strong></td>
                                    <td class="text-end fw-bold"><?= formatRupiah($businessExpenses) ?></td>
                                    <td class="text-end"><?= $totalDeductibleExpenses > 0 ? number_format(($businessExpenses / $totalDeductibleExpenses) * 100, 1) : 0 ?>%</td>
                                </tr>
                                <?php endif; ?>
                                <?php foreach ($deductibleExpenses as $expense): ?>
                                <tr>
                                    <td><?= htmlspecialchars($expense['nama_kategori']) ?></td>
                                    <td class="text-end"><?= formatRupiah($expense['total_amount']) ?></td>
                                    <td class="text-end"><?= $totalDeductibleExpenses > 0 ? number_format(($expense['total_amount'] / $totalDeductibleExpenses) * 100, 1) : 0 ?>%</td>
                                </tr>
                                <?php endforeach; ?>
                                <tr class="table-warning">
                                    <td><strong>Total Biaya</strong></td>
                                    <td class="text-end fw-bold"><?= formatRupiah($totalDeductibleExpenses) ?></td>
                                    <td class="text-end fw-bold">100%</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Tax Calculation -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-calculator me-2"></i>Perhitungan Pajak</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table">
                            <tbody>
                                <tr>
                                    <td>Total Penghasilan</td>
                                    <td class="text-end"><?= formatRupiah($totalIncome) ?></td>
                                </tr>
                                <tr>
                                    <td>Dikurangi: Biaya yang Dapat Dikurangkan</td>
                                    <td class="text-end text-danger">(<?= formatRupiah($totalDeductibleExpenses) ?>)</td>
                                </tr>
                                <tr class="table-light">
                                    <td><strong>Penghasilan Neto</strong></td>
                                    <td class="text-end fw-bold"><?= formatRupiah($netIncome) ?></td>
                                </tr>
                                <tr>
                                    <td>Dikurangi: PTKP (Penghasilan Tidak Kena Pajak)</td>
                                    <td class="text-end text-danger">(<?= formatRupiah($ptkp) ?>)</td>
                                </tr>
                                <tr class="table-primary">
                                    <td><strong>Penghasilan Kena Pajak</strong></td>
                                    <td class="text-end fw-bold"><?= formatRupiah($taxableIncome) ?></td>
                                </tr>
                                <tr class="table-danger">
                                    <td><strong>PPh Terutang (Estimasi)</strong></td>
                                    <td class="text-end fw-bold text-danger"><?= formatRupiah($estimatedTax) ?></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tax Brackets -->
        <div class="col-lg-4">
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0"><i class="fas fa-layer-group me-2"></i>Tarif Pajak</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Lapisan</th>
                                    <th class="text-center">Tarif</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>s.d. 60 juta</td>
                                    <td class="text-center">5%</td>
                                </tr>
                                <tr>
                                    <td>60 juta - 250 juta</td>
                                    <td class="text-center">15%</td>
                                </tr>
                                <tr>
                                    <td>250 juta - 500 juta</td>
                                    <td class="text-center">25%</td>
                                </tr>
                                <tr>
                                    <td>500 juta - 5 miliar</td>
                                    <td class="text-center">30%</td>
                                </tr>
                                <tr>
                                    <td>di atas 5 miliar</td>
                                    <td class="text-center">35%</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="alert alert-info mt-3">
                        <small><i class="fas fa-info-circle me-1"></i>PTKP untuk WP tidak kawin: Rp 54.000.000</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Monthly Breakdown Chart -->
    <?php if (!empty($monthlyBreakdown)): ?>
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-secondary text-white">
                    <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>Breakdown Bulanan</h5>
                </div>
                <div class="card-body">
                    <canvas id="monthlyChart" height="100"></canvas>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Monthly breakdown chart
<?php if (!empty($monthlyBreakdown)): ?>
const monthlyData = <?= json_encode($monthlyBreakdown) ?>;
const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'Mei', 'Jun', 'Jul', 'Ags', 'Sep', 'Okt', 'Nov', 'Des'];

// Create arrays for all 12 months
const labels = monthNames;
const incomeData = new Array(12).fill(0);
const expenseData = new Array(12).fill(0);

monthlyData.forEach(item => {
    incomeData[item.bulan - 1] = item.pemasukan;
    expenseData[item.bulan - 1] = item.pengeluaran;
});

const ctx = document.getElementById('monthlyChart').getContext('2d');
new Chart(ctx, {
    type: 'bar',
    data: {
        labels: labels,
        datasets: [{
            label: 'Pemasukan',
            data: incomeData,
            backgroundColor: 'rgba(75, 192, 192, 0.8)',
            borderColor: 'rgba(75, 192, 192, 1)',
            borderWidth: 1
        }, {
            label: 'Pengeluaran',
            data: expenseData,
            backgroundColor: 'rgba(255, 99, 132, 0.8)',
            borderColor: 'rgba(255, 99, 132, 1)',
            borderWidth: 1
        }]
    },
    options: {
        responsive: true,
        scales: {
            y: {
                beginAtZero: true,
                title: {
                    display: true,
                    text: 'Jumlah (Rp)'
                }
            }
        },
        plugins: {
            tooltip: {
                callbacks: {
                    label: function(context) {
                        return context.dataset.label + ': Rp ' + new Intl.NumberFormat('id-ID').format(context.parsed.y);
                    }
                }
            }
        }
    }
});
<?php endif; ?>

function exportTaxReport(format) {
    const taxYear = '<?= $taxYear ?>';
    
    if (format === 'excel') {
        alert('Fitur export Excel akan segera tersedia');
    } else if (format === 'pdf') {
        alert('Fitur export PDF akan segera tersedia');
    }
}
</script>

<?php include 'includes/views/layouts/footer.php'; ?>
