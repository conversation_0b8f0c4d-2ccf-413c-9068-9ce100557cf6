<?php
require_once 'includes/config/database.php';
require_once 'includes/helpers/functions.php';
require_once 'includes/helpers/layout_helper.php';

// Check if user is logged in
$currentUser = getCurrentUser();
if (!$currentUser) {
    redirect('login.php');
}

$pageTitle = 'Clean Layout Manager';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['save_layout'])) {
    try {
        // Check if table exists, create if not
        $stmt = $pdo->query("SHOW TABLES LIKE 'layout_preferences'");
        if ($stmt->rowCount() === 0) {
            setFlashMessage('warning', 'Layout preferences table not found. Please create it first.');
            redirect('create_layout_table.php');
        }

        $stmt = $pdo->prepare("
            INSERT INTO layout_preferences (user_id, layout_type, color_scheme, border_radius, shadow_style, animation_style) 
            VALUES (?, ?, ?, ?, ?, ?)
            ON DUPLICATE KEY UPDATE 
            layout_type = VALUES(layout_type),
            color_scheme = VALUES(color_scheme),
            border_radius = VALUES(border_radius),
            shadow_style = VALUES(shadow_style),
            animation_style = VALUES(animation_style),
            updated_at = CURRENT_TIMESTAMP
        ");
        
        $result = $stmt->execute([
            $currentUser['id'],
            $_POST['layout_type'],
            $_POST['color_scheme'],
            $_POST['border_radius'],
            $_POST['shadow_style'],
            $_POST['animation_style']
        ]);

        if ($result) {
            setFlashMessage('success', 'Layout preferences saved successfully!');
        } else {
            setFlashMessage('danger', 'Failed to save layout preferences');
        }
        redirect('clean_layout_manager.php');
    } catch (Exception $e) {
        setFlashMessage('danger', 'Error: ' . $e->getMessage());
    }
}

// Get current preferences
$currentPrefs = getUserLayoutPreferences($currentUser['id']);

include 'includes/views/layouts/header.php';
?>

<!-- External CSS -->
<link rel="stylesheet" href="assets/css/layout-manager.css?v=<?= time() ?>">

<div class="container-fluid px-4 py-3">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h3 class="mb-1">
                        <i class="fas fa-paint-brush me-2 text-primary"></i>
                        Clean Layout Manager
                    </h3>
                    <p class="text-muted mb-0">Easy layout customization with live preview - No conflicts</p>
                </div>
                <div class="btn-group">
                    <a href="force_layout_apply.php" class="btn btn-warning">
                        <i class="fas fa-bug me-1"></i>Debug
                    </a>
                    <a href="/keuangan/dashboard.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i>Dashboard
                    </a>
                </div>
            </div>

            <div class="row">
                <!-- Layout Configuration -->
                <div class="col-lg-8 mb-4">
                    <form method="POST" id="layoutForm">
                        <!-- Layout Types -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-th-large me-2"></i>
                                    Choose Layout Style
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                                    <div class="col-md-4">
                                        <div class="layout-card">
                                            <input type="radio" id="layout_classic" name="layout_type" value="classic" <?= $currentPrefs['layout_type'] === 'classic' ? 'checked' : '' ?>>
                                            <label for="layout_classic" class="layout-label">
                                                <div class="layout-preview">
                                                    <div class="preview-sidebar classic-sidebar"></div>
                                                    <div class="preview-main">
                                                        <div class="preview-navbar classic-navbar"></div>
                                                        <div class="preview-content"></div>
                                                    </div>
                                                </div>
                                                <div class="layout-info">
                                                    <h6>Classic</h6>
                                                    <small>Professional & Traditional</small>
                                                </div>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="layout-card">
                                            <input type="radio" id="layout_modern" name="layout_type" value="modern" <?= $currentPrefs['layout_type'] === 'modern' ? 'checked' : '' ?>>
                                            <label for="layout_modern" class="layout-label">
                                                <div class="layout-preview">
                                                    <div class="preview-sidebar modern-sidebar"></div>
                                                    <div class="preview-main">
                                                        <div class="preview-navbar modern-navbar"></div>
                                                        <div class="preview-content"></div>
                                                    </div>
                                                </div>
                                                <div class="layout-info">
                                                    <h6>Modern</h6>
                                                    <small>Rounded & Contemporary</small>
                                                </div>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="layout-card">
                                            <input type="radio" id="layout_colorful" name="layout_type" value="colorful" <?= $currentPrefs['layout_type'] === 'colorful' ? 'checked' : '' ?>>
                                            <label for="layout_colorful" class="layout-label">
                                                <div class="layout-preview">
                                                    <div class="preview-sidebar colorful-sidebar"></div>
                                                    <div class="preview-main">
                                                        <div class="preview-navbar colorful-navbar"></div>
                                                        <div class="preview-content"></div>
                                                    </div>
                                                </div>
                                                <div class="layout-info">
                                                    <h6>Colorful</h6>
                                                    <small>Vibrant & Creative</small>
                                                </div>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="layout-card">
                                            <input type="radio" id="layout_minimal" name="layout_type" value="minimal" <?= $currentPrefs['layout_type'] === 'minimal' ? 'checked' : '' ?>>
                                            <label for="layout_minimal" class="layout-label">
                                                <div class="layout-preview">
                                                    <div class="preview-sidebar minimal-sidebar"></div>
                                                    <div class="preview-main">
                                                        <div class="preview-navbar minimal-navbar"></div>
                                                        <div class="preview-content"></div>
                                                    </div>
                                                </div>
                                                <div class="layout-info">
                                                    <h6>Minimal</h6>
                                                    <small>Clean & Simple</small>
                                                </div>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="layout-card">
                                            <input type="radio" id="layout_glassmorphism" name="layout_type" value="glassmorphism" <?= $currentPrefs['layout_type'] === 'glassmorphism' ? 'checked' : '' ?>>
                                            <label for="layout_glassmorphism" class="layout-label">
                                                <div class="layout-preview">
                                                    <div class="preview-sidebar glass-sidebar"></div>
                                                    <div class="preview-main">
                                                        <div class="preview-navbar glass-navbar"></div>
                                                        <div class="preview-content"></div>
                                                    </div>
                                                </div>
                                                <div class="layout-info">
                                                    <h6>Glassmorphism</h6>
                                                    <small>Glass Effect & Modern</small>
                                                </div>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="layout-card">
                                            <input type="radio" id="layout_neon" name="layout_type" value="neon" <?= $currentPrefs['layout_type'] === 'neon' ? 'checked' : '' ?>>
                                            <label for="layout_neon" class="layout-label">
                                                <div class="layout-preview">
                                                    <div class="preview-sidebar neon-sidebar"></div>
                                                    <div class="preview-main">
                                                        <div class="preview-navbar neon-navbar"></div>
                                                        <div class="preview-content"></div>
                                                    </div>
                                                </div>
                                                <div class="layout-info">
                                                    <h6>Neon</h6>
                                                    <small>Glowing & Futuristic</small>
                                                </div>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Color Schemes -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-palette me-2"></i>
                                    Choose Color Scheme
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                                    <div class="col-md-3">
                                        <div class="color-card">
                                            <input type="radio" id="color_default" name="color_scheme" value="default" <?= $currentPrefs['color_scheme'] === 'default' ? 'checked' : '' ?>>
                                            <label for="color_default" class="color-label">
                                                <div class="color-preview default-colors"></div>
                                                <span>Default</span>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="color-card">
                                            <input type="radio" id="color_vibrant" name="color_scheme" value="vibrant" <?= $currentPrefs['color_scheme'] === 'vibrant' ? 'checked' : '' ?>>
                                            <label for="color_vibrant" class="color-label">
                                                <div class="color-preview vibrant-colors"></div>
                                                <span>Vibrant</span>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="color-card">
                                            <input type="radio" id="color_pastel" name="color_scheme" value="pastel" <?= $currentPrefs['color_scheme'] === 'pastel' ? 'checked' : '' ?>>
                                            <label for="color_pastel" class="color-label">
                                                <div class="color-preview pastel-colors"></div>
                                                <span>Pastel</span>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="color-card">
                                            <input type="radio" id="color_neon" name="color_scheme" value="neon" <?= $currentPrefs['color_scheme'] === 'neon' ? 'checked' : '' ?>>
                                            <label for="color_neon" class="color-label">
                                                <div class="color-preview neon-colors"></div>
                                                <span>Neon</span>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="color-card">
                                            <input type="radio" id="color_ocean" name="color_scheme" value="ocean" <?= $currentPrefs['color_scheme'] === 'ocean' ? 'checked' : '' ?>>
                                            <label for="color_ocean" class="color-label">
                                                <div class="color-preview ocean-colors"></div>
                                                <span>Ocean</span>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="color-card">
                                            <input type="radio" id="color_sunset" name="color_scheme" value="sunset" <?= $currentPrefs['color_scheme'] === 'sunset' ? 'checked' : '' ?>>
                                            <label for="color_sunset" class="color-label">
                                                <div class="color-preview sunset-colors"></div>
                                                <span>Sunset</span>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="color-card">
                                            <input type="radio" id="color_midnight" name="color_scheme" value="midnight" <?= $currentPrefs['color_scheme'] === 'midnight' ? 'checked' : '' ?>>
                                            <label for="color_midnight" class="color-label">
                                                <div class="color-preview midnight-colors"></div>
                                                <span>Midnight</span>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="color-card">
                                            <input type="radio" id="color_royal" name="color_scheme" value="royal" <?= $currentPrefs['color_scheme'] === 'royal' ? 'checked' : '' ?>>
                                            <label for="color_royal" class="color-label">
                                                <div class="color-preview royal-colors"></div>
                                                <span>Royal</span>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Quick Options -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-sliders-h me-2"></i>
                                    Quick Options
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                                    <div class="col-md-4">
                                        <label class="form-label">Border Radius</label>
                                        <select name="border_radius" class="form-select">
                                            <option value="none" <?= $currentPrefs['border_radius'] === 'none' ? 'selected' : '' ?>>None</option>
                                            <option value="small" <?= $currentPrefs['border_radius'] === 'small' ? 'selected' : '' ?>>Small</option>
                                            <option value="medium" <?= $currentPrefs['border_radius'] === 'medium' ? 'selected' : '' ?>>Medium</option>
                                            <option value="large" <?= $currentPrefs['border_radius'] === 'large' ? 'selected' : '' ?>>Large</option>
                                            <option value="xl" <?= $currentPrefs['border_radius'] === 'xl' ? 'selected' : '' ?>>Extra Large</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">Shadow Style</label>
                                        <select name="shadow_style" class="form-select">
                                            <option value="none" <?= $currentPrefs['shadow_style'] === 'none' ? 'selected' : '' ?>>None</option>
                                            <option value="soft" <?= $currentPrefs['shadow_style'] === 'soft' ? 'selected' : '' ?>>Soft</option>
                                            <option value="medium" <?= $currentPrefs['shadow_style'] === 'medium' ? 'selected' : '' ?>>Medium</option>
                                            <option value="strong" <?= $currentPrefs['shadow_style'] === 'strong' ? 'selected' : '' ?>>Strong</option>
                                            <option value="colored" <?= $currentPrefs['shadow_style'] === 'colored' ? 'selected' : '' ?>>Colored</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">Animation</label>
                                        <select name="animation_style" class="form-select">
                                            <option value="none" <?= $currentPrefs['animation_style'] === 'none' ? 'selected' : '' ?>>None</option>
                                            <option value="subtle" <?= $currentPrefs['animation_style'] === 'subtle' ? 'selected' : '' ?>>Subtle</option>
                                            <option value="smooth" <?= $currentPrefs['animation_style'] === 'smooth' ? 'selected' : '' ?>>Smooth</option>
                                            <option value="bouncy" <?= $currentPrefs['animation_style'] === 'bouncy' ? 'selected' : '' ?>>Bouncy</option>
                                            <option value="elastic" <?= $currentPrefs['animation_style'] === 'elastic' ? 'selected' : '' ?>>Elastic</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="card">
                            <div class="card-body text-center">
                                <button type="submit" name="save_layout" class="btn btn-primary btn-lg me-3">
                                    <i class="fas fa-save me-2"></i>Save Layout
                                </button>
                                <button type="button" class="btn btn-info btn-lg me-3" onclick="applyPreview()">
                                    <i class="fas fa-eye me-2"></i>Apply Preview
                                </button>
                                <button type="button" class="btn btn-secondary btn-lg" onclick="resetLayout()">
                                    <i class="fas fa-undo me-2"></i>Reset
                                </button>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- Live Preview -->
                <div class="col-lg-4">
                    <div class="card sticky-top">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-eye me-2"></i>
                                Live Preview
                            </h5>
                        </div>
                        <div class="card-body">
                            <div id="livePreview" class="live-preview-container">
                                <div class="preview-app">
                                    <div class="preview-sidebar-large" id="previewSidebar">
                                        <div class="preview-logo">
                                            <i class="fas fa-chart-line"></i>
                                            <span>App</span>
                                        </div>
                                        <div class="preview-menu">
                                            <div class="preview-menu-item active">
                                                <i class="fas fa-home"></i>
                                                <span>Dashboard</span>
                                            </div>
                                            <div class="preview-menu-item">
                                                <i class="fas fa-users"></i>
                                                <span>Users</span>
                                            </div>
                                            <div class="preview-menu-item">
                                                <i class="fas fa-cog"></i>
                                                <span>Settings</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="preview-main-large">
                                        <div class="preview-navbar-large" id="previewNavbar">
                                            <div class="preview-nav-content">
                                                <span>Dashboard</span>
                                                <div class="preview-nav-actions">
                                                    <i class="fas fa-bell"></i>
                                                    <i class="fas fa-user"></i>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="preview-content-large">
                                            <div class="preview-card-large">
                                                <h6>Sample Card</h6>
                                                <p>This is how your content will look</p>
                                            </div>
                                            <div class="preview-card-large">
                                                <h6>Another Card</h6>
                                                <p>With the selected layout style</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="text-center mt-3">
                                <button type="button" class="btn btn-sm btn-outline-primary" onclick="updateLivePreview()">
                                    <i class="fas fa-sync me-1"></i>Update Preview
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- External JavaScript -->
<script src="assets/js/layout-manager.js?v=<?= time() ?>"></script>

<?php include 'includes/views/layouts/footer.php'; ?>
