<?php
/**
 * Create Layout Preferences Table
 * Run this script to create the required layout_preferences table
 */

require_once 'includes/config/database.php';
require_once 'includes/helpers/functions.php';

// Check admin access
$currentUser = getCurrentUser();
if (!$currentUser || $currentUser['role'] !== 'admin') {
    die('Access denied! Admin access required.');
}

echo "<h2>🗄️ Creating Layout Preferences Table</h2>\n";

try {
    // Check if table already exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'layout_preferences'");
    if ($stmt->rowCount() > 0) {
        echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 10px 0; border-radius: 5px;'>\n";
        echo "⚠️ Table 'layout_preferences' already exists. Skipping creation.\n";
        echo "</div>\n";
    } else {
        // Create layout_preferences table
        $sql = "CREATE TABLE layout_preferences (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            layout_type ENUM('classic', 'modern', 'colorful', 'minimal', 'gradient', 'glassmorphism', 'neon', 'corporate', 'retro') DEFAULT 'classic',
            sidebar_style ENUM('classic', 'modern', 'floating', 'transparent', 'gradient', 'glassmorphism') DEFAULT 'classic',
            navbar_style ENUM('classic', 'modern', 'floating', 'transparent', 'gradient', 'glassmorphism') DEFAULT 'classic',
            footer_style ENUM('classic', 'modern', 'floating', 'transparent', 'gradient', 'minimal') DEFAULT 'classic',
            main_content_style ENUM('classic', 'modern', 'cards', 'floating', 'gradient', 'glassmorphism') DEFAULT 'classic',
            color_scheme ENUM('default', 'vibrant', 'pastel', 'neon', 'earth', 'ocean', 'sunset', 'forest', 'midnight', 'royal', 'cyberpunk', 'autumn') DEFAULT 'default',
            border_radius ENUM('none', 'small', 'medium', 'large', 'xl') DEFAULT 'medium',
            shadow_style ENUM('none', 'soft', 'medium', 'strong', 'colored') DEFAULT 'soft',
            animation_style ENUM('none', 'subtle', 'smooth', 'bouncy', 'elastic') DEFAULT 'subtle',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY unique_user (user_id),
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

        $pdo->exec($sql);

        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; margin: 10px 0; border-radius: 5px;'>\n";
        echo "✅ Table 'layout_preferences' created successfully!\n";
        echo "</div>\n";
    }

    // Show table structure
    echo "<h3>📋 Table Structure:</h3>\n";
    $stmt = $pdo->query("DESCRIBE layout_preferences");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);

    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>\n";
    echo "<tr style='background: #f8f9fa;'>\n";
    echo "<th style='padding: 8px; text-align: left;'>Field</th>\n";
    echo "<th style='padding: 8px; text-align: left;'>Type</th>\n";
    echo "<th style='padding: 8px; text-align: left;'>Null</th>\n";
    echo "<th style='padding: 8px; text-align: left;'>Key</th>\n";
    echo "<th style='padding: 8px; text-align: left;'>Default</th>\n";
    echo "<th style='padding: 8px; text-align: left;'>Extra</th>\n";
    echo "</tr>\n";

    foreach ($columns as $column) {
        echo "<tr>\n";
        echo "<td style='padding: 8px;'>{$column['Field']}</td>\n";
        echo "<td style='padding: 8px;'>{$column['Type']}</td>\n";
        echo "<td style='padding: 8px;'>{$column['Null']}</td>\n";
        echo "<td style='padding: 8px;'>{$column['Key']}</td>\n";
        echo "<td style='padding: 8px;'>{$column['Default']}</td>\n";
        echo "<td style='padding: 8px;'>{$column['Extra']}</td>\n";
        echo "</tr>\n";
    }
    echo "</table>\n";

    // Create default layout preferences for current user
    echo "<h3>👤 Creating Default Preferences:</h3>\n";

    try {
        $stmt = $pdo->prepare("
            INSERT INTO layout_preferences (user_id, layout_type, color_scheme)
            VALUES (?, 'classic', 'default')
            ON DUPLICATE KEY UPDATE updated_at = CURRENT_TIMESTAMP
        ");
        $result = $stmt->execute([$currentUser['id']]);

        if ($result) {
            echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; margin: 10px 0; border-radius: 5px;'>\n";
            echo "✅ Default layout preferences created for user ID: {$currentUser['id']}\n";
            echo "</div>\n";
        }
    } catch (PDOException $e) {
        if (strpos($e->getMessage(), 'Duplicate entry') !== false) {
            echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 10px 0; border-radius: 5px;'>\n";
            echo "⚠️ Layout preferences already exist for user ID: {$currentUser['id']}\n";
            echo "</div>\n";
        } else {
            throw $e;
        }
    }

    // Show current user's layout preferences
    echo "<h3>🎨 Current User Layout Preferences:</h3>\n";
    $stmt = $pdo->prepare("SELECT * FROM layout_preferences WHERE user_id = ?");
    $stmt->execute([$currentUser['id']]);
    $userPrefs = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($userPrefs) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>\n";
        echo "<tr style='background: #f8f9fa;'>\n";
        echo "<th style='padding: 8px; text-align: left;'>Setting</th>\n";
        echo "<th style='padding: 8px; text-align: left;'>Value</th>\n";
        echo "</tr>\n";

        foreach ($userPrefs as $key => $value) {
            if ($key !== 'id' && $key !== 'user_id') {
                echo "<tr>\n";
                echo "<td style='padding: 8px;'>" . ucfirst(str_replace('_', ' ', $key)) . "</td>\n";
                echo "<td style='padding: 8px;'><strong>{$value}</strong></td>\n";
                echo "</tr>\n";
            }
        }
        echo "</table>\n";
    } else {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; margin: 10px 0; border-radius: 5px;'>\n";
        echo "❌ No layout preferences found for current user.\n";
        echo "</div>\n";
    }

    // Test layout helper functions
    echo "<h3>🧪 Testing Layout Helper Functions:</h3>\n";

    try {
        require_once 'includes/helpers/layout_helper.php';

        // Test getUserLayoutPreferences
        $testPrefs = getUserLayoutPreferences($currentUser['id']);
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; margin: 10px 0; border-radius: 5px;'>\n";
        echo "✅ getUserLayoutPreferences() works correctly\n";
        echo "<details><summary>Layout Preferences Data</summary>\n";
        echo "<pre>" . htmlspecialchars(json_encode($testPrefs, JSON_PRETTY_PRINT)) . "</pre>\n";
        echo "</details>\n";
        echo "</div>\n";

        // Test generateLayoutCSS
        $testCSS = generateLayoutCSS($testPrefs);
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; margin: 10px 0; border-radius: 5px;'>\n";
        echo "✅ generateLayoutCSS() works correctly\n";
        echo "<details><summary>Generated CSS (first 300 chars)</summary>\n";
        echo "<pre>" . htmlspecialchars(substr($testCSS, 0, 300)) . "...</pre>\n";
        echo "</details>\n";
        echo "</div>\n";

    } catch (Exception $e) {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; margin: 10px 0; border-radius: 5px;'>\n";
        echo "❌ Error testing layout functions: " . $e->getMessage() . "\n";
        echo "</div>\n";
    }

    // Create sample layout preferences for testing
    echo "<h3>🎯 Creating Sample Layout Configurations:</h3>\n";

    $sampleLayouts = [
        [
            'name' => 'Colorful Modern',
            'layout_type' => 'colorful',
            'sidebar_style' => 'modern',
            'navbar_style' => 'modern',
            'footer_style' => 'modern',
            'main_content_style' => 'modern',
            'color_scheme' => 'vibrant',
            'border_radius' => 'large',
            'shadow_style' => 'medium',
            'animation_style' => 'smooth'
        ],
        [
            'name' => 'Glassmorphism',
            'layout_type' => 'glassmorphism',
            'sidebar_style' => 'glassmorphism',
            'navbar_style' => 'glassmorphism',
            'footer_style' => 'transparent',
            'main_content_style' => 'glassmorphism',
            'color_scheme' => 'pastel',
            'border_radius' => 'medium',
            'shadow_style' => 'soft',
            'animation_style' => 'subtle'
        ],
        [
            'name' => 'Minimal Clean',
            'layout_type' => 'minimal',
            'sidebar_style' => 'classic',
            'navbar_style' => 'classic',
            'footer_style' => 'minimal',
            'main_content_style' => 'classic',
            'color_scheme' => 'default',
            'border_radius' => 'small',
            'shadow_style' => 'none',
            'animation_style' => 'none'
        ]
    ];

    echo "<div style='background: #e2e3e5; border: 1px solid #d6d8db; padding: 15px; margin: 10px 0; border-radius: 5px;'>\n";
    echo "<strong>📝 Sample Configurations Available:</strong><br>\n";
    foreach ($sampleLayouts as $layout) {
        echo "• <strong>{$layout['name']}</strong>: {$layout['layout_type']} layout with {$layout['color_scheme']} colors<br>\n";
    }
    echo "<br>You can apply these in the Layout Manager or Test Layout page.\n";
    echo "</div>\n";

} catch (PDOException $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; margin: 10px 0; border-radius: 5px;'>\n";
    echo "❌ Database Error: " . $e->getMessage() . "\n";
    echo "</div>\n";

    // Show SQL for manual execution
    echo "<h3>🔧 Manual SQL Execution:</h3>\n";
    echo "<p>If the automatic creation failed, you can run this SQL manually:</p>\n";
    echo "<textarea style='width: 100%; height: 200px; font-family: monospace; font-size: 12px;' readonly>\n";
    echo "CREATE TABLE layout_preferences (\n";
    echo "    id INT AUTO_INCREMENT PRIMARY KEY,\n";
    echo "    user_id INT NOT NULL,\n";
    echo "    layout_type ENUM('classic', 'modern', 'colorful', 'minimal', 'gradient', 'glassmorphism') DEFAULT 'classic',\n";
    echo "    sidebar_style ENUM('classic', 'modern', 'floating', 'transparent', 'gradient', 'glassmorphism') DEFAULT 'classic',\n";
    echo "    navbar_style ENUM('classic', 'modern', 'floating', 'transparent', 'gradient', 'glassmorphism') DEFAULT 'classic',\n";
    echo "    footer_style ENUM('classic', 'modern', 'floating', 'transparent', 'gradient', 'minimal') DEFAULT 'classic',\n";
    echo "    main_content_style ENUM('classic', 'modern', 'cards', 'floating', 'gradient', 'glassmorphism') DEFAULT 'classic',\n";
    echo "    color_scheme ENUM('default', 'vibrant', 'pastel', 'neon', 'earth', 'ocean', 'sunset', 'forest') DEFAULT 'default',\n";
    echo "    border_radius ENUM('none', 'small', 'medium', 'large', 'xl') DEFAULT 'medium',\n";
    echo "    shadow_style ENUM('none', 'soft', 'medium', 'strong', 'colored') DEFAULT 'soft',\n";
    echo "    animation_style ENUM('none', 'subtle', 'smooth', 'bouncy', 'elastic') DEFAULT 'subtle',\n";
    echo "    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n";
    echo "    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,\n";
    echo "    UNIQUE KEY unique_user (user_id),\n";
    echo "    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE\n";
    echo ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;\n";
    echo "</textarea>\n";

} catch (Exception $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; margin: 10px 0; border-radius: 5px;'>\n";
    echo "❌ General Error: " . $e->getMessage() . "\n";
    echo "</div>\n";
}

// Navigation links
echo "<hr>\n";
echo "<h3>🔗 Next Steps:</h3>\n";
echo "<ul>\n";
echo "<li><a href='layout_manager.php'>Layout Manager</a> - Configure your layout preferences</li>\n";
echo "<li><a href='test_layout.php'>Test Layout</a> - Test different layout configurations</li>\n";
echo "<li><a href='test_functions.php'>Test Functions</a> - Verify all functions work</li>\n";
echo "<li><a href='dashboard.php'>Dashboard</a> - Return to main dashboard</li>\n";
echo "</ul>\n";

?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

h2, h3 {
    color: #333;
}

table {
    font-size: 14px;
}

th {
    background: #f8f9fa !important;
}

details {
    margin: 10px 0;
}

summary {
    cursor: pointer;
    font-weight: bold;
}

pre {
    background: #f8f9fa;
    padding: 10px;
    border-radius: 3px;
    overflow-x: auto;
    font-size: 12px;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}
</style>
