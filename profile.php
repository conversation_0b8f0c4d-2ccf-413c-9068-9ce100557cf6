<?php
require_once 'includes/config/database.php';
require_once 'includes/helpers/functions.php';
require_once 'includes/controllers/AuthController.php';

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isLoggedIn()) {
    setFlashMessage('danger', 'Silakan login terlebih dahulu');
    redirect('/keuangan/login.php');
}

$auth = new AuthController($pdo);
$currentUser = getCurrentUser();
$currentPage = 'profile';
$error = '';
$success = '';

// Get current settings
$settings = json_decode($currentUser['settings'] ?? '{}', true);
$settings = array_merge([
    'theme' => 'light',
    'notifications' => true,
    'language' => 'id',
    'currency' => 'IDR',
    'date_format' => 'd/m/Y',
    'time_format' => 'H:i'
], $settings);

// Handle profile update
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_profile'])) {
    $data = [
        'nama' => cleanInput($_POST['nama']),
        'email' => cleanInput($_POST['email'])
    ];
    
    // Validate input
    $errors = validateInput($data, [
        'nama' => 'required',
        'email' => 'required|email'
    ]);
    
    if (empty($errors)) {
        try {
            // Check if email is already used by another user
            $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ? AND id != ?");
            $stmt->execute([$data['email'], $currentUser['id']]);
            if ($stmt->fetch()) {
                $error = 'Email sudah digunakan oleh pengguna lain';
            } else {
                // Update profile
                $stmt = $pdo->prepare("UPDATE users SET nama = ?, email = ? WHERE id = ?");
                $stmt->execute([$data['nama'], $data['email'], $currentUser['id']]);
                
                // Log activity
                logActivity($currentUser['id'], 'User updated profile information');
                
                $success = 'Profil berhasil diperbarui';
                $currentUser = getCurrentUser(); // Refresh user data
            }
        } catch (PDOException $e) {
            $error = 'Terjadi kesalahan saat memperbarui profil';
            error_log("Error updating profile: " . $e->getMessage());
        }
    } else {
        $error = 'Validasi gagal: ' . implode(', ', $errors);
    }
}

// Handle password change
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['change_password'])) {
    $currentPassword = $_POST['current_password'];
    $newPassword = $_POST['new_password'];
    $confirmPassword = $_POST['confirm_password'];
    
    if (empty($currentPassword) || empty($newPassword) || empty($confirmPassword)) {
        $error = 'Semua field password harus diisi';
    } elseif ($newPassword !== $confirmPassword) {
        $error = 'Konfirmasi password tidak sesuai';
    } elseif (strlen($newPassword) < 6) {
        $error = 'Password minimal 6 karakter';
    } else {
        try {
            // Verify current password
            $stmt = $pdo->prepare("SELECT password FROM users WHERE id = ?");
            $stmt->execute([$currentUser['id']]);
            $user = $stmt->fetch();
            
            if (password_verify($currentPassword, $user['password'])) {
                // Update password
                $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
                $stmt = $pdo->prepare("UPDATE users SET password = ? WHERE id = ?");
                $stmt->execute([$hashedPassword, $currentUser['id']]);
                
                // Log activity
                logActivity($currentUser['id'], 'User changed password');
                
                $success = 'Password berhasil diubah';
            } else {
                $error = 'Password saat ini tidak sesuai';
            }
        } catch (PDOException $e) {
            $error = 'Terjadi kesalahan saat mengubah password';
            error_log("Error changing password: " . $e->getMessage());
        }
    }
}

// Handle settings update
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_settings'])) {
    try {
        $newSettings = [
            'theme' => $_POST['theme'] ?? 'light',
            'notifications' => isset($_POST['notifications']) ? 1 : 0,
            'language' => $_POST['language'] ?? 'id',
            'currency' => $_POST['currency'] ?? 'IDR',
            'date_format' => $_POST['date_format'] ?? 'd/m/Y',
            'time_format' => $_POST['time_format'] ?? 'H:i'
        ];
        
        // Update user settings
        $stmt = $pdo->prepare("UPDATE users SET settings = ? WHERE id = ?");
        $stmt->execute([json_encode($newSettings), $currentUser['id']]);
        
        // Log activity
        logActivity($currentUser['id'], 'User updated settings');
        
        $success = 'Pengaturan berhasil diperbarui';
        $settings = $newSettings; // Update current settings
        $currentUser = getCurrentUser(); // Refresh user data
    } catch (PDOException $e) {
        $error = 'Terjadi kesalahan saat memperbarui pengaturan';
        error_log("Error updating settings: " . $e->getMessage());
    }
}

// Get user activity logs with pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$perPage = 10;
$offset = ($page - 1) * $perPage;

try {
    // Get total records
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM activity_logs WHERE user_id = ?");
    $stmt->execute([$currentUser['id']]);
    $totalRecords = $stmt->fetchColumn();
    $totalPages = ceil($totalRecords / $perPage);

    // Get activity logs
    $stmt = $pdo->prepare("
        SELECT * FROM activity_logs 
        WHERE user_id = ? 
        ORDER BY created_at DESC 
        LIMIT ? OFFSET ?
    ");
    $stmt->execute([$currentUser['id'], $perPage, $offset]);
    $activityLogs = $stmt->fetchAll();
} catch (PDOException $e) {
    error_log("Error getting activity logs: " . $e->getMessage());
    $activityLogs = [];
    $totalPages = 0;
}

// Include header
include 'includes/views/layouts/header.php';
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Profil & Pengaturan</h1>
    </div>

    <?php if ($error): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <?= htmlspecialchars($error) ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    <?php endif; ?>

    <?php if ($success): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <?= htmlspecialchars($success) ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    <?php endif; ?>

    <div class="row">
        <div class="col-md-3">
            <div class="card">
                <div class="card-body p-0">
                    <div class="list-group list-group-flush">
                        <a href="#profile" class="list-group-item list-group-item-action active" data-bs-toggle="list">
                            <i class="fas fa-user me-2"></i>Profil
                        </a>
                        <a href="#password" class="list-group-item list-group-item-action" data-bs-toggle="list">
                            <i class="fas fa-key me-2"></i>Password
                        </a>
                        <a href="#settings" class="list-group-item list-group-item-action" data-bs-toggle="list">
                            <i class="fas fa-cog me-2"></i>Pengaturan
                        </a>
                        <a href="#activity" class="list-group-item list-group-item-action" data-bs-toggle="list">
                            <i class="fas fa-history me-2"></i>Aktivitas
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-9">
            <div class="tab-content">
                <!-- Profile Tab -->
                <div class="tab-pane fade show active" id="profile">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Informasi Profil</h5>
                        </div>
                        <div class="card-body">
                            <form method="POST" class="needs-validation" novalidate>
                                <div class="mb-3">
                                    <label for="nama" class="form-label">Nama Lengkap</label>
                                    <input type="text" class="form-control" id="nama" name="nama" 
                                           value="<?= htmlspecialchars($currentUser['nama']) ?>" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="email" class="form-label">Email</label>
                                    <input type="email" class="form-control" id="email" name="email" 
                                           value="<?= htmlspecialchars($currentUser['email']) ?>" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">Role</label>
                                    <input type="text" class="form-control" value="<?= ucfirst($currentUser['role']) ?>" readonly>
                                </div>
                                
                                <button type="submit" name="update_profile" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>Simpan Perubahan
                                </button>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Password Tab -->
                <div class="tab-pane fade" id="password">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Ubah Password</h5>
                        </div>
                        <div class="card-body">
                            <form method="POST" class="needs-validation" novalidate>
                                <div class="mb-3">
                                    <label for="current_password" class="form-label">Password Saat Ini</label>
                                    <input type="password" class="form-control" id="current_password" 
                                           name="current_password" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="new_password" class="form-label">Password Baru</label>
                                    <input type="password" class="form-control" id="new_password" 
                                           name="new_password" required minlength="6">
                                    <div class="form-text">
                                        Password minimal 6 karakter
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="confirm_password" class="form-label">Konfirmasi Password Baru</label>
                                    <input type="password" class="form-control" id="confirm_password" 
                                           name="confirm_password" required>
                                </div>
                                
                                <button type="submit" name="change_password" class="btn btn-primary">
                                    <i class="fas fa-key me-2"></i>Ubah Password
                                </button>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Settings Tab -->
                <div class="tab-pane fade" id="settings">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Pengaturan</h5>
                        </div>
                        <div class="card-body">
                            <form method="POST" class="needs-validation" novalidate>
                                <div class="mb-3">
                                    <label class="form-label">Tema</label>
                                    <select class="form-select" name="theme">
                                        <option value="light" <?= $settings['theme'] === 'light' ? 'selected' : '' ?>>Light</option>
                                        <option value="dark" <?= $settings['theme'] === 'dark' ? 'selected' : '' ?>>Dark</option>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">Bahasa</label>
                                    <select class="form-select" name="language">
                                        <option value="id" <?= $settings['language'] === 'id' ? 'selected' : '' ?>>Indonesia</option>
                                        <option value="en" <?= $settings['language'] === 'en' ? 'selected' : '' ?>>English</option>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">Mata Uang</label>
                                    <select class="form-select" name="currency">
                                        <option value="IDR" <?= $settings['currency'] === 'IDR' ? 'selected' : '' ?>>Rupiah (IDR)</option>
                                        <option value="USD" <?= $settings['currency'] === 'USD' ? 'selected' : '' ?>>US Dollar (USD)</option>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">Format Tanggal</label>
                                    <select class="form-select" name="date_format">
                                        <option value="d/m/Y" <?= $settings['date_format'] === 'd/m/Y' ? 'selected' : '' ?>>DD/MM/YYYY</option>
                                        <option value="Y-m-d" <?= $settings['date_format'] === 'Y-m-d' ? 'selected' : '' ?>>YYYY-MM-DD</option>
                                        <option value="d-m-Y" <?= $settings['date_format'] === 'd-m-Y' ? 'selected' : '' ?>>DD-MM-YYYY</option>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">Format Waktu</label>
                                    <select class="form-select" name="time_format">
                                        <option value="H:i" <?= $settings['time_format'] === 'H:i' ? 'selected' : '' ?>>24 Jam (HH:mm)</option>
                                        <option value="h:i A" <?= $settings['time_format'] === 'h:i A' ? 'selected' : '' ?>>12 Jam (hh:mm AM/PM)</option>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" name="notifications" 
                                               id="notifications" <?= $settings['notifications'] ? 'checked' : '' ?>>
                                        <label class="form-check-label" for="notifications">
                                            Aktifkan Notifikasi
                                        </label>
                                    </div>
                                </div>

                                <button type="submit" name="update_settings" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>Simpan Pengaturan
                                </button>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Activity Tab -->
                <div class="tab-pane fade" id="activity">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Aktivitas Terakhir</h5>
                        </div>
                        <div class="card-body">
                            <?php if (empty($activityLogs)): ?>
                            <p class="text-muted mb-0">Belum ada aktivitas</p>
                            <?php else: ?>
                            <div class="list-group list-group-flush">
                                <?php foreach ($activityLogs as $log): ?>
                                <div class="list-group-item px-0">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <p class="mb-0"><?= htmlspecialchars($log['activity']) ?></p>
                                            <small class="text-muted">
                                                <?= date($settings['date_format'] . ' ' . $settings['time_format'], strtotime($log['created_at'])) ?>
                                            </small>
                                        </div>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>

                            <?php if ($totalPages > 1): ?>
                            <nav class="mt-4">
                                <ul class="pagination justify-content-center">
                                    <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                                    <li class="page-item <?= $i === $page ? 'active' : '' ?>">
                                        <a class="page-link" href="?page=<?= $i ?>"><?= $i ?></a>
                                    </li>
                                    <?php endfor; ?>
                                </ul>
                            </nav>
                            <?php endif; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Password validation
const newPassword = document.getElementById('new_password');
const confirmPassword = document.getElementById('confirm_password');

function validatePassword() {
    if (newPassword.value !== confirmPassword.value) {
        confirmPassword.setCustomValidity('Password tidak sesuai');
    } else {
        confirmPassword.setCustomValidity('');
    }
}

newPassword.addEventListener('change', validatePassword);
confirmPassword.addEventListener('keyup', validatePassword);

// Settings change handlers
document.querySelector('select[name="theme"]').addEventListener('change', function() {
    document.documentElement.setAttribute('data-bs-theme', this.value);
    localStorage.setItem('theme', this.value);
});

// Form validation
(function() {
    'use strict';
    
    const forms = document.querySelectorAll('.needs-validation');
    
    Array.from(forms).forEach(form => {
        form.addEventListener('submit', event => {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        }, false);
    });
})();

// Auto-dismiss alerts after 5 seconds
document.querySelectorAll('.alert').forEach(alert => {
    setTimeout(() => {
        const bsAlert = new bootstrap.Alert(alert);
        bsAlert.close();
    }, 5000);
});
</script>

<?php include 'includes/views/layouts/footer.php'; ?> 