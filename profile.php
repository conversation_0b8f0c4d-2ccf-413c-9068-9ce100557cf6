<?php
require_once 'includes/config/database.php';
require_once 'includes/helpers/functions.php';
require_once 'includes/controllers/AuthController.php';

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isLoggedIn()) {
    setFlashMessage('danger', 'Silakan login terlebih dahulu');
    redirect('/keuangan/login.php');
}

$auth = new AuthController($pdo);
$currentUser = getCurrentUser();
$currentPage = 'profile';
$error = '';
$success = '';

// Get current settings
$settings = json_decode($currentUser['settings'] ?? '{}', true);
$settings = array_merge([
    'theme' => 'light',
    'notifications' => true,
    'language' => 'id',
    'currency' => 'IDR',
    'date_format' => 'd/m/Y',
    'time_format' => 'H:i'
], $settings);

// Handle profile update
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_profile'])) {
    $data = [
        'nama' => cleanInput($_POST['nama']),
        'email' => cleanInput($_POST['email'])
    ];
    
    // Validate input
    $errors = validateInput($data, [
        'nama' => 'required',
        'email' => 'required|email'
    ]);
    
    if (empty($errors)) {
        try {
            // Check if email is already used by another user
            $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ? AND id != ?");
            $stmt->execute([$data['email'], $currentUser['id']]);
            if ($stmt->fetch()) {
                $error = 'Email sudah digunakan oleh pengguna lain';
            } else {
                // Update profile
                $stmt = $pdo->prepare("UPDATE users SET nama = ?, email = ? WHERE id = ?");
                $stmt->execute([$data['nama'], $data['email'], $currentUser['id']]);
                
                // Log activity
                logActivity($currentUser['id'], 'User updated profile information');
                
                $success = 'Profil berhasil diperbarui';
                $currentUser = getCurrentUser(); // Refresh user data
            }
        } catch (PDOException $e) {
            $error = 'Terjadi kesalahan saat memperbarui profil';
            error_log("Error updating profile: " . $e->getMessage());
        }
    } else {
        $error = 'Validasi gagal: ' . implode(', ', $errors);
    }
}

// Handle password change
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['change_password'])) {
    $currentPassword = $_POST['current_password'];
    $newPassword = $_POST['new_password'];
    $confirmPassword = $_POST['confirm_password'];
    
    if (empty($currentPassword) || empty($newPassword) || empty($confirmPassword)) {
        $error = 'Semua field password harus diisi';
    } elseif ($newPassword !== $confirmPassword) {
        $error = 'Konfirmasi password tidak sesuai';
    } elseif (strlen($newPassword) < 6) {
        $error = 'Password minimal 6 karakter';
    } else {
        try {
            // Verify current password
            $stmt = $pdo->prepare("SELECT password FROM users WHERE id = ?");
            $stmt->execute([$currentUser['id']]);
            $user = $stmt->fetch();
            
            if (password_verify($currentPassword, $user['password'])) {
                // Update password
                $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
                $stmt = $pdo->prepare("UPDATE users SET password = ? WHERE id = ?");
                $stmt->execute([$hashedPassword, $currentUser['id']]);
                
                // Log activity
                logActivity($currentUser['id'], 'User changed password');
                
                $success = 'Password berhasil diubah';
            } else {
                $error = 'Password saat ini tidak sesuai';
            }
        } catch (PDOException $e) {
            $error = 'Terjadi kesalahan saat mengubah password';
            error_log("Error changing password: " . $e->getMessage());
        }
    }
}

// Handle settings update
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_settings'])) {
    try {
        $newSettings = [
            'theme' => $_POST['theme'] ?? 'light',
            'notifications' => isset($_POST['notifications']) ? 1 : 0,
            'language' => $_POST['language'] ?? 'id',
            'currency' => $_POST['currency'] ?? 'IDR',
            'date_format' => $_POST['date_format'] ?? 'd/m/Y',
            'time_format' => $_POST['time_format'] ?? 'H:i'
        ];
        
        // Update user settings
        $stmt = $pdo->prepare("UPDATE users SET settings = ? WHERE id = ?");
        $stmt->execute([json_encode($newSettings), $currentUser['id']]);
        
        // Log activity
        logActivity($currentUser['id'], 'User updated settings');
        
        $success = 'Pengaturan berhasil diperbarui';
        $settings = $newSettings; // Update current settings
        $currentUser = getCurrentUser(); // Refresh user data
    } catch (PDOException $e) {
        $error = 'Terjadi kesalahan saat memperbarui pengaturan';
        error_log("Error updating settings: " . $e->getMessage());
    }
}

// Get user activity logs with pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$perPage = 10;
$offset = ($page - 1) * $perPage;

try {
    // Get total records
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM activity_logs WHERE user_id = ?");
    $stmt->execute([$currentUser['id']]);
    $totalRecords = $stmt->fetchColumn();
    $totalPages = ceil($totalRecords / $perPage);

    // Get activity logs
    $stmt = $pdo->prepare("
        SELECT * FROM activity_logs 
        WHERE user_id = ? 
        ORDER BY created_at DESC 
        LIMIT ? OFFSET ?
    ");
    $stmt->execute([$currentUser['id'], $perPage, $offset]);
    $activityLogs = $stmt->fetchAll();
} catch (PDOException $e) {
    error_log("Error getting activity logs: " . $e->getMessage());
    $activityLogs = [];
    $totalPages = 0;
}

// Include header and sidebar
require_once 'includes/views/layouts/header.php';
require_once 'includes/views/layouts/sidebar.php';
?>

<div class="modern-layout">
    <div class="modern-container">
        <!-- Modern Page Header -->
        <div class="modern-page-header modern-mb-xl">
            <h1 class="modern-page-title modern-mb-0">Profil & Pengaturan</h1>
            <p class="modern-page-subtitle">Kelola informasi profil dan pengaturan akun Anda</p>
        </div>

        <!-- Flash Messages -->
        <?php if ($error): ?>
        <div class="modern-alert modern-alert-danger modern-mb-lg">
            <div class="modern-alert-content">
                <i class="fas fa-exclamation-triangle modern-alert-icon"></i>
                <div class="modern-alert-message"><?= htmlspecialchars($error) ?></div>
            </div>
            <button type="button" class="modern-alert-close" onclick="this.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <?php endif; ?>

        <?php if ($success): ?>
        <div class="modern-alert modern-alert-success modern-mb-lg">
            <div class="modern-alert-content">
                <i class="fas fa-check-circle modern-alert-icon"></i>
                <div class="modern-alert-message"><?= htmlspecialchars($success) ?></div>
            </div>
            <button type="button" class="modern-alert-close" onclick="this.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <?php endif; ?>

        <!-- Modern Tab Layout -->
        <div class="modern-grid modern-grid-cols-4 modern-gap-lg">
            <!-- Modern Navigation Tabs -->
            <div class="modern-card">
                <div class="modern-card-header">
                    <h5 class="modern-card-title">
                        <i class="fas fa-user-cog modern-text-primary modern-mr-sm"></i>
                        Menu
                    </h5>
                </div>
                <div class="modern-card-body modern-p-0">
                    <div class="modern-nav-tabs">
                        <a href="#profile" class="modern-nav-tab active" data-bs-toggle="list">
                            <i class="fas fa-user"></i>
                            <span>Profil</span>
                        </a>
                        <a href="#password" class="modern-nav-tab" data-bs-toggle="list">
                            <i class="fas fa-key"></i>
                            <span>Password</span>
                        </a>
                        <a href="#settings" class="modern-nav-tab" data-bs-toggle="list">
                            <i class="fas fa-cog"></i>
                            <span>Pengaturan</span>
                        </a>
                        <a href="#activity" class="modern-nav-tab" data-bs-toggle="list">
                            <i class="fas fa-history"></i>
                            <span>Aktivitas</span>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Modern Tab Content -->
            <div class="modern-tab-content">
                <!-- Profile Tab -->
                <div class="tab-pane fade show active" id="profile">
                    <div class="modern-card">
                        <div class="modern-card-header">
                            <h5 class="modern-card-title">
                                <i class="fas fa-user modern-text-primary modern-mr-sm"></i>
                                Informasi Profil
                            </h5>
                        </div>
                        <div class="modern-card-body">
                            <form method="POST" class="needs-validation" novalidate>
                                <div class="modern-form-group">
                                    <label for="nama" class="modern-form-label">
                                        <i class="fas fa-user modern-text-primary"></i>
                                        Nama Lengkap <span class="modern-text-danger">*</span>
                                    </label>
                                    <input type="text" class="modern-form-control" id="nama" name="nama"
                                           value="<?= htmlspecialchars($currentUser['nama']) ?>" required>
                                    <div class="invalid-feedback">Nama lengkap harus diisi</div>
                                </div>

                                <div class="modern-form-group">
                                    <label for="email" class="modern-form-label">
                                        <i class="fas fa-envelope modern-text-primary"></i>
                                        Email <span class="modern-text-danger">*</span>
                                    </label>
                                    <input type="email" class="modern-form-control" id="email" name="email"
                                           value="<?= htmlspecialchars($currentUser['email']) ?>" required>
                                    <div class="invalid-feedback">Email harus valid</div>
                                </div>

                                <div class="modern-form-group">
                                    <label class="modern-form-label">
                                        <i class="fas fa-shield-alt modern-text-primary"></i>
                                        Role
                                    </label>
                                    <input type="text" class="modern-form-control" value="<?= ucfirst($currentUser['role']) ?>" readonly>
                                </div>

                                <div class="modern-form-group modern-mb-0">
                                    <button type="submit" name="update_profile" class="modern-btn modern-btn-primary">
                                        <i class="fas fa-save"></i>
                                        Simpan Perubahan
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Password Tab -->
                <div class="tab-pane fade" id="password">
                    <div class="modern-card">
                        <div class="modern-card-header">
                            <h5 class="modern-card-title">
                                <i class="fas fa-key modern-text-primary modern-mr-sm"></i>
                                Ubah Password
                            </h5>
                        </div>
                        <div class="modern-card-body">
                            <form method="POST" class="needs-validation" novalidate>
                                <div class="modern-form-group">
                                    <label for="current_password" class="modern-form-label">
                                        <i class="fas fa-lock modern-text-primary"></i>
                                        Password Saat Ini <span class="modern-text-danger">*</span>
                                    </label>
                                    <input type="password" class="modern-form-control" id="current_password"
                                           name="current_password" required placeholder="Masukkan password saat ini">
                                    <div class="invalid-feedback">Password saat ini harus diisi</div>
                                </div>

                                <div class="modern-form-group">
                                    <label for="new_password" class="modern-form-label">
                                        <i class="fas fa-key modern-text-primary"></i>
                                        Password Baru <span class="modern-text-danger">*</span>
                                    </label>
                                    <input type="password" class="modern-form-control" id="new_password"
                                           name="new_password" required minlength="6" placeholder="Masukkan password baru">
                                    <small class="modern-form-help">
                                        <i class="fas fa-info-circle modern-mr-xs"></i>
                                        Password minimal 6 karakter
                                    </small>
                                    <div class="invalid-feedback">Password baru minimal 6 karakter</div>
                                </div>

                                <div class="modern-form-group">
                                    <label for="confirm_password" class="modern-form-label">
                                        <i class="fas fa-check-circle modern-text-primary"></i>
                                        Konfirmasi Password Baru <span class="modern-text-danger">*</span>
                                    </label>
                                    <input type="password" class="modern-form-control" id="confirm_password"
                                           name="confirm_password" required placeholder="Konfirmasi password baru">
                                    <div class="invalid-feedback">Konfirmasi password harus sesuai</div>
                                </div>

                                <div class="modern-form-group modern-mb-0">
                                    <button type="submit" name="change_password" class="modern-btn modern-btn-primary">
                                        <i class="fas fa-key"></i>
                                        Ubah Password
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Settings Tab -->
                <div class="tab-pane fade" id="settings">
                    <div class="modern-card">
                        <div class="modern-card-header">
                            <h5 class="modern-card-title">
                                <i class="fas fa-cog modern-text-primary modern-mr-sm"></i>
                                Pengaturan
                            </h5>
                        </div>
                        <div class="modern-card-body">
                            <form method="POST" class="needs-validation" novalidate>
                                <div class="modern-form-group">
                                    <label class="modern-form-label">
                                        <i class="fas fa-palette modern-text-primary"></i>
                                        Tema
                                    </label>
                                    <select class="modern-form-control" name="theme">
                                        <option value="light" <?= $settings['theme'] === 'light' ? 'selected' : '' ?>>🌞 Light Mode</option>
                                        <option value="dark" <?= $settings['theme'] === 'dark' ? 'selected' : '' ?>>🌙 Dark Mode</option>
                                    </select>
                                </div>

                                <div class="modern-form-group">
                                    <label class="modern-form-label">
                                        <i class="fas fa-language modern-text-primary"></i>
                                        Bahasa
                                    </label>
                                    <select class="modern-form-control" name="language">
                                        <option value="id" <?= $settings['language'] === 'id' ? 'selected' : '' ?>>🇮🇩 Indonesia</option>
                                        <option value="en" <?= $settings['language'] === 'en' ? 'selected' : '' ?>>🇺🇸 English</option>
                                    </select>
                                </div>

                                <div class="modern-form-group">
                                    <label class="modern-form-label">
                                        <i class="fas fa-money-bill modern-text-primary"></i>
                                        Mata Uang
                                    </label>
                                    <select class="modern-form-control" name="currency">
                                        <option value="IDR" <?= $settings['currency'] === 'IDR' ? 'selected' : '' ?>>💰 Rupiah (IDR)</option>
                                        <option value="USD" <?= $settings['currency'] === 'USD' ? 'selected' : '' ?>>💵 US Dollar (USD)</option>
                                    </select>
                                </div>

                                <div class="modern-form-group">
                                    <label class="modern-form-label">
                                        <i class="fas fa-calendar modern-text-primary"></i>
                                        Format Tanggal
                                    </label>
                                    <select class="modern-form-control" name="date_format">
                                        <option value="d/m/Y" <?= $settings['date_format'] === 'd/m/Y' ? 'selected' : '' ?>>📅 DD/MM/YYYY</option>
                                        <option value="Y-m-d" <?= $settings['date_format'] === 'Y-m-d' ? 'selected' : '' ?>>📅 YYYY-MM-DD</option>
                                        <option value="d-m-Y" <?= $settings['date_format'] === 'd-m-Y' ? 'selected' : '' ?>>📅 DD-MM-YYYY</option>
                                    </select>
                                </div>

                                <div class="modern-form-group">
                                    <label class="modern-form-label">
                                        <i class="fas fa-clock modern-text-primary"></i>
                                        Format Waktu
                                    </label>
                                    <select class="modern-form-control" name="time_format">
                                        <option value="H:i" <?= $settings['time_format'] === 'H:i' ? 'selected' : '' ?>>🕐 24 Jam (HH:mm)</option>
                                        <option value="h:i A" <?= $settings['time_format'] === 'h:i A' ? 'selected' : '' ?>>🕐 12 Jam (hh:mm AM/PM)</option>
                                    </select>
                                </div>

                                <div class="modern-form-group">
                                    <div class="modern-form-switch">
                                        <input class="modern-switch-input" type="checkbox" name="notifications"
                                               id="notifications" <?= $settings['notifications'] ? 'checked' : '' ?>>
                                        <label class="modern-switch-label" for="notifications">
                                            <span class="modern-switch-slider"></span>
                                            <span class="modern-switch-text">
                                                <i class="fas fa-bell modern-text-primary"></i>
                                                Aktifkan Notifikasi
                                            </span>
                                        </label>
                                    </div>
                                </div>

                                <div class="modern-form-group modern-mb-0">
                                    <button type="submit" name="update_settings" class="modern-btn modern-btn-primary">
                                        <i class="fas fa-save"></i>
                                        Simpan Pengaturan
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Activity Tab -->
                <div class="tab-pane fade" id="activity">
                    <div class="modern-card">
                        <div class="modern-card-header">
                            <h5 class="modern-card-title">
                                <i class="fas fa-history modern-text-primary modern-mr-sm"></i>
                                Aktivitas Terakhir
                            </h5>
                        </div>
                        <div class="modern-card-body">
                            <?php if (empty($activityLogs)): ?>
                            <div class="modern-empty-state">
                                <div class="modern-empty-icon">
                                    <i class="fas fa-history"></i>
                                </div>
                                <div class="modern-empty-content">
                                    <h6 class="modern-empty-title">Belum Ada Aktivitas</h6>
                                    <p class="modern-empty-text">Aktivitas Anda akan muncul di sini</p>
                                </div>
                            </div>
                            <?php else: ?>
                            <div class="modern-activity-list">
                                <?php foreach ($activityLogs as $log): ?>
                                <div class="modern-activity-item">
                                    <div class="modern-activity-icon">
                                        <i class="fas fa-circle"></i>
                                    </div>
                                    <div class="modern-activity-content">
                                        <div class="modern-activity-title"><?= htmlspecialchars($log['activity']) ?></div>
                                        <div class="modern-activity-time">
                                            <i class="fas fa-clock modern-mr-xs"></i>
                                            <?= date($settings['date_format'] . ' ' . $settings['time_format'], strtotime($log['created_at'])) ?>
                                        </div>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>

                            <?php if ($totalPages > 1): ?>
                            <div class="modern-pagination-wrapper modern-mt-lg">
                                <nav class="modern-pagination">
                                    <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                                    <a class="modern-pagination-number <?= $i === $page ? 'active' : '' ?>" href="?page=<?= $i ?>"><?= $i ?></a>
                                    <?php endfor; ?>
                                </nav>
                            </div>
                            <?php endif; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Password validation
const newPassword = document.getElementById('new_password');
const confirmPassword = document.getElementById('confirm_password');

function validatePassword() {
    if (newPassword.value !== confirmPassword.value) {
        confirmPassword.setCustomValidity('Password tidak sesuai');
    } else {
        confirmPassword.setCustomValidity('');
    }
}

newPassword.addEventListener('change', validatePassword);
confirmPassword.addEventListener('keyup', validatePassword);

// Settings change handlers
document.querySelector('select[name="theme"]').addEventListener('change', function() {
    document.documentElement.setAttribute('data-bs-theme', this.value);
    localStorage.setItem('theme', this.value);
});

// Form validation
(function() {
    'use strict';
    
    const forms = document.querySelectorAll('.needs-validation');
    
    Array.from(forms).forEach(form => {
        form.addEventListener('submit', event => {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        }, false);
    });
})();

// Auto-dismiss alerts after 5 seconds
document.querySelectorAll('.alert').forEach(alert => {
    setTimeout(() => {
        const bsAlert = new bootstrap.Alert(alert);
        bsAlert.close();
    }, 5000);
});
</script>

<?php include 'includes/views/layouts/footer.php'; ?> 