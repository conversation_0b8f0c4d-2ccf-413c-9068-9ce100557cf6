<?php
/**
 * Fix All Pages Script
 * 
 * This script fixes common issues across all PHP pages
 */

echo "<h2>🔧 Fixing All Pages</h2>\n";

// List of all PHP files to fix
$phpFiles = [
    'dashboard.php',
    'admin-dashboard.php', 
    'transaksi.php',
    'kategori.php',
    'target.php',
    'laporan.php',
    'users.php',
    'supplier.php',
    'inventory.php',
    'return.php',
    'menu_permissions_advanced.php',
    'customization_dashboard.php'
];

$fixedFiles = [];
$errorFiles = [];

foreach ($phpFiles as $file) {
    if (!file_exists($file)) {
        echo "⚠️ File not found: $file<br>\n";
        continue;
    }
    
    echo "<h3>Fixing: $file</h3>\n";
    
    $content = file_get_contents($file);
    $originalContent = $content;
    $changes = 0;
    
    // 1. Add function_check.php if missing
    if (strpos($content, "require_once 'includes/helpers/functions.php'") !== false &&
        strpos($content, "require_once 'includes/helpers/function_check.php'") === false) {
        
        $content = str_replace(
            "require_once 'includes/helpers/functions.php'",
            "require_once 'includes/helpers/function_check.php';\nrequire_once 'includes/helpers/functions.php'",
            $content
        );
        $changes++;
        echo "  ✅ Added function_check.php include<br>\n";
    }
    
    // 2. Fix redirect paths
    $redirectFixes = [
        "redirect('/keuangan/login.php')" => "redirect('login.php')",
        "redirect('/login.php')" => "redirect('login.php')",
        "redirect('/dashboard.php')" => "redirect('dashboard.php')",
        "redirect('/transaksi.php')" => "redirect('transaksi.php')",
        "redirect('/kategori.php')" => "redirect('kategori.php')",
        "redirect('/target.php')" => "redirect('target.php')",
        "redirect('/laporan.php')" => "redirect('laporan.php')",
        "redirect('/users.php')" => "redirect('users.php')",
        "redirect('/supplier.php')" => "redirect('supplier.php')",
        "redirect('/inventory.php')" => "redirect('inventory.php')",
        "redirect('/return.php')" => "redirect('return.php')"
    ];
    
    foreach ($redirectFixes as $old => $new) {
        if (strpos($content, $old) !== false) {
            $content = str_replace($old, $new, $content);
            $changes++;
            echo "  ✅ Fixed redirect: $old → $new<br>\n";
        }
    }
    
    // 3. Fix function names
    $functionFixes = [
        'formatDate(' => 'formatTanggal(',
        'cleanInput(' => 'sanitizeInput(',
        'validateInput(' => 'validateFormData('
    ];
    
    foreach ($functionFixes as $old => $new) {
        if (strpos($content, $old) !== false) {
            $content = str_replace($old, $new, $content);
            $changes++;
            echo "  ✅ Fixed function: $old → $new<br>\n";
        }
    }
    
    // 4. Fix session start issues
    if (strpos($content, 'session_start()') !== false && 
        strpos($content, 'if (session_status() === PHP_SESSION_NONE)') === false) {
        
        $content = str_replace(
            'session_start();',
            "if (session_status() === PHP_SESSION_NONE) {\n    session_start();\n}",
            $content
        );
        $changes++;
        echo "  ✅ Fixed session_start()<br>\n";
    }
    
    // 5. Add error handling for database operations
    if (strpos($content, '$pdo->prepare') !== false && 
        strpos($content, 'try {') === false) {
        
        // This is a complex fix, just note it for manual review
        echo "  ⚠️ Manual review needed: Add try-catch for database operations<br>\n";
    }
    
    // Save changes if any were made
    if ($content !== $originalContent) {
        if (file_put_contents($file, $content)) {
            $fixedFiles[] = $file;
            echo "  ✅ Fixed $changes issue(s) in $file<br>\n";
        } else {
            $errorFiles[] = $file;
            echo "  ❌ Failed to save changes to $file<br>\n";
        }
    } else {
        echo "  ✅ No issues found in $file<br>\n";
    }
}

echo "<h3>📊 Summary</h3>\n";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 10px 0;'>\n";
echo "<strong>Total Files Processed:</strong> " . count($phpFiles) . "<br>\n";
echo "<strong>✅ Files Fixed:</strong> " . count($fixedFiles) . "<br>\n";
echo "<strong>❌ Files with Errors:</strong> " . count($errorFiles) . "<br>\n";
echo "</div>\n";

if (!empty($fixedFiles)) {
    echo "<h4>✅ Fixed Files:</h4>\n";
    echo "<ul>\n";
    foreach ($fixedFiles as $file) {
        echo "<li>$file</li>\n";
    }
    echo "</ul>\n";
}

if (!empty($errorFiles)) {
    echo "<h4>❌ Files with Errors:</h4>\n";
    echo "<ul>\n";
    foreach ($errorFiles as $file) {
        echo "<li>$file</li>\n";
    }
    echo "</ul>\n";
}

// Test core functions
echo "<h3>🧪 Testing Core Functions</h3>\n";

try {
    require_once 'includes/config/database.php';
    require_once 'includes/helpers/function_check.php';
    
    $testFunctions = [
        'setFlashMessage',
        'getFlashMessage',
        'formatRupiah',
        'formatTanggal',
        'getCurrentUser',
        'isLoggedIn',
        'sanitizeInput',
        'redirect'
    ];
    
    $availableFunctions = 0;
    foreach ($testFunctions as $func) {
        if (function_exists($func)) {
            echo "✅ $func is available<br>\n";
            $availableFunctions++;
        } else {
            echo "❌ $func is NOT available<br>\n";
        }
    }
    
    echo "<p><strong>Functions Available:</strong> $availableFunctions / " . count($testFunctions) . "</p>\n";
    
} catch (Exception $e) {
    echo "❌ Error testing functions: " . $e->getMessage() . "<br>\n";
}

echo "<h3>🎯 Next Steps</h3>\n";
echo "<ol>\n";
echo "<li>Test each page in your browser</li>\n";
echo "<li>Check for any remaining errors in browser console</li>\n";
echo "<li>Clear PHP OpCache if you're using it</li>\n";
echo "<li>Restart your web server if needed</li>\n";
echo "<li>Check error logs for any issues</li>\n";
echo "</ol>\n";

echo "<h3>🔗 Quick Test Links</h3>\n";
echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 8px;'>\n";
foreach ($phpFiles as $file) {
    if (file_exists($file)) {
        echo "<a href='$file' target='_blank' style='margin-right: 10px; color: #1976d2;'>$file</a><br>\n";
    }
}
echo "</div>\n";

?>

<style>
body { 
    font-family: Arial, sans-serif; 
    margin: 20px; 
    background: #f5f5f5;
}
h2 { 
    color: #2c3e50; 
    background: white;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
h3 { 
    color: #3498db; 
    margin-top: 20px;
}
h4 {
    color: #e67e22;
}
ul, ol { 
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
a {
    text-decoration: none;
    padding: 5px 10px;
    background: white;
    border-radius: 4px;
    display: inline-block;
    margin: 2px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}
a:hover {
    background: #f0f0f0;
}
</style>
