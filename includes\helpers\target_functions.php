<?php
/**
 * Get nearest targets for the current user
 * @param int $limit Number of targets to return
 * @return array
 */
function getTargetTerdekat($limit = 3) {
    global $pdo;
    try {
        $stmt = $pdo->prepare("
            SELECT 
                nama as nama_target,
                jumlah_target as target_jumlah,
                jumlah_terkumpul as terkumpul,
                tanggal_selesai
            FROM target
            WHERE user_id = ? AND status = 'aktif'
            ORDER BY tanggal_selesai ASC
            LIMIT ?
        ");
        $stmt->execute([$_SESSION['user_id'], $limit]);
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        error_log("Error getting nearest targets: " . $e->getMessage());
        return [];
    }
}

/**
 * Get target progress percentage
 * @param float $collected Amount collected
 * @param float $target Target amount
 * @return float
 */
function getTargetProgress($collected, $target) {
    if ($target <= 0) return 0;
    return min(100, ($collected / $target) * 100);
}

/**
 * Get days remaining until target completion
 * @param string $endDate Target end date
 * @return int
 */
function getDaysRemaining($endDate) {
    $end = new DateTime($endDate);
    $now = new DateTime();
    $diff = $now->diff($end);
    return $diff->days;
}

/**
 * Check if target is completed
 * @param float $collected Amount collected
 * @param float $target Target amount
 * @return bool
 */
function isTargetCompleted($collected, $target) {
    return $collected >= $target;
}

/**
 * Get all active targets for the current user
 * @return array
 */
function getActiveTargets() {
    global $pdo;
    try {
        $stmt = $pdo->prepare("
            SELECT *
            FROM target
            WHERE user_id = ? AND status = 'aktif'
            ORDER BY tanggal_selesai ASC
        ");
        $stmt->execute([$_SESSION['user_id']]);
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        error_log("Error getting active targets: " . $e->getMessage());
        return [];
    }
}

/**
 * Get target details by ID
 * @param int $targetId Target ID
 * @return array|null
 */
function getTargetById($targetId) {
    global $pdo;
    try {
        $stmt = $pdo->prepare("
            SELECT *
            FROM target
            WHERE id = ? AND user_id = ?
        ");
        $stmt->execute([$targetId, $_SESSION['user_id']]);
        return $stmt->fetch();
    } catch (PDOException $e) {
        error_log("Error getting target details: " . $e->getMessage());
        return null;
    }
}

/**
 * Update target progress
 * @param int $targetId Target ID
 * @param float $amount Amount to add
 * @return bool
 */
function updateTargetProgress($targetId, $amount) {
    global $pdo;
    try {
        $stmt = $pdo->prepare("
            UPDATE target
            SET jumlah_terkumpul = jumlah_terkumpul + ?,
                status = CASE 
                    WHEN jumlah_terkumpul + ? >= jumlah_target THEN 'selesai'
                    ELSE status
                END
            WHERE id = ? AND user_id = ?
        ");
        return $stmt->execute([$amount, $amount, $targetId, $_SESSION['user_id']]);
    } catch (PDOException $e) {
        error_log("Error updating target progress: " . $e->getMessage());
        return false;
    }
} 