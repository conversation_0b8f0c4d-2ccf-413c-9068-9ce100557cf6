<?php
require_once 'includes/config/database.php';
require_once 'includes/helpers/functions.php';
require_once 'includes/helpers/notifications.php';

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isLoggedIn()) {
    setFlashMessage('danger', 'Silakan login terlebih dahulu');
    redirect('/keuangan/login.php');
}

$currentUser = getCurrentUser();
$currentPage = 'kalender';

// Get current month and year
$currentMonth = isset($_GET['month']) ? (int)$_GET['month'] : date('n');
$currentYear = isset($_GET['year']) ? (int)$_GET['year'] : date('Y');

// Validate month and year
if ($currentMonth < 1 || $currentMonth > 12) {
    $currentMonth = date('n');
}
if ($currentYear < 2020 || $currentYear > 2030) {
    $currentYear = date('Y');
}

// Get transactions for the current month
$startDate = sprintf('%04d-%02d-01', $currentYear, $currentMonth);
$endDate = date('Y-m-t', strtotime($startDate));

try {
    $stmt = $pdo->prepare("
        SELECT 
            t.*,
            k.nama_kategori,
            k.tipe as kategori_tipe,
            DATE(t.tanggal_transaksi) as tanggal_only
        FROM transaksi t
        LEFT JOIN kategori k ON t.kategori_id = k.id
        WHERE t.user_id = ? AND t.tanggal_transaksi BETWEEN ? AND ?
        ORDER BY t.tanggal_transaksi ASC
    ");
    $stmt->execute([$currentUser['id'], $startDate, $endDate]);
    $transactions = $stmt->fetchAll();
} catch (PDOException $e) {
    $transactions = [];
}

// Group transactions by date
$transactionsByDate = [];
foreach ($transactions as $transaction) {
    $date = $transaction['tanggal_only'];
    if (!isset($transactionsByDate[$date])) {
        $transactionsByDate[$date] = [];
    }
    $transactionsByDate[$date][] = $transaction;
}

// Get monthly summary
try {
    $stmt = $pdo->prepare("
        SELECT 
            SUM(CASE WHEN k.tipe = 'pemasukan' THEN t.jumlah ELSE 0 END) as total_pemasukan,
            SUM(CASE WHEN k.tipe = 'pengeluaran' THEN t.jumlah ELSE 0 END) as total_pengeluaran,
            COUNT(*) as total_transaksi
        FROM transaksi t
        LEFT JOIN kategori k ON t.kategori_id = k.id
        WHERE t.user_id = ? AND t.tanggal_transaksi BETWEEN ? AND ?
    ");
    $stmt->execute([$currentUser['id'], $startDate, $endDate]);
    $monthlySummary = $stmt->fetch();
} catch (PDOException $e) {
    $monthlySummary = ['total_pemasukan' => 0, 'total_pengeluaran' => 0, 'total_transaksi' => 0];
}

// Calendar helper functions
function getFirstDayOfMonth($year, $month) {
    return date('w', mktime(0, 0, 0, $month, 1, $year));
}

function getDaysInMonth($year, $month) {
    return date('t', mktime(0, 0, 0, $month, 1, $year));
}

function getMonthName($month) {
    $months = [
        1 => 'Januari', 2 => 'Februari', 3 => 'Maret', 4 => 'April',
        5 => 'Mei', 6 => 'Juni', 7 => 'Juli', 8 => 'Agustus',
        9 => 'September', 10 => 'Oktober', 11 => 'November', 12 => 'Desember'
    ];
    return $months[$month];
}

// Include header
include 'includes/views/layouts/header.php';
?>

<div class="container-fluid py-4">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-1">Kalender Keuangan</h1>
            <p class="text-muted mb-0">Lihat transaksi keuangan dalam tampilan kalender</p>
        </div>
        <div class="d-flex align-items-center">
            <!-- Month Navigation -->
            <div class="btn-group me-3">
                <a href="?month=<?= $currentMonth == 1 ? 12 : $currentMonth - 1 ?>&year=<?= $currentMonth == 1 ? $currentYear - 1 : $currentYear ?>" class="btn btn-outline-primary">
                    <i class="fas fa-chevron-left"></i>
                </a>
                <button class="btn btn-primary" disabled>
                    <?= getMonthName($currentMonth) ?> <?= $currentYear ?>
                </button>
                <a href="?month=<?= $currentMonth == 12 ? 1 : $currentMonth + 1 ?>&year=<?= $currentMonth == 12 ? $currentYear + 1 : $currentYear ?>" class="btn btn-outline-primary">
                    <i class="fas fa-chevron-right"></i>
                </a>
            </div>
            <a href="?month=<?= date('n') ?>&year=<?= date('Y') ?>" class="btn btn-light">
                <i class="fas fa-home me-2"></i>Hari Ini
            </a>
        </div>
    </div>

    <!-- Monthly Summary -->
    <div class="row g-3 mb-4">
        <div class="col-md-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted mb-1">Total Pemasukan</h6>
                            <h4 class="mb-0 text-success"><?= formatRupiah($monthlySummary['total_pemasukan'] ?? 0) ?></h4>
                        </div>
                        <div class="bg-success bg-opacity-10 p-3 rounded">
                            <i class="fas fa-arrow-up text-success"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted mb-1">Total Pengeluaran</h6>
                            <h4 class="mb-0 text-danger"><?= formatRupiah($monthlySummary['total_pengeluaran'] ?? 0) ?></h4>
                        </div>
                        <div class="bg-danger bg-opacity-10 p-3 rounded">
                            <i class="fas fa-arrow-down text-danger"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted mb-1">Saldo Bersih</h6>
                            <?php 
                            $saldoBersih = ($monthlySummary['total_pemasukan'] ?? 0) - ($monthlySummary['total_pengeluaran'] ?? 0);
                            ?>
                            <h4 class="mb-0 text-<?= $saldoBersih >= 0 ? 'primary' : 'warning' ?>"><?= formatRupiah($saldoBersih) ?></h4>
                        </div>
                        <div class="bg-primary bg-opacity-10 p-3 rounded">
                            <i class="fas fa-wallet text-primary"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted mb-1">Total Transaksi</h6>
                            <h4 class="mb-0 text-info"><?= $monthlySummary['total_transaksi'] ?? 0 ?></h4>
                        </div>
                        <div class="bg-info bg-opacity-10 p-3 rounded">
                            <i class="fas fa-list text-info"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Calendar -->
    <div class="card border-0 shadow-sm">
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-bordered mb-0 calendar-table">
                    <thead class="table-light">
                        <tr>
                            <th class="text-center py-3">Minggu</th>
                            <th class="text-center py-3">Senin</th>
                            <th class="text-center py-3">Selasa</th>
                            <th class="text-center py-3">Rabu</th>
                            <th class="text-center py-3">Kamis</th>
                            <th class="text-center py-3">Jumat</th>
                            <th class="text-center py-3">Sabtu</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        $firstDay = getFirstDayOfMonth($currentYear, $currentMonth);
                        $daysInMonth = getDaysInMonth($currentYear, $currentMonth);
                        $currentDay = 1;
                        $today = date('Y-m-d');
                        
                        // Calculate number of weeks needed
                        $totalCells = $firstDay + $daysInMonth;
                        $weeks = ceil($totalCells / 7);
                        
                        for ($week = 0; $week < $weeks; $week++):
                        ?>
                        <tr>
                            <?php for ($dayOfWeek = 0; $dayOfWeek < 7; $dayOfWeek++): ?>
                            <td class="calendar-cell p-0" style="height: 120px; width: 14.28%; vertical-align: top;">
                                <?php
                                $cellDate = '';
                                $isCurrentMonth = false;
                                
                                if ($week == 0 && $dayOfWeek < $firstDay) {
                                    // Previous month days
                                    $prevMonth = $currentMonth == 1 ? 12 : $currentMonth - 1;
                                    $prevYear = $currentMonth == 1 ? $currentYear - 1 : $currentYear;
                                    $prevMonthDays = getDaysInMonth($prevYear, $prevMonth);
                                    $day = $prevMonthDays - ($firstDay - $dayOfWeek - 1);
                                    $cellDate = sprintf('%04d-%02d-%02d', $prevYear, $prevMonth, $day);
                                } elseif ($currentDay <= $daysInMonth) {
                                    // Current month days
                                    $day = $currentDay;
                                    $cellDate = sprintf('%04d-%02d-%02d', $currentYear, $currentMonth, $day);
                                    $isCurrentMonth = true;
                                    $currentDay++;
                                } else {
                                    // Next month days
                                    $nextMonth = $currentMonth == 12 ? 1 : $currentMonth + 1;
                                    $nextYear = $currentMonth == 12 ? $currentYear + 1 : $currentYear;
                                    $day = $currentDay - $daysInMonth;
                                    $cellDate = sprintf('%04d-%02d-%02d', $nextYear, $nextMonth, $day);
                                    $currentDay++;
                                }
                                
                                $isToday = $cellDate === $today;
                                $dayTransactions = isset($transactionsByDate[$cellDate]) ? $transactionsByDate[$cellDate] : [];
                                ?>
                                
                                <div class="h-100 p-2 <?= $isToday ? 'bg-primary bg-opacity-10' : '' ?> <?= !$isCurrentMonth ? 'text-muted' : '' ?>">
                                    <div class="d-flex justify-content-between align-items-start mb-1">
                                        <span class="fw-bold <?= $isToday ? 'text-primary' : '' ?>"><?= $day ?></span>
                                        <?php if ($isToday): ?>
                                        <span class="badge bg-primary">Hari Ini</span>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <?php if (!empty($dayTransactions) && $isCurrentMonth): ?>
                                    <div class="transactions-list">
                                        <?php 
                                        $totalPemasukan = 0;
                                        $totalPengeluaran = 0;
                                        foreach ($dayTransactions as $transaction) {
                                            if ($transaction['kategori_tipe'] === 'pemasukan') {
                                                $totalPemasukan += $transaction['jumlah'];
                                            } else {
                                                $totalPengeluaran += $transaction['jumlah'];
                                            }
                                        }
                                        ?>
                                        
                                        <?php if ($totalPemasukan > 0): ?>
                                        <div class="transaction-summary mb-1">
                                            <small class="text-success">
                                                <i class="fas fa-arrow-up"></i>
                                                <?= formatRupiahShort($totalPemasukan) ?>
                                            </small>
                                        </div>
                                        <?php endif; ?>
                                        
                                        <?php if ($totalPengeluaran > 0): ?>
                                        <div class="transaction-summary mb-1">
                                            <small class="text-danger">
                                                <i class="fas fa-arrow-down"></i>
                                                <?= formatRupiahShort($totalPengeluaran) ?>
                                            </small>
                                        </div>
                                        <?php endif; ?>
                                        
                                        <div class="transaction-count">
                                            <small class="text-muted">
                                                <i class="fas fa-list"></i>
                                                <?= count($dayTransactions) ?> transaksi
                                            </small>
                                        </div>
                                    </div>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <?php endfor; ?>
                        </tr>
                        <?php endfor; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Legend -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <h6 class="mb-3">Keterangan:</h6>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-arrow-up text-success me-2"></i>
                                <span>Pemasukan</span>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-arrow-down text-danger me-2"></i>
                                <span>Pengeluaran</span>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-list text-muted me-2"></i>
                                <span>Jumlah Transaksi</span>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="d-flex align-items-center mb-2">
                                <span class="badge bg-primary me-2">Hari Ini</span>
                                <span>Tanggal Hari Ini</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.calendar-table {
    font-size: 0.875rem;
}

.calendar-cell {
    border: 1px solid #dee2e6 !important;
    position: relative;
}

.calendar-cell:hover {
    background-color: #f8f9fa;
}

.transaction-summary {
    font-size: 0.75rem;
    line-height: 1.2;
}

.transaction-count {
    font-size: 0.7rem;
}

.transactions-list {
    max-height: 80px;
    overflow-y: auto;
}

@media (max-width: 768px) {
    .calendar-table {
        font-size: 0.75rem;
    }
    
    .calendar-cell {
        height: 80px !important;
    }
    
    .transaction-summary,
    .transaction-count {
        font-size: 0.65rem;
    }
}
</style>

<?php
// Helper function for short rupiah format
function formatRupiahShort($amount) {
    if ($amount >= 1000000000) {
        return 'Rp ' . number_format($amount / 1000000000, 1) . 'M';
    } elseif ($amount >= 1000000) {
        return 'Rp ' . number_format($amount / 1000000, 1) . 'jt';
    } elseif ($amount >= 1000) {
        return 'Rp ' . number_format($amount / 1000, 0) . 'rb';
    } else {
        return 'Rp ' . number_format($amount, 0);
    }
}
?>

<?php include 'includes/views/layouts/footer.php'; ?>
