/**
 * Modern UI Framework JavaScript
 * Provides interactive functionality for modern UI components
 */

class ModernUI {
    constructor() {
        this.init();
    }

    init() {
        this.initAnimations();
        this.initNumberFormatting();
        this.initFormValidation();
        this.initTooltips();
        this.initModals();
        this.initAlerts();
    }

    // Initialize animations
    initAnimations() {
        // Add fade-in animation to cards when they come into view
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('modern-fade-in');
                }
            });
        }, {
            threshold: 0.1
        });

        // Observe all modern cards
        document.querySelectorAll('.modern-card, .modern-stats-card').forEach(card => {
            observer.observe(card);
        });
    }

    // Initialize number formatting for currency inputs
    initNumberFormatting() {
        document.addEventListener('input', (e) => {
            if (e.target.classList.contains('number-format')) {
                this.formatNumberInput(e.target);
            }
        });
    }

    formatNumberInput(input) {
        let value = input.value.replace(/[^\d]/g, '');
        if (value) {
            input.value = new Intl.NumberFormat('id-ID').format(value);
        }
    }

    // Initialize form validation
    initFormValidation() {
        document.addEventListener('submit', (e) => {
            const form = e.target;
            if (form.classList.contains('needs-validation')) {
                if (!form.checkValidity()) {
                    e.preventDefault();
                    e.stopPropagation();
                }
                form.classList.add('was-validated');
            }
        });
    }

    // Initialize tooltips
    initTooltips() {
        // Simple tooltip implementation
        document.querySelectorAll('[title]').forEach(element => {
            element.addEventListener('mouseenter', (e) => {
                this.showTooltip(e.target);
            });
            element.addEventListener('mouseleave', (e) => {
                this.hideTooltip();
            });
        });
    }

    showTooltip(element) {
        const tooltip = document.createElement('div');
        tooltip.className = 'modern-tooltip';
        tooltip.textContent = element.getAttribute('title');
        
        // Remove title to prevent default tooltip
        element.setAttribute('data-original-title', element.getAttribute('title'));
        element.removeAttribute('title');
        
        document.body.appendChild(tooltip);
        
        const rect = element.getBoundingClientRect();
        tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
        tooltip.style.top = rect.top - tooltip.offsetHeight - 8 + 'px';
        
        setTimeout(() => tooltip.classList.add('show'), 10);
    }

    hideTooltip() {
        const tooltip = document.querySelector('.modern-tooltip');
        if (tooltip) {
            tooltip.remove();
        }
        
        // Restore original title
        document.querySelectorAll('[data-original-title]').forEach(element => {
            element.setAttribute('title', element.getAttribute('data-original-title'));
            element.removeAttribute('data-original-title');
        });
    }

    // Initialize modals
    initModals() {
        // Auto-focus first input when modal opens
        document.addEventListener('shown.bs.modal', (e) => {
            const modal = e.target;
            const firstInput = modal.querySelector('input:not([type="hidden"]), select, textarea');
            if (firstInput) {
                firstInput.focus();
            }
        });

        // Clear form when modal closes
        document.addEventListener('hidden.bs.modal', (e) => {
            const modal = e.target;
            const form = modal.querySelector('form');
            if (form) {
                form.reset();
                form.classList.remove('was-validated');
            }
        });
    }

    // Initialize alerts
    initAlerts() {
        // Auto-hide alerts after 5 seconds
        document.querySelectorAll('.modern-alert').forEach(alert => {
            setTimeout(() => {
                this.fadeOutAlert(alert);
            }, 5000);
        });
    }

    fadeOutAlert(alert) {
        alert.style.opacity = '0';
        alert.style.transform = 'translateY(-10px)';
        setTimeout(() => {
            if (alert.parentNode) {
                alert.parentNode.removeChild(alert);
            }
        }, 300);
    }

    // Utility functions
    static formatCurrency(amount) {
        return new Intl.NumberFormat('id-ID', {
            style: 'currency',
            currency: 'IDR',
            minimumFractionDigits: 0
        }).format(amount);
    }

    static formatNumber(number) {
        return new Intl.NumberFormat('id-ID').format(number);
    }

    static formatDate(date, options = {}) {
        const defaultOptions = {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        };
        return new Intl.DateTimeFormat('id-ID', { ...defaultOptions, ...options }).format(new Date(date));
    }

    // Show loading state
    static showLoading(element) {
        element.disabled = true;
        const originalText = element.textContent;
        element.setAttribute('data-original-text', originalText);
        element.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Loading...';
    }

    // Hide loading state
    static hideLoading(element) {
        element.disabled = false;
        const originalText = element.getAttribute('data-original-text');
        if (originalText) {
            element.textContent = originalText;
            element.removeAttribute('data-original-text');
        }
    }

    // Show notification
    static showNotification(message, type = 'info', duration = 3000) {
        const notification = document.createElement('div');
        notification.className = `modern-notification modern-notification-${type}`;
        notification.innerHTML = `
            <div class="modern-notification-content">
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-triangle' : 'info-circle'}"></i>
                <span>${message}</span>
            </div>
            <button class="modern-notification-close" onclick="this.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        `;

        // Add to page
        let container = document.querySelector('.modern-notifications');
        if (!container) {
            container = document.createElement('div');
            container.className = 'modern-notifications';
            document.body.appendChild(container);
        }
        
        container.appendChild(notification);

        // Auto-remove after duration
        setTimeout(() => {
            if (notification.parentNode) {
                notification.style.opacity = '0';
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }
        }, duration);
    }

    // Confirm dialog
    static confirm(message, title = 'Konfirmasi') {
        return new Promise((resolve) => {
            const modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.innerHTML = `
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modern-modal-content">
                        <div class="modern-modal-header">
                            <h5 class="modern-modal-title">
                                <i class="fas fa-question-circle modern-text-warning"></i>
                                ${title}
                            </h5>
                        </div>
                        <div class="modern-modal-body">
                            <p>${message}</p>
                        </div>
                        <div class="modern-modal-footer">
                            <button type="button" class="modern-btn modern-btn-light" data-bs-dismiss="modal">
                                <i class="fas fa-times"></i>
                                Batal
                            </button>
                            <button type="button" class="modern-btn modern-btn-primary confirm-btn">
                                <i class="fas fa-check"></i>
                                Ya, Lanjutkan
                            </button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            const bootstrapModal = new bootstrap.Modal(modal);
            
            modal.querySelector('.confirm-btn').addEventListener('click', () => {
                resolve(true);
                bootstrapModal.hide();
            });

            modal.addEventListener('hidden.bs.modal', () => {
                resolve(false);
                document.body.removeChild(modal);
            });

            bootstrapModal.show();
        });
    }
}

// CSS for additional components
const additionalCSS = `
.modern-tooltip {
    position: absolute;
    z-index: 1000;
    padding: 8px 12px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    font-size: 12px;
    border-radius: 4px;
    opacity: 0;
    transition: opacity 0.2s;
    pointer-events: none;
}

.modern-tooltip.show {
    opacity: 1;
}

.modern-notifications {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1050;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.modern-notification {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-left: 4px solid;
    min-width: 300px;
    transform: translateX(100%);
    transition: all 0.3s ease;
    animation: slideInRight 0.3s ease forwards;
}

@keyframes slideInRight {
    to {
        transform: translateX(0);
    }
}

.modern-notification-success { border-left-color: #10b981; }
.modern-notification-error { border-left-color: #ef4444; }
.modern-notification-info { border-left-color: #06b6d4; }
.modern-notification-warning { border-left-color: #f59e0b; }

.modern-notification-content {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;
}

.modern-notification-close {
    background: none;
    border: none;
    padding: 4px;
    cursor: pointer;
    border-radius: 4px;
    transition: background 0.2s;
}

.modern-notification-close:hover {
    background: rgba(0, 0, 0, 0.1);
}
`;

// Inject additional CSS
const style = document.createElement('style');
style.textContent = additionalCSS;
document.head.appendChild(style);

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.modernUI = new ModernUI();
});

// Export for global use
window.ModernUI = ModernUI;
