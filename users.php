<?php
require_once 'includes/config/database.php';
require_once 'includes/helpers/function_check.php';
require_once 'includes/helpers/functions.php';
require_once 'includes/controllers/AuthController.php';

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isLoggedIn()) {
    setFlashMessage('danger', 'Silakan login terlebih dahulu');
    redirect('login.php');
}

$currentUser = getCurrentUser();
$currentPage = 'users';
$error = '';
$success = '';

// Check if user is admin
if ($currentUser['role'] !== 'admin') {
    setFlashMessage('danger', '<PERSON><PERSON><PERSON> ditolak');
    redirect('dashboard.php');
}

// Handle user actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['add_user'])) {
        $data = [
            'nama' => sanitizeInput($_POST['nama']),
            'email' => sanitizeInput($_POST['email']),
            'password' => $_POST['password'],
            'role' => sanitizeInput($_POST['role'])
        ];
        
        // Validate input
        $errors = validateFormData($data, [
            'nama' => 'required',
            'email' => 'required|email',
            'password' => 'required|min:6',
            'role' => 'required'
        ]);
        
        if (empty($errors)) {
            try {
                // Check if email already exists
                $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ?");
                $stmt->execute([$data['email']]);
                if ($stmt->fetch()) {
                    $error = 'Email sudah digunakan';
                } else {
                    // Create new user
                    $hashedPassword = password_hash($data['password'], PASSWORD_DEFAULT);
                    // Use email as username for simplicity based on current form
                    $username = $data['email']; // Or you might want to extract part of the email
                    $stmt = $pdo->prepare("INSERT INTO users (username, nama, nama_lengkap, email, password, role) VALUES (?, ?, ?, ?, ?, ?)");
                    $stmt->execute([$username, $data['nama'], $data['nama'], $data['email'], $hashedPassword, $data['role']]);
                    
                    // Log activity
                    logActivity($currentUser['id'], 'Admin created new user: ' . $data['email']);
                    
                    $success = 'User berhasil ditambahkan';
                }
            } catch (PDOException $e) {
                setFlashMessage('danger', 'Database Error (Add User): ' . $e->getMessage());
                error_log("Error adding user: " . $e->getMessage());
            }
        } else {
            setFlashMessage('danger', 'Validasi gagal (Add User): ' . implode(', ', $errors));
        }
    } elseif (isset($_POST['edit_user'])) {
        $userId = (int)$_POST['user_id'];
        $data = [
            'nama' => sanitizeInput($_POST['nama']),
            'email' => sanitizeInput($_POST['email']),
            'role' => sanitizeInput($_POST['role'])
        ];
        
        // Validate input
        $errors = validateFormData($data, [
            'nama' => 'required',
            'email' => 'required|email',
            'role' => 'required'
        ]);
        
        if (empty($errors)) {
            try {
                // Check if email is used by another user
                $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ? AND id != ?");
                $stmt->execute([$data['email'], $userId]);
                if ($stmt->fetch()) {
                    $error = 'Email sudah digunakan oleh user lain';
                } else {
                    // Update user
                    $stmt = $pdo->prepare("UPDATE users SET nama = ?, email = ?, role = ? WHERE id = ?");
                    $stmt->execute([$data['nama'], $data['email'], $data['role'], $userId]);
                    
                    // Log activity
                    logActivity($currentUser['id'], 'Admin updated user: ' . $data['email']);
                    
                    $success = 'User berhasil diperbarui';
                }
            } catch (PDOException $e) {
                setFlashMessage('danger', 'Database Error (Edit User): ' . $e->getMessage());
                error_log("Error updating user: " . $e->getMessage());
            }
        } else {
            setFlashMessage('danger', 'Validasi gagal (Edit User): ' . implode(', ', $errors));
        }
    } elseif (isset($_POST['delete_user'])) {
        $userId = (int)$_POST['user_id'];
        
        try {
            // Get user email for logging
            $stmt = $pdo->prepare("SELECT email FROM users WHERE id = ?");
            $stmt->execute([$userId]);
            $user = $stmt->fetch();
            
            // Delete user
            $stmt = $pdo->prepare("DELETE FROM users WHERE id = ?");
            $stmt->execute([$userId]);
            
            // Log activity
            logActivity($currentUser['id'], 'Admin deleted user: ' . $user['email']);
            
            $success = 'User berhasil dihapus';
        } catch (PDOException $e) {
            setFlashMessage('danger', 'Database Error (Delete User): ' . $e->getMessage());
            error_log("Error deleting user: " . $e->getMessage());
        }
    } elseif (isset($_POST['reset_password'])) {
        $userId = (int)$_POST['user_id'];
        $defaultPassword = '12345678';
        
        try {
            // Get user email for logging
            $stmt = $pdo->prepare("SELECT email FROM users WHERE id = ?");
            $stmt->execute([$userId]);
            $user = $stmt->fetch();
            
            if (!$user) {
                setFlashMessage('danger', 'User tidak ditemukan');
                redirect('users.php');
                exit;
            }
            
            // Reset password
            $hashedPassword = password_hash($defaultPassword, PASSWORD_DEFAULT);
            $stmt = $pdo->prepare("UPDATE users SET password = ? WHERE id = ?");
            $result = $stmt->execute([$hashedPassword, $userId]);
            
            if ($result) {
                // Log activity
                logActivity($currentUser['id'], 'Admin reset password for user: ' . $user['email']);
                setFlashMessage('success', 'Password berhasil direset ke default (12345678)');
            } else {
                setFlashMessage('danger', 'Gagal mereset password');
            }
            redirect('users.php');
            exit;
        } catch (PDOException $e) {
            setFlashMessage('danger', 'Database Error (Reset Password): ' . $e->getMessage());
            error_log("Error resetting password: " . $e->getMessage());
            redirect('users.php');
            exit;
        }
    } elseif (isset($_POST['import_users'])) {
        try {
            if (!isset($_FILES['import_file']) || $_FILES['import_file']['error'] !== UPLOAD_ERR_OK) {
                throw new Exception('File tidak ditemukan atau error');
            }

            $file = $_FILES['import_file'];
            $ext = pathinfo($file['name'], PATHINFO_EXTENSION);
            
            if (!in_array($ext, ['xlsx', 'xls'])) {
                throw new Exception('Format file tidak didukung');
            }

            require 'vendor/autoload.php';
            $spreadsheet = \PhpOffice\PhpSpreadsheet\IOFactory::load($file['tmp_name']);
            $worksheet = $spreadsheet->getActiveSheet();
            $rows = $worksheet->toArray();

            // Skip header row
            array_shift($rows);
            
            $successCount = 0;
            $errorCount = 0;
            $errors = [];

            foreach ($rows as $row) {
                if (empty($row[0])) continue; // Skip empty rows
                
                $data = [
                    'nama' => sanitizeInput($row[0]),
                    'email' => sanitizeInput($row[1]),
                    'role' => sanitizeInput($row[2]),
                    'status' => sanitizeInput($row[3])
                ];

                // Validate data
                $validationErrors = validateFormData($data, [
                    'nama' => 'required',
                    'email' => 'required|email',
                    'role' => 'required|in:admin,user',
                    'status' => 'required|in:active,inactive'
                ]);

                if (!empty($validationErrors)) {
                    $errorCount++;
                    $errors[] = "Baris {$row[0]}: " . implode(', ', $validationErrors);
                    continue;
                }

                // Check if email exists
                $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ?");
                $stmt->execute([$data['email']]);
                if ($stmt->fetch()) {
                    $errorCount++;
                    $errors[] = "Email {$data['email']} sudah digunakan";
                    continue;
                }

                // Insert user
                $defaultPassword = password_hash('12345678', PASSWORD_DEFAULT);
                $stmt = $pdo->prepare("INSERT INTO users (nama, email, password, role, status) VALUES (?, ?, ?, ?, ?)");
                $stmt->execute([$data['nama'], $data['email'], $defaultPassword, $data['role'], $data['status']]);
                $successCount++;
            }

            if ($successCount > 0) {
                setFlashMessage('success', "Berhasil import {$successCount} user");
            }
            if ($errorCount > 0) {
                setFlashMessage('warning', "Gagal import {$errorCount} user: " . implode('; ', $errors));
            }

        } catch (Exception $e) {
            setFlashMessage('danger', 'Error: ' . $e->getMessage());
        }
        redirect('users.php');
    } elseif (isset($_POST['bulk_action'])) {
        $action = $_POST['bulk_action'];
        $userIds = $_POST['user_ids'] ?? [];
        
        if (empty($userIds)) {
            setFlashMessage('warning', 'Pilih user terlebih dahulu');
            redirect('users.php');
        }

        try {
            switch ($action) {
                case 'activate':
                    $stmt = $pdo->prepare("UPDATE users SET status = 'active' WHERE id IN (" . implode(',', array_fill(0, count($userIds), '?')) . ")");
                    $stmt->execute($userIds);
                    setFlashMessage('success', 'User berhasil diaktifkan');
                    break;

                case 'deactivate':
                    $stmt = $pdo->prepare("UPDATE users SET status = 'inactive' WHERE id IN (" . implode(',', array_fill(0, count($userIds), '?')) . ")");
                    $stmt->execute($userIds);
                    setFlashMessage('success', 'User berhasil dinonaktifkan');
                    break;

                case 'delete':
                    $stmt = $pdo->prepare("DELETE FROM users WHERE id IN (" . implode(',', array_fill(0, count($userIds), '?')) . ")");
                    $stmt->execute($userIds);
                    setFlashMessage('success', 'User berhasil dihapus');
                    break;

                case 'reset_password':
                    $defaultPassword = password_hash('12345678', PASSWORD_DEFAULT);
                    $stmt = $pdo->prepare("UPDATE users SET password = ? WHERE id IN (" . implode(',', array_fill(0, count($userIds), '?')) . ")");
                    $stmt->execute(array_merge([$defaultPassword], $userIds));
                    setFlashMessage('success', 'Password user berhasil direset');
                    break;
            }
        } catch (PDOException $e) {
            setFlashMessage('danger', 'Database Error: ' . $e->getMessage());
        }
        redirect('users.php');
    }
}

// Get users with pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$perPage = 10;
$offset = ($page - 1) * $perPage;

// Modifikasi query untuk mendukung filter
$where = [];
$params = [];

if (!empty($_GET['search'])) {
    $where[] = "(nama LIKE ? OR email LIKE ?)";
    $search = "%{$_GET['search']}%";
    $params[] = $search;
    $params[] = $search;
}

if (!empty($_GET['role'])) {
    $where[] = "role = ?";
    $params[] = $_GET['role'];
}

if (!empty($_GET['status'])) {
    $where[] = "status = ?";
    $params[] = $_GET['status'];
}

$whereClause = !empty($where) ? "WHERE " . implode(" AND ", $where) : "";

// Get total records with filter
$stmt = $pdo->prepare("SELECT COUNT(*) FROM users $whereClause");
$stmt->execute($params);
$totalRecords = $stmt->fetchColumn();
$totalPages = ceil($totalRecords / $perPage);

// Get users with filter
$stmt = $pdo->prepare("
    SELECT * FROM users 
    $whereClause
    ORDER BY created_at DESC 
    LIMIT ? OFFSET ?
");
$params[] = $perPage;
$params[] = $offset;
$stmt->execute($params);
$users = $stmt->fetchAll();

// Include header
include 'includes/views/layouts/header.php';
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-1">Kelola User</h1>
            <p class="text-muted mb-0">Kelola pengguna dan hak akses sistem</p>
        </div>
        <div class="d-flex gap-2">
            <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#importUserModal">
                <i class="fas fa-file-import me-2"></i>Import User
            </button>
            <button type="button" class="btn btn-info" onclick="exportUsers()">
                <i class="fas fa-file-export me-2"></i>Export User
            </button>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addUserModal">
                <i class="fas fa-plus me-2"></i>Tambah User
            </button>
        </div>
    </div>

    <!-- Tambahkan filter dan pencarian -->
    <div class="card mb-4">
        <div class="card-body">
            <form action="" method="GET" class="row g-3">
                <div class="col-md-4">
                    <label class="form-label">Cari</label>
                    <input type="text" name="search" class="form-control" placeholder="Cari nama atau email..." 
                           value="<?= $_GET['search'] ?? '' ?>">
                </div>
                <div class="col-md-3">
                    <label class="form-label">Role</label>
                    <select name="role" class="form-select">
                        <option value="">Semua Role</option>
                        <option value="admin" <?= (isset($_GET['role']) && $_GET['role'] === 'admin') ? 'selected' : '' ?>>Admin</option>
                        <option value="user" <?= (isset($_GET['role']) && $_GET['role'] === 'user') ? 'selected' : '' ?>>User</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">Status</label>
                    <select name="status" class="form-select">
                        <option value="">Semua Status</option>
                        <option value="active" <?= (isset($_GET['status']) && $_GET['status'] === 'active') ? 'selected' : '' ?>>Aktif</option>
                        <option value="inactive" <?= (isset($_GET['status']) && $_GET['status'] === 'inactive') ? 'selected' : '' ?>>Tidak Aktif</option>
                    </select>
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search me-2"></i>Filter
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Tambahkan statistik user -->
    <div class="row g-3 mb-4">
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted mb-1">Total User</h6>
                            <h3 class="mb-0"><?= $totalRecords ?></h3>
                        </div>
                        <div class="bg-primary bg-opacity-10 p-3 rounded">
                            <i class="fas fa-users text-primary"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted mb-1">Admin</h6>
                            <h3 class="mb-0"><?= count(array_filter($users, fn($u) => $u['role'] === 'admin')) ?></h3>
                        </div>
                        <div class="bg-danger bg-opacity-10 p-3 rounded">
                            <i class="fas fa-user-shield text-danger"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted mb-1">User Aktif</h6>
                            <h3 class="mb-0"><?= count(array_filter($users, fn($u) => $u['status'] === 'active')) ?></h3>
                        </div>
                        <div class="bg-success bg-opacity-10 p-3 rounded">
                            <i class="fas fa-user-check text-success"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted mb-1">User Baru</h6>
                            <h3 class="mb-0"><?= count(array_filter($users, fn($u) => strtotime($u['created_at']) > strtotime('-7 days'))) ?></h3>
                        </div>
                        <div class="bg-info bg-opacity-10 p-3 rounded">
                            <i class="fas fa-user-plus text-info"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php if ($error): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <?= htmlspecialchars($error) ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    <?php endif; ?>

    <?php if ($success): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <?= htmlspecialchars($success) ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    <?php endif; ?>

    <?php if (isset($_SESSION['flash_message'])): ?>
    <div class="alert alert-<?= $_SESSION['flash_message']['type'] ?> alert-dismissible fade show" role="alert">
        <?= htmlspecialchars($_SESSION['flash_message']['message']) ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    <?php 
    // Clear flash message after displaying
    unset($_SESSION['flash_message']);
    endif; ?>

    <div class="card mb-4">
        <div class="card-body">
            <form method="POST" id="bulkActionForm">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <div class="d-flex gap-2">
                        <select name="bulk_action" class="form-select" style="width: auto;">
                            <option value="">Pilih Aksi</option>
                            <option value="activate">Aktifkan</option>
                            <option value="deactivate">Nonaktifkan</option>
                            <option value="reset_password">Reset Password</option>
                            <option value="delete">Hapus</option>
                        </select>
                        <button type="submit" class="btn btn-primary" id="bulkActionBtn" disabled>
                            Terapkan
                        </button>
                    </div>
                    <div class="text-muted">
                        <span id="selectedCount">0</span> user dipilih
                    </div>
                </div>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th width="40">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="selectAll">
                                    </div>
                                </th>
                                <th>Nama</th>
                                <th>Email</th>
                                <th>Role</th>
                                <th>Status</th>
                                <th>Tanggal Dibuat</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (empty($users)): ?>
                            <tr>
                                <td colspan="7" class="text-center">Tidak ada data</td>
                            </tr>
                            <?php else: ?>
                            <?php foreach ($users as $user): ?>
                            <tr>
                                <td>
                                    <div class="form-check">
                                        <input class="form-check-input user-checkbox" type="checkbox" 
                                               name="user_ids[]" value="<?= $user['id'] ?>">
                                    </div>
                                </td>
                                <td><?= htmlspecialchars($user['nama']) ?></td>
                                <td><?= htmlspecialchars($user['email']) ?></td>
                                <td>
                                    <span class="badge bg-<?= $user['role'] === 'admin' ? 'danger' : 'primary' ?>">
                                        <?= ucfirst($user['role']) ?>
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-<?= $user['status'] === 'active' ? 'success' : 'warning' ?>">
                                        <?= ucfirst($user['status']) ?>
                                    </span>
                                </td>
                                <td><?= date('d/m/Y H:i', strtotime($user['created_at'])) ?></td>
                                <td>
                                    <div class="btn-group">
                                        <button type="button" class="btn btn-sm btn-info" 
                                                data-bs-toggle="modal" 
                                                data-bs-target="#editUserModal"
                                                data-user='<?= htmlspecialchars(json_encode($user)) ?>'>
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-warning" 
                                                data-bs-toggle="modal" 
                                                data-bs-target="#resetPasswordModal"
                                                data-user-id="<?= $user['id'] ?>"
                                                data-user-name="<?= htmlspecialchars($user['nama']) ?>">
                                            <i class="fas fa-key"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-danger" 
                                                data-bs-toggle="modal" 
                                                data-bs-target="#deleteUserModal"
                                                data-user-id="<?= $user['id'] ?>"
                                                data-user-name="<?= htmlspecialchars($user['nama']) ?>">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </form>
        </div>
    </div>

    <?php if ($totalPages > 1): ?>
    <nav class="mt-4">
        <ul class="pagination justify-content-center">
            <?php for ($i = 1; $i <= $totalPages; $i++): ?>
            <li class="page-item <?= $i === $page ? 'active' : '' ?>">
                <a class="page-link" href="?page=<?= $i ?>"><?= $i ?></a>
            </li>
            <?php endfor; ?>
        </ul>
    </nav>
    <?php endif; ?>
</div>

<!-- Add User Modal -->
<div class="modal fade" id="addUserModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST" class="needs-validation" novalidate>
                <div class="modal-header">
                    <h5 class="modal-title">Tambah User</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="nama" class="form-label">Nama Lengkap</label>
                        <input type="text" class="form-control" id="nama" name="nama" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="email" class="form-label">Email</label>
                        <input type="email" class="form-control" id="email" name="email" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="password" class="form-label">Password</label>
                        <input type="password" class="form-control" id="password" name="password" required minlength="6">
                        <div class="form-text">Password minimal 6 karakter</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="role" class="form-label">Role</label>
                        <select class="form-select" id="role" name="role" required>
                            <option value="user">User</option>
                            <option value="admin">Admin</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" name="add_user" class="btn btn-primary">Simpan</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit User Modal -->
<div class="modal fade" id="editUserModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST" class="needs-validation" novalidate>
                <input type="hidden" name="user_id" id="edit_user_id">
                <div class="modal-header">
                    <h5 class="modal-title">Edit User</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="edit_nama" class="form-label">Nama Lengkap</label>
                        <input type="text" class="form-control" id="edit_nama" name="nama" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="edit_email" class="form-label">Email</label>
                        <input type="email" class="form-control" id="edit_email" name="email" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="edit_role" class="form-label">Role</label>
                        <select class="form-select" id="edit_role" name="role" required>
                            <option value="user">User</option>
                            <option value="admin">Admin</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" name="edit_user" class="btn btn-primary">Simpan</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete User Modal -->
<div class="modal fade" id="deleteUserModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST">
                <input type="hidden" name="user_id" id="delete_user_id">
                <div class="modal-header">
                    <h5 class="modal-title">Hapus User</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>Apakah Anda yakin ingin menghapus user <strong id="delete_user_name"></strong>?</p>
                    <p class="text-danger">Tindakan ini tidak dapat dibatalkan.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" name="delete_user" class="btn btn-danger">Hapus</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Reset Password Modal -->
<div class="modal fade" id="resetPasswordModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST" action="">
                <input type="hidden" name="user_id" id="resetPasswordUserId">
                <input type="hidden" name="reset_password" value="1">
                <div class="modal-header">
                    <h5 class="modal-title">Reset Password</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>Apakah Anda yakin ingin mereset password untuk user <strong id="resetPasswordUserName"></strong>?</p>
                    <p class="text-warning">Password akan direset ke default: <strong>12345678</strong></p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-warning">Reset Password</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Logout Confirmation Modal -->
<div class="modal fade" id="logoutModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Konfirmasi Logout</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Apakah Anda yakin ingin keluar dari sistem?</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                <a href="logout.php" class="btn btn-danger">Logout</a>
            </div>
        </div>
    </div>
</div>

<!-- Tambahkan modal import user -->
<div class="modal fade" id="importUserModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST" enctype="multipart/form-data">
                <div class="modal-header">
                    <h5 class="modal-title">Import User</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">File Excel</label>
                        <input type="file" class="form-control" name="import_file" accept=".xlsx,.xls" required>
                        <div class="form-text">Format file: .xlsx atau .xls</div>
                    </div>
                    <div class="alert alert-info">
                        <h6 class="alert-heading">Petunjuk Import:</h6>
                        <ol class="mb-0 small">
                            <li>Download template Excel terlebih dahulu</li>
                            <li>Isi data sesuai format yang ada</li>
                            <li>Upload file yang sudah diisi</li>
                        </ol>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <a href="templates/user_import_template.xlsx" class="btn btn-info">
                        <i class="fas fa-download me-2"></i>Download Template
                    </a>
                    <button type="submit" name="import_users" class="btn btn-primary">Import</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Form validation
(function() {
    'use strict';
    
    const forms = document.querySelectorAll('.needs-validation');
    
    Array.from(forms).forEach(form => {
        form.addEventListener('submit', event => {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        }, false);
    });
})();

// Edit user modal handler
document.getElementById('editUserModal').addEventListener('show.bs.modal', function(event) {
    const button = event.relatedTarget;
    const user = JSON.parse(button.getAttribute('data-user'));
    
    document.getElementById('edit_user_id').value = user.id;
    document.getElementById('edit_nama').value = user.nama;
    document.getElementById('edit_email').value = user.email;
    document.getElementById('edit_role').value = user.role;
});

// Delete user modal handler
document.getElementById('deleteUserModal').addEventListener('show.bs.modal', function(event) {
    const button = event.relatedTarget;
    const userId = button.getAttribute('data-user-id');
    const userName = button.getAttribute('data-user-name');
    
    document.getElementById('delete_user_id').value = userId;
    document.getElementById('delete_user_name').textContent = userName;
});

// Reset Password Modal Handler
document.addEventListener('DOMContentLoaded', function() {
    const resetPasswordModal = document.getElementById('resetPasswordModal');
    if (resetPasswordModal) {
        resetPasswordModal.addEventListener('show.bs.modal', function(event) {
            const button = event.relatedTarget;
            const userId = button.getAttribute('data-user-id');
            const userName = button.getAttribute('data-user-name');
            
            // Set the user ID and name in the modal
            document.getElementById('resetPasswordUserId').value = userId;
            document.getElementById('resetPasswordUserName').textContent = userName;
        });
    }
});

// Auto-dismiss alerts after 5 seconds
document.querySelectorAll('.alert').forEach(alert => {
    setTimeout(() => {
        const bsAlert = new bootstrap.Alert(alert);
        bsAlert.close();
    }, 5000);
});

// Logout Modal Handler
document.addEventListener('DOMContentLoaded', function() {
    // Get all logout links/buttons
    const logoutButtons = document.querySelectorAll('[data-bs-target="#logoutModal"]');
    
    // Add click event listener to each logout button
    logoutButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault(); // Prevent default link behavior
            const logoutModal = new bootstrap.Modal(document.getElementById('logoutModal'));
            logoutModal.show();
        });
    });
});

function exportUsers() {
    // Tampilkan loading
    Swal.fire({
        title: 'Mengekspor data...',
        text: 'Mohon tunggu sebentar',
        allowOutsideClick: false,
        didOpen: () => {
            Swal.showLoading();
        }
    });

    // Redirect ke halaman export
    window.location.href = 'export_users.php';
}

// Tambahkan event listener untuk form filter
document.querySelector('form[method="GET"]').addEventListener('submit', function(e) {
    const search = this.querySelector('[name="search"]').value.trim();
    const role = this.querySelector('[name="role"]').value;
    const status = this.querySelector('[name="status"]').value;
    
    if (!search && !role && !status) {
        e.preventDefault();
        window.location.href = 'users.php';
    }
});

document.addEventListener('DOMContentLoaded', function() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.user-checkbox');
    const bulkActionBtn = document.getElementById('bulkActionBtn');
    const selectedCount = document.getElementById('selectedCount');
    const bulkActionForm = document.getElementById('bulkActionForm');

    // Handle select all
    selectAll.addEventListener('change', function() {
        checkboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        updateSelectedCount();
    });

    // Handle individual checkboxes
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            updateSelectedCount();
            // Update select all checkbox
            selectAll.checked = checkboxes.length === document.querySelectorAll('.user-checkbox:checked').length;
        });
    });

    // Update selected count and bulk action button
    function updateSelectedCount() {
        const count = document.querySelectorAll('.user-checkbox:checked').length;
        selectedCount.textContent = count;
        bulkActionBtn.disabled = count === 0;
    }

    // Handle bulk action form submit
    bulkActionForm.addEventListener('submit', function(e) {
        e.preventDefault();
        const action = this.querySelector('[name="bulk_action"]').value;
        const selectedCount = document.querySelectorAll('.user-checkbox:checked').length;

        if (!action) {
            Swal.fire('Error', 'Pilih aksi terlebih dahulu', 'error');
            return;
        }

        if (selectedCount === 0) {
            Swal.fire('Error', 'Pilih user terlebih dahulu', 'error');
            return;
        }

        // Confirm action
        let confirmMessage = '';
        switch (action) {
            case 'activate':
                confirmMessage = 'Aktifkan user yang dipilih?';
                break;
            case 'deactivate':
                confirmMessage = 'Nonaktifkan user yang dipilih?';
                break;
            case 'delete':
                confirmMessage = 'Hapus user yang dipilih? Tindakan ini tidak dapat dibatalkan.';
                break;
            case 'reset_password':
                confirmMessage = 'Reset password user yang dipilih ke default (12345678)?';
                break;
        }

        Swal.fire({
            title: 'Konfirmasi',
            text: confirmMessage,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Ya',
            cancelButtonText: 'Batal'
        }).then((result) => {
            if (result.isConfirmed) {
                this.submit();
            }
        });
    });
});
</script>

<?php include 'includes/views/layouts/footer.php'; ?> 