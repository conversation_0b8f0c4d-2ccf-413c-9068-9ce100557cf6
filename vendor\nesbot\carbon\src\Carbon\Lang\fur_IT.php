<?php

/**
 * This file is part of the Carbon package.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

/*
 * Authors: <AUTHORS>
 */
return [
    'formats' => [
        'LT' => 'HH:mm',
        'LTS' => 'HH:mm:ss',
        'L' => 'DD. MM. YY',
        'LL' => 'DD di MMMM dal YYYY',
        'LLL' => 'DD di MMM HH:mm',
        'LLLL' => 'DD di MMMM dal YYYY HH:mm',
    ],
    'months' => ['zenâr', 'fevrâr', 'març', 'avrîl', 'mai', 'jugn', 'lui', 'avost', 'setembar', 'otubar', 'novembar', 'dicembar'],
    'months_short' => ['zen', 'fev', 'mar', 'avr', 'mai', 'jug', 'lui', 'avo', 'set', 'otu', 'nov', 'dic'],
    'weekdays' => ['domenie', 'lunis', 'martars', 'miercus', 'joibe', 'vinars', 'sabide'],
    'weekdays_short' => ['dom', 'lun', 'mar', 'mie', 'joi', 'vin', 'sab'],
    'weekdays_min' => ['dom', 'lun', 'mar', 'mie', 'joi', 'vin', 'sab'],
    'first_day_of_week' => 1,
    'day_of_first_week_of_year' => 4,
    'year' => ':count an',
    'month' => ':count mês',
    'week' => ':count setemane',
    'day' => ':count zornade',
    'hour' => ':count ore',
    'minute' => ':count minût',
    'second' => ':count secont',
];
