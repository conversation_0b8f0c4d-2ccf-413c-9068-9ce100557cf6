<?php
require_once 'includes/config/database.php';
require_once 'includes/helpers/functions.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || getCurrentUser()['role'] !== 'admin') {
    setFlashMessage('danger', 'Akses ditolak! Anda tidak memiliki izin untuk mengakses halaman ini.');
    redirect('index.php');
}

$currentPage = 'logs';
$pageTitle = '📋 System Activity Logs';

// Handle actions
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    try {
        switch ($_POST['action']) {
            case 'clear_old_logs':
                $days = (int)($_POST['days'] ?? 30);
                $stmt = $pdo->prepare("DELETE FROM activity_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL ? DAY)");
                $stmt->execute([$days]);
                $deletedRows = $stmt->rowCount();
                setFlashMessage('success', "Berhasil menghapus $deletedRows log lama (lebih dari $days hari).");
                break;

            case 'export_logs':
                $dateFrom = $_POST['date_from'] ?? date('Y-m-d', strtotime('-30 days'));
                $dateTo = $_POST['date_to'] ?? date('Y-m-d');

                // Export logs to CSV
                header('Content-Type: text/csv');
                header('Content-Disposition: attachment; filename="activity_logs_' . date('Y-m-d') . '.csv"');

                $output = fopen('php://output', 'w');
                fputcsv($output, ['Waktu', 'User', 'Aktivitas', 'IP Address', 'User Agent']);

                $stmt = $pdo->prepare("
                    SELECT l.created_at, u.nama as user_nama, l.aktivitas, l.ip_address, l.user_agent
                    FROM activity_logs l
                    LEFT JOIN users u ON l.user_id = u.id
                    WHERE DATE(l.created_at) BETWEEN ? AND ?
                    ORDER BY l.created_at DESC
                ");
                $stmt->execute([$dateFrom, $dateTo]);

                while ($row = $stmt->fetch()) {
                    fputcsv($output, [
                        $row['created_at'],
                        $row['user_nama'] ?? 'System',
                        $row['aktivitas'] ?? 'No activity',
                        $row['ip_address'] ?? 'N/A',
                        $row['user_agent'] ?? 'N/A'
                    ]);
                }

                fclose($output);
                exit;
        }
        redirect('logs.php');
    } catch (Exception $e) {
        setFlashMessage('danger', 'Error: ' . $e->getMessage());
    }
}

// Get filter parameters
$search = $_GET['search'] ?? '';
$dateFrom = $_GET['date_from'] ?? '';
$dateTo = $_GET['date_to'] ?? '';
$userFilter = $_GET['user'] ?? '';

// Pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$perPage = isset($_GET['per_page']) ? (int)$_GET['per_page'] : 20;
$offset = ($page - 1) * $perPage;

try {
    // Check which columns exist in activity_logs table
    $stmt = $pdo->query("SHOW COLUMNS FROM activity_logs LIKE 'ip_address'");
    $ipAddressColumnExists = $stmt->rowCount() > 0;

    $stmt = $pdo->query("SHOW COLUMNS FROM activity_logs LIKE 'user_agent'");
    $userAgentColumnExists = $stmt->rowCount() > 0;

    $stmt = $pdo->query("SHOW COLUMNS FROM activity_logs LIKE 'activity'");
    $activityColumnExists = $stmt->rowCount() > 0;

    $stmt = $pdo->query("SHOW COLUMNS FROM activity_logs LIKE 'aktivitas'");
    $aktivitasColumnExists = $stmt->rowCount() > 0;

    // Build the query based on available columns
    $activityColumn = $activityColumnExists ? 'l.activity' : ($aktivitasColumnExists ? 'l.aktivitas' : "'No activity'");
    $ipColumn = $ipAddressColumnExists ? 'l.ip_address' : "'N/A'";
    $userAgentColumn = $userAgentColumnExists ? 'l.user_agent' : "'N/A'";

    // Build WHERE clause for filters
    $whereConditions = [];
    $params = [];

    if (!empty($search)) {
        $whereConditions[] = "($activityColumn LIKE ? OR u.nama LIKE ?)";
        $params[] = "%$search%";
        $params[] = "%$search%";
    }

    if (!empty($dateFrom)) {
        $whereConditions[] = "DATE(l.created_at) >= ?";
        $params[] = $dateFrom;
    }

    if (!empty($dateTo)) {
        $whereConditions[] = "DATE(l.created_at) <= ?";
        $params[] = $dateTo;
    }

    if (!empty($userFilter)) {
        $whereConditions[] = "l.user_id = ?";
        $params[] = $userFilter;
    }

    $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

    // Get total logs count with filters
    $countQuery = "SELECT COUNT(*) FROM activity_logs l LEFT JOIN users u ON l.user_id = u.id $whereClause";
    $stmt = $pdo->prepare($countQuery);
    $stmt->execute($params);
    $totalLogs = $stmt->fetchColumn();
    $totalPages = ceil($totalLogs / $perPage);

    // Get logs with user information and filters
    $query = "
        SELECT l.*, u.nama as user_nama,
               $activityColumn as activity,
               $ipColumn as ip_address,
               $userAgentColumn as user_agent
        FROM activity_logs l
        LEFT JOIN users u ON l.user_id = u.id
        $whereClause
        ORDER BY l.created_at DESC
        LIMIT ? OFFSET ?
    ";

    $stmt = $pdo->prepare($query);
    $stmt->execute(array_merge($params, [$perPage, $offset]));
    $logs = $stmt->fetchAll();

    // Get statistics
    $stats = [];

    // Total logs
    $stmt = $pdo->query("SELECT COUNT(*) FROM activity_logs");
    $stats['total'] = $stmt->fetchColumn();

    // Today's logs
    $stmt = $pdo->query("SELECT COUNT(*) FROM activity_logs WHERE DATE(created_at) = CURDATE()");
    $stats['today'] = $stmt->fetchColumn();

    // This week's logs
    $stmt = $pdo->query("SELECT COUNT(*) FROM activity_logs WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)");
    $stats['week'] = $stmt->fetchColumn();

    // Unique users today
    $stmt = $pdo->query("SELECT COUNT(DISTINCT user_id) FROM activity_logs WHERE DATE(created_at) = CURDATE()");
    $stats['unique_users_today'] = $stmt->fetchColumn();

    // Get all users for filter dropdown
    $stmt = $pdo->query("SELECT id, nama FROM users ORDER BY nama");
    $allUsers = $stmt->fetchAll();

} catch (Exception $e) {
    error_log("Error loading logs: " . $e->getMessage());
    setFlashMessage('danger', 'Error loading logs: ' . $e->getMessage());
    $logs = [];
    $totalLogs = 0;
    $totalPages = 0;
    $stats = ['total' => 0, 'today' => 0, 'week' => 0, 'unique_users_today' => 0];
    $allUsers = [];
}

include 'includes/views/layouts/header.php';
?>

<div class="container-fluid px-4 py-3">
    <!-- Header Section -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-1">📋 System Activity Logs</h1>
            <p class="text-muted mb-0">Monitor and track all system activities and user actions</p>
        </div>
        <div class="d-flex gap-2">
            <div class="dropdown">
                <button class="btn btn-outline-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                    <i class="fas fa-tools me-1"></i>Actions
                </button>
                <ul class="dropdown-menu">
                    <li>
                        <button type="button" class="dropdown-item" data-bs-toggle="modal" data-bs-target="#exportModal">
                            <i class="fas fa-download me-2"></i>Export Logs
                        </button>
                    </li>
                    <li>
                        <button type="button" class="dropdown-item" data-bs-toggle="modal" data-bs-target="#clearLogsModal">
                            <i class="fas fa-trash me-2"></i>Clear Old Logs
                        </button>
                    </li>
                    <li><hr class="dropdown-divider"></li>
                    <li>
                        <a class="dropdown-item" href="admin-dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i>Back to Dashboard
                        </a>
                    </li>
                </ul>
            </div>
            <button class="btn btn-primary" onclick="location.reload()">
                <i class="fas fa-sync-alt me-1"></i>Refresh
            </button>
        </div>
    </div>

    <?php echo displayFlashMessage(); ?>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Logs</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?= number_format($stats['total']) ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-list fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Today's Activities</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?= number_format($stats['today']) ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calendar-day fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">This Week</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?= number_format($stats['week']) ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calendar-week fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Active Users Today</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?= number_format($stats['unique_users_today']) ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters Section -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">🔍 Filter & Search</h6>
        </div>
        <div class="card-body">
            <form method="GET" class="row g-3">
                <div class="col-md-3">
                    <label for="search" class="form-label">Search</label>
                    <input type="text" class="form-control" id="search" name="search"
                           value="<?= htmlspecialchars($search) ?>"
                           placeholder="Search activity or user...">
                </div>
                <div class="col-md-2">
                    <label for="date_from" class="form-label">From Date</label>
                    <input type="date" class="form-control" id="date_from" name="date_from"
                           value="<?= htmlspecialchars($dateFrom) ?>">
                </div>
                <div class="col-md-2">
                    <label for="date_to" class="form-label">To Date</label>
                    <input type="date" class="form-control" id="date_to" name="date_to"
                           value="<?= htmlspecialchars($dateTo) ?>">
                </div>
                <div class="col-md-2">
                    <label for="user" class="form-label">User</label>
                    <select class="form-select" id="user" name="user">
                        <option value="">All Users</option>
                        <?php foreach ($allUsers as $user): ?>
                            <option value="<?= $user['id'] ?>" <?= $userFilter == $user['id'] ? 'selected' : '' ?>>
                                <?= htmlspecialchars($user['nama']) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="per_page" class="form-label">Per Page</label>
                    <select class="form-select" id="per_page" name="per_page">
                        <option value="10" <?= $perPage == 10 ? 'selected' : '' ?>>10</option>
                        <option value="20" <?= $perPage == 20 ? 'selected' : '' ?>>20</option>
                        <option value="50" <?= $perPage == 50 ? 'selected' : '' ?>>50</option>
                        <option value="100" <?= $perPage == 100 ? 'selected' : '' ?>>100</option>
                    </select>
                </div>
                <div class="col-md-1 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </form>

            <?php if (!empty($search) || !empty($dateFrom) || !empty($dateTo) || !empty($userFilter)): ?>
                <div class="mt-3">
                    <a href="logs.php" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-times me-1"></i>Clear Filters
                    </a>
                    <span class="text-muted ms-2">
                        Showing <?= number_format($totalLogs) ?> filtered results
                    </span>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Logs Table -->
    <div class="card shadow">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="m-0 font-weight-bold text-primary">📋 Activity Logs</h6>
            <div class="text-muted small">
                Page <?= $page ?> of <?= $totalPages ?> (<?= number_format($totalLogs) ?> total entries)
            </div>
        </div>
        <div class="card-body">
            <?php if (empty($logs)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No logs found</h5>
                    <p class="text-muted">Try adjusting your filters or check back later.</p>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th width="15%">
                                    <i class="fas fa-clock me-1"></i>Time
                                </th>
                                <th width="15%">
                                    <i class="fas fa-user me-1"></i>User
                                </th>
                                <th width="35%">
                                    <i class="fas fa-activity me-1"></i>Activity
                                </th>
                                <th width="15%">
                                    <i class="fas fa-globe me-1"></i>IP Address
                                </th>
                                <th width="20%">
                                    <i class="fas fa-desktop me-1"></i>User Agent
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($logs as $log): ?>
                                <tr>
                                    <td>
                                        <div class="fw-bold"><?= date('H:i:s', strtotime($log['created_at'] ?? date('Y-m-d H:i:s'))) ?></div>
                                        <small class="text-muted"><?= date('M d, Y', strtotime($log['created_at'] ?? date('Y-m-d H:i:s'))) ?></small>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm me-2">
                                                <div class="avatar-initial bg-primary rounded-circle">
                                                    <?= strtoupper(substr($log['user_nama'] ?? 'S', 0, 1)) ?>
                                                </div>
                                            </div>
                                            <div>
                                                <div class="fw-bold"><?= htmlspecialchars($log['user_nama'] ?? 'System') ?></div>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="text-wrap"><?= htmlspecialchars($log['activity'] ?? 'No activity') ?></span>
                                    </td>
                                    <td>
                                        <span class="badge bg-light text-dark"><?= htmlspecialchars($log['ip_address'] ?? 'N/A') ?></span>
                                    </td>
                                    <td>
                                        <span class="text-truncate d-inline-block" style="max-width: 200px;"
                                              title="<?= htmlspecialchars($log['user_agent'] ?? 'N/A') ?>">
                                            <?= htmlspecialchars($log['user_agent'] ?? 'N/A') ?>
                                        </span>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <?php if ($totalPages > 1): ?>
                    <nav aria-label="Page navigation" class="mt-4">
                        <ul class="pagination justify-content-center">
                            <?php if ($page > 1): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?<?= http_build_query(array_merge($_GET, ['page' => $page - 1])) ?>">
                                        <i class="fas fa-chevron-left"></i>
                                    </a>
                                </li>
                            <?php endif; ?>

                            <?php
                            $start = max(1, $page - 2);
                            $end = min($totalPages, $page + 2);
                            ?>

                            <?php if ($start > 1): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?<?= http_build_query(array_merge($_GET, ['page' => 1])) ?>">1</a>
                                </li>
                                <?php if ($start > 2): ?>
                                    <li class="page-item disabled"><span class="page-link">...</span></li>
                                <?php endif; ?>
                            <?php endif; ?>

                            <?php for ($i = $start; $i <= $end; $i++): ?>
                                <li class="page-item <?= $i === $page ? 'active' : '' ?>">
                                    <a class="page-link" href="?<?= http_build_query(array_merge($_GET, ['page' => $i])) ?>"><?= $i ?></a>
                                </li>
                            <?php endfor; ?>

                            <?php if ($end < $totalPages): ?>
                                <?php if ($end < $totalPages - 1): ?>
                                    <li class="page-item disabled"><span class="page-link">...</span></li>
                                <?php endif; ?>
                                <li class="page-item">
                                    <a class="page-link" href="?<?= http_build_query(array_merge($_GET, ['page' => $totalPages])) ?>"><?= $totalPages ?></a>
                                </li>
                            <?php endif; ?>

                            <?php if ($page < $totalPages): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?<?= http_build_query(array_merge($_GET, ['page' => $page + 1])) ?>">
                                        <i class="fas fa-chevron-right"></i>
                                    </a>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Export Modal -->
<div class="modal fade" id="exportModal" tabindex="-1" aria-labelledby="exportModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exportModalLabel">
                    <i class="fas fa-download me-2"></i>Export Activity Logs
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="export_logs">

                    <div class="mb-3">
                        <label for="export_date_from" class="form-label">From Date</label>
                        <input type="date" class="form-control" id="export_date_from" name="date_from"
                               value="<?= date('Y-m-d', strtotime('-30 days')) ?>" required>
                    </div>

                    <div class="mb-3">
                        <label for="export_date_to" class="form-label">To Date</label>
                        <input type="date" class="form-control" id="export_date_to" name="date_to"
                               value="<?= date('Y-m-d') ?>" required>
                    </div>

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        The logs will be exported as a CSV file containing all activity data within the selected date range.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-download me-1"></i>Export CSV
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Clear Logs Modal -->
<div class="modal fade" id="clearLogsModal" tabindex="-1" aria-labelledby="clearLogsModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="clearLogsModalLabel">
                    <i class="fas fa-trash me-2"></i>Clear Old Activity Logs
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="clear_old_logs">

                    <div class="mb-3">
                        <label for="days" class="form-label">Delete logs older than (days)</label>
                        <select class="form-select" id="days" name="days" required>
                            <option value="30">30 days</option>
                            <option value="60">60 days</option>
                            <option value="90">90 days</option>
                            <option value="180">6 months</option>
                            <option value="365">1 year</option>
                        </select>
                    </div>

                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Warning:</strong> This action cannot be undone. All activity logs older than the selected period will be permanently deleted.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash me-1"></i>Delete Old Logs
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Custom CSS for avatar -->
<style>
.avatar-sm {
    width: 32px;
    height: 32px;
}

.avatar-initial {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: 600;
    color: white;
}

.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}

.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}

.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}

.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}

.text-gray-800 {
    color: #5a5c69 !important;
}

.text-gray-300 {
    color: #dddfeb !important;
}

.shadow {
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15) !important;
}
</style>

<!-- Auto-refresh functionality -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-refresh every 30 seconds if no filters are applied
    <?php if (empty($search) && empty($dateFrom) && empty($dateTo) && empty($userFilter)): ?>
    setInterval(function() {
        if (document.visibilityState === 'visible') {
            location.reload();
        }
    }, 30000);
    <?php endif; ?>
});
</script>

<?php include 'includes/views/layouts/footer.php'; ?>