<?php
require_once 'includes/config/database.php';
require_once 'includes/helpers/function_check.php';
require_once 'includes/helpers/functions.php';

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in and is admin
if (!isLoggedIn()) {
    setFlashMessage('danger', 'Silakan login terlebih dahulu');
    redirect('login.php');
}

$currentUser = getCurrentUser();
if ($currentUser['role'] !== 'admin') {
    setFlashMessage('danger', 'Akses ditolak. Hanya admin yang dapat mengakses halaman ini.');
    redirect('dashboard.php');
}

$currentPage = 'logs';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'clear_log':
                $logType = $_POST['log_type'] ?? '';
                $result = clearLogFile($logType);
                if ($result['success']) {
                    setFlashMessage('success', $result['message']);
                } else {
                    setFlashMessage('danger', $result['message']);
                }
                break;
                
            case 'download_log':
                $logType = $_POST['log_type'] ?? '';
                downloadLogFile($logType);
                exit;
                
            case 'clear_all_logs':
                $result = clearAllLogFiles();
                if ($result['success']) {
                    setFlashMessage('success', $result['message']);
                } else {
                    setFlashMessage('danger', $result['message']);
                }
                break;
        }
        redirect('logs.php');
    }
}

// Get log data
$logType = $_GET['type'] ?? 'system';
$page = (int)($_GET['page'] ?? 1);
$perPage = 50;

$logData = getLogData($logType, $page, $perPage);
$logFiles = getAvailableLogFiles();

// Include header and sidebar
require_once 'includes/views/layouts/header.php';
require_once 'includes/views/layouts/sidebar.php';

/**
 * Get available log files
 */
function getAvailableLogFiles() {
    $logDir = 'logs/';
    $files = [];
    
    // Create logs directory if not exists
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    // Scan for log files
    $logFiles = glob($logDir . '*.log');
    
    foreach ($logFiles as $file) {
        $filename = basename($file);
        $type = str_replace('.log', '', $filename);
        
        $files[$type] = [
            'name' => $filename,
            'path' => $file,
            'size' => file_exists($file) ? filesize($file) : 0,
            'modified' => file_exists($file) ? filemtime($file) : 0,
            'lines' => file_exists($file) ? count(file($file)) : 0
        ];
    }
    
    return $files;
}

/**
 * Get log data
 */
function getLogData($logType, $page = 1, $perPage = 50) {
    $logFile = "logs/{$logType}.log";
    
    if (!file_exists($logFile)) {
        return [
            'entries' => [],
            'total' => 0,
            'pages' => 0,
            'current_page' => 1
        ];
    }
    
    $lines = file($logFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    $lines = array_reverse($lines); // Show newest first
    
    $total = count($lines);
    $pages = ceil($total / $perPage);
    $offset = ($page - 1) * $perPage;
    
    $entries = [];
    $pageLines = array_slice($lines, $offset, $perPage);
    
    foreach ($pageLines as $line) {
        $entry = parseLogLine($line, $logType);
        if ($entry) {
            $entries[] = $entry;
        }
    }
    
    return [
        'entries' => $entries,
        'total' => $total,
        'pages' => $pages,
        'current_page' => $page
    ];
}

/**
 * Parse log line
 */
function parseLogLine($line, $logType) {
    // Try to parse different log formats
    
    // JSON format (system logs)
    if (strpos($line, '{') === 0) {
        $data = json_decode($line, true);
        if ($data) {
            return [
                'timestamp' => $data['timestamp'] ?? date('Y-m-d H:i:s'),
                'level' => $data['level'] ?? 'info',
                'message' => $data['message'] ?? $line,
                'context' => $data['context'] ?? [],
                'raw' => $line
            ];
        }
    }
    
    // Standard format [timestamp] level: message
    if (preg_match('/\[([^\]]+)\]\s*(\w+):\s*(.+)/', $line, $matches)) {
        return [
            'timestamp' => $matches[1],
            'level' => strtolower($matches[2]),
            'message' => $matches[3],
            'context' => [],
            'raw' => $line
        ];
    }
    
    // PHP error log format
    if (preg_match('/\[([^\]]+)\]\s*(.+)/', $line, $matches)) {
        return [
            'timestamp' => $matches[1],
            'level' => 'error',
            'message' => $matches[2],
            'context' => [],
            'raw' => $line
        ];
    }
    
    // Fallback - treat as plain message
    return [
        'timestamp' => date('Y-m-d H:i:s'),
        'level' => 'info',
        'message' => $line,
        'context' => [],
        'raw' => $line
    ];
}

/**
 * Clear log file
 */
function clearLogFile($logType) {
    try {
        $logFile = "logs/{$logType}.log";
        
        if (!file_exists($logFile)) {
            return [
                'success' => false,
                'message' => 'Log file not found: ' . $logType
            ];
        }
        
        if (file_put_contents($logFile, '') !== false) {
            logSystemEvent("Log file cleared", 'info', ['log_type' => $logType]);
            
            return [
                'success' => true,
                'message' => "Log file '{$logType}' cleared successfully"
            ];
        } else {
            return [
                'success' => false,
                'message' => 'Failed to clear log file'
            ];
        }
        
    } catch (Exception $e) {
        error_log("Clear log file error: " . $e->getMessage());
        return [
            'success' => false,
            'message' => 'Error clearing log file: ' . $e->getMessage()
        ];
    }
}

/**
 * Download log file
 */
function downloadLogFile($logType) {
    $logFile = "logs/{$logType}.log";
    
    if (!file_exists($logFile)) {
        setFlashMessage('danger', 'Log file not found');
        redirect('logs.php');
        return;
    }
    
    $filename = $logType . '_log_' . date('Y-m-d_H-i-s') . '.log';
    
    header('Content-Type: application/octet-stream');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Content-Length: ' . filesize($logFile));
    
    readfile($logFile);
}

/**
 * Clear all log files
 */
function clearAllLogFiles() {
    try {
        $logFiles = glob('logs/*.log');
        $cleared = 0;
        
        foreach ($logFiles as $file) {
            if (file_put_contents($file, '') !== false) {
                $cleared++;
            }
        }
        
        logSystemEvent("All log files cleared", 'info', ['files_cleared' => $cleared]);
        
        return [
            'success' => true,
            'message' => "All log files cleared successfully. $cleared files processed."
        ];
        
    } catch (Exception $e) {
        error_log("Clear all log files error: " . $e->getMessage());
        return [
            'success' => false,
            'message' => 'Error clearing log files: ' . $e->getMessage()
        ];
    }
}
?>

<div class="modern-layout">
    <div class="modern-container">
        <!-- Modern Page Header -->
        <div class="modern-page-header modern-flex modern-justify-between modern-items-center modern-mb-xl">
            <div>
                <h1 class="modern-page-title modern-mb-0">System Logs</h1>
                <p class="modern-page-subtitle">View and manage system activity logs</p>
            </div>
            <div class="modern-page-actions">
                <form method="POST" style="display: inline;">
                    <input type="hidden" name="action" value="clear_all_logs">
                    <button type="submit" class="modern-btn modern-btn-danger" onclick="return confirm('Clear all log files? This action cannot be undone.')">
                        <i class="fas fa-trash"></i>
                        Clear All Logs
                    </button>
                </form>
            </div>
        </div>

        <!-- Flash Messages -->
        <?php if ($flash = getFlashMessage()): ?>
        <div class="modern-alert modern-alert-<?= $flash['type'] ?> modern-mb-lg">
            <div class="modern-alert-content">
                <i class="fas fa-<?= $flash['type'] === 'success' ? 'check-circle' : 'exclamation-triangle' ?> modern-alert-icon"></i>
                <div class="modern-alert-message"><?= $flash['message'] ?></div>
            </div>
            <button type="button" class="modern-alert-close" onclick="this.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <?php endif; ?>

        <!-- Log Files Overview -->
        <div class="modern-card modern-mb-lg">
            <div class="modern-card-header">
                <h5 class="modern-card-title">
                    <i class="fas fa-file-alt modern-text-primary modern-mr-sm"></i>
                    Available Log Files
                </h5>
            </div>
            <div class="modern-card-body modern-p-0">
                <div class="modern-log-files">
                    <?php foreach ($logFiles as $type => $file): ?>
                    <div class="modern-log-file <?= $logType === $type ? 'modern-log-file-active' : '' ?>">
                        <div class="modern-log-file-info">
                            <div class="modern-log-file-name">
                                <a href="?type=<?= $type ?>" class="modern-log-file-link">
                                    <i class="fas fa-file-alt modern-mr-sm"></i>
                                    <?= ucfirst($type) ?> Log
                                </a>
                            </div>
                            <div class="modern-log-file-meta">
                                <span class="modern-log-meta-item">
                                    <i class="fas fa-weight modern-mr-xs"></i>
                                    <?= formatBytes($file['size']) ?>
                                </span>
                                <span class="modern-log-meta-item">
                                    <i class="fas fa-list modern-mr-xs"></i>
                                    <?= number_format($file['lines']) ?> lines
                                </span>
                                <span class="modern-log-meta-item">
                                    <i class="fas fa-clock modern-mr-xs"></i>
                                    <?= $file['modified'] ? date('d/m/Y H:i', $file['modified']) : 'Never' ?>
                                </span>
                            </div>
                        </div>
                        <div class="modern-log-file-actions">
                            <form method="POST" style="display: inline;">
                                <input type="hidden" name="action" value="download_log">
                                <input type="hidden" name="log_type" value="<?= $type ?>">
                                <button type="submit" class="modern-btn modern-btn-sm modern-btn-outline-primary">
                                    <i class="fas fa-download"></i>
                                </button>
                            </form>
                            <form method="POST" style="display: inline;">
                                <input type="hidden" name="action" value="clear_log">
                                <input type="hidden" name="log_type" value="<?= $type ?>">
                                <button type="submit" class="modern-btn modern-btn-sm modern-btn-outline-danger" onclick="return confirm('Clear this log file?')">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </form>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>

        <!-- Log Entries -->
        <div class="modern-card">
            <div class="modern-card-header">
                <h5 class="modern-card-title">
                    <i class="fas fa-list modern-text-primary modern-mr-sm"></i>
                    <?= ucfirst($logType) ?> Log Entries
                </h5>
                <div class="modern-card-actions">
                    <span class="modern-badge modern-badge-info">
                        <?= number_format($logData['total']) ?> entries
                    </span>
                </div>
            </div>
            <div class="modern-card-body modern-p-0">
                <?php if (!empty($logData['entries'])): ?>
                <div class="modern-log-entries">
                    <?php foreach ($logData['entries'] as $entry): ?>
                    <div class="modern-log-entry modern-log-<?= $entry['level'] ?>">
                        <div class="modern-log-timestamp">
                            <?= date('d/m/Y H:i:s', strtotime($entry['timestamp'])) ?>
                        </div>
                        <div class="modern-log-level">
                            <span class="modern-badge modern-badge-<?= getLevelBadgeClass($entry['level']) ?>">
                                <?= strtoupper($entry['level']) ?>
                            </span>
                        </div>
                        <div class="modern-log-message">
                            <?= htmlspecialchars($entry['message']) ?>
                            <?php if (!empty($entry['context'])): ?>
                            <div class="modern-log-context">
                                <details>
                                    <summary>Context</summary>
                                    <pre><?= htmlspecialchars(json_encode($entry['context'], JSON_PRETTY_PRINT)) ?></pre>
                                </details>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>

                <!-- Pagination -->
                <?php if ($logData['pages'] > 1): ?>
                <div class="modern-pagination">
                    <div class="modern-pagination-info">
                        Page <?= $logData['current_page'] ?> of <?= $logData['pages'] ?>
                    </div>
                    <div class="modern-pagination-links">
                        <?php if ($logData['current_page'] > 1): ?>
                        <a href="?type=<?= $logType ?>&page=<?= $logData['current_page'] - 1 ?>" class="modern-btn modern-btn-sm modern-btn-outline-primary">
                            <i class="fas fa-chevron-left"></i>
                            Previous
                        </a>
                        <?php endif; ?>
                        
                        <?php if ($logData['current_page'] < $logData['pages']): ?>
                        <a href="?type=<?= $logType ?>&page=<?= $logData['current_page'] + 1 ?>" class="modern-btn modern-btn-sm modern-btn-outline-primary">
                            Next
                            <i class="fas fa-chevron-right"></i>
                        </a>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endif; ?>
                <?php else: ?>
                <div class="modern-empty-state modern-py-xl">
                    <div class="modern-empty-icon">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <h6 class="modern-empty-title">No Log Entries</h6>
                    <p class="modern-empty-description">No entries found in the <?= $logType ?> log file.</p>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/views/layouts/footer.php'; ?>

<?php
/**
 * Get badge class for log level
 */
function getLevelBadgeClass($level) {
    $classes = [
        'error' => 'danger',
        'warning' => 'warning',
        'info' => 'info',
        'debug' => 'secondary',
        'success' => 'success'
    ];
    
    return $classes[$level] ?? 'secondary';
}
?>

<style>
.modern-log-files {
    display: flex;
    flex-direction: column;
}

.modern-log-file {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #eee;
    transition: background-color 0.2s;
}

.modern-log-file:hover {
    background-color: #f8f9fa;
}

.modern-log-file:last-child {
    border-bottom: none;
}

.modern-log-file-active {
    background-color: #e3f2fd;
    border-left: 4px solid #2196f3;
}

.modern-log-file-info {
    flex: 1;
}

.modern-log-file-name {
    margin-bottom: 5px;
}

.modern-log-file-link {
    font-weight: 600;
    color: #2c3e50;
    text-decoration: none;
}

.modern-log-file-link:hover {
    color: #3498db;
}

.modern-log-file-meta {
    display: flex;
    gap: 15px;
    font-size: 12px;
    color: #6c757d;
}

.modern-log-meta-item {
    display: flex;
    align-items: center;
}

.modern-log-file-actions {
    display: flex;
    gap: 5px;
}

.modern-log-entries {
    display: flex;
    flex-direction: column;
}

.modern-log-entry {
    display: grid;
    grid-template-columns: 150px 80px 1fr;
    gap: 15px;
    padding: 15px 20px;
    border-bottom: 1px solid #eee;
    font-family: 'Courier New', monospace;
    font-size: 13px;
}

.modern-log-entry:last-child {
    border-bottom: none;
}

.modern-log-error {
    border-left: 4px solid #dc3545;
    background-color: #f8d7da;
}

.modern-log-warning {
    border-left: 4px solid #ffc107;
    background-color: #fff3cd;
}

.modern-log-info {
    border-left: 4px solid #17a2b8;
    background-color: #d1ecf1;
}

.modern-log-debug {
    border-left: 4px solid #6c757d;
    background-color: #f8f9fa;
}

.modern-log-success {
    border-left: 4px solid #28a745;
    background-color: #d4edda;
}

.modern-log-timestamp {
    color: #6c757d;
    font-size: 12px;
}

.modern-log-level {
    display: flex;
    align-items: flex-start;
}

.modern-log-message {
    line-height: 1.4;
    word-break: break-word;
}

.modern-log-context {
    margin-top: 10px;
    font-size: 11px;
}

.modern-log-context details {
    background-color: rgba(0,0,0,0.05);
    padding: 5px;
    border-radius: 3px;
}

.modern-log-context pre {
    margin: 5px 0 0 0;
    font-size: 10px;
    max-height: 200px;
    overflow-y: auto;
}

.modern-pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-top: 1px solid #eee;
    background-color: #f8f9fa;
}

.modern-pagination-info {
    font-size: 14px;
    color: #6c757d;
}

.modern-pagination-links {
    display: flex;
    gap: 10px;
}
</style>
