menu_permissions_advanced.php<?php
require_once 'includes/config/database.php';
require_once 'includes/helpers/functions.php';
require_once 'includes/helpers/notifications.php';

// Handle AJAX request to get menu permissions for a role
if (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest') {
    // This is an AJAX request
    if (isset($_GET['action']) && $_GET['action'] === 'get_role_menu_permissions') {
        header('Content-Type: application/json');

        if (isset($_GET['role_id']) && !empty($_GET['role_id'])) {
            $roleId = $_GET['role_id'];
            try {
                $stmt = $pdo->prepare("SELECT menu_id FROM role_menu_access WHERE role_id = ?");
                $stmt->execute([$roleId]);
                $allowedMenus = $stmt->fetchAll(PDO::FETCH_COLUMN);

                echo json_encode(['success' => true, 'allowed_menus' => $allowedMenus]);
            } catch (PDOException $e) {
                error_log("Error fetching menu permissions: " . $e->getMessage());
                echo json_encode(['success' => false, 'message' => 'Terjadi kesalahan saat mengambil data menu.']);
            }
        } else {
            echo json_encode(['success' => false, 'message' => 'ID role tidak valid.']);
        }
        exit(); // Stop further script execution for AJAX requests
    }
}

// Define sidebar menu structure (for management purposes)
$sidebar_menus = [
    ['id' => 'dashboard', 'label' => 'Dashboard', 'icon' => 'fas fa-home'],
    ['id' => 'keuangan', 'label' => 'Keuangan', 'icon' => 'fas fa-money-bill-wave', 'submenu' => [
        ['id' => 'transaksi', 'label' => 'Transaksi', 'icon' => 'fas fa-exchange-alt'],
        ['id' => 'kategori', 'label' => 'Kategori', 'icon' => 'fas fa-tags'],
        ['id' => 'target', 'label' => 'Target', 'icon' => 'fas fa-bullseye'],
        ['id' => 'anggaran', 'label' => 'Anggaran', 'icon' => 'fas fa-chart-pie'],
        ['id' => 'investasi', 'label' => 'Investasi', 'icon' => 'fas fa-chart-line'],
        ['id' => 'hutang', 'label' => 'Hutang & Piutang', 'icon' => 'fas fa-hand-holding-usd'],
    ]],
    ['id' => 'bisnis', 'label' => 'Bisnis', 'icon' => 'fas fa-store', 'submenu' => [
        ['id' => 'produk', 'label' => 'Produk', 'icon' => 'fas fa-box'],
        ['id' => 'penjualan', 'label' => 'Penjualan', 'icon' => 'fas fa-shopping-cart'],
        ['id' => 'pembelian', 'label' => 'Pembelian', 'icon' => 'fas fa-shopping-basket'],
        ['id' => 'supplier', 'label' => 'Supplier', 'icon' => 'fas fa-truck'],
        ['id' => 'inventory', 'label' => 'Inventory', 'icon' => 'fas fa-warehouse'],
        ['id' => 'retur', 'label' => 'Retur', 'icon' => 'fas fa-undo'],
    ]],
    ['id' => 'laporan', 'label' => 'Laporan', 'icon' => 'fas fa-chart-bar', 'submenu' => [
        ['id' => 'laporan_keuangan', 'label' => 'Laporan Keuangan', 'icon' => 'fas fa-file-invoice-dollar'],
        ['id' => 'laporan_bisnis', 'label' => 'Laporan Bisnis', 'icon' => 'fas fa-chart-line'],
        ['id' => 'laporan_tax', 'label' => 'Laporan Pajak', 'icon' => 'fas fa-file-invoice'],
    ]],
     ['id' => 'tools', 'label' => 'Tools', 'icon' => 'fas fa-tools', 'submenu' => [
        ['id' => 'kalkulator', 'label' => 'Kalkulator', 'icon' => 'fas fa-calculator'],
        ['id' => 'konverter', 'label' => 'Konverter Mata Uang', 'icon' => 'fas fa-exchange-alt'],
        ['id' => 'kalender', 'label' => 'Kalender', 'icon' => 'fas fa-calendar-alt'],
        ['id' => 'pengingat', 'label' => 'Pengingat', 'icon' => 'fas fa-bell'],
    ]],
    ['id' => 'admin_panel', 'label' => 'Admin Panel', 'icon' => 'fas fa-cogs', 'submenu' => [
        ['id' => 'users', 'label' => 'Kelola User', 'icon' => 'fas fa-users'],
        ['id' => 'notifications', 'label' => 'Notifikasi', 'icon' => 'fas fa-bell'],
        ['id' => 'system_customization', 'label' => 'System Customization', 'icon' => 'fas fa-paint-brush'],
        ['id' => 'database_management', 'label' => 'Database Management', 'icon' => 'fas fa-database'],
        ['id' => 'backup', 'label' => 'Backup Data', 'icon' => 'fas fa-database'],
        ['id' => 'logs', 'label' => 'Log Aktivitas', 'icon' => 'fas fa-history'],
        ['id' => 'settings', 'label' => 'Pengaturan', 'icon' => 'fas fa-cog'],
    ]],
    ['id' => 'customization', 'label' => 'Customization', 'icon' => 'fas fa-palette', 'submenu' => [
        ['id' => 'customization_dashboard', 'label' => 'Dashboard', 'icon' => 'fas fa-tachometer-alt'],
        ['id' => 'theme_manager', 'label' => 'Theme Manager', 'icon' => 'fas fa-swatchbook'],
    ]],
    ['id' => 'profile', 'label' => 'Profil', 'icon' => 'fas fa-user'],
    ['id' => 'bantuan', 'label' => 'Bantuan', 'icon' => 'fas fa-question-circle', 'submenu' => [
        ['id' => 'panduan', 'label' => 'Panduan', 'icon' => 'fas fa-book'],
        ['id' => 'faq', 'label' => 'FAQ', 'icon' => 'fas fa-question'],
        ['id' => 'tutorial', 'label' => 'Tutorial', 'icon' => 'fas fa-graduation-cap'],
        ['id' => 'support', 'label' => 'Support', 'icon' => 'fas fa-headset'],
    ]],
    ['id' => 'logout', 'label' => 'Keluar', 'icon' => 'fas fa-sign-out-alt'],
];

// Check if user is logged in and is admin
if (!isLoggedIn()) {
    setFlashMessage('danger', 'Silakan login terlebih dahulu');
    redirect('login.php');
}

$currentUser = getCurrentUser();
if ($currentUser['role'] !== 'admin') {
    setFlashMessage('danger', 'Akses ditolak! Anda tidak memiliki izin untuk mengakses halaman ini.');
    redirect('index.php');
}

$currentPage = 'permissions';
$pageTitle = 'Kelola Hak Akses';

// Check if tables exist and create if not
try {
    // Create permissions table
    $pdo->exec("CREATE TABLE IF NOT EXISTS permissions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(50) NOT NULL UNIQUE,
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )");

    // Create roles table
    $pdo->exec("CREATE TABLE IF NOT EXISTS roles (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(50) NOT NULL UNIQUE,
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )");

    // Create role_permissions table
    $pdo->exec("CREATE TABLE IF NOT EXISTS role_permissions (
        role_id INT,
        permission_id INT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (role_id, permission_id),
        FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
        FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE
    )");

    // Create role_menu_access table
    $pdo->exec("CREATE TABLE IF NOT EXISTS role_menu_access (
        role_id INT,
        menu_id VARCHAR(50),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (role_id, menu_id),
        FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE
        -- FOREIGN KEY for menu_id is not possible as menu_id comes from a PHP array, not a DB table
    )");

    // Insert default roles if not exists
    $stmt = $pdo->query("SELECT COUNT(*) FROM roles");
    if ($stmt->fetchColumn() == 0) {
        $pdo->exec("INSERT INTO roles (name, description) VALUES
            ('admin', 'Administrator - Akses penuh ke semua fitur sistem'),
            ('user', 'Pengguna - Akses terbatas ke fitur dasar')
        ");
        logActivity('Membuat role default: admin dan user');
    }

    // Insert default permissions if not exists
    $stmt = $pdo->query("SELECT COUNT(*) FROM permissions");
    if ($stmt->fetchColumn() == 0) {
        $pdo->exec("INSERT INTO permissions (name, description) VALUES
            ('manage_users', 'Mengelola pengguna - Menambah, mengedit, dan menghapus pengguna'),
            ('manage_roles', 'Mengelola peran - Menambah, mengedit, dan menghapus peran'),
            ('manage_permissions', 'Mengelola hak akses - Menambah, mengedit, dan menghapus hak akses'),
            ('view_reports', 'Melihat laporan - Akses ke semua laporan keuangan'),
            ('manage_transactions', 'Mengelola transaksi - Menambah, mengedit, dan menghapus transaksi'),
            ('manage_categories', 'Mengelola kategori - Menambah, mengedit, dan menghapus kategori'),
            ('view_dashboard', 'Melihat dashboard - Akses ke halaman utama dan statistik'),
            ('export_data', 'Export data - Mengunduh data dalam format Excel/PDF'),
            ('manage_settings', 'Mengelola pengaturan - Mengubah konfigurasi sistem'),
            ('view_logs', 'Melihat log - Akses ke catatan aktivitas sistem'),
            ('manage_backup', 'Mengelola backup - Membuat dan memulihkan backup data'),
            ('view_statistics', 'Melihat statistik - Akses ke grafik dan analisis data'),
            ('manage_notifications', 'Mengelola notifikasi - Mengatur pengingat dan pemberitahuan'),
            ('view_profile', 'Melihat profil - Akses ke informasi pengguna'),
            ('edit_profile', 'Edit profil - Mengubah informasi pengguna')
        ");
        logActivity('Membuat permission default untuk sistem');
    }

    // Set default permissions for admin role
    $stmt = $pdo->prepare("SELECT id FROM roles WHERE name = 'admin'");
    $stmt->execute();
    $adminRoleId = $stmt->fetchColumn();

    if ($adminRoleId) {
        // Get all permission IDs
        $stmt = $pdo->query("SELECT id FROM permissions");
        $permissionIds = $stmt->fetchAll(PDO::FETCH_COLUMN);

        // Insert all permissions for admin
        $stmt = $pdo->prepare("INSERT INTO role_permissions (role_id, permission_id) VALUES (?, ?)");
        foreach ($permissionIds as $permissionId) {
            // Check if the permission is already assigned to avoid duplicates
            $checkStmt = $pdo->prepare("SELECT COUNT(*) FROM role_permissions WHERE role_id = ? AND permission_id = ?");
            $checkStmt->execute([$adminRoleId, $permissionId]);
            if ($checkStmt->fetchColumn() == 0) {
                 $stmt->execute([$adminRoleId, $permissionId]);
            }
        }
    }

    // Set default permissions for user role
    $stmt = $pdo->prepare("SELECT id FROM roles WHERE name = 'user'");
    $stmt->execute();
    $userRoleId = $stmt->fetchColumn();

    if ($userRoleId) {
        // Get basic permission IDs
        $stmt = $pdo->prepare("SELECT id FROM permissions WHERE name IN (
            'view_dashboard',
            'view_reports',
            'manage_transactions',
            'view_profile',
            'edit_profile',
            'view_statistics'
        )");
        $stmt->execute();
        $basicPermissionIds = $stmt->fetchAll(PDO::FETCH_COLUMN);

        // Insert basic permissions for user
        $stmt = $pdo->prepare("INSERT INTO role_permissions (role_id, permission_id) VALUES (?, ?)");
        foreach ($basicPermissionIds as $permissionId) {
             // Check if the permission is already assigned to avoid duplicates
            $checkStmt = $pdo->prepare("SELECT COUNT(*) FROM role_permissions WHERE role_id = ? AND permission_id = ?");
            $checkStmt->execute([$userRoleId, $permissionId]);
            if ($checkStmt->fetchColumn() == 0) {
                 $stmt->execute([$userRoleId, $permissionId]);
            }
        }
    }

    // Hapus role yang tidak digunakan (jika ada)
    $stmt = $pdo->prepare("DELETE FROM roles WHERE name NOT IN ('admin', 'user')");
    $stmt->execute();

} catch (PDOException $e) {
    error_log("Error creating tables or default data: " . $e->getMessage());
    setFlashMessage('danger', 'Terjadi kesalahan saat menyiapkan database. Silakan hubungi administrator.');
    redirect('index.php');
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        try {
            switch ($_POST['action']) {
                case 'add':
                    // Validate input
                    if (empty($_POST['name']) || strlen($_POST['name']) < 3 || strlen($_POST['name']) > 50) {
                        throw new Exception('Nama hak akses harus diisi (3-50 karakter)');
                    }
                    if (!preg_match('/^[a-z_]+$/', $_POST['name'])) {
                        throw new Exception('Nama hak akses hanya boleh berisi huruf kecil dan underscore');
                    }

                    // Check if permission name already exists
                    $stmt = $pdo->prepare("SELECT COUNT(*) FROM permissions WHERE name = ?");
                    $stmt->execute([$_POST['name']]);
                    if ($stmt->fetchColumn() > 0) {
                        throw new Exception('Nama hak akses sudah digunakan');
                    }

                    $stmt = $pdo->prepare("INSERT INTO permissions (name, description) VALUES (?, ?)");
                    $stmt->execute([$_POST['name'], $_POST['description']]);
                    setFlashMessage('success', 'Hak akses berhasil ditambahkan');
                    logActivity('Menambahkan hak akses baru: ' . $_POST['name']);
                    break;

                case 'edit':
                    // Validate input
                    if (empty($_POST['id']) || empty($_POST['name'])) {
                        throw new Exception('Data tidak lengkap');
                    }
                    if (strlen($_POST['name']) < 3 || strlen($_POST['name']) > 50) {
                        throw new Exception('Nama hak akses harus 3-50 karakter');
                    }
                    if (!preg_match('/^[a-z_]+$/', $_POST['name'])) {
                        throw new Exception('Nama hak akses hanya boleh berisi huruf kecil dan underscore');
                    }

                    // Check if permission name already exists (excluding current permission)
                    $stmt = $pdo->prepare("SELECT COUNT(*) FROM permissions WHERE name = ? AND id != ?");
                    $stmt->execute([$_POST['name'], $_POST['id']]);
                    if ($stmt->fetchColumn() > 0) {
                        throw new Exception('Nama hak akses sudah digunakan');
                    }

                    $stmt = $pdo->prepare("UPDATE permissions SET name = ?, description = ? WHERE id = ?");
                    $stmt->execute([$_POST['name'], $_POST['description'], $_POST['id']]);
                    setFlashMessage('success', 'Hak akses berhasil diperbarui');
                    logActivity('Memperbarui hak akses: ' . $_POST['name']);
                    break;

                case 'delete':
                    if (empty($_POST['id'])) {
                        throw new Exception('ID hak akses tidak valid');
                    }

                    // Check if permission is assigned to any role
                    $stmt = $pdo->prepare("SELECT COUNT(*) FROM role_permissions WHERE permission_id = ?");
                    $stmt->execute([$_POST['id']]);
                    if ($stmt->fetchColumn() > 0) {
                        throw new Exception('Hak akses tidak dapat dihapus karena masih digunakan oleh role');
                    }

                    // Get permission name for logging
                    $stmt = $pdo->prepare("SELECT name FROM permissions WHERE id = ?");
                    $stmt->execute([$_POST['id']]);
                    $permissionName = $stmt->fetchColumn();

                    $stmt = $pdo->prepare("DELETE FROM permissions WHERE id = ?");
                    $stmt->execute([$_POST['id']]);
                    setFlashMessage('success', 'Hak akses berhasil dihapus');
                    logActivity('Menghapus hak akses: ' . $permissionName);
                    break;

                case 'assign':
                    if (empty($_POST['role_id'])) {
                        throw new Exception('ID role tidak valid');
                    }

                    // Remove existing permissions for this role
                    $stmt = $pdo->prepare("DELETE FROM role_permissions WHERE role_id = ?");
                    $stmt->execute([$_POST['role_id']]);

                    // Add new permissions
                    if (isset($_POST['permissions']) && is_array($_POST['permissions'])) {
                        $stmt = $pdo->prepare("INSERT INTO role_permissions (role_id, permission_id) VALUES (?, ?)");
                        foreach ($_POST['permissions'] as $permissionId) {
                            // Check if the permission is already assigned to avoid duplicates
                             $checkStmt = $pdo->prepare("SELECT COUNT(*) FROM role_permissions WHERE role_id = ? AND permission_id = ?");
                            $checkStmt->execute([$_POST['role_id'], $permissionId]);
                            if ($checkStmt->fetchColumn() == 0) {
                                 $stmt->execute([$_POST['role_id'], $permissionId]);
                            }
                        }
                    }

                    // Get role name for logging
                    $stmt = $pdo->prepare("SELECT name FROM roles WHERE id = ?");
                    $stmt->execute([$_POST['role_id']]);
                    $roleName = $stmt->fetchColumn();

                    setFlashMessage('success', 'Hak akses berhasil ditetapkan untuk role');
                    logActivity(getCurrentUser()['id'], 'Menetapkan hak akses untuk role: ' . $roleName, 'update');
                    break;

                // Handle saving menu permissions
                case 'save_menu_permissions':
                    // Debug: Log received data
                    error_log("DEBUG save_menu_permissions: " . print_r($_POST, true));

                    // Get role_id from hidden input or fallback to role_filter
                    $roleId = $_POST['role_id'] ?? $_POST['role_filter'] ?? '';

                    if (empty($roleId)) {
                        throw new Exception('ID role tidak valid. Silakan pilih role terlebih dahulu. Received role_id: ' . ($_POST['role_id'] ?? 'NULL') . ', role_filter: ' . ($_POST['role_filter'] ?? 'NULL'));
                    }

                    // Use the role_id for further processing
                    $_POST['role_id'] = $roleId;

                    // Delete existing menu permissions for this role
                    $deleteStmt = $pdo->prepare("DELETE FROM role_menu_access WHERE role_id = ?");
                    $deleteStmt->execute([$_POST['role_id']]);

                    // Insert selected menu permissions
                    if (isset($_POST['allowed_menus']) && is_array($_POST['allowed_menus'])) {
                        $insertStmt = $pdo->prepare("INSERT INTO role_menu_access (role_id, menu_id) VALUES (?, ?)");
                        foreach ($_POST['allowed_menus'] as $menuId) {
                            // Optional: Add validation to ensure menuId exists in your $sidebar_menus array
                             $insertStmt->execute([$_POST['role_id'], $menuId]);
                        }
                    }

                     // Get role name for logging
                    $stmt = $pdo->prepare("SELECT name FROM roles WHERE id = ?");
                    $stmt->execute([$_POST['role_id']]);
                    $roleName = $stmt->fetchColumn();

                    setFlashMessage('success', 'Pengaturan tampilan menu berhasil disimpan untuk role: ' . htmlspecialchars($roleName));
                    logActivity(getCurrentUser()['id'], 'Menyimpan pengaturan menu untuk role: ' . $roleName, 'update');
                    break;

            }
        } catch (Exception $e) {
            setFlashMessage('danger', $e->getMessage());
            error_log("Permission Error: " . $e->getMessage());
        }
        redirect('permissions.php');
    }
}

// Get all permissions
try {
    $stmt = $pdo->query("SELECT * FROM permissions ORDER BY name");
    $permissions = $stmt->fetchAll();

    // Get all roles
    $stmt = $pdo->query("SELECT * FROM roles ORDER BY name");
    $roles = $stmt->fetchAll();

    // Get role permissions
    $rolePermissions = [];
    $stmt = $pdo->query("SELECT role_id, permission_id FROM role_permissions");
    while ($row = $stmt->fetch()) {
        $rolePermissions[$row['role_id']][] = $row['permission_id'];
    }
} catch (PDOException $e) {
    error_log("Error fetching data: " . $e->getMessage());
    setFlashMessage('danger', 'Terjadi kesalahan saat mengambil data');
    $permissions = [];
    $roles = [];
    $rolePermissions = [];
}

include 'includes/views/layouts/header.php';
?>

<div class="container-fluid px-4 py-3">
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white py-2 d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="card-title mb-1 d-flex align-items-center gap-2">
                            <i class="fas fa-shield-alt text-primary small"></i>
                            <span class="small fw-medium">Kelola Hak Akses</span>
                        </h5>
                        <p class="text-muted small mb-0">Kelola hak akses dan peran pengguna sistem</p>
                    </div>
                    <div class="d-flex gap-2">
                        <button type="button" class="btn btn-success btn-sm d-flex align-items-center gap-2"
                                data-bs-toggle="modal" data-bs-target="#importPermissionsModal">
                            <i class="fas fa-file-import small"></i>
                            <span>Import</span>
                        </button>
                        <button type="button" class="btn btn-info btn-sm d-flex align-items-center gap-2"
                                onclick="exportPermissions()">
                            <i class="fas fa-file-export small"></i>
                            <span>Export</span>
                        </button>
                        <button type="button" class="btn btn-primary btn-sm d-flex align-items-center gap-2"
                                data-bs-toggle="modal" data-bs-target="#addPermissionModal">
                            <i class="fas fa-plus small"></i>
                            <span>Tambah Hak Akses</span>
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Statistik -->
                    <div class="row g-3 mb-4">
                        <div class="col-md-3">
                            <div class="card border-0 shadow-sm h-100">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="text-muted mb-1 small">Total Hak Akses</h6>
                                            <h3 class="mb-0"><?= count($permissions) ?></h3>
                                        </div>
                                        <div class="bg-primary bg-opacity-10 p-3 rounded">
                                            <i class="fas fa-shield-alt text-primary"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card border-0 shadow-sm h-100">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="text-muted mb-1 small">Total Role</h6>
                                            <h3 class="mb-0"><?= count($roles) ?></h3>
                                        </div>
                                        <div class="bg-success bg-opacity-10 p-3 rounded">
                                            <i class="fas fa-users text-success"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card border-0 shadow-sm h-100">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="text-muted mb-1 small">Hak Akses Aktif</h6>
                                            <h3 class="mb-0"><?= count(array_filter($permissions, fn($p) => !empty($rolePermissions[$p['id']]))) ?></h3>
                                        </div>
                                        <div class="bg-info bg-opacity-10 p-3 rounded">
                                            <i class="fas fa-check-circle text-info"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card border-0 shadow-sm h-100">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="text-muted mb-1 small">Hak Akses Baru</h6>
                                            <h3 class="mb-0"><?= count(array_filter($permissions, fn($p) => strtotime($p['created_at']) > strtotime('-7 days'))) ?></h3>
                                        </div>
                                        <div class="bg-warning bg-opacity-10 p-3 rounded">
                                            <i class="fas fa-star text-warning"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Filter dan Pencarian -->
                    <div class="card border-0 shadow-sm mb-4">
                        <div class="card-body">
                            <form action="" method="GET" class="row g-3">
                                <div class="col-md-4">
                                    <label class="form-label small">Cari</label>
                                    <input type="text" name="search" class="form-control form-control-sm"
                                           placeholder="Cari nama atau deskripsi..."
                                           value="<?= $_GET['search'] ?? '' ?>">
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label small">Role</label>
                                    <select name="role" class="form-select form-select-sm">
                                        <option value="">Semua Role</option>
                                        <?php foreach ($roles as $role): ?>
                                        <option value="<?= $role['id'] ?>"
                                                <?= (isset($_GET['role']) && $_GET['role'] == $role['id']) ? 'selected' : '' ?>>
                                            <?= ucfirst($role['name']) ?>
                                        </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label small">Status</label>
                                    <select name="status" class="form-select form-select-sm">
                                        <option value="">Semua Status</option>
                                        <option value="active" <?= (isset($_GET['status']) && $_GET['status'] === 'active') ? 'selected' : '' ?>>
                                            Aktif
                                        </option>
                                        <option value="inactive" <?= (isset($_GET['status']) && $_GET['status'] === 'inactive') ? 'selected' : '' ?>>
                                            Tidak Aktif
                                        </option>
                                    </select>
                                </div>
                                <div class="col-md-2 d-flex align-items-end">
                                    <button type="submit" class="btn btn-primary btn-sm w-100 d-flex align-items-center justify-content-center gap-2">
                                        <i class="fas fa-search small"></i>
                                        <span>Filter</span>
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Import Modal -->
                    <div class="modal fade" id="importPermissionsModal" tabindex="-1">
                        <div class="modal-dialog modal-dialog-centered">
                            <div class="modal-content border-0 shadow">
                                <form method="POST" enctype="multipart/form-data">
                                    <input type="hidden" name="action" value="import">
                                    <div class="modal-header border-0 py-2">
                                        <h5 class="modal-title small fw-medium">Import Hak Akses</h5>
                                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                    </div>
                                    <div class="modal-body py-2">
                                        <div class="mb-3">
                                            <label class="form-label small">File Excel</label>
                                            <input type="file" class="form-control form-control-sm" name="import_file"
                                                   accept=".xlsx,.xls" required>
                                            <div class="form-text small">Format file: .xlsx atau .xls</div>
                                        </div>
                                        <div class="alert alert-info small">
                                            <h6 class="alert-heading small">Petunjuk Import:</h6>
                                            <ol class="mb-0 small">
                                                <li>Download template Excel terlebih dahulu</li>
                                                <li>Isi data sesuai format yang ada</li>
                                                <li>Upload file yang sudah diisi</li>
                                            </ol>
                                        </div>
                                    </div>
                                    <div class="modal-footer border-0 py-2">
                                        <button type="button" class="btn btn-light btn-sm" data-bs-dismiss="modal">Batal</button>
                                        <a href="templates/permissions_import_template.xlsx" class="btn btn-info btn-sm">
                                            <i class="fas fa-download small me-1"></i>Download Template
                                        </a>
                                        <button type="submit" class="btn btn-primary btn-sm">Import</button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- Tambahkan script untuk export -->
                    <script>
                    function exportPermissions() {
                        Swal.fire({
                            title: 'Mengekspor data...',
                            text: 'Mohon tunggu sebentar',
                            allowOutsideClick: false,
                            didOpen: () => {
                                Swal.showLoading();
                            }
                        });
                        window.location.href = 'export_permissions.php';
                    }

                    // Form validation
                    document.addEventListener('DOMContentLoaded', function() {
                        const forms = document.querySelectorAll('.needs-validation');
                        Array.from(forms).forEach(form => {
                            form.addEventListener('submit', event => {
                                if (!form.checkValidity()) {
                                    event.preventDefault();
                                    event.stopPropagation();
                                }
                                form.classList.add('was-validated');
                            }, false);
                        });

                        // Filter form handler
                        const filterForm = document.querySelector('form[method="GET"]');
                        if (filterForm) {
                            filterForm.addEventListener('submit', function(e) {
                                const search = this.querySelector('[name="search"]').value.trim();
                                const role = this.querySelector('[name="role"]').value;
                                const status = this.querySelector('[name="status"]').value;

                                if (!search && !role && !status) {
                                    e.preventDefault();
                                    window.location.href = 'permissions.php';
                                }
                            });
                        }
                    });

// JavaScript for Menu Management Section
document.addEventListener('DOMContentLoaded', function() {
    const roleFilterDropdown = document.getElementById('roleFilter');
    const menuManagementRoleIdInput = document.getElementById('menuManagementRoleId');
    const saveMenuButtonArea = document.getElementById('saveMenuButtonArea');
    const menuListInitialState = document.getElementById('menuListInitialState');
    const menuManagementForm = document.getElementById('menuManagementForm');

    if (roleFilterDropdown && menuManagementRoleIdInput && saveMenuButtonArea && menuListInitialState && menuManagementForm) {
        roleFilterDropdown.addEventListener('change', function() {
            const selectedRoleId = this.value;

            if (selectedRoleId) {
                // Set the role ID in the hidden input
                menuManagementRoleIdInput.value = selectedRoleId;

                // Show the form and save button area
                menuManagementForm.style.display = 'block';
                saveMenuButtonArea.style.display = 'block';

                // Hide initial state message
                menuListInitialState.style.display = 'none';

                // Get all menu checkboxes
                const menuCheckboxes = menuManagementForm.querySelectorAll('input[type="checkbox"]');

                // --- AJAX Call to load menu permissions ---
                // Uncheck all checkboxes first
                menuCheckboxes.forEach(checkbox => {
                    if (checkbox.id !== 'selectAllMenus') {
                        checkbox.checked = false;
                    }
                });

                // Fetch permissions for the selected role
                fetch(`permissions.php?action=get_role_menu_permissions&role_id=${selectedRoleId}`, {
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            const allowedMenus = data.allowed_menus;
                            // Check checkboxes for allowed menus
                            allowedMenus.forEach(menuId => {
                                const checkbox = document.getElementById(`menu_${menuId}`);
                                if (checkbox) {
                                    checkbox.checked = true;
                                }
                            });

                            // Update select all checkbox state
                            updateSelectAllMenuState();
                        } else {
                            console.error('Error loading menu permissions:', data.message);
                            alert('Error loading menu permissions: ' + data.message);
                        }
                    })
                    .catch(error => {
                        console.error('Fetch error:', error);
                        alert('Error loading menu permissions. Please try again.');
                    });
                // --- End AJAX Call ---

            } else {
                // Hide the form and save button area
                menuManagementForm.style.display = 'none';
                saveMenuButtonArea.style.display = 'none';

                // Clear the role ID
                menuManagementRoleIdInput.value = '';

                // Show initial state message
                menuListInitialState.style.display = 'block';
            }
        });
    }

    // Function to update select all menu state
    function updateSelectAllMenuState() {
        const selectAllMenus = document.getElementById('selectAllMenus');
        const menuCheckboxes = document.querySelectorAll('.menu-checkbox, .submenu-checkbox');

        if (selectAllMenus && menuCheckboxes.length > 0) {
            const allChecked = Array.from(menuCheckboxes).every(checkbox => checkbox.checked);
            const someChecked = Array.from(menuCheckboxes).some(checkbox => checkbox.checked);

            selectAllMenus.checked = allChecked;
            selectAllMenus.indeterminate = someChecked && !allChecked;
        }
    }
});

</script>

<!-- Tambahkan style -->
<style>
/* General Page Styling */
body {
    background-color: #f8f9fa; /* Light background */
    font-family: 'Segoe UI', Roboto, "Helvetica Neue", Arial, sans-serif; /* Modern font stack */
}

.container-fluid {
    padding: 1.5rem !important; /* Increased overall padding */
}

h5.card-title {
    font-size: 1.1rem; /* Slightly larger card titles */
    font-weight: 600; /* Bolder titles */
}

p.text-muted.small {
    font-size: 0.85rem; /* Slightly smaller descriptive text */
}

/* Card Styling */
.card {
    border: none; /* Remove default border */
    border-radius: 0.75rem; /* Rounded corners */
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05); /* Softer shadow */
    transition: all 0.3s ease; /* Smooth transition for hover */
    overflow: hidden; /* Ensure content respects border-radius */
}

.card:hover {
    transform: translateY(-3px); /* Lift card slightly on hover */
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1); /* More pronounced shadow on hover */
}

.card-header {
    background-color: #ffffff; /* White header background */
    border-bottom: 1px solid #e9ecef; /* Light border bottom */
    padding: 1rem 1.5rem; /* Consistent padding */
}

.card-body {
    padding: 1.5rem; /* Consistent padding */
}

/* Form Styling */
.form-label.small {
    font-size: 0.875rem; /* Standard label size */
    font-weight: 500; /* Slightly bolder labels */
    margin-bottom: 0.5rem; /* Space below labels */
}

.form-control-sm, .form-select-sm {
    padding: 0.5rem 0.75rem; /* Comfortable padding */
    border-radius: 0.5rem; /* Rounded form elements */
}

/* Table Styling */
.table th, .table td {
    padding: 1rem 1.5rem; /* Increased table padding */
}

.table-hover tbody tr:hover {
    background-color: #f1f3f5; /* Light hover effect */
}

.table thead th {
    border-bottom: 2px solid #e9ecef; /* More prominent header border */
}

/* Badge Styling */
.badge {
    font-size: 0.75em; /* Slightly smaller badges */
    padding: 0.4em 0.6em; /* Adjusted padding */
    border-radius: 0.25rem; /* Standard border radius */
}

/* Button Styling */
.btn {
    border-radius: 0.5rem; /* Rounded buttons */
    font-weight: 500; /* Medium font weight */
    transition: all 0.2s ease; /* Smooth transitions */
}

.btn-primary {
    background: linear-gradient(45deg, #007bff, #0056b3); /* Updated gradient */
    border: none;
    box-shadow: 0 2px 5px rgba(0, 123, 255, 0.2); /* Subtle shadow */
}

.btn-primary:hover {
    background: linear-gradient(45deg, #0056b3, #003f7f); /* Darker gradient on hover */
    box-shadow: 0 4px 10px rgba(0, 123, 255, 0.3); /* More pronounced shadow on hover */
    transform: translateY(-1px); /* Lift effect */
}

.btn-secondary {
     border-radius: 0.5rem; /* Rounded buttons */
    font-weight: 500; /* Medium font weight */
    transition: all 0.2s ease; /* Smooth transitions */
    background-color: #6c757d;
    border-color: #6c757d;
    color: white;
     box-shadow: 0 2px 5px rgba(108, 117, 125, 0.2); /* Subtle shadow */
}

.btn-secondary:hover {
    background-color: #5a6268;
    border-color: #5a6268;
    box-shadow: 0 4px 10px rgba(108, 117, 125, 0.3); /* More pronounced shadow on hover */
     transform: translateY(-1px); /* Lift effect */
}

.btn-success {
     border-radius: 0.5rem; /* Rounded buttons */
    font-weight: 500; /* Medium font weight */
    transition: all 0.2s ease; /* Smooth transitions */
     background-color: #28a745;
     border-color: #28a745;
     color: white;
     box-shadow: 0 2px 5px rgba(40, 167, 69, 0.2); /* Subtle shadow */
}

.btn-success:hover {
    background-color: #218838;
    border-color: #1e7e34;
     box-shadow: 0 4px 10px rgba(40, 167, 69, 0.3); /* More pronounced shadow on hover */
     transform: translateY(-1px); /* Lift effect */
}

.btn-info {
     border-radius: 0.5rem; /* Rounded buttons */
    font-weight: 500; /* Medium font weight */
    transition: all 0.2s ease; /* Smooth transitions */
     background-color: #17a2b8;
     border-color: #17a2b8;
     color: white;
     box-shadow: 0 2px 5px rgba(23, 162, 184, 0.2); /* Subtle shadow */
}

.btn-info:hover {
    background-color: #138496;
    border-color: #117a8b;
     box-shadow: 0 4px 10px rgba(23, 162, 184, 0.3); /* More pronounced shadow on hover */
     transform: translateY(-1px); /* Lift effect */
}

.btn-danger {
     border-radius: 0.5rem; /* Rounded buttons */
    font-weight: 500; /* Medium font weight */
    transition: all 0.2s ease; /* Smooth transitions */
     background-color: #dc3545;
     border-color: #dc3545;
     color: white;
     box-shadow: 0 2px 5px rgba(220, 53, 69, 0.2); /* Subtle shadow */
}

.btn-danger:hover {
    background-color: #c82333;
    border-color: #bd2130;
     box-shadow: 0 4px 10px rgba(220, 53, 69, 0.3); /* More pronounced shadow on hover */
     transform: translateY(-1px); /* Lift effect */
}

/* Modal Styling */
.modal-content {
     border-radius: 0.75rem; /* Rounded corners */
     border: none;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1); /* More pronounced shadow */
}

.modal-header {
    border-bottom: 1px solid #e9ecef; /* Light border */
     padding: 1rem 1.5rem; /* Consistent padding */
}

.modal-title {
    font-size: 1.1rem; /* Slightly larger title */
    font-weight: 600; /* Bolder title */
}

.modal-body {
     padding: 1.5rem; /* Consistent padding */
}

.modal-footer {
    border-top: 1px solid #e9ecef; /* Light border */
    padding: 1rem 1.5rem; /* Consistent padding */
}

/* Specific Adjustments for Permissions Page */
.form-check-input[type="checkbox"] {
    border-radius: 0.25em; /* Square checkboxes */
}

.form-check-input:checked[type="checkbox"] {
    background-color: #007bff; /* Primary color for checked */
    border-color: #007bff;
}

/* Dark Mode Adjustments */
[data-bs-theme="dark"] body {
    background-color: #1a202c; /* Darker background */
    color: #e2e8f0; /* Light text */
}

[data-bs-theme="dark"] .card-header {
    background-color: #2d3748; /* Darker header */
    border-bottom-color: #4a5568; /* Darker border */
}

[data-bs-theme="dark"] .card-body {
    background-color: #2d3748; /* Darker body */
}

[data-bs-theme="dark"] .text-muted {
    color: #a0aec0 !important; /* Lighter muted text */
}

[data-bs-theme="dark"] .form-control-sm,
[data-bs-theme="dark"] .form-select-sm {
    background-color: #4a5568; /* Darker input background */
    border-color: #4a5568;
    color: #e2e8f0; /* Light text */
}

[data-bs-theme="dark"] .form-control-sm::placeholder {
    color: #a0aec0; /* Lighter placeholder text */
}

[data-bs-theme="dark"] .table {
    color: #e2e8f0; /* Light table text */
}

[data-bs-theme="dark"] .table thead th {
    border-bottom-color: #4a5568; /* Darker header border */
}

[data-bs-theme="dark"] .table-hover tbody tr:hover {
    background-color: #4a5568; /* Darker hover effect */
}

[data-bs-theme="dark"] .modal-content {
     background-color: #2d3748; /* Darker modal background */
    color: #e2e8f0; /* Light text */
}

[data-bs-theme="dark"] .modal-header {
    border-bottom-color: #4a5568; /* Darker border */
}

[data-bs-theme="dark"] .modal-footer {
    border-top-color: #4a5568; /* Darker border */
}

[data-bs-theme="dark"] .btn-light {
    background-color: #4a5568; /* Darker light button */
    border-color: #4a5568;
    color: #e2e8f0; /* Light text */
}

[data-bs-theme="dark"] .btn-light:hover {
    background-color: #5a6268;
    border-color: #5a6268;
    color: #e2e8f0; /* Light text */
}

/* Ensure menu list area has proper top margin when shown */
#menuManagementSection.collapse.show {
    margin-top: 1.5rem; /* Add space above the section when expanded */
}

/* Style for menu management checkboxes */
#menuListArea .form-check {
    margin-bottom: 0.5rem; /* Space between menu items */
}

#menuListArea .form-check-label {
    display: flex;
    align-items: center;
    gap: 8px; /* Space between checkbox and label content */
}

#menuListArea .form-check-label i {
    font-size: 1rem; /* Icon size */
    width: 1.2rem; /* Fixed width for alignment */
    text-align: center;
}

#menuListArea .col-12.ps-4 .form-check-label i {
     font-size: 0.9rem; /* Slightly smaller icons for submenu */
     width: 1.1rem;
}

/* Adjust alert in menu list area */
#menuListArea .alert-info {
    padding: 0.75rem 1.25rem; /* Adjusted padding */
    border-radius: 0.5rem; /* Rounded corners */
}

/* Menu Management Styles */
.menu-list {
    max-height: 600px;
    overflow-y: auto;
}

.menu-group {
    border: 1px solid #e9ecef;
    border-radius: 0.5rem;
    overflow: hidden;
}

.menu-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.menu-header:hover {
    background-color: #e9ecef;
}

.submenu-items {
    background-color: #ffffff;
    border-top: 1px solid #e9ecef;
}

.submenu-items .form-check {
    padding-left: 2rem;
}

.toggle-submenu {
    padding: 0;
    color: #6c757d;
}

.toggle-submenu:hover {
    color: #495057;
}

.toggle-submenu[aria-expanded="false"] .fa-chevron-down {
    transform: rotate(-90deg);
    transition: transform 0.2s ease;
}

.form-check-input:checked {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

/* Dark mode adjustments */
[data-bs-theme="dark"] .menu-group {
    border-color: #495057;
}

[data-bs-theme="dark"] .menu-header {
    background-color: #343a40;
    border-color: #495057;
}

[data-bs-theme="dark"] .menu-header:hover {
    background-color: #495057;
}

[data-bs-theme="dark"] .submenu-items {
    background-color: #2d3748;
    border-color: #495057;
}

[data-bs-theme="dark"] .toggle-submenu {
    color: #adb5bd;
}

[data-bs-theme="dark"] .toggle-submenu:hover {
    color: #dee2e6;
}
</style>

<?php if ($flash = getFlashMessage()): ?>
<div class="alert alert-<?= $flash['type'] ?> alert-dismissible fade show mb-3 py-2" role="alert">
    <small><?= $flash['message'] ?></small>
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>
<?php endif; ?>

<!-- Menu Management Section Toggle -->
<div class="d-flex justify-content-between align-items-center mb-3">
    <div>
        <h5 class="mb-0 small fw-medium">Kelola Hak Akses</h5>
        <p class="text-muted small mb-0">Atur hak akses dan menu untuk setiap role</p>
    </div>
    <button class="btn btn-primary btn-sm d-flex align-items-center gap-2" type="button" data-bs-toggle="collapse" data-bs-target="#menuManagementSection" aria-expanded="false" aria-controls="menuManagementSection">
        <i class="fas fa-bars small"></i>
        <span>Kelola Tampilan Menu</span>
    </button>
</div>

<!-- Menu Management Section -->
<div class="collapse mb-4" id="menuManagementSection">
    <div class="card border-0 shadow-sm">
        <div class="card-header bg-white py-2">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h6 class="card-title mb-1 small fw-medium">Pengaturan Tampilan Menu per Role</h6>
                    <p class="text-muted small mb-0">Atur menu yang dapat diakses oleh setiap role</p>
                </div>
                <div class="d-flex gap-2">
                    <button type="button" class="btn btn-light btn-sm d-flex align-items-center gap-2" id="expandAllMenus">
                        <i class="fas fa-expand-alt small"></i>
                        <span>Expand All</span>
                    </button>
                    <button type="button" class="btn btn-light btn-sm d-flex align-items-center gap-2" id="collapseAllMenus">
                        <i class="fas fa-compress-alt small"></i>
                        <span>Collapse All</span>
                    </button>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="row g-3">
                <div class="col-md-4">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-header bg-white py-2">
                            <h6 class="card-title mb-0 small fw-medium">Pilih Role</h6>
                        </div>
                        <div class="card-body">
                            <select id="roleFilter" class="form-select form-select-sm mb-3">
                                <option value="">-- Pilih Role --</option>
                                <?php foreach ($roles as $role): ?>
                                    <option value="<?= $role['id'] ?>"><?= ucfirst(htmlspecialchars($role['name'])) ?></option>
                                <?php endforeach; ?>
                            </select>

                            <div id="menuListInitialState" class="text-center py-4">
                                <div class="text-muted">
                                    <i class="fas fa-users fa-2x mb-2"></i>
                                    <p class="mb-0 small">Pilih role dari dropdown di atas</p>
                                    <p class="mb-0 small text-muted">untuk mengelola tampilan menunya</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-8">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-header bg-white py-2">
                            <div class="d-flex justify-content-between align-items-center">
                                <h6 class="card-title mb-0 small fw-medium">Daftar Menu</h6>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="selectAllMenus">
                                    <label class="form-check-label small" for="selectAllMenus">Pilih Semua</label>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <form id="menuManagementForm" method="POST">
                                <input type="hidden" name="action" value="save_menu_permissions">
                                <input type="hidden" name="role_id" id="menuManagementRoleId">

                                <div id="menuListContent" class="menu-list" style="display: none;">
                                    <?php foreach ($sidebar_menus as $main_menu): ?>
                                        <div class="menu-group mb-3">
                                            <div class="menu-header d-flex align-items-center p-2 rounded bg-light">
                                                <div class="form-check">
                                                    <input class="form-check-input menu-checkbox" type="checkbox"
                                                           id="menu_<?= $main_menu['id'] ?>"
                                                           name="allowed_menus[]"
                                                           value="<?= $main_menu['id'] ?>"
                                                           checked>
                                                    <label class="form-check-label fw-medium" for="menu_<?= $main_menu['id'] ?>">
                                                        <i class="<?= $main_menu['icon'] ?> me-2"></i>
                                                        <?= $main_menu['label'] ?>
                                                    </label>
                                                </div>
                                                <?php if (isset($main_menu['submenu'])): ?>
                                                    <button type="button" class="btn btn-link btn-sm ms-auto toggle-submenu"
                                                            data-bs-toggle="collapse"
                                                            data-bs-target="#submenu_<?= $main_menu['id'] ?>">
                                                        <i class="fas fa-chevron-down"></i>
                                                    </button>
                                                <?php endif; ?>
                                            </div>

                                            <?php if (isset($main_menu['submenu'])): ?>
                                                <div class="collapse show" id="submenu_<?= $main_menu['id'] ?>">
                                                    <div class="submenu-items p-2">
                                                        <?php foreach ($main_menu['submenu'] as $submenu_item): ?>
                                                            <div class="form-check mb-2">
                                                                <input class="form-check-input submenu-checkbox" type="checkbox"
                                                                       id="menu_<?= $submenu_item['id'] ?>"
                                                                       name="allowed_menus[]"
                                                                       value="<?= $submenu_item['id'] ?>"
                                                                       checked>
                                                                <label class="form-check-label" for="menu_<?= $submenu_item['id'] ?>">
                                                                    <i class="<?= $submenu_item['icon'] ?> me-2"></i>
                                                                    <?= $submenu_item['label'] ?>
                                                                </label>
                                                            </div>
                                                        <?php endforeach; ?>
                                                    </div>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    <?php endforeach; ?>
                                </div>

                                <div class="mt-3" id="saveMenuButtonArea" style="display: none;">
                                    <button type="submit" class="btn btn-primary btn-sm w-100 d-flex align-items-center justify-content-center gap-2">
                                        <i class="fas fa-save small"></i>
                                        <span>Simpan Pengaturan Menu</span>
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

                    <!-- Tab Navigation -->
                    <ul class="nav nav-tabs mb-4" id="permissionTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="permissions-tab" data-bs-toggle="tab"
                                    data-bs-target="#permissions" type="button" role="tab">
                                <i class="fas fa-shield-alt me-2"></i>Hak Akses
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="roles-tab" data-bs-toggle="tab"
                                    data-bs-target="#roles" type="button" role="tab">
                                <i class="fas fa-users me-2"></i>Role & Permissions
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="menu-access-tab" data-bs-toggle="tab"
                                    data-bs-target="#menu-access" type="button" role="tab">
                                <i class="fas fa-bars me-2"></i>Akses Menu
                            </button>
                        </li>
                    </ul>

                    <!-- Tab Content -->
                    <div class="tab-content" id="permissionTabContent">
                        <!-- Permissions Tab -->
                        <div class="tab-pane fade show active" id="permissions" role="tabpanel">
                            <!-- Permissions List -->
                            <div class="table-responsive mb-4">
                                <table class="table table-hover align-middle mb-0 small">
                                    <thead>
                                        <tr>
                                            <th class="border-0 px-3 fw-medium">Nama Hak Akses</th>
                                            <th class="border-0 px-3 fw-medium">Deskripsi</th>
                                            <th class="border-0 px-3 fw-medium">Role yang Menggunakan</th>
                                            <th class="border-0 px-3 fw-medium text-center">Aksi</th>
                                        </tr>
                                    </thead>
                                    <tbody>
            <?php foreach ($permissions as $permission):
                // Get roles using this permission
                $stmt = $pdo->prepare("
                    SELECT r.name
                    FROM roles r
                    JOIN role_permissions rp ON r.id = rp.role_id
                    WHERE rp.permission_id = ?
                ");
                $stmt->execute([$permission['id']]);
                $usingRoles = $stmt->fetchAll(PDO::FETCH_COLUMN);
            ?>
                <tr>
                    <td class="fw-medium px-3"><?= htmlspecialchars($permission['name']) ?></td>
                    <td class="px-3"><?= htmlspecialchars($permission['description']) ?></td>
                    <td class="px-3">
                        <?php if (!empty($usingRoles)): ?>
                            <?= implode(', ', array_map('htmlspecialchars', $usingRoles)) ?>
                        <?php else: ?>
                            <span class="text-muted">Tidak ada</span>
                        <?php endif; ?>
                    </td>
                    <td class="text-center px-3">
                        <div class="btn-group">
                            <button type="button" class="btn btn-sm btn-primary"
                                    data-bs-toggle="modal"
                                    data-bs-target="#editPermissionModal"
                                    data-id="<?= $permission['id'] ?>"
                                    data-name="<?= htmlspecialchars($permission['name']) ?>"
                                    data-description="<?= htmlspecialchars($permission['description']) ?>">
                                <i class="fas fa-edit small"></i>
                            </button>
                            <?php if (empty($usingRoles)): ?>
                            <button type="button" class="btn btn-sm btn-outline-danger"
                                    data-bs-toggle="modal"
                                    data-bs-target="#deletePermissionModal"
                                    data-id="<?= $permission['id'] ?>"
                                    data-name="<?= htmlspecialchars($permission['name']) ?>">
                                <i class="fas fa-trash small"></i>
                            </button>
                            <?php endif; ?>
                        </div>
                    </td>
                </tr>
            <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- Roles Tab -->
                        <div class="tab-pane fade" id="roles" role="tabpanel">
                            <!-- Role Permissions Assignment -->
                            <h6 class="mb-3 small fw-medium">Tetapkan Hak Akses ke Role</h6>
                            <div class="row g-3">
                                <?php foreach ($roles as $role): ?>
                                    <div class="col-md-6">
                                        <div class="card border-0 shadow-sm h-100">
                                            <div class="card-header bg-white py-2">
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <h6 class="card-title mb-0 small fw-medium">
                                                        <?= ucfirst(htmlspecialchars($role['name'])) ?>
                                                    </h6>
                                                    <span class="badge bg-<?= $role['name'] === 'admin' ? 'primary' : 'success' ?>">
                                                        <?= $role['name'] === 'admin' ? 'Administrator' : 'Pengguna' ?>
                                                    </span>
                                                </div>
                                                <small class="text-muted d-block mt-1"><?= htmlspecialchars($role['description']) ?></small>
                                            </div>
                                            <div class="card-body">
                                                <form method="POST">
                                                    <input type="hidden" name="action" value="assign">
                                                    <input type="hidden" name="role_id" value="<?= $role['id'] ?>">

                                                    <div class="mb-3">
                                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                                            <label class="form-label small mb-0">Hak Akses</label>
                                                            <div class="form-check form-switch">
                                                                <input class="form-check-input" type="checkbox"
                                                                       id="selectAll_<?= $role['id'] ?>"
                                                                       onchange="toggleAllPermissions(<?= $role['id'] ?>)">
                                                                <label class="form-check-label small" for="selectAll_<?= $role['id'] ?>">
                                                                    Pilih Semua
                                                                </label>
                                                            </div>
                                                        </div>

                                                        <?php foreach ($permissions as $permission): ?>
                                                            <div class="form-check mb-2">
                                                                <input type="checkbox" class="form-check-input permission-checkbox"
                                                                       name="permissions[]"
                                                                       value="<?= $permission['id'] ?>"
                                                                       id="role_<?= $role['id'] ?>_perm_<?= $permission['id'] ?>"
                                                                       data-role-id="<?= $role['id'] ?>"
                                                                       <?= isset($rolePermissions[$role['id']]) && in_array($permission['id'], $rolePermissions[$role['id']]) ? 'checked' : '' ?>>
                                                                <label class="form-check-label small" for="role_<?= $role['id'] ?>_perm_<?= $permission['id'] ?>">
                                                                    <div class="d-flex flex-column">
                                                                        <span class="fw-medium"><?= ucwords(str_replace('_', ' ', htmlspecialchars($permission['name']))) ?></span>
                                                                        <small class="text-muted"><?= htmlspecialchars($permission['description']) ?></small>
                                                                    </div>
                                                                </label>
                                                            </div>
                                                        <?php endforeach; ?>
                                                    </div>

                                                    <button type="submit" class="btn btn-primary btn-sm w-100 d-flex align-items-center justify-content-center gap-2">
                                                        <i class="fas fa-save small"></i>
                                                        <span>Simpan Perubahan</span>
                                                    </button>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>

                        <!-- Menu Access Tab -->
                        <div class="tab-pane fade" id="menu-access" role="tabpanel">
                            <div class="card border-0 shadow-sm">
                                <div class="card-header bg-white py-3">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="card-title mb-1 small fw-medium">Kelola Akses Menu</h6>
                                            <p class="text-muted small mb-0">Atur menu yang dapat diakses oleh setiap role</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <form method="POST" id="newMenuForm">
                                        <input type="hidden" name="action" value="save_menu_permissions">

                                        <!-- Role Selection -->
                                        <div class="mb-4">
                                            <label class="form-label small fw-medium">Pilih Role:</label>
                                            <select class="form-select form-select-sm" name="role_id" id="newRoleSelect" style="max-width: 300px;" required>
                                                <option value="">-- Pilih Role --</option>
                                                <?php foreach ($roles as $role): ?>
                                                    <option value="<?= $role['id'] ?>"><?= ucfirst(htmlspecialchars($role['name'])) ?></option>
                                                <?php endforeach; ?>
                                            </select>
                                            <div class="form-text small text-muted">Pilih role untuk mengatur akses menu</div>
                                        </div>

                                        <!-- Menu Selection -->
                                        <div class="mb-4">
                                            <div class="d-flex justify-content-between align-items-center mb-3">
                                                <label class="form-label small fw-medium mb-0">Pilih Menu yang Dapat Diakses:</label>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="newSelectAll">
                                                    <label class="form-check-label small" for="newSelectAll">
                                                        Pilih Semua
                                                    </label>
                                                </div>
                                            </div>

                                            <div class="row g-3">
                                                <?php foreach ($sidebar_menus as $menu): ?>
                                                    <div class="col-md-6">
                                                        <div class="card border h-100">
                                                            <div class="card-body p-3">
                                                                <div class="form-check mb-2">
                                                                    <input class="form-check-input new-menu-checkbox" type="checkbox"
                                                                           name="allowed_menus[]"
                                                                           value="<?= $menu['id'] ?>"
                                                                           id="new_menu_<?= $menu['id'] ?>">
                                                                    <label class="form-check-label fw-medium" for="new_menu_<?= $menu['id'] ?>">
                                                                        <i class="<?= $menu['icon'] ?> me-2"></i>
                                                                        <?= htmlspecialchars($menu['label']) ?>
                                                                    </label>
                                                                </div>

                                                                <?php if (isset($menu['submenu']) && !empty($menu['submenu'])): ?>
                                                                    <div class="submenu-items ms-3">
                                                                        <?php foreach ($menu['submenu'] as $submenu): ?>
                                                                            <div class="form-check mb-1">
                                                                                <input class="form-check-input new-submenu-checkbox" type="checkbox"
                                                                                       name="allowed_menus[]"
                                                                                       value="<?= $submenu['id'] ?>"
                                                                                       id="new_menu_<?= $submenu['id'] ?>">
                                                                                <label class="form-check-label small" for="new_menu_<?= $submenu['id'] ?>">
                                                                                    <i class="<?= $submenu['icon'] ?> me-2"></i>
                                                                                    <?= htmlspecialchars($submenu['label']) ?>
                                                                                </label>
                                                                            </div>
                                                                        <?php endforeach; ?>
                                                                    </div>
                                                                <?php endif; ?>
                                                            </div>
                                                        </div>
                                                    </div>
                                                <?php endforeach; ?>
                                            </div>
                                        </div>

                                        <!-- Submit Button -->
                                        <div class="d-flex gap-2">
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fas fa-save me-2"></i>Simpan Pengaturan Menu
                                            </button>
                                            <button type="button" class="btn btn-secondary" onclick="loadExistingPermissions()">
                                                <i class="fas fa-sync me-2"></i>Muat Pengaturan Saat Ini
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Permission Modal -->
<div class="modal fade" id="addPermissionModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content border-0 shadow">
            <form method="POST" class="needs-validation" novalidate>
                <input type="hidden" name="action" value="add">
                <div class="modal-header border-0 py-2">
                    <h5 class="modal-title small fw-medium">Tambah Hak Akses</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body py-2">
                    <div class="mb-2">
                        <label class="form-label small mb-1">Nama Hak Akses</label>
                        <input type="text" name="name" class="form-control form-control-sm" required
                               minlength="3" maxlength="50" pattern="[a-z_]+">
                        <div class="invalid-feedback small">
                            Nama hak akses harus diisi (3-50 karakter, hanya huruf kecil dan underscore)
                        </div>
                    </div>
                    <div class="mb-2">
                        <label class="form-label small mb-1">Deskripsi</label>
                        <textarea name="description" class="form-control form-control-sm" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer border-0 py-2">
                    <button type="button" class="btn btn-light btn-sm" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary btn-sm">Simpan</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Permission Modal -->
<div class="modal fade" id="editPermissionModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content border-0 shadow">
            <form method="POST" class="needs-validation" novalidate>
                <input type="hidden" name="action" value="edit">
                <input type="hidden" name="id" id="editPermissionId">
                <div class="modal-header border-0 py-2">
                    <h5 class="modal-title small fw-medium">Edit Hak Akses</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body py-2">
                    <div class="mb-2">
                        <label class="form-label small mb-1">Nama Hak Akses</label>
                        <input type="text" name="name" id="editPermissionName" class="form-control form-control-sm" required
                               minlength="3" maxlength="50" pattern="[a-z_]+">
                        <div class="invalid-feedback small">
                            Nama hak akses harus diisi (3-50 karakter, hanya huruf kecil dan underscore)
                        </div>
                    </div>
                    <div class="mb-2">
                        <label class="form-label small mb-1">Deskripsi</label>
                        <textarea name="description" id="editPermissionDescription" class="form-control form-control-sm" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer border-0 py-2">
                    <button type="button" class="btn btn-light btn-sm" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary btn-sm">Simpan</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Permission Modal -->
<div class="modal fade" id="deletePermissionModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content border-0 shadow">
            <form method="POST">
                <input type="hidden" name="action" value="delete">
                <input type="hidden" name="id" id="deletePermissionId">
                <div class="modal-header border-0 py-2">
                    <h5 class="modal-title small fw-medium">Hapus Hak Akses</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body py-2">
                    <p class="small mb-0">Apakah Anda yakin ingin menghapus hak akses <strong id="deletePermissionName"></strong>?</p>
                </div>
                <div class="modal-footer border-0 py-2">
                    <button type="button" class="btn btn-light btn-sm" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-danger btn-sm">Hapus</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Function to toggle all permissions for a role
function toggleAllPermissions(roleId) {
    const selectAll = document.getElementById('selectAll_' + roleId);
    const checkboxes = document.querySelectorAll(`.permission-checkbox[data-role-id="${roleId}"]`);

    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
}

// Function to update select all checkbox state
function updateSelectAllState(roleId) {
    const checkboxes = document.querySelectorAll(`.permission-checkbox[data-role-id="${roleId}"]`);
    const selectAll = document.getElementById('selectAll_' + roleId);

    const allChecked = Array.from(checkboxes).every(checkbox => checkbox.checked);
    const someChecked = Array.from(checkboxes).some(checkbox => checkbox.checked);

    selectAll.checked = allChecked;
    selectAll.indeterminate = someChecked && !allChecked;
}

// Add event listeners to all permission checkboxes
document.querySelectorAll('.permission-checkbox').forEach(checkbox => {
    checkbox.addEventListener('change', function() {
        const roleId = this.getAttribute('data-role-id');
        updateSelectAllState(roleId);
    });
});

// Initialize select all states
document.querySelectorAll('[id^="selectAll_"]').forEach(selectAll => {
    const roleId = selectAll.id.split('_')[1];
    updateSelectAllState(roleId);
});

document.addEventListener('DOMContentLoaded', function() {
    // Edit Permission Modal
    const editPermissionModal = document.getElementById('editPermissionModal');
    if (editPermissionModal) {
        editPermissionModal.addEventListener('show.bs.modal', function(event) {
            const button = event.relatedTarget;
            const id = button.getAttribute('data-id');
            const name = button.getAttribute('data-name');
            const description = button.getAttribute('data-description');

            document.getElementById('editPermissionId').value = id;
            document.getElementById('editPermissionName').value = name;
            document.getElementById('editPermissionDescription').value = description;
        });
    }

    // Delete Permission Modal
    const deletePermissionModal = document.getElementById('deletePermissionModal');
    if (deletePermissionModal) {
        deletePermissionModal.addEventListener('show.bs.modal', function(event) {
            const button = event.relatedTarget;
            const id = button.getAttribute('data-id');
            const name = button.getAttribute('data-name');

            document.getElementById('deletePermissionId').value = id;
            document.getElementById('deletePermissionName').textContent = name;
        });
    }

    // Form validation
    const forms = document.querySelectorAll('.needs-validation');
    Array.from(forms).forEach(form => {
        form.addEventListener('submit', event => {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        }, false);
    });

    // JavaScript for Menu Management Section
    const roleFilterDropdown = document.getElementById('roleFilter');
    const menuManagementRoleIdInput = document.getElementById('menuManagementRoleId');
    const menuListInitialState = document.getElementById('menuListInitialState');
    const menuManagementForm = document.getElementById('menuManagementForm');

    if (roleFilterDropdown && menuManagementRoleIdInput && menuListInitialState && menuManagementForm) {
        roleFilterDropdown.addEventListener('change', function() {
            const selectedRoleId = this.value;

            if (selectedRoleId) {
                // Set the role ID in the hidden input
                menuManagementRoleIdInput.value = selectedRoleId;

                // Show the form
                menuManagementForm.style.display = 'block';

                // Hide initial state message
                menuListInitialState.style.display = 'none';

                // Get all menu checkboxes
                const menuCheckboxes = menuManagementForm.querySelectorAll('input[type="checkbox"]');

                // --- AJAX Call to load menu permissions ---
                // Uncheck all checkboxes first
                menuCheckboxes.forEach(checkbox => {
                    if (checkbox.id !== 'selectAllMenus') {
                        checkbox.checked = false;
                    }
                });

                // Fetch permissions for the selected role
                fetch(`permissions.php?action=get_role_menu_permissions&role_id=${selectedRoleId}`, {
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            const allowedMenus = data.allowed_menus;
                            // Check checkboxes for allowed menus
                            allowedMenus.forEach(menuId => {
                                const checkbox = document.getElementById(`menu_${menuId}`);
                                if (checkbox) {
                                    checkbox.checked = true;
                                }
                            });

                            // Update select all checkbox state
                            updateSelectAllMenuStateForRole();
                        } else {
                            console.error('Error loading menu permissions:', data.message);
                        }
                    })
                    .catch(error => {
                        console.error('Fetch error:', error);
                    });

            } else {
                // Hide the form
                menuManagementForm.style.display = 'none';

                // Clear the role ID
                menuManagementRoleIdInput.value = '';

                // Show initial state message
                menuListInitialState.style.display = 'block';
            }
        });
    }

    // Function to update select all menu state for role management
    function updateSelectAllMenuStateForRole() {
        const selectAllMenus = document.getElementById('selectAllMenus');
        const menuCheckboxes = document.querySelectorAll('.menu-checkbox, .submenu-checkbox');

        if (selectAllMenus && menuCheckboxes.length > 0) {
            const allChecked = Array.from(menuCheckboxes).every(checkbox => checkbox.checked);
            const someChecked = Array.from(menuCheckboxes).some(checkbox => checkbox.checked);

            selectAllMenus.checked = allChecked;
            selectAllMenus.indeterminate = someChecked && !allChecked;
        }
    }

    // Form validation for menu permissions - Enhanced
    const menuPermissionForm = document.getElementById('menuPermissionForm');
    const roleWarning = document.getElementById('roleWarning');
    const roleFilterDropdown = document.getElementById('roleFilter');
    const menuManagementRoleIdInput = document.getElementById('menuManagementRoleId');

    // Update role_id when dropdown changes
    if (roleFilterDropdown && menuManagementRoleIdInput) {
        roleFilterDropdown.addEventListener('change', function() {
            const selectedRoleId = this.value;
            console.log('Role selected:', selectedRoleId); // Debug
            menuManagementRoleIdInput.value = selectedRoleId;

            // Hide warning if role is selected
            if (selectedRoleId && roleWarning) {
                roleWarning.style.display = 'none';
            }
        });
    }

    // Form submission validation
    if (menuPermissionForm) {
        menuPermissionForm.addEventListener('submit', function(e) {
            console.log('Form submitted'); // Debug

            const roleId = menuManagementRoleIdInput ? menuManagementRoleIdInput.value : '';
            const roleFilter = roleFilterDropdown ? roleFilterDropdown.value : '';

            console.log('Role ID from hidden input:', roleId); // Debug
            console.log('Role ID from dropdown:', roleFilter); // Debug

            // Update role_id from dropdown if not set
            if (!roleId && roleFilter && menuManagementRoleIdInput) {
                menuManagementRoleIdInput.value = roleFilter;
                console.log('Updated role ID to:', roleFilter); // Debug
            }

            // Validate role selection
            const finalRoleId = menuManagementRoleIdInput ? menuManagementRoleIdInput.value : '';
            console.log('Final role ID:', finalRoleId); // Debug

            if (!finalRoleId) {
                e.preventDefault();
                e.stopPropagation();

                if (roleWarning) {
                    roleWarning.style.display = 'block';
                }

                if (roleFilterDropdown) {
                    roleFilterDropdown.focus();
                }

                // Hide warning after 5 seconds
                setTimeout(() => {
                    if (roleWarning) {
                        roleWarning.style.display = 'none';
                    }
                }, 5000);

                alert('Silakan pilih role terlebih dahulu!'); // Additional feedback
                return false;
            } else {
                if (roleWarning) {
                    roleWarning.style.display = 'none';
                }
                console.log('Form validation passed, submitting...'); // Debug
            }
        });
    }
});

// Expand/Collapse All functionality
const expandAllBtn = document.getElementById('expandAllMenus');
const collapseAllBtn = document.getElementById('collapseAllMenus');
const submenus = document.querySelectorAll('.collapse');

if (expandAllBtn && collapseAllBtn) {
    expandAllBtn.addEventListener('click', function() {
        submenus.forEach(submenu => {
            if (!submenu.classList.contains('show')) {
                submenu.classList.add('show');
            }
        });
    });

    collapseAllBtn.addEventListener('click', function() {
        submenus.forEach(submenu => {
            if (submenu.classList.contains('show')) {
                submenu.classList.remove('show');
            }
        });
    });
}

// Select All functionality - Updated to work properly
document.addEventListener('DOMContentLoaded', function() {
    const selectAllMenus = document.getElementById('selectAllMenus');

    if (selectAllMenus) {
        selectAllMenus.addEventListener('change', function() {
            const isChecked = this.checked;
            // Get all menu and submenu checkboxes dynamically
            const allMenuCheckboxes = document.querySelectorAll('.menu-checkbox, .submenu-checkbox');

            allMenuCheckboxes.forEach(checkbox => {
                checkbox.checked = isChecked;
            });
        });
    }

    // Update select all state when individual checkboxes change
    function updateSelectAllState() {
        const selectAllMenus = document.getElementById('selectAllMenus');
        const allMenuCheckboxes = document.querySelectorAll('.menu-checkbox, .submenu-checkbox');

        if (selectAllMenus && allMenuCheckboxes.length > 0) {
            const allChecked = Array.from(allMenuCheckboxes).every(checkbox => checkbox.checked);
            const someChecked = Array.from(allMenuCheckboxes).some(checkbox => checkbox.checked);

            selectAllMenus.checked = allChecked;
            selectAllMenus.indeterminate = someChecked && !allChecked;
        }
    }

    // Add event listeners to all menu checkboxes for updating select all state
    document.addEventListener('change', function(e) {
        if (e.target.classList.contains('menu-checkbox') || e.target.classList.contains('submenu-checkbox')) {
            updateSelectAllState();
        }
    });
});

// Parent-Child checkbox relationship - Updated to work properly
document.addEventListener('DOMContentLoaded', function() {
    // Parent-Child checkbox relationship
    document.addEventListener('change', function(e) {
        if (e.target.classList.contains('menu-checkbox')) {
            // When parent menu is checked/unchecked, update all its submenus
            const parentCard = e.target.closest('.card');
            if (parentCard) {
                const submenuCheckboxes = parentCard.querySelectorAll('.submenu-checkbox');
                submenuCheckboxes.forEach(subCheckbox => {
                    subCheckbox.checked = e.target.checked;
                });
            }
        }

        if (e.target.classList.contains('submenu-checkbox')) {
            // When submenu is checked/unchecked, update parent menu state
            const parentCard = e.target.closest('.card');
            if (parentCard) {
                const parentCheckbox = parentCard.querySelector('.menu-checkbox');
                const submenuCheckboxes = parentCard.querySelectorAll('.submenu-checkbox');

                if (parentCheckbox && submenuCheckboxes.length > 0) {
                    const allChecked = Array.from(submenuCheckboxes).every(cb => cb.checked);
                    const someChecked = Array.from(submenuCheckboxes).some(cb => cb.checked);

                    parentCheckbox.checked = allChecked;
                    parentCheckbox.indeterminate = someChecked && !allChecked;
                }
            }
        }
    });
});

// New Menu Management JavaScript - Simple and Clean
document.addEventListener('DOMContentLoaded', function() {
    // New Select All functionality
    const newSelectAll = document.getElementById('newSelectAll');
    if (newSelectAll) {
        newSelectAll.addEventListener('change', function() {
            const isChecked = this.checked;
            const allCheckboxes = document.querySelectorAll('.new-menu-checkbox, .new-submenu-checkbox');
            allCheckboxes.forEach(checkbox => {
                checkbox.checked = isChecked;
            });
        });
    }

    // Update select all state when individual checkboxes change
    document.addEventListener('change', function(e) {
        if (e.target.classList.contains('new-menu-checkbox') || e.target.classList.contains('new-submenu-checkbox')) {
            updateNewSelectAllState();
        }
    });

    function updateNewSelectAllState() {
        const selectAll = document.getElementById('newSelectAll');
        const allCheckboxes = document.querySelectorAll('.new-menu-checkbox, .new-submenu-checkbox');

        if (selectAll && allCheckboxes.length > 0) {
            const allChecked = Array.from(allCheckboxes).every(cb => cb.checked);
            const someChecked = Array.from(allCheckboxes).some(cb => cb.checked);

            selectAll.checked = allChecked;
            selectAll.indeterminate = someChecked && !allChecked;
        }
    }

    // Parent-child relationship for new menu
    document.addEventListener('change', function(e) {
        if (e.target.classList.contains('new-menu-checkbox')) {
            // When parent menu is checked/unchecked, update all its submenus
            const parentCard = e.target.closest('.card');
            if (parentCard) {
                const submenuCheckboxes = parentCard.querySelectorAll('.new-submenu-checkbox');
                submenuCheckboxes.forEach(subCheckbox => {
                    subCheckbox.checked = e.target.checked;
                });
            }
            updateNewSelectAllState();
        }

        if (e.target.classList.contains('new-submenu-checkbox')) {
            // When submenu is checked/unchecked, update parent menu state
            const parentCard = e.target.closest('.card');
            if (parentCard) {
                const parentCheckbox = parentCard.querySelector('.new-menu-checkbox');
                const submenuCheckboxes = parentCard.querySelectorAll('.new-submenu-checkbox');

                if (parentCheckbox && submenuCheckboxes.length > 0) {
                    const allChecked = Array.from(submenuCheckboxes).every(cb => cb.checked);
                    const someChecked = Array.from(submenuCheckboxes).some(cb => cb.checked);

                    parentCheckbox.checked = allChecked;
                    parentCheckbox.indeterminate = someChecked && !allChecked;
                }
            }
            updateNewSelectAllState();
        }
    });
});

// Load existing permissions function
function loadExistingPermissions() {
    const roleSelect = document.getElementById('newRoleSelect');
    const roleId = roleSelect.value;

    if (!roleId) {
        alert('Silakan pilih role terlebih dahulu!');
        roleSelect.focus();
        return;
    }

    // Clear all checkboxes first
    const allCheckboxes = document.querySelectorAll('.new-menu-checkbox, .new-submenu-checkbox');
    allCheckboxes.forEach(checkbox => {
        checkbox.checked = false;
    });

    // Fetch existing permissions
    fetch(`permissions.php?action=get_role_menu_permissions&role_id=${roleId}`, {
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const allowedMenus = data.allowed_menus;
            allowedMenus.forEach(menuId => {
                const checkbox = document.getElementById(`new_menu_${menuId}`);
                if (checkbox) {
                    checkbox.checked = true;
                }
            });

            // Update select all state
            const selectAll = document.getElementById('newSelectAll');
            const allCheckboxes = document.querySelectorAll('.new-menu-checkbox, .new-submenu-checkbox');
            const allChecked = Array.from(allCheckboxes).every(cb => cb.checked);
            const someChecked = Array.from(allCheckboxes).some(cb => cb.checked);

            if (selectAll) {
                selectAll.checked = allChecked;
                selectAll.indeterminate = someChecked && !allChecked;
            }

            alert('Pengaturan menu saat ini berhasil dimuat!');
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Terjadi kesalahan saat memuat pengaturan menu.');
    });
}
</script>

<?php include 'includes/views/layouts/footer.php'; ?>