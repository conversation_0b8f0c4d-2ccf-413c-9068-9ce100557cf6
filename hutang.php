<?php
require_once 'includes/config/database.php';
require_once 'includes/helpers/functions.php';
require_once 'includes/helpers/notifications.php';

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isLoggedIn()) {
    setFlashMessage('danger', 'Silakan login terlebih dahulu');
    redirect('/keuangan/login.php');
}

$currentUser = getCurrentUser();
$currentPage = 'hutang';

// Create hutang_piutang table if not exists
try {
    $pdo->exec("CREATE TABLE IF NOT EXISTS hutang_piutang (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        jenis <PERSON>('hutang', 'piutang') NOT NULL,
        nama_pihak VARCHAR(255) NOT NULL,
        jumlah DECIMAL(15,2) NOT NULL,
        jumlah_terbayar DECIMAL(15,2) DEFAULT 0,
        tanggal_transaksi DATE NOT NULL,
        tanggal_jatuh_tempo DATE,
        keterangan TEXT,
        status ENUM('belum_lunas', 'lunas', 'terlambat') DEFAULT 'belum_lunas',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    )");
} catch (PDOException $e) {
    error_log("Error creating hutang_piutang table: " . $e->getMessage());
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        try {
            switch ($_POST['action']) {
                case 'add':
                    // Validasi input
                    $errors = [];
                    
                    if (empty($_POST['jenis'])) {
                        $errors[] = 'Jenis harus dipilih';
                    }
                    
                    if (empty($_POST['nama_pihak'])) {
                        $errors[] = 'Nama pihak harus diisi';
                    }
                    
                    if (empty($_POST['jumlah'])) {
                        $errors[] = 'Jumlah harus diisi';
                    }
                    
                    if (empty($_POST['tanggal_transaksi'])) {
                        $errors[] = 'Tanggal transaksi harus diisi';
                    }
                    
                    if (empty($errors)) {
                        try {
                            // Format jumlah (hapus format angka)
                            $jumlah = str_replace(['.', ','], '', $_POST['jumlah']);
                            $jumlah_terbayar = !empty($_POST['jumlah_terbayar']) ? str_replace(['.', ','], '', $_POST['jumlah_terbayar']) : 0;
                            
                            // Format tanggal ke format Y-m-d
                            $tanggal_transaksi = date('Y-m-d', strtotime($_POST['tanggal_transaksi']));
                            $tanggal_jatuh_tempo = !empty($_POST['tanggal_jatuh_tempo']) ? date('Y-m-d', strtotime($_POST['tanggal_jatuh_tempo'])) : null;
                            
                            // Tentukan status berdasarkan jumlah terbayar
                            $status = 'belum_lunas';
                            if ($jumlah_terbayar >= $jumlah) {
                                $status = 'lunas';
                            } elseif ($tanggal_jatuh_tempo && $tanggal_jatuh_tempo < date('Y-m-d') && $jumlah_terbayar < $jumlah) {
                                $status = 'terlambat';
                            }
                            
                            // Insert hutang/piutang dengan prepared statement
                            $sql = "INSERT INTO hutang_piutang (user_id, jenis, nama_pihak, jumlah, jumlah_terbayar, tanggal_transaksi, tanggal_jatuh_tempo, keterangan, status, created_at) 
                                   VALUES (:user_id, :jenis, :nama_pihak, :jumlah, :jumlah_terbayar, :tanggal_transaksi, :tanggal_jatuh_tempo, :keterangan, :status, NOW())";
                            
                            $stmt = $pdo->prepare($sql);
                            
                            $params = [
                                ':user_id' => $currentUser['id'],
                                ':jenis' => $_POST['jenis'],
                                ':nama_pihak' => $_POST['nama_pihak'],
                                ':jumlah' => $jumlah,
                                ':jumlah_terbayar' => $jumlah_terbayar,
                                ':tanggal_transaksi' => $tanggal_transaksi,
                                ':tanggal_jatuh_tempo' => $tanggal_jatuh_tempo,
                                ':keterangan' => $_POST['keterangan'],
                                ':status' => $status
                            ];
                            
                            $result = $stmt->execute($params);
                            
                            if ($result) {
                                // Log aktivitas
                                logActivity($currentUser['id'], sprintf(
                                    'Menambahkan %s %s sebesar %s',
                                    $_POST['jenis'],
                                    $_POST['nama_pihak'],
                                    formatRupiah($jumlah)
                                ));
                                
                                setFlashMessage('success', ucfirst($_POST['jenis']) . ' berhasil ditambahkan');
                                redirect('/hutang.php');
                            } else {
                                throw new Exception('Gagal menyimpan data');
                            }
                        } catch (Exception $e) {
                            setFlashMessage('danger', $e->getMessage());
                        }
                    } else {
                        setFlashMessage('danger', implode('<br>', $errors));
                    }
                    break;

                case 'update':
                    if (empty($_POST['id'])) {
                        setFlashMessage('danger', 'ID tidak valid');
                        break;
                    }

                    // Format jumlah (hapus format angka)
                    $jumlah = str_replace(['.', ','], '', $_POST['jumlah']);
                    $jumlah_terbayar = str_replace(['.', ','], '', $_POST['jumlah_terbayar']);
                    
                    // Format tanggal ke format Y-m-d
                    $tanggal_transaksi = date('Y-m-d', strtotime($_POST['tanggal_transaksi']));
                    $tanggal_jatuh_tempo = !empty($_POST['tanggal_jatuh_tempo']) ? date('Y-m-d', strtotime($_POST['tanggal_jatuh_tempo'])) : null;
                    
                    // Tentukan status berdasarkan jumlah terbayar
                    $status = 'belum_lunas';
                    if ($jumlah_terbayar >= $jumlah) {
                        $status = 'lunas';
                    } elseif ($tanggal_jatuh_tempo && $tanggal_jatuh_tempo < date('Y-m-d') && $jumlah_terbayar < $jumlah) {
                        $status = 'terlambat';
                    }
                    
                    $stmt = $pdo->prepare("
                        UPDATE hutang_piutang 
                        SET jenis = ?, nama_pihak = ?, jumlah = ?, jumlah_terbayar = ?, tanggal_transaksi = ?, tanggal_jatuh_tempo = ?, keterangan = ?, status = ?
                        WHERE id = ? AND user_id = ?
                    ");
                    
                    $result = $stmt->execute([
                        $_POST['jenis'],
                        $_POST['nama_pihak'],
                        $jumlah,
                        $jumlah_terbayar,
                        $tanggal_transaksi,
                        $tanggal_jatuh_tempo,
                        $_POST['keterangan'],
                        $status,
                        $_POST['id'],
                        $currentUser['id']
                    ]);

                    if ($result) {
                        setFlashMessage('success', 'Data berhasil diperbarui');
                    } else {
                        setFlashMessage('danger', 'Gagal memperbarui data');
                    }
                    break;

                case 'delete':
                    if (empty($_POST['id'])) {
                        setFlashMessage('danger', 'ID tidak valid');
                        break;
                    }

                    $stmt = $pdo->prepare("DELETE FROM hutang_piutang WHERE id = ? AND user_id = ?");
                    $result = $stmt->execute([$_POST['id'], $currentUser['id']]);

                    if ($result) {
                        setFlashMessage('success', 'Data berhasil dihapus');
                    } else {
                        setFlashMessage('danger', 'Gagal menghapus data');
                    }
                    break;

                case 'bayar':
                    if (empty($_POST['id']) || empty($_POST['jumlah_bayar'])) {
                        setFlashMessage('danger', 'Data pembayaran tidak lengkap');
                        break;
                    }

                    $jumlah_bayar = str_replace(['.', ','], '', $_POST['jumlah_bayar']);
                    
                    // Get current data
                    $stmt = $pdo->prepare("SELECT * FROM hutang_piutang WHERE id = ? AND user_id = ?");
                    $stmt->execute([$_POST['id'], $currentUser['id']]);
                    $data = $stmt->fetch();
                    
                    if ($data) {
                        $jumlah_terbayar_baru = $data['jumlah_terbayar'] + $jumlah_bayar;
                        
                        // Tentukan status baru
                        $status_baru = 'belum_lunas';
                        if ($jumlah_terbayar_baru >= $data['jumlah']) {
                            $status_baru = 'lunas';
                            $jumlah_terbayar_baru = $data['jumlah']; // Tidak boleh lebih dari jumlah total
                        } elseif ($data['tanggal_jatuh_tempo'] && $data['tanggal_jatuh_tempo'] < date('Y-m-d')) {
                            $status_baru = 'terlambat';
                        }
                        
                        $stmt = $pdo->prepare("UPDATE hutang_piutang SET jumlah_terbayar = ?, status = ? WHERE id = ? AND user_id = ?");
                        $result = $stmt->execute([$jumlah_terbayar_baru, $status_baru, $_POST['id'], $currentUser['id']]);
                        
                        if ($result) {
                            setFlashMessage('success', 'Pembayaran berhasil dicatat');
                        } else {
                            setFlashMessage('danger', 'Gagal mencatat pembayaran');
                        }
                    }
                    break;
            }
        } catch (PDOException $e) {
            setFlashMessage('danger', 'Terjadi kesalahan. Silakan coba lagi.');
        }
        redirect('/hutang.php');
    }
}

// Get data with pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$perPage = 10;
$offset = ($page - 1) * $perPage;

// Get total records with filters
$where = ["user_id = ?"];
$params = [$currentUser['id']];

if (!empty($_GET['jenis'])) {
    $where[] = "jenis = ?";
    $params[] = $_GET['jenis'];
}

if (!empty($_GET['status'])) {
    $where[] = "status = ?";
    $params[] = $_GET['status'];
}

$whereClause = implode(" AND ", $where);

// Get total records
$stmt = $pdo->prepare("
    SELECT COUNT(*) as total 
    FROM hutang_piutang
    WHERE $whereClause
");
$stmt->execute($params);
$totalRecords = $stmt->fetch()['total'];
$totalPages = ceil($totalRecords / $perPage);

// Get data
$stmt = $pdo->prepare("
    SELECT * FROM hutang_piutang
    WHERE $whereClause
    ORDER BY tanggal_transaksi DESC, created_at DESC
    LIMIT ? OFFSET ?
");

// Create new array for pagination parameters
$paginationParams = $params;
$paginationParams[] = $perPage;
$paginationParams[] = $offset;

$stmt->execute($paginationParams);
$hutang_piutang = $stmt->fetchAll();

// Get statistics
$stmt = $pdo->prepare("
    SELECT 
        SUM(CASE WHEN jenis = 'hutang' AND status != 'lunas' THEN (jumlah - jumlah_terbayar) ELSE 0 END) as total_hutang,
        SUM(CASE WHEN jenis = 'piutang' AND status != 'lunas' THEN (jumlah - jumlah_terbayar) ELSE 0 END) as total_piutang,
        COUNT(CASE WHEN status = 'terlambat' THEN 1 END) as total_terlambat,
        COUNT(CASE WHEN status = 'lunas' THEN 1 END) as total_lunas
    FROM hutang_piutang
    WHERE user_id = ?
");
$stmt->execute([$currentUser['id']]);
$stats = $stmt->fetch();

// Include header and sidebar
require_once 'includes/views/layouts/header.php';
require_once 'includes/views/layouts/sidebar.php';
?>

<div class="modern-layout">
    <div class="modern-container">
        <!-- Modern Page Header -->
        <div class="modern-page-header modern-flex modern-justify-between modern-items-center modern-mb-xl">
            <div>
                <h1 class="modern-page-title modern-mb-0">Hutang & Piutang</h1>
                <p class="modern-page-subtitle">Kelola dan pantau hutang serta piutang Anda dengan mudah</p>
            </div>
            <div class="modern-page-actions">
                <button type="button" class="modern-btn modern-btn-primary" data-bs-toggle="modal" data-bs-target="#addModal">
                    <i class="fas fa-plus"></i>
                    Tambah Data
                </button>
            </div>
        </div>

        <!-- Modern Statistics Cards -->
        <div class="modern-grid modern-grid-cols-4 modern-gap-lg modern-mb-xl">
            <div class="modern-stats-card modern-stats-danger">
                <div class="modern-stats-content">
                    <div class="modern-stats-info">
                        <div class="modern-stats-label">Total Hutang</div>
                        <div class="modern-stats-value"><?= formatRupiah($stats['total_hutang'] ?? 0) ?></div>
                        <div class="modern-stats-meta">Yang harus dibayar</div>
                    </div>
                    <div class="modern-stats-icon">
                        <i class="fas fa-hand-holding-usd"></i>
                    </div>
                </div>
                <div class="modern-stats-glow"></div>
            </div>

            <div class="modern-stats-card modern-stats-success">
                <div class="modern-stats-content">
                    <div class="modern-stats-info">
                        <div class="modern-stats-label">Total Piutang</div>
                        <div class="modern-stats-value"><?= formatRupiah($stats['total_piutang'] ?? 0) ?></div>
                        <div class="modern-stats-meta">Yang akan diterima</div>
                    </div>
                    <div class="modern-stats-icon">
                        <i class="fas fa-hand-holding-heart"></i>
                    </div>
                </div>
                <div class="modern-stats-glow"></div>
            </div>

            <div class="modern-stats-card modern-stats-warning">
                <div class="modern-stats-content">
                    <div class="modern-stats-info">
                        <div class="modern-stats-label">Terlambat</div>
                        <div class="modern-stats-value"><?= $stats['total_terlambat'] ?? 0 ?></div>
                        <div class="modern-stats-meta">Item terlambat</div>
                    </div>
                    <div class="modern-stats-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                </div>
                <div class="modern-stats-glow"></div>
            </div>

            <div class="modern-stats-card modern-stats-info">
                <div class="modern-stats-content">
                    <div class="modern-stats-info">
                        <div class="modern-stats-label">Lunas</div>
                        <div class="modern-stats-value"><?= $stats['total_lunas'] ?? 0 ?></div>
                        <div class="modern-stats-meta">Sudah selesai</div>
                    </div>
                    <div class="modern-stats-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                </div>
                <div class="modern-stats-glow"></div>
            </div>
        </div>

        <!-- Modern Filter Section -->
        <div class="modern-card modern-mb-lg">
            <div class="modern-card-header">
                <h5 class="modern-card-title">
                    <i class="fas fa-filter modern-text-primary modern-mr-sm"></i>
                    Filter Data
                </h5>
            </div>
            <div class="modern-card-body">
                <form action="" method="GET" class="modern-grid modern-grid-cols-3 modern-gap-md modern-items-end">
                    <div class="modern-form-group">
                        <label class="modern-form-label">
                            <i class="fas fa-tag modern-text-primary"></i>
                            Jenis
                        </label>
                        <select name="jenis" class="modern-form-control">
                            <option value="">🔍 Semua Jenis</option>
                            <option value="hutang" <?= (isset($_GET['jenis']) && $_GET['jenis'] == 'hutang') ? 'selected' : '' ?>>📉 Hutang</option>
                            <option value="piutang" <?= (isset($_GET['jenis']) && $_GET['jenis'] == 'piutang') ? 'selected' : '' ?>>📈 Piutang</option>
                        </select>
                    </div>

                    <div class="modern-form-group">
                        <label class="modern-form-label">
                            <i class="fas fa-toggle-on modern-text-primary"></i>
                            Status
                        </label>
                        <select name="status" class="modern-form-control">
                            <option value="">🔍 Semua Status</option>
                            <option value="belum_lunas" <?= (isset($_GET['status']) && $_GET['status'] == 'belum_lunas') ? 'selected' : '' ?>>⏳ Belum Lunas</option>
                            <option value="lunas" <?= (isset($_GET['status']) && $_GET['status'] == 'lunas') ? 'selected' : '' ?>>✅ Lunas</option>
                            <option value="terlambat" <?= (isset($_GET['status']) && $_GET['status'] == 'terlambat') ? 'selected' : '' ?>>⚠️ Terlambat</option>
                        </select>
                    </div>

                    <div class="modern-form-group modern-flex modern-gap-sm">
                        <button type="submit" class="modern-btn modern-btn-primary modern-flex-1">
                            <i class="fas fa-search"></i>
                            Filter
                        </button>
                        <a href="hutang.php" class="modern-btn modern-btn-light">
                            <i class="fas fa-sync"></i>
                            Reset
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- Modern Data Table -->
        <div class="modern-card">
            <div class="modern-card-header">
                <h5 class="modern-card-title">
                    <i class="fas fa-list modern-text-primary modern-mr-sm"></i>
                    Data Hutang & Piutang
                </h5>
                <div class="modern-card-actions">
                    <span class="modern-badge modern-badge-info">
                        Total: <?= count($hutang_piutang) ?> data
                    </span>
                </div>
            </div>
            <div class="modern-card-body modern-p-0">
                <div class="modern-table-responsive">
                    <table class="modern-table">
                        <thead class="modern-table-header">
                            <tr>
                                <th class="modern-table-th">
                                    <i class="fas fa-tag modern-mr-xs"></i>
                                    Jenis
                                </th>
                                <th class="modern-table-th">
                                    <i class="fas fa-user modern-mr-xs"></i>
                                    Nama Pihak
                                </th>
                                <th class="modern-table-th">
                                    <i class="fas fa-calendar modern-mr-xs"></i>
                                    Tanggal
                                </th>
                                <th class="modern-table-th">
                                    <i class="fas fa-clock modern-mr-xs"></i>
                                    Jatuh Tempo
                                </th>
                                <th class="modern-table-th modern-text-right">
                                    <i class="fas fa-money-bill modern-mr-xs"></i>
                                    Jumlah
                                </th>
                                <th class="modern-table-th modern-text-right">
                                    <i class="fas fa-chart-line modern-mr-xs"></i>
                                    Terbayar
                                </th>
                                <th class="modern-table-th modern-text-right">
                                    <i class="fas fa-calculator modern-mr-xs"></i>
                                    Sisa
                                </th>
                                <th class="modern-table-th modern-text-center">
                                    <i class="fas fa-toggle-on modern-mr-xs"></i>
                                    Status
                                </th>
                                <th class="modern-table-th modern-text-center">
                                    <i class="fas fa-cogs modern-mr-xs"></i>
                                    Aksi
                                </th>
                            </tr>
                        </thead>
                        <tbody class="modern-table-body">
                            <?php if (empty($hutang_piutang)): ?>
                            <tr>
                                <td colspan="9" class="modern-table-td">
                                    <div class="modern-empty-state">
                                        <div class="modern-empty-icon">
                                            <i class="fas fa-hand-holding-usd"></i>
                                        </div>
                                        <div class="modern-empty-content">
                                            <h6 class="modern-empty-title">Belum Ada Data</h6>
                                            <p class="modern-empty-text">Belum ada data hutang atau piutang yang tercatat</p>
                                            <button type="button" class="modern-btn modern-btn-primary modern-btn-sm" data-bs-toggle="modal" data-bs-target="#addModal">
                                                <i class="fas fa-plus"></i>
                                                Tambah Data Pertama
                                            </button>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <?php else: ?>
                            <?php foreach ($hutang_piutang as $item): ?>
                            <?php
                                $sisa = $item['jumlah'] - $item['jumlah_terbayar'];
                                $progress = $item['jumlah'] > 0 ? ($item['jumlah_terbayar'] / $item['jumlah']) * 100 : 0;
                            ?>
                            <tr class="modern-table-row">
                                <td class="modern-table-td">
                                    <div class="modern-badge modern-badge-<?= $item['jenis'] === 'hutang' ? 'danger' : 'success' ?>">
                                        <i class="fas fa-<?= $item['jenis'] === 'hutang' ? 'arrow-down' : 'arrow-up' ?>"></i>
                                        <?= ucfirst($item['jenis']) ?>
                                    </div>
                                </td>
                                <td class="modern-table-td">
                                    <div class="modern-table-content">
                                        <div class="modern-table-title"><?= htmlspecialchars($item['nama_pihak']) ?></div>
                                        <?php if ($item['keterangan']): ?>
                                            <div class="modern-table-subtitle"><?= htmlspecialchars($item['keterangan']) ?></div>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td class="modern-table-td">
                                    <div class="modern-table-date">
                                        <i class="fas fa-calendar modern-text-muted modern-mr-xs"></i>
                                        <?= formatDate($item['tanggal_transaksi']) ?>
                                    </div>
                                </td>
                                <td class="modern-table-td">
                                    <?php if ($item['tanggal_jatuh_tempo']): ?>
                                        <div class="modern-table-date">
                                            <i class="fas fa-clock modern-text-muted modern-mr-xs"></i>
                                            <?= formatDate($item['tanggal_jatuh_tempo']) ?>
                                        </div>
                                        <?php if ($item['tanggal_jatuh_tempo'] < date('Y-m-d') && $item['status'] !== 'lunas'): ?>
                                            <div class="modern-badge modern-badge-danger modern-badge-sm modern-mt-xs">
                                                <i class="fas fa-exclamation-triangle"></i>
                                                Terlambat
                                            </div>
                                        <?php endif; ?>
                                    <?php else: ?>
                                        <span class="modern-text-muted">-</span>
                                    <?php endif; ?>
                                </td>
                                <td class="modern-table-td modern-text-right">
                                    <div class="modern-table-amount modern-text-primary"><?= formatRupiah($item['jumlah']) ?></div>
                                </td>
                                <td class="modern-table-td modern-text-right">
                                    <div class="modern-table-amount"><?= formatRupiah($item['jumlah_terbayar']) ?></div>
                                    <div class="modern-progress modern-mt-xs">
                                        <div class="modern-progress-bar modern-progress-<?= $item['status'] === 'lunas' ? 'success' : 'primary' ?>" style="width: <?= $progress ?>%"></div>
                                    </div>
                                </td>
                                <td class="modern-table-td modern-text-right">
                                    <div class="modern-table-amount modern-text-<?= $sisa > 0 ? 'warning' : 'success' ?>"><?= formatRupiah($sisa) ?></div>
                                </td>
                                <td class="modern-table-td modern-text-center">
                                    <div class="modern-badge modern-badge-<?= $item['status'] === 'lunas' ? 'success' : ($item['status'] === 'terlambat' ? 'danger' : 'warning') ?>">
                                        <?= str_replace('_', ' ', ucfirst($item['status'])) ?>
                                    </div>
                                </td>
                                <td class="modern-table-td modern-text-center">
                                    <div class="modern-action-buttons">
                                        <?php if ($item['status'] !== 'lunas'): ?>
                                        <button type="button" class="modern-btn modern-btn-success modern-btn-sm" onclick="bayar(<?= $item['id'] ?>, '<?= htmlspecialchars($item['nama_pihak']) ?>', <?= $sisa ?>)" title="Bayar">
                                            <i class="fas fa-money-bill-wave"></i>
                                        </button>
                                        <?php endif; ?>
                                        <button type="button" class="modern-btn modern-btn-light modern-btn-sm" onclick="editData(
                                            '<?= $item['id'] ?>',
                                            '<?= $item['jenis'] ?>',
                                            '<?= htmlspecialchars($item['nama_pihak']) ?>',
                                            '<?= $item['jumlah'] ?>',
                                            '<?= $item['jumlah_terbayar'] ?>',
                                            '<?= date('Y-m-d', strtotime($item['tanggal_transaksi'])) ?>',
                                            '<?= $item['tanggal_jatuh_tempo'] ? date('Y-m-d', strtotime($item['tanggal_jatuh_tempo'])) : '' ?>',
                                            '<?= htmlspecialchars($item['keterangan']) ?>'
                                        )" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button type="button" class="modern-btn modern-btn-danger modern-btn-sm" onclick="deleteData(<?= $item['id'] ?>)" title="Hapus">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Modern Pagination -->
                <?php if ($totalPages > 1): ?>
                <div class="modern-card-footer">
                    <div class="modern-pagination-wrapper">
                        <div class="modern-pagination-info">
                            <span class="modern-text-muted">
                                Halaman <?= $page ?> dari <?= $totalPages ?>
                                (<?= $totalRecords ?> total data)
                            </span>
                        </div>
                        <nav class="modern-pagination">
                            <?php if ($page > 1): ?>
                            <a class="modern-pagination-btn modern-pagination-prev"
                               href="?page=<?= $page - 1 ?><?= isset($_GET['jenis']) ? '&jenis='.$_GET['jenis'] : '' ?><?= isset($_GET['status']) ? '&status='.$_GET['status'] : '' ?>">
                                <i class="fas fa-chevron-left"></i>
                                Sebelumnya
                            </a>
                            <?php endif; ?>

                            <div class="modern-pagination-numbers">
                                <?php
                                $start = max(1, $page - 2);
                                $end = min($totalPages, $page + 2);

                                if ($start > 1): ?>
                                    <a class="modern-pagination-number" href="?page=1<?= isset($_GET['jenis']) ? '&jenis='.$_GET['jenis'] : '' ?><?= isset($_GET['status']) ? '&status='.$_GET['status'] : '' ?>">1</a>
                                    <?php if ($start > 2): ?>
                                        <span class="modern-pagination-dots">...</span>
                                    <?php endif; ?>
                                <?php endif; ?>

                                <?php for ($i = $start; $i <= $end; $i++): ?>
                                <a class="modern-pagination-number <?= $i === $page ? 'active' : '' ?>"
                                   href="?page=<?= $i ?><?= isset($_GET['jenis']) ? '&jenis='.$_GET['jenis'] : '' ?><?= isset($_GET['status']) ? '&status='.$_GET['status'] : '' ?>"><?= $i ?></a>
                                <?php endfor; ?>

                                <?php if ($end < $totalPages): ?>
                                    <?php if ($end < $totalPages - 1): ?>
                                        <span class="modern-pagination-dots">...</span>
                                    <?php endif; ?>
                                    <a class="modern-pagination-number" href="?page=<?= $totalPages ?><?= isset($_GET['jenis']) ? '&jenis='.$_GET['jenis'] : '' ?><?= isset($_GET['status']) ? '&status='.$_GET['status'] : '' ?>"><?= $totalPages ?></a>
                                <?php endif; ?>
                            </div>

                            <?php if ($page < $totalPages): ?>
                            <a class="modern-pagination-btn modern-pagination-next"
                               href="?page=<?= $page + 1 ?><?= isset($_GET['jenis']) ? '&jenis='.$_GET['jenis'] : '' ?><?= isset($_GET['status']) ? '&status='.$_GET['status'] : '' ?>">
                                Selanjutnya
                                <i class="fas fa-chevron-right"></i>
                            </a>
                            <?php endif; ?>
                        </nav>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Modern Add Modal -->
<div class="modal fade" id="addModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <div class="modern-modal-content">
            <div class="modern-modal-header">
                <h5 class="modern-modal-title">
                    <i class="fas fa-plus modern-text-primary modern-mr-sm"></i>
                    Tambah Hutang/Piutang
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="" method="POST" class="needs-validation" novalidate>
                <div class="modern-modal-body">
                    <input type="hidden" name="action" value="add">

                    <div class="modern-grid modern-grid-cols-2 modern-gap-md">
                        <div class="modern-form-group">
                            <label class="modern-form-label">
                                <i class="fas fa-tag modern-text-primary"></i>
                                Jenis <span class="modern-text-danger">*</span>
                            </label>
                            <select name="jenis" class="modern-form-control" required>
                                <option value="">Pilih Jenis</option>
                                <option value="hutang">📉 Hutang</option>
                                <option value="piutang">📈 Piutang</option>
                            </select>
                            <div class="invalid-feedback">Jenis harus dipilih</div>
                        </div>

                        <div class="modern-form-group">
                            <label class="modern-form-label">
                                <i class="fas fa-user modern-text-primary"></i>
                                Nama Pihak <span class="modern-text-danger">*</span>
                            </label>
                            <input type="text" name="nama_pihak" class="modern-form-control" required>
                            <div class="invalid-feedback">Nama pihak harus diisi</div>
                        </div>
                    </div>

                    <div class="modern-grid modern-grid-cols-2 modern-gap-md">
                        <div class="modern-form-group">
                            <label class="modern-form-label">
                                <i class="fas fa-money-bill-wave modern-text-primary"></i>
                                Jumlah <span class="modern-text-danger">*</span>
                            </label>
                            <div class="modern-input-group">
                                <span class="modern-input-group-text">Rp</span>
                                <input type="text" name="jumlah" class="modern-form-control number-format" required>
                            </div>
                            <div class="invalid-feedback">Jumlah harus diisi</div>
                        </div>

                        <div class="modern-form-group">
                            <label class="modern-form-label">
                                <i class="fas fa-chart-line modern-text-primary"></i>
                                Jumlah Terbayar
                            </label>
                            <div class="modern-input-group">
                                <span class="modern-input-group-text">Rp</span>
                                <input type="text" name="jumlah_terbayar" class="modern-form-control number-format" value="0">
                            </div>
                        </div>
                    </div>

                    <div class="modern-grid modern-grid-cols-2 modern-gap-md">
                        <div class="modern-form-group">
                            <label class="modern-form-label">
                                <i class="fas fa-calendar-alt modern-text-primary"></i>
                                Tanggal Transaksi <span class="modern-text-danger">*</span>
                            </label>
                            <input type="date" name="tanggal_transaksi" class="modern-form-control" value="<?= date('Y-m-d') ?>" required>
                            <div class="invalid-feedback">Tanggal transaksi harus diisi</div>
                        </div>

                        <div class="modern-form-group">
                            <label class="modern-form-label">
                                <i class="fas fa-clock modern-text-primary"></i>
                                Tanggal Jatuh Tempo
                            </label>
                            <input type="date" name="tanggal_jatuh_tempo" class="modern-form-control">
                            <small class="modern-form-help">Opsional</small>
                        </div>
                    </div>

                    <div class="modern-form-group modern-mb-0">
                        <label class="modern-form-label">
                            <i class="fas fa-sticky-note modern-text-primary"></i>
                            Keterangan
                        </label>
                        <textarea name="keterangan" class="modern-form-control" rows="3" placeholder="Catatan tambahan..."></textarea>
                    </div>
                </div>
                <div class="modern-modal-footer">
                    <button type="button" class="modern-btn modern-btn-light" data-bs-dismiss="modal">
                        <i class="fas fa-times"></i>
                        Batal
                    </button>
                    <button type="submit" class="modern-btn modern-btn-primary">
                        <i class="fas fa-save"></i>
                        Simpan Data
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modern Edit Modal -->
<div class="modal fade" id="editModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <div class="modern-modal-content">
            <div class="modern-modal-header">
                <h5 class="modern-modal-title">
                    <i class="fas fa-edit modern-text-primary modern-mr-sm"></i>
                    Edit Hutang/Piutang
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="" method="POST" class="needs-validation" novalidate>
                <div class="modern-modal-body">
                    <input type="hidden" name="action" value="update">
                    <input type="hidden" name="id" id="edit_id">

                    <div class="modern-grid modern-grid-cols-2 modern-gap-md">
                        <div class="modern-form-group">
                            <label class="modern-form-label">
                                <i class="fas fa-tag modern-text-primary"></i>
                                Jenis <span class="modern-text-danger">*</span>
                            </label>
                            <select name="jenis" id="edit_jenis" class="modern-form-control" required>
                                <option value="hutang">📉 Hutang</option>
                                <option value="piutang">📈 Piutang</option>
                            </select>
                            <div class="invalid-feedback">Jenis harus dipilih</div>
                        </div>

                        <div class="modern-form-group">
                            <label class="modern-form-label">
                                <i class="fas fa-user modern-text-primary"></i>
                                Nama Pihak <span class="modern-text-danger">*</span>
                            </label>
                            <input type="text" name="nama_pihak" id="edit_nama_pihak" class="modern-form-control" required>
                            <div class="invalid-feedback">Nama pihak harus diisi</div>
                        </div>
                    </div>

                    <div class="modern-grid modern-grid-cols-2 modern-gap-md">
                        <div class="modern-form-group">
                            <label class="modern-form-label">
                                <i class="fas fa-money-bill-wave modern-text-primary"></i>
                                Jumlah <span class="modern-text-danger">*</span>
                            </label>
                            <div class="modern-input-group">
                                <span class="modern-input-group-text">Rp</span>
                                <input type="text" name="jumlah" id="edit_jumlah" class="modern-form-control number-format" required>
                            </div>
                            <div class="invalid-feedback">Jumlah harus diisi</div>
                        </div>

                        <div class="modern-form-group">
                            <label class="modern-form-label">
                                <i class="fas fa-chart-line modern-text-primary"></i>
                                Jumlah Terbayar
                            </label>
                            <div class="modern-input-group">
                                <span class="modern-input-group-text">Rp</span>
                                <input type="text" name="jumlah_terbayar" id="edit_jumlah_terbayar" class="modern-form-control number-format">
                            </div>
                        </div>
                    </div>

                    <div class="modern-grid modern-grid-cols-2 modern-gap-md">
                        <div class="modern-form-group">
                            <label class="modern-form-label">
                                <i class="fas fa-calendar-alt modern-text-primary"></i>
                                Tanggal Transaksi <span class="modern-text-danger">*</span>
                            </label>
                            <input type="date" name="tanggal_transaksi" id="edit_tanggal_transaksi" class="modern-form-control" required>
                            <div class="invalid-feedback">Tanggal transaksi harus diisi</div>
                        </div>

                        <div class="modern-form-group">
                            <label class="modern-form-label">
                                <i class="fas fa-clock modern-text-primary"></i>
                                Tanggal Jatuh Tempo
                            </label>
                            <input type="date" name="tanggal_jatuh_tempo" id="edit_tanggal_jatuh_tempo" class="modern-form-control">
                        </div>
                    </div>

                    <div class="modern-form-group modern-mb-0">
                        <label class="modern-form-label">
                            <i class="fas fa-sticky-note modern-text-primary"></i>
                            Keterangan
                        </label>
                        <textarea name="keterangan" id="edit_keterangan" class="modern-form-control" rows="3"></textarea>
                    </div>
                </div>
                <div class="modern-modal-footer">
                    <button type="button" class="modern-btn modern-btn-light" data-bs-dismiss="modal">
                        <i class="fas fa-times"></i>
                        Batal
                    </button>
                    <button type="submit" class="modern-btn modern-btn-primary">
                        <i class="fas fa-save"></i>
                        Update Data
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modern Payment Modal -->
<div class="modal fade" id="paymentModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modern-modal-content">
            <div class="modern-modal-header">
                <h5 class="modern-modal-title">
                    <i class="fas fa-money-bill-wave modern-text-success modern-mr-sm"></i>
                    Catat Pembayaran
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="" method="POST" class="needs-validation" novalidate>
                <div class="modern-modal-body">
                    <input type="hidden" name="action" value="bayar">
                    <input type="hidden" name="id" id="payment_id">

                    <div class="modern-form-group">
                        <label class="modern-form-label">
                            <i class="fas fa-user modern-text-primary"></i>
                            Nama Pihak
                        </label>
                        <input type="text" id="payment_nama" class="modern-form-control" readonly>
                    </div>

                    <div class="modern-form-group">
                        <label class="modern-form-label">
                            <i class="fas fa-calculator modern-text-primary"></i>
                            Sisa yang Harus Dibayar
                        </label>
                        <input type="text" id="payment_sisa" class="modern-form-control" readonly>
                    </div>

                    <div class="modern-form-group modern-mb-0">
                        <label class="modern-form-label">
                            <i class="fas fa-money-bill-wave modern-text-primary"></i>
                            Jumlah Pembayaran <span class="modern-text-danger">*</span>
                        </label>
                        <div class="modern-input-group">
                            <span class="modern-input-group-text">Rp</span>
                            <input type="text" name="jumlah_bayar" id="payment_jumlah" class="modern-form-control number-format" required>
                        </div>
                        <div class="invalid-feedback">Jumlah pembayaran harus diisi</div>
                    </div>
                </div>
                <div class="modern-modal-footer">
                    <button type="button" class="modern-btn modern-btn-light" data-bs-dismiss="modal">
                        <i class="fas fa-times"></i>
                        Batal
                    </button>
                    <button type="submit" class="modern-btn modern-btn-success">
                        <i class="fas fa-check"></i>
                        Catat Pembayaran
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function editData(id, jenis, nama_pihak, jumlah, jumlah_terbayar, tanggal_transaksi, tanggal_jatuh_tempo, keterangan) {
    document.getElementById('edit_id').value = id;
    document.getElementById('edit_jenis').value = jenis;
    document.getElementById('edit_nama_pihak').value = nama_pihak;
    document.getElementById('edit_jumlah').value = formatNumber(jumlah);
    document.getElementById('edit_jumlah_terbayar').value = formatNumber(jumlah_terbayar);
    document.getElementById('edit_tanggal_transaksi').value = tanggal_transaksi;
    document.getElementById('edit_tanggal_jatuh_tempo').value = tanggal_jatuh_tempo;
    document.getElementById('edit_keterangan').value = keterangan;

    const modal = new bootstrap.Modal(document.getElementById('editModal'));
    modal.show();
}

function deleteData(id) {
    if (confirm('Apakah Anda yakin ingin menghapus data ini?')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="delete">
            <input type="hidden" name="id" value="${id}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

function bayar(id, nama, sisa) {
    document.getElementById('payment_id').value = id;
    document.getElementById('payment_nama').value = nama;
    document.getElementById('payment_sisa').value = formatRupiah(sisa);
    document.getElementById('payment_jumlah').value = formatNumber(sisa);

    const modal = new bootstrap.Modal(document.getElementById('paymentModal'));
    modal.show();
}

function formatNumber(num) {
    return new Intl.NumberFormat('id-ID').format(num);
}

function formatRupiah(num) {
    return 'Rp ' + new Intl.NumberFormat('id-ID').format(num);
}

// Format number inputs
document.addEventListener('DOMContentLoaded', function() {
    const numberInputs = document.querySelectorAll('.number-format');

    numberInputs.forEach(function(input) {
        input.addEventListener('input', function(e) {
            let value = e.target.value.replace(/[^\d]/g, '');
            if (value) {
                e.target.value = formatNumber(value);
            }
        });
    });
});
</script>

<?php include 'includes/views/layouts/footer.php'; ?>
