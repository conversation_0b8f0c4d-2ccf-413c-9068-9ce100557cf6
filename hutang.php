<?php
require_once 'includes/config/database.php';
require_once 'includes/helpers/functions.php';
require_once 'includes/helpers/notifications.php';

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isLoggedIn()) {
    setFlashMessage('danger', 'Silakan login terlebih dahulu');
    redirect('/keuangan/login.php');
}

$currentUser = getCurrentUser();
$currentPage = 'hutang';

// Create hutang_piutang table if not exists
try {
    $pdo->exec("CREATE TABLE IF NOT EXISTS hutang_piutang (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        jenis <PERSON>('hutang', 'piutang') NOT NULL,
        nama_pihak VARCHAR(255) NOT NULL,
        jumlah DECIMAL(15,2) NOT NULL,
        jumlah_terbayar DECIMAL(15,2) DEFAULT 0,
        tanggal_transaksi DATE NOT NULL,
        tanggal_jatuh_tempo DATE,
        keterangan TEXT,
        status ENUM('belum_lunas', 'lunas', 'terlambat') DEFAULT 'belum_lunas',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    )");
} catch (PDOException $e) {
    error_log("Error creating hutang_piutang table: " . $e->getMessage());
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        try {
            switch ($_POST['action']) {
                case 'add':
                    // Validasi input
                    $errors = [];
                    
                    if (empty($_POST['jenis'])) {
                        $errors[] = 'Jenis harus dipilih';
                    }
                    
                    if (empty($_POST['nama_pihak'])) {
                        $errors[] = 'Nama pihak harus diisi';
                    }
                    
                    if (empty($_POST['jumlah'])) {
                        $errors[] = 'Jumlah harus diisi';
                    }
                    
                    if (empty($_POST['tanggal_transaksi'])) {
                        $errors[] = 'Tanggal transaksi harus diisi';
                    }
                    
                    if (empty($errors)) {
                        try {
                            // Format jumlah (hapus format angka)
                            $jumlah = str_replace(['.', ','], '', $_POST['jumlah']);
                            $jumlah_terbayar = !empty($_POST['jumlah_terbayar']) ? str_replace(['.', ','], '', $_POST['jumlah_terbayar']) : 0;
                            
                            // Format tanggal ke format Y-m-d
                            $tanggal_transaksi = date('Y-m-d', strtotime($_POST['tanggal_transaksi']));
                            $tanggal_jatuh_tempo = !empty($_POST['tanggal_jatuh_tempo']) ? date('Y-m-d', strtotime($_POST['tanggal_jatuh_tempo'])) : null;
                            
                            // Tentukan status berdasarkan jumlah terbayar
                            $status = 'belum_lunas';
                            if ($jumlah_terbayar >= $jumlah) {
                                $status = 'lunas';
                            } elseif ($tanggal_jatuh_tempo && $tanggal_jatuh_tempo < date('Y-m-d') && $jumlah_terbayar < $jumlah) {
                                $status = 'terlambat';
                            }
                            
                            // Insert hutang/piutang dengan prepared statement
                            $sql = "INSERT INTO hutang_piutang (user_id, jenis, nama_pihak, jumlah, jumlah_terbayar, tanggal_transaksi, tanggal_jatuh_tempo, keterangan, status, created_at) 
                                   VALUES (:user_id, :jenis, :nama_pihak, :jumlah, :jumlah_terbayar, :tanggal_transaksi, :tanggal_jatuh_tempo, :keterangan, :status, NOW())";
                            
                            $stmt = $pdo->prepare($sql);
                            
                            $params = [
                                ':user_id' => $currentUser['id'],
                                ':jenis' => $_POST['jenis'],
                                ':nama_pihak' => $_POST['nama_pihak'],
                                ':jumlah' => $jumlah,
                                ':jumlah_terbayar' => $jumlah_terbayar,
                                ':tanggal_transaksi' => $tanggal_transaksi,
                                ':tanggal_jatuh_tempo' => $tanggal_jatuh_tempo,
                                ':keterangan' => $_POST['keterangan'],
                                ':status' => $status
                            ];
                            
                            $result = $stmt->execute($params);
                            
                            if ($result) {
                                // Log aktivitas
                                logActivity($currentUser['id'], sprintf(
                                    'Menambahkan %s %s sebesar %s',
                                    $_POST['jenis'],
                                    $_POST['nama_pihak'],
                                    formatRupiah($jumlah)
                                ));
                                
                                setFlashMessage('success', ucfirst($_POST['jenis']) . ' berhasil ditambahkan');
                                redirect('/hutang.php');
                            } else {
                                throw new Exception('Gagal menyimpan data');
                            }
                        } catch (Exception $e) {
                            setFlashMessage('danger', $e->getMessage());
                        }
                    } else {
                        setFlashMessage('danger', implode('<br>', $errors));
                    }
                    break;

                case 'update':
                    if (empty($_POST['id'])) {
                        setFlashMessage('danger', 'ID tidak valid');
                        break;
                    }

                    // Format jumlah (hapus format angka)
                    $jumlah = str_replace(['.', ','], '', $_POST['jumlah']);
                    $jumlah_terbayar = str_replace(['.', ','], '', $_POST['jumlah_terbayar']);
                    
                    // Format tanggal ke format Y-m-d
                    $tanggal_transaksi = date('Y-m-d', strtotime($_POST['tanggal_transaksi']));
                    $tanggal_jatuh_tempo = !empty($_POST['tanggal_jatuh_tempo']) ? date('Y-m-d', strtotime($_POST['tanggal_jatuh_tempo'])) : null;
                    
                    // Tentukan status berdasarkan jumlah terbayar
                    $status = 'belum_lunas';
                    if ($jumlah_terbayar >= $jumlah) {
                        $status = 'lunas';
                    } elseif ($tanggal_jatuh_tempo && $tanggal_jatuh_tempo < date('Y-m-d') && $jumlah_terbayar < $jumlah) {
                        $status = 'terlambat';
                    }
                    
                    $stmt = $pdo->prepare("
                        UPDATE hutang_piutang 
                        SET jenis = ?, nama_pihak = ?, jumlah = ?, jumlah_terbayar = ?, tanggal_transaksi = ?, tanggal_jatuh_tempo = ?, keterangan = ?, status = ?
                        WHERE id = ? AND user_id = ?
                    ");
                    
                    $result = $stmt->execute([
                        $_POST['jenis'],
                        $_POST['nama_pihak'],
                        $jumlah,
                        $jumlah_terbayar,
                        $tanggal_transaksi,
                        $tanggal_jatuh_tempo,
                        $_POST['keterangan'],
                        $status,
                        $_POST['id'],
                        $currentUser['id']
                    ]);

                    if ($result) {
                        setFlashMessage('success', 'Data berhasil diperbarui');
                    } else {
                        setFlashMessage('danger', 'Gagal memperbarui data');
                    }
                    break;

                case 'delete':
                    if (empty($_POST['id'])) {
                        setFlashMessage('danger', 'ID tidak valid');
                        break;
                    }

                    $stmt = $pdo->prepare("DELETE FROM hutang_piutang WHERE id = ? AND user_id = ?");
                    $result = $stmt->execute([$_POST['id'], $currentUser['id']]);

                    if ($result) {
                        setFlashMessage('success', 'Data berhasil dihapus');
                    } else {
                        setFlashMessage('danger', 'Gagal menghapus data');
                    }
                    break;

                case 'bayar':
                    if (empty($_POST['id']) || empty($_POST['jumlah_bayar'])) {
                        setFlashMessage('danger', 'Data pembayaran tidak lengkap');
                        break;
                    }

                    $jumlah_bayar = str_replace(['.', ','], '', $_POST['jumlah_bayar']);
                    
                    // Get current data
                    $stmt = $pdo->prepare("SELECT * FROM hutang_piutang WHERE id = ? AND user_id = ?");
                    $stmt->execute([$_POST['id'], $currentUser['id']]);
                    $data = $stmt->fetch();
                    
                    if ($data) {
                        $jumlah_terbayar_baru = $data['jumlah_terbayar'] + $jumlah_bayar;
                        
                        // Tentukan status baru
                        $status_baru = 'belum_lunas';
                        if ($jumlah_terbayar_baru >= $data['jumlah']) {
                            $status_baru = 'lunas';
                            $jumlah_terbayar_baru = $data['jumlah']; // Tidak boleh lebih dari jumlah total
                        } elseif ($data['tanggal_jatuh_tempo'] && $data['tanggal_jatuh_tempo'] < date('Y-m-d')) {
                            $status_baru = 'terlambat';
                        }
                        
                        $stmt = $pdo->prepare("UPDATE hutang_piutang SET jumlah_terbayar = ?, status = ? WHERE id = ? AND user_id = ?");
                        $result = $stmt->execute([$jumlah_terbayar_baru, $status_baru, $_POST['id'], $currentUser['id']]);
                        
                        if ($result) {
                            setFlashMessage('success', 'Pembayaran berhasil dicatat');
                        } else {
                            setFlashMessage('danger', 'Gagal mencatat pembayaran');
                        }
                    }
                    break;
            }
        } catch (PDOException $e) {
            setFlashMessage('danger', 'Terjadi kesalahan. Silakan coba lagi.');
        }
        redirect('/hutang.php');
    }
}

// Get data with pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$perPage = 10;
$offset = ($page - 1) * $perPage;

// Get total records with filters
$where = ["user_id = ?"];
$params = [$currentUser['id']];

if (!empty($_GET['jenis'])) {
    $where[] = "jenis = ?";
    $params[] = $_GET['jenis'];
}

if (!empty($_GET['status'])) {
    $where[] = "status = ?";
    $params[] = $_GET['status'];
}

$whereClause = implode(" AND ", $where);

// Get total records
$stmt = $pdo->prepare("
    SELECT COUNT(*) as total 
    FROM hutang_piutang
    WHERE $whereClause
");
$stmt->execute($params);
$totalRecords = $stmt->fetch()['total'];
$totalPages = ceil($totalRecords / $perPage);

// Get data
$stmt = $pdo->prepare("
    SELECT * FROM hutang_piutang
    WHERE $whereClause
    ORDER BY tanggal_transaksi DESC, created_at DESC
    LIMIT ? OFFSET ?
");

// Create new array for pagination parameters
$paginationParams = $params;
$paginationParams[] = $perPage;
$paginationParams[] = $offset;

$stmt->execute($paginationParams);
$hutang_piutang = $stmt->fetchAll();

// Get statistics
$stmt = $pdo->prepare("
    SELECT 
        SUM(CASE WHEN jenis = 'hutang' AND status != 'lunas' THEN (jumlah - jumlah_terbayar) ELSE 0 END) as total_hutang,
        SUM(CASE WHEN jenis = 'piutang' AND status != 'lunas' THEN (jumlah - jumlah_terbayar) ELSE 0 END) as total_piutang,
        COUNT(CASE WHEN status = 'terlambat' THEN 1 END) as total_terlambat,
        COUNT(CASE WHEN status = 'lunas' THEN 1 END) as total_lunas
    FROM hutang_piutang
    WHERE user_id = ?
");
$stmt->execute([$currentUser['id']]);
$stats = $stmt->fetch();

// Include header
include 'includes/views/layouts/header.php';
?>

<div class="container-fluid py-4">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-1">Hutang & Piutang</h1>
            <p class="text-muted mb-0">Kelola hutang dan piutang Anda</p>
        </div>
        <button type="button" class="btn btn-primary d-flex align-items-center" data-bs-toggle="modal" data-bs-target="#addModal">
            <i class="fas fa-plus me-2"></i>Tambah Data
        </button>
    </div>

    <!-- Statistics Cards -->
    <div class="row g-3 mb-4">
        <div class="col-md-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted mb-1">Total Hutang</h6>
                            <h3 class="mb-0 text-danger"><?= formatRupiah($stats['total_hutang'] ?? 0) ?></h3>
                        </div>
                        <div class="bg-danger bg-opacity-10 p-3 rounded">
                            <i class="fas fa-hand-holding-usd text-danger"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted mb-1">Total Piutang</h6>
                            <h3 class="mb-0 text-success"><?= formatRupiah($stats['total_piutang'] ?? 0) ?></h3>
                        </div>
                        <div class="bg-success bg-opacity-10 p-3 rounded">
                            <i class="fas fa-hand-holding-heart text-success"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted mb-1">Terlambat</h6>
                            <h3 class="mb-0 text-warning"><?= $stats['total_terlambat'] ?? 0 ?></h3>
                        </div>
                        <div class="bg-warning bg-opacity-10 p-3 rounded">
                            <i class="fas fa-exclamation-triangle text-warning"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted mb-1">Lunas</h6>
                            <h3 class="mb-0 text-info"><?= $stats['total_lunas'] ?? 0 ?></h3>
                        </div>
                        <div class="bg-info bg-opacity-10 p-3 rounded">
                            <i class="fas fa-check-circle text-info"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filter Section -->
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-body">
            <form action="" method="GET" class="row g-3">
                <div class="col-md-4">
                    <label class="form-label">Jenis</label>
                    <select name="jenis" class="form-select">
                        <option value="">Semua Jenis</option>
                        <option value="hutang" <?= (isset($_GET['jenis']) && $_GET['jenis'] == 'hutang') ? 'selected' : '' ?>>Hutang</option>
                        <option value="piutang" <?= (isset($_GET['jenis']) && $_GET['jenis'] == 'piutang') ? 'selected' : '' ?>>Piutang</option>
                    </select>
                </div>
                <div class="col-md-4">
                    <label class="form-label">Status</label>
                    <select name="status" class="form-select">
                        <option value="">Semua Status</option>
                        <option value="belum_lunas" <?= (isset($_GET['status']) && $_GET['status'] == 'belum_lunas') ? 'selected' : '' ?>>Belum Lunas</option>
                        <option value="lunas" <?= (isset($_GET['status']) && $_GET['status'] == 'lunas') ? 'selected' : '' ?>>Lunas</option>
                        <option value="terlambat" <?= (isset($_GET['status']) && $_GET['status'] == 'terlambat') ? 'selected' : '' ?>>Terlambat</option>
                    </select>
                </div>
                <div class="col-12 text-end">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-filter me-2"></i>Filter
                    </button>
                    <a href="hutang.php" class="btn btn-light">
                        <i class="fas fa-sync me-2"></i>Reset
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Data Table -->
    <div class="card border-0 shadow-sm">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover align-middle">
                    <thead>
                        <tr>
                            <th>Jenis</th>
                            <th>Nama Pihak</th>
                            <th>Tanggal</th>
                            <th>Jatuh Tempo</th>
                            <th class="text-end">Jumlah</th>
                            <th class="text-end">Terbayar</th>
                            <th class="text-end">Sisa</th>
                            <th class="text-center">Status</th>
                            <th class="text-center">Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($hutang_piutang)): ?>
                        <tr>
                            <td colspan="9" class="text-center py-5">
                                <div class="text-muted">
                                    <i class="fas fa-hand-holding-usd fa-3x mb-3"></i>
                                    <p class="mb-0">Belum ada data hutang atau piutang</p>
                                </div>
                            </td>
                        </tr>
                        <?php else: ?>
                        <?php foreach ($hutang_piutang as $item): ?>
                        <?php
                            $sisa = $item['jumlah'] - $item['jumlah_terbayar'];
                            $progress = $item['jumlah'] > 0 ? ($item['jumlah_terbayar'] / $item['jumlah']) * 100 : 0;
                        ?>
                        <tr>
                            <td>
                                <span class="badge bg-<?= $item['jenis'] === 'hutang' ? 'danger' : 'success' ?> bg-opacity-10 text-<?= $item['jenis'] === 'hutang' ? 'danger' : 'success' ?>">
                                    <i class="fas fa-<?= $item['jenis'] === 'hutang' ? 'arrow-down' : 'arrow-up' ?> me-1"></i>
                                    <?= ucfirst($item['jenis']) ?>
                                </span>
                            </td>
                            <td>
                                <div class="fw-bold"><?= htmlspecialchars($item['nama_pihak']) ?></div>
                                <?php if ($item['keterangan']): ?>
                                    <small class="text-muted"><?= htmlspecialchars($item['keterangan']) ?></small>
                                <?php endif; ?>
                            </td>
                            <td><?= formatTanggal($item['tanggal_transaksi']) ?></td>
                            <td>
                                <?php if ($item['tanggal_jatuh_tempo']): ?>
                                    <?= formatTanggal($item['tanggal_jatuh_tempo']) ?>
                                    <?php if ($item['tanggal_jatuh_tempo'] < date('Y-m-d') && $item['status'] !== 'lunas'): ?>
                                        <br><small class="text-danger"><i class="fas fa-exclamation-triangle"></i> Terlambat</small>
                                    <?php endif; ?>
                                <?php else: ?>
                                    <span class="text-muted">-</span>
                                <?php endif; ?>
                            </td>
                            <td class="text-end fw-bold"><?= formatRupiah($item['jumlah']) ?></td>
                            <td class="text-end">
                                <?= formatRupiah($item['jumlah_terbayar']) ?>
                                <div class="progress mt-1" style="height: 4px;">
                                    <div class="progress-bar bg-<?= $item['status'] === 'lunas' ? 'success' : 'primary' ?>" style="width: <?= $progress ?>%"></div>
                                </div>
                            </td>
                            <td class="text-end fw-bold text-<?= $sisa > 0 ? 'warning' : 'success' ?>"><?= formatRupiah($sisa) ?></td>
                            <td class="text-center">
                                <span class="badge bg-<?= $item['status'] === 'lunas' ? 'success' : ($item['status'] === 'terlambat' ? 'danger' : 'warning') ?>">
                                    <?= str_replace('_', ' ', ucfirst($item['status'])) ?>
                                </span>
                            </td>
                            <td class="text-center">
                                <div class="btn-group">
                                    <?php if ($item['status'] !== 'lunas'): ?>
                                    <button type="button" class="btn btn-sm btn-success" onclick="bayar(<?= $item['id'] ?>, '<?= htmlspecialchars($item['nama_pihak']) ?>', <?= $sisa ?>)" title="Bayar">
                                        <i class="fas fa-money-bill-wave"></i>
                                    </button>
                                    <?php endif; ?>
                                    <button type="button" class="btn btn-sm btn-light" onclick="editData(
                                        '<?= $item['id'] ?>',
                                        '<?= $item['jenis'] ?>',
                                        '<?= htmlspecialchars($item['nama_pihak']) ?>',
                                        '<?= $item['jumlah'] ?>',
                                        '<?= $item['jumlah_terbayar'] ?>',
                                        '<?= date('Y-m-d', strtotime($item['tanggal_transaksi'])) ?>',
                                        '<?= $item['tanggal_jatuh_tempo'] ? date('Y-m-d', strtotime($item['tanggal_jatuh_tempo'])) : '' ?>',
                                        '<?= htmlspecialchars($item['keterangan']) ?>'
                                    )">
                                        <i class="fas fa-edit text-primary"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-light" onclick="deleteData(<?= $item['id'] ?>)">
                                        <i class="fas fa-trash text-danger"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <?php if ($totalPages > 1): ?>
            <nav class="mt-4">
                <ul class="pagination justify-content-center">
                    <?php if ($page > 1): ?>
                    <li class="page-item">
                        <a class="page-link" href="?page=<?= $page - 1 ?><?= isset($_GET['jenis']) ? '&jenis='.$_GET['jenis'] : '' ?><?= isset($_GET['status']) ? '&status='.$_GET['status'] : '' ?>">
                            <i class="fas fa-chevron-left"></i>
                        </a>
                    </li>
                    <?php endif; ?>

                    <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                    <li class="page-item <?= $i === $page ? 'active' : '' ?>">
                        <a class="page-link" href="?page=<?= $i ?><?= isset($_GET['jenis']) ? '&jenis='.$_GET['jenis'] : '' ?><?= isset($_GET['status']) ? '&status='.$_GET['status'] : '' ?>"><?= $i ?></a>
                    </li>
                    <?php endfor; ?>

                    <?php if ($page < $totalPages): ?>
                    <li class="page-item">
                        <a class="page-link" href="?page=<?= $page + 1 ?><?= isset($_GET['jenis']) ? '&jenis='.$_GET['jenis'] : '' ?><?= isset($_GET['status']) ? '&status='.$_GET['status'] : '' ?>">
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    </li>
                    <?php endif; ?>
                </ul>
            </nav>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Add Modal -->
<div class="modal fade" id="addModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header border-0">
                <h5 class="modal-title">Tambah Hutang/Piutang</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="" method="POST" class="needs-validation" novalidate>
                <div class="modal-body">
                    <input type="hidden" name="action" value="add">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Jenis</label>
                                <select name="jenis" class="form-select" required>
                                    <option value="">Pilih Jenis</option>
                                    <option value="hutang">Hutang</option>
                                    <option value="piutang">Piutang</option>
                                </select>
                                <div class="invalid-feedback">Jenis harus dipilih</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Nama Pihak</label>
                                <input type="text" name="nama_pihak" class="form-control" required>
                                <div class="invalid-feedback">Nama pihak harus diisi</div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Jumlah</label>
                                <div class="input-group">
                                    <span class="input-group-text">Rp</span>
                                    <input type="text" name="jumlah" class="form-control number-format" required>
                                </div>
                                <div class="invalid-feedback">Jumlah harus diisi</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Jumlah Terbayar</label>
                                <div class="input-group">
                                    <span class="input-group-text">Rp</span>
                                    <input type="text" name="jumlah_terbayar" class="form-control number-format" value="0">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Tanggal Transaksi</label>
                                <input type="date" name="tanggal_transaksi" class="form-control" value="<?= date('Y-m-d') ?>" required>
                                <div class="invalid-feedback">Tanggal transaksi harus diisi</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Tanggal Jatuh Tempo</label>
                                <input type="date" name="tanggal_jatuh_tempo" class="form-control">
                                <small class="text-muted">Opsional</small>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Keterangan</label>
                        <textarea name="keterangan" class="form-control" rows="3" placeholder="Catatan tambahan..."></textarea>
                    </div>
                </div>
                <div class="modal-footer border-0">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">Simpan Data</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Modal -->
<div class="modal fade" id="editModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header border-0">
                <h5 class="modal-title">Edit Hutang/Piutang</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="" method="POST" class="needs-validation" novalidate>
                <div class="modal-body">
                    <input type="hidden" name="action" value="update">
                    <input type="hidden" name="id" id="edit_id">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Jenis</label>
                                <select name="jenis" id="edit_jenis" class="form-select" required>
                                    <option value="hutang">Hutang</option>
                                    <option value="piutang">Piutang</option>
                                </select>
                                <div class="invalid-feedback">Jenis harus dipilih</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Nama Pihak</label>
                                <input type="text" name="nama_pihak" id="edit_nama_pihak" class="form-control" required>
                                <div class="invalid-feedback">Nama pihak harus diisi</div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Jumlah</label>
                                <div class="input-group">
                                    <span class="input-group-text">Rp</span>
                                    <input type="text" name="jumlah" id="edit_jumlah" class="form-control number-format" required>
                                </div>
                                <div class="invalid-feedback">Jumlah harus diisi</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Jumlah Terbayar</label>
                                <div class="input-group">
                                    <span class="input-group-text">Rp</span>
                                    <input type="text" name="jumlah_terbayar" id="edit_jumlah_terbayar" class="form-control number-format">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Tanggal Transaksi</label>
                                <input type="date" name="tanggal_transaksi" id="edit_tanggal_transaksi" class="form-control" required>
                                <div class="invalid-feedback">Tanggal transaksi harus diisi</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Tanggal Jatuh Tempo</label>
                                <input type="date" name="tanggal_jatuh_tempo" id="edit_tanggal_jatuh_tempo" class="form-control">
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Keterangan</label>
                        <textarea name="keterangan" id="edit_keterangan" class="form-control" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer border-0">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">Update Data</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Payment Modal -->
<div class="modal fade" id="paymentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header border-0">
                <h5 class="modal-title">Catat Pembayaran</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="" method="POST" class="needs-validation" novalidate>
                <div class="modal-body">
                    <input type="hidden" name="action" value="bayar">
                    <input type="hidden" name="id" id="payment_id">

                    <div class="mb-3">
                        <label class="form-label">Nama Pihak</label>
                        <input type="text" id="payment_nama" class="form-control" readonly>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Sisa yang Harus Dibayar</label>
                        <input type="text" id="payment_sisa" class="form-control" readonly>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Jumlah Pembayaran</label>
                        <div class="input-group">
                            <span class="input-group-text">Rp</span>
                            <input type="text" name="jumlah_bayar" id="payment_jumlah" class="form-control number-format" required>
                        </div>
                        <div class="invalid-feedback">Jumlah pembayaran harus diisi</div>
                    </div>
                </div>
                <div class="modal-footer border-0">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-success">Catat Pembayaran</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function editData(id, jenis, nama_pihak, jumlah, jumlah_terbayar, tanggal_transaksi, tanggal_jatuh_tempo, keterangan) {
    document.getElementById('edit_id').value = id;
    document.getElementById('edit_jenis').value = jenis;
    document.getElementById('edit_nama_pihak').value = nama_pihak;
    document.getElementById('edit_jumlah').value = formatNumber(jumlah);
    document.getElementById('edit_jumlah_terbayar').value = formatNumber(jumlah_terbayar);
    document.getElementById('edit_tanggal_transaksi').value = tanggal_transaksi;
    document.getElementById('edit_tanggal_jatuh_tempo').value = tanggal_jatuh_tempo;
    document.getElementById('edit_keterangan').value = keterangan;

    const modal = new bootstrap.Modal(document.getElementById('editModal'));
    modal.show();
}

function deleteData(id) {
    if (confirm('Apakah Anda yakin ingin menghapus data ini?')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="delete">
            <input type="hidden" name="id" value="${id}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

function bayar(id, nama, sisa) {
    document.getElementById('payment_id').value = id;
    document.getElementById('payment_nama').value = nama;
    document.getElementById('payment_sisa').value = formatRupiah(sisa);
    document.getElementById('payment_jumlah').value = formatNumber(sisa);

    const modal = new bootstrap.Modal(document.getElementById('paymentModal'));
    modal.show();
}

function formatNumber(num) {
    return new Intl.NumberFormat('id-ID').format(num);
}

function formatRupiah(num) {
    return 'Rp ' + new Intl.NumberFormat('id-ID').format(num);
}

// Format number inputs
document.addEventListener('DOMContentLoaded', function() {
    const numberInputs = document.querySelectorAll('.number-format');

    numberInputs.forEach(function(input) {
        input.addEventListener('input', function(e) {
            let value = e.target.value.replace(/[^\d]/g, '');
            if (value) {
                e.target.value = formatNumber(value);
            }
        });
    });
});
</script>

<?php include 'includes/views/layouts/footer.php'; ?>
