<?php
require_once 'config/database.php';
require_once 'includes/helpers/functions.php';

// Cek apakah user sudah login
if (!isLoggedIn()) {
    setFlashMessage('danger', 'Silakan login terlebih dahulu');
    redirect('login.php');
}

// Dapatkan data user yang sedang login
$currentUser = getCurrentUser();
if (!$currentUser) {
    setFlashMessage('danger', 'Sesi tidak valid');
    redirect('login.php');
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'add') {
        $produk_id = $_POST['produk_id'] ?? 0;
        $jumlah = $_POST['jumlah'] ?? 0;
        $tanggal = $_POST['tanggal'] ?? date('Y-m-d');
        $keterangan = $_POST['keterangan'] ?? '';
        
        try {
            // Get product data
            $stmt = $pdo->prepare("SELECT * FROM produk WHERE id = ?");
            $stmt->execute([$produk_id]);
            $produk = $stmt->fetch();
            
            if (!$produk) {
                throw new Exception('Produk tidak ditemukan');
            }
            
            if ($produk['stok'] < $jumlah) {
                throw new Exception('Stok tidak mencukupi');
            }
            
            // Calculate total
            $total = $jumlah * $produk['harga_jual'];
            
            // Begin transaction
            $pdo->beginTransaction();
            
            // Insert transaction
            $stmt = $pdo->prepare("
                INSERT INTO transaksi_produk (user_id, produk_id, jumlah, harga_jual, total, tanggal, keterangan)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $currentUser['id'], 
                $produk_id, 
                $jumlah, 
                $produk['harga_jual'], 
                $total, 
                $tanggal, 
                $keterangan
            ]);
            
            // Update stock
            $stmt = $pdo->prepare("
                UPDATE produk 
                SET stok = stok - ? 
                WHERE id = ?
            ");
            $stmt->execute([$jumlah, $produk_id]);
            
            // Get kategori_id for Penjualan
            $stmt = $pdo->prepare("SELECT id FROM kategori WHERE nama = 'Penjualan' AND tipe = 'masuk' LIMIT 1");
            $stmt->execute();
            $kategori = $stmt->fetch();
            
            if ($kategori) {
            // Insert to transaksi table
            $stmt = $pdo->prepare("
                INSERT INTO transaksi (user_id, kategori_id, jumlah, keterangan, tanggal)
                    VALUES (?, ?, ?, ?, ?)
            ");
                $stmt->execute([
                    $currentUser['id'], 
                    $kategori['id'], 
                    $total, 
                    "Penjualan produk: " . $produk['nama'], 
                    $tanggal
                ]);
            }
            
            $pdo->commit();
            setFlashMessage('success', 'Penjualan berhasil dicatat');
            
        } catch (Exception $e) {
            $pdo->rollBack();
            setFlashMessage('danger', 'Terjadi kesalahan: ' . $e->getMessage());
        }
        
        redirect('penjualan.php');
    }
}

// Get all products
$stmt = $pdo->prepare("
    SELECT id, nama, harga_beli, harga_jual, stok 
    FROM produk 
    WHERE stok > 0
    ORDER BY nama ASC
");
$stmt->execute();
$produk = $stmt->fetchAll();

// Get recent sales
$stmt = $pdo->prepare("
    SELECT 
        tp.id,
        tp.jumlah,
        tp.harga_jual,
        tp.tanggal,
        tp.keterangan,
        p.nama,
        p.harga_beli
    FROM transaksi_produk tp
    JOIN produk p ON tp.produk_id = p.id
    WHERE tp.user_id = ?
    ORDER BY tp.tanggal DESC
    LIMIT 10
");
$stmt->execute([$currentUser['id']]);
$penjualan = $stmt->fetchAll();

// Calculate total sales and profit
$stmt = $pdo->prepare("
    SELECT 
        COALESCE(SUM(tp.jumlah * tp.harga_jual), 0) as total_penjualan,
        COALESCE(SUM(tp.jumlah * (tp.harga_jual - p.harga_beli)), 0) as total_keuntungan
    FROM transaksi_produk tp
    JOIN produk p ON tp.produk_id = p.id
    WHERE tp.user_id = ?
");
$stmt->execute([$currentUser['id']]);
$total = $stmt->fetch();

// Include header
include 'includes/views/layouts/header.php';
?>

<div class="container-fluid px-4 py-3">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-3">
        <div>
            <h1 class="h4 mb-1 text-gray-800">Penjualan Produk</h1>
            <p class="text-muted small mb-0">Kelola penjualan dan laporan keuntungan</p>
        </div>
        <button type="button" class="btn btn-primary btn-sm d-flex align-items-center gap-2" data-bs-toggle="modal" data-bs-target="#addSaleModal">
            <i class="fas fa-plus"></i>
            <span>Tambah Penjualan</span>
        </button>
    </div>

    <!-- Flash Messages -->
    <?php if ($flash = getFlashMessage()): ?>
    <div class="alert alert-<?= $flash['type'] ?> alert-dismissible fade show mb-3 py-2" role="alert">
        <small><?= $flash['message'] ?></small>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    <?php endif; ?>

    <!-- Summary Cards -->
    <div class="row g-3 mb-3">
        <div class="col-xl-6 col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body py-3">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h6 class="text-success small fw-medium mb-1">Total Penjualan</h6>
                            <h5 class="mb-0 fw-bold text-gray-800">
                                Rp <?= number_format($total['total_penjualan'], 0, ',', '.') ?>
                            </h5>
                        </div>
                        <div class="ms-3">
                            <i class="fas fa-shopping-cart fa-2x text-success opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-6 col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body py-3">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h6 class="text-primary small fw-medium mb-1">Total Keuntungan</h6>
                            <h5 class="mb-0 fw-bold text-gray-800">
                                Rp <?= number_format($total['total_keuntungan'], 0, ',', '.') ?>
                            </h5>
                        </div>
                        <div class="ms-3">
                            <i class="fas fa-dollar-sign fa-2x text-primary opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Sales Table -->
    <div class="card border-0 shadow-sm">
        <div class="card-header bg-white py-2">
            <h5 class="card-title mb-0 d-flex align-items-center gap-2">
                <i class="fas fa-history text-primary small"></i>
                <span class="small fw-medium">Penjualan Terakhir</span>
            </h5>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover align-middle mb-0 small">
                    <thead>
                        <tr>
                            <th class="border-0 px-3 fw-medium">Tanggal</th>
                            <th class="border-0 px-3 fw-medium">Produk</th>
                            <th class="border-0 px-3 fw-medium">Jumlah</th>
                            <th class="border-0 px-3 fw-medium">Harga Jual</th>
                            <th class="border-0 px-3 fw-medium">Total</th>
                            <th class="border-0 px-3 fw-medium">Keuntungan</th>
                            <th class="border-0 px-3 fw-medium">Keterangan</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($penjualan as $item): ?>
                        <tr>
                            <td class="px-3"><?= date('d/m/Y', strtotime($item['tanggal'])) ?></td>
                            <td class="fw-medium px-3"><?= htmlspecialchars($item['nama']) ?></td>
                            <td class="px-3">
                                <span class="badge bg-primary rounded-pill px-2 py-1">
                                    <?= $item['jumlah'] ?>
                                </span>
                            </td>
                            <td class="text-muted px-3">Rp <?= number_format($item['harga_jual'], 0, ',', '.') ?></td>
                            <td class="text-muted px-3">Rp <?= number_format($item['jumlah'] * $item['harga_jual'], 0, ',', '.') ?></td>
                            <td class="text-success px-3">
                                Rp <?= number_format($item['jumlah'] * ($item['harga_jual'] - $item['harga_beli']), 0, ',', '.') ?>
                            </td>
                            <td class="text-muted px-3"><?= htmlspecialchars($item['keterangan']) ?></td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Add Sale Modal -->
<div class="modal fade" id="addSaleModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content border-0 shadow">
            <form action="" method="POST" class="needs-validation" novalidate>
                <input type="hidden" name="action" value="add">
                <div class="modal-header border-0 py-2">
                    <h5 class="modal-title small fw-medium">Tambah Penjualan</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body py-2">
                    <div class="mb-2">
                        <label class="form-label small mb-1">Produk</label>
                        <select class="form-select form-select-sm" name="produk_id" required>
                            <option value="">Pilih Produk</option>
                            <?php foreach ($produk as $item): ?>
                            <option value="<?= $item['id'] ?>" 
                                    data-harga="<?= $item['harga_jual'] ?>"
                                    data-stok="<?= $item['stok'] ?>">
                                <?= htmlspecialchars($item['nama']) ?> 
                                (Stok: <?= $item['stok'] ?>, 
                                Harga: Rp <?= number_format($item['harga_jual'], 0, ',', '.') ?>)
                            </option>
                            <?php endforeach; ?>
                        </select>
                        <div class="invalid-feedback small">
                            Silakan pilih produk
                        </div>
                    </div>
                    <div class="mb-2">
                        <label class="form-label small mb-1">Jumlah</label>
                        <input type="number" class="form-control form-control-sm" name="jumlah" required min="1">
                        <small class="text-muted small">Stok tersedia: <span id="stok-tersedia">0</span></small>
                        <div class="invalid-feedback small">
                            Jumlah harus diisi (minimal 1)
                        </div>
                    </div>
                    <div class="mb-2">
                        <label class="form-label small mb-1">Tanggal</label>
                        <input type="date" class="form-control form-control-sm" name="tanggal" value="<?= date('Y-m-d') ?>" required>
                        <div class="invalid-feedback small">
                            Tanggal harus diisi
                        </div>
                    </div>
                    <div class="mb-2">
                        <label class="form-label small mb-1">Keterangan</label>
                        <textarea class="form-control form-control-sm" name="keterangan" rows="2"></textarea>
                    </div>
                </div>
                <div class="modal-footer border-0 py-2">
                    <button type="button" class="btn btn-light btn-sm" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary btn-sm">Simpan</button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.card {
    transition: transform 0.2s ease-in-out;
    background: #fff;
}

.card:hover {
    transform: translateY(-5px);
}

.btn {
    padding: 0.5rem 1rem;
    border-radius: 8px;
    transition: all 0.2s ease-in-out;
}

.btn:hover {
    transform: translateY(-2px);
}

.modal-content {
    border-radius: 15px;
}

.form-control {
    border-radius: 8px;
    padding: 0.6rem 1rem;
    border: 1px solid #e0e0e0;
}

.form-control:focus {
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.15);
    border-color: #86b7fe;
}

.form-select {
    border-radius: 8px;
    padding: 0.6rem 1rem;
    border: 1px solid #e0e0e0;
}

.badge {
    font-weight: 500;
    padding: 0.5rem 1rem;
}

.bg-success {
    background-color: #198754 !important;
}

.bg-primary {
    background-color: #0d6efd !important;
}

.bg-danger {
    background-color: #dc3545 !important;
}

.btn-success {
    background-color: #198754;
    border-color: #198754;
}

.btn-primary {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.btn-danger {
    background-color: #dc3545;
    border-color: #dc3545;
}

.btn-light {
    background-color: #f8f9fa;
    border-color: #e0e0e0;
}

.text-muted {
    color: #6c757d !important;
}

.text-gray-800 {
    color: #212529 !important;
}

.shadow-sm {
    box-shadow: 0 .125rem .25rem rgba(0,0,0,.075) !important;
}

.shadow {
    box-shadow: 0 .5rem 1rem rgba(0,0,0,.15) !important;
}

.table > :not(caption) > * > * {
    padding: 1rem;
}

.pagination .page-link {
    border-radius: 8px;
    margin: 0 2px;
    border: none;
    color: #6c757d;
}

.pagination .page-item.active .page-link {
    background-color: #0d6efd;
    color: #fff;
}

.pagination .page-link:hover {
    background-color: #f8f9fa;
    color: #0d6efd;
}
</style>

<script>
// Form validation
const forms = document.querySelectorAll('.needs-validation');
Array.from(forms).forEach(form => {
    form.addEventListener('submit', event => {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        }
        form.classList.add('was-validated');
    }, false);
});

// Update stok tersedia
document.querySelector('select[name="produk_id"]').addEventListener('change', function() {
    const option = this.options[this.selectedIndex];
    const stok = option.dataset.stok;
    document.getElementById('stok-tersedia').textContent = stok;
    
    const jumlahInput = document.querySelector('input[name="jumlah"]');
    jumlahInput.max = stok;
    if (parseInt(jumlahInput.value) > parseInt(stok)) {
        jumlahInput.value = stok;
    }
});
</script>

<?php include 'includes/views/layouts/footer.php'; ?> 