<?php
require_once 'config/database.php';
require_once 'includes/helpers/functions.php';

// Cek apakah user sudah login
if (!isLoggedIn()) {
    setFlashMessage('danger', 'Silakan login terlebih dahulu');
    redirect('login.php');
}

// Dapatkan data user yang sedang login
$currentUser = getCurrentUser();
if (!$currentUser) {
    setFlashMessage('danger', 'Sesi tidak valid');
    redirect('login.php');
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'add') {
        $produk_id = $_POST['produk_id'] ?? 0;
        $jumlah = $_POST['jumlah'] ?? 0;
        $tanggal = $_POST['tanggal'] ?? date('Y-m-d');
        $keterangan = $_POST['keterangan'] ?? '';
        
        try {
            // Get product data
            $stmt = $pdo->prepare("SELECT * FROM produk WHERE id = ?");
            $stmt->execute([$produk_id]);
            $produk = $stmt->fetch();
            
            if (!$produk) {
                throw new Exception('Produk tidak ditemukan');
            }
            
            if ($produk['stok'] < $jumlah) {
                throw new Exception('Stok tidak mencukupi');
            }
            
            // Calculate total
            $total = $jumlah * $produk['harga_jual'];
            
            // Begin transaction
            $pdo->beginTransaction();
            
            // Insert transaction
            $stmt = $pdo->prepare("
                INSERT INTO transaksi_produk (user_id, produk_id, jumlah, harga_jual, total, tanggal, keterangan)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $currentUser['id'], 
                $produk_id, 
                $jumlah, 
                $produk['harga_jual'], 
                $total, 
                $tanggal, 
                $keterangan
            ]);
            
            // Update stock
            $stmt = $pdo->prepare("
                UPDATE produk 
                SET stok = stok - ? 
                WHERE id = ?
            ");
            $stmt->execute([$jumlah, $produk_id]);
            
            // Get kategori_id for Penjualan
            $stmt = $pdo->prepare("SELECT id FROM kategori WHERE nama = 'Penjualan' AND tipe = 'masuk' LIMIT 1");
            $stmt->execute();
            $kategori = $stmt->fetch();
            
            if ($kategori) {
            // Insert to transaksi table
            $stmt = $pdo->prepare("
                INSERT INTO transaksi (user_id, kategori_id, jumlah, keterangan, tanggal)
                    VALUES (?, ?, ?, ?, ?)
            ");
                $stmt->execute([
                    $currentUser['id'], 
                    $kategori['id'], 
                    $total, 
                    "Penjualan produk: " . $produk['nama'], 
                    $tanggal
                ]);
            }
            
            $pdo->commit();
            setFlashMessage('success', 'Penjualan berhasil dicatat');
            
        } catch (Exception $e) {
            $pdo->rollBack();
            setFlashMessage('danger', 'Terjadi kesalahan: ' . $e->getMessage());
        }
        
        redirect('penjualan.php');
    }
}

// Get all products
$stmt = $pdo->prepare("
    SELECT id, nama, harga_beli, harga_jual, stok 
    FROM produk 
    WHERE stok > 0
    ORDER BY nama ASC
");
$stmt->execute();
$produk = $stmt->fetchAll();

// Get recent sales
$stmt = $pdo->prepare("
    SELECT 
        tp.id,
        tp.jumlah,
        tp.harga_jual,
        tp.tanggal,
        tp.keterangan,
        p.nama,
        p.harga_beli
    FROM transaksi_produk tp
    JOIN produk p ON tp.produk_id = p.id
    WHERE tp.user_id = ?
    ORDER BY tp.tanggal DESC
    LIMIT 10
");
$stmt->execute([$currentUser['id']]);
$penjualan = $stmt->fetchAll();

// Calculate total sales and profit
$stmt = $pdo->prepare("
    SELECT 
        COALESCE(SUM(tp.jumlah * tp.harga_jual), 0) as total_penjualan,
        COALESCE(SUM(tp.jumlah * (tp.harga_jual - p.harga_beli)), 0) as total_keuntungan
    FROM transaksi_produk tp
    JOIN produk p ON tp.produk_id = p.id
    WHERE tp.user_id = ?
");
$stmt->execute([$currentUser['id']]);
$total = $stmt->fetch();

// Include header and sidebar
require_once 'includes/views/layouts/header.php';
require_once 'includes/views/layouts/sidebar.php';
?>

<div class="modern-layout">
    <div class="modern-container">
        <!-- Modern Page Header -->
        <div class="modern-page-header modern-flex modern-justify-between modern-items-center modern-mb-xl">
            <div>
                <h1 class="modern-page-title modern-mb-0">Penjualan Produk</h1>
                <p class="modern-page-subtitle">Kelola penjualan dan laporan keuntungan dengan mudah</p>
            </div>
            <div class="modern-page-actions">
                <button type="button" class="modern-btn modern-btn-primary" data-bs-toggle="modal" data-bs-target="#addSaleModal">
                    <i class="fas fa-plus"></i>
                    Tambah Penjualan
                </button>
            </div>
        </div>

        <!-- Flash Messages -->
        <?php if ($flash = getFlashMessage()): ?>
        <div class="modern-alert modern-alert-<?= $flash['type'] ?> modern-mb-lg">
            <div class="modern-alert-content">
                <i class="fas fa-<?= $flash['type'] === 'success' ? 'check-circle' : 'exclamation-triangle' ?> modern-alert-icon"></i>
                <div class="modern-alert-message"><?= $flash['message'] ?></div>
            </div>
            <button type="button" class="modern-alert-close" onclick="this.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <?php endif; ?>

        <!-- Modern Summary Cards -->
        <div class="modern-grid modern-grid-cols-2 modern-gap-lg modern-mb-xl">
            <div class="modern-stats-card modern-stats-success">
                <div class="modern-stats-content">
                    <div class="modern-stats-info">
                        <div class="modern-stats-label">Total Penjualan</div>
                        <div class="modern-stats-value"><?= formatRupiah($total['total_penjualan']) ?></div>
                        <div class="modern-stats-meta">Pendapatan kotor</div>
                    </div>
                    <div class="modern-stats-icon">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                </div>
                <div class="modern-stats-glow"></div>
            </div>

            <div class="modern-stats-card modern-stats-primary">
                <div class="modern-stats-content">
                    <div class="modern-stats-info">
                        <div class="modern-stats-label">Total Keuntungan</div>
                        <div class="modern-stats-value"><?= formatRupiah($total['total_keuntungan']) ?></div>
                        <div class="modern-stats-meta">Profit bersih</div>
                    </div>
                    <div class="modern-stats-icon">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                </div>
                <div class="modern-stats-glow"></div>
            </div>
        </div>

        <!-- Modern Recent Sales Table -->
        <div class="modern-card">
            <div class="modern-card-header">
                <h5 class="modern-card-title">
                    <i class="fas fa-history modern-text-primary modern-mr-sm"></i>
                    Penjualan Terakhir
                </h5>
                <div class="modern-card-actions">
                    <span class="modern-badge modern-badge-info">
                        <?= count($penjualan) ?> transaksi
                    </span>
                </div>
            </div>
            <div class="modern-card-body modern-p-0">
                <div class="modern-table-responsive">
                    <table class="modern-table">
                        <thead class="modern-table-header">
                            <tr>
                                <th class="modern-table-th">
                                    <i class="fas fa-calendar modern-mr-xs"></i>
                                    Tanggal
                                </th>
                                <th class="modern-table-th">
                                    <i class="fas fa-box modern-mr-xs"></i>
                                    Produk
                                </th>
                                <th class="modern-table-th modern-text-center">
                                    <i class="fas fa-sort-numeric-up modern-mr-xs"></i>
                                    Jumlah
                                </th>
                                <th class="modern-table-th modern-text-right">
                                    <i class="fas fa-tag modern-mr-xs"></i>
                                    Harga Jual
                                </th>
                                <th class="modern-table-th modern-text-right">
                                    <i class="fas fa-calculator modern-mr-xs"></i>
                                    Total
                                </th>
                                <th class="modern-table-th modern-text-right">
                                    <i class="fas fa-chart-line modern-mr-xs"></i>
                                    Keuntungan
                                </th>
                                <th class="modern-table-th">
                                    <i class="fas fa-sticky-note modern-mr-xs"></i>
                                    Keterangan
                                </th>
                            </tr>
                        </thead>
                        <tbody class="modern-table-body">
                            <?php if (empty($penjualan)): ?>
                            <tr>
                                <td colspan="7" class="modern-table-td">
                                    <div class="modern-empty-state">
                                        <div class="modern-empty-icon">
                                            <i class="fas fa-shopping-cart"></i>
                                        </div>
                                        <div class="modern-empty-content">
                                            <h6 class="modern-empty-title">Belum Ada Penjualan</h6>
                                            <p class="modern-empty-text">Belum ada transaksi penjualan yang tercatat</p>
                                            <button type="button" class="modern-btn modern-btn-primary modern-btn-sm" data-bs-toggle="modal" data-bs-target="#addSaleModal">
                                                <i class="fas fa-plus"></i>
                                                Tambah Penjualan Pertama
                                            </button>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <?php else: ?>
                            <?php foreach ($penjualan as $item): ?>
                            <tr class="modern-table-row">
                                <td class="modern-table-td">
                                    <div class="modern-table-date">
                                        <i class="fas fa-calendar modern-text-muted modern-mr-xs"></i>
                                        <?= formatDate($item['tanggal']) ?>
                                    </div>
                                </td>
                                <td class="modern-table-td">
                                    <div class="modern-table-content">
                                        <div class="modern-table-title"><?= htmlspecialchars($item['nama']) ?></div>
                                    </div>
                                </td>
                                <td class="modern-table-td modern-text-center">
                                    <div class="modern-badge modern-badge-primary">
                                        <i class="fas fa-cube"></i>
                                        <?= $item['jumlah'] ?>
                                    </div>
                                </td>
                                <td class="modern-table-td modern-text-right">
                                    <div class="modern-table-amount"><?= formatRupiah($item['harga_jual']) ?></div>
                                </td>
                                <td class="modern-table-td modern-text-right">
                                    <div class="modern-table-amount modern-text-success"><?= formatRupiah($item['jumlah'] * $item['harga_jual']) ?></div>
                                </td>
                                <td class="modern-table-td modern-text-right">
                                    <div class="modern-table-amount modern-text-primary">
                                        <?= formatRupiah($item['jumlah'] * ($item['harga_jual'] - $item['harga_beli'])) ?>
                                    </div>
                                </td>
                                <td class="modern-table-td">
                                    <div class="modern-table-subtitle"><?= htmlspecialchars($item['keterangan']) ?: '-' ?></div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modern Add Sale Modal -->
<div class="modal fade" id="addSaleModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modern-modal-content">
            <div class="modern-modal-header">
                <h5 class="modern-modal-title">
                    <i class="fas fa-plus modern-text-primary modern-mr-sm"></i>
                    Tambah Penjualan
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="" method="POST" class="needs-validation" novalidate>
                <input type="hidden" name="action" value="add">
                <div class="modern-modal-body">
                    <div class="modern-form-group">
                        <label class="modern-form-label">
                            <i class="fas fa-box modern-text-primary"></i>
                            Produk <span class="modern-text-danger">*</span>
                        </label>
                        <select class="modern-form-control" name="produk_id" required>
                            <option value="">Pilih Produk</option>
                            <?php foreach ($produk as $item): ?>
                            <option value="<?= $item['id'] ?>"
                                    data-harga="<?= $item['harga_jual'] ?>"
                                    data-stok="<?= $item['stok'] ?>">
                                📦 <?= htmlspecialchars($item['nama']) ?>
                                (Stok: <?= $item['stok'] ?>,
                                Harga: <?= formatRupiah($item['harga_jual']) ?>)
                            </option>
                            <?php endforeach; ?>
                        </select>
                        <div class="invalid-feedback">
                            Silakan pilih produk
                        </div>
                    </div>

                    <div class="modern-form-group">
                        <label class="modern-form-label">
                            <i class="fas fa-sort-numeric-up modern-text-primary"></i>
                            Jumlah <span class="modern-text-danger">*</span>
                        </label>
                        <input type="number" class="modern-form-control" name="jumlah" required min="1" placeholder="Masukkan jumlah">
                        <small class="modern-form-help">
                            <i class="fas fa-warehouse modern-mr-xs"></i>
                            Stok tersedia: <span id="stok-tersedia" class="modern-font-semibold">0</span>
                        </small>
                        <div class="invalid-feedback">
                            Jumlah harus diisi (minimal 1)
                        </div>
                    </div>

                    <div class="modern-form-group">
                        <label class="modern-form-label">
                            <i class="fas fa-calendar-alt modern-text-primary"></i>
                            Tanggal <span class="modern-text-danger">*</span>
                        </label>
                        <input type="date" class="modern-form-control" name="tanggal" value="<?= date('Y-m-d') ?>" required>
                        <div class="invalid-feedback">
                            Tanggal harus diisi
                        </div>
                    </div>

                    <div class="modern-form-group modern-mb-0">
                        <label class="modern-form-label">
                            <i class="fas fa-sticky-note modern-text-primary"></i>
                            Keterangan
                        </label>
                        <textarea class="modern-form-control" name="keterangan" rows="3" placeholder="Catatan tambahan (opsional)"></textarea>
                    </div>
                </div>
                <div class="modern-modal-footer">
                    <button type="button" class="modern-btn modern-btn-light" data-bs-dismiss="modal">
                        <i class="fas fa-times"></i>
                        Batal
                    </button>
                    <button type="submit" class="modern-btn modern-btn-primary">
                        <i class="fas fa-save"></i>
                        Simpan Penjualan
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.card {
    transition: transform 0.2s ease-in-out;
    background: #fff;
}

.card:hover {
    transform: translateY(-5px);
}

.btn {
    padding: 0.5rem 1rem;
    border-radius: 8px;
    transition: all 0.2s ease-in-out;
}

.btn:hover {
    transform: translateY(-2px);
}

.modal-content {
    border-radius: 15px;
}

.form-control {
    border-radius: 8px;
    padding: 0.6rem 1rem;
    border: 1px solid #e0e0e0;
}

.form-control:focus {
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.15);
    border-color: #86b7fe;
}

.form-select {
    border-radius: 8px;
    padding: 0.6rem 1rem;
    border: 1px solid #e0e0e0;
}

.badge {
    font-weight: 500;
    padding: 0.5rem 1rem;
}

.bg-success {
    background-color: #198754 !important;
}

.bg-primary {
    background-color: #0d6efd !important;
}

.bg-danger {
    background-color: #dc3545 !important;
}

.btn-success {
    background-color: #198754;
    border-color: #198754;
}

.btn-primary {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.btn-danger {
    background-color: #dc3545;
    border-color: #dc3545;
}

.btn-light {
    background-color: #f8f9fa;
    border-color: #e0e0e0;
}

.text-muted {
    color: #6c757d !important;
}

.text-gray-800 {
    color: #212529 !important;
}

.shadow-sm {
    box-shadow: 0 .125rem .25rem rgba(0,0,0,.075) !important;
}

.shadow {
    box-shadow: 0 .5rem 1rem rgba(0,0,0,.15) !important;
}

.table > :not(caption) > * > * {
    padding: 1rem;
}

.pagination .page-link {
    border-radius: 8px;
    margin: 0 2px;
    border: none;
    color: #6c757d;
}

.pagination .page-item.active .page-link {
    background-color: #0d6efd;
    color: #fff;
}

.pagination .page-link:hover {
    background-color: #f8f9fa;
    color: #0d6efd;
}
</style>

<script>
// Form validation
const forms = document.querySelectorAll('.needs-validation');
Array.from(forms).forEach(form => {
    form.addEventListener('submit', event => {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        }
        form.classList.add('was-validated');
    }, false);
});

// Update stok tersedia
document.querySelector('select[name="produk_id"]').addEventListener('change', function() {
    const option = this.options[this.selectedIndex];
    const stok = option.dataset.stok;
    document.getElementById('stok-tersedia').textContent = stok;
    
    const jumlahInput = document.querySelector('input[name="jumlah"]');
    jumlahInput.max = stok;
    if (parseInt(jumlahInput.value) > parseInt(stok)) {
        jumlahInput.value = stok;
    }
});
</script>

<?php include 'includes/views/layouts/footer.php'; ?> 