<?php
require_once 'includes/config/database.php';

$results = [];

try {
    // 1. Check if supplier table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'supplier'");
    if ($stmt->rowCount() == 0) {
        // Create supplier table
        $pdo->exec("CREATE TABLE supplier (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            nama_supplier VARCHAR(255) NOT NULL,
            kontak VARCHAR(100),
            email VARCHAR(255),
            alamat TEXT,
            keterangan TEXT,
            status ENUM('aktif', 'nonaktif') DEFAULT 'aktif',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        )");
        $results[] = "✅ Supplier table created successfully";
    } else {
        $results[] = "✅ Supplier table already exists";
        
        // 2. Check if status column exists
        $stmt = $pdo->query("SHOW COLUMNS FROM supplier LIKE 'status'");
        if ($stmt->rowCount() == 0) {
            // Add status column
            $pdo->exec("ALTER TABLE supplier ADD COLUMN status ENUM('aktif', 'nonaktif') DEFAULT 'aktif' AFTER keterangan");
            $results[] = "✅ Status column added successfully";
        } else {
            $results[] = "✅ Status column already exists";
        }
        
        // 3. Check if created_at column exists
        $stmt = $pdo->query("SHOW COLUMNS FROM supplier LIKE 'created_at'");
        if ($stmt->rowCount() == 0) {
            // Add created_at column
            $pdo->exec("ALTER TABLE supplier ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP AFTER status");
            $results[] = "✅ Created_at column added successfully";
        } else {
            $results[] = "✅ Created_at column already exists";
        }
        
        // 4. Check if updated_at column exists
        $stmt = $pdo->query("SHOW COLUMNS FROM supplier LIKE 'updated_at'");
        if ($stmt->rowCount() == 0) {
            // Add updated_at column
            $pdo->exec("ALTER TABLE supplier ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP AFTER created_at");
            $results[] = "✅ Updated_at column added successfully";
        } else {
            $results[] = "✅ Updated_at column already exists";
        }
    }
    
    // 5. Update existing records to have default status
    $stmt = $pdo->query("SELECT COUNT(*) FROM supplier WHERE status IS NULL");
    $nullStatusCount = $stmt->fetchColumn();
    if ($nullStatusCount > 0) {
        $pdo->exec("UPDATE supplier SET status = 'aktif' WHERE status IS NULL");
        $results[] = "✅ Updated $nullStatusCount records with default status";
    }
    
    // 6. Test insert
    $testName = 'Test Supplier ' . date('Y-m-d H:i:s');
    $stmt = $pdo->prepare("INSERT INTO supplier (user_id, nama_supplier, kontak, email, alamat, keterangan, status) VALUES (1, ?, '081234567890', '<EMAIL>', 'Test Address', 'Test supplier for validation', 'aktif')");
    if ($stmt->execute([$testName])) {
        $testId = $pdo->lastInsertId();
        $results[] = "✅ Test insert successful (ID: $testId)";
        
        // Clean up test record
        $pdo->prepare("DELETE FROM supplier WHERE id = ?")->execute([$testId]);
        $results[] = "✅ Test record cleaned up";
    } else {
        $results[] = "❌ Test insert failed";
    }
    
    $results[] = "🎉 All supplier table fixes completed successfully!";
    
} catch (PDOException $e) {
    $results[] = "❌ Error: " . $e->getMessage();
}

?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fix Supplier Table - KeuanganKu</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card shadow">
                    <div class="card-header bg-success text-white">
                        <h4 class="mb-0">
                            <i class="fas fa-wrench me-2"></i>Fix Supplier Table Results
                        </h4>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>Supplier Table Fix Results:</h6>
                            <ul class="mb-0">
                                <?php foreach ($results as $result): ?>
                                <li><?= $result ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                        
                        <div class="text-center">
                            <a href="/keuangan/supplier.php" class="btn btn-primary me-2">
                                <i class="fas fa-truck me-2"></i>Test Supplier Page
                            </a>
                            <a href="/keuangan/debug_supplier.php" class="btn btn-info me-2">
                                <i class="fas fa-bug me-2"></i>Debug Info
                            </a>
                            <a href="/keuangan/index.php" class="btn btn-success">
                                <i class="fas fa-home me-2"></i>Dashboard
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
