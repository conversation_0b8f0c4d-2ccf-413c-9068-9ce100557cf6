-- Create system_settings table for storing customization settings
CREATE TABLE IF NOT EXISTS `system_settings` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `setting_key` varchar(100) NOT NULL,
    `setting_value` text,
    `setting_type` enum('string','integer','boolean','json') DEFAULT 'string',
    `description` text,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `setting_key` (`setting_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert default settings
INSERT INTO `system_settings` (`setting_key`, `setting_value`, `setting_type`, `description`) VALUES
('sidebar_style', 'default', 'string', 'Sidebar style (default, modern, minimal, compact)'),
('sidebar_color', 'dark', 'string', 'Sidebar color theme'),
('sidebar_position', 'left', 'string', 'Sidebar position (left, right)'),
('sidebar_collapsed', '0', 'boolean', 'Start with collapsed sidebar'),
('sidebar_animation', 'slide', 'string', 'Sidebar animation type'),
('navbar_style', 'default', 'string', 'Navbar style'),
('navbar_color', 'primary', 'string', 'Navbar color theme'),
('navbar_position', 'top', 'string', 'Navbar position'),
('navbar_brand_show', '1', 'boolean', 'Show brand/logo in navbar'),
('navbar_search_show', '1', 'boolean', 'Show search box in navbar'),
('page_transition', 'none', 'string', 'Page transition effect'),
('hover_effects', '1', 'boolean', 'Enable hover effects'),
('loading_animation', 'spinner', 'string', 'Loading animation type'),
('card_animations', '0', 'boolean', 'Enable card animations'),
('smooth_scrolling', '0', 'boolean', 'Enable smooth scrolling'),
('site_title', 'Sistem Keuangan', 'string', 'Site title'),
('site_description', 'Sistem Manajemen Keuangan', 'string', 'Site description'),
('theme_mode', 'light', 'string', 'Theme mode (light, dark, auto)'),
('language', 'id', 'string', 'System language'),
('timezone', 'Asia/Jakarta', 'string', 'System timezone')
ON DUPLICATE KEY UPDATE
    `setting_value` = VALUES(`setting_value`),
    `updated_at` = CURRENT_TIMESTAMP;

-- Create custom_themes table
CREATE TABLE IF NOT EXISTS `custom_themes` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `name` varchar(100) NOT NULL,
    `theme_data` json NOT NULL,
    `is_active` tinyint(1) DEFAULT 0,
    `created_by` int(11) NOT NULL,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_created_by` (`created_by`),
    KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create custom_menus table
CREATE TABLE IF NOT EXISTS `custom_menus` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `menu_id` varchar(50) NOT NULL,
    `label` varchar(100) NOT NULL,
    `icon` varchar(50) DEFAULT NULL,
    `url` varchar(255) DEFAULT NULL,
    `parent_id` int(11) DEFAULT NULL,
    `sort_order` int(11) DEFAULT 0,
    `is_active` tinyint(1) DEFAULT 1,
    `created_by` int(11) NOT NULL,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `menu_id` (`menu_id`),
    KEY `idx_parent_id` (`parent_id`),
    KEY `idx_sort_order` (`sort_order`),
    KEY `idx_is_active` (`is_active`),
    KEY `idx_created_by` (`created_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create layout_components table
CREATE TABLE IF NOT EXISTS `layout_components` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `component_name` varchar(100) NOT NULL,
    `component_type` enum('widget','block','section') DEFAULT 'widget',
    `component_data` json NOT NULL,
    `position` varchar(50) DEFAULT NULL,
    `sort_order` int(11) DEFAULT 0,
    `is_active` tinyint(1) DEFAULT 1,
    `created_by` int(11) NOT NULL,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_component_type` (`component_type`),
    KEY `idx_position` (`position`),
    KEY `idx_sort_order` (`sort_order`),
    KEY `idx_is_active` (`is_active`),
    KEY `idx_created_by` (`created_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create custom_css table
CREATE TABLE IF NOT EXISTS `custom_css` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `css_name` varchar(100) NOT NULL,
    `css_content` longtext NOT NULL,
    `is_active` tinyint(1) DEFAULT 1,
    `created_by` int(11) NOT NULL,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_is_active` (`is_active`),
    KEY `idx_created_by` (`created_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create page_templates table
CREATE TABLE IF NOT EXISTS `page_templates` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `name` varchar(100) NOT NULL,
    `type` enum('page','section','component','layout','email') DEFAULT 'page',
    `category` varchar(50) DEFAULT 'general',
    `description` text,
    `html_content` longtext,
    `css_content` longtext,
    `js_content` longtext,
    `is_active` tinyint(1) DEFAULT 1,
    `created_by` int(11) NOT NULL,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_type` (`type`),
    KEY `idx_category` (`category`),
    KEY `idx_is_active` (`is_active`),
    KEY `idx_created_by` (`created_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create customization_presets table
CREATE TABLE IF NOT EXISTS `customization_presets` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `name` varchar(100) NOT NULL,
    `description` text,
    `preset_data` json NOT NULL,
    `preset_type` enum('theme','layout','complete') DEFAULT 'complete',
    `is_default` tinyint(1) DEFAULT 0,
    `is_active` tinyint(1) DEFAULT 1,
    `created_by` int(11) NOT NULL,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_preset_type` (`preset_type`),
    KEY `idx_is_default` (`is_default`),
    KEY `idx_is_active` (`is_active`),
    KEY `idx_created_by` (`created_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create user_customizations table
CREATE TABLE IF NOT EXISTS `user_customizations` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `user_id` int(11) NOT NULL,
    `customization_key` varchar(100) NOT NULL,
    `customization_value` json NOT NULL,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `unique_user_key` (`user_id`, `customization_key`),
    KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
