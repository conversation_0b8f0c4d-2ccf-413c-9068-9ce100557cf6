<?php
/**
 * Helper Functions
 * 
 * This file contains various helper functions used throughout the application.
 */

/**
 * Format currency
 * 
 * @param float $amount Amount to format
 * @param string $currency Currency code
 * @return string
 */
function format_currency($amount, $currency = DEFAULT_CURRENCY) {
    $formatted = number_format(
        $amount,
        CURRENCY_DECIMAL_PLACES,
        CURRENCY_DECIMAL_SEPARATOR,
        CURRENCY_THOUSAND_SEPARATOR
    );
    
    return CURRENCY_SYMBOL . ' ' . $formatted;
}

/**
 * Format date
 * 
 * @param string $date Date to format
 * @param string $format Format to use
 * @return string
 */
function format_date($date, $format = DATE_FORMAT) {
    return date($format, strtotime($date));
}

/**
 * Format datetime
 * 
 * @param string $datetime Datetime to format
 * @param string $format Format to use
 * @return string
 */
function format_datetime($datetime, $format = DATETIME_FORMAT) {
    return date($format, strtotime($datetime));
}

/**
 * Get current date
 * 
 * @param string $format Format to use
 * @return string
 */
function get_current_date($format = DATE_FORMAT) {
    return date($format);
}

/**
 * Get current datetime
 * 
 * @param string $format Format to use
 * @return string
 */
function get_current_datetime($format = DATETIME_FORMAT) {
    return date($format);
}

/**
 * Generate random string
 * 
 * @param int $length Length of string
 * @return string
 */
function generate_random_string($length = 10) {
    $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $string = '';
    
    for ($i = 0; $i < $length; $i++) {
        $string .= $characters[rand(0, strlen($characters) - 1)];
    }
    
    return $string;
}

/**
 * Generate slug
 * 
 * @param string $text Text to slugify
 * @return string
 */
function generate_slug($text) {
    // Replace non letter or digits by -
    $text = preg_replace('~[^\pL\d]+~u', '-', $text);
    
    // Transliterate
    $text = iconv('utf-8', 'us-ascii//TRANSLIT', $text);
    
    // Remove unwanted characters
    $text = preg_replace('~[^-\w]+~', '', $text);
    
    // Trim
    $text = trim($text, '-');
    
    // Remove duplicate -
    $text = preg_replace('~-+~', '-', $text);
    
    // Lowercase
    $text = strtolower($text);
    
    return $text;
}

/**
 * Get file extension
 * 
 * @param string $filename Filename
 * @return string
 */
function get_file_extension($filename) {
    return strtolower(pathinfo($filename, PATHINFO_EXTENSION));
}

/**
 * Check if file extension is allowed
 * 
 * @param string $filename Filename
 * @return bool
 */
function is_allowed_extension($filename) {
    $extension = get_file_extension($filename);
    return in_array($extension, ALLOWED_EXTENSIONS);
}

/**
 * Check if file size is allowed
 * 
 * @param int $size File size in bytes
 * @return bool
 */
function is_allowed_size($size) {
    return $size <= UPLOAD_MAX_SIZE;
}

/**
 * Generate unique filename
 * 
 * @param string $filename Original filename
 * @return string
 */
function generate_unique_filename($filename) {
    $extension = get_file_extension($filename);
    $name = pathinfo($filename, PATHINFO_FILENAME);
    $slug = generate_slug($name);
    
    return $slug . '-' . uniqid() . '.' . $extension;
}

/**
 * Upload file
 * 
 * @param array $file File data from $_FILES
 * @param string $destination Destination directory
 * @return string|false
 */
function upload_file($file, $destination) {
    if (!isset($file['error']) || is_array($file['error'])) {
        return false;
    }
    
    switch ($file['error']) {
        case UPLOAD_ERR_OK:
            break;
        case UPLOAD_ERR_INI_SIZE:
        case UPLOAD_ERR_FORM_SIZE:
            return false;
        case UPLOAD_ERR_PARTIAL:
            return false;
        case UPLOAD_ERR_NO_FILE:
            return false;
        case UPLOAD_ERR_NO_TMP_DIR:
            return false;
        case UPLOAD_ERR_CANT_WRITE:
            return false;
        case UPLOAD_ERR_EXTENSION:
            return false;
        default:
            return false;
    }
    
    if (!is_allowed_size($file['size'])) {
        return false;
    }
    
    if (!is_allowed_extension($file['name'])) {
        return false;
    }
    
    $filename = generate_unique_filename($file['name']);
    $destination = rtrim($destination, '/') . '/' . $filename;
    
    if (!move_uploaded_file($file['tmp_name'], $destination)) {
        return false;
    }
    
    return $filename;
}

/**
 * Delete file
 * 
 * @param string $filename Filename
 * @param string $directory Directory
 * @return bool
 */
function delete_file($filename, $directory) {
    $path = rtrim($directory, '/') . '/' . $filename;
    
    if (file_exists($path)) {
        return unlink($path);
    }
    
    return false;
}

/**
 * Get pagination data
 * 
 * @param int $total Total items
 * @param int $page Current page
 * @param int $per_page Items per page
 * @return array
 */
function get_pagination($total, $page = 1, $per_page = ITEMS_PER_PAGE) {
    $total_pages = ceil($total / $per_page);
    $page = max(1, min($page, $total_pages));
    
    $start = ($page - 1) * $per_page;
    $end = min($start + $per_page, $total);
    
    $pages = [];
    $start_page = max(1, $page - floor(PAGINATION_LINKS / 2));
    $end_page = min($total_pages, $start_page + PAGINATION_LINKS - 1);
    
    for ($i = $start_page; $i <= $end_page; $i++) {
        $pages[] = $i;
    }
    
    return [
        'total' => $total,
        'per_page' => $per_page,
        'current_page' => $page,
        'total_pages' => $total_pages,
        'start' => $start,
        'end' => $end,
        'pages' => $pages,
        'has_previous' => $page > 1,
        'has_next' => $page < $total_pages
    ];
}

/**
 * Get pagination HTML
 * 
 * @param array $pagination Pagination data
 * @param string $url URL pattern
 * @return string
 */
function get_pagination_html($pagination, $url) {
    $html = '<nav aria-label="Page navigation"><ul class="pagination">';
    
    // Previous button
    if ($pagination['has_previous']) {
        $html .= '<li class="page-item">';
        $html .= '<a class="page-link" href="' . sprintf($url, $pagination['current_page'] - 1) . '">';
        $html .= '&laquo;';
        $html .= '</a></li>';
    } else {
        $html .= '<li class="page-item disabled">';
        $html .= '<span class="page-link">&laquo;</span></li>';
    }
    
    // Page numbers
    foreach ($pagination['pages'] as $page) {
        if ($page == $pagination['current_page']) {
            $html .= '<li class="page-item active">';
            $html .= '<span class="page-link">' . $page . '</span></li>';
        } else {
            $html .= '<li class="page-item">';
            $html .= '<a class="page-link" href="' . sprintf($url, $page) . '">' . $page . '</a></li>';
        }
    }
    
    // Next button
    if ($pagination['has_next']) {
        $html .= '<li class="page-item">';
        $html .= '<a class="page-link" href="' . sprintf($url, $pagination['current_page'] + 1) . '">';
        $html .= '&raquo;';
        $html .= '</a></li>';
    } else {
        $html .= '<li class="page-item disabled">';
        $html .= '<span class="page-link">&raquo;</span></li>';
    }
    
    $html .= '</ul></nav>';
    
    return $html;
}

/**
 * Get flash message
 * 
 * @param string $type Message type
 * @param string $message Message
 * @return void
 */
function set_flash_message($type, $message) {
    $_SESSION['flash_message'] = [
        'type' => $type,
        'message' => $message
    ];
}

/**
 * Get flash message
 * 
 * @return array|null
 */
function get_flash_message() {
    if (isset($_SESSION['flash_message'])) {
        $message = $_SESSION['flash_message'];
        unset($_SESSION['flash_message']);
        return $message;
    }
    
    return null;
}

/**
 * Display flash message
 * 
 * @return string
 */
function display_flash_message() {
    $message = get_flash_message();
    
    if ($message) {
        $html = '<div class="alert alert-' . $message['type'] . ' alert-dismissible fade show" role="alert">';
        $html .= $message['message'];
        $html .= '<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>';
        $html .= '</div>';
        
        return $html;
    }
    
    return '';
}

/**
 * Redirect function is defined in includes/helpers/functions.php
 * @see includes/helpers/functions.php
 */

/**
 * Get current URL
 * 
 * @return string
 */
function get_current_url() {
    return (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]$_SERVER[REQUEST_URI]";
}

/**
 * Get base URL
 * 
 * @return string
 */
function get_base_url() {
    return (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]";
}

/**
 * Get asset URL
 * 
 * @param string $path Asset path
 * @return string
 */
function asset_url($path) {
    return get_base_url() . '/assets/' . ltrim($path, '/');
}

/**
 * Get upload URL
 * 
 * @param string $path Upload path
 * @return string
 */
function upload_url($path) {
    return get_base_url() . '/uploads/' . ltrim($path, '/');
}

/**
 * Get current user function is defined in includes/helpers/functions.php
 * @see includes/helpers/functions.php
 */

/**
 * Check if user is logged in function is defined in includes/init.php
 * @see includes/init.php
 */

/**
 * Check if user has permission function is defined in includes/init.php
 * @see includes/init.php
 */

/**
 * Require permission function is defined in includes/init.php
 * @see includes/init.php
 */

/**
 * Log activity
 * 
 * @param string $action Action performed
 * @param string $description Description of action
 * @return void
 */
function log_activity($action, $description) {
    global $db;
    
    $user_id = $_SESSION['user_id'] ?? null;
    
    $stmt = $db->prepare("
        INSERT INTO activity_logs (user_id, action, description, ip_address, user_agent, created_at)
        VALUES (?, ?, ?, ?, ?, NOW())
    ");
    
    $stmt->execute([
        $user_id,
        $action,
        $description,
        $_SERVER['REMOTE_ADDR'],
        $_SERVER['HTTP_USER_AGENT']
    ]);
}

/**
 * Get activity logs
 * 
 * @param int $limit Limit
 * @return array
 */
function get_activity_logs($limit = 10) {
    global $db;
    
    $stmt = $db->prepare("
        SELECT al.*, u.name as user_name
        FROM activity_logs al
        LEFT JOIN users u ON al.user_id = u.id
        ORDER BY al.created_at DESC
        LIMIT ?
    ");
    
    $stmt->execute([$limit]);
    
    return $stmt->fetchAll();
}

/**
 * Send email
 * 
 * @param string $to Recipient email
 * @param string $subject Email subject
 * @param string $body Email body
 * @return bool
 */
function send_email($to, $subject, $body) {
    require_once ROOT_PATH . '/vendor/autoload.php';
    
    $mail = new PHPMailer\PHPMailer\PHPMailer(true);
    
    try {
        $mail->isSMTP();
        $mail->Host = SMTP_HOST;
        $mail->SMTPAuth = true;
        $mail->Username = SMTP_USERNAME;
        $mail->Password = SMTP_PASSWORD;
        $mail->SMTPSecure = PHPMailer\PHPMailer\PHPMailer::ENCRYPTION_STARTTLS;
        $mail->Port = SMTP_PORT;
        
        $mail->setFrom(SMTP_FROM_EMAIL, SMTP_FROM_NAME);
        $mail->addAddress($to);
        
        $mail->isHTML(true);
        $mail->Subject = $subject;
        $mail->Body = $body;
        
        return $mail->send();
    } catch (Exception $e) {
        error_log("Error sending email: " . $e->getMessage());
        return false;
    }
}

/**
 * Generate password reset token
 * 
 * @param int $user_id User ID
 * @return string
 */
function generate_password_reset_token($user_id) {
    global $db;
    
    $token = bin2hex(random_bytes(32));
    $expires = date('Y-m-d H:i:s', strtotime('+1 hour'));
    
    $stmt = $db->prepare("
        INSERT INTO password_resets (user_id, token, expires_at)
        VALUES (?, ?, ?)
    ");
    
    $stmt->execute([$user_id, $token, $expires]);
    
    return $token;
}

/**
 * Verify password reset token
 * 
 * @param string $token Token to verify
 * @return int|false
 */
function verify_password_reset_token($token) {
    global $db;
    
    $stmt = $db->prepare("
        SELECT user_id 
        FROM password_resets 
        WHERE token = ? AND expires_at > NOW() AND used = 0
    ");
    
    $stmt->execute([$token]);
    
    return $stmt->fetchColumn();
}

/**
 * Mark password reset token as used
 * 
 * @param string $token Token to mark as used
 * @return bool
 */
function mark_password_reset_token_used($token) {
    global $db;
    
    $stmt = $db->prepare("
        UPDATE password_resets 
        SET used = 1 
        WHERE token = ?
    ");
    
    return $stmt->execute([$token]);
}

/**
 * Hash password
 * 
 * @param string $password Password to hash
 * @return string
 */
function hash_password($password) {
    return password_hash($password, PASSWORD_BCRYPT, ['cost' => HASH_COST]);
}

/**
 * Verify password
 * 
 * @param string $password Password to verify
 * @param string $hash Hash to verify against
 * @return bool
 */
function verify_password($password, $hash) {
    return password_verify($password, $hash);
}

/**
 * Generate API token
 * 
 * @param int $user_id User ID
 * @return string
 */
function generate_api_token($user_id) {
    global $db;
    
    $token = bin2hex(random_bytes(32));
    $expires = date('Y-m-d H:i:s', strtotime('+30 days'));
    
    $stmt = $db->prepare("
        INSERT INTO api_tokens (user_id, token, expires_at)
        VALUES (?, ?, ?)
    ");
    
    $stmt->execute([$user_id, $token, $expires]);
    
    return $token;
}

/**
 * Verify API token
 * 
 * @param string $token Token to verify
 * @return int|false
 */
function verify_api_token($token) {
    global $db;
    
    $stmt = $db->prepare("
        SELECT user_id 
        FROM api_tokens 
        WHERE token = ? AND expires_at > NOW() AND revoked = 0
    ");
    
    $stmt->execute([$token]);
    
    return $stmt->fetchColumn();
}

/**
 * Revoke API token
 * 
 * @param string $token Token to revoke
 * @return bool
 */
function revoke_api_token($token) {
    global $db;
    
    $stmt = $db->prepare("
        UPDATE api_tokens 
        SET revoked = 1 
        WHERE token = ?
    ");
    
    return $stmt->execute([$token]);
}

/**
 * Get cache
 * 
 * @param string $key Cache key
 * @return mixed
 */
function get_cache($key) {
    if (!CACHE_ENABLED) {
        return false;
    }
    
    $file = CACHE_PATH . '/' . md5($key);
    
    if (file_exists($file)) {
        $data = file_get_contents($file);
        $cache = unserialize($data);
        
        if ($cache['expires'] > time()) {
            return $cache['data'];
        }
        
        unlink($file);
    }
    
    return false;
}

/**
 * Set cache
 * 
 * @param string $key Cache key
 * @param mixed $data Data to cache
 * @param int $lifetime Cache lifetime in seconds
 * @return bool
 */
function set_cache($key, $data, $lifetime = CACHE_LIFETIME) {
    if (!CACHE_ENABLED) {
        return false;
    }
    
    $file = CACHE_PATH . '/' . md5($key);
    $cache = [
        'data' => $data,
        'expires' => time() + $lifetime
    ];
    
    return file_put_contents($file, serialize($cache)) !== false;
}

/**
 * Delete cache
 * 
 * @param string $key Cache key
 * @return bool
 */
function delete_cache($key) {
    if (!CACHE_ENABLED) {
        return false;
    }
    
    $file = CACHE_PATH . '/' . md5($key);
    
    if (file_exists($file)) {
        return unlink($file);
    }
    
    return false;
}

/**
 * Clear cache
 * 
 * @return bool
 */
function clear_cache() {
    if (!CACHE_ENABLED) {
        return false;
    }
    
    $files = glob(CACHE_PATH . '/*');
    
    foreach ($files as $file) {
        if (is_file($file)) {
            unlink($file);
        }
    }
    
    return true;
}

/**
 * Log message
 * 
 * @param string $level Log level
 * @param string $message Log message
 * @return void
 */
function log_message($level, $message) {
    if (!LOG_ENABLED) {
        return;
    }
    
    $levels = ['debug', 'info', 'warning', 'error', 'critical'];
    
    if (!in_array($level, $levels)) {
        return;
    }
    
    $log_level = array_search(LOG_LEVEL, $levels);
    $message_level = array_search($level, $levels);
    
    if ($message_level < $log_level) {
        return;
    }
    
    $date = date('Y-m-d H:i:s');
    $log = "[$date] [$level] $message" . PHP_EOL;
    
    $file = LOG_PATH . '/' . date('Y-m-d') . '.log';
    
    file_put_contents($file, $log, FILE_APPEND);
}

/**
 * Debug log
 * 
 * @param string $message Log message
 * @return void
 */
function debug_log($message) {
    log_message('debug', $message);
}

/**
 * Info log
 * 
 * @param string $message Log message
 * @return void
 */
function info_log($message) {
    log_message('info', $message);
}

/**
 * Warning log
 * 
 * @param string $message Log message
 * @return void
 */
function warning_log($message) {
    log_message('warning', $message);
}

/**
 * Custom error log
 * 
 * @param string $message
 * @return void
 */
function custom_error_log($message) {
    $log_file = ROOT_PATH . '/logs/error.log';
    $timestamp = date('Y-m-d H:i:s');
    $log_message = "[{$timestamp}] ERROR: {$message}\n";
    
    if (!is_dir(dirname($log_file))) {
        mkdir(dirname($log_file), 0777, true);
    }
    
    file_put_contents($log_file, $log_message, FILE_APPEND);
}

/**
 * Critical log
 * 
 * @param string $message
 * @return void
 */
function critical_log($message) {
    $log_file = ROOT_PATH . '/logs/critical.log';
    $timestamp = date('Y-m-d H:i:s');
    $log_message = "[{$timestamp}] CRITICAL: {$message}\n";
    
    if (!is_dir(dirname($log_file))) {
        mkdir(dirname($log_file), 0777, true);
    }
    
    file_put_contents($log_file, $log_message, FILE_APPEND);
}

/**
 * Get theme
 * 
 * @return string
 */
function get_theme() {
    return $_SESSION['theme'] ?? DEFAULT_THEME;
}

/**
 * Set theme
 * 
 * @param string $theme Theme to set
 * @return void
 */
function set_theme($theme) {
    if (in_array($theme, AVAILABLE_THEMES)) {
        $_SESSION['theme'] = $theme;
    }
}

/**
 * Get language
 * 
 * @return string
 */
function get_language() {
    return $_SESSION['language'] ?? DEFAULT_LANGUAGE;
}

/**
 * Set language
 * 
 * @param string $language Language to set
 * @return void
 */
function set_language($language) {
    if (in_array($language, AVAILABLE_LANGUAGES)) {
        $_SESSION['language'] = $language;
    }
}

/**
 * Translate string
 * 
 * @param string $key Translation key
 * @param array $replacements Replacements
 * @return string
 */
function __($key, $replacements = []) {
    $language = get_language();
    $file = ROOT_PATH . '/languages/' . $language . '.php';
    
    if (file_exists($file)) {
        $translations = require $file;
        
        if (isset($translations[$key])) {
            $string = $translations[$key];
            
            foreach ($replacements as $key => $value) {
                $string = str_replace(':' . $key, $value, $string);
            }
            
            return $string;
        }
    }
    
    return $key;
}

/**
 * Get backup
 * 
 * @return array
 */
function get_backup() {
    // Implementation here
    return [];
} 