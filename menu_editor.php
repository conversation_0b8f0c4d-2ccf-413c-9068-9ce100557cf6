<?php
require_once 'includes/config/database.php';
require_once 'includes/helpers/functions.php';

// Check admin access
$currentUser = getCurrentUser();
if (!$currentUser || $currentUser['role'] !== 'admin') {
    setFlashMessage('danger', 'Aks<PERSON> ditolak! Anda tidak memiliki izin untuk mengakses halaman ini.');
    redirect('dashboard.php');
}

$pageTitle = 'Menu Editor';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    try {
        switch ($_POST['action']) {
            case 'add_menu':
                $menuData = [
                    'menu_id' => $_POST['menu_id'] ?? '',
                    'label' => $_POST['label'] ?? '',
                    'icon' => $_POST['icon'] ?? '',
                    'url' => $_POST['url'] ?? '',
                    'parent_id' => $_POST['parent_id'] ?? null,
                    'sort_order' => $_POST['sort_order'] ?? 0,
                    'is_active' => isset($_POST['is_active']) ? 1 : 0
                ];

                $stmt = $pdo->prepare("INSERT INTO custom_menus (menu_id, label, icon, url, parent_id, sort_order, is_active, created_by) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
                $stmt->execute([
                    $menuData['menu_id'],
                    $menuData['label'],
                    $menuData['icon'],
                    $menuData['url'],
                    $menuData['parent_id'],
                    $menuData['sort_order'],
                    $menuData['is_active'],
                    $currentUser['id']
                ]);

                setFlashMessage('success', 'Menu berhasil ditambahkan');
                break;

            case 'update_menu':
                $id = $_POST['id'] ?? '';
                $menuData = [
                    'label' => $_POST['label'] ?? '',
                    'icon' => $_POST['icon'] ?? '',
                    'url' => $_POST['url'] ?? '',
                    'sort_order' => $_POST['sort_order'] ?? 0,
                    'is_active' => isset($_POST['is_active']) ? 1 : 0
                ];

                $stmt = $pdo->prepare("UPDATE custom_menus SET label = ?, icon = ?, url = ?, sort_order = ?, is_active = ? WHERE id = ?");
                $stmt->execute([
                    $menuData['label'],
                    $menuData['icon'],
                    $menuData['url'],
                    $menuData['sort_order'],
                    $menuData['is_active'],
                    $id
                ]);

                setFlashMessage('success', 'Menu berhasil diupdate');
                break;

            case 'delete_menu':
                $id = $_POST['id'] ?? '';
                if ($id) {
                    $stmt = $pdo->prepare("DELETE FROM custom_menus WHERE id = ?");
                    $stmt->execute([$id]);
                    setFlashMessage('success', 'Menu berhasil dihapus');
                }
                break;

            case 'reorder_menus':
                $menuOrder = json_decode($_POST['menu_order'] ?? '[]', true);
                foreach ($menuOrder as $index => $menuId) {
                    $stmt = $pdo->prepare("UPDATE custom_menus SET sort_order = ? WHERE id = ?");
                    $stmt->execute([$index + 1, $menuId]);
                }
                setFlashMessage('success', 'Urutan menu berhasil diupdate');
                break;
        }
        redirect('menu_editor.php');
    } catch (Exception $e) {
        setFlashMessage('danger', 'Error: ' . $e->getMessage());
    }
}

// Get custom menus
try {
    $stmt = $pdo->query("SELECT * FROM custom_menus ORDER BY sort_order ASC, created_at ASC");
    $customMenus = $stmt->fetchAll();
} catch (PDOException $e) {
    $customMenus = [];
}

// Available icons
$availableIcons = [
    'fas fa-home', 'fas fa-user', 'fas fa-cog', 'fas fa-chart-bar', 'fas fa-table',
    'fas fa-file', 'fas fa-folder', 'fas fa-image', 'fas fa-video', 'fas fa-music',
    'fas fa-envelope', 'fas fa-phone', 'fas fa-calendar', 'fas fa-clock', 'fas fa-bell',
    'fas fa-star', 'fas fa-heart', 'fas fa-thumbs-up', 'fas fa-comment', 'fas fa-share',
    'fas fa-download', 'fas fa-upload', 'fas fa-print', 'fas fa-search', 'fas fa-filter',
    'fas fa-edit', 'fas fa-trash', 'fas fa-plus', 'fas fa-minus', 'fas fa-check',
    'fas fa-times', 'fas fa-arrow-left', 'fas fa-arrow-right', 'fas fa-arrow-up', 'fas fa-arrow-down'
];

include 'includes/views/layouts/header.php';
?>

<div class="container-fluid px-4 py-3">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h3 class="mb-1">Menu Editor</h3>
                    <p class="text-muted mb-0">Create and manage custom menu items</p>
                </div>
                <div class="btn-group">
                    <a href="system_customization.php" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>Back to Customization
                    </a>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addMenuModal">
                        <i class="fas fa-plus me-1"></i>Add Menu
                    </button>
                </div>
            </div>

            <!-- Menu List -->
            <div class="row">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Custom Menus</h5>
                        </div>
                        <div class="card-body">
                            <?php if (empty($customMenus)): ?>
                                <div class="text-center py-4">
                                    <i class="fas fa-menu fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">No custom menus</h5>
                                    <p class="text-muted">Create your first custom menu item</p>
                                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addMenuModal">
                                        <i class="fas fa-plus me-2"></i>Add Menu
                                    </button>
                                </div>
                            <?php else: ?>
                                <div id="menuList" class="list-group">
                                    <?php foreach ($customMenus as $menu): ?>
                                        <div class="list-group-item" data-menu-id="<?= $menu['id'] ?>">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div class="d-flex align-items-center">
                                                    <div class="drag-handle me-3" style="cursor: move;">
                                                        <i class="fas fa-grip-vertical text-muted"></i>
                                                    </div>
                                                    <div>
                                                        <h6 class="mb-1">
                                                            <i class="<?= htmlspecialchars($menu['icon']) ?> me-2"></i>
                                                            <?= htmlspecialchars($menu['label']) ?>
                                                            <?php if (!$menu['is_active']): ?>
                                                                <span class="badge bg-secondary ms-2">Inactive</span>
                                                            <?php endif; ?>
                                                        </h6>
                                                        <small class="text-muted">
                                                            URL: <?= htmlspecialchars($menu['url']) ?> |
                                                            Order: <?= $menu['sort_order'] ?>
                                                        </small>
                                                    </div>
                                                </div>
                                                <div class="btn-group btn-group-sm">
                                                    <button type="button" class="btn btn-outline-primary" onclick="editMenu(<?= htmlspecialchars(json_encode($menu)) ?>)">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <form method="POST" class="d-inline" onsubmit="return confirm('Delete this menu?')">
                                                        <input type="hidden" name="action" value="delete_menu">
                                                        <input type="hidden" name="id" value="<?= $menu['id'] ?>">
                                                        <button type="submit" class="btn btn-outline-danger">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>

                                <div class="mt-3">
                                    <button type="button" class="btn btn-success" onclick="saveMenuOrder()">
                                        <i class="fas fa-save me-2"></i>Save Order
                                    </button>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Menu Preview</h5>
                        </div>
                        <div class="card-body">
                            <div class="border rounded p-3" style="background: #f8f9fa;">
                                <h6 class="mb-3">Sidebar Preview</h6>
                                <div class="nav flex-column">
                                    <?php foreach ($customMenus as $menu): ?>
                                        <?php if ($menu['is_active']): ?>
                                            <a href="#" class="nav-link text-dark py-2">
                                                <i class="<?= htmlspecialchars($menu['icon']) ?> me-2"></i>
                                                <?= htmlspecialchars($menu['label']) ?>
                                            </a>
                                        <?php endif; ?>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card mt-4">
                        <div class="card-header">
                            <h5 class="mb-0">Quick Actions</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <button type="button" class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#addMenuModal">
                                    <i class="fas fa-plus me-2"></i>Add New Menu
                                </button>
                                <button type="button" class="btn btn-outline-info" onclick="exportMenus()">
                                    <i class="fas fa-download me-2"></i>Export Menus
                                </button>
                                <button type="button" class="btn btn-outline-warning" onclick="resetMenuOrder()">
                                    <i class="fas fa-undo me-2"></i>Reset Order
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Menu Modal -->
<div class="modal fade" id="addMenuModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add New Menu</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="add_menu">

                    <div class="mb-3">
                        <label class="form-label">Menu ID</label>
                        <input type="text" class="form-control" name="menu_id" required placeholder="unique_menu_id">
                        <div class="form-text">Unique identifier for the menu</div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Label</label>
                        <input type="text" class="form-control" name="label" required placeholder="Menu Label">
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Icon</label>
                        <select class="form-select" name="icon" required>
                            <option value="">Select Icon</option>
                            <?php foreach ($availableIcons as $icon): ?>
                                <option value="<?= $icon ?>"><?= $icon ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">URL</label>
                        <input type="text" class="form-control" name="url" required placeholder="page.php">
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Sort Order</label>
                        <input type="number" class="form-control" name="sort_order" value="0" min="0">
                    </div>

                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" name="is_active" id="is_active" checked>
                        <label class="form-check-label" for="is_active">
                            Active
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Menu</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Menu Modal -->
<div class="modal fade" id="editMenuModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Menu</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="update_menu">
                    <input type="hidden" name="id" id="edit_menu_id">

                    <div class="mb-3">
                        <label class="form-label">Label</label>
                        <input type="text" class="form-control" name="label" id="edit_label" required>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Icon</label>
                        <select class="form-select" name="icon" id="edit_icon" required>
                            <option value="">Select Icon</option>
                            <?php foreach ($availableIcons as $icon): ?>
                                <option value="<?= $icon ?>"><?= $icon ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">URL</label>
                        <input type="text" class="form-control" name="url" id="edit_url" required>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Sort Order</label>
                        <input type="number" class="form-control" name="sort_order" id="edit_sort_order" min="0">
                    </div>

                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" name="is_active" id="edit_is_active">
                        <label class="form-check-label" for="edit_is_active">
                            Active
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Menu</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
<script>
// Initialize sortable
let sortable;
document.addEventListener('DOMContentLoaded', function() {
    const menuList = document.getElementById('menuList');
    if (menuList) {
        sortable = Sortable.create(menuList, {
            handle: '.drag-handle',
            animation: 150,
            ghostClass: 'sortable-ghost'
        });
    }
});

// Edit menu function
function editMenu(menu) {
    document.getElementById('edit_menu_id').value = menu.id;
    document.getElementById('edit_label').value = menu.label;
    document.getElementById('edit_icon').value = menu.icon;
    document.getElementById('edit_url').value = menu.url;
    document.getElementById('edit_sort_order').value = menu.sort_order;
    document.getElementById('edit_is_active').checked = menu.is_active == 1;

    const modal = new bootstrap.Modal(document.getElementById('editMenuModal'));
    modal.show();
}

// Save menu order
function saveMenuOrder() {
    const menuItems = document.querySelectorAll('#menuList .list-group-item');
    const menuOrder = Array.from(menuItems).map(item => item.dataset.menuId);

    const form = document.createElement('form');
    form.method = 'POST';
    form.style.display = 'none';

    const actionInput = document.createElement('input');
    actionInput.type = 'hidden';
    actionInput.name = 'action';
    actionInput.value = 'reorder_menus';
    form.appendChild(actionInput);

    const orderInput = document.createElement('input');
    orderInput.type = 'hidden';
    orderInput.name = 'menu_order';
    orderInput.value = JSON.stringify(menuOrder);
    form.appendChild(orderInput);

    document.body.appendChild(form);
    form.submit();
}

// Export menus
function exportMenus() {
    const menus = <?= json_encode($customMenus) ?>;
    const dataStr = JSON.stringify(menus, null, 2);
    const dataBlob = new Blob([dataStr], {type: 'application/json'});
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'custom_menus.json';
    link.click();
    URL.revokeObjectURL(url);
}

// Reset menu order
function resetMenuOrder() {
    if (confirm('Reset menu order to default?')) {
        location.reload();
    }
}
</script>

<style>
.sortable-ghost {
    opacity: 0.5;
}

.drag-handle:hover {
    color: #007bff !important;
}

.list-group-item {
    transition: all 0.3s ease;
}

.list-group-item:hover {
    background-color: #f8f9fa;
}
</style>

<?php include 'includes/views/layouts/footer.php'; ?>
