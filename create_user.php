<?php
require_once 'config/database.php';

// Data user yang akan dibuat
$users = [
    [
        'nama' => 'Administrator',
        'email' => '<EMAIL>',
        'password' => 'admin123',
        'role' => 'admin'
    ],
    [
        'nama' => 'User Test',
        'email' => '<EMAIL>',
        'password' => 'user123',
        'role' => 'user'
    ]
];

try {
    $pdo = Database::getInstance()->getConnection();
    
    // Siapkan query untuk insert user
    $stmt = $pdo->prepare("
        INSERT INTO users (nama, email, password, role, email_verified, status) 
        VALUES (?, ?, ?, ?, 1, 'active')
    ");
    
    foreach ($users as $user) {
        // Hash password
        $hashed_password = password_hash($user['password'], PASSWORD_DEFAULT);
        
        // Cek apakah email sudah ada
        $check = $pdo->prepare("SELECT id FROM users WHERE email = ?");
        $check->execute([$user['email']]);
        
        if (!$check->fetch()) {
            // Insert user baru
            if ($stmt->execute([
                $user['nama'],
                $user['email'],
                $hashed_password,
                $user['role']
            ])) {
                echo "User {$user['nama']} berhasil dibuat.<br>";
                echo "Email: {$user['email']}<br>";
                echo "Password: {$user['password']}<br>";
                echo "Role: {$user['role']}<br>";
                echo "<hr>";
            }
        } else {
            echo "User dengan email {$user['email']} sudah ada.<br>";
            echo "<hr>";
        }
    }
    
    echo "<br>Proses selesai! Silakan hapus file ini setelah selesai untuk keamanan.";
    
} catch (PDOException $e) {
    die("Error: " . $e->getMessage());
}
?> 