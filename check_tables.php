<?php
require_once 'includes/config/database.php';
require_once 'includes/helpers/functions.php';

// Check admin access
$currentUser = getCurrentUser();
if (!$currentUser || $currentUser['role'] !== 'admin') {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Access denied']);
    exit;
}

header('Content-Type: application/json');

try {
    // Define required tables
    $requiredTables = [
        'system_settings' => 'Global system configuration',
        'custom_themes' => 'Custom theme configurations',
        'custom_menus' => 'Custom menu items',
        'layout_components' => 'Layout components & widgets',
        'custom_css' => 'Custom CSS files',
        'page_templates' => 'Page templates',
        'customization_presets' => 'Customization presets',
        'user_customizations' => 'User-specific customizations',
        'database_versions' => 'Database version tracking',
        'database_migrations' => 'Migration tracking',
        'system_info' => 'System information'
    ];
    
    $tableStatus = [];
    $missingCount = 0;
    
    foreach ($requiredTables as $tableName => $description) {
        try {
            $stmt = $pdo->query("SHOW TABLES LIKE '$tableName'");
            $exists = $stmt->rowCount() > 0;
            
            if (!$exists) {
                $missingCount++;
            }
            
            // Get table info if exists
            $rowCount = 0;
            $size = 0;
            
            if ($exists) {
                try {
                    $stmt = $pdo->query("SELECT COUNT(*) FROM `$tableName`");
                    $rowCount = $stmt->fetchColumn();
                    
                    $stmt = $pdo->query("SELECT ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'size' FROM information_schema.TABLES WHERE table_schema = DATABASE() AND table_name = '$tableName'");
                    $size = $stmt->fetchColumn() ?: 0;
                } catch (Exception $e) {
                    // Table might exist but have issues
                }
            }
            
            $tableStatus[] = [
                'name' => $tableName,
                'description' => $description,
                'exists' => $exists,
                'row_count' => $rowCount,
                'size_mb' => $size
            ];
            
        } catch (Exception $e) {
            $tableStatus[] = [
                'name' => $tableName,
                'description' => $description,
                'exists' => false,
                'row_count' => 0,
                'size_mb' => 0,
                'error' => $e->getMessage()
            ];
            $missingCount++;
        }
    }
    
    // Get additional database info
    $dbInfo = [];
    try {
        $stmt = $pdo->query("SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE()");
        $dbInfo['total_tables'] = $stmt->fetchColumn();
        
        $stmt = $pdo->query("SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS db_size FROM information_schema.tables WHERE table_schema = DATABASE()");
        $dbInfo['total_size_mb'] = $stmt->fetchColumn();
        
        $stmt = $pdo->query("SELECT DATABASE() as db_name");
        $dbInfo['database_name'] = $stmt->fetchColumn();
        
    } catch (Exception $e) {
        $dbInfo = [
            'total_tables' => 0,
            'total_size_mb' => 0,
            'database_name' => 'Unknown'
        ];
    }
    
    echo json_encode([
        'success' => true,
        'tables' => $tableStatus,
        'missing_count' => $missingCount,
        'total_required' => count($requiredTables),
        'database_info' => $dbInfo,
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
