<?php
require_once 'includes/config/database.php';
require_once 'includes/helpers/functions.php';
require_once 'includes/helpers/notifications.php';

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isLoggedIn()) {
    setFlashMessage('danger', 'Silakan login terlebih dahulu');
    redirect('/keuangan/login.php');
}

$currentUser = getCurrentUser();

// Check if ID is provided
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    setFlashMessage('danger', 'ID kategori tidak valid');
    redirect('/kategori.php');
}

$id = (int)$_GET['id'];

try {
    // Check if category exists and belongs to user
    $stmt = $pdo->prepare("
        SELECT * FROM kategori 
        WHERE id = ? AND user_id = ?
    ");
    $stmt->execute([$id, $currentUser['id']]);
    $kategori = $stmt->fetch();
    
    if (!$kategori) {
        setFlashMessage('danger', 'Kategori tidak ditemukan');
        redirect('/kategori.php');
    }
    
    // Check if category is being used in transactions
    $stmt = $pdo->prepare("
        SELECT COUNT(*) as count 
        FROM transaksi 
        WHERE kategori_id = ?
    ");
    $stmt->execute([$id]);
    
    if ($stmt->fetch()['count'] > 0) {
        setFlashMessage('danger', 'Kategori tidak dapat dihapus karena masih digunakan dalam transaksi');
        redirect('/kategori.php');
    }
    
    // Delete category
    $stmt = $pdo->prepare("DELETE FROM kategori WHERE id = ?");
    $stmt->execute([$id]);
    
    // Create notification
    $judul = 'Kategori Dihapus';
    $pesan = sprintf(
        'Kategori %s (%s) telah dihapus',
        $kategori['nama'],
        $kategori['tipe'] === 'pemasukan' ? 'Pemasukan' : 'Pengeluaran'
    );
    createNotification($currentUser['id'], $judul, $pesan, 'warning');
    
    // Log activity
    logActivity($currentUser['id'], sprintf(
        'Menghapus kategori %s (%s)',
        $kategori['nama'],
        $kategori['tipe'] === 'pemasukan' ? 'Pemasukan' : 'Pengeluaran'
    ));
    
    setFlashMessage('success', 'Kategori berhasil dihapus');
} catch (PDOException $e) {
    setFlashMessage('danger', 'Terjadi kesalahan: ' . $e->getMessage());
}

redirect('/kategori.php'); 