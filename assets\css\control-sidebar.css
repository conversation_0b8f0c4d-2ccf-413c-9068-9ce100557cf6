/* Control Sidebar CSS - Consolidated and Optimized */

/* Control Sidebar Styles */
.control-sidebar {
    position: fixed;
    top: 0;
    right: -300px;
    width: 300px;
    height: 100vh;
    background: var(--bg-primary);
    border-left: 1px solid var(--border-color);
    box-shadow: var(--shadow-xl);
    z-index: 1050;
    transition: right var(--transition-normal);
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: var(--border-color) transparent;
}

.control-sidebar::-webkit-scrollbar {
    width: 6px;
}

.control-sidebar::-webkit-scrollbar-track {
    background: transparent;
}

.control-sidebar::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 3px;
}

.control-sidebar.open {
    right: 0;
}

.control-sidebar-content {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.control-sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
    background: var(--bg-tertiary);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.control-sidebar-header h5 {
    margin: 0;
    color: var(--text-primary);
    font-weight: 600;
}

.control-sidebar-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 1.25rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
}

.control-sidebar-close:hover {
    background: var(--bg-accent);
    color: var(--text-primary);
}

.control-sidebar-body {
    flex: 1;
    padding: 1rem;
    overflow-y: auto;
}

.control-section {
    margin-bottom: 2rem;
}

.control-section-title {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--border-light);
    display: flex;
    align-items: center;
}

.control-section-title i {
    color: var(--primary-color);
}

.form-check {
    margin-bottom: 0.75rem;
}

.form-check-input {
    background-color: var(--bg-tertiary);
    border-color: var(--border-color);
}

.form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.form-check-input:focus {
    box-shadow: 0 0 0 0.25rem rgba(37, 99, 235, 0.25);
}

.form-check-label {
    color: var(--text-secondary);
    font-size: 0.875rem;
    cursor: pointer;
}

.color-variants {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
    gap: 0.75rem;
}

.color-option {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem;
    border: 2px solid transparent;
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all var(--transition-fast);
    background: var(--bg-tertiary);
}

.color-option:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.color-option.active {
    border-color: var(--primary-color);
    background: rgba(37, 99, 235, 0.1);
}

.color-preview {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    border: 2px solid var(--border-light);
    box-shadow: var(--shadow-sm);
}

.color-option span {
    font-size: 0.75rem;
    color: var(--text-secondary);
    text-align: center;
    font-weight: 500;
}

.color-option.active span {
    color: var(--primary-color);
    font-weight: 600;
}

.control-sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1040;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
}

.control-sidebar-overlay.show {
    opacity: 1;
    visibility: visible;
}

/* Dark Mode for Control Sidebar */
[data-bs-theme="dark"] .control-sidebar {
    background: var(--bg-primary);
    border-left-color: var(--border-color);
}

[data-bs-theme="dark"] .control-sidebar-header {
    background: var(--bg-tertiary);
}

[data-bs-theme="dark"] .color-option {
    background: var(--bg-tertiary);
}

[data-bs-theme="dark"] .color-option.active {
    background: rgba(59, 130, 246, 0.15);
}

/* Dark Mode for Navbar */
[data-bs-theme="dark"] .modern-navbar {
    background: var(--bg-primary);
    border-bottom-color: var(--border-color);
    color: var(--text-primary);
}

[data-bs-theme="dark"] .modern-navbar .nav-link {
    color: var(--text-secondary);
}

[data-bs-theme="dark"] .modern-navbar .nav-link:hover {
    color: var(--text-primary);
    background-color: var(--bg-tertiary);
}

[data-bs-theme="dark"] .modern-navbar .navbar-brand {
    color: var(--text-primary);
}

[data-bs-theme="dark"] .modern-navbar .brand-subtitle {
    color: var(--text-muted);
}

/* Dark Mode for Sidebar */
[data-bs-theme="dark"] .modern-sidebar {
    background: var(--bg-primary);
    border-right-color: var(--border-color);
}

[data-bs-theme="dark"] .modern-sidebar .menu-item {
    color: var(--text-secondary);
}

[data-bs-theme="dark"] .modern-sidebar .menu-item:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

[data-bs-theme="dark"] .modern-sidebar .menu-item.active {
    background: var(--primary-color);
    color: white;
}

/* Dark Mode for Cards and Content */
[data-bs-theme="dark"] .card {
    background: var(--bg-primary);
    border-color: var(--border-color);
    color: var(--text-primary);
}

[data-bs-theme="dark"] .card-header {
    background: var(--bg-tertiary);
    border-bottom-color: var(--border-color);
    color: var(--text-primary);
}

[data-bs-theme="dark"] .card-body {
    color: var(--text-primary);
}

/* Dark Mode for Buttons */
[data-bs-theme="dark"] .btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

[data-bs-theme="dark"] .btn-outline-primary:hover {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

/* Dark Mode for Dropdowns */
[data-bs-theme="dark"] .dropdown-menu {
    background: var(--bg-primary);
    border-color: var(--border-color);
}

[data-bs-theme="dark"] .dropdown-item {
    color: var(--text-secondary);
}

[data-bs-theme="dark"] .dropdown-item:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

/* Dark Mode for Forms */
[data-bs-theme="dark"] .form-control {
    background: var(--bg-tertiary);
    border-color: var(--border-color);
    color: var(--text-primary);
}

[data-bs-theme="dark"] .form-control:focus {
    background: var(--bg-tertiary);
    border-color: var(--primary-color);
    color: var(--text-primary);
    box-shadow: 0 0 0 0.25rem rgba(59, 130, 246, 0.25);
}

[data-bs-theme="dark"] .form-label {
    color: var(--text-primary);
}

/* Dark Mode for Tables */
[data-bs-theme="dark"] .table {
    color: var(--text-primary);
}

[data-bs-theme="dark"] .table-striped > tbody > tr:nth-of-type(odd) > td {
    background: var(--bg-tertiary);
}

[data-bs-theme="dark"] .table-hover > tbody > tr:hover > td {
    background: var(--bg-accent);
}

/* Dark Mode for Alerts */
[data-bs-theme="dark"] .alert-info {
    background: rgba(6, 182, 212, 0.1);
    border-color: rgba(6, 182, 212, 0.2);
    color: var(--info-color);
}

[data-bs-theme="dark"] .alert-success {
    background: rgba(16, 185, 129, 0.1);
    border-color: rgba(16, 185, 129, 0.2);
    color: var(--success-color);
}

[data-bs-theme="dark"] .alert-warning {
    background: rgba(245, 158, 11, 0.1);
    border-color: rgba(245, 158, 11, 0.2);
    color: var(--warning-color);
}

[data-bs-theme="dark"] .alert-danger {
    background: rgba(239, 68, 68, 0.1);
    border-color: rgba(239, 68, 68, 0.2);
    color: var(--danger-color);
}

/* Dark Mode for List Groups */
[data-bs-theme="dark"] .list-group-item {
    background: var(--bg-primary);
    border-color: var(--border-color);
    color: var(--text-primary);
}

[data-bs-theme="dark"] .list-group-item:hover {
    background: var(--bg-tertiary);
}

/* Dark Mode for Body */
[data-bs-theme="dark"] body {
    background: var(--bg-secondary);
    color: var(--text-primary);
}

/* Dark Mode for Main Content */
[data-bs-theme="dark"] .main-content {
    background: var(--bg-secondary);
}

/* Dark Mode for Container Fluid */
[data-bs-theme="dark"] .container-fluid {
    background: var(--bg-secondary);
}

/* Enhanced Dark Mode Styles */
[data-bs-theme="dark"] {
    /* Bootstrap component overrides */
    --bs-body-bg: #111827;
    --bs-body-color: #f9fafb;
    --bs-card-bg: #1f2937;
    --bs-card-border-color: #374151;
    --bs-table-color: #f9fafb;
    --bs-table-bg: #1f2937;
    --bs-table-border-color: #374151;
    --bs-dropdown-bg: #1f2937;
    --bs-dropdown-border-color: #374151;
    --bs-dropdown-link-color: #f9fafb;
    --bs-dropdown-link-hover-bg: #374151;
    --bs-navbar-bg: #1f2937;
    --bs-navbar-color: #f9fafb;
    --bs-badge-bg: #6b7280;
    --bs-badge-color: #ffffff;
    --bs-btn-close-color: #f9fafb;
    --bs-modal-bg: #1f2937;
    --bs-modal-border-color: #374151;
    --bs-offcanvas-bg: #1f2937;
    --bs-offcanvas-border-color: #374151;
}

/* Dark Mode for Forms */
[data-bs-theme="dark"] .form-control {
    background-color: #374151;
    border-color: #4b5563;
    color: #f9fafb;
}

[data-bs-theme="dark"] .form-control:focus {
    background-color: #374151;
    border-color: #60a5fa;
    color: #f9fafb;
    box-shadow: 0 0 0 0.25rem rgba(96, 165, 250, 0.25);
}

[data-bs-theme="dark"] .form-select {
    background-color: #374151;
    border-color: #4b5563;
    color: #f9fafb;
}

[data-bs-theme="dark"] .form-check-input {
    background-color: #374151;
    border-color: #4b5563;
}

[data-bs-theme="dark"] .form-check-input:checked {
    background-color: #60a5fa;
    border-color: #60a5fa;
}

/* Dark Mode for Buttons */
[data-bs-theme="dark"] .btn-outline-primary {
    color: #60a5fa;
    border-color: #60a5fa;
}

[data-bs-theme="dark"] .btn-outline-primary:hover {
    background-color: #60a5fa;
    border-color: #60a5fa;
    color: #ffffff;
}

/* Dark Mode for Alerts */
[data-bs-theme="dark"] .alert-primary {
    background-color: rgba(96, 165, 250, 0.1);
    border-color: rgba(96, 165, 250, 0.2);
    color: #93c5fd;
}

[data-bs-theme="dark"] .alert-success {
    background-color: rgba(16, 185, 129, 0.1);
    border-color: rgba(16, 185, 129, 0.2);
    color: #6ee7b7;
}

[data-bs-theme="dark"] .alert-danger {
    background-color: rgba(239, 68, 68, 0.1);
    border-color: rgba(239, 68, 68, 0.2);
    color: #fca5a5;
}

[data-bs-theme="dark"] .alert-warning {
    background-color: rgba(245, 158, 11, 0.1);
    border-color: rgba(245, 158, 11, 0.2);
    color: #fcd34d;
}

/* Dark Mode for Breadcrumbs */
[data-bs-theme="dark"] .breadcrumb {
    background-color: #374151;
}

[data-bs-theme="dark"] .breadcrumb-item a {
    color: #60a5fa;
}

[data-bs-theme="dark"] .breadcrumb-item.active {
    color: #d1d5db;
}

/* Dark Mode for Pagination */
[data-bs-theme="dark"] .page-link {
    background-color: #374151;
    border-color: #4b5563;
    color: #f9fafb;
}

[data-bs-theme="dark"] .page-link:hover {
    background-color: #4b5563;
    border-color: #6b7280;
    color: #f9fafb;
}

[data-bs-theme="dark"] .page-item.active .page-link {
    background-color: #60a5fa;
    border-color: #60a5fa;
}

/* Dark Mode for Progress Bars */
[data-bs-theme="dark"] .progress {
    background-color: #374151;
}

/* Dark Mode for Tooltips */
[data-bs-theme="dark"] .tooltip .tooltip-inner {
    background-color: #1f2937;
    color: #f9fafb;
}

/* Dark Mode for Popovers */
[data-bs-theme="dark"] .popover {
    background-color: #1f2937;
    border-color: #374151;
}

[data-bs-theme="dark"] .popover-header {
    background-color: #374151;
    border-bottom-color: #4b5563;
    color: #f9fafb;
}

[data-bs-theme="dark"] .popover-body {
    color: #f9fafb;
}

/* Smooth Theme Transitions */
*,
*::before,
*::after {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
}

/* Prevent transition on page load */
.preload * {
    transition: none !important;
}

/* Remove preload class after page loads */
body.preload {
    transition: none !important;
}

/* Dark Mode for Text Colors */
[data-bs-theme="dark"] h1,
[data-bs-theme="dark"] h2,
[data-bs-theme="dark"] h3,
[data-bs-theme="dark"] h4,
[data-bs-theme="dark"] h5,
[data-bs-theme="dark"] h6 {
    color: #f9fafb;
}

[data-bs-theme="dark"] p,
[data-bs-theme="dark"] span,
[data-bs-theme="dark"] div {
    color: #d1d5db;
}

[data-bs-theme="dark"] .text-muted {
    color: #9ca3af !important;
}

[data-bs-theme="dark"] .text-primary {
    color: #60a5fa !important;
}

[data-bs-theme="dark"] .text-success {
    color: #10b981 !important;
}

[data-bs-theme="dark"] .text-danger {
    color: #ef4444 !important;
}

[data-bs-theme="dark"] .text-warning {
    color: #f59e0b !important;
}

[data-bs-theme="dark"] .text-info {
    color: #06b6d4 !important;
}

/* Dark Mode for Links */
[data-bs-theme="dark"] a {
    color: #60a5fa;
}

[data-bs-theme="dark"] a:hover {
    color: #93c5fd;
}

/* Dark Mode for Tables */
[data-bs-theme="dark"] .table {
    color: #f9fafb;
}

[data-bs-theme="dark"] .table-striped > tbody > tr:nth-of-type(odd) > td,
[data-bs-theme="dark"] .table-striped > tbody > tr:nth-of-type(odd) > th {
    background-color: rgba(255, 255, 255, 0.05);
}

[data-bs-theme="dark"] .table-hover > tbody > tr:hover > td,
[data-bs-theme="dark"] .table-hover > tbody > tr:hover > th {
    background-color: rgba(255, 255, 255, 0.075);
}

/* Dark Mode for Borders */
[data-bs-theme="dark"] .border {
    border-color: #374151 !important;
}

[data-bs-theme="dark"] .border-top {
    border-top-color: #374151 !important;
}

[data-bs-theme="dark"] .border-bottom {
    border-bottom-color: #374151 !important;
}

[data-bs-theme="dark"] .border-start {
    border-left-color: #374151 !important;
}

[data-bs-theme="dark"] .border-end {
    border-right-color: #374151 !important;
}

/* Dark Mode for Background Colors */
[data-bs-theme="dark"] .bg-light {
    background-color: #374151 !important;
}

[data-bs-theme="dark"] .bg-white {
    background-color: #1f2937 !important;
}

[data-bs-theme="dark"] .bg-secondary {
    background-color: #4b5563 !important;
}

/* Dark Mode for Navbar Brand and Text */
[data-bs-theme="dark"] .navbar-brand {
    color: #f9fafb !important;
}

[data-bs-theme="dark"] .navbar-text {
    color: #d1d5db !important;
}

[data-bs-theme="dark"] .navbar-nav .nav-link {
    color: #d1d5db !important;
}

[data-bs-theme="dark"] .navbar-nav .nav-link:hover {
    color: #f9fafb !important;
}

/* Dark Mode for Modern Navbar - Force Dark Background */
[data-bs-theme="dark"] .modern-navbar,
[data-bs-theme="dark"] .navbar,
[data-bs-theme="dark"] nav.navbar {
    background: #111827 !important;
    background-color: #111827 !important;
    border-bottom-color: #374151 !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.5) !important;
}

/* Force navbar background with higher specificity */
[data-bs-theme="dark"] .navbar-expand-lg,
[data-bs-theme="dark"] .navbar-expand-md,
[data-bs-theme="dark"] .navbar-expand-sm,
[data-bs-theme="dark"] .navbar-expand {
    background: #111827 !important;
    background-color: #111827 !important;
}

[data-bs-theme="dark"] .modern-navbar .navbar-brand {
    color: #f9fafb !important;
}

[data-bs-theme="dark"] .modern-navbar .navbar-brand:hover {
    color: #60a5fa !important;
}

[data-bs-theme="dark"] .modern-navbar .nav-link {
    color: #d1d5db !important;
}

[data-bs-theme="dark"] .modern-navbar .nav-link:hover {
    color: #f9fafb !important;
    background-color: rgba(255, 255, 255, 0.1) !important;
}

[data-bs-theme="dark"] .modern-navbar .nav-link.active {
    color: #60a5fa !important;
}

/* Dark Mode for Navbar Icons */
[data-bs-theme="dark"] .modern-navbar .nav-link i {
    color: #d1d5db !important;
}

[data-bs-theme="dark"] .modern-navbar .nav-link:hover i {
    color: #f9fafb !important;
}

[data-bs-theme="dark"] .modern-navbar .nav-link.active i {
    color: #60a5fa !important;
}

/* Dark Mode for Modern Sidebar - Force Dark Background */
[data-bs-theme="dark"] .modern-sidebar,
[data-bs-theme="dark"] .sidebar,
[data-bs-theme="dark"] aside.sidebar,
[data-bs-theme="dark"] #sidebar {
    background: #111827 !important;
    background-color: #111827 !important;
    border-right-color: #374151 !important;
    box-shadow: 2px 0 4px rgba(0, 0, 0, 0.3) !important;
}

/* Force sidebar background with linear gradient override */
[data-bs-theme="dark"] .modern-sidebar,
[data-bs-theme="dark"] .sidebar {
    background-image: none !important;
    background: #111827 !important;
}

/* Dark Mode for Sidebar User Profile */
[data-bs-theme="dark"] .sidebar-user {
    background: rgba(0, 0, 0, 0.3) !important;
    border-bottom-color: #374151 !important;
}

[data-bs-theme="dark"] .sidebar-user .user-name {
    color: #f9fafb !important;
}

[data-bs-theme="dark"] .sidebar-user .user-role {
    color: #d1d5db !important;
}

[data-bs-theme="dark"] .sidebar-user .user-status {
    color: #9ca3af !important;
}

[data-bs-theme="dark"] .sidebar-user .user-status .text-success {
    color: #10b981 !important;
}

/* Dark Mode for Sidebar Header */
[data-bs-theme="dark"] .sidebar-header {
    background: #111827 !important;
    background-color: #111827 !important;
    border-bottom-color: #374151 !important;
}

/* Dark Mode for Sidebar Navigation */
[data-bs-theme="dark"] .sidebar-navigation {
    background: #111827 !important;
    background-color: #111827 !important;
}

/* Dark Mode for Sidebar Footer */
[data-bs-theme="dark"] .sidebar-footer {
    background: #111827 !important;
    background-color: #111827 !important;
    border-top-color: #374151 !important;
}

[data-bs-theme="dark"] .nav-menu .menu-item {
    color: #d1d5db !important;
}

[data-bs-theme="dark"] .nav-menu .menu-item:hover {
    background: #374151 !important;
    color: #f9fafb !important;
}

[data-bs-theme="dark"] .nav-menu .menu-item.active {
    background: #60a5fa !important;
    color: #ffffff !important;
    box-shadow: 0 2px 8px rgba(96, 165, 250, 0.3) !important;
}

[data-bs-theme="dark"] .nav-menu .menu-item .menu-icon i {
    color: #d1d5db !important;
}

[data-bs-theme="dark"] .nav-menu .menu-item:hover .menu-icon i {
    color: #f9fafb !important;
}

[data-bs-theme="dark"] .nav-menu .menu-item.active .menu-icon i {
    color: #ffffff !important;
}

[data-bs-theme="dark"] .nav-menu .menu-text {
    color: inherit !important;
}

[data-bs-theme="dark"] .nav-menu .menu-arrow {
    color: #9ca3af !important;
}

[data-bs-theme="dark"] .nav-menu .menu-item:hover .menu-arrow {
    color: #f9fafb !important;
}

[data-bs-theme="dark"] .nav-menu .menu-item.active .menu-arrow {
    color: #ffffff !important;
}

/* Dark Mode for Submenu */
[data-bs-theme="dark"] .submenu {
    background: rgba(0, 0, 0, 0.2) !important;
}

[data-bs-theme="dark"] .submenu-container {
    background: transparent !important;
}

[data-bs-theme="dark"] .submenu-item {
    color: #d1d5db !important;
}

[data-bs-theme="dark"] .submenu-item:hover {
    background: #4b5563 !important;
    color: #f9fafb !important;
}

[data-bs-theme="dark"] .submenu-item.active {
    background: #60a5fa !important;
    color: #ffffff !important;
}

[data-bs-theme="dark"] .submenu-item .submenu-icon i {
    color: #9ca3af !important;
}

[data-bs-theme="dark"] .submenu-item:hover .submenu-icon i {
    color: #f9fafb !important;
}

[data-bs-theme="dark"] .submenu-item.active .submenu-icon i {
    color: #ffffff !important;
}

[data-bs-theme="dark"] .submenu-text {
    color: inherit !important;
}

/* Dark Mode for Menu Groups */
[data-bs-theme="dark"] .menu-group {
    border-bottom-color: #374151 !important;
}

[data-bs-theme="dark"] .menu-toggle.has-submenu {
    color: #d1d5db !important;
}

[data-bs-theme="dark"] .menu-toggle.has-submenu:hover {
    background: #374151 !important;
    color: #f9fafb !important;
}

[data-bs-theme="dark"] .menu-toggle.has-submenu.active {
    background: #60a5fa !important;
    color: #ffffff !important;
}

/* Dark Mode for Menu Indicators */
[data-bs-theme="dark"] .menu-indicator {
    background: #60a5fa !important;
}

[data-bs-theme="dark"] .submenu-indicator {
    background: #60a5fa !important;
}

/* Dark Mode for Brand */
[data-bs-theme="dark"] .brand-title {
    color: #f9fafb !important;
}

[data-bs-theme="dark"] .brand-subtitle {
    color: #d1d5db !important;
}

/* Dark Mode for Navbar Theme Toggle */
[data-bs-theme="dark"] .navbar .nav-link[data-theme-toggle] {
    color: #d1d5db !important;
}

[data-bs-theme="dark"] .navbar .nav-link[data-theme-toggle]:hover {
    color: #f9fafb !important;
    background-color: rgba(255, 255, 255, 0.1) !important;
}

[data-bs-theme="dark"] .navbar .nav-link[data-theme-toggle] i {
    color: #60a5fa !important;
}

/* Dark Mode for Modern User Profile Button */
[data-bs-theme="dark"] .modern-user-profile-btn {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.02)) !important;
    border-color: rgba(255, 255, 255, 0.1) !important;
    color: #f9fafb !important;
}

[data-bs-theme="dark"] .modern-user-profile-btn:hover {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05)) !important;
    border-color: rgba(96, 165, 250, 0.3) !important;
    box-shadow: 0 4px 16px rgba(96, 165, 250, 0.2) !important;
}

[data-bs-theme="dark"] .modern-user-name {
    color: #f9fafb !important;
}

[data-bs-theme="dark"] .modern-user-role {
    color: #9ca3af !important;
}

[data-bs-theme="dark"] .dropdown-arrow {
    color: #9ca3af !important;
}

[data-bs-theme="dark"] .modern-user-profile-btn[aria-expanded="true"] .dropdown-arrow {
    color: #60a5fa !important;
}

[data-bs-theme="dark"] .dropdown-arrow-container {
    background: rgba(255, 255, 255, 0.05) !important;
}

[data-bs-theme="dark"] .modern-user-profile-btn[aria-expanded="true"] .dropdown-arrow-container {
    background: rgba(96, 165, 250, 0.1) !important;
}

/* Dark Mode for Ultra Modern Profile Dropdown */
[data-bs-theme="dark"] .ultra-modern-profile-dropdown {
    background: rgba(31, 41, 55, 0.95) !important;
    border: 1px solid rgba(55, 65, 81, 0.5) !important;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(55, 65, 81, 0.2) !important;
}

[data-bs-theme="dark"] .modern-dropdown-header {
    background: linear-gradient(135deg, rgba(96, 165, 250, 0.08), rgba(147, 197, 253, 0.08)) !important;
    border-bottom-color: rgba(55, 65, 81, 0.3) !important;
}

[data-bs-theme="dark"] .modern-profile-name {
    color: #f9fafb !important;
}

[data-bs-theme="dark"] .modern-profile-email {
    color: #9ca3af !important;
}

[data-bs-theme="dark"] .modern-profile-role-badge {
    background: linear-gradient(135deg, rgba(96, 165, 250, 0.15), rgba(147, 197, 253, 0.15)) !important;
    border-color: rgba(96, 165, 250, 0.3) !important;
    color: #60a5fa !important;
}

[data-bs-theme="dark"] .modern-dropdown-divider {
    border-color: rgba(55, 65, 81, 0.3) !important;
}

[data-bs-theme="dark"] .ultra-modern-dropdown-item {
    color: #f9fafb !important;
}

[data-bs-theme="dark"] .ultra-modern-dropdown-item:hover {
    background: linear-gradient(135deg, rgba(96, 165, 250, 0.1), rgba(147, 197, 253, 0.1)) !important;
    border-color: rgba(96, 165, 250, 0.2) !important;
    color: #60a5fa !important;
}

[data-bs-theme="dark"] .modern-item-icon {
    background: linear-gradient(135deg, rgba(96, 165, 250, 0.1), rgba(147, 197, 253, 0.1)) !important;
}

[data-bs-theme="dark"] .modern-item-icon i {
    color: #60a5fa !important;
}

[data-bs-theme="dark"] .modern-item-title {
    color: inherit !important;
}

[data-bs-theme="dark"] .modern-item-subtitle {
    color: #9ca3af !important;
}

[data-bs-theme="dark"] .modern-item-arrow {
    background: rgba(96, 165, 250, 0.05) !important;
}

[data-bs-theme="dark"] .modern-item-arrow i {
    color: #9ca3af !important;
}

[data-bs-theme="dark"] .ultra-modern-dropdown-item:hover .modern-item-arrow i {
    color: #60a5fa !important;
}

/* Dark Mode for Admin Item */
[data-bs-theme="dark"] .admin-item {
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.08), rgba(251, 191, 36, 0.08)) !important;
    border-color: rgba(245, 158, 11, 0.15) !important;
}

[data-bs-theme="dark"] .admin-item:hover {
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.15), rgba(251, 191, 36, 0.15)) !important;
    border-color: rgba(245, 158, 11, 0.3) !important;
    color: #fbbf24 !important;
}

[data-bs-theme="dark"] .admin-icon {
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.15), rgba(251, 191, 36, 0.15)) !important;
}

[data-bs-theme="dark"] .admin-icon i {
    color: #fbbf24 !important;
}

/* Dark Mode for Logout Item */
[data-bs-theme="dark"] .logout-item {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.08), rgba(248, 113, 113, 0.08)) !important;
    border-color: rgba(239, 68, 68, 0.15) !important;
}

[data-bs-theme="dark"] .logout-item:hover {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.15), rgba(248, 113, 113, 0.15)) !important;
    border-color: rgba(239, 68, 68, 0.3) !important;
    color: #f87171 !important;
}

[data-bs-theme="dark"] .logout-icon {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.15), rgba(248, 113, 113, 0.15)) !important;
}

[data-bs-theme="dark"] .logout-icon i {
    color: #f87171 !important;
}

[data-bs-theme="dark"] .logout-title {
    color: #f87171 !important;
}

[data-bs-theme="dark"] .logout-arrow {
    background: rgba(239, 68, 68, 0.08) !important;
}

[data-bs-theme="dark"] .logout-arrow i {
    color: #f87171 !important;
}

/* Dark Mode for Navbar Dropdowns */
[data-bs-theme="dark"] .navbar .dropdown-menu {
    background-color: #1f2937 !important;
    border-color: #374151 !important;
}

[data-bs-theme="dark"] .navbar .dropdown-item {
    color: #d1d5db !important;
}

[data-bs-theme="dark"] .navbar .dropdown-item:hover {
    background-color: #374151 !important;
    color: #f9fafb !important;
}

/* Dark Mode for Navbar Buttons */
[data-bs-theme="dark"] .navbar .btn-link {
    color: #d1d5db !important;
}

[data-bs-theme="dark"] .navbar .btn-link:hover {
    color: #f9fafb !important;
}

/* Dark Mode for Notification Badge */
[data-bs-theme="dark"] .navbar .badge {
    background-color: #ef4444 !important;
    color: #ffffff !important;
}

/* Dark Mode for Scrollbars */
[data-bs-theme="dark"] ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

[data-bs-theme="dark"] ::-webkit-scrollbar-track {
    background: #374151;
    border-radius: 4px;
}

[data-bs-theme="dark"] ::-webkit-scrollbar-thumb {
    background: #6b7280;
    border-radius: 4px;
}

[data-bs-theme="dark"] ::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
}

/* Dark Mode for Input Groups */
[data-bs-theme="dark"] .input-group-text {
    background-color: #374151 !important;
    border-color: #4b5563 !important;
    color: #f9fafb !important;
}

/* Dark Mode for List Groups */
[data-bs-theme="dark"] .list-group {
    background-color: transparent !important;
}

[data-bs-theme="dark"] .list-group-item {
    background-color: #1f2937 !important;
    border-color: #374151 !important;
    color: #f9fafb !important;
}

[data-bs-theme="dark"] .list-group-item:hover {
    background-color: #374151 !important;
}

[data-bs-theme="dark"] .list-group-item.active {
    background-color: #60a5fa !important;
    border-color: #60a5fa !important;
    color: #ffffff !important;
}

/* Dark Mode for Tabs */
[data-bs-theme="dark"] .nav-tabs {
    border-bottom-color: #374151 !important;
}

[data-bs-theme="dark"] .nav-tabs .nav-link {
    color: #d1d5db !important;
    border-color: transparent !important;
}

[data-bs-theme="dark"] .nav-tabs .nav-link:hover {
    color: #f9fafb !important;
    border-color: #374151 #374151 transparent !important;
}

[data-bs-theme="dark"] .nav-tabs .nav-link.active {
    color: #60a5fa !important;
    background-color: #1f2937 !important;
    border-color: #374151 #374151 #1f2937 !important;
}

[data-bs-theme="dark"] .tab-content {
    background-color: #1f2937 !important;
    border-color: #374151 !important;
}

/* Dark Mode for Accordion */
[data-bs-theme="dark"] .accordion {
    background-color: transparent !important;
}

[data-bs-theme="dark"] .accordion-item {
    background-color: #1f2937 !important;
    border-color: #374151 !important;
}

[data-bs-theme="dark"] .accordion-header .accordion-button {
    background-color: #374151 !important;
    color: #f9fafb !important;
    border-color: #4b5563 !important;
}

[data-bs-theme="dark"] .accordion-header .accordion-button:not(.collapsed) {
    background-color: #60a5fa !important;
    color: #ffffff !important;
}

[data-bs-theme="dark"] .accordion-body {
    background-color: #1f2937 !important;
    color: #d1d5db !important;
}

/* Dark Mode for Offcanvas */
[data-bs-theme="dark"] .offcanvas {
    background-color: #1f2937 !important;
    color: #f9fafb !important;
}

[data-bs-theme="dark"] .offcanvas-header {
    border-bottom-color: #374151 !important;
}

[data-bs-theme="dark"] .offcanvas-title {
    color: #f9fafb !important;
}

/* Dark Mode for Toast */
[data-bs-theme="dark"] .toast {
    background-color: #1f2937 !important;
    border-color: #374151 !important;
    color: #f9fafb !important;
}

[data-bs-theme="dark"] .toast-header {
    background-color: #374151 !important;
    border-bottom-color: #4b5563 !important;
    color: #f9fafb !important;
}

[data-bs-theme="dark"] .toast-body {
    color: #d1d5db !important;
}

/* Dark Mode for Footer */
[data-bs-theme="dark"] .modern-footer {
    background: linear-gradient(135deg, #1f2937, #111827) !important;
    border-top-color: #374151 !important;
    color: #d1d5db !important;
}

[data-bs-theme="dark"] .footer-brand .footer-title {
    color: #f9fafb !important;
}

[data-bs-theme="dark"] .footer-brand .footer-subtitle {
    color: #9ca3af !important;
}

[data-bs-theme="dark"] .footer-link {
    color: #d1d5db !important;
}

[data-bs-theme="dark"] .footer-link:hover {
    color: #60a5fa !important;
}

[data-bs-theme="dark"] .footer-version,
[data-bs-theme="dark"] .footer-copyright {
    color: #9ca3af !important;
}

[data-bs-theme="dark"] .footer-stats .stat-item {
    color: #9ca3af !important;
}

[data-bs-theme="dark"] .footer-stats .stat-item i {
    color: #60a5fa !important;
}

[data-bs-theme="dark"] .footer-bottom {
    border-top-color: #374151 !important;
}

/* Dark Mode for Badges */
[data-bs-theme="dark"] .badge {
    color: #ffffff !important;
}

[data-bs-theme="dark"] .badge.bg-primary {
    background-color: #60a5fa !important;
}

[data-bs-theme="dark"] .badge.bg-secondary {
    background-color: #6b7280 !important;
}

[data-bs-theme="dark"] .badge.bg-success {
    background-color: #10b981 !important;
}

[data-bs-theme="dark"] .badge.bg-danger {
    background-color: #ef4444 !important;
}

[data-bs-theme="dark"] .badge.bg-warning {
    background-color: #f59e0b !important;
    color: #000000 !important;
}

[data-bs-theme="dark"] .badge.bg-info {
    background-color: #06b6d4 !important;
}

[data-bs-theme="dark"] .badge.bg-light {
    background-color: #6b7280 !important;
    color: #ffffff !important;
}

[data-bs-theme="dark"] .badge.bg-dark {
    background-color: #374151 !important;
}

/* Dark Mode for Spinners */
[data-bs-theme="dark"] .spinner-border {
    color: #60a5fa !important;
}

[data-bs-theme="dark"] .spinner-grow {
    color: #60a5fa !important;
}

/* Dark Mode for Close Button */
[data-bs-theme="dark"] .btn-close {
    filter: invert(1) grayscale(100%) brightness(200%);
}

/* Dark Mode for Code */
[data-bs-theme="dark"] code {
    background-color: #374151 !important;
    color: #f9fafb !important;
}

[data-bs-theme="dark"] pre {
    background-color: #1f2937 !important;
    color: #f9fafb !important;
    border-color: #374151 !important;
}

/* Dark Mode for Blockquote */
[data-bs-theme="dark"] blockquote {
    border-left-color: #60a5fa !important;
    color: #d1d5db !important;
}

/* Dark Mode for HR */
[data-bs-theme="dark"] hr {
    border-color: #374151 !important;
    opacity: 1 !important;
}

/* Dark Mode for Custom Components */
[data-bs-theme="dark"] .card-header {
    background-color: #374151 !important;
    border-bottom-color: #4b5563 !important;
    color: #f9fafb !important;
}

[data-bs-theme="dark"] .card-footer {
    background-color: #374151 !important;
    border-top-color: #4b5563 !important;
    color: #d1d5db !important;
}

/* Dark Mode for Dashboard Components */
[data-bs-theme="dark"] .dashboard-card {
    background-color: #1f2937 !important;
    border-color: #374151 !important;
    color: #f9fafb !important;
}

[data-bs-theme="dark"] .dashboard-card .card-title {
    color: #f9fafb !important;
}

[data-bs-theme="dark"] .dashboard-card .card-text {
    color: #d1d5db !important;
}

[data-bs-theme="dark"] .stat-card {
    background: linear-gradient(135deg, #1f2937, #374151) !important;
    border-color: #4b5563 !important;
    color: #f9fafb !important;
}

[data-bs-theme="dark"] .stat-card .stat-value {
    color: #60a5fa !important;
}

[data-bs-theme="dark"] .stat-card .stat-label {
    color: #d1d5db !important;
}

[data-bs-theme="dark"] .stat-card .stat-icon {
    color: #60a5fa !important;
}

/* Dark Mode for Data Tables */
[data-bs-theme="dark"] .dataTables_wrapper {
    color: #f9fafb !important;
}

[data-bs-theme="dark"] .dataTables_wrapper .dataTables_length,
[data-bs-theme="dark"] .dataTables_wrapper .dataTables_filter,
[data-bs-theme="dark"] .dataTables_wrapper .dataTables_info,
[data-bs-theme="dark"] .dataTables_wrapper .dataTables_paginate {
    color: #d1d5db !important;
}

[data-bs-theme="dark"] .dataTables_wrapper .dataTables_length select,
[data-bs-theme="dark"] .dataTables_wrapper .dataTables_filter input {
    background-color: #374151 !important;
    border-color: #4b5563 !important;
    color: #f9fafb !important;
}

/* Dark Mode for Charts */
[data-bs-theme="dark"] .chart-container {
    background-color: #1f2937 !important;
    border-color: #374151 !important;
}

/* Dark Mode for Notifications */
[data-bs-theme="dark"] .notification-item {
    background-color: #1f2937 !important;
    border-color: #374151 !important;
    color: #f9fafb !important;
}

[data-bs-theme="dark"] .notification-item:hover {
    background-color: #374151 !important;
}

[data-bs-theme="dark"] .notification-item.unread {
    background-color: rgba(96, 165, 250, 0.1) !important;
    border-left-color: #60a5fa !important;
}

[data-bs-theme="dark"] .notification-title {
    color: #f9fafb !important;
}

[data-bs-theme="dark"] .notification-message {
    color: #d1d5db !important;
}

[data-bs-theme="dark"] .unread-indicator {
    background-color: #60a5fa !important;
}

/* Dark Mode for Search */
[data-bs-theme="dark"] .search-container {
    background-color: #1f2937 !important;
    border-color: #374151 !important;
}

[data-bs-theme="dark"] .search-input {
    background-color: #374151 !important;
    border-color: #4b5563 !important;
    color: #f9fafb !important;
}

[data-bs-theme="dark"] .search-input::placeholder {
    color: #9ca3af !important;
}

[data-bs-theme="dark"] .search-results {
    background-color: #1f2937 !important;
    border-color: #374151 !important;
}

[data-bs-theme="dark"] .search-result-item {
    color: #d1d5db !important;
    border-bottom-color: #374151 !important;
}

[data-bs-theme="dark"] .search-result-item:hover {
    background-color: #374151 !important;
    color: #f9fafb !important;
}

/* FORCE DARK MODE - HIGHEST SPECIFICITY */
/* This section ensures dark mode is applied even if other CSS tries to override */

/* Force Sidebar Dark Background */
html[data-bs-theme="dark"] body .modern-sidebar,
html[data-bs-theme="dark"] body .sidebar,
html[data-bs-theme="dark"] body aside.sidebar,
html[data-bs-theme="dark"] body #sidebar {
    background: #111827 !important;
    background-color: #111827 !important;
    background-image: none !important;
}

/* Force Navbar Dark Background */
html[data-bs-theme="dark"] body .modern-navbar,
html[data-bs-theme="dark"] body .navbar,
html[data-bs-theme="dark"] body nav.navbar {
    background: #111827 !important;
    background-color: #111827 !important;
    background-image: none !important;
}

/* Force Sidebar Sections Dark Background */
html[data-bs-theme="dark"] body .sidebar-header,
html[data-bs-theme="dark"] body .sidebar-navigation,
html[data-bs-theme="dark"] body .sidebar-footer,
html[data-bs-theme="dark"] body .sidebar-user {
    background: #111827 !important;
    background-color: #111827 !important;
    background-image: none !important;
}

/* Force Main Content Dark Background */
html[data-bs-theme="dark"] body {
    background: #0f172a !important;
    background-color: #0f172a !important;
    color: #f9fafb !important;
}

html[data-bs-theme="dark"] body .main-content {
    background: #0f172a !important;
    background-color: #0f172a !important;
}

/* Force Container Dark Background */
html[data-bs-theme="dark"] body .container-fluid {
    background: #0f172a !important;
    background-color: #0f172a !important;
}

/* Override any gradient backgrounds in dark mode */
html[data-bs-theme="dark"] * {
    background-image: none !important;
}

html[data-bs-theme="dark"] .modern-sidebar,
html[data-bs-theme="dark"] .modern-navbar {
    background-image: none !important;
    background: #111827 !important;
}

/* Override Bootstrap navbar classes */
html[data-bs-theme="dark"] .navbar-light,
html[data-bs-theme="dark"] .navbar-dark,
html[data-bs-theme="dark"] .bg-light,
html[data-bs-theme="dark"] .bg-white,
html[data-bs-theme="dark"] .bg-primary,
html[data-bs-theme="dark"] .bg-secondary {
    background: #111827 !important;
    background-color: #111827 !important;
}

/* Override any CSS that might set sidebar/navbar backgrounds */
html[data-bs-theme="dark"] .sidebar[style*="background"],
html[data-bs-theme="dark"] .navbar[style*="background"],
html[data-bs-theme="dark"] .modern-sidebar[style*="background"],
html[data-bs-theme="dark"] .modern-navbar[style*="background"] {
    background: #111827 !important;
    background-color: #111827 !important;
    background-image: none !important;
}

/* Force dark mode on all possible sidebar and navbar selectors */
html[data-bs-theme="dark"] aside,
html[data-bs-theme="dark"] nav,
html[data-bs-theme="dark"] .sidebar,
html[data-bs-theme="dark"] .navbar,
html[data-bs-theme="dark"] .modern-sidebar,
html[data-bs-theme="dark"] .modern-navbar,
html[data-bs-theme="dark"] #sidebar,
html[data-bs-theme="dark"] #navbar,
html[data-bs-theme="dark"] [class*="sidebar"],
html[data-bs-theme="dark"] [class*="navbar"] {
    background: #111827 !important;
    background-color: #111827 !important;
    background-image: none !important;
}

/* Ensure text colors are correct in dark mode */
html[data-bs-theme="dark"] .sidebar *,
html[data-bs-theme="dark"] .navbar *,
html[data-bs-theme="dark"] .modern-sidebar *,
html[data-bs-theme="dark"] .modern-navbar * {
    color: #f9fafb !important;
}

html[data-bs-theme="dark"] .sidebar .text-muted,
html[data-bs-theme="dark"] .navbar .text-muted,
html[data-bs-theme="dark"] .modern-sidebar .text-muted,
html[data-bs-theme="dark"] .modern-navbar .text-muted {
    color: #9ca3af !important;
}

/* ===== NAVBAR COLOR VARIANTS ===== */

/* White Navbar */
.navbar-variant-white {
    background: #ffffff !important;
    border-bottom: 1px solid #e9ecef;
    color: #333333 !important;
}

.navbar-variant-white .nav-link,
.navbar-variant-white .navbar-brand,
.navbar-variant-white .btn-link {
    color: #333333 !important;
}

.navbar-variant-white .nav-link:hover {
    background: rgba(0, 0, 0, 0.05);
}

/* Primary Navbar */
.navbar-variant-primary {
    background: linear-gradient(135deg, #007bff, #0056b3) !important;
    color: #ffffff !important;
    border-bottom: 1px solid #0056b3;
}

.navbar-variant-primary .nav-link,
.navbar-variant-primary .navbar-brand,
.navbar-variant-primary .btn-link {
    color: #ffffff !important;
}

.navbar-variant-primary .nav-link:hover {
    background: rgba(255, 255, 255, 0.1);
}

/* Secondary Navbar */
.navbar-variant-secondary {
    background: linear-gradient(135deg, #6c757d, #545b62) !important;
    color: #ffffff !important;
    border-bottom: 1px solid #545b62;
}

.navbar-variant-secondary .nav-link,
.navbar-variant-secondary .navbar-brand,
.navbar-variant-secondary .btn-link {
    color: #ffffff !important;
}

/* Success Navbar */
.navbar-variant-success {
    background: linear-gradient(135deg, #28a745, #1e7e34) !important;
    color: #ffffff !important;
    border-bottom: 1px solid #1e7e34;
}

.navbar-variant-success .nav-link,
.navbar-variant-success .navbar-brand,
.navbar-variant-success .btn-link {
    color: #ffffff !important;
}

/* Info Navbar */
.navbar-variant-info {
    background: linear-gradient(135deg, #17a2b8, #117a8b) !important;
    color: #ffffff !important;
    border-bottom: 1px solid #117a8b;
}

.navbar-variant-info .nav-link,
.navbar-variant-info .navbar-brand,
.navbar-variant-info .btn-link {
    color: #ffffff !important;
}

/* Warning Navbar */
.navbar-variant-warning {
    background: linear-gradient(135deg, #ffc107, #d39e00) !important;
    color: #212529 !important;
    border-bottom: 1px solid #d39e00;
}

.navbar-variant-warning .nav-link,
.navbar-variant-warning .navbar-brand,
.navbar-variant-warning .btn-link {
    color: #212529 !important;
}

/* Danger Navbar */
.navbar-variant-danger {
    background: linear-gradient(135deg, #dc3545, #bd2130) !important;
    color: #ffffff !important;
    border-bottom: 1px solid #bd2130;
}

.navbar-variant-danger .nav-link,
.navbar-variant-danger .navbar-brand,
.navbar-variant-danger .btn-link {
    color: #ffffff !important;
}

/* Dark Navbar */
.navbar-variant-dark {
    background: linear-gradient(135deg, #343a40, #1d2124) !important;
    color: #ffffff !important;
    border-bottom: 1px solid #1d2124;
}

.navbar-variant-dark .nav-link,
.navbar-variant-dark .navbar-brand,
.navbar-variant-dark .btn-link {
    color: #ffffff !important;
}

/* Gradient Primary Navbar */
.navbar-variant-gradient-primary {
    background: linear-gradient(45deg, #007bff, #6610f2) !important;
    color: #ffffff !important;
    border-bottom: 1px solid #6610f2;
}

.navbar-variant-gradient-primary .nav-link,
.navbar-variant-gradient-primary .navbar-brand,
.navbar-variant-gradient-primary .btn-link {
    color: #ffffff !important;
}

/* Transparent/Glass Navbar */
.navbar-variant-transparent {
    background: rgba(255, 255, 255, 0.9) !important;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    color: #333333 !important;
}

.navbar-variant-transparent .nav-link,
.navbar-variant-transparent .navbar-brand,
.navbar-variant-transparent .btn-link {
    color: #333333 !important;
}

/* ===== NAVBAR ICON COLOR VARIANTS ===== */

/* Default Icons */
.navbar-icons-default .nav-link i,
.navbar-icons-default .btn-link i {
    color: inherit !important;
}

/* Primary Icons */
.navbar-icons-primary .nav-link i,
.navbar-icons-primary .btn-link i {
    color: #007bff !important;
}

/* Success Icons */
.navbar-icons-success .nav-link i,
.navbar-icons-success .btn-link i {
    color: #28a745 !important;
}

/* White Icons */
.navbar-icons-white .nav-link i,
.navbar-icons-white .btn-link i {
    color: #ffffff !important;
}

/* Dark Icons */
.navbar-icons-dark .nav-link i,
.navbar-icons-dark .btn-link i {
    color: #1a1a1a !important;
}

/* ===== LAYOUT OPTIONS ===== */

/* Header Fixed */
.header-fixed .modern-navbar {
    position: fixed !important;
    top: 0;
    z-index: 1030;
}

.header-fixed .main-content {
    margin-top: 72px;
}

/* Footer Fixed */
.footer-fixed .modern-footer {
    position: fixed !important;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 1020;
}

.footer-fixed .main-content {
    margin-bottom: 60px;
}

/* Header No Border */
.header-no-border .modern-navbar {
    border-bottom: none !important;
    box-shadow: none !important;
}

/* Dropdown Legacy */
.dropdown-legacy .dropdown-menu {
    border-radius: 0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Small Text */
.text-sm {
    font-size: 0.875rem !important;
}

.text-sm .nav-link {
    font-size: 0.8rem !important;
}

.text-sm .navbar-brand {
    font-size: 1.1rem !important;
}

/* Navbar Shadow */
.navbar-shadow .modern-navbar {
    box-shadow: 0 2px 8px rgba(0,0,0,0.15) !important;
}

/* Sidebar Shadow */
.sidebar-shadow .modern-sidebar {
    box-shadow: 2px 0 8px rgba(0,0,0,0.15) !important;
}

/* ===== SIDEBAR OPTIONS ===== */

/* Sidebar Fixed */
.sidebar-fixed .modern-sidebar {
    position: fixed !important;
}

/* Sidebar Mini */
.sidebar-mini .modern-sidebar {
    width: 70px !important;
}

.sidebar-mini .modern-sidebar .menu-text {
    display: none;
}

.sidebar-mini .modern-sidebar .menu-icon {
    text-align: center;
    width: 100%;
}

/* Sidebar Mini on Tablet */
@media (max-width: 992px) {
    .sidebar-mini-md .modern-sidebar {
        width: 70px !important;
    }

    .sidebar-mini-md .modern-sidebar .menu-text {
        display: none;
    }
}

/* Sidebar Mini on Mobile */
@media (max-width: 768px) {
    .sidebar-mini-xs .modern-sidebar {
        width: 70px !important;
    }

    .sidebar-mini-xs .modern-sidebar .menu-text {
        display: none;
    }
}

/* Navigation Styles */
.nav-flat .modern-sidebar .menu-item {
    border-radius: 0 !important;
}

.nav-legacy .modern-sidebar {
    background: #f8f9fa;
    border-right: 1px solid #dee2e6;
}

.nav-compact .modern-sidebar .menu-item {
    padding: 0.5rem 1rem !important;
    font-size: 0.875rem;
}

.nav-child-indent .modern-sidebar .submenu .menu-item {
    padding-left: 3rem !important;
}

.nav-child-hide.sidebar-collapsed .modern-sidebar .submenu {
    display: none !important;
}

.nav-disable-hover .modern-sidebar .menu-item:hover {
    background: transparent !important;
}

/* ===== SIDEBAR COLOR VARIANTS ===== */

/* Default Sidebar */
.sidebar-variant-default {
    background: linear-gradient(135deg, #ffffff, #f8f9fa) !important;
    border-right: 1px solid #dee2e6;
}

/* Dark Sidebar */
.sidebar-variant-dark {
    background: linear-gradient(135deg, #343a40, #1d2124) !important;
    color: #ffffff !important;
}

.sidebar-variant-dark .menu-item {
    color: #ffffff !important;
}

/* Primary Sidebar */
.sidebar-variant-primary {
    background: linear-gradient(135deg, #007bff, #0056b3) !important;
    color: #ffffff !important;
}

.sidebar-variant-primary .menu-item {
    color: #ffffff !important;
}

/* Success Sidebar */
.sidebar-variant-success {
    background: linear-gradient(135deg, #28a745, #1e7e34) !important;
    color: #ffffff !important;
}

.sidebar-variant-success .menu-item {
    color: #ffffff !important;
}

/* Info Sidebar */
.sidebar-variant-info {
    background: linear-gradient(135deg, #17a2b8, #117a8b) !important;
    color: #ffffff !important;
}

.sidebar-variant-info .menu-item {
    color: #ffffff !important;
}

/* Gradient Variants */
.sidebar-variant-gradient-blue {
    background: linear-gradient(135deg, #667eea, #764ba2) !important;
    color: #ffffff !important;
}

.sidebar-variant-gradient-green {
    background: linear-gradient(135deg, #11998e, #38ef7d) !important;
    color: #ffffff !important;
}

.sidebar-variant-gradient-purple {
    background: linear-gradient(135deg, #667eea, #764ba2) !important;
    color: #ffffff !important;
}

/* ===== CONTENT AREA VARIANTS ===== */

/* Content Background Variants */
.content-bg-default {
    background: #f8f9fa !important;
}

.content-bg-white {
    background: #ffffff !important;
}

.content-bg-gray {
    background: #e9ecef !important;
}

.content-bg-pattern {
    background: repeating-linear-gradient(45deg, #f8f9fa, #f8f9fa 10px, #ffffff 10px, #ffffff 20px) !important;
}

/* Content Layout Options */
.content-boxed .main-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

.content-no-padding .main-content {
    padding: 0 !important;
}

.no-card-shadows .card {
    box-shadow: none !important;
    border: 1px solid #dee2e6;
}

.no-rounded-corners .card,
.no-rounded-corners .btn,
.no-rounded-corners .form-control {
    border-radius: 0 !important;
}

/* ===== TYPOGRAPHY VARIANTS ===== */

/* Font Families */
.font-inter {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif !important;
}

.font-roboto {
    font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif !important;
}

.font-opensans {
    font-family: 'Open Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif !important;
}

.font-lato {
    font-family: 'Lato', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif !important;
}

.font-poppins {
    font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif !important;
}

.font-nunito {
    font-family: 'Nunito', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif !important;
}

.font-montserrat {
    font-family: 'Montserrat', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif !important;
}

/* Font Sizes */
.font-size-small {
    font-size: 14px !important;
}

.font-size-default {
    font-size: 16px !important;
}

.font-size-large {
    font-size: 18px !important;
}

.font-size-xlarge {
    font-size: 20px !important;
}

/* Typography Options */
.font-smoothing {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.text-shadow {
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* ===== ANIMATION & EFFECTS ===== */

/* Animation Speeds */
.animation-slow * {
    transition-duration: 0.5s !important;
}

.animation-normal * {
    transition-duration: 0.3s !important;
}

.animation-fast * {
    transition-duration: 0.15s !important;
}

.animation-none * {
    transition: none !important;
}

/* Visual Effects */
.no-hover-effects *:hover {
    transform: none !important;
    box-shadow: none !important;
}

.fade-transitions * {
    transition: opacity 0.3s ease, transform 0.3s ease;
}

.slide-animations .card,
.slide-animations .btn {
    transition: transform 0.3s ease;
}

.slide-animations .card:hover {
    transform: translateY(-2px);
}

.parallax-effect {
    background-attachment: fixed;
}

/* ===== ACCESSIBILITY ===== */

/* High Contrast */
.high-contrast {
    filter: contrast(150%);
}

.high-contrast .card {
    border: 2px solid #000000;
}

.high-contrast .btn {
    border: 2px solid currentColor;
}

/* Focus Indicators */
.focus-indicators *:focus {
    outline: 3px solid #007bff !important;
    outline-offset: 2px !important;
}

/* Reduced Motion */
.reduced-motion * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
}

/* Screen Reader Optimized */
.screen-reader-optimized .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* ===== MODERN RESPONSIVE SIDEBAR ===== */

/* CSS Variables for Sidebar */
:root {
    --sidebar-width: 280px;
    --sidebar-collapsed-width: 80px;
    --sidebar-bg: #ffffff;
    --sidebar-border: #e5e7eb;
    --sidebar-text: #374151;
    --sidebar-text-muted: #6b7280;
    --sidebar-hover: #f3f4f6;
    --sidebar-active: #3b82f6;
    --sidebar-active-bg: #eff6ff;
    --sidebar-active-shadow: rgba(59, 130, 246, 0.3);
    --primary-hover: #2563eb;
    --sidebar-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --sidebar-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Dark mode variables */
[data-bs-theme="dark"] {
    --sidebar-bg: #111827;
    --sidebar-border: #374151;
    --sidebar-text: #f9fafb;
    --sidebar-text-muted: #d1d5db;
    --sidebar-hover: #374151;
    --sidebar-active: #60a5fa;
    --sidebar-active-bg: #1e3a8a;
    --primary-hover: #3b82f6;

    /* Additional dark mode variables */
    --bg-primary: #111827;
    --bg-secondary: #0f172a;
    --bg-tertiary: #374151;
    --text-primary: #f9fafb;
    --text-secondary: #d1d5db;
    --text-muted: #9ca3af;
    --border-color: #374151;
    --border-light: #4b5563;

    /* Navbar variables */
    --navbar-bg: #111827;
    --navbar-border: #374151;
}

/* Modern Sidebar Base */
.modern-sidebar {
    width: var(--sidebar-width);
    height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1000;
    background: var(--sidebar-bg);
    border-right: 1px solid var(--sidebar-border);
    box-shadow: var(--sidebar-shadow);
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    overflow-x: visible; /* Allow dropdown to show outside */
    transition: var(--sidebar-transition);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
}

/* Sidebar States */
.sidebar-collapsed .modern-sidebar {
    width: var(--sidebar-collapsed-width);
}

.sidebar-hidden .modern-sidebar {
    transform: translateX(-100%);
}

/* Mobile Overlay */
.sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transition: var(--sidebar-transition);
}

.sidebar-mobile-open .sidebar-overlay {
    opacity: 1;
    visibility: visible;
}

/* Simple Modern Sidebar Header */
.sidebar-header {
    padding: 1.5rem;
    background: var(--sidebar-bg);
    border-bottom: 1px solid var(--sidebar-border);
    flex-shrink: 0;
    transition: var(--sidebar-transition);
}

.brand-container {
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: var(--sidebar-transition);
}

.brand-logo {
    position: relative;
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.logo-icon {
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--sidebar-active);
    font-size: 2rem;
    transition: var(--sidebar-transition);
}

.logo-icon:hover {
    transform: scale(1.1);
    color: var(--primary-hover);
}

.brand-content {
    flex: 1;
    min-width: 0;
    opacity: 1;
    transition: var(--sidebar-transition);
}



.brand-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--sidebar-text);
    margin: 0;
    line-height: 1.2;
    white-space: nowrap;
}

.brand-subtitle {
    font-size: 0.75rem;
    color: var(--sidebar-text-muted);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-top: 2px;
}

/* Simple User Profile Section */
.sidebar-user {
    padding: 1.5rem;
    background: var(--sidebar-bg);
    border-bottom: 1px solid var(--sidebar-border);
    flex-shrink: 0;
}

.user-profile {
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: var(--sidebar-transition);
    padding: 1rem;
    background: var(--sidebar-hover);
    border-radius: 12px;
    border: 1px solid var(--sidebar-border);
}

.user-profile:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-color: var(--sidebar-active);
}

.user-avatar {
    position: relative;
    width: 40px;
    height: 40px;
    flex-shrink: 0;
}

.avatar-img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    border: 2px solid var(--sidebar-active);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    object-fit: cover;
}

.status-indicator {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid var(--sidebar-bg);
    background-color: #10b981;
}

.user-details {
    flex: 1;
    min-width: 0;
    opacity: 1;
    transition: var(--sidebar-transition);
}



.user-name {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--sidebar-text);
    margin: 0 0 0.25rem 0;
    line-height: 1.2;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.user-role {
    display: inline-block;
    font-size: 0.75rem;
    font-weight: 500;
    color: var(--sidebar-active);
    background: var(--sidebar-active-bg);
    padding: 0.125rem 0.5rem;
    border-radius: 6px;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

/* Navigation Menu */
.sidebar-navigation {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    padding: 1rem 0;
    scrollbar-width: thin;
    scrollbar-color: var(--sidebar-border) transparent;
}

.sidebar-navigation::-webkit-scrollbar {
    width: 4px;
}

.sidebar-navigation::-webkit-scrollbar-track {
    background: transparent;
}

.sidebar-navigation::-webkit-scrollbar-thumb {
    background: var(--sidebar-border);
    border-radius: 2px;
}

.sidebar-navigation::-webkit-scrollbar-thumb:hover {
    background: var(--sidebar-text-muted);
}

.nav-menu {
    padding: 0 1rem;
}

.menu-section {
    margin-bottom: 1.5rem;
}

.menu-section-title {
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--sidebar-text-muted);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    padding: 0 1rem 0.5rem 1rem;
    margin-bottom: 0.5rem;
    opacity: 1;
    transition: var(--sidebar-transition);
}



.menu-item {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 0.75rem 1rem;
    margin-bottom: 0.25rem;
    border: none;
    background: transparent;
    color: var(--sidebar-text);
    text-decoration: none;
    border-radius: 10px;
    transition: var(--sidebar-transition);
    font-size: 0.875rem;
    font-weight: 500;
    position: relative;
    cursor: pointer;
    border: 1px solid transparent;
}

.menu-item:hover {
    background: var(--sidebar-hover);
    color: var(--sidebar-text);
    transform: translateX(2px);
    border-color: var(--sidebar-border);
}

.menu-item.active {
    background: var(--sidebar-active-bg);
    color: var(--sidebar-active);
    border-color: var(--sidebar-active);
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.15);
}

.menu-item.active::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 3px;
    height: 20px;
    background: var(--sidebar-active);
    border-radius: 0 2px 2px 0;
}

.menu-icon {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.75rem;
    flex-shrink: 0;
    transition: var(--sidebar-transition);
}



.menu-icon i {
    font-size: 1.125rem;
    color: var(--sidebar-text-muted);
    transition: var(--sidebar-transition);
}

.menu-item:hover .menu-icon i,
.menu-item.active .menu-icon i {
    color: var(--sidebar-active);
    transform: scale(1.1);
}

.menu-text {
    flex: 1;
    text-align: left;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    opacity: 1;
    transition: var(--sidebar-transition);
}



.menu-arrow {
    margin-left: auto;
    font-size: 0.75rem;
    color: var(--sidebar-text-muted);
    transition: var(--sidebar-transition);
    opacity: 1;
}



.menu-toggle[aria-expanded="true"] .menu-arrow {
    transform: rotate(180deg);
}

.menu-badge {
    margin-left: auto;
    background: var(--sidebar-active);
    color: white;
    font-size: 0.75rem;
    font-weight: 600;
    padding: 0.125rem 0.375rem;
    border-radius: 10px;
    min-width: 18px;
    text-align: center;
    opacity: 1;
    transition: var(--sidebar-transition);
}



/* Submenu Styles */
.submenu {
    margin-top: 0.25rem;
    margin-left: 2.5rem;
    border-left: 2px solid var(--sidebar-border);
    padding-left: 1rem;
    opacity: 1;
    transition: var(--sidebar-transition);
}



.submenu-item {
    display: flex;
    align-items: center;
    padding: 0.5rem 0.75rem;
    margin-bottom: 0.125rem;
    color: var(--sidebar-text-muted);
    text-decoration: none;
    border-radius: 8px;
    transition: var(--sidebar-transition);
    font-size: 0.8125rem;
    font-weight: 500;
    position: relative;
}

.submenu-item:hover {
    background: var(--sidebar-hover);
    color: var(--sidebar-text);
    transform: translateX(2px);
}

.submenu-item.active {
    background: var(--sidebar-active-bg);
    color: var(--sidebar-active);
    font-weight: 600;
}

.submenu-item.active::before {
    content: '';
    position: absolute;
    left: -1rem;
    top: 50%;
    transform: translateY(-50%);
    width: 2px;
    height: 16px;
    background: var(--sidebar-active);
    border-radius: 1px;
}

.submenu-icon {
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.5rem;
    flex-shrink: 0;
}

.submenu-icon i {
    font-size: 0.875rem;
    color: var(--sidebar-text-muted);
    transition: var(--sidebar-transition);
}

.submenu-item:hover .submenu-icon i,
.submenu-item.active .submenu-icon i {
    color: var(--sidebar-active);
}

/* Modern Compact Sidebar Footer */
.sidebar-footer {
    padding: 0.75rem;
    border-top: 1px solid var(--sidebar-border);
    background: var(--sidebar-bg);
    flex-shrink: 0;
    margin-top: auto;
}

.sidebar-footer-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.375rem;
}

.theme-toggle-btn {
    width: 100%;
    height: 36px;
    border: 1px solid var(--sidebar-border);
    background: var(--sidebar-hover);
    border-radius: 6px;
    color: var(--sidebar-text);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    font-size: 0.8rem;
    font-weight: 500;
    gap: 0.375rem;
    position: relative;
    overflow: hidden;
}

.theme-toggle-btn:hover {
    background: var(--sidebar-active-bg);
    border-color: var(--sidebar-active);
    color: var(--sidebar-active);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.15);
}

.theme-toggle-btn:active {
    transform: translateY(0);
    box-shadow: 0 1px 4px rgba(59, 130, 246, 0.1);
}

.sidebar-toggle-btn {
    width: 36px;
    height: 36px;
    border: 1px solid var(--sidebar-border);
    background: var(--sidebar-hover);
    border-radius: 6px;
    color: var(--sidebar-text);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    font-size: 0.8rem;
    position: relative;
    overflow: hidden;
}

.sidebar-toggle-btn:hover {
    background: var(--sidebar-active-bg);
    border-color: var(--sidebar-active);
    color: var(--sidebar-active);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.15);
}

.sidebar-toggle-btn:active {
    transform: translateY(0);
    box-shadow: 0 1px 4px rgba(59, 130, 246, 0.1);
}

/* Theme toggle text visibility */
.theme-toggle-text {
    font-size: 0.75rem;
    font-weight: 500;
    opacity: 1;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.theme-toggle-btn i {
    font-size: 0.875rem;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Add subtle gradient and modern effects */
.theme-toggle-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, transparent 0%, rgba(59, 130, 246, 0.05) 100%);
    border-radius: inherit;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.theme-toggle-btn:hover::before {
    opacity: 1;
}

.sidebar-toggle-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, transparent 0%, rgba(59, 130, 246, 0.05) 100%);
    border-radius: inherit;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.sidebar-toggle-btn:hover::before {
    opacity: 1;
}

/* Dark mode adjustments */
[data-bs-theme="dark"] .theme-toggle-btn::before,
[data-bs-theme="dark"] .sidebar-toggle-btn::before {
    background: linear-gradient(135deg, transparent 0%, rgba(96, 165, 250, 0.1) 100%);
}

/* Dropdown Submenu for Collapsed Sidebar */
.sidebar-collapsed .menu-item.has-submenu {
    position: relative;
}

.collapsed-dropdown {
    position: fixed !important;
    background: #ffffff !important;
    border: 1px solid #e5e7eb !important;
    border-radius: 12px !important;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15) !important;
    padding: 0.75rem 0 !important;
    min-width: 260px !important;
    max-width: 320px !important;
    z-index: 9999 !important;
    opacity: 0 !important;
    visibility: hidden !important;
    transform: translateX(-15px) scale(0.9) !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    backdrop-filter: blur(20px) !important;
    -webkit-backdrop-filter: blur(20px) !important;
    pointer-events: none !important;
    display: none !important;
    border-left: 4px solid var(--sidebar-active) !important;
}

.collapsed-dropdown.show {
    opacity: 1 !important;
    visibility: visible !important;
    transform: translateX(0) scale(1) !important;
    pointer-events: auto !important;
    display: block !important;
}

/* Arrow pointer */
.collapsed-dropdown::before {
    content: '';
    position: absolute;
    left: -8px;
    top: 25px;
    width: 0;
    height: 0;
    border-top: 10px solid transparent;
    border-bottom: 10px solid transparent;
    border-right: 10px solid #ffffff;
    filter: drop-shadow(-2px 0 2px rgba(0, 0, 0, 0.1));
}

.collapsed-dropdown::after {
    content: '';
    position: absolute;
    left: -4px;
    top: 25px;
    width: 0;
    height: 0;
    border-top: 10px solid transparent;
    border-bottom: 10px solid transparent;
    border-right: 10px solid var(--sidebar-active);
}

/* Dropdown header */
.collapsed-dropdown-header {
    padding: 1rem 1.25rem 0.75rem;
    border-bottom: 1px solid #f3f4f6;
    margin-bottom: 0.5rem;
    background: linear-gradient(135deg, rgba(37, 99, 235, 0.05), rgba(59, 130, 246, 0.02));
    border-radius: 12px 12px 0 0;
}

.collapsed-dropdown-title {
    font-size: 0.9rem;
    font-weight: 700;
    color: #1f2937;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.collapsed-dropdown-title i {
    color: var(--sidebar-active);
    font-size: 1rem;
}

/* Dropdown items */
.collapsed-dropdown .submenu-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 1.25rem;
    color: #4b5563;
    text-decoration: none;
    transition: all 0.3s ease;
    border-radius: 8px;
    margin: 0.25rem 0.75rem;
    white-space: nowrap;
    font-weight: 500;
    border: 1px solid transparent;
}

.collapsed-dropdown .submenu-item:hover {
    background: linear-gradient(135deg, rgba(37, 99, 235, 0.1), rgba(59, 130, 246, 0.05));
    color: var(--sidebar-active);
    transform: translateX(5px);
    border-color: rgba(37, 99, 235, 0.2);
    box-shadow: 0 2px 8px rgba(37, 99, 235, 0.15);
}

.collapsed-dropdown .submenu-item.active {
    background: linear-gradient(135deg, var(--sidebar-active), rgba(37, 99, 235, 0.9));
    color: #ffffff;
    font-weight: 600;
    border-color: var(--sidebar-active);
    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
}

.collapsed-dropdown .submenu-icon {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    border-radius: 4px;
    background: rgba(37, 99, 235, 0.1);
}

.collapsed-dropdown .submenu-icon i {
    font-size: 0.9rem;
    color: var(--sidebar-active);
}

.collapsed-dropdown .submenu-item:hover .submenu-icon {
    background: rgba(255, 255, 255, 0.2);
}

.collapsed-dropdown .submenu-item.active .submenu-icon {
    background: rgba(255, 255, 255, 0.2);
}

.collapsed-dropdown .submenu-item.active .submenu-icon i {
    color: #ffffff;
}

.collapsed-dropdown .submenu-text {
    flex: 1;
    font-size: 0.9rem;
    font-weight: 500;
}

/* Animation for dropdown items */
.collapsed-dropdown .submenu-item {
    opacity: 0;
    transform: translateY(-5px);
    animation: dropdownItemFadeIn 0.3s ease forwards;
}

.collapsed-dropdown .submenu-item:nth-child(1) { animation-delay: 0.05s; }
.collapsed-dropdown .submenu-item:nth-child(2) { animation-delay: 0.1s; }
.collapsed-dropdown .submenu-item:nth-child(3) { animation-delay: 0.15s; }
.collapsed-dropdown .submenu-item:nth-child(4) { animation-delay: 0.2s; }
.collapsed-dropdown .submenu-item:nth-child(5) { animation-delay: 0.25s; }
.collapsed-dropdown .submenu-item:nth-child(6) { animation-delay: 0.3s; }

@keyframes dropdownItemFadeIn {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Backdrop for closing dropdown */
.dropdown-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: transparent;
    z-index: 1000;
    display: none;
}

.dropdown-backdrop.show {
    display: block;
}

/* Mobile Responsive */
@media (max-width: 1024px) {
    .modern-sidebar {
        transform: translateX(-100%);
    }

    .sidebar-mobile-open .modern-sidebar {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0 !important;
    }
}

@media (max-width: 768px) {
    .modern-sidebar {
        width: 280px;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
    }

    .sidebar-header {
        padding: 1.5rem 1rem;
    }

    .sidebar-user {
        padding: 1rem;
    }

    .user-profile {
        padding: 1rem;
    }

    .logo-icon {
        width: 48px;
        height: 48px;
        font-size: 1.75rem;
    }

    .brand-title {
        font-size: 1.25rem;
    }

    .brand-subtitle {
        font-size: 0.75rem;
    }

    .user-avatar {
        width: 40px;
        height: 40px;
    }

    .status-indicator {
        width: 14px;
        height: 14px;
    }

    /* Mobile footer adjustments */
    .sidebar-footer {
        padding: 0.625rem;
    }

    .theme-toggle-btn {
        height: 34px;
        font-size: 0.75rem;
    }

    .sidebar-toggle-btn {
        width: 34px;
        height: 34px;
    }
}

@media (max-width: 480px) {
    .modern-sidebar {
        width: 100%;
    }

    .sidebar-header {
        padding: 1rem 0.75rem;
    }

    .sidebar-user {
        padding: 0.75rem;
    }

    .user-profile {
        padding: 0.75rem;
    }

    .logo-icon {
        width: 44px;
        height: 44px;
        font-size: 1.5rem;
    }

    .brand-title {
        font-size: 1.125rem;
    }

    .brand-title::after {
        right: -1rem;
        font-size: 0.75rem;
    }

    .user-avatar {
        width: 36px;
        height: 36px;
    }

    .status-indicator {
        width: 12px;
        height: 12px;
        border-width: 2px;
    }
}

/* Collapsed Sidebar Styles */
.sidebar-collapsed .menu-text,
.sidebar-collapsed .menu-arrow,
.sidebar-collapsed .brand-content,
.sidebar-collapsed .user-details,
.sidebar-collapsed .menu-section-title,
.sidebar-collapsed .theme-toggle-text {
    display: none;
}

.sidebar-collapsed .menu-item {
    justify-content: center;
    padding: 1rem 0.75rem;
    margin: 0.25rem 0.5rem;
    border-radius: 8px;
    position: relative;
}

.sidebar-collapsed .menu-item:hover {
    background: var(--menu-hover-bg);
    transform: translateX(2px);
}

.sidebar-collapsed .menu-icon {
    margin-right: 0;
    font-size: 1.1rem;
}

.sidebar-collapsed .user-profile {
    justify-content: center;
    padding: 0.75rem;
}

.sidebar-collapsed .user-avatar {
    margin-right: 0;
}

.sidebar-collapsed .brand-logo {
    justify-content: center;
    margin-bottom: 0;
}

.sidebar-collapsed .logo-icon {
    font-size: 1.5rem;
}

.sidebar-collapsed .sidebar-footer {
    padding: 0.5rem;
}

.sidebar-collapsed .sidebar-footer-content {
    justify-content: center;
}

.sidebar-collapsed .theme-toggle-btn {
    width: 32px;
    height: 32px;
    padding: 0;
    justify-content: center;
    border-radius: 6px;
}

/* Tooltip for collapsed sidebar */
.sidebar-collapsed .menu-item {
    position: relative;
}

.sidebar-collapsed .menu-item:hover::after {
    content: attr(data-tooltip);
    position: absolute;
    left: 100%;
    top: 50%;
    transform: translateY(-50%);
    background: var(--sidebar-text);
    color: var(--sidebar-bg);
    padding: 0.5rem 0.75rem;
    border-radius: 6px;
    font-size: 0.875rem;
    white-space: nowrap;
    z-index: 1001;
    margin-left: 0.5rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    opacity: 0;
    animation: tooltipFadeIn 0.2s ease-out forwards;
}

@keyframes tooltipFadeIn {
    from {
        opacity: 0;
        transform: translateY(-50%) translateX(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(-50%) translateX(0);
    }
}

/* Smooth animations */
@keyframes slideInLeft {
    from {
        transform: translateX(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.modern-sidebar {
    animation: slideInLeft 0.3s ease-out;
}

/* ===== GOOGLE FONTS IMPORT ===== */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Roboto:wght@300;400;500;700&family=Open+Sans:wght@300;400;600;700&family=Lato:wght@300;400;700&family=Poppins:wght@300;400;500;600;700&family=Nunito:wght@300;400;600;700&family=Montserrat:wght@300;400;500;600;700&display=swap');

/* Layout Variant Classes */
.text-sm {
    font-size: 0.875rem !important;
}

.text-xs {
    font-size: 0.75rem !important;
}

/* Icon Variants Styles */
.icon-variants {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
    gap: 0.75rem;
}

.icon-option {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem;
    border: 2px solid transparent;
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all var(--transition-fast);
    background: var(--bg-tertiary);
}

.icon-option:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.icon-option.active {
    border-color: var(--primary-color);
    background: rgba(37, 99, 235, 0.1);
}

.icon-preview {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: var(--bg-primary);
    border: 2px solid var(--border-light);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-sm);
}

.icon-option span {
    font-size: 0.75rem;
    color: var(--text-secondary);
    text-align: center;
    font-weight: 500;
}

.icon-option.active span {
    color: var(--primary-color);
    font-weight: 600;
}

/* Pattern Variants Styles */
.pattern-variants {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
    gap: 0.75rem;
}

.pattern-option {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem;
    border: 2px solid transparent;
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all var(--transition-fast);
    background: var(--bg-tertiary);
}

.pattern-option:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.pattern-option.active {
    border-color: var(--primary-color);
    background: rgba(37, 99, 235, 0.1);
}

.pattern-preview {
    width: 30px;
    height: 30px;
    border-radius: var(--radius-md);
    border: 2px solid var(--border-light);
    box-shadow: var(--shadow-sm);
}

.pattern-option span {
    font-size: 0.75rem;
    color: var(--text-secondary);
    text-align: center;
    font-weight: 500;
}

.pattern-option.active span {
    color: var(--primary-color);
    font-weight: 600;
}

/* Enhanced Layout Classes */
.brand-section {
    background: linear-gradient(135deg, rgba(37, 99, 235, 0.1), rgba(59, 130, 246, 0.05));
    border-radius: var(--radius-lg);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    border: 1px solid rgba(37, 99, 235, 0.2);
}

/* Navbar Variants */
.navbar-variant-white {
    background-color: #ffffff !important;
    color: #333333 !important;
}

.navbar-variant-primary {
    background-color: var(--primary-color) !important;
    color: #ffffff !important;
}

.navbar-variant-secondary {
    background-color: var(--secondary-color) !important;
    color: #ffffff !important;
}

.navbar-variant-success {
    background-color: var(--success-color) !important;
    color: #ffffff !important;
}

.navbar-variant-info {
    background-color: var(--info-color) !important;
    color: #ffffff !important;
}

.navbar-variant-warning {
    background-color: var(--warning-color) !important;
    color: #ffffff !important;
}

.navbar-variant-danger {
    background-color: var(--danger-color) !important;
    color: #ffffff !important;
}

.navbar-variant-dark {
    background-color: #1a1a1a !important;
    color: #ffffff !important;
}

.navbar-variant-gradient-primary {
    background: linear-gradient(45deg, #007bff, #6610f2) !important;
    color: #ffffff !important;
}

.navbar-variant-gradient-success {
    background: linear-gradient(45deg, #28a745, #20c997) !important;
    color: #ffffff !important;
}

.navbar-variant-gradient-danger {
    background: linear-gradient(45deg, #dc3545, #e83e8c) !important;
    color: #ffffff !important;
}

.navbar-variant-transparent {
    background: rgba(255, 255, 255, 0.9) !important;
    backdrop-filter: blur(10px) !important;
    -webkit-backdrop-filter: blur(10px) !important;
    color: #333333 !important;
}

/* Navbar Icon Color Variants */
.navbar-icons-default .nav-link i,
.navbar-icons-default .navbar-brand i,
.navbar-icons-default .btn i {
    color: var(--text-secondary) !important;
}

.navbar-icons-primary .nav-link i,
.navbar-icons-primary .navbar-brand i,
.navbar-icons-primary .btn i {
    color: var(--primary-color) !important;
}

.navbar-icons-success .nav-link i,
.navbar-icons-success .navbar-brand i,
.navbar-icons-success .btn i {
    color: var(--success-color) !important;
}

.navbar-icons-info .nav-link i,
.navbar-icons-info .navbar-brand i,
.navbar-icons-info .btn i {
    color: var(--info-color) !important;
}

.navbar-icons-warning .nav-link i,
.navbar-icons-warning .navbar-brand i,
.navbar-icons-warning .btn i {
    color: var(--warning-color) !important;
}

.navbar-icons-danger .nav-link i,
.navbar-icons-danger .navbar-brand i,
.navbar-icons-danger .btn i {
    color: var(--danger-color) !important;
}

.navbar-icons-white .nav-link i,
.navbar-icons-white .navbar-brand i,
.navbar-icons-white .btn i {
    color: #ffffff !important;
}

.navbar-icons-dark .nav-link i,
.navbar-icons-dark .navbar-brand i,
.navbar-icons-dark .btn i {
    color: #1a1a1a !important;
}

/* Layout Variant Classes */
.text-sm {
    font-size: 0.875rem !important;
}

.text-xs {
    font-size: 0.75rem !important;
}

/* Sidebar Layout Options */
.sidebar-shadow .modern-sidebar {
    box-shadow: var(--shadow-lg) !important;
}

.sidebar-no-shadow .modern-sidebar {
    box-shadow: none !important;
}

.sidebar-mini .modern-sidebar {
    width: 70px !important;
}

.sidebar-mini .modern-sidebar .menu-text {
    display: none !important;
}

.sidebar-mini .modern-sidebar .brand-title {
    display: none !important;
}

.sidebar-mini-md .modern-sidebar {
    width: 70px !important;
}

@media (max-width: 991.98px) {
    .sidebar-mini-md .modern-sidebar .menu-text {
        display: none !important;
    }

    .sidebar-mini-md .modern-sidebar .brand-title {
        display: none !important;
    }
}

.sidebar-mini-xs .modern-sidebar {
    width: 70px !important;
}

@media (max-width: 575.98px) {
    .sidebar-mini-xs .modern-sidebar .menu-text {
        display: none !important;
    }

    .sidebar-mini-xs .modern-sidebar .brand-title {
        display: none !important;
    }
}

/* Navigation Style Options */
.nav-flat .modern-sidebar .menu-item {
    border-radius: 0 !important;
}

.nav-legacy .modern-sidebar .menu-item {
    background: transparent !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

.nav-compact .modern-sidebar .menu-item {
    padding: 0.5rem 1rem !important;
    margin-bottom: 0.25rem !important;
}

.nav-child-indent .modern-sidebar .submenu .menu-item {
    padding-left: 3rem !important;
}

.nav-child-hide.sidebar-collapsed .modern-sidebar .submenu {
    display: none !important;
}

.nav-disable-hover .menu-item:hover {
    background: transparent !important;
    transform: none !important;
}

/* Header No Border */
.header-no-border .modern-navbar {
    border-bottom: none !important;
    box-shadow: none !important;
}

/* Footer Fixed */
.footer-fixed .modern-footer {
    position: fixed !important;
    bottom: 0 !important;
    width: 100% !important;
    z-index: 1000 !important;
}

.footer-fixed .main-content {
    padding-bottom: 120px !important;
}

/* Dropdown Legacy Offset */
.dropdown-legacy .dropdown-menu {
    margin-top: 0 !important;
    border-radius: 0 !important;
}

/* Navbar Shadow */
.navbar-shadow .modern-navbar {
    box-shadow: var(--shadow-md) !important;
}

.navbar-no-shadow .modern-navbar {
    box-shadow: none !important;
}

/* Mobile Sidebar Overlay */
.mobile-sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@media (min-width: 992px) {
    .mobile-sidebar-overlay {
        display: none !important;
    }
}

/* Footer Styles */
.modern-footer {
    background: linear-gradient(135deg, var(--bg-primary), var(--bg-tertiary));
    border-top: 1px solid var(--border-color);
    margin-top: auto;
    padding: 2rem 0 1rem;
    color: var(--text-secondary);
}

.footer-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1.5rem;
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 2rem;
    margin-bottom: 1.5rem;
}

.footer-brand {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.footer-logo {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-inverse);
    font-size: 1.25rem;
}

.footer-title {
    font-family: var(--font-family-secondary);
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
}

.footer-subtitle {
    color: var(--text-muted);
    font-size: 0.75rem;
}

.footer-links {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.footer-link {
    color: var(--text-secondary);
    text-decoration: none;
    font-size: 0.9rem;
    transition: color var(--transition-fast);
}

.footer-link:hover {
    color: var(--primary-color);
}

.footer-info {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    text-align: right;
}

.footer-version, .footer-copyright {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    font-size: 0.85rem;
    color: var(--text-muted);
}

.footer-bottom {
    padding-top: 1.5rem;
    border-top: 1px solid var(--border-light);
}

.footer-stats {
    display: flex;
    justify-content: center;
    gap: 2rem;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.85rem;
    color: var(--text-muted);
}

.stat-item i {
    font-size: 1rem;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 1.5rem;
    }

    .footer-info {
        text-align: center;
    }

    .footer-version, .footer-copyright {
        justify-content: center;
    }

    .footer-stats {
        flex-direction: column;
        gap: 1rem;
    }
}
