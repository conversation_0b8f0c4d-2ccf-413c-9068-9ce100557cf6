<?php
require_once 'includes/config/database.php';
require_once 'includes/helpers/functions.php';
require_once 'includes/helpers/notifications.php';

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isLoggedIn()) {
    setFlashMessage('danger', 'Silakan login terlebih dahulu');
    redirect('/keuangan/login.php');
}

$currentUser = getCurrentUser();
$currentPage = 'investment';

// Create investment table if not exists
try {
    $pdo->exec("CREATE TABLE IF NOT EXISTS investments (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        kode_investasi VARCHAR(50) UNIQUE NOT NULL,
        nama_investasi VARCHAR(255) NOT NULL,
        jenis_investasi ENUM('saham', 'obligasi', 'reksadana', 'emas', 'properti', 'deposito', 'crypto', 'lainnya') NOT NULL,
        platform_broker VARCHAR(255),
        jumlah_unit DECIMAL(15,4) DEFAULT 0,
        harga_beli DECIMAL(15,2) NOT NULL,
        total_investasi DECIMAL(15,2) NOT NULL,
        harga_saat_ini DECIMAL(15,2) DEFAULT 0,
        nilai_saat_ini DECIMAL(15,2) DEFAULT 0,
        keuntungan_rugi DECIMAL(15,2) DEFAULT 0,
        persentase_return DECIMAL(8,4) DEFAULT 0,
        tanggal_beli DATE NOT NULL,
        tanggal_jatuh_tempo DATE NULL,
        status_investasi ENUM('aktif', 'dijual', 'jatuh_tempo') DEFAULT 'aktif',
        dividen_bunga DECIMAL(15,2) DEFAULT 0,
        biaya_admin DECIMAL(15,2) DEFAULT 0,
        pajak DECIMAL(15,2) DEFAULT 0,
        catatan TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    )");

    $pdo->exec("CREATE TABLE IF NOT EXISTS investment_transactions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        investment_id INT NOT NULL,
        jenis_transaksi ENUM('beli', 'jual', 'dividen', 'bunga', 'biaya') NOT NULL,
        jumlah_unit DECIMAL(15,4) DEFAULT 0,
        harga_per_unit DECIMAL(15,2) DEFAULT 0,
        total_transaksi DECIMAL(15,2) NOT NULL,
        tanggal_transaksi DATE NOT NULL,
        keterangan TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (investment_id) REFERENCES investments(id) ON DELETE CASCADE
    )");
} catch (PDOException $e) {
    error_log("Error creating investment tables: " . $e->getMessage());
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        try {
            switch ($_POST['action']) {
                case 'add':
                    $errors = [];
                    
                    if (empty($_POST['nama_investasi'])) {
                        $errors[] = 'Nama investasi harus diisi';
                    }
                    
                    if (empty($_POST['jenis_investasi'])) {
                        $errors[] = 'Jenis investasi harus dipilih';
                    }
                    
                    if (empty($_POST['harga_beli']) || $_POST['harga_beli'] <= 0) {
                        $errors[] = 'Harga beli harus diisi dan lebih dari 0';
                    }
                    
                    if (empty($errors)) {
                        // Generate kode investasi
                        $prefix = strtoupper(substr($_POST['jenis_investasi'], 0, 3));
                        $kode_investasi = $prefix . date('Ymd') . sprintf('%04d', rand(1, 9999));
                        
                        // Format numbers
                        $jumlah_unit = !empty($_POST['jumlah_unit']) ? str_replace(['.', ','], '', $_POST['jumlah_unit']) : 1;
                        $harga_beli = str_replace(['.', ','], '', $_POST['harga_beli']);
                        $total_investasi = $jumlah_unit * $harga_beli;
                        $biaya_admin = !empty($_POST['biaya_admin']) ? str_replace(['.', ','], '', $_POST['biaya_admin']) : 0;
                        
                        $stmt = $pdo->prepare("INSERT INTO investments (user_id, kode_investasi, nama_investasi, jenis_investasi, platform_broker, jumlah_unit, harga_beli, total_investasi, harga_saat_ini, nilai_saat_ini, tanggal_beli, tanggal_jatuh_tempo, biaya_admin, catatan) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
                        
                        $result = $stmt->execute([
                            $currentUser['id'],
                            $kode_investasi,
                            $_POST['nama_investasi'],
                            $_POST['jenis_investasi'],
                            $_POST['platform_broker'],
                            $jumlah_unit,
                            $harga_beli,
                            $total_investasi,
                            $harga_beli, // harga_saat_ini default sama dengan harga_beli
                            $total_investasi, // nilai_saat_ini default sama dengan total_investasi
                            $_POST['tanggal_beli'],
                            !empty($_POST['tanggal_jatuh_tempo']) ? $_POST['tanggal_jatuh_tempo'] : null,
                            $biaya_admin,
                            $_POST['catatan']
                        ]);
                        
                        if ($result) {
                            setFlashMessage('success', 'Investasi berhasil ditambahkan');
                        } else {
                            setFlashMessage('danger', 'Gagal menambahkan investasi');
                        }
                    }
                    
                    if (!empty($errors)) {
                        setFlashMessage('danger', implode('<br>', $errors));
                    }
                    break;

                case 'update_price':
                    if (!empty($_POST['id']) && !empty($_POST['harga_saat_ini'])) {
                        $harga_saat_ini = str_replace(['.', ','], '', $_POST['harga_saat_ini']);
                        
                        // Get investment data
                        $stmt = $pdo->prepare("SELECT jumlah_unit, harga_beli, total_investasi FROM investments WHERE id = ? AND user_id = ?");
                        $stmt->execute([$_POST['id'], $currentUser['id']]);
                        $investment = $stmt->fetch();
                        
                        if ($investment) {
                            $nilai_saat_ini = $investment['jumlah_unit'] * $harga_saat_ini;
                            $keuntungan_rugi = $nilai_saat_ini - $investment['total_investasi'];
                            $persentase_return = ($keuntungan_rugi / $investment['total_investasi']) * 100;
                            
                            $stmt = $pdo->prepare("UPDATE investments SET harga_saat_ini = ?, nilai_saat_ini = ?, keuntungan_rugi = ?, persentase_return = ? WHERE id = ? AND user_id = ?");
                            $result = $stmt->execute([$harga_saat_ini, $nilai_saat_ini, $keuntungan_rugi, $persentase_return, $_POST['id'], $currentUser['id']]);
                            
                            if ($result) {
                                setFlashMessage('success', 'Harga investasi berhasil diperbarui');
                            } else {
                                setFlashMessage('danger', 'Gagal memperbarui harga investasi');
                            }
                        }
                    }
                    break;

                case 'delete':
                    if (!empty($_POST['id'])) {
                        $stmt = $pdo->prepare("DELETE FROM investments WHERE id = ? AND user_id = ?");
                        $result = $stmt->execute([$_POST['id'], $currentUser['id']]);
                        
                        if ($result) {
                            setFlashMessage('success', 'Investasi berhasil dihapus');
                        } else {
                            setFlashMessage('danger', 'Gagal menghapus investasi');
                        }
                    }
                    break;
            }
        } catch (PDOException $e) {
            setFlashMessage('danger', 'Terjadi kesalahan. Silakan coba lagi.');
        }
        redirect('/keuangan/investment.php');
    }
}

// Get investments with pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$perPage = 15;
$offset = ($page - 1) * $perPage;

$where = ["user_id = ?"];
$params = [$currentUser['id']];

if (!empty($_GET['jenis'])) {
    $where[] = "jenis_investasi = ?";
    $params[] = $_GET['jenis'];
}

if (!empty($_GET['status'])) {
    $where[] = "status_investasi = ?";
    $params[] = $_GET['status'];
}

if (!empty($_GET['search'])) {
    $where[] = "(nama_investasi LIKE ? OR kode_investasi LIKE ?)";
    $searchTerm = '%' . $_GET['search'] . '%';
    $params[] = $searchTerm;
    $params[] = $searchTerm;
}

$whereClause = implode(" AND ", $where);

// Get total records
$stmt = $pdo->prepare("SELECT COUNT(*) as total FROM investments WHERE $whereClause");
$stmt->execute($params);
$totalRecords = $stmt->fetch()['total'];
$totalPages = ceil($totalRecords / $perPage);

// Get investments
$stmt = $pdo->prepare("
    SELECT * FROM investments 
    WHERE $whereClause 
    ORDER BY tanggal_beli DESC, created_at DESC 
    LIMIT ? OFFSET ?
");

$paginationParams = $params;
$paginationParams[] = $perPage;
$paginationParams[] = $offset;

$stmt->execute($paginationParams);
$investments = $stmt->fetchAll();

// Get statistics
$stmt = $pdo->prepare("
    SELECT 
        COUNT(*) as total_investments,
        COUNT(CASE WHEN status_investasi = 'aktif' THEN 1 END) as aktif,
        COUNT(CASE WHEN status_investasi = 'dijual' THEN 1 END) as dijual,
        SUM(CASE WHEN status_investasi = 'aktif' THEN total_investasi ELSE 0 END) as total_modal,
        SUM(CASE WHEN status_investasi = 'aktif' THEN nilai_saat_ini ELSE 0 END) as total_nilai_sekarang,
        SUM(CASE WHEN status_investasi = 'aktif' THEN keuntungan_rugi ELSE 0 END) as total_keuntungan_rugi,
        SUM(dividen_bunga) as total_dividen
    FROM investments 
    WHERE user_id = ?
");
$stmt->execute([$currentUser['id']]);
$stats = $stmt->fetch();

// Calculate overall return percentage
$overall_return = 0;
if ($stats['total_modal'] > 0) {
    $overall_return = ($stats['total_keuntungan_rugi'] / $stats['total_modal']) * 100;
}

// Include header and sidebar
require_once 'includes/views/layouts/header.php';
require_once 'includes/views/layouts/sidebar.php';
?>

<div class="modern-layout">
    <div class="modern-container">
        <!-- Modern Page Header -->
        <div class="modern-page-header modern-flex modern-justify-between modern-items-center modern-mb-xl">
            <div>
                <h1 class="modern-page-title modern-mb-0">Investment Portfolio</h1>
                <p class="modern-page-subtitle">Kelola dan pantau portofolio investasi Anda dengan analisis mendalam</p>
            </div>
            <div class="modern-page-actions">
                <button type="button" class="modern-btn modern-btn-success" onclick="updateAllPrices()">
                    <i class="fas fa-sync"></i>
                    Update Harga
                </button>
                <button type="button" class="modern-btn modern-btn-primary" data-bs-toggle="modal" data-bs-target="#addInvestmentModal">
                    <i class="fas fa-plus"></i>
                    Tambah Investasi
                </button>
            </div>
        </div>

        <!-- Flash Messages -->
        <?php if ($flash = getFlashMessage()): ?>
        <div class="modern-alert modern-alert-<?= $flash['type'] ?> modern-mb-lg">
            <div class="modern-alert-content">
                <i class="fas fa-<?= $flash['type'] === 'success' ? 'check-circle' : 'exclamation-triangle' ?> modern-alert-icon"></i>
                <div class="modern-alert-message"><?= $flash['message'] ?></div>
            </div>
            <button type="button" class="modern-alert-close" onclick="this.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <?php endif; ?>

        <!-- Modern Statistics Cards -->
        <div class="modern-grid modern-grid-cols-4 modern-gap-lg modern-mb-xl">
            <div class="modern-stats-card modern-stats-primary">
                <div class="modern-stats-content">
                    <div class="modern-stats-info">
                        <div class="modern-stats-label">Total Investasi</div>
                        <div class="modern-stats-value"><?= number_format($stats['total_investments'] ?? 0) ?></div>
                        <div class="modern-stats-meta"><?= $stats['aktif'] ?? 0 ?> aktif</div>
                    </div>
                    <div class="modern-stats-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                </div>
                <div class="modern-stats-glow"></div>
            </div>

            <div class="modern-stats-card modern-stats-info">
                <div class="modern-stats-content">
                    <div class="modern-stats-info">
                        <div class="modern-stats-label">Total Modal</div>
                        <div class="modern-stats-value"><?= formatRupiahShort($stats['total_modal'] ?? 0) ?></div>
                        <div class="modern-stats-meta">💰 Modal investasi</div>
                    </div>
                    <div class="modern-stats-icon">
                        <i class="fas fa-money-bill-wave"></i>
                    </div>
                </div>
                <div class="modern-stats-glow"></div>
            </div>

            <div class="modern-stats-card modern-stats-<?= ($stats['total_keuntungan_rugi'] ?? 0) >= 0 ? 'success' : 'danger' ?>">
                <div class="modern-stats-content">
                    <div class="modern-stats-info">
                        <div class="modern-stats-label">P&L</div>
                        <div class="modern-stats-value"><?= formatRupiahShort($stats['total_keuntungan_rugi'] ?? 0) ?></div>
                        <div class="modern-stats-meta"><?= ($stats['total_keuntungan_rugi'] ?? 0) >= 0 ? '📈' : '📉' ?> <?= number_format($overall_return, 2) ?>%</div>
                    </div>
                    <div class="modern-stats-icon">
                        <i class="fas fa-<?= ($stats['total_keuntungan_rugi'] ?? 0) >= 0 ? 'arrow-up' : 'arrow-down' ?>"></i>
                    </div>
                </div>
                <div class="modern-stats-glow"></div>
            </div>

            <div class="modern-stats-card modern-stats-warning">
                <div class="modern-stats-content">
                    <div class="modern-stats-info">
                        <div class="modern-stats-label">Nilai Sekarang</div>
                        <div class="modern-stats-value"><?= formatRupiahShort($stats['total_nilai_sekarang'] ?? 0) ?></div>
                        <div class="modern-stats-meta">💎 Nilai portofolio</div>
                    </div>
                    <div class="modern-stats-icon">
                        <i class="fas fa-gem"></i>
                    </div>
                </div>
                <div class="modern-stats-glow"></div>
            </div>
        </div>
