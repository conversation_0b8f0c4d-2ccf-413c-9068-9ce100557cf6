<?php
require_once 'includes/config/database.php';

try {
    // Cek apakah tabel users ada
    $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
    if ($stmt->rowCount() == 0) {
        // Buat tabel users jika belum ada
        $pdo->exec("CREATE TABLE IF NOT EXISTS users (
            id INT PRIMARY KEY AUTO_INCREMENT,
            nama VARCHAR(100) NOT NULL,
            email VARCHAR(100) NOT NULL UNIQUE,
            password VARCHAR(255) NOT NULL,
            role ENUM('admin', 'user') DEFAULT 'user',
            status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
            last_login TIMESTAMP NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
        echo "Tabel users berhasil dibuat!<br>";
    } else {
        echo "Tabel users sudah ada.<br>";
    }

    // Tampilkan struktur tabel
    $stmt = $pdo->query("DESCRIBE users");
    echo "<h3>Struktur Tabel Users:</h3>";
    echo "<pre>";
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        print_r($row);
    }
    echo "</pre>";

    // Cek apakah ada user admin default
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM users WHERE email = '<EMAIL>'");
    $result = $stmt->fetch();
    if ($result['count'] == 0) {
        // Buat user admin default
        $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("INSERT INTO users (nama, email, password, role, status) 
                              VALUES (?, ?, ?, 'admin', 'active')");
        $stmt->execute(['Administrator', '<EMAIL>', $admin_password]);
        echo "User admin default berhasil dibuat!<br>";
    } else {
        echo "User admin default sudah ada.<br>";
    }

    echo "<br>Pemeriksaan selesai! Silakan <a href='register.php'>kembali ke halaman register</a>.";
} catch(PDOException $e) {
    die("Error: " . $e->getMessage());
}
?> 