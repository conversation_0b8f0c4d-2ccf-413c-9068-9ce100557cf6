<?php
require_once 'includes/config/database.php';
require_once 'includes/helpers/functions.php';

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

$success = false;
$message = '';

if (isset($_GET['token'])) {
    $token = cleanInput($_GET['token']);
    
    try {
        // Cek token di database
        $stmt = $pdo->prepare("
            SELECT id, email_verified 
            FROM users 
            WHERE verification_token = ? AND email_verified = 0
        ");
        $stmt->execute([$token]);
        $user = $stmt->fetch();
        
        if ($user) {
            // Update status verifikasi
            $updateStmt = $pdo->prepare("
                UPDATE users 
                SET email_verified = 1, 
                    verification_token = NULL,
                    status = 'active'
                WHERE id = ?
            ");
            
            if ($updateStmt->execute([$user['id']])) {
                $success = true;
                $message = 'Email berhasil diverifikasi! <PERSON>lakan login untuk melanjutkan.';
                
                // Log aktivitas
                logActivity($user['id'], 'Email verified');
                
                // Set flash message
                setFlashMessage('success', $message);
                
                // Redirect ke halaman login
                redirect('login.php');
            } else {
                $message = 'Gagal memverifikasi email. Silakan coba lagi.';
            }
        } else {
            $message = 'Token verifikasi tidak valid atau sudah digunakan.';
        }
    } catch (PDOException $e) {
        error_log("Email Verification Error: " . $e->getMessage());
        $message = 'Terjadi kesalahan sistem. Silakan coba beberapa saat lagi.';
    }
} else {
    $message = 'Token verifikasi tidak ditemukan.';
}
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Verifikasi Email - Sistem Keuangan</title>
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container">
        <div class="row justify-content-center mt-5">
            <div class="col-md-6">
                <div class="card shadow">
                    <div class="card-body p-5 text-center">
                        <?php if ($success): ?>
                            <i class="fas fa-check-circle text-success fa-4x mb-4"></i>
                        <?php else: ?>
                            <i class="fas fa-exclamation-circle text-danger fa-4x mb-4"></i>
                        <?php endif; ?>
                        
                        <h3 class="mb-4"><?= $success ? 'Verifikasi Berhasil' : 'Verifikasi Gagal' ?></h3>
                        <p class="mb-4"><?= $message ?></p>
                        
                        <div class="mt-4">
                            <a href="login.php" class="btn btn-primary">Kembali ke Login</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 