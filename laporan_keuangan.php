<?php
require_once 'includes/config/database.php';
require_once 'includes/helpers/functions.php';

$currentUser = getCurrentUser();
if (!$currentUser) {
    setFlashMessage('danger', 'Sesi tidak valid. Silakan login kembali.');
    redirect('login.php');
}

$currentPage = 'laporan_keuangan';
$pageTitle = '<PERSON><PERSON><PERSON>';

// Get filter parameters
$startDate = $_GET['start_date'] ?? date('Y-m-01');
$endDate = $_GET['end_date'] ?? date('Y-m-t');
$reportType = $_GET['report_type'] ?? 'summary';

// Get financial data
try {
    // Income and expense summary
    $stmt = $pdo->prepare("
        SELECT 
            SUM(CASE WHEN type = 'income' THEN amount ELSE 0 END) as total_income,
            SUM(CASE WHEN type = 'expense' THEN amount ELSE 0 END) as total_expense,
            COUNT(CASE WHEN type = 'income' THEN 1 END) as income_count,
            COUNT(CASE WHEN type = 'expense' THEN 1 END) as expense_count
        FROM transactions 
        WHERE user_id = ? AND transaction_date BETWEEN ? AND ?
    ");
    $stmt->execute([$currentUser['id'], $startDate, $endDate]);
    $summary = $stmt->fetch();
    
    $netIncome = ($summary['total_income'] ?? 0) - ($summary['total_expense'] ?? 0);
    
    // Income by category
    $stmt = $pdo->prepare("
        SELECT 
            c.name as category_name,
            c.color as category_color,
            SUM(t.amount) as total_amount,
            COUNT(t.id) as transaction_count
        FROM transactions t
        LEFT JOIN categories c ON t.category_id = c.id
        WHERE t.user_id = ? AND t.type = 'income' AND t.transaction_date BETWEEN ? AND ?
        GROUP BY c.id, c.name, c.color
        ORDER BY total_amount DESC
    ");
    $stmt->execute([$currentUser['id'], $startDate, $endDate]);
    $incomeByCategory = $stmt->fetchAll();
    
    // Expense by category
    $stmt = $pdo->prepare("
        SELECT 
            c.name as category_name,
            c.color as category_color,
            SUM(t.amount) as total_amount,
            COUNT(t.id) as transaction_count
        FROM transactions t
        LEFT JOIN categories c ON t.category_id = c.id
        WHERE t.user_id = ? AND t.type = 'expense' AND t.transaction_date BETWEEN ? AND ?
        GROUP BY c.id, c.name, c.color
        ORDER BY total_amount DESC
    ");
    $stmt->execute([$currentUser['id'], $startDate, $endDate]);
    $expenseByCategory = $stmt->fetchAll();
    
    // Daily transactions for chart
    $stmt = $pdo->prepare("
        SELECT 
            transaction_date,
            SUM(CASE WHEN type = 'income' THEN amount ELSE 0 END) as daily_income,
            SUM(CASE WHEN type = 'expense' THEN amount ELSE 0 END) as daily_expense
        FROM transactions 
        WHERE user_id = ? AND transaction_date BETWEEN ? AND ?
        GROUP BY transaction_date
        ORDER BY transaction_date
    ");
    $stmt->execute([$currentUser['id'], $startDate, $endDate]);
    $dailyData = $stmt->fetchAll();
    
    // Monthly comparison (last 6 months)
    $stmt = $pdo->prepare("
        SELECT 
            DATE_FORMAT(transaction_date, '%Y-%m') as month,
            SUM(CASE WHEN type = 'income' THEN amount ELSE 0 END) as monthly_income,
            SUM(CASE WHEN type = 'expense' THEN amount ELSE 0 END) as monthly_expense
        FROM transactions 
        WHERE user_id = ? AND transaction_date >= DATE_SUB(CURDATE(), INTERVAL 6 MONTH)
        GROUP BY DATE_FORMAT(transaction_date, '%Y-%m')
        ORDER BY month
    ");
    $stmt->execute([$currentUser['id']]);
    $monthlyData = $stmt->fetchAll();
    
} catch (PDOException $e) {
    error_log("Error getting financial data: " . $e->getMessage());
    $summary = ['total_income' => 0, 'total_expense' => 0, 'income_count' => 0, 'expense_count' => 0];
    $netIncome = 0;
    $incomeByCategory = [];
    $expenseByCategory = [];
    $dailyData = [];
    $monthlyData = [];
}

include 'includes/views/layouts/header.php';
?>

<div class="container-fluid px-4 py-3">
    <div class="row">
        <!-- Page Header -->
        <div class="col-12 mb-4">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-1">Laporan Keuangan</h1>
                    <p class="text-muted mb-0">Analisis dan ringkasan keuangan Anda</p>
                </div>
                <div class="btn-group">
                    <button type="button" class="btn btn-outline-primary" onclick="exportReport('pdf')">
                        <i class="fas fa-file-pdf me-2"></i>Export PDF
                    </button>
                    <button type="button" class="btn btn-outline-success" onclick="exportReport('excel')">
                        <i class="fas fa-file-excel me-2"></i>Export Excel
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Filter Section -->
        <div class="col-12 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label class="form-label">Tanggal Mulai</label>
                            <input type="date" name="start_date" class="form-control" value="<?= $startDate ?>">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Tanggal Akhir</label>
                            <input type="date" name="end_date" class="form-control" value="<?= $endDate ?>">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Jenis Laporan</label>
                            <select name="report_type" class="form-select">
                                <option value="summary" <?= $reportType === 'summary' ? 'selected' : '' ?>>Ringkasan</option>
                                <option value="detailed" <?= $reportType === 'detailed' ? 'selected' : '' ?>>Detail</option>
                                <option value="comparison" <?= $reportType === 'comparison' ? 'selected' : '' ?>>Perbandingan</option>
                            </select>
                        </div>
                        <div class="col-md-3 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-search me-2"></i>Generate Laporan
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- Summary Cards -->
        <div class="col-12 mb-4">
            <div class="row">
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <h4 class="mb-1">Rp <?= number_format($summary['total_income'] ?? 0, 0, ',', '.') ?></h4>
                            <small>Total Pemasukan</small>
                            <div class="mt-2">
                                <small><?= $summary['income_count'] ?? 0 ?> transaksi</small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-danger text-white">
                        <div class="card-body text-center">
                            <h4 class="mb-1">Rp <?= number_format($summary['total_expense'] ?? 0, 0, ',', '.') ?></h4>
                            <small>Total Pengeluaran</small>
                            <div class="mt-2">
                                <small><?= $summary['expense_count'] ?? 0 ?> transaksi</small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-<?= $netIncome >= 0 ? 'info' : 'warning' ?> text-white">
                        <div class="card-body text-center">
                            <h4 class="mb-1">Rp <?= number_format($netIncome, 0, ',', '.') ?></h4>
                            <small>Saldo Bersih</small>
                            <div class="mt-2">
                                <small><?= $netIncome >= 0 ? 'Surplus' : 'Defisit' ?></small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-secondary text-white">
                        <div class="card-body text-center">
                            <h4 class="mb-1"><?= ($summary['income_count'] ?? 0) + ($summary['expense_count'] ?? 0) ?></h4>
                            <small>Total Transaksi</small>
                            <div class="mt-2">
                                <small>Periode: <?= date('d/m/Y', strtotime($startDate)) ?> - <?= date('d/m/Y', strtotime($endDate)) ?></small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Charts Section -->
        <div class="col-12 mb-4">
            <div class="row">
                <!-- Income by Category -->
                <div class="col-md-6">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-success text-white">
                            <h6 class="mb-0">
                                <i class="fas fa-chart-pie me-2"></i>Pemasukan per Kategori
                            </h6>
                        </div>
                        <div class="card-body">
                            <?php if (empty($incomeByCategory)): ?>
                                <div class="text-center py-4">
                                    <i class="fas fa-chart-pie fa-2x text-muted mb-2"></i>
                                    <p class="text-muted mb-0">Tidak ada data pemasukan</p>
                                </div>
                            <?php else: ?>
                                <canvas id="incomeChart" width="400" height="300"></canvas>
                                <div class="mt-3">
                                    <?php foreach ($incomeByCategory as $income): ?>
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <div class="d-flex align-items-center">
                                                <div class="me-2" style="width: 12px; height: 12px; background-color: <?= $income['category_color'] ?? '#007bff' ?>; border-radius: 2px;"></div>
                                                <small><?= htmlspecialchars($income['category_name'] ?? 'Tanpa Kategori') ?></small>
                                            </div>
                                            <small class="fw-bold">Rp <?= number_format($income['total_amount'], 0, ',', '.') ?></small>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                
                <!-- Expense by Category -->
                <div class="col-md-6">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-danger text-white">
                            <h6 class="mb-0">
                                <i class="fas fa-chart-pie me-2"></i>Pengeluaran per Kategori
                            </h6>
                        </div>
                        <div class="card-body">
                            <?php if (empty($expenseByCategory)): ?>
                                <div class="text-center py-4">
                                    <i class="fas fa-chart-pie fa-2x text-muted mb-2"></i>
                                    <p class="text-muted mb-0">Tidak ada data pengeluaran</p>
                                </div>
                            <?php else: ?>
                                <canvas id="expenseChart" width="400" height="300"></canvas>
                                <div class="mt-3">
                                    <?php foreach ($expenseByCategory as $expense): ?>
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <div class="d-flex align-items-center">
                                                <div class="me-2" style="width: 12px; height: 12px; background-color: <?= $expense['category_color'] ?? '#dc3545' ?>; border-radius: 2px;"></div>
                                                <small><?= htmlspecialchars($expense['category_name'] ?? 'Tanpa Kategori') ?></small>
                                            </div>
                                            <small class="fw-bold">Rp <?= number_format($expense['total_amount'], 0, ',', '.') ?></small>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Trend Chart -->
        <div class="col-12 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-chart-line me-2"></i>Tren Keuangan Harian
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (empty($dailyData)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-chart-line fa-2x text-muted mb-2"></i>
                            <p class="text-muted mb-0">Tidak ada data untuk periode ini</p>
                        </div>
                    <?php else: ?>
                        <canvas id="trendChart" width="400" height="200"></canvas>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- Monthly Comparison -->
        <?php if (!empty($monthlyData)): ?>
        <div class="col-12 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>Perbandingan Bulanan (6 Bulan Terakhir)
                    </h6>
                </div>
                <div class="card-body">
                    <canvas id="monthlyChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
        <?php endif; ?>
        
        <!-- Detailed Tables -->
        <?php if ($reportType === 'detailed'): ?>
        <div class="col-12">
            <div class="row">
                <!-- Top Income Categories -->
                <div class="col-md-6">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-success text-white">
                            <h6 class="mb-0">Detail Pemasukan per Kategori</h6>
                        </div>
                        <div class="card-body p-0">
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead class="table-light">
                                        <tr>
                                            <th>Kategori</th>
                                            <th class="text-end">Jumlah</th>
                                            <th class="text-center">Transaksi</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($incomeByCategory as $income): ?>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="me-2" style="width: 8px; height: 8px; background-color: <?= $income['category_color'] ?? '#007bff' ?>; border-radius: 50%;"></div>
                                                    <?= htmlspecialchars($income['category_name'] ?? 'Tanpa Kategori') ?>
                                                </div>
                                            </td>
                                            <td class="text-end fw-bold text-success">
                                                Rp <?= number_format($income['total_amount'], 0, ',', '.') ?>
                                            </td>
                                            <td class="text-center">
                                                <span class="badge bg-success"><?= $income['transaction_count'] ?></span>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Top Expense Categories -->
                <div class="col-md-6">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-danger text-white">
                            <h6 class="mb-0">Detail Pengeluaran per Kategori</h6>
                        </div>
                        <div class="card-body p-0">
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead class="table-light">
                                        <tr>
                                            <th>Kategori</th>
                                            <th class="text-end">Jumlah</th>
                                            <th class="text-center">Transaksi</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($expenseByCategory as $expense): ?>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="me-2" style="width: 8px; height: 8px; background-color: <?= $expense['category_color'] ?? '#dc3545' ?>; border-radius: 50%;"></div>
                                                    <?= htmlspecialchars($expense['category_name'] ?? 'Tanpa Kategori') ?>
                                                </div>
                                            </td>
                                            <td class="text-end fw-bold text-danger">
                                                Rp <?= number_format($expense['total_amount'], 0, ',', '.') ?>
                                            </td>
                                            <td class="text-center">
                                                <span class="badge bg-danger"><?= $expense['transaction_count'] ?></span>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>
</div>

<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
// Income Pie Chart
<?php if (!empty($incomeByCategory)): ?>
const incomeCtx = document.getElementById('incomeChart').getContext('2d');
new Chart(incomeCtx, {
    type: 'doughnut',
    data: {
        labels: <?= json_encode(array_column($incomeByCategory, 'category_name')) ?>,
        datasets: [{
            data: <?= json_encode(array_column($incomeByCategory, 'total_amount')) ?>,
            backgroundColor: <?= json_encode(array_column($incomeByCategory, 'category_color')) ?>,
            borderWidth: 2,
            borderColor: '#fff'
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        }
    }
});
<?php endif; ?>

// Expense Pie Chart
<?php if (!empty($expenseByCategory)): ?>
const expenseCtx = document.getElementById('expenseChart').getContext('2d');
new Chart(expenseCtx, {
    type: 'doughnut',
    data: {
        labels: <?= json_encode(array_column($expenseByCategory, 'category_name')) ?>,
        datasets: [{
            data: <?= json_encode(array_column($expenseByCategory, 'total_amount')) ?>,
            backgroundColor: <?= json_encode(array_column($expenseByCategory, 'category_color')) ?>,
            borderWidth: 2,
            borderColor: '#fff'
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        }
    }
});
<?php endif; ?>

// Daily Trend Chart
<?php if (!empty($dailyData)): ?>
const trendCtx = document.getElementById('trendChart').getContext('2d');
new Chart(trendCtx, {
    type: 'line',
    data: {
        labels: <?= json_encode(array_map(function($d) { return date('d/m', strtotime($d['transaction_date'])); }, $dailyData)) ?>,
        datasets: [{
            label: 'Pemasukan',
            data: <?= json_encode(array_column($dailyData, 'daily_income')) ?>,
            borderColor: '#28a745',
            backgroundColor: 'rgba(40, 167, 69, 0.1)',
            tension: 0.4
        }, {
            label: 'Pengeluaran',
            data: <?= json_encode(array_column($dailyData, 'daily_expense')) ?>,
            borderColor: '#dc3545',
            backgroundColor: 'rgba(220, 53, 69, 0.1)',
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return 'Rp ' + value.toLocaleString('id-ID');
                    }
                }
            }
        },
        plugins: {
            tooltip: {
                callbacks: {
                    label: function(context) {
                        return context.dataset.label + ': Rp ' + context.parsed.y.toLocaleString('id-ID');
                    }
                }
            }
        }
    }
});
<?php endif; ?>

// Monthly Comparison Chart
<?php if (!empty($monthlyData)): ?>
const monthlyCtx = document.getElementById('monthlyChart').getContext('2d');
new Chart(monthlyCtx, {
    type: 'bar',
    data: {
        labels: <?= json_encode(array_map(function($d) { return date('M Y', strtotime($d['month'] . '-01')); }, $monthlyData)) ?>,
        datasets: [{
            label: 'Pemasukan',
            data: <?= json_encode(array_column($monthlyData, 'monthly_income')) ?>,
            backgroundColor: '#28a745'
        }, {
            label: 'Pengeluaran',
            data: <?= json_encode(array_column($monthlyData, 'monthly_expense')) ?>,
            backgroundColor: '#dc3545'
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return 'Rp ' + value.toLocaleString('id-ID');
                    }
                }
            }
        },
        plugins: {
            tooltip: {
                callbacks: {
                    label: function(context) {
                        return context.dataset.label + ': Rp ' + context.parsed.y.toLocaleString('id-ID');
                    }
                }
            }
        }
    }
});
<?php endif; ?>

// Export functions
function exportReport(format) {
    const params = new URLSearchParams(window.location.search);
    params.set('export', format);
    
    // Create a temporary link to download
    const link = document.createElement('a');
    link.href = 'export_report.php?' + params.toString();
    link.download = `laporan_keuangan_${format}.${format === 'pdf' ? 'pdf' : 'xlsx'}`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}
</script>

<?php include 'includes/views/layouts/footer.php'; ?>
