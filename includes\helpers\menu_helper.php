<?php
/**
 * Menu Helper Functions - Simple and Working Version
 * Handles menu permissions and visibility based on user roles
 */

// Include database connection
require_once __DIR__ . '/../config/database.php';

/**
 * Get allowed menus for a specific role
 */
function getAllowedMenusForRole($roleId) {
    global $pdo;
    try {
        $stmt = $pdo->prepare("SELECT menu_id FROM role_menu_access WHERE role_id = ?");
        $stmt->execute([$roleId]);
        return $stmt->fetchAll(PDO::FETCH_COLUMN);
    } catch (PDOException $e) {
        error_log("Error getting allowed menus: " . $e->getMessage());
        return [];
    }
}

/**
 * Get role ID by role name
 */
function getRoleId($roleName) {
    global $pdo;
    try {
        $stmt = $pdo->prepare("SELECT id FROM roles WHERE name = ?");
        $stmt->execute([$roleName]);
        return $stmt->fetchColumn();
    } catch (PDOException $e) {
        error_log("Error getting role ID: " . $e->getMessage());
        return false;
    }
}

/**
 * Check if user has access to a specific menu
 */
function hasMenuAccess($menuId, $userRole = null) {
    global $pdo;

    // If no role specified, get current user's role
    if ($userRole === null) {
        $currentUser = getCurrentUser();
        if (!$currentUser) {
            return false;
        }
        $userRole = $currentUser['role'];
    }

    // Admin has access to everything
    if ($userRole === 'admin') {
        return true;
    }

    // Get role ID and check permissions
    $roleId = getRoleId($userRole);
    if (!$roleId) {
        return false;
    }

    // Check if menu is allowed for this role
    try {
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM role_menu_access WHERE role_id = ? AND menu_id = ?");
        $stmt->execute([$roleId, $menuId]);
        return $stmt->fetchColumn() > 0;
    } catch (PDOException $e) {
        error_log("Error checking menu access: " . $e->getMessage());
        return false;
    }
}

/**
 * Get user's allowed menus from database (with user-specific permissions)
 */
function getUserAllowedMenus($userRole = null, $userId = null) {
    global $pdo;

    // If no role specified, get current user's role and ID
    if ($userRole === null || $userId === null) {
        $currentUser = getCurrentUser();
        if (!$currentUser) {
            return [];
        }
        $userRole = $currentUser['role'];
        $userId = $currentUser['id'];
    }

    // Check if user has specific menu permissions
    try {
        $stmt = $pdo->prepare("SELECT menu_id FROM user_menu_permissions WHERE user_id = ?");
        $stmt->execute([$userId]);
        $userSpecificMenus = $stmt->fetchAll(PDO::FETCH_COLUMN);

        // If user has specific permissions, use those
        if (!empty($userSpecificMenus)) {
            return $userSpecificMenus;
        }
    } catch (PDOException $e) {
        error_log("Error getting user specific menus: " . $e->getMessage());
    }

    // Fallback to role-based permissions
    // Admin has access to everything
    if ($userRole === 'admin') {
        return 'all'; // Special flag for admin
    }

    // Get role ID and allowed menus
    $roleId = getRoleId($userRole);
    if (!$roleId) {
        return [];
    }

    return getAllowedMenusForRole($roleId);
}

/**
 * Check if specific user has access to a menu
 */
function userHasMenuAccess($menuId, $userId) {
    global $pdo;

    try {
        // Check user-specific permissions first
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM user_menu_permissions WHERE user_id = ? AND menu_id = ?");
        $stmt->execute([$userId, $menuId]);

        if ($stmt->fetchColumn() > 0) {
            return true;
        }

        // Check if user has any specific permissions at all
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM user_menu_permissions WHERE user_id = ?");
        $stmt->execute([$userId]);

        // If user has specific permissions but this menu is not included, deny access
        if ($stmt->fetchColumn() > 0) {
            return false;
        }

        // Fallback to role-based permissions
        $stmt = $pdo->prepare("SELECT role FROM users WHERE id = ?");
        $stmt->execute([$userId]);
        $userRole = $stmt->fetchColumn();

        return hasMenuAccess($menuId, $userRole);

    } catch (PDOException $e) {
        error_log("Error checking user menu access: " . $e->getMessage());
        return false;
    }
}

/**
 * Filter sidebar menus based on user permissions (with user-specific support)
 */
function getFilteredSidebarMenus($userRole = null, $userId = null) {
    // Get user's allowed menus (including user-specific permissions)
    $allowedMenus = getUserAllowedMenus($userRole, $userId);

    // Define all complete menus and submenus
    $allMenus = [
        ['id' => 'dashboard', 'label' => 'Dashboard', 'icon' => 'fas fa-home', 'url' => '/keuangan/dashboard.php'],
        ['id' => 'admin_dashboard', 'label' => 'Admin Dashboard', 'icon' => 'fas fa-tachometer-alt', 'url' => '/keuangan/admin-dashboard.php'],
        ['id' => 'keuangan', 'label' => 'Keuangan', 'icon' => 'fas fa-money-bill-wave', 'submenu' => [
            ['id' => 'transaksi', 'label' => 'Transaksi', 'icon' => 'fas fa-exchange-alt', 'url' => '/keuangan/transaksi.php'],
            ['id' => 'kategori', 'label' => 'Kategori', 'icon' => 'fas fa-tags', 'url' => '/keuangan/kategori.php'],
            ['id' => 'target', 'label' => 'Target', 'icon' => 'fas fa-bullseye', 'url' => '/keuangan/target.php'],
            ['id' => 'anggaran', 'label' => 'Anggaran', 'icon' => 'fas fa-chart-pie', 'url' => '/keuangan/anggaran.php'],
            ['id' => 'investasi', 'label' => 'Investasi', 'icon' => 'fas fa-chart-line', 'url' => '/keuangan/investasi.php'],
            ['id' => 'hutang', 'label' => 'Hutang & Piutang', 'icon' => 'fas fa-hand-holding-usd', 'url' => '/keuangan/hutang.php'],
        ]],
        ['id' => 'bisnis', 'label' => 'Bisnis', 'icon' => 'fas fa-store', 'submenu' => [
            ['id' => 'produk', 'label' => 'Produk', 'icon' => 'fas fa-box', 'url' => '/keuangan/produk.php'],
            ['id' => 'penjualan', 'label' => 'Penjualan', 'icon' => 'fas fa-shopping-cart', 'url' => '/keuangan/penjualan.php'],
            ['id' => 'pembelian', 'label' => 'Pembelian', 'icon' => 'fas fa-shopping-basket', 'url' => '/keuangan/pembelian.php'],
            ['id' => 'supplier', 'label' => 'Supplier', 'icon' => 'fas fa-truck', 'url' => '/keuangan/supplier.php'],
            ['id' => 'inventory', 'label' => 'Inventory', 'icon' => 'fas fa-warehouse', 'url' => '/keuangan/inventory.php'],
            ['id' => 'retur', 'label' => 'Retur', 'icon' => 'fas fa-undo', 'url' => '/keuangan/return.php'],
        ]],
        ['id' => 'laporan', 'label' => 'Laporan', 'icon' => 'fas fa-chart-bar', 'submenu' => [
            ['id' => 'laporan_keuangan', 'label' => 'Laporan Keuangan', 'icon' => 'fas fa-file-invoice-dollar', 'url' => '/keuangan/laporan_keuangan.php'],
            ['id' => 'laporan_bisnis', 'label' => 'Laporan Bisnis', 'icon' => 'fas fa-chart-line', 'url' => '/keuangan/laporan_bisnis.php'],
            ['id' => 'laporan_tax', 'label' => 'Laporan Pajak', 'icon' => 'fas fa-file-invoice', 'url' => '/keuangan/laporan_pajak.php'],
        ]],
        ['id' => 'tools', 'label' => 'Tools', 'icon' => 'fas fa-tools', 'submenu' => [
            ['id' => 'kalkulator', 'label' => 'Kalkulator', 'icon' => 'fas fa-calculator', 'url' => '/keuangan/kalkulator.php'],
            ['id' => 'konverter', 'label' => 'Konverter Mata Uang', 'icon' => 'fas fa-exchange-alt', 'url' => '/keuangan/konverter.php'],
            ['id' => 'kalender', 'label' => 'Kalender', 'icon' => 'fas fa-calendar-alt', 'url' => '/keuangan/kalender.php'],
            ['id' => 'pengingat', 'label' => 'Pengingat', 'icon' => 'fas fa-bell', 'url' => '/keuangan/pengingat.php'],
        ]],
        ['id' => 'admin_panel', 'label' => 'Admin Panel', 'icon' => 'fas fa-cogs', 'submenu' => [
            ['id' => 'users', 'label' => 'Kelola User', 'icon' => 'fas fa-users', 'url' => '/keuangan/users.php'],
            ['id' => 'permissions', 'label' => 'Kelola Hak Akses', 'icon' => 'fas fa-key', 'url' => '/keuangan/menu_permissions_advanced.php'],
            ['id' => 'notifications', 'label' => 'Notifikasi Sistem', 'icon' => 'fas fa-bell', 'url' => '/keuangan/notifications.php'],
            ['id' => 'system_customization', 'label' => 'System Customization', 'icon' => 'fas fa-paint-brush', 'url' => '/keuangan/system_customization.php'],
            ['id' => 'database_management', 'label' => 'Database Management', 'icon' => 'fas fa-database', 'url' => '/keuangan/database_management.php'],
            ['id' => 'fix_errors', 'label' => 'Fix System Errors', 'icon' => 'fas fa-wrench', 'url' => '/keuangan/fix_errors.php'],
            ['id' => 'update_database', 'label' => 'Update Database', 'icon' => 'fas fa-sync-alt', 'url' => '/keuangan/update_database.php'],
            ['id' => 'system_check', 'label' => 'System Health Check', 'icon' => 'fas fa-check-circle', 'url' => '/keuangan/system_check.php'],
            ['id' => 'clear_cache', 'label' => 'Clear Cache', 'icon' => 'fas fa-broom', 'url' => '/keuangan/clear_cache.php'],
            ['id' => 'backup', 'label' => 'Backup Data', 'icon' => 'fas fa-save', 'url' => '/keuangan/backup_restore.php'],
            ['id' => 'logs', 'label' => 'Log Aktivitas', 'icon' => 'fas fa-history', 'url' => '/keuangan/logs.php'],
            ['id' => 'settings', 'label' => 'Pengaturan Sistem', 'icon' => 'fas fa-cog', 'url' => '/keuangan/settings.php'],
        ]],
        ['id' => 'customization', 'label' => 'Customization', 'icon' => 'fas fa-palette', 'submenu' => [
            ['id' => 'customization_dashboard', 'label' => 'Dashboard', 'icon' => 'fas fa-tachometer-alt', 'url' => '/keuangan/customization_dashboard.php'],
            ['id' => 'theme_manager', 'label' => 'Theme Manager', 'icon' => 'fas fa-swatchbook', 'url' => '/keuangan/theme_manager.php']
        ]],
        ['id' => 'profile', 'label' => 'Profil', 'icon' => 'fas fa-user', 'url' => '/keuangan/profile.php'],
        ['id' => 'bantuan', 'label' => 'Bantuan', 'icon' => 'fas fa-question-circle', 'submenu' => [
            ['id' => 'panduan', 'label' => 'Panduan', 'icon' => 'fas fa-book', 'url' => '/keuangan/panduan.php'],
            ['id' => 'faq', 'label' => 'FAQ', 'icon' => 'fas fa-question', 'url' => '/keuangan/faq.php'],
            ['id' => 'tutorial', 'label' => 'Tutorial', 'icon' => 'fas fa-graduation-cap', 'url' => '/keuangan/tutorial.php'],
            ['id' => 'support', 'label' => 'Support', 'icon' => 'fas fa-headset', 'url' => '/keuangan/support.php'],
        ]],
        ['id' => 'logout', 'label' => 'Keluar', 'icon' => 'fas fa-sign-out-alt', 'url' => '/keuangan/logout.php'],
    ];

    // Admin sees everything
    if ($allowedMenus === 'all') {
        return $allMenus;
    }

    // If no allowed menus, return empty
    if (empty($allowedMenus)) {
        return [];
    }

    // Filter menus based on permissions
    $filteredMenus = [];

    foreach ($allMenus as $menu) {
        // Special handling for admin_dashboard - only show to admin
        if ($menu['id'] === 'admin_dashboard' && $userRole !== 'admin') {
            continue;
        }

        // Special handling for regular dashboard - don't show to admin if they have admin_dashboard
        if ($menu['id'] === 'dashboard' && $userRole === 'admin' && in_array('admin_dashboard', $allowedMenus)) {
            continue;
        }

        // Check if user has access to this menu
        if (in_array($menu['id'], $allowedMenus)) {
            $filteredMenu = $menu;

            // If menu has submenu, filter submenu too
            if (isset($menu['submenu'])) {
                $filteredSubmenu = [];
                foreach ($menu['submenu'] as $submenu) {
                    if (in_array($submenu['id'], $allowedMenus)) {
                        $filteredSubmenu[] = $submenu;
                    }
                }

                // Only include parent menu if it has accessible submenus
                if (!empty($filteredSubmenu)) {
                    $filteredMenu['submenu'] = $filteredSubmenu;
                    $filteredMenus[] = $filteredMenu;
                }
            } else {
                // Menu without submenu
                $filteredMenus[] = $filteredMenu;
            }
        }
    }

    return $filteredMenus;
}

/**
 * Get appropriate dashboard based on user role
 */
function getDashboardForRole($userRole = null) {
    if ($userRole === null) {
        $currentUser = getCurrentUser();
        if (!$currentUser) {
            return '/keuangan/dashboard.php';
        }
        $userRole = $currentUser['role'];
    }

    return ($userRole === 'admin') ? '/keuangan/admin-dashboard.php' : '/keuangan/dashboard.php';
}

/**
 * Generate sidebar HTML based on user permissions
 */
function generateSidebarHTML($userRole = null) {
    $menus = getFilteredSidebarMenus($userRole);
    $html = '';

    // Get current user role if not provided
    if ($userRole === null) {
        $currentUser = getCurrentUser();
        $userRole = $currentUser ? $currentUser['role'] : 'user';
    }

    foreach ($menus as $menu) {
        if (isset($menu['submenu']) && !empty($menu['submenu'])) {
            // Menu with submenu
            $html .= '<li class="nav-item">';
            $html .= '<a class="nav-link collapsed" href="#" data-bs-toggle="collapse" data-bs-target="#collapse' . $menu['id'] . '">';
            $html .= '<i class="' . $menu['icon'] . '"></i>';
            $html .= '<span>' . htmlspecialchars($menu['label']) . '</span>';
            $html .= '</a>';
            $html .= '<div id="collapse' . $menu['id'] . '" class="collapse">';
            $html .= '<nav class="sb-sidenav-menu-nested nav">';

            foreach ($menu['submenu'] as $submenu) {
                $html .= '<a class="nav-link" href="' . $submenu['url'] . '">';
                $html .= '<i class="' . $submenu['icon'] . '"></i>';
                $html .= htmlspecialchars($submenu['label']);
                $html .= '</a>';
            }

            $html .= '</nav>';
            $html .= '</div>';
            $html .= '</li>';
        } else {
            // Single menu item
            $html .= '<li class="nav-item">';

            // Special handling for dashboard - show appropriate dashboard based on role
            if ($menu['id'] === 'dashboard') {
                $dashboardUrl = getDashboardForRole($userRole);
                $dashboardLabel = ($userRole === 'admin') ? 'Admin Dashboard' : 'Dashboard';
                $html .= '<a class="nav-link" href="' . $dashboardUrl . '">';
                $html .= '<i class="' . $menu['icon'] . '"></i>';
                $html .= '<span>' . htmlspecialchars($dashboardLabel) . '</span>';
                $html .= '</a>';
            } else {
                $html .= '<a class="nav-link" href="' . $menu['url'] . '">';
                $html .= '<i class="' . $menu['icon'] . '"></i>';
                $html .= '<span>' . htmlspecialchars($menu['label']) . '</span>';
                $html .= '</a>';
            }

            $html .= '</li>';
        }
    }

    return $html;
}
?>
