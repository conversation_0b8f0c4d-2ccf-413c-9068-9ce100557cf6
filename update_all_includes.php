<?php
/**
 * Update All PHP Files to Include Function Check
 * 
 * This script adds function_check.php include to all PHP files that use functions.php
 */

echo "<h2>🔧 Updating All PHP Files</h2>\n";

// Get all PHP files in the root directory
$phpFiles = glob('*.php');

// Files to skip (utility files)
$skipFiles = [
    'fix_function_conflicts.php',
    'update_all_includes.php',
    'test_functions.php',
    'setup_database.php',
    'migrate_supplier_status.php'
];

$updatedFiles = [];
$skippedFiles = [];

foreach ($phpFiles as $file) {
    if (in_array($file, $skipFiles)) {
        $skippedFiles[] = $file;
        continue;
    }
    
    $content = file_get_contents($file);
    $originalContent = $content;
    
    // Check if file includes functions.php but not function_check.php
    if (strpos($content, "require_once 'includes/helpers/functions.php'") !== false &&
        strpos($content, "require_once 'includes/helpers/function_check.php'") === false) {
        
        // Add function_check.php before functions.php
        $content = str_replace(
            "require_once 'includes/helpers/functions.php'",
            "require_once 'includes/helpers/function_check.php';\nrequire_once 'includes/helpers/functions.php'",
            $content
        );
        
        if ($content !== $originalContent) {
            file_put_contents($file, $content);
            $updatedFiles[] = $file;
            echo "✅ Updated: $file<br>\n";
        }
    } else {
        echo "ℹ️ Skipped: $file (no functions.php include or already has function_check.php)<br>\n";
    }
}

echo "<h3>Summary</h3>\n";
echo "✅ Updated files: " . count($updatedFiles) . "<br>\n";
echo "ℹ️ Skipped files: " . count($skippedFiles) . "<br>\n";

if (!empty($updatedFiles)) {
    echo "<h4>Updated Files:</h4>\n";
    echo "<ul>\n";
    foreach ($updatedFiles as $file) {
        echo "<li>$file</li>\n";
    }
    echo "</ul>\n";
}

echo "<h3>Testing Function Availability</h3>\n";

// Test a few key files
$testFiles = ['dashboard.php', 'admin-dashboard.php', 'menu_permissions_advanced.php'];

foreach ($testFiles as $testFile) {
    if (file_exists($testFile)) {
        echo "<h4>Testing: $testFile</h4>\n";
        
        // Capture any errors
        ob_start();
        $errorReporting = error_reporting(E_ALL);
        
        try {
            // Test include without executing the full file
            $testContent = file_get_contents($testFile);
            
            // Extract just the includes
            preg_match_all("/require_once\s+['\"]([^'\"]+)['\"]/", $testContent, $matches);
            
            foreach ($matches[1] as $include) {
                if (file_exists($include)) {
                    echo "  ✅ Include found: $include<br>\n";
                } else {
                    echo "  ❌ Include missing: $include<br>\n";
                }
            }
            
        } catch (Exception $e) {
            echo "  ❌ Error: " . $e->getMessage() . "<br>\n";
        }
        
        error_reporting($errorReporting);
        ob_end_clean();
    }
}

echo "<h3>Next Steps</h3>\n";
echo "1. Test your pages in the browser<br>\n";
echo "2. Check for any remaining function conflicts<br>\n";
echo "3. Clear PHP OpCache if you're using it<br>\n";
echo "4. Monitor error logs for any issues<br>\n";

?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2 { color: #2c3e50; }
h3 { color: #3498db; }
h4 { color: #e67e22; }
ul { margin-left: 20px; }
</style>
