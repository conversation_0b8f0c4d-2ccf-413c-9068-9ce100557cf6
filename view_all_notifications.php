<?php
require_once 'includes/config/database.php';
require_once 'includes/helpers/functions.php';

// Check admin access
$currentUser = getCurrentUser();
if (!$currentUser || $currentUser['role'] !== 'admin') {
    setFlashMessage('danger', 'Aks<PERSON> ditolak! Anda tidak memiliki izin untuk mengakses halaman ini.');
    redirect('dashboard.php');
}

$pageTitle = 'View All Notifications';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    try {
        switch ($_POST['action']) {
            case 'delete_notification':
                $notificationId = $_POST['notification_id'] ?? '';
                if ($notificationId) {
                    $stmt = $pdo->prepare("DELETE FROM system_notifications WHERE id = ?");
                    $stmt->execute([$notificationId]);
                    setFlashMessage('success', 'Notifikasi berhasil dihapus');
                }
                break;

            case 'edit_notification':
                $notificationId = $_POST['notification_id'] ?? '';
                $title = trim($_POST['title'] ?? '');
                $message = trim($_POST['message'] ?? '');
                $type = $_POST['type'] ?? 'info';

                if (empty($notificationId) || empty($title) || empty($message)) {
                    throw new Exception('Data notifikasi tidak lengkap');
                }

                $stmt = $pdo->prepare("UPDATE system_notifications SET title = ?, message = ?, type = ?, updated_at = NOW() WHERE id = ?");
                $stmt->execute([$title, $message, $type, $notificationId]);

                setFlashMessage('success', 'Notifikasi berhasil diperbarui');
                break;

            case 'mark_as_read':
                $notificationId = $_POST['notification_id'] ?? '';
                if ($notificationId) {
                    $stmt = $pdo->prepare("UPDATE system_notifications SET is_read = TRUE WHERE id = ?");
                    $stmt->execute([$notificationId]);
                    setFlashMessage('success', 'Notifikasi ditandai sebagai sudah dibaca');
                }
                break;

            case 'mark_as_unread':
                $notificationId = $_POST['notification_id'] ?? '';
                if ($notificationId) {
                    $stmt = $pdo->prepare("UPDATE system_notifications SET is_read = FALSE WHERE id = ?");
                    $stmt->execute([$notificationId]);
                    setFlashMessage('success', 'Notifikasi ditandai sebagai belum dibaca');
                }
                break;

            case 'clear_all':
                $stmt = $pdo->prepare("DELETE FROM system_notifications");
                $stmt->execute();
                $deleted = $stmt->rowCount();
                setFlashMessage('success', "Berhasil menghapus semua notifikasi ($deleted items)");
                break;

            case 'mark_all_read':
                $stmt = $pdo->prepare("UPDATE system_notifications SET is_read = TRUE");
                $stmt->execute();
                $updated = $stmt->rowCount();
                setFlashMessage('success', "Berhasil menandai semua notifikasi sebagai sudah dibaca ($updated items)");
                break;
        }
        redirect('view_all_notifications.php');
    } catch (Exception $e) {
        setFlashMessage('danger', 'Error: ' . $e->getMessage());
    }
}

// Get filter parameters
$filterType = $_GET['type'] ?? '';
$filterStatus = $_GET['status'] ?? '';
$filterSource = $_GET['source'] ?? '';
$filterDate = $_GET['date'] ?? '';
$search = $_GET['search'] ?? '';

// Build WHERE clause for filters
$whereConditions = [];
$params = [];

if (!empty($filterType)) {
    $whereConditions[] = "type = ?";
    $params[] = $filterType;
}

if (!empty($filterStatus)) {
    if ($filterStatus === 'read') {
        $whereConditions[] = "is_read = TRUE";
    } elseif ($filterStatus === 'unread') {
        $whereConditions[] = "is_read = FALSE";
    }
}

if (!empty($filterSource)) {
    $whereConditions[] = "source = ?";
    $params[] = $filterSource;
}

if (!empty($filterDate)) {
    $whereConditions[] = "DATE(created_at) = ?";
    $params[] = $filterDate;
}

if (!empty($search)) {
    $whereConditions[] = "(title LIKE ? OR message LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

$whereClause = !empty($whereConditions) ? "WHERE " . implode(" AND ", $whereConditions) : "";

// Get filtered notifications
try {
    $sql = "SELECT * FROM system_notifications $whereClause ORDER BY created_at DESC";
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $allNotifications = $stmt->fetchAll();

    // Get statistics (always show total stats, not filtered)
    $stmt = $pdo->query("SELECT
        COUNT(*) as total,
        SUM(CASE WHEN type = 'error' THEN 1 ELSE 0 END) as errors,
        SUM(CASE WHEN type = 'warning' THEN 1 ELSE 0 END) as warnings,
        SUM(CASE WHEN type = 'info' THEN 1 ELSE 0 END) as info,
        SUM(CASE WHEN type = 'success' THEN 1 ELSE 0 END) as success,
        SUM(CASE WHEN is_read = FALSE THEN 1 ELSE 0 END) as unread
        FROM system_notifications");
    $stats = $stmt->fetch();

    // Get filter options
    $stmt = $pdo->query("SELECT DISTINCT source FROM system_notifications WHERE source IS NOT NULL ORDER BY source");
    $sources = $stmt->fetchAll(PDO::FETCH_COLUMN);

} catch (PDOException $e) {
    error_log("Error fetching notifications: " . $e->getMessage());
    $allNotifications = [];
    $stats = ['total' => 0, 'errors' => 0, 'warnings' => 0, 'info' => 0, 'success' => 0, 'unread' => 0];
    $sources = [];
}

// Function to get type info
function getTypeInfo($type) {
    $types = [
        'error' => ['class' => 'danger', 'icon' => 'fas fa-exclamation-triangle', 'label' => 'Error'],
        'warning' => ['class' => 'warning', 'icon' => 'fas fa-exclamation-circle', 'label' => 'Warning'],
        'info' => ['class' => 'info', 'icon' => 'fas fa-info-circle', 'label' => 'Info'],
        'success' => ['class' => 'success', 'icon' => 'fas fa-check-circle', 'label' => 'Success']
    ];
    return $types[$type] ?? $types['info'];
}

include 'includes/views/layouts/header.php';
?>

<div class="container-fluid px-4 py-3">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h3 class="mb-1">All Notifications</h3>
                    <p class="text-muted mb-0">Manage all system notifications</p>
                </div>
                <div class="btn-group">
                    <a href="notifications.php" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>Back
                    </a>
                    <a href="add_sample_notifications.php" class="btn btn-outline-primary">
                        <i class="fas fa-plus me-1"></i>Add More
                    </a>
                </div>
            </div>

            <!-- Statistics -->
            <div class="row mb-4">
                <div class="col-md-2">
                    <div class="card text-center h-100 <?= empty($filterType) && empty($filterStatus) ? 'border-primary' : '' ?>">
                        <div class="card-body py-3">
                            <h4 class="text-primary mb-1"><?= $stats['total'] ?></h4>
                            <small class="text-muted">Total</small>
                            <div class="mt-2">
                                <button class="btn btn-outline-primary btn-sm" onclick="clearAllFilters()">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card text-center h-100 <?= $filterType === 'error' ? 'border-danger' : '' ?>">
                        <div class="card-body py-3">
                            <h4 class="text-danger mb-1"><?= $stats['errors'] ?></h4>
                            <small class="text-muted">Errors</small>
                            <div class="mt-2">
                                <button class="btn btn-outline-danger btn-sm" onclick="filterByType('error')">
                                    <i class="fas fa-filter"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card text-center h-100 <?= $filterType === 'warning' ? 'border-warning' : '' ?>">
                        <div class="card-body py-3">
                            <h4 class="text-warning mb-1"><?= $stats['warnings'] ?></h4>
                            <small class="text-muted">Warnings</small>
                            <div class="mt-2">
                                <button class="btn btn-outline-warning btn-sm" onclick="filterByType('warning')">
                                    <i class="fas fa-filter"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card text-center h-100 <?= $filterType === 'info' ? 'border-info' : '' ?>">
                        <div class="card-body py-3">
                            <h4 class="text-info mb-1"><?= $stats['info'] ?></h4>
                            <small class="text-muted">Info</small>
                            <div class="mt-2">
                                <button class="btn btn-outline-info btn-sm" onclick="filterByType('info')">
                                    <i class="fas fa-filter"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card text-center h-100 <?= $filterType === 'success' ? 'border-success' : '' ?>">
                        <div class="card-body py-3">
                            <h4 class="text-success mb-1"><?= $stats['success'] ?></h4>
                            <small class="text-muted">Success</small>
                            <div class="mt-2">
                                <button class="btn btn-outline-success btn-sm" onclick="filterByType('success')">
                                    <i class="fas fa-filter"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card text-center h-100 <?= $filterStatus === 'unread' ? 'border-secondary' : '' ?>">
                        <div class="card-body py-3">
                            <h4 class="text-secondary mb-1"><?= $stats['unread'] ?></h4>
                            <small class="text-muted">Unread</small>
                            <div class="mt-2">
                                <button class="btn btn-outline-secondary btn-sm" onclick="filterByStatus('unread')">
                                    <i class="fas fa-filter"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filter Panel -->
            <div class="card mb-4">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">
                            <i class="fas fa-filter me-2"></i>Filter Notifications
                        </h6>
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="toggleFilters()">
                            <i class="fas fa-chevron-down" id="filterToggleIcon"></i>
                        </button>
                    </div>
                </div>
                <div class="card-body" id="filterPanel" style="display: none;">
                    <form method="GET" id="filterForm">
                        <div class="row g-3">
                            <!-- Search -->
                            <div class="col-md-3">
                                <label class="form-label">Search</label>
                                <input type="text" class="form-control" name="search" value="<?= htmlspecialchars($search) ?>" placeholder="Search title or message...">
                            </div>

                            <!-- Type Filter -->
                            <div class="col-md-2">
                                <label class="form-label">Type</label>
                                <select class="form-select" name="type">
                                    <option value="">All Types</option>
                                    <option value="error" <?= $filterType === 'error' ? 'selected' : '' ?>>Error</option>
                                    <option value="warning" <?= $filterType === 'warning' ? 'selected' : '' ?>>Warning</option>
                                    <option value="info" <?= $filterType === 'info' ? 'selected' : '' ?>>Info</option>
                                    <option value="success" <?= $filterType === 'success' ? 'selected' : '' ?>>Success</option>
                                </select>
                            </div>

                            <!-- Status Filter -->
                            <div class="col-md-2">
                                <label class="form-label">Status</label>
                                <select class="form-select" name="status">
                                    <option value="">All Status</option>
                                    <option value="read" <?= $filterStatus === 'read' ? 'selected' : '' ?>>Read</option>
                                    <option value="unread" <?= $filterStatus === 'unread' ? 'selected' : '' ?>>Unread</option>
                                </select>
                            </div>

                            <!-- Source Filter -->
                            <div class="col-md-2">
                                <label class="form-label">Source</label>
                                <select class="form-select" name="source">
                                    <option value="">All Sources</option>
                                    <?php foreach ($sources as $source): ?>
                                        <option value="<?= htmlspecialchars($source) ?>" <?= $filterSource === $source ? 'selected' : '' ?>>
                                            <?= htmlspecialchars($source) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <!-- Date Filter -->
                            <div class="col-md-2">
                                <label class="form-label">Date</label>
                                <input type="date" class="form-control" name="date" value="<?= htmlspecialchars($filterDate) ?>">
                            </div>

                            <!-- Filter Actions -->
                            <div class="col-md-1">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary btn-sm">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Filter Actions Row -->
                        <div class="row mt-3">
                            <div class="col-12">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <?php if (!empty($filterType) || !empty($filterStatus) || !empty($filterSource) || !empty($filterDate) || !empty($search)): ?>
                                            <span class="text-muted">
                                                <i class="fas fa-info-circle me-1"></i>
                                                Showing <?= count($allNotifications) ?> filtered results
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                    <div>
                                        <a href="view_all_notifications.php" class="btn btn-outline-secondary btn-sm me-2">
                                            <i class="fas fa-times me-1"></i>Clear Filters
                                        </a>
                                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="toggleFilters()">
                                            <i class="fas fa-chevron-up me-1"></i>Hide Filters
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <h6 class="mb-0">Quick Actions</h6>
                        </div>
                        <div class="col-md-6 text-end">
                            <form method="POST" class="d-inline me-2" onsubmit="return confirm('Mark all notifications as read?')">
                                <input type="hidden" name="action" value="mark_all_read">
                                <button type="submit" class="btn btn-success btn-sm">
                                    <i class="fas fa-check-double me-1"></i>Mark All Read
                                </button>
                            </form>
                            <form method="POST" class="d-inline" onsubmit="return confirm('Delete ALL notifications? This cannot be undone!')">
                                <input type="hidden" name="action" value="clear_all">
                                <button type="submit" class="btn btn-danger btn-sm">
                                    <i class="fas fa-trash me-1"></i>Clear All
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Notifications List -->
            <?php if (empty($allNotifications)): ?>
                <div class="card">
                    <div class="card-body text-center py-5">
                        <i class="fas fa-bell-slash fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No notifications found</h5>
                        <p class="text-muted">There are no notifications to display</p>
                        <a href="add_sample_notifications.php" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Add Sample Notifications
                        </a>
                    </div>
                </div>
            <?php else: ?>
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">All Notifications (<?= count($allNotifications) ?>)</h6>
                    </div>
                    <div class="card-body p-0">
                        <div class="list-group list-group-flush">
                            <?php foreach ($allNotifications as $notification): ?>
                                <?php $typeInfo = getTypeInfo($notification['type']); ?>
                                <div class="list-group-item">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div class="flex-grow-1">
                                            <div class="d-flex align-items-center mb-2">
                                                <i class="<?= $typeInfo['icon'] ?> text-<?= $typeInfo['class'] ?> me-2"></i>
                                                <h6 class="mb-0 me-2"><?= htmlspecialchars($notification['title']) ?></h6>
                                                <span class="badge bg-<?= $typeInfo['class'] ?>"><?= $typeInfo['label'] ?></span>
                                                <?php if (!$notification['is_read']): ?>
                                                    <span class="badge bg-warning ms-2">New</span>
                                                <?php endif; ?>
                                            </div>
                                            <p class="mb-2 text-muted"><?= htmlspecialchars($notification['message']) ?></p>
                                            <small class="text-muted">
                                                <i class="fas fa-clock me-1"></i>
                                                <?= date('d/m/Y H:i:s', strtotime($notification['created_at'])) ?>
                                                <?php if ($notification['source']): ?>
                                                    | <i class="fas fa-tag me-1"></i><?= htmlspecialchars($notification['source']) ?>
                                                <?php endif; ?>
                                            </small>
                                        </div>
                                        <div class="btn-group btn-group-sm">
                                            <button type="button" class="btn btn-outline-primary" onclick="editNotification(<?= htmlspecialchars(json_encode($notification)) ?>)">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <form method="POST" class="d-inline">
                                                <input type="hidden" name="action" value="<?= $notification['is_read'] ? 'mark_as_unread' : 'mark_as_read' ?>">
                                                <input type="hidden" name="notification_id" value="<?= $notification['id'] ?>">
                                                <button type="submit" class="btn btn-outline-<?= $notification['is_read'] ? 'warning' : 'success' ?>" title="<?= $notification['is_read'] ? 'Mark as Unread' : 'Mark as Read' ?>">
                                                    <i class="fas fa-<?= $notification['is_read'] ? 'eye-slash' : 'check' ?>"></i>
                                                </button>
                                            </form>
                                            <form method="POST" class="d-inline" onsubmit="return confirm('Delete this notification?')">
                                                <input type="hidden" name="action" value="delete_notification">
                                                <input type="hidden" name="notification_id" value="<?= $notification['id'] ?>">
                                                <button type="submit" class="btn btn-outline-danger">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Edit Notification Modal -->
<div class="modal fade" id="editNotificationModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Notification</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="edit_notification">
                    <input type="hidden" name="notification_id" id="edit_notification_id">

                    <div class="mb-3">
                        <label class="form-label">Type</label>
                        <select class="form-select" name="type" id="edit_type" required>
                            <option value="info">Info</option>
                            <option value="success">Success</option>
                            <option value="warning">Warning</option>
                            <option value="error">Error</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Title</label>
                        <input type="text" class="form-control" name="title" id="edit_title" required>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Message</label>
                        <textarea class="form-control" name="message" id="edit_message" rows="4" required></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Notification</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Simple edit notification function
function editNotification(notification) {
    document.getElementById('edit_notification_id').value = notification.id;
    document.getElementById('edit_type').value = notification.type;
    document.getElementById('edit_title').value = notification.title;
    document.getElementById('edit_message').value = notification.message;

    // Show modal
    var modal = new bootstrap.Modal(document.getElementById('editNotificationModal'));
    modal.show();
}

// Toggle filter panel
function toggleFilters() {
    const panel = document.getElementById('filterPanel');
    const icon = document.getElementById('filterToggleIcon');

    if (panel.style.display === 'none') {
        panel.style.display = 'block';
        icon.className = 'fas fa-chevron-up';
    } else {
        panel.style.display = 'none';
        icon.className = 'fas fa-chevron-down';
    }
}

// Auto-show filters if any filter is active
document.addEventListener('DOMContentLoaded', function() {
    const hasActiveFilters = <?= json_encode(!empty($filterType) || !empty($filterStatus) || !empty($filterSource) || !empty($filterDate) || !empty($search)) ?>;

    if (hasActiveFilters) {
        const panel = document.getElementById('filterPanel');
        const icon = document.getElementById('filterToggleIcon');
        panel.style.display = 'block';
        icon.className = 'fas fa-chevron-up';
    }
});

// Quick filter functions
function filterByType(type) {
    const url = new URL(window.location);
    url.searchParams.set('type', type);
    window.location.href = url.toString();
}

function filterByStatus(status) {
    const url = new URL(window.location);
    url.searchParams.set('status', status);
    window.location.href = url.toString();
}

function clearAllFilters() {
    window.location.href = 'view_all_notifications.php';
}
</script>

<?php include 'includes/views/layouts/footer.php'; ?>
