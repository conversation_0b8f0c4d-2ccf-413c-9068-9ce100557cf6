<?php
require_once 'includes/config/database.php';
require_once 'includes/helpers/functions.php';

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Set content type to JSON
header('Content-Type: application/json');

// Check if user is logged in
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

// Check if request method is GET
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    $query = $_GET['q'] ?? '';
    $type = $_GET['type'] ?? 'all'; // all, transactions, categories, targets
    
    if (strlen($query) < 2) {
        echo json_encode(['success' => true, 'results' => []]);
        exit;
    }
    
    $currentUser = getCurrentUser();
    if (!$currentUser) {
        http_response_code(401);
        echo json_encode(['success' => false, 'message' => 'User not found']);
        exit;
    }
    
    $results = [];
    $searchTerm = '%' . $query . '%';
    
    // Search transactions
    if ($type === 'all' || $type === 'transactions') {
        $stmt = $pdo->prepare("
            SELECT 
                t.id,
                t.deskripsi as title,
                CONCAT('Rp ', FORMAT(t.jumlah, 0)) as subtitle,
                k.nama as category,
                t.tanggal as date,
                'transaction' as type,
                'transaksi.php' as url
            FROM transaksi t
            JOIN kategori k ON t.kategori_id = k.id
            WHERE t.user_id = ? 
            AND (t.deskripsi LIKE ? OR k.nama LIKE ?)
            ORDER BY t.tanggal DESC
            LIMIT 10
        ");
        $stmt->execute([$currentUser['id'], $searchTerm, $searchTerm]);
        $transactions = $stmt->fetchAll();
        
        foreach ($transactions as $transaction) {
            $results[] = [
                'id' => $transaction['id'],
                'title' => $transaction['title'],
                'subtitle' => $transaction['subtitle'] . ' - ' . $transaction['category'],
                'type' => 'transaction',
                'icon' => 'fas fa-exchange-alt',
                'url' => $transaction['url'],
                'date' => $transaction['date']
            ];
        }
    }
    
    // Search categories
    if ($type === 'all' || $type === 'categories') {
        $stmt = $pdo->prepare("
            SELECT 
                id,
                nama as title,
                tipe as subtitle,
                'category' as type,
                'kategori.php' as url
            FROM kategori
            WHERE user_id = ? 
            AND nama LIKE ?
            ORDER BY nama ASC
            LIMIT 10
        ");
        $stmt->execute([$currentUser['id'], $searchTerm]);
        $categories = $stmt->fetchAll();
        
        foreach ($categories as $category) {
            $results[] = [
                'id' => $category['id'],
                'title' => $category['title'],
                'subtitle' => ucfirst($category['subtitle']),
                'type' => 'category',
                'icon' => 'fas fa-tag',
                'url' => $category['url'],
                'date' => null
            ];
        }
    }
    
    // Search targets
    if ($type === 'all' || $type === 'targets') {
        $stmt = $pdo->prepare("
            SELECT 
                id,
                nama as title,
                CONCAT('Target: Rp ', FORMAT(jumlah_target, 0)) as subtitle,
                'target' as type,
                'target.php' as url,
                tanggal_selesai as date
            FROM target
            WHERE user_id = ? 
            AND nama LIKE ?
            ORDER BY tanggal_selesai ASC
            LIMIT 10
        ");
        $stmt->execute([$currentUser['id'], $searchTerm]);
        $targets = $stmt->fetchAll();
        
        foreach ($targets as $target) {
            $results[] = [
                'id' => $target['id'],
                'title' => $target['title'],
                'subtitle' => $target['subtitle'],
                'type' => 'target',
                'icon' => 'fas fa-bullseye',
                'url' => $target['url'],
                'date' => $target['date']
            ];
        }
    }
    
    // Search products (if table exists)
    if ($type === 'all' || $type === 'products') {
        try {
            $stmt = $pdo->prepare("
                SELECT 
                    id,
                    nama as title,
                    CONCAT('Rp ', FORMAT(harga_jual, 0)) as subtitle,
                    'product' as type,
                    'produk.php' as url
                FROM produk
                WHERE user_id = ? 
                AND nama LIKE ?
                ORDER BY nama ASC
                LIMIT 10
            ");
            $stmt->execute([$currentUser['id'], $searchTerm]);
            $products = $stmt->fetchAll();
            
            foreach ($products as $product) {
                $results[] = [
                    'id' => $product['id'],
                    'title' => $product['title'],
                    'subtitle' => $product['subtitle'],
                    'type' => 'product',
                    'icon' => 'fas fa-box',
                    'url' => $product['url'],
                    'date' => null
                ];
            }
        } catch (PDOException $e) {
            // Table might not exist, ignore
        }
    }
    
    // Sort results by relevance (exact matches first)
    usort($results, function($a, $b) use ($query) {
        $aExact = stripos($a['title'], $query) === 0 ? 1 : 0;
        $bExact = stripos($b['title'], $query) === 0 ? 1 : 0;
        return $bExact - $aExact;
    });
    
    // Limit total results
    $results = array_slice($results, 0, 20);
    
    echo json_encode([
        'success' => true,
        'results' => $results,
        'query' => $query,
        'total' => count($results)
    ]);
    
} catch (PDOException $e) {
    error_log("Error in search: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Database error']);
} catch (Exception $e) {
    error_log("Error in search.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Server error']);
}
?>
