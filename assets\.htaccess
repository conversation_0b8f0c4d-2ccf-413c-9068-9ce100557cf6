# Prevent PHP processing in assets directory
<FilesMatch "\.php$">
    Order Allow,<PERSON><PERSON> from all
</FilesMatch>

# Allow only specific file types
<FilesMatch "\.(css|js|png|jpg|jpeg|gif|svg|ico|webp|webmanifest)$">
    Order Allow,<PERSON>y
    Allow from all
</FilesMatch>

# Set correct content types
AddType text/css .css
AddType application/javascript .js
AddType image/png .png
AddType image/jpeg .jpg .jpeg
AddType image/gif .gif
AddType image/svg+xml .svg
AddType image/x-icon .ico
AddType image/webp .webp
AddType application/manifest+json .webmanifest

# Enable CORS
<IfModule mod_headers.c>
    Header set Access-Control-Allow-Origin "*"
</IfModule>

# Cache control
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
    ExpiresByType image/x-icon "access plus 1 year"
    ExpiresByType image/webp "access plus 1 year"
    ExpiresByType application/manifest+json "access plus 1 year"
</IfModule> 