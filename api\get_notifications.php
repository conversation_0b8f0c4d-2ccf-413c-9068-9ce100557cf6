<?php
header('Content-Type: application/json');
header('Cache-Control: no-cache, must-revalidate');

require_once '../includes/config/database.php';
require_once '../includes/helpers/functions.php';

// Check if user is logged in
$currentUser = getCurrentUser();
if (!$currentUser) {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

try {
    // Get unread notifications count
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM system_notifications WHERE is_read = FALSE OR is_read = 0");
    $stmt->execute();
    $unreadCount = $stmt->fetchColumn() ?: 0;

    // Get latest notifications (limit 5 for navbar)
    $stmt = $pdo->prepare("
        SELECT * FROM system_notifications
        ORDER BY created_at DESC
        LIMIT 5
    ");
    $stmt->execute();
    $notifications = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Ensure all notifications have required fields with defaults
    $processedNotifications = [];
    foreach ($notifications as $notification) {
        $processedNotifications[] = [
            'id' => $notification['id'] ?? 0,
            'type' => $notification['type'] ?? 'info',
            'title' => $notification['title'] ?? 'No Title',
            'message' => $notification['message'] ?? 'No Message',
            'source' => $notification['source'] ?? '',
            'is_read' => (bool)($notification['is_read'] ?? false),
            'created_at' => $notification['created_at'] ?? date('Y-m-d H:i:s'),
            'user_id' => $notification['user_id'] ?? null
        ];
    }

    // Get system errors and warnings from PHP error log (if accessible)
    $systemErrors = [];
    $systemWarnings = [];
    
    // Try to get recent PHP errors from error log
    $errorLogPath = ini_get('error_log');
    if ($errorLogPath && file_exists($errorLogPath) && is_readable($errorLogPath)) {
        $errorLines = [];
        $handle = fopen($errorLogPath, 'r');
        if ($handle) {
            // Read last 100 lines
            $lines = [];
            while (($line = fgets($handle)) !== false) {
                $lines[] = $line;
                if (count($lines) > 100) {
                    array_shift($lines);
                }
            }
            fclose($handle);
            
            // Parse recent errors (last 24 hours)
            $yesterday = date('Y-m-d', strtotime('-1 day'));
            foreach ($lines as $line) {
                if (strpos($line, $yesterday) !== false || strpos($line, date('Y-m-d')) !== false) {
                    if (stripos($line, 'error') !== false || stripos($line, 'fatal') !== false) {
                        $systemErrors[] = trim($line);
                    } elseif (stripos($line, 'warning') !== false || stripos($line, 'notice') !== false) {
                        $systemWarnings[] = trim($line);
                    }
                }
            }
        }
    }

    // Get database connection status
    $dbStatus = 'connected';
    try {
        $pdo->query('SELECT 1');
    } catch (PDOException $e) {
        $dbStatus = 'error';
        $systemErrors[] = 'Database connection error: ' . $e->getMessage();
    }

    // Check for missing files or configuration issues
    $configIssues = [];
    
    // Check if important files exist
    $importantFiles = [
        '../includes/config/database.php',
        '../includes/helpers/functions.php',
        '../notifications.php',
        '../dashboard.php'
    ];
    
    foreach ($importantFiles as $file) {
        if (!file_exists($file)) {
            $configIssues[] = "Missing file: " . basename($file);
        }
    }

    // Check database tables
    try {
        $tables = ['system_notifications', 'users', 'transaksi', 'kategori'];
        foreach ($tables as $table) {
            $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
            if ($stmt->rowCount() === 0) {
                $configIssues[] = "Missing database table: $table";
            }
        }
    } catch (PDOException $e) {
        $configIssues[] = "Database table check failed: " . $e->getMessage();
    }

    // Prepare response
    $response = [
        'success' => true,
        'unread_count' => $unreadCount,
        'notifications' => $processedNotifications,
        'system_status' => [
            'database' => $dbStatus,
            'errors_count' => count($systemErrors),
            'warnings_count' => count($systemWarnings),
            'config_issues_count' => count($configIssues),
            'last_check' => date('Y-m-d H:i:s')
        ],
        'realtime_data' => [
            'recent_errors' => array_slice($systemErrors, -5), // Last 5 errors
            'recent_warnings' => array_slice($systemWarnings, -5), // Last 5 warnings
            'config_issues' => $configIssues,
            'server_time' => date('Y-m-d H:i:s'),
            'php_version' => PHP_VERSION,
            'memory_usage' => memory_get_usage(true),
            'memory_peak' => memory_get_peak_usage(true)
        ]
    ];

    // Add system notifications for errors if any
    if (!empty($systemErrors) || !empty($configIssues)) {
        // Create system error notification if not exists
        $errorTitle = 'System Issues Detected';
        $errorMessage = '';
        
        if (!empty($systemErrors)) {
            $errorMessage .= count($systemErrors) . ' system errors detected. ';
        }
        if (!empty($configIssues)) {
            $errorMessage .= count($configIssues) . ' configuration issues found. ';
        }
        $errorMessage .= 'Check system logs for details.';

        // Check if similar notification already exists (within last hour)
        $stmt = $pdo->prepare("
            SELECT id FROM system_notifications 
            WHERE title = ? AND created_at > DATE_SUB(NOW(), INTERVAL 1 HOUR)
            ORDER BY created_at DESC LIMIT 1
        ");
        $stmt->execute([$errorTitle]);
        
        if ($stmt->rowCount() === 0) {
            // Create new system error notification
            $stmt = $pdo->prepare("
                INSERT INTO system_notifications (type, title, message, source, user_id) 
                VALUES (?, ?, ?, ?, ?)
            ");
            $stmt->execute(['error', $errorTitle, $errorMessage, 'system_monitor', $currentUser['id']]);
            
            // Refresh notifications to include the new one
            $stmt = $pdo->prepare("
                SELECT * FROM system_notifications
                ORDER BY created_at DESC
                LIMIT 5
            ");
            $stmt->execute();
            $notifications = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            $processedNotifications = [];
            foreach ($notifications as $notification) {
                $processedNotifications[] = [
                    'id' => $notification['id'] ?? 0,
                    'type' => $notification['type'] ?? 'info',
                    'title' => $notification['title'] ?? 'No Title',
                    'message' => $notification['message'] ?? 'No Message',
                    'source' => $notification['source'] ?? '',
                    'is_read' => (bool)($notification['is_read'] ?? false),
                    'created_at' => $notification['created_at'] ?? date('Y-m-d H:i:s'),
                    'user_id' => $notification['user_id'] ?? null
                ];
            }
            
            $response['notifications'] = $processedNotifications;
            $response['unread_count'] = $unreadCount + 1;
        }
    }

    echo json_encode($response);

} catch (Exception $e) {
    error_log("Error in get_notifications.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Internal server error',
        'message' => $e->getMessage()
    ]);
}
?>
