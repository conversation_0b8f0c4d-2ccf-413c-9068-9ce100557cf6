# 🔧 DATABASE ERROR SOLUTION

## 🚨 **MASALAH: "Database error"**

Error "Database error" pada theme manager biasanya disebabkan oleh:
1. **Tabel theme belum dibuat**
2. **Database connection issues**
3. **Missing user permissions**
4. **Corrupted table structure**

---

## 🎯 **SOLUSI CEPAT**

### **Step 1: Diagnose Problem**
```bash
# Akses untuk diagnosis
http://localhost/keuangan/check_theme_database.php
```

### **Step 2: Auto Fix Issues**
```bash
# Akses untuk auto-fix
http://localhost/keuangan/fix_theme_database.php
```

### **Step 3: Manual Setup (jika diperlukan)**
```bash
# Setup manual
http://localhost/keuangan/setup_theme_tables.php
```

---

## 🔍 **DIAGNOSTIC TOOLS**

### **1. check_theme_database.php**
**Fungsi:**
- ✅ Check database connection
- ✅ Verify table existence
- ✅ Check table structure
- ✅ Validate user permissions
- ✅ Test theme functions

**Output:**
- Detailed status report
- Error identification
- Recommendations

### **2. fix_theme_database.php**
**Fungsi:**
- 🔧 Auto-create missing tables
- 🔧 Add missing system settings
- 🔧 Create user theme preferences
- 🔧 Fix table structure issues
- 🔧 Test functionality

**Output:**
- List of fixes applied
- Remaining issues
- Success confirmation

---

## 🛠️ **MANUAL FIXES**

### **Fix 1: Create Missing Tables**
```sql
-- System Settings Table
CREATE TABLE IF NOT EXISTS system_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    setting_key VARCHAR(100) NOT NULL UNIQUE,
    setting_value TEXT,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_setting_key (setting_key)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- User Theme Preferences Table
CREATE TABLE IF NOT EXISTS user_theme_preferences (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    theme_mode VARCHAR(20) DEFAULT 'light',
    primary_color VARCHAR(7) DEFAULT '#2563eb',
    secondary_color VARCHAR(7) DEFAULT '#64748b',
    sidebar_color VARCHAR(20) DEFAULT 'dark',
    navbar_color VARCHAR(20) DEFAULT 'primary',
    custom_css TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_user_theme (user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Custom Themes Table
CREATE TABLE IF NOT EXISTS custom_themes (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    theme_data TEXT NOT NULL,
    created_by INT NOT NULL,
    is_active BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_created_by (created_by),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### **Fix 2: Add Default Settings**
```sql
INSERT IGNORE INTO system_settings (setting_key, setting_value, description) VALUES
('theme_mode', 'light', 'Default theme mode (light/dark/auto)'),
('primary_color', '#2563eb', 'Primary color for the theme'),
('secondary_color', '#64748b', 'Secondary color for the theme'),
('sidebar_color', 'dark', 'Sidebar color scheme'),
('navbar_color', 'primary', 'Navbar color scheme'),
('enable_dark_mode', '1', 'Enable dark mode toggle'),
('enable_custom_themes', '1', 'Enable custom theme creation'),
('default_theme', 'default', 'Default theme for new users');
```

### **Fix 3: Create User Preferences**
```sql
INSERT IGNORE INTO user_theme_preferences (user_id, theme_mode, primary_color, secondary_color, sidebar_color, navbar_color)
SELECT id, 'light', '#2563eb', '#64748b', 'dark', 'primary'
FROM users;
```

---

## 🔧 **TROUBLESHOOTING STEPS**

### **Error: "Table doesn't exist"**
**Solution:**
1. Run `setup_theme_tables.php`
2. Or use `fix_theme_database.php`
3. Check database permissions

### **Error: "Database connection failed"**
**Solution:**
1. Check `includes/config/database.php`
2. Verify MySQL service running
3. Check database credentials

### **Error: "User not found"**
**Solution:**
1. Login as admin user
2. Check user session
3. Verify user table exists

### **Error: "Foreign key constraint fails"**
**Solution:**
1. Check if `users` table exists
2. Remove foreign key constraints temporarily
3. Use `fix_theme_database.php`

---

## 📋 **VERIFICATION CHECKLIST**

### **Database Structure:**
- ✅ `system_settings` table exists
- ✅ `user_theme_preferences` table exists  
- ✅ `custom_themes` table exists
- ✅ All tables have proper structure

### **Data Integrity:**
- ✅ Default system settings inserted
- ✅ User theme preferences created
- ✅ No orphaned records

### **Functionality:**
- ✅ Theme functions work
- ✅ API endpoints respond
- ✅ No database errors in logs

### **User Experience:**
- ✅ Theme manager loads
- ✅ Themes can be applied
- ✅ Preferences are saved

---

## 🚀 **PREVENTION**

### **1. Regular Backups**
```bash
# Backup theme tables
mysqldump -u username -p database_name system_settings user_theme_preferences custom_themes > theme_backup.sql
```

### **2. Error Monitoring**
```php
// Add to error handler
error_log("Theme error: " . $e->getMessage());
```

### **3. Graceful Degradation**
```php
// Always provide fallback
if (!$userTheme) {
    $userTheme = getDefaultThemePreferences();
}
```

---

## 🎯 **QUICK REFERENCE**

### **Common Commands:**
```bash
# Check status
http://localhost/keuangan/check_theme_database.php

# Auto-fix issues  
http://localhost/keuangan/fix_theme_database.php

# Manual setup
http://localhost/keuangan/setup_theme_tables.php

# Test functions
http://localhost/keuangan/test_theme_functions.php
```

### **File Locations:**
```
📁 Database Tools:
├── 📄 check_theme_database.php (Diagnostic)
├── 📄 fix_theme_database.php (Auto-fix)
├── 📄 setup_theme_tables.php (Manual setup)
└── 📄 test_theme_functions.php (Testing)

📁 Core Files:
├── 📄 api/theme.php (API endpoints)
├── 📄 includes/helpers/theme_helper.php (Functions)
└── 📄 theme_manager.php (Main interface)
```

---

## 🎉 **EXPECTED RESULTS**

### **After Fix:**
- ✅ No "Database error" messages
- ✅ Theme manager loads properly
- ✅ Themes can be applied
- ✅ Preferences are persistent
- ✅ All functions work correctly

### **Performance:**
- ⚡ Fast theme switching
- 💾 Efficient database queries
- 🔄 Real-time updates
- 📱 Mobile responsive

---

## 📞 **SUPPORT**

Jika masih mengalami masalah:

1. **Check Error Logs:**
   - PHP error log
   - MySQL error log
   - Application logs

2. **Run Diagnostic:**
   - `check_theme_database.php`
   - `test_theme_functions.php`

3. **Manual Verification:**
   - Database connection
   - Table structure
   - User permissions

**Status: Database error sudah dapat diatasi dengan tools yang disediakan!** 🔧✨
