<?php
require_once 'includes/config/database.php';
require_once 'includes/helpers/functions.php';
require_once 'includes/helpers/notifications.php';

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isLoggedIn()) {
    setFlashMessage('danger', 'Silakan login terlebih dahulu');
    redirect('/keuangan/login.php');
}

$currentUser = getCurrentUser();

// Check if ID is provided
if (!isset($_GET['id'])) {
    setFlashMessage('danger', 'ID transaksi tidak valid');
    redirect('/transaksi.php');
}

$id = (int)$_GET['id'];

try {
    // Get transaction details for notification
    $stmt = $pdo->prepare("
        SELECT t.*, k.nama as kategori_nama, k.tipe as kategori_tipe
        FROM transaksi t
        JOIN kategori k ON t.kategori_id = k.id
        WHERE t.id = ? AND t.user_id = ?
    ");
    $stmt->execute([$id, $currentUser['id']]);
    $transaksi = $stmt->fetch();
    
    if (!$transaksi) {
        setFlashMessage('danger', 'Transaksi tidak ditemukan');
        redirect('/transaksi.php');
    }
    
    // Delete transaction
    $stmt = $pdo->prepare("DELETE FROM transaksi WHERE id = ? AND user_id = ?");
    $stmt->execute([$id, $currentUser['id']]);
    
    // Create notification
    $judul = 'Transaksi Dihapus';
    $pesan = sprintf(
        'Transaksi %s sebesar %s telah dihapus',
        $transaksi['kategori_tipe'] === 'pemasukan' ? 'pemasukan' : 'pengeluaran',
        formatRupiah($transaksi['jumlah'])
    );
    createNotification($currentUser['id'], $judul, $pesan, 'warning');
    
    // Log activity
    logActivity($currentUser['id'], sprintf(
        'Menghapus transaksi %s sebesar %s',
        $transaksi['kategori_tipe'] === 'pemasukan' ? 'pemasukan' : 'pengeluaran',
        formatRupiah($transaksi['jumlah'])
    ));
    
    setFlashMessage('success', 'Transaksi berhasil dihapus');
} catch (PDOException $e) {
    setFlashMessage('danger', 'Terjadi kesalahan: ' . $e->getMessage());
}

redirect('/transaksi.php'); 