<?php
require_once 'includes/config/database.php';
require_once 'includes/helpers/functions.php';
require_once 'includes/helpers/notifications.php';

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isLoggedIn()) {
    setFlashMessage('danger', 'Silakan login terlebih dahulu');
    redirect('/keuangan/login.php');
}

$currentUser = getCurrentUser();
$currentPage = 'supplier';

// Create supplier table if not exists
try {
    $pdo->exec("CREATE TABLE IF NOT EXISTS supplier (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        nama_supplier VARCHAR(255) NOT NULL,
        kontak VARCHAR(100),
        email VARCHAR(255),
        alamat TEXT,
        keterangan TEXT,
        status ENUM('aktif', 'nonaktif') DEFAULT 'aktif',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    )");

    // Check if status column exists, if not add it
    $stmt = $pdo->query("SHOW COLUMNS FROM supplier LIKE 'status'");
    if ($stmt->rowCount() == 0) {
        $pdo->exec("ALTER TABLE supplier ADD COLUMN status ENUM('aktif', 'nonaktif') DEFAULT 'aktif' AFTER keterangan");
    }
} catch (PDOException $e) {
    error_log("Error creating/updating supplier table: " . $e->getMessage());
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        try {
            switch ($_POST['action']) {
                case 'add':
                    $errors = [];

                    if (empty($_POST['nama_supplier'])) {
                        $errors[] = 'Nama supplier harus diisi';
                    }

                    if (empty($errors)) {
                        // Check if status column exists
                        $statusColumnExists = true;
                        try {
                            $stmt = $pdo->query("SHOW COLUMNS FROM supplier LIKE 'status'");
                            $statusColumnExists = $stmt->rowCount() > 0;
                        } catch (PDOException $e) {
                            $statusColumnExists = false;
                        }

                        if ($statusColumnExists) {
                            $stmt = $pdo->prepare("INSERT INTO supplier (user_id, nama_supplier, kontak, email, alamat, keterangan, status) VALUES (?, ?, ?, ?, ?, ?, ?)");
                            $result = $stmt->execute([
                                $currentUser['id'],
                                $_POST['nama_supplier'],
                                $_POST['kontak'] ?? '',
                                $_POST['email'] ?? '',
                                $_POST['alamat'] ?? '',
                                $_POST['keterangan'] ?? '',
                                $_POST['status'] ?? 'aktif'
                            ]);
                        } else {
                            $stmt = $pdo->prepare("INSERT INTO supplier (user_id, nama_supplier, kontak, email, alamat, keterangan) VALUES (?, ?, ?, ?, ?, ?)");
                            $result = $stmt->execute([
                                $currentUser['id'],
                                $_POST['nama_supplier'],
                                $_POST['kontak'] ?? '',
                                $_POST['email'] ?? '',
                                $_POST['alamat'] ?? '',
                                $_POST['keterangan'] ?? ''
                            ]);
                        }

                        if ($result) {
                            setFlashMessage('success', 'Supplier berhasil ditambahkan');
                        } else {
                            setFlashMessage('danger', 'Gagal menambahkan supplier');
                        }
                    } else {
                        setFlashMessage('danger', implode('<br>', $errors));
                    }
                    break;

                case 'update':
                    if (!empty($_POST['id'])) {
                        // Check if status column exists
                        $statusColumnExists = true;
                        try {
                            $stmt = $pdo->query("SHOW COLUMNS FROM supplier LIKE 'status'");
                            $statusColumnExists = $stmt->rowCount() > 0;
                        } catch (PDOException $e) {
                            $statusColumnExists = false;
                        }

                        if ($statusColumnExists) {
                            $stmt = $pdo->prepare("UPDATE supplier SET nama_supplier = ?, kontak = ?, email = ?, alamat = ?, keterangan = ?, status = ? WHERE id = ? AND user_id = ?");
                            $result = $stmt->execute([
                                $_POST['nama_supplier'],
                                $_POST['kontak'] ?? '',
                                $_POST['email'] ?? '',
                                $_POST['alamat'] ?? '',
                                $_POST['keterangan'] ?? '',
                                $_POST['status'] ?? 'aktif',
                                $_POST['id'],
                                $currentUser['id']
                            ]);
                        } else {
                            $stmt = $pdo->prepare("UPDATE supplier SET nama_supplier = ?, kontak = ?, email = ?, alamat = ?, keterangan = ? WHERE id = ? AND user_id = ?");
                            $result = $stmt->execute([
                                $_POST['nama_supplier'],
                                $_POST['kontak'] ?? '',
                                $_POST['email'] ?? '',
                                $_POST['alamat'] ?? '',
                                $_POST['keterangan'] ?? '',
                                $_POST['id'],
                                $currentUser['id']
                            ]);
                        }

                        if ($result) {
                            setFlashMessage('success', 'Supplier berhasil diperbarui');
                        } else {
                            setFlashMessage('danger', 'Gagal memperbarui supplier');
                        }
                    }
                    break;

                case 'delete':
                    if (!empty($_POST['id'])) {
                        $stmt = $pdo->prepare("DELETE FROM supplier WHERE id = ? AND user_id = ?");
                        $result = $stmt->execute([$_POST['id'], $currentUser['id']]);
                        
                        if ($result) {
                            setFlashMessage('success', 'Supplier berhasil dihapus');
                        } else {
                            setFlashMessage('danger', 'Gagal menghapus supplier');
                        }
                    }
                    break;
            }
        } catch (PDOException $e) {
            // Log the actual error for debugging
            error_log("Supplier error: " . $e->getMessage());
            error_log("SQL State: " . $e->getCode());

            // Show user-friendly message
            if (strpos($e->getMessage(), 'status') !== false) {
                setFlashMessage('danger', 'Database belum diupdate. Silakan jalankan setup database terlebih dahulu.');
            } else {
                setFlashMessage('danger', 'Terjadi kesalahan database. Silakan coba lagi atau hubungi administrator.');
            }
        } catch (Exception $e) {
            error_log("General error in supplier: " . $e->getMessage());
            setFlashMessage('danger', 'Terjadi kesalahan. Silakan coba lagi.');
        }
        redirect('/keuangan/supplier.php');
    }
}

// Get suppliers with pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$perPage = 10;
$offset = ($page - 1) * $perPage;

$where = ["user_id = ?"];
$params = [$currentUser['id']];

// Check if status column exists before using it in filter
$statusColumnExists = true;
try {
    $stmt = $pdo->query("SHOW COLUMNS FROM supplier LIKE 'status'");
    $statusColumnExists = $stmt->rowCount() > 0;
} catch (PDOException $e) {
    $statusColumnExists = false;
}

if (!empty($_GET['status']) && $statusColumnExists) {
    $where[] = "status = ?";
    $params[] = $_GET['status'];
}

if (!empty($_GET['search'])) {
    $where[] = "(nama_supplier LIKE ? OR kontak LIKE ? OR email LIKE ?)";
    $searchTerm = '%' . $_GET['search'] . '%';
    $params[] = $searchTerm;
    $params[] = $searchTerm;
    $params[] = $searchTerm;
}

$whereClause = implode(" AND ", $where);

// Get total records
$stmt = $pdo->prepare("SELECT COUNT(*) as total FROM supplier WHERE $whereClause");
$stmt->execute($params);
$totalRecords = $stmt->fetch()['total'];
$totalPages = ceil($totalRecords / $perPage);

// Get suppliers
$stmt = $pdo->prepare("
    SELECT * FROM supplier 
    WHERE $whereClause 
    ORDER BY nama_supplier ASC 
    LIMIT ? OFFSET ?
");

$paginationParams = $params;
$paginationParams[] = $perPage;
$paginationParams[] = $offset;

$stmt->execute($paginationParams);
$suppliers = $stmt->fetchAll();

// Get statistics
try {
    $stmt = $pdo->prepare("
        SELECT
            COUNT(CASE WHEN status = 'aktif' THEN 1 END) as aktif,
            COUNT(CASE WHEN status = 'nonaktif' THEN 1 END) as nonaktif,
            COUNT(*) as total
        FROM supplier
        WHERE user_id = ?
    ");
    $stmt->execute([$currentUser['id']]);
    $stats = $stmt->fetch();
} catch (PDOException $e) {
    // Fallback if status column doesn't exist yet
    $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM supplier WHERE user_id = ?");
    $stmt->execute([$currentUser['id']]);
    $total = $stmt->fetchColumn();
    $stats = [
        'total' => $total,
        'aktif' => $total, // Assume all are active if no status column
        'nonaktif' => 0
    ];
}

// Include header and sidebar
require_once 'includes/views/layouts/header.php';
require_once 'includes/views/layouts/sidebar.php';
?>

<div class="modern-layout">
    <div class="modern-container">
        <!-- Modern Page Header -->
        <div class="modern-page-header modern-flex modern-justify-between modern-items-center modern-mb-xl">
            <div>
                <h1 class="modern-page-title modern-mb-0">Supplier Management</h1>
                <p class="modern-page-subtitle">Kelola data supplier untuk bisnis Anda dengan mudah dan efisien</p>
            </div>
            <div class="modern-page-actions">
                <button type="button" class="modern-btn modern-btn-success" onclick="exportSuppliers()">
                    <i class="fas fa-download"></i>
                    Export
                </button>
                <button type="button" class="modern-btn modern-btn-primary" data-bs-toggle="modal" data-bs-target="#addSupplierModal">
                    <i class="fas fa-plus"></i>
                    Tambah Supplier
                </button>
            </div>
        </div>

        <!-- Modern Statistics Cards -->
        <div class="modern-grid modern-grid-cols-4 modern-gap-lg modern-mb-xl">
            <div class="modern-stats-card modern-stats-primary">
                <div class="modern-stats-content">
                    <div class="modern-stats-info">
                        <div class="modern-stats-label">Total Supplier</div>
                        <div class="modern-stats-value"><?= number_format($stats['total'] ?? 0) ?></div>
                        <div class="modern-stats-meta">Semua supplier</div>
                    </div>
                    <div class="modern-stats-icon">
                        <i class="fas fa-truck"></i>
                    </div>
                </div>
                <div class="modern-stats-glow"></div>
            </div>

            <div class="modern-stats-card modern-stats-success">
                <div class="modern-stats-content">
                    <div class="modern-stats-info">
                        <div class="modern-stats-label">Supplier Aktif</div>
                        <div class="modern-stats-value"><?= number_format($stats['aktif'] ?? 0) ?></div>
                        <div class="modern-stats-meta">Sedang aktif</div>
                    </div>
                    <div class="modern-stats-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                </div>
                <div class="modern-stats-glow"></div>
            </div>

            <div class="modern-stats-card modern-stats-secondary">
                <div class="modern-stats-content">
                    <div class="modern-stats-info">
                        <div class="modern-stats-label">Non-Aktif</div>
                        <div class="modern-stats-value"><?= number_format($stats['nonaktif'] ?? 0) ?></div>
                        <div class="modern-stats-meta">Tidak aktif</div>
                    </div>
                    <div class="modern-stats-icon">
                        <i class="fas fa-times-circle"></i>
                    </div>
                </div>
                <div class="modern-stats-glow"></div>
            </div>

            <div class="modern-stats-card modern-stats-info">
                <div class="modern-stats-content">
                    <div class="modern-stats-info">
                        <div class="modern-stats-label">Supplier Baru</div>
                        <div class="modern-stats-value">
                            <?php
                            // Get suppliers added this month
                            $stmt = $pdo->prepare("SELECT COUNT(*) FROM supplier WHERE user_id = ? AND MONTH(created_at) = MONTH(CURRENT_DATE()) AND YEAR(created_at) = YEAR(CURRENT_DATE())");
                            $stmt->execute([$currentUser['id']]);
                            echo number_format($stmt->fetchColumn());
                            ?>
                        </div>
                        <div class="modern-stats-meta">Bulan ini</div>
                    </div>
                    <div class="modern-stats-icon">
                        <i class="fas fa-plus-circle"></i>
                    </div>
                </div>
                <div class="modern-stats-glow"></div>
            </div>
        </div>

        <!-- Modern Quick Actions -->
        <div class="modern-card modern-mb-lg">
            <div class="modern-card-header">
                <h5 class="modern-card-title">
                    <i class="fas fa-bolt modern-text-primary modern-mr-sm"></i>
                    Quick Actions
                </h5>
            </div>
            <div class="modern-card-body">
                <div class="modern-grid modern-grid-cols-6 modern-gap-sm">
                    <a href="?status=aktif" class="modern-btn modern-btn-success modern-btn-sm">
                        <i class="fas fa-check-circle"></i>
                        Aktif
                    </a>
                    <a href="?status=nonaktif" class="modern-btn modern-btn-secondary modern-btn-sm">
                        <i class="fas fa-times-circle"></i>
                        Non-Aktif
                    </a>
                    <button type="button" class="modern-btn modern-btn-info modern-btn-sm" onclick="exportSuppliers()">
                        <i class="fas fa-download"></i>
                        Export
                    </button>
                    <button type="button" class="modern-btn modern-btn-warning modern-btn-sm" onclick="printSuppliers()">
                        <i class="fas fa-print"></i>
                        Print
                    </button>
                    <button type="button" class="modern-btn modern-btn-primary modern-btn-sm" data-bs-toggle="modal" data-bs-target="#bulkImportModal">
                        <i class="fas fa-upload"></i>
                        Import
                    </button>
                    <a href="/keuangan/supplier.php" class="modern-btn modern-btn-light modern-btn-sm">
                        <i class="fas fa-sync"></i>
                        Refresh
                    </a>
                </div>
            </div>
        </div>

        <!-- Modern Filter Section -->
        <div class="modern-card modern-mb-lg">
            <div class="modern-card-header">
                <h5 class="modern-card-title">
                    <i class="fas fa-filter modern-text-primary modern-mr-sm"></i>
                    Filter & Pencarian
                </h5>
            </div>
            <div class="modern-card-body">
                <form action="" method="GET" class="modern-grid modern-grid-cols-3 modern-gap-md modern-items-end">
                    <div class="modern-form-group">
                        <label class="modern-form-label">
                            <i class="fas fa-search modern-text-primary"></i>
                            Pencarian
                        </label>
                        <input type="text" name="search" class="modern-form-control" placeholder="Cari nama, kontak, atau email..." value="<?= $_GET['search'] ?? '' ?>">
                    </div>

                    <div class="modern-form-group">
                        <label class="modern-form-label">
                            <i class="fas fa-toggle-on modern-text-primary"></i>
                            Status
                        </label>
                        <select name="status" class="modern-form-control">
                            <option value="">🔍 Semua Status</option>
                            <option value="aktif" <?= (isset($_GET['status']) && $_GET['status'] == 'aktif') ? 'selected' : '' ?>>✅ Aktif</option>
                            <option value="nonaktif" <?= (isset($_GET['status']) && $_GET['status'] == 'nonaktif') ? 'selected' : '' ?>>❌ Non-Aktif</option>
                        </select>
                    </div>

                    <div class="modern-form-group modern-flex modern-gap-sm modern-mb-0">
                        <button type="submit" class="modern-btn modern-btn-primary modern-flex-1">
                            <i class="fas fa-search"></i>
                            Cari
                        </button>
                        <a href="/keuangan/supplier.php" class="modern-btn modern-btn-light">
                            <i class="fas fa-times"></i>
                            Reset
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- Modern Suppliers Table -->
        <div class="modern-card">
            <div class="modern-card-header">
                <h5 class="modern-card-title">
                    <i class="fas fa-list modern-text-primary modern-mr-sm"></i>
                    Daftar Supplier
                </h5>
                <div class="modern-card-actions">
                    <span class="modern-badge modern-badge-info">
                        Total: <?= count($suppliers) ?> supplier
                    </span>
                    <div class="modern-view-toggle">
                        <button type="button" class="modern-btn modern-btn-light modern-btn-sm active" onclick="toggleView('table')">
                            <i class="fas fa-table"></i>
                        </button>
                        <button type="button" class="modern-btn modern-btn-light modern-btn-sm" onclick="toggleView('grid')">
                            <i class="fas fa-th"></i>
                        </button>
                    </div>
                </div>
            </div>
            <div class="modern-card-body modern-p-0">
                <div id="tableView">
                    <div class="modern-table-responsive">
                        <table class="modern-table">
                            <thead class="modern-table-header">
                                <tr>
                                    <th class="modern-table-th">
                                        <i class="fas fa-building modern-mr-xs"></i>
                                        Supplier
                                    </th>
                                    <th class="modern-table-th">
                                        <i class="fas fa-phone modern-mr-xs"></i>
                                        Kontak
                                    </th>
                                    <th class="modern-table-th">
                                        <i class="fas fa-envelope modern-mr-xs"></i>
                                        Email
                                    </th>
                                    <th class="modern-table-th">
                                        <i class="fas fa-map-marker-alt modern-mr-xs"></i>
                                        Alamat
                                    </th>
                                    <th class="modern-table-th modern-text-center">
                                        <i class="fas fa-toggle-on modern-mr-xs"></i>
                                        Status
                                    </th>
                                    <th class="modern-table-th modern-text-center">
                                        <i class="fas fa-cogs modern-mr-xs"></i>
                                        Aksi
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="modern-table-body">
                                <?php if (empty($suppliers)): ?>
                                <tr>
                                    <td colspan="6" class="modern-table-td">
                                        <div class="modern-empty-state">
                                            <div class="modern-empty-icon">
                                                <i class="fas fa-truck"></i>
                                            </div>
                                            <div class="modern-empty-content">
                                                <h6 class="modern-empty-title">Belum Ada Data Supplier</h6>
                                                <p class="modern-empty-text">Mulai kelola supplier dengan menambahkan supplier pertama</p>
                                                <button type="button" class="modern-btn modern-btn-primary modern-btn-sm" data-bs-toggle="modal" data-bs-target="#addSupplierModal">
                                                    <i class="fas fa-plus"></i>
                                                    Tambah Supplier Pertama
                                                </button>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <?php else: ?>
                                <?php foreach ($suppliers as $supplier): ?>
                                <tr class="modern-table-row">
                                    <td class="modern-table-td">
                                        <div class="modern-supplier-item">
                                            <div class="modern-supplier-icon">
                                                <i class="fas fa-building"></i>
                                            </div>
                                            <div class="modern-supplier-info">
                                                <div class="modern-supplier-name"><?= htmlspecialchars($supplier['nama_supplier']) ?></div>
                                                <?php if ($supplier['keterangan']): ?>
                                                    <div class="modern-supplier-desc"><?= htmlspecialchars($supplier['keterangan']) ?></div>
                                                <?php endif; ?>
                                                <?php if (isset($supplier['created_at'])): ?>
                                                    <div class="modern-supplier-date">
                                                        <i class="fas fa-calendar-alt"></i>
                                                        Bergabung: <?= formatDate($supplier['created_at']) ?>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="modern-table-td">
                                        <?php if ($supplier['kontak']): ?>
                                            <a href="tel:<?= $supplier['kontak'] ?>" class="modern-contact-link modern-contact-phone">
                                                <i class="fas fa-phone"></i>
                                                <?= htmlspecialchars($supplier['kontak']) ?>
                                            </a>
                                        <?php else: ?>
                                            <span class="modern-text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="modern-table-td">
                                        <?php if ($supplier['email']): ?>
                                            <a href="mailto:<?= $supplier['email'] ?>" class="modern-contact-link modern-contact-email">
                                                <i class="fas fa-envelope"></i>
                                                <?= htmlspecialchars($supplier['email']) ?>
                                            </a>
                                        <?php else: ?>
                                            <span class="modern-text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="modern-table-td">
                                        <?php if ($supplier['alamat']): ?>
                                            <div class="modern-address" title="<?= htmlspecialchars($supplier['alamat']) ?>">
                                                <i class="fas fa-map-marker-alt modern-text-danger"></i>
                                                <?= htmlspecialchars(substr($supplier['alamat'], 0, 50)) ?><?= strlen($supplier['alamat']) > 50 ? '...' : '' ?>
                                            </div>
                                        <?php else: ?>
                                            <span class="modern-text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="modern-table-td modern-text-center">
                                        <?php
                                        $status = isset($supplier['status']) ? $supplier['status'] : 'aktif';
                                        ?>
                                        <div class="modern-badge modern-badge-<?= $status === 'aktif' ? 'success' : 'secondary' ?>">
                                            <?php if ($status === 'aktif'): ?>
                                                <i class="fas fa-check-circle"></i>
                                            <?php else: ?>
                                                <i class="fas fa-times-circle"></i>
                                            <?php endif; ?>
                                            <?= ucfirst($status) ?>
                                        </div>
                                    </td>
                                    <td class="modern-table-td modern-text-center">
                                        <div class="modern-action-buttons">
                                            <button type="button" class="modern-btn modern-btn-info modern-btn-sm"
                                                    onclick="viewSupplier(<?= htmlspecialchars(json_encode($supplier)) ?>)" title="Lihat Detail">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button type="button" class="modern-btn modern-btn-light modern-btn-sm"
                                                    onclick="editSupplier(
                                                        '<?= $supplier['id'] ?>',
                                                        '<?= htmlspecialchars($supplier['nama_supplier']) ?>',
                                                        '<?= htmlspecialchars($supplier['kontak']) ?>',
                                                        '<?= htmlspecialchars($supplier['email']) ?>',
                                                        '<?= htmlspecialchars($supplier['alamat']) ?>',
                                                        '<?= htmlspecialchars($supplier['keterangan']) ?>',
                                                        '<?= isset($supplier['status']) ? $supplier['status'] : 'aktif' ?>'
                                                    )" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button type="button" class="modern-btn modern-btn-danger modern-btn-sm"
                                                    onclick="deleteSupplier(<?= $supplier['id'] ?>)" title="Hapus">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                            </tr>
                            <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Grid View (Hidden by default) -->
            <div id="gridView" style="display: none;">
                <div class="row g-3 p-3">
                    <?php foreach ($suppliers as $supplier): ?>
                    <div class="col-lg-4 col-md-6">
                        <div class="card h-100 border-0 shadow-sm">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-start mb-3">
                                    <div class="bg-primary bg-opacity-10 p-2 rounded">
                                        <i class="fas fa-building text-primary"></i>
                                    </div>
                                    <?php
                                    $status = isset($supplier['status']) ? $supplier['status'] : 'aktif';
                                    ?>
                                    <span class="badge bg-<?= $status === 'aktif' ? 'success' : 'secondary' ?>">
                                        <?= ucfirst($status) ?>
                                    </span>
                                </div>
                                <h6 class="card-title"><?= htmlspecialchars($supplier['nama_supplier']) ?></h6>
                                <div class="card-text">
                                    <?php if ($supplier['kontak']): ?>
                                        <small class="text-muted d-block"><i class="fas fa-phone me-1"></i><?= htmlspecialchars($supplier['kontak']) ?></small>
                                    <?php endif; ?>
                                    <?php if ($supplier['email']): ?>
                                        <small class="text-muted d-block"><i class="fas fa-envelope me-1"></i><?= htmlspecialchars($supplier['email']) ?></small>
                                    <?php endif; ?>
                                    <?php if ($supplier['alamat']): ?>
                                        <small class="text-muted d-block"><i class="fas fa-map-marker-alt me-1"></i><?= htmlspecialchars(substr($supplier['alamat'], 0, 50)) ?><?= strlen($supplier['alamat']) > 50 ? '...' : '' ?></small>
                                    <?php endif; ?>
                                </div>
                                <div class="btn-group w-100 mt-3">
                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="editSupplier(
                                        '<?= $supplier['id'] ?>',
                                        '<?= htmlspecialchars($supplier['nama_supplier']) ?>',
                                        '<?= htmlspecialchars($supplier['kontak']) ?>',
                                        '<?= htmlspecialchars($supplier['email']) ?>',
                                        '<?= htmlspecialchars($supplier['alamat']) ?>',
                                        '<?= htmlspecialchars($supplier['keterangan']) ?>',
                                        '<?= isset($supplier['status']) ? $supplier['status'] : 'aktif' ?>'
                                    )">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-info" onclick="viewSupplier(<?= htmlspecialchars(json_encode($supplier)) ?>)">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteSupplier(<?= $supplier['id'] ?>)">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>

            <!-- Pagination -->
            <?php if ($totalPages > 1): ?>
            <nav class="mt-4">
                <ul class="pagination justify-content-center">
                    <?php if ($page > 1): ?>
                    <li class="page-item">
                        <a class="page-link" href="?page=<?= $page - 1 ?><?= isset($_GET['status']) ? '&status='.$_GET['status'] : '' ?><?= isset($_GET['search']) ? '&search='.urlencode($_GET['search']) : '' ?>">
                            <i class="fas fa-chevron-left"></i>
                        </a>
                    </li>
                    <?php endif; ?>
                    
                    <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                    <li class="page-item <?= $i === $page ? 'active' : '' ?>">
                        <a class="page-link" href="?page=<?= $i ?><?= isset($_GET['status']) ? '&status='.$_GET['status'] : '' ?><?= isset($_GET['search']) ? '&search='.urlencode($_GET['search']) : '' ?>"><?= $i ?></a>
                    </li>
                    <?php endfor; ?>
                    
                    <?php if ($page < $totalPages): ?>
                    <li class="page-item">
                        <a class="page-link" href="?page=<?= $page + 1 ?><?= isset($_GET['status']) ? '&status='.$_GET['status'] : '' ?><?= isset($_GET['search']) ? '&search='.urlencode($_GET['search']) : '' ?>">
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    </li>
                    <?php endif; ?>
                </ul>
            </nav>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Add Supplier Modal -->
<div class="modal fade" id="addSupplierModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header border-0">
                <h5 class="modal-title">Tambah Supplier</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="" method="POST" class="needs-validation" novalidate>
                <div class="modal-body">
                    <input type="hidden" name="action" value="add">

                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label class="form-label">Nama Supplier</label>
                                <input type="text" name="nama_supplier" class="form-control" required placeholder="Nama perusahaan atau supplier">
                                <div class="invalid-feedback">Nama supplier harus diisi</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Status</label>
                                <select name="status" class="form-select">
                                    <option value="aktif">Aktif</option>
                                    <option value="nonaktif">Non-Aktif</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Kontak/Telepon</label>
                                <input type="text" name="kontak" class="form-control" placeholder="Nomor telepon">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Email</label>
                                <input type="email" name="email" class="form-control" placeholder="<EMAIL>">
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Alamat</label>
                        <textarea name="alamat" class="form-control" rows="2" placeholder="Alamat lengkap supplier"></textarea>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Keterangan</label>
                        <textarea name="keterangan" class="form-control" rows="2" placeholder="Catatan tambahan tentang supplier"></textarea>
                    </div>
                </div>
                <div class="modal-footer border-0">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">Simpan Supplier</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Supplier Modal -->
<div class="modal fade" id="editSupplierModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header border-0">
                <h5 class="modal-title">Edit Supplier</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="" method="POST" class="needs-validation" novalidate>
                <div class="modal-body">
                    <input type="hidden" name="action" value="update">
                    <input type="hidden" name="id" id="edit_id">

                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label class="form-label">Nama Supplier</label>
                                <input type="text" name="nama_supplier" id="edit_nama_supplier" class="form-control" required>
                                <div class="invalid-feedback">Nama supplier harus diisi</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Status</label>
                                <select name="status" id="edit_status" class="form-select">
                                    <option value="aktif">Aktif</option>
                                    <option value="nonaktif">Non-Aktif</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Kontak/Telepon</label>
                                <input type="text" name="kontak" id="edit_kontak" class="form-control">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Email</label>
                                <input type="email" name="email" id="edit_email" class="form-control">
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Alamat</label>
                        <textarea name="alamat" id="edit_alamat" class="form-control" rows="2"></textarea>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Keterangan</label>
                        <textarea name="keterangan" id="edit_keterangan" class="form-control" rows="2"></textarea>
                    </div>
                </div>
                <div class="modal-footer border-0">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">Update Supplier</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Bulk Import Modal -->
<div class="modal fade" id="bulkImportModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header border-0">
                <h5 class="modal-title">Import Supplier</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label class="form-label">File Excel/CSV</label>
                    <input type="file" class="form-control" accept=".xlsx,.xls,.csv">
                    <small class="text-muted">Format: Excel (.xlsx, .xls) atau CSV</small>
                </div>

                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle me-2"></i>Format File:</h6>
                    <p class="mb-2">File harus memiliki kolom berikut:</p>
                    <ul class="mb-0">
                        <li>Nama Supplier</li>
                        <li>Kontak/Telepon</li>
                        <li>Email</li>
                        <li>Alamat</li>
                        <li>Status (aktif/nonaktif)</li>
                        <li>Keterangan</li>
                    </ul>
                </div>

                <div class="text-center">
                    <a href="#" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-download me-2"></i>Download Template
                    </a>
                </div>
            </div>
            <div class="modal-footer border-0">
                <button type="button" class="btn btn-light" data-bs-dismiss="modal">Batal</button>
                <button type="button" class="btn btn-success">Import Data</button>
            </div>
        </div>
    </div>
</div>

<!-- View Supplier Modal -->
<div class="modal fade" id="viewSupplierModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header border-0">
                <h5 class="modal-title">Detail Supplier</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-8">
                        <h4 id="view_nama_supplier" class="text-primary"></h4>
                        <p id="view_keterangan" class="text-muted"></p>
                    </div>
                    <div class="col-md-4 text-end">
                        <span id="view_status_badge" class="badge fs-6"></span>
                    </div>
                </div>

                <hr>

                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="fas fa-phone text-success me-2"></i>Kontak</h6>
                        <p id="view_kontak" class="mb-3"></p>

                        <h6><i class="fas fa-envelope text-info me-2"></i>Email</h6>
                        <p id="view_email" class="mb-3"></p>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="fas fa-map-marker-alt text-danger me-2"></i>Alamat</h6>
                        <p id="view_alamat" class="mb-3"></p>

                        <h6><i class="fas fa-calendar text-warning me-2"></i>Bergabung</h6>
                        <p id="view_created_at" class="mb-3"></p>
                    </div>
                </div>
            </div>
            <div class="modal-footer border-0">
                <button type="button" class="btn btn-light" data-bs-dismiss="modal">Tutup</button>
                <button type="button" class="btn btn-primary" onclick="editFromView()">
                    <i class="fas fa-edit me-2"></i>Edit Supplier
                </button>
            </div>
        </div>
    </div>
</div>

<script>
let currentSupplierData = null;

function editSupplier(id, nama, kontak, email, alamat, keterangan, status) {
    document.getElementById('edit_id').value = id;
    document.getElementById('edit_nama_supplier').value = nama;
    document.getElementById('edit_kontak').value = kontak;
    document.getElementById('edit_email').value = email;
    document.getElementById('edit_alamat').value = alamat;
    document.getElementById('edit_keterangan').value = keterangan;
    document.getElementById('edit_status').value = status;

    const modal = new bootstrap.Modal(document.getElementById('editSupplierModal'));
    modal.show();
}

function deleteSupplier(id) {
    // Show confirmation dialog with better styling
    if (confirm('⚠️ Apakah Anda yakin ingin menghapus supplier ini?\n\nData yang sudah dihapus tidak dapat dikembalikan.')) {
        // Create form element
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '/keuangan/supplier.php';

        // Create hidden inputs
        const actionInput = document.createElement('input');
        actionInput.type = 'hidden';
        actionInput.name = 'action';
        actionInput.value = 'delete';

        const idInput = document.createElement('input');
        idInput.type = 'hidden';
        idInput.name = 'id';
        idInput.value = id;

        // Append inputs to form
        form.appendChild(actionInput);
        form.appendChild(idInput);

        // Append form to body and submit
        document.body.appendChild(form);
        form.submit();
    }
}

function viewSupplier(supplier) {
    currentSupplierData = supplier;

    document.getElementById('view_nama_supplier').textContent = supplier.nama_supplier;
    document.getElementById('view_keterangan').textContent = supplier.keterangan || 'Tidak ada keterangan';

    const statusBadge = document.getElementById('view_status_badge');
    const status = supplier.status || 'aktif';
    statusBadge.textContent = status.charAt(0).toUpperCase() + status.slice(1);
    statusBadge.className = `badge fs-6 bg-${status === 'aktif' ? 'success' : 'secondary'}`;

    document.getElementById('view_kontak').innerHTML = supplier.kontak ?
        `<a href="tel:${supplier.kontak}" class="text-decoration-none">${supplier.kontak}</a>` :
        '<span class="text-muted">Tidak ada kontak</span>';

    document.getElementById('view_email').innerHTML = supplier.email ?
        `<a href="mailto:${supplier.email}" class="text-decoration-none">${supplier.email}</a>` :
        '<span class="text-muted">Tidak ada email</span>';

    document.getElementById('view_alamat').textContent = supplier.alamat || 'Tidak ada alamat';
    document.getElementById('view_created_at').textContent = formatTanggal(supplier.created_at);

    const modal = new bootstrap.Modal(document.getElementById('viewSupplierModal'));
    modal.show();
}

function editFromView() {
    if (currentSupplierData) {
        // Close view modal
        const viewModal = bootstrap.Modal.getInstance(document.getElementById('viewSupplierModal'));
        viewModal.hide();

        // Open edit modal
        setTimeout(() => {
            editSupplier(
                currentSupplierData.id,
                currentSupplierData.nama_supplier,
                currentSupplierData.kontak || '',
                currentSupplierData.email || '',
                currentSupplierData.alamat || '',
                currentSupplierData.keterangan || '',
                currentSupplierData.status || 'aktif'
            );
        }, 300);
    }
}

function toggleView(view) {
    const tableView = document.getElementById('tableView');
    const gridView = document.getElementById('gridView');
    const buttons = document.querySelectorAll('.btn-group .btn');

    buttons.forEach(btn => btn.classList.remove('active'));

    if (view === 'table') {
        tableView.style.display = 'block';
        gridView.style.display = 'none';
        buttons[0].classList.add('active');
    } else {
        tableView.style.display = 'none';
        gridView.style.display = 'block';
        buttons[1].classList.add('active');
    }
}

function exportSuppliers() {
    alert('Fitur export akan segera tersedia');
}

function printSuppliers() {
    window.print();
}

function formatTanggal(dateString) {
    if (!dateString) return '-';
    try {
        const date = new Date(dateString);
        if (isNaN(date.getTime())) return '-';
        const options = {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        };
        return date.toLocaleDateString('id-ID', options);
    } catch (e) {
        return '-';
    }
}
</script>

<style>
.bg-gradient-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
}

.bg-gradient-success {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
}

.bg-gradient-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #545b62 100%);
}

.bg-gradient-info {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
}

.text-truncate {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.table-responsive {
    border-radius: 0.375rem;
}

.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

@media (max-width: 768px) {
    .card-body .row .col-md-2 {
        margin-bottom: 0.5rem;
    }

    .btn-group {
        flex-direction: column;
    }

    .btn-group .btn {
        border-radius: 0.375rem !important;
        margin-bottom: 0.25rem;
    }

    .d-flex.gap-2 {
        flex-direction: column;
        gap: 0.5rem !important;
    }
}

.card-header {
    border-bottom: 1px solid rgba(0,0,0,.125);
}

.badge.fs-6 {
    font-size: 0.875rem !important;
    padding: 0.5rem 1rem;
}
</style>

        <!-- Modern Footer -->
        <div class="modern-footer">
            <div class="modern-footer-content">
                <div class="modern-footer-left">
                    <p class="modern-footer-text">
                        © <?= date('Y') ?> Sistem Keuangan Modern.
                        <span class="modern-text-primary">Supplier Management</span>
                    </p>
                </div>
                <div class="modern-footer-right">
                    <span class="modern-footer-version">v2.0.0</span>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/views/layouts/footer.php'; ?>
