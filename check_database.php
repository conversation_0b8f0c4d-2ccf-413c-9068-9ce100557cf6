<?php
require_once 'config/database.php';

try {
    // Drop existing tables if exists
    $pdo->exec("DROP TABLE IF EXISTS transaksi_produk");
    $pdo->exec("DROP TABLE IF EXISTS produk");
    $pdo->exec("DROP TABLE IF EXISTS kategori");
    $pdo->exec("DROP TABLE IF EXISTS transaksi");
    echo "Tabel lama berhasil dihapus!<br>";
    
    // Create kategori table
    $sql = "CREATE TABLE kategori (
        id INT PRIMARY KEY AUTO_INCREMENT,
        nama VARCHAR(50) NOT NULL,
        tipe ENUM('masuk', 'keluar') NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    $pdo->exec($sql);
    echo "Tabel kategori berhasil dibuat!<br>";

    // Insert default kategori
    $sql = "INSERT INTO kategori (nama, tipe) VALUES 
        ('Penjualan', 'masuk'),
        ('Pembelian', 'keluar'),
        ('Biaya Operasional', 'keluar'),
        ('Pendapatan Lainnya', 'masuk')";
    $pdo->exec($sql);
    echo "Data kategori berhasil ditambahkan!<br>";

    // Create produk table
    $sql = "CREATE TABLE produk (
        id INT PRIMARY KEY AUTO_INCREMENT,
        nama VARCHAR(100) NOT NULL,
        harga_beli DECIMAL(15,2) NOT NULL,
        harga_jual DECIMAL(15,2) NOT NULL,
        stok INT NOT NULL DEFAULT 0,
        user_id INT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    $pdo->exec($sql);
    echo "Tabel produk berhasil dibuat!<br>";

    // Insert sample data for produk
    $sql = "INSERT INTO produk (nama, harga_beli, harga_jual, stok, user_id) VALUES 
        ('Test Produk 1', 10000, 15000, 10, 1),
        ('Test Produk 2', 20000, 25000, 5, 1)";
    $pdo->exec($sql);
    echo "Data sample produk berhasil ditambahkan!<br>";

    // Create transaksi table
    $sql = "CREATE TABLE transaksi (
        id INT PRIMARY KEY AUTO_INCREMENT,
        user_id INT NOT NULL,
        kategori_id INT NOT NULL,
        jumlah DECIMAL(15,2) NOT NULL,
        keterangan TEXT,
        tanggal DATE NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (kategori_id) REFERENCES kategori(id) ON DELETE RESTRICT
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    $pdo->exec($sql);
    echo "Tabel transaksi berhasil dibuat!<br>";

    // Create transaksi_produk table
    $sql = "CREATE TABLE transaksi_produk (
        id INT PRIMARY KEY AUTO_INCREMENT,
        user_id INT NOT NULL,
        produk_id INT NOT NULL,
        jumlah INT NOT NULL,
        harga_jual DECIMAL(15,2) NOT NULL,
        total DECIMAL(15,2) NOT NULL,
        tanggal DATE NOT NULL,
        keterangan TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (produk_id) REFERENCES produk(id) ON DELETE RESTRICT,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    $pdo->exec($sql);
    echo "Tabel transaksi_produk berhasil dibuat!<br>";

    // Verify table structures
    $tables = ['kategori', 'produk', 'transaksi', 'transaksi_produk'];
    foreach ($tables as $table) {
        $stmt = $pdo->query("SHOW COLUMNS FROM $table");
        echo "<pre>Struktur tabel $table:\n";
        while($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            print_r($row);
        }
        echo "</pre>";
    }

    echo "Pemeriksaan database selesai!";
} catch(PDOException $e) {
    die("Error: " . $e->getMessage() . "<br>SQL State: " . $e->getCode());
}
?> 