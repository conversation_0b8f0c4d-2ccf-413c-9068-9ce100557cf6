<?php
/**
 * Helper Functions
 *
 * This file contains helper functions used throughout the application.
 */

require_once __DIR__ . '/../config/database.php';

// Fungsi untuk memformat angka ke format mata uang Rupiah
function formatRupiah($angka) {
    return 'Rp ' . number_format($angka, 0, ',', '.');
}

// Fungsi untuk memformat tanggal ke format Indonesia
function formatTanggal($tanggal) {
    if (empty($tanggal) || $tanggal === '0000-00-00' || $tanggal === '0000-00-00 00:00:00') {
        return '-';
    }

    try {
        $timestamp = strtotime($tanggal);
        if ($timestamp === false) {
            return '-';
        }
        return date('d F Y', $timestamp);
    } catch (Exception $e) {
        return '-';
    }
}

/**
 * Membersihkan input dari karakter yang tidak diinginkan
 * @param string $data Input yang akan dibersihkan
 * @return string
 */
function cleanInput($data) {
    if (is_array($data)) {
        return array_map('cleanInput', $data);
    }
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
    return $data;
}

// Fungsi untuk mengecek apakah request adalah AJAX
function isAjax() {
    return isset($_SERVER['HTTP_X_REQUESTED_WITH']) &&
           strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
}

/**
 * Get flash message
 * @return array|null
 */
function getFlashMessage() {
    if (!isset($_SESSION)) {
        session_start();
    }
    if (isset($_SESSION['flash_message'])) {
        $message = $_SESSION['flash_message'];
        unset($_SESSION['flash_message']);
        return $message;
    }
    return null;
}

/**
 * Set flash message
 * @param string $type Message type (success, error, warning, info)
 * @param string $message Message content
 */
function setFlashMessage($type, $message) {
    if (!isset($_SESSION)) {
        session_start();
    }
    $_SESSION['flash_message'] = [
        'type' => $type,
        'message' => $message
    ];
}

/**
 * Check if flash message exists
 * @return bool
 */
function hasFlashMessage() {
    if (!isset($_SESSION)) {
        session_start();
    }
    return isset($_SESSION['flash_message']);
}

/**
 * Display flash message
 * @return string
 */
function displayFlashMessage() {
    $message = getFlashMessage();
    if ($message) {
        return '<div class="alert alert-' . $message['type'] . ' alert-dismissible fade show" role="alert">' .
               $message['message'] .
               '<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>' .
               '</div>';
    }
    return '';
}

/**
 * Redirect to specified URL
 * @param string $url URL to redirect to
 */
function redirect($url) {
    header('Location: ' . $url);
    exit;
}

/**
 * Cek apakah user sudah login
 * @return bool
 */
function isLoggedIn() {
    if (!isset($_SESSION)) {
        session_start();
    }
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

/**
 * Get current user data
 * @return array|null
 */
function getCurrentUser() {
    global $pdo;

    if (!isLoggedIn()) {
        return null;
    }

    try {
        $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
        $stmt->execute([$_SESSION['user_id']]);
        $user = $stmt->fetch();

        // If user not found in database, clear the session
        if (!$user) {
            error_log("User ID {$_SESSION['user_id']} not found in database, clearing session");
            session_unset();
            session_destroy();
            return null;
        }

        return $user;
    } catch (PDOException $e) {
        error_log("Error getting current user: " . $e->getMessage());
        // Clear session on database error to prevent loops
        session_unset();
        session_destroy();
        return null;
    }
}

/**
 * Generate CSRF token
 * @return string
 */
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

/**
 * Validate CSRF token
 * @param string $token Token yang akan divalidasi
 * @return bool
 */
function validateCSRFToken($token) {
    if (!isset($_SESSION['csrf_token']) || $token !== $_SESSION['csrf_token']) {
        throw new Exception('Invalid CSRF token');
    }
    return true;
}

/**
 * Sanitize output
 * @param mixed $data Data yang akan disanitasi
 * @return mixed
 */
function sanitizeOutput($data) {
    if (is_array($data)) {
        return array_map('sanitizeOutput', $data);
    }
    return htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
}

/**
 * Execute database query
 * @param string $sql SQL query
 * @param array $params Parameters
 * @return PDOStatement
 */
function executeQuery($sql, $params = []) {
    global $pdo;
    try {
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        return $stmt;
    } catch (PDOException $e) {
        error_log("Database Error: " . $e->getMessage() . "\nSQL: " . $sql . "\nParams: " . print_r($params, true));
        throw new Exception("Database Error: " . $e->getMessage());
    }
}

/**
 * Validate input
 * @param array $data Data yang akan divalidasi
 * @param array $rules Rules validasi
 * @return array
 */
function validateInput($data, $rules) {
    $errors = [];
    foreach ($rules as $field => $rule) {
        $value = $data[$field] ?? null;

        // Required check
        if (strpos($rule, 'required') !== false && (empty($value) && $value !== '0')) {
            $errors[$field] = "Field ini wajib diisi";
            continue;
        }

        // Skip other validations if field is empty and not required
        if (empty($value) && strpos($rule, 'required') === false) {
            continue;
        }

        // Email validation
        if (strpos($rule, 'email') !== false && !filter_var($value, FILTER_VALIDATE_EMAIL)) {
            $errors[$field] = "Format email tidak valid";
        }

        // Numeric validation
        if (strpos($rule, 'numeric') !== false && !is_numeric($value)) {
            $errors[$field] = "Field ini harus berupa angka";
        }

        // Min length validation
        if (preg_match('/min:(\d+)/', $rule, $matches)) {
            $min = $matches[1];
            if (strlen($value) < $min) {
                $errors[$field] = "Minimal {$min} karakter";
            }
        }

        // Max length validation
        if (preg_match('/max:(\d+)/', $rule, $matches)) {
            $max = $matches[1];
            if (strlen($value) > $max) {
                $errors[$field] = "Maksimal {$max} karakter";
            }
        }
    }
    return $errors;
}

/**
 * Upload file
 * @param array $file File yang akan diupload
 * @param string $target_dir Direktori tujuan
 * @param array $allowed_types Tipe file yang diizinkan
 * @param int $max_size Ukuran maksimal file (dalam bytes)
 * @return string
 */
function uploadFile($file, $target_dir, $allowed_types = ['jpg', 'jpeg', 'png'], $max_size = 5242880) {
    // Validasi file
    if (!isset($file['error']) || is_array($file['error'])) {
        throw new Exception('Invalid file parameter');
    }

    // Cek error upload
    switch ($file['error']) {
        case UPLOAD_ERR_OK:
            break;
        case UPLOAD_ERR_INI_SIZE:
        case UPLOAD_ERR_FORM_SIZE:
            throw new Exception('File terlalu besar');
        case UPLOAD_ERR_PARTIAL:
            throw new Exception('File hanya terupload sebagian');
        case UPLOAD_ERR_NO_FILE:
            throw new Exception('Tidak ada file yang diupload');
        case UPLOAD_ERR_NO_TMP_DIR:
            throw new Exception('Folder temporary tidak ditemukan');
        case UPLOAD_ERR_CANT_WRITE:
            throw new Exception('Gagal menulis file ke disk');
        case UPLOAD_ERR_EXTENSION:
            throw new Exception('Upload dihentikan oleh ekstensi PHP');
        default:
            throw new Exception('Unknown upload error');
    }

    // Validasi ukuran
    if ($file['size'] > $max_size) {
        throw new Exception('File terlalu besar. Maksimal ' . formatFileSize($max_size));
    }

    // Validasi tipe file
    $finfo = new finfo(FILEINFO_MIME_TYPE);
    $mime_type = $finfo->file($file['tmp_name']);
    $ext = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));

    if (!in_array($ext, $allowed_types)) {
        throw new Exception('Tipe file tidak diizinkan');
    }

    // Generate nama file unik
    $filename = uniqid() . '.' . $ext;
    $target_path = $target_dir . '/' . $filename;

    // Pastikan direktori target ada
    if (!is_dir($target_dir)) {
        mkdir($target_dir, 0755, true);
    }

    // Pindahkan file
    if (!move_uploaded_file($file['tmp_name'], $target_path)) {
        throw new Exception('Gagal memindahkan file');
    }

    return $filename;
}

/**
 * Format file size
 * @param int $bytes Ukuran dalam bytes
 * @return string
 */
function formatFileSize($bytes) {
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];
    $bytes = max($bytes, 0);
    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
    $pow = min($pow, count($units) - 1);
    $bytes /= pow(1024, $pow);
    return round($bytes, 2) . ' ' . $units[$pow];
}

/**
 * Format bytes to human readable format (alias for formatFileSize)
 * @param int $bytes Ukuran dalam bytes
 * @param int $precision Precision for decimal places
 * @return string
 */
function formatBytes($bytes, $precision = 2) {
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];
    $bytes = max($bytes, 0);
    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
    $pow = min($pow, count($units) - 1);
    $bytes /= pow(1024, $pow);
    return round($bytes, $precision) . ' ' . $units[$pow];
}

/**
 * Check if the current page matches the given page
 */
function isCurrentPage($page) {
    $current_page = basename($_SERVER['PHP_SELF']);
    return $current_page === $page;
}

/**
 * Mencatat aktivitas user ke database
 *
 * @param int $user_id ID user yang melakukan aktivitas
 * @param string $activity Deskripsi aktivitas
 * @param string $type Tipe aktivitas (login, logout, create, update, delete)
 * @return bool
 */
function logActivity($user_id, $activity, $type = 'other') {
    global $pdo;

    try {
        // Cek apakah tabel activity_log sudah ada
        $stmt = $pdo->query("SHOW TABLES LIKE 'activity_log'");
        if ($stmt->rowCount() == 0) {
            // Buat tabel activity_log jika belum ada
            $sql = "CREATE TABLE IF NOT EXISTS activity_log (
                id INT PRIMARY KEY AUTO_INCREMENT,
                user_id INT NOT NULL,
                activity TEXT NOT NULL,
                type VARCHAR(50) NOT NULL,
                ip_address VARCHAR(45),
                user_agent TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            $pdo->exec($sql);
        }

        // Insert log aktivitas
        $stmt = $pdo->prepare("
            INSERT INTO activity_log (user_id, activity, type, ip_address, user_agent)
            VALUES (?, ?, ?, ?, ?)
        ");

        return $stmt->execute([
            $user_id,
            $activity,
            $type,
            $_SERVER['REMOTE_ADDR'] ?? null,
            $_SERVER['HTTP_USER_AGENT'] ?? null
        ]);

    } catch (PDOException $e) {
        error_log("Error logging activity: " . $e->getMessage());
        return false;
    }
}

/**
 * Format currency to Indonesian Rupiah format
 * @param float $amount Amount to format
 * @return string Formatted currency string
 */
function formatCurrency($amount) {
    return 'Rp ' . number_format($amount, 0, ',', '.');
}

/**
 * Format date to Indonesian format
 * @param string $date Date to format
 * @param string $format Format output (default: d F Y)
 * @return string Formatted date string
 */
function formatDate($date, $format = 'd F Y') {
    return date($format, strtotime($date));
}

/**
 * Format datetime to Indonesian format
 * @param string $datetime Datetime to format
 * @param string $format Format output (default: d F Y H:i)
 * @return string Formatted datetime string
 */
function formatDateTime($datetime, $format = 'd F Y H:i') {
    return date($format, strtotime($datetime));
}

/**
 * Get total income
 * @return float
 */
function getTotalPemasukan() {
    global $pdo;
    try {
        $stmt = $pdo->prepare("
            SELECT COALESCE(SUM(t.jumlah), 0) as total
            FROM transaksi t
            JOIN kategori k ON t.kategori_id = k.id
            WHERE t.user_id = ? AND k.tipe = 'pemasukan'
        ");
        $stmt->execute([$_SESSION['user_id']]);
        $result = $stmt->fetch();
        return $result['total'] ?? 0;
    } catch (PDOException $e) {
        error_log("Error getting total pemasukan: " . $e->getMessage());
        return 0;
    }
}

/**
 * Get total expenses
 * @return float
 */
function getTotalPengeluaran() {
    global $pdo;
    try {
        $stmt = $pdo->prepare("
            SELECT COALESCE(SUM(t.jumlah), 0) as total
            FROM transaksi t
            JOIN kategori k ON t.kategori_id = k.id
            WHERE t.user_id = ? AND k.tipe = 'pengeluaran'
        ");
        $stmt->execute([$_SESSION['user_id']]);
        $result = $stmt->fetch();
        return $result['total'] ?? 0;
    } catch (PDOException $e) {
        error_log("Error getting total pengeluaran: " . $e->getMessage());
        return 0;
    }
}

/**
 * Get recent transactions
 * @param int $limit Number of transactions to get
 * @return array
 */
function getTransaksiTerakhir($limit = 5) {
    global $pdo;
    try {
        $stmt = $pdo->prepare("
            SELECT t.*, k.nama as nama_kategori, k.tipe
            FROM transaksi t
            JOIN kategori k ON t.kategori_id = k.id
            WHERE t.user_id = ?
            ORDER BY t.tanggal DESC, t.created_at DESC
            LIMIT ?
        ");
        $stmt->execute([$_SESSION['user_id'], $limit]);
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        error_log("Error getting recent transactions: " . $e->getMessage());
        return [];
    }
}

/**
 * Get top categories
 * @param int $limit Number of categories to get
 * @return array
 */
function getKategoriTerbanyak($limit = 5) {
    global $pdo;
    try {
        $stmt = $pdo->prepare("
            SELECT
                k.nama as nama_kategori,
                COUNT(t.id) as total_transaksi,
                COALESCE(SUM(t.jumlah), 0) as total_jumlah
            FROM kategori k
            LEFT JOIN transaksi t ON k.id = t.kategori_id AND t.user_id = ?
            WHERE k.user_id = ?
            GROUP BY k.id
            ORDER BY total_transaksi DESC
            LIMIT ?
        ");
        $stmt->execute([$_SESSION['user_id'], $_SESSION['user_id'], $limit]);
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        error_log("Error getting top categories: " . $e->getMessage());
        return [];
    }
}

/**
 * Get nearest targets
 * @param int $limit Number of targets to get
 * @return array
 */
function getTargetTerdekat($limit = 3) {
    global $pdo;
    try {
        $stmt = $pdo->prepare("
            SELECT
                nama as nama_target,
                jumlah_target as target_jumlah,
                jumlah_terkumpul as terkumpul,
                tanggal_selesai
            FROM target
            WHERE user_id = ? AND status = 'aktif'
            ORDER BY tanggal_selesai ASC
            LIMIT ?
        ");
        $stmt->execute([$_SESSION['user_id'], $limit]);
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        error_log("Error getting nearest targets: " . $e->getMessage());
        return [];
    }
}

/**
 * Get today's sales
 * @return float
 */
function getPenjualanHariIni() {
    global $pdo;
    try {
        $stmt = $pdo->prepare("
            SELECT COALESCE(SUM(tp.jumlah * tp.harga_jual), 0) as total
            FROM transaksi_produk tp
            JOIN produk p ON tp.produk_id = p.id
            WHERE tp.user_id = ? AND DATE(tp.tanggal) = CURDATE()
        ");
        $stmt->execute([$_SESSION['user_id']]);
        $result = $stmt->fetch();
        return $result['total'] ?? 0;
    } catch (PDOException $e) {
        error_log("Error getting today's sales: " . $e->getMessage());
        return 0;
    }
}

/**
 * Get this week's sales
 * @return float
 */
function getPenjualanMingguIni() {
    global $pdo;
    try {
        $stmt = $pdo->prepare("
            SELECT COALESCE(SUM(tp.jumlah * tp.harga_jual), 0) as total
            FROM transaksi_produk tp
            JOIN produk p ON tp.produk_id = p.id
            WHERE tp.user_id = ?
            AND tp.tanggal >= DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) DAY)
            AND tp.tanggal < DATE_ADD(DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) DAY), INTERVAL 7 DAY)
        ");
        $stmt->execute([$_SESSION['user_id']]);
        $result = $stmt->fetch();
        return $result['total'] ?? 0;
    } catch (PDOException $e) {
        error_log("Error getting this week's sales: " . $e->getMessage());
        return 0;
    }
}

/**
 * Get this month's sales
 * @return float
 */
function getPenjualanBulanIni() {
    global $pdo;
    try {
        $stmt = $pdo->prepare("
            SELECT COALESCE(SUM(tp.jumlah * tp.harga_jual), 0) as total
            FROM transaksi_produk tp
            JOIN produk p ON tp.produk_id = p.id
            WHERE tp.user_id = ?
            AND MONTH(tp.tanggal) = MONTH(CURRENT_DATE())
            AND YEAR(tp.tanggal) = YEAR(CURRENT_DATE())
        ");
        $stmt->execute([$_SESSION['user_id']]);
        $result = $stmt->fetch();
        return $result['total'] ?? 0;
    } catch (PDOException $e) {
        error_log("Error getting this month's sales: " . $e->getMessage());
        return 0;
    }
}

/**
 * Format Rupiah to short format (K, M, B)
 * @param float $amount Amount to format
 * @return string Formatted short currency string
 */
function formatRupiahShort($amount) {
    if ($amount == 0) return 'Rp 0';

    $units = ['', 'K', 'M', 'B', 'T'];
    $unitIndex = 0;

    while ($amount >= 1000 && $unitIndex < count($units) - 1) {
        $amount /= 1000;
        $unitIndex++;
    }

    if ($unitIndex == 0) {
        return 'Rp ' . number_format($amount, 0, ',', '.');
    } else {
        return 'Rp ' . number_format($amount, 1, ',', '.') . $units[$unitIndex];
    }
}

/**
 * Get system statistics for admin dashboard
 * @return array
 */
function getSystemStats() {
    global $pdo;
    try {
        $stats = [];

        // Total users
        $stmt = $pdo->query("SELECT COUNT(*) as total FROM users");
        $stats['total_users'] = $stmt->fetchColumn();

        // Total transactions today
        $stmt = $pdo->query("SELECT COUNT(*) as total FROM transaksi WHERE DATE(created_at) = CURDATE()");
        $stats['transactions_today'] = $stmt->fetchColumn();

        // Total revenue this month
        $stmt = $pdo->prepare("
            SELECT COALESCE(SUM(t.jumlah), 0) as total
            FROM transaksi t
            JOIN kategori k ON t.kategori_id = k.id
            WHERE k.tipe = 'pemasukan'
            AND MONTH(t.tanggal) = MONTH(CURRENT_DATE())
            AND YEAR(t.tanggal) = YEAR(CURRENT_DATE())
        ");
        $stmt->execute();
        $stats['revenue_month'] = $stmt->fetchColumn();

        // Active users (logged in last 7 days)
        $stmt = $pdo->query("SELECT COUNT(*) as total FROM users WHERE last_login >= DATE_SUB(NOW(), INTERVAL 7 DAY)");
        $stats['active_users'] = $stmt->fetchColumn();

        return $stats;
    } catch (PDOException $e) {
        error_log("Error getting system stats: " . $e->getMessage());
        return [
            'total_users' => 0,
            'transactions_today' => 0,
            'revenue_month' => 0,
            'active_users' => 0
        ];
    }
}

/**
 * Get disk usage percentage
 * @return float
 */
function getDiskUsage() {
    $totalBytes = disk_total_space('.');
    $freeBytes = disk_free_space('.');
    $usedBytes = $totalBytes - $freeBytes;
    return round(($usedBytes / $totalBytes) * 100, 2);
}

/**
 * Get memory usage percentage
 * @return float
 */
function getMemoryUsage() {
    $memoryLimit = ini_get('memory_limit');
    $memoryLimitBytes = convertToBytes($memoryLimit);
    $memoryUsage = memory_get_usage(true);
    return round(($memoryUsage / $memoryLimitBytes) * 100, 2);
}

/**
 * Convert memory limit string to bytes
 * @param string $val Memory limit string (e.g., "128M")
 * @return int
 */
function convertToBytes($val) {
    $val = trim($val);
    $last = strtolower($val[strlen($val)-1]);
    $val = (int) $val;
    switch($last) {
        case 'g':
            $val *= 1024;
        case 'm':
            $val *= 1024;
        case 'k':
            $val *= 1024;
    }
    return $val;
}

/**
 * Generate random color for charts
 * @return string
 */
function generateRandomColor() {
    return sprintf('#%06X', mt_rand(0, 0xFFFFFF));
}

/**
 * Validate date format
 * @param string $date Date string
 * @param string $format Expected format
 * @return bool
 */
function validateDate($date, $format = 'Y-m-d') {
    $d = DateTime::createFromFormat($format, $date);
    return $d && $d->format($format) === $date;
}

/**
 * Get user's timezone
 * @return string
 */
function getUserTimezone() {
    return $_SESSION['timezone'] ?? 'Asia/Jakarta';
}

/**
 * Convert UTC time to user timezone
 * @param string $utcTime UTC time string
 * @return string
 */
function convertToUserTimezone($utcTime) {
    $utc = new DateTime($utcTime, new DateTimeZone('UTC'));
    $utc->setTimezone(new DateTimeZone(getUserTimezone()));
    return $utc->format('Y-m-d H:i:s');
}

/**
 * Get browser information
 * @return array
 */
function getBrowserInfo() {
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    $browser = 'Unknown';
    $version = '';

    if (preg_match('/MSIE/i', $userAgent) && !preg_match('/Opera/i', $userAgent)) {
        $browser = 'Internet Explorer';
    } elseif (preg_match('/Firefox/i', $userAgent)) {
        $browser = 'Firefox';
    } elseif (preg_match('/Chrome/i', $userAgent)) {
        $browser = 'Chrome';
    } elseif (preg_match('/Safari/i', $userAgent)) {
        $browser = 'Safari';
    } elseif (preg_match('/Opera/i', $userAgent)) {
        $browser = 'Opera';
    }

    return ['browser' => $browser, 'user_agent' => $userAgent];
}

/**
 * Check if user has permission for specific action
 * @param string $permission Permission name
 * @return bool
 */
function hasPermission($permission) {
    $currentUser = getCurrentUser();
    if (!$currentUser) return false;

    // Admin has all permissions
    if ($currentUser['role'] === 'admin') return true;

    // Check specific permissions (can be extended)
    $userPermissions = $_SESSION['permissions'] ?? [];
    return in_array($permission, $userPermissions);
}

/**
 * Log system event
 * @param string $event Event description
 * @param string $level Log level (info, warning, error)
 * @param array $context Additional context
 * @return bool
 */
function logSystemEvent($event, $level = 'info', $context = []) {
    global $pdo;

    try {
        // Create system_logs table if not exists
        $pdo->exec("CREATE TABLE IF NOT EXISTS system_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            event TEXT NOT NULL,
            level ENUM('info', 'warning', 'error') DEFAULT 'info',
            context JSON,
            ip_address VARCHAR(45),
            user_agent TEXT,
            user_id INT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
        )");

        $currentUser = getCurrentUser();
        $stmt = $pdo->prepare("
            INSERT INTO system_logs (event, level, context, ip_address, user_agent, user_id)
            VALUES (?, ?, ?, ?, ?, ?)
        ");

        return $stmt->execute([
            $event,
            $level,
            json_encode($context),
            $_SERVER['REMOTE_ADDR'] ?? null,
            $_SERVER['HTTP_USER_AGENT'] ?? null,
            $currentUser['id'] ?? null
        ]);

    } catch (PDOException $e) {
        error_log("Error logging system event: " . $e->getMessage());
        return false;
    }
}


