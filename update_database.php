<?php
require_once 'includes/config/database.php';
require_once 'includes/helpers/functions.php';

// Check admin access
$currentUser = getCurrentUser();
if (!$currentUser || $currentUser['role'] !== 'admin') {
    setFlashMessage('danger', 'Aks<PERSON> ditolak! Anda tidak memiliki izin untuk mengakses halaman ini.');
    redirect('dashboard.php');
}

$currentPage = 'update_database';
$pageTitle = 'Update Database';

// Ensure required tables exist first
try {
    // Create system_notifications table if not exists
    $pdo->exec("CREATE TABLE IF NOT EXISTS system_notifications (
        id INT AUTO_INCREMENT PRIMARY KEY,
        type ENUM('error', 'warning', 'info', 'success') DEFAULT 'info',
        title VARCHAR(255) NOT NULL,
        message TEXT NOT NULL,
        source VARCHAR(100),
        user_id INT,
        is_read BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_type (type),
        INDEX idx_created_at (created_at),
        INDEX idx_is_read (is_read)
    )");

    // Create database_versions table if not exists
    $pdo->exec("CREATE TABLE IF NOT EXISTS database_versions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        version VARCHAR(20) NOT NULL UNIQUE,
        description TEXT,
        applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        applied_by INT,
        INDEX idx_version (version),
        FOREIGN KEY (applied_by) REFERENCES users(id) ON DELETE SET NULL
    )");
} catch (PDOException $e) {
    error_log("Error creating required tables: " . $e->getMessage());
}

// Function to add notification
function addNotification($type, $title, $message, $source = 'update_database') {
    global $pdo, $currentUser;
    try {
        $stmt = $pdo->prepare("INSERT INTO system_notifications (type, title, message, source, user_id) VALUES (?, ?, ?, ?, ?)");
        $stmt->execute([$type, $title, $message, $source, $currentUser['id']]);
    } catch (Exception $e) {
        error_log("Error adding notification: " . $e->getMessage());
    }
}

// Database migrations/updates
$migrations = [
    'v1.0.1' => [
        'description' => 'Add system notifications table',
        'sql' => "CREATE TABLE IF NOT EXISTS system_notifications (
            id INT AUTO_INCREMENT PRIMARY KEY,
            type ENUM('error', 'warning', 'info', 'success') DEFAULT 'info',
            title VARCHAR(255) NOT NULL,
            message TEXT NOT NULL,
            source VARCHAR(100),
            user_id INT,
            is_read BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_type (type),
            INDEX idx_created_at (created_at),
            INDEX idx_is_read (is_read)
        )"
    ],
    'v1.0.2' => [
        'description' => 'Add user menu permissions table',
        'sql' => "CREATE TABLE IF NOT EXISTS user_menu_permissions (
            user_id INT,
            menu_id VARCHAR(50),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (user_id, menu_id),
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        )"
    ],
    'v1.0.3' => [
        'description' => 'Add role menu access table',
        'sql' => "CREATE TABLE IF NOT EXISTS role_menu_access (
            role_id INT,
            menu_id VARCHAR(50),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (role_id, menu_id),
            FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE
        )"
    ],
    'v1.0.4' => [
        'description' => 'Add database version tracking',
        'sql' => "CREATE TABLE IF NOT EXISTS database_versions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            version VARCHAR(20) NOT NULL UNIQUE,
            description TEXT,
            applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            applied_by INT,
            FOREIGN KEY (applied_by) REFERENCES users(id)
        )"
    ],
    'v1.0.5' => [
        'description' => 'Add system logs table',
        'sql' => "CREATE TABLE IF NOT EXISTS system_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            level ENUM('debug', 'info', 'warning', 'error', 'critical') DEFAULT 'info',
            message TEXT NOT NULL,
            context JSON,
            user_id INT,
            ip_address VARCHAR(45),
            user_agent TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_level (level),
            INDEX idx_created_at (created_at),
            INDEX idx_user_id (user_id),
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
        )"
    ]
];

// Handle update actions
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    try {
        switch ($_POST['action']) {
            case 'run_migration':
                $version = $_POST['version'] ?? '';
                if (isset($migrations[$version])) {
                    $migration = $migrations[$version];

                    // Check if already applied
                    $isApplied = false;
                    try {
                        $stmt = $pdo->query("SHOW TABLES LIKE 'database_versions'");
                        if ($stmt->rowCount() > 0) {
                            $stmt = $pdo->prepare("SELECT COUNT(*) FROM database_versions WHERE version = ?");
                            $stmt->execute([$version]);
                            $isApplied = $stmt->fetchColumn() > 0;
                        }
                    } catch (Exception $e) {
                        // Table doesn't exist, migration not applied
                        $isApplied = false;
                    }

                    if (!$isApplied) {
                        // Run migration
                        $pdo->exec($migration['sql']);

                        // Record migration
                        $stmt = $pdo->prepare("INSERT INTO database_versions (version, description, applied_by) VALUES (?, ?, ?)");
                        $stmt->execute([$version, $migration['description'], $currentUser['id']]);

                        addNotification('success', 'Migration Applied', "Successfully applied migration $version: {$migration['description']}");
                        setFlashMessage('success', "Migration $version berhasil diterapkan");
                    } else {
                        setFlashMessage('warning', "Migration $version sudah pernah diterapkan");
                    }
                }
                break;

            case 'run_all_migrations':
                $applied = 0;
                foreach ($migrations as $version => $migration) {
                    // Check if already applied
                    $isApplied = false;
                    try {
                        $stmt = $pdo->query("SHOW TABLES LIKE 'database_versions'");
                        if ($stmt->rowCount() > 0) {
                            $stmt = $pdo->prepare("SELECT COUNT(*) FROM database_versions WHERE version = ?");
                            $stmt->execute([$version]);
                            $isApplied = $stmt->fetchColumn() > 0;
                        }
                    } catch (Exception $e) {
                        // Table doesn't exist, migration not applied
                        $isApplied = false;
                    }

                    if (!$isApplied) {
                        try {
                            // Run migration
                            $pdo->exec($migration['sql']);

                            // Record migration
                            $stmt = $pdo->prepare("INSERT INTO database_versions (version, description, applied_by) VALUES (?, ?, ?)");
                            $stmt->execute([$version, $migration['description'], $currentUser['id']]);

                            $applied++;
                        } catch (Exception $e) {
                            error_log("Migration $version failed: " . $e->getMessage());
                        }
                    }
                }

                addNotification('success', 'Bulk Migration Completed', "Applied $applied new migrations");
                setFlashMessage('success', "Berhasil menerapkan $applied migration baru");
                break;

            case 'optimize_database':
                // Optimize tables
                $stmt = $pdo->query("SHOW TABLES");
                $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);

                $optimized = 0;
                foreach ($tables as $table) {
                    try {
                        $pdo->exec("OPTIMIZE TABLE `$table`");
                        $optimized++;
                    } catch (Exception $e) {
                        error_log("Failed to optimize table $table: " . $e->getMessage());
                    }
                }

                addNotification('success', 'Database Optimized', "Optimized $optimized tables");
                setFlashMessage('success', "Berhasil mengoptimasi $optimized tabel");
                break;

            case 'analyze_database':
                // Analyze tables
                $stmt = $pdo->query("SHOW TABLES");
                $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);

                $analyzed = 0;
                foreach ($tables as $table) {
                    try {
                        $pdo->exec("ANALYZE TABLE `$table`");
                        $analyzed++;
                    } catch (Exception $e) {
                        error_log("Failed to analyze table $table: " . $e->getMessage());
                    }
                }

                addNotification('success', 'Database Analyzed', "Analyzed $analyzed tables");
                setFlashMessage('success', "Berhasil menganalisis $analyzed tabel");
                break;

            case 'repair_database':
                // Repair tables
                $stmt = $pdo->query("SHOW TABLES");
                $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);

                $repaired = 0;
                foreach ($tables as $table) {
                    try {
                        $pdo->exec("REPAIR TABLE `$table`");
                        $repaired++;
                    } catch (Exception $e) {
                        error_log("Failed to repair table $table: " . $e->getMessage());
                    }
                }

                addNotification('success', 'Database Repaired', "Repaired $repaired tables");
                setFlashMessage('success', "Berhasil memperbaiki $repaired tabel");
                break;
        }
        redirect('update_database.php');
    } catch (Exception $e) {
        addNotification('error', 'Database Update Error', 'Error during database update: ' . $e->getMessage());
        setFlashMessage('danger', 'Error: ' . $e->getMessage());
    }
}

// Get current database info
$dbInfo = [];

// Get database version
try {
    $stmt = $pdo->query("SELECT VERSION() as version");
    $dbInfo['mysql_version'] = $stmt->fetchColumn();
} catch (Exception $e) {
    $dbInfo['mysql_version'] = 'Unknown';
}

// Get database size
try {
    $stmt = $pdo->query("SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS size_mb FROM information_schema.tables WHERE table_schema = DATABASE()");
    $dbInfo['size_mb'] = $stmt->fetchColumn() ?: 0;
} catch (Exception $e) {
    $dbInfo['size_mb'] = 'Unknown';
}

// Get table count
try {
    $stmt = $pdo->query("SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE()");
    $dbInfo['table_count'] = $stmt->fetchColumn();
} catch (Exception $e) {
    $dbInfo['table_count'] = 'Unknown';
}

// Get applied migrations
$appliedMigrations = [];
try {
    // Check if table exists first
    $stmt = $pdo->query("SHOW TABLES LIKE 'database_versions'");
    if ($stmt->rowCount() > 0) {
        $stmt = $pdo->query("SELECT version, description, applied_at FROM database_versions ORDER BY applied_at DESC");
        $appliedMigrations = $stmt->fetchAll();
    }
} catch (Exception $e) {
    // Table might not exist yet
    error_log("Error getting applied migrations: " . $e->getMessage());
}

include 'includes/views/layouts/header.php';
?>

<div class="container-fluid px-4 py-3">
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-gradient-warning text-dark py-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h5 class="card-title mb-0">
                                <i class="fas fa-database me-2"></i>
                                Update Database
                            </h5>
                            <p class="mb-0 small opacity-75">Kelola migrasi dan optimasi database</p>
                        </div>
                        <div class="d-flex gap-2">
                            <a href="notifications.php" class="btn btn-dark btn-sm">
                                <i class="fas fa-bell me-1"></i>Notifications
                            </a>
                            <a href="fix_errors.php" class="btn btn-dark btn-sm">
                                <i class="fas fa-wrench me-1"></i>Fix Errors
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body p-4">

                    <!-- Database Info -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="mb-3">
                                <i class="fas fa-info-circle me-2"></i>Database Information
                            </h6>
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="card bg-primary text-white">
                                        <div class="card-body text-center py-3">
                                            <h5 class="mb-1"><?= $dbInfo['mysql_version'] ?></h5>
                                            <small>MySQL Version</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card bg-success text-white">
                                        <div class="card-body text-center py-3">
                                            <h5 class="mb-1"><?= $dbInfo['size_mb'] ?> MB</h5>
                                            <small>Database Size</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card bg-info text-white">
                                        <div class="card-body text-center py-3">
                                            <h5 class="mb-1"><?= $dbInfo['table_count'] ?></h5>
                                            <small>Total Tables</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card bg-secondary text-white">
                                        <div class="card-body text-center py-3">
                                            <h5 class="mb-1"><?= count($appliedMigrations) ?></h5>
                                            <small>Applied Migrations</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Database Operations -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card border-warning">
                                <div class="card-header bg-warning text-dark">
                                    <h6 class="mb-0">
                                        <i class="fas fa-cogs me-2"></i>Database Operations
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <form method="POST" class="d-grid gap-2">
                                        <button type="submit" name="action" value="optimize_database" class="btn btn-outline-success btn-sm">
                                            <i class="fas fa-tachometer-alt me-2"></i>Optimize Database
                                        </button>
                                        <button type="submit" name="action" value="analyze_database" class="btn btn-outline-info btn-sm">
                                            <i class="fas fa-chart-line me-2"></i>Analyze Tables
                                        </button>
                                        <button type="submit" name="action" value="repair_database" class="btn btn-outline-warning btn-sm">
                                            <i class="fas fa-tools me-2"></i>Repair Tables
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border-primary">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-rocket me-2"></i>Migration Actions
                                    </h6>
                                </div>
                                <div class="card-body text-center">
                                    <p class="mb-3">Terapkan semua migrasi yang belum dijalankan</p>
                                    <form method="POST" onsubmit="return confirm('Yakin ingin menjalankan semua migrasi?')">
                                        <button type="submit" name="action" value="run_all_migrations" class="btn btn-primary">
                                            <i class="fas fa-rocket me-2"></i>Run All Migrations
                                        </button>
                                    </form>
                                    <small class="text-muted d-block mt-2">
                                        Ini akan menjalankan semua migrasi yang belum diterapkan
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Available Migrations -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="mb-3">
                                <i class="fas fa-list me-2"></i>Available Migrations
                            </h6>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Version</th>
                                            <th>Description</th>
                                            <th>Status</th>
                                            <th>Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($migrations as $version => $migration): ?>
                                            <?php
                                            $isApplied = false;
                                            foreach ($appliedMigrations as $applied) {
                                                if ($applied['version'] === $version) {
                                                    $isApplied = true;
                                                    break;
                                                }
                                            }
                                            ?>
                                            <tr>
                                                <td><code><?= $version ?></code></td>
                                                <td><?= $migration['description'] ?></td>
                                                <td>
                                                    <?php if ($isApplied): ?>
                                                        <span class="badge bg-success">Applied</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-warning">Pending</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php if (!$isApplied): ?>
                                                        <form method="POST" class="d-inline">
                                                            <input type="hidden" name="action" value="run_migration">
                                                            <input type="hidden" name="version" value="<?= $version ?>">
                                                            <button type="submit" class="btn btn-sm btn-outline-primary" onclick="return confirm('Apply migration <?= $version ?>?')">
                                                                <i class="fas fa-play me-1"></i>Apply
                                                            </button>
                                                        </form>
                                                    <?php else: ?>
                                                        <span class="text-muted">-</span>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Applied Migrations History -->
                    <?php if (!empty($appliedMigrations)): ?>
                        <div class="row">
                            <div class="col-12">
                                <h6 class="mb-3">
                                    <i class="fas fa-history me-2"></i>Migration History
                                </h6>
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>Version</th>
                                                <th>Description</th>
                                                <th>Applied At</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($appliedMigrations as $migration): ?>
                                                <tr>
                                                    <td><code><?= $migration['version'] ?></code></td>
                                                    <td><?= $migration['description'] ?></td>
                                                    <td><?= date('d/m/Y H:i', strtotime($migration['applied_at'])) ?></td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Back to Notifications -->
                    <div class="mt-4 text-center">
                        <a href="notifications.php" class="btn btn-outline-primary">
                            <i class="fas fa-arrow-left me-2"></i>Back to Notifications
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/views/layouts/footer.php'; ?>
