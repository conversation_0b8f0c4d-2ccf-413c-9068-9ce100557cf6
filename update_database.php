<?php
require_once 'includes/config/database.php';
require_once 'includes/helpers/function_check.php';
require_once 'includes/helpers/functions.php';

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in and is admin
if (!isLoggedIn()) {
    setFlashMessage('danger', 'Silakan login terlebih dahulu');
    redirect('login.php');
}

$currentUser = getCurrentUser();
if ($currentUser['role'] !== 'admin') {
    setFlashMessage('danger', 'Akses ditolak. Hanya admin yang dapat mengakses halaman ini.');
    redirect('dashboard.php');
}

$currentPage = 'update_database';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'run_migration':
                $migrationFile = $_POST['migration_file'] ?? '';
                $result = runDatabaseMigration($migrationFile);
                if ($result['success']) {
                    setFlashMessage('success', $result['message']);
                } else {
                    setFlashMessage('danger', $result['message']);
                }
                break;
                
            case 'create_backup_before_update':
                $result = createBackupBeforeUpdate();
                if ($result['success']) {
                    setFlashMessage('success', $result['message']);
                } else {
                    setFlashMessage('danger', $result['message']);
                }
                break;
                
            case 'run_all_migrations':
                $result = runAllPendingMigrations();
                if ($result['success']) {
                    setFlashMessage('success', $result['message']);
                } else {
                    setFlashMessage('danger', $result['message']);
                }
                break;
        }
        redirect('update_database.php');
    }
}

// Get database version info
$currentVersion = getCurrentDatabaseVersion();
$availableMigrations = getAvailableMigrations();
$migrationHistory = getMigrationHistory();

// Include header and sidebar
require_once 'includes/views/layouts/header.php';
require_once 'includes/views/layouts/sidebar.php';

/**
 * Get current database version
 */
function getCurrentDatabaseVersion() {
    global $pdo;
    
    try {
        // Create database_versions table if not exists
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS database_versions (
                id INT AUTO_INCREMENT PRIMARY KEY,
                version VARCHAR(20) NOT NULL,
                description TEXT,
                sql_file VARCHAR(255),
                executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                executed_by VARCHAR(100),
                status ENUM('success', 'failed') DEFAULT 'success'
            )
        ");
        
        $stmt = $pdo->query("
            SELECT version, executed_at 
            FROM database_versions 
            WHERE status = 'success' 
            ORDER BY executed_at DESC 
            LIMIT 1
        ");
        
        $result = $stmt->fetch();
        
        if ($result) {
            return [
                'version' => $result['version'],
                'date' => $result['executed_at']
            ];
        } else {
            return [
                'version' => '1.0.0',
                'date' => date('Y-m-d H:i:s')
            ];
        }
        
    } catch (Exception $e) {
        error_log("Get current database version error: " . $e->getMessage());
        return [
            'version' => 'Unknown',
            'date' => 'Unknown'
        ];
    }
}

/**
 * Get available migrations
 */
function getAvailableMigrations() {
    $migrations = [];
    $migrationDir = 'database/migrations/';
    
    // Create migrations directory if not exists
    if (!is_dir($migrationDir)) {
        mkdir($migrationDir, 0755, true);
    }
    
    // Scan for migration files
    $files = glob($migrationDir . '*.sql');
    
    foreach ($files as $file) {
        $filename = basename($file);
        $version = extractVersionFromFilename($filename);
        
        $migrations[] = [
            'file' => $filename,
            'path' => $file,
            'version' => $version,
            'description' => getDescriptionFromFile($file),
            'size' => filesize($file),
            'modified' => filemtime($file)
        ];
    }
    
    // Sort by version
    usort($migrations, function($a, $b) {
        return version_compare($a['version'], $b['version']);
    });
    
    return $migrations;
}

/**
 * Extract version from filename
 */
function extractVersionFromFilename($filename) {
    if (preg_match('/(\d+\.\d+\.\d+)/', $filename, $matches)) {
        return $matches[1];
    }
    return '0.0.0';
}

/**
 * Get description from migration file
 */
function getDescriptionFromFile($file) {
    $content = file_get_contents($file);
    if (preg_match('/-- Description: (.+)/i', $content, $matches)) {
        return trim($matches[1]);
    }
    return 'No description available';
}

/**
 * Get migration history
 */
function getMigrationHistory() {
    global $pdo;
    
    try {
        $stmt = $pdo->query("
            SELECT * FROM database_versions 
            ORDER BY executed_at DESC 
            LIMIT 20
        ");
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
        
    } catch (Exception $e) {
        error_log("Get migration history error: " . $e->getMessage());
        return [];
    }
}

/**
 * Run database migration
 */
function runDatabaseMigration($migrationFile) {
    global $pdo;
    
    try {
        $migrationPath = 'database/migrations/' . $migrationFile;
        
        if (!file_exists($migrationPath)) {
            return [
                'success' => false,
                'message' => 'Migration file not found: ' . $migrationFile
            ];
        }
        
        // Read migration file
        $sql = file_get_contents($migrationPath);
        
        if (empty($sql)) {
            return [
                'success' => false,
                'message' => 'Migration file is empty'
            ];
        }
        
        // Begin transaction
        $pdo->beginTransaction();
        
        try {
            // Execute migration
            $statements = explode(';', $sql);
            $executedStatements = 0;
            
            foreach ($statements as $statement) {
                $statement = trim($statement);
                if (!empty($statement) && !preg_match('/^--/', $statement)) {
                    $pdo->exec($statement);
                    $executedStatements++;
                }
            }
            
            // Record migration
            $version = extractVersionFromFilename($migrationFile);
            $description = getDescriptionFromFile($migrationPath);
            
            $stmt = $pdo->prepare("
                INSERT INTO database_versions (version, description, sql_file, executed_by, status)
                VALUES (?, ?, ?, ?, 'success')
            ");
            $stmt->execute([$version, $description, $migrationFile, getCurrentUser()['username']]);
            
            // Commit transaction
            $pdo->commit();
            
            logSystemEvent("Database migration executed", 'info', [
                'migration_file' => $migrationFile,
                'version' => $version,
                'statements_executed' => $executedStatements
            ]);
            
            return [
                'success' => true,
                'message' => "Migration executed successfully. $executedStatements statements processed."
            ];
            
        } catch (Exception $e) {
            $pdo->rollback();
            
            // Record failed migration
            $version = extractVersionFromFilename($migrationFile);
            $stmt = $pdo->prepare("
                INSERT INTO database_versions (version, description, sql_file, executed_by, status)
                VALUES (?, ?, ?, ?, 'failed')
            ");
            $stmt->execute([$version, 'Migration failed: ' . $e->getMessage(), $migrationFile, getCurrentUser()['username']]);
            
            throw $e;
        }
        
    } catch (Exception $e) {
        error_log("Run database migration error: " . $e->getMessage());
        return [
            'success' => false,
            'message' => 'Migration failed: ' . $e->getMessage()
        ];
    }
}

/**
 * Create backup before update
 */
function createBackupBeforeUpdate() {
    try {
        require_once 'includes/helpers/backup_system.php';
        
        $backupFile = createDatabaseBackup('pre_migration_' . date('Y-m-d_H-i-s'));
        
        if ($backupFile) {
            logSystemEvent("Pre-migration backup created", 'info', ['backup_file' => $backupFile]);
            
            return [
                'success' => true,
                'message' => 'Backup created successfully: ' . basename($backupFile)
            ];
        } else {
            return [
                'success' => false,
                'message' => 'Failed to create backup'
            ];
        }
        
    } catch (Exception $e) {
        error_log("Create backup before update error: " . $e->getMessage());
        return [
            'success' => false,
            'message' => 'Backup failed: ' . $e->getMessage()
        ];
    }
}

/**
 * Run all pending migrations
 */
function runAllPendingMigrations() {
    try {
        $migrations = getAvailableMigrations();
        $currentVersion = getCurrentDatabaseVersion()['version'];
        
        $pendingMigrations = [];
        foreach ($migrations as $migration) {
            if (version_compare($migration['version'], $currentVersion, '>')) {
                $pendingMigrations[] = $migration;
            }
        }
        
        if (empty($pendingMigrations)) {
            return [
                'success' => true,
                'message' => 'No pending migrations found. Database is up to date.'
            ];
        }
        
        $executed = 0;
        $failed = 0;
        
        foreach ($pendingMigrations as $migration) {
            $result = runDatabaseMigration($migration['file']);
            if ($result['success']) {
                $executed++;
            } else {
                $failed++;
                break; // Stop on first failure
            }
        }
        
        if ($failed > 0) {
            return [
                'success' => false,
                'message' => "Migration failed. $executed migrations executed successfully before failure."
            ];
        } else {
            return [
                'success' => true,
                'message' => "All pending migrations executed successfully. $executed migrations processed."
            ];
        }
        
    } catch (Exception $e) {
        error_log("Run all pending migrations error: " . $e->getMessage());
        return [
            'success' => false,
            'message' => 'Failed to run migrations: ' . $e->getMessage()
        ];
    }
}
?>

<div class="modern-layout">
    <div class="modern-container">
        <!-- Modern Page Header -->
        <div class="modern-page-header modern-flex modern-justify-between modern-items-center modern-mb-xl">
            <div>
                <h1 class="modern-page-title modern-mb-0">Database Updates</h1>
                <p class="modern-page-subtitle">Manage database schema migrations and updates</p>
            </div>
            <div class="modern-page-actions">
                <form method="POST" style="display: inline;">
                    <input type="hidden" name="action" value="create_backup_before_update">
                    <button type="submit" class="modern-btn modern-btn-warning" onclick="return confirm('Create backup before running migrations?')">
                        <i class="fas fa-shield-alt"></i>
                        Create Backup
                    </button>
                </form>
            </div>
        </div>

        <!-- Flash Messages -->
        <?php if ($flash = getFlashMessage()): ?>
        <div class="modern-alert modern-alert-<?= $flash['type'] ?> modern-mb-lg">
            <div class="modern-alert-content">
                <i class="fas fa-<?= $flash['type'] === 'success' ? 'check-circle' : 'exclamation-triangle' ?> modern-alert-icon"></i>
                <div class="modern-alert-message"><?= $flash['message'] ?></div>
            </div>
            <button type="button" class="modern-alert-close" onclick="this.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <?php endif; ?>

        <!-- Current Version Info -->
        <div class="modern-card modern-mb-lg">
            <div class="modern-card-header">
                <h5 class="modern-card-title">
                    <i class="fas fa-info-circle modern-text-primary modern-mr-sm"></i>
                    Current Database Version
                </h5>
            </div>
            <div class="modern-card-body">
                <div class="modern-version-info">
                    <div class="modern-version-current">
                        <div class="modern-version-number">v<?= $currentVersion['version'] ?></div>
                        <div class="modern-version-date">Last updated: <?= date('d/m/Y H:i', strtotime($currentVersion['date'])) ?></div>
                    </div>
                    <div class="modern-version-actions">
                        <form method="POST" style="display: inline;">
                            <input type="hidden" name="action" value="run_all_migrations">
                            <button type="submit" class="modern-btn modern-btn-success" onclick="return confirm('Run all pending migrations? This will update your database to the latest version.')">
                                <i class="fas fa-rocket"></i>
                                Run All Pending Migrations
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Available Migrations -->
        <div class="modern-card modern-mb-lg">
            <div class="modern-card-header">
                <h5 class="modern-card-title">
                    <i class="fas fa-database modern-text-primary modern-mr-sm"></i>
                    Available Migrations
                </h5>
                <div class="modern-card-actions">
                    <span class="modern-badge modern-badge-info">
                        <?= count($availableMigrations) ?> migration(s)
                    </span>
                </div>
            </div>
            <div class="modern-card-body modern-p-0">
                <?php if (!empty($availableMigrations)): ?>
                <div class="modern-table-responsive">
                    <table class="modern-table">
                        <thead class="modern-table-header">
                            <tr>
                                <th class="modern-table-th">Version</th>
                                <th class="modern-table-th">Description</th>
                                <th class="modern-table-th">File</th>
                                <th class="modern-table-th">Size</th>
                                <th class="modern-table-th">Modified</th>
                                <th class="modern-table-th">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="modern-table-body">
                            <?php foreach ($availableMigrations as $migration): ?>
                            <?php 
                            $isPending = version_compare($migration['version'], $currentVersion['version'], '>');
                            $isApplied = !$isPending;
                            ?>
                            <tr class="modern-table-row">
                                <td class="modern-table-td">
                                    <div class="modern-version-badge">
                                        <span class="modern-badge modern-badge-<?= $isPending ? 'warning' : 'success' ?>">
                                            v<?= $migration['version'] ?>
                                        </span>
                                        <?php if ($isPending): ?>
                                        <span class="modern-badge modern-badge-outline-warning modern-ml-sm">Pending</span>
                                        <?php else: ?>
                                        <span class="modern-badge modern-badge-outline-success modern-ml-sm">Applied</span>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td class="modern-table-td">
                                    <div class="modern-migration-description">
                                        <?= htmlspecialchars($migration['description']) ?>
                                    </div>
                                </td>
                                <td class="modern-table-td">
                                    <code class="modern-code"><?= htmlspecialchars($migration['file']) ?></code>
                                </td>
                                <td class="modern-table-td">
                                    <span class="modern-text-sm modern-text-muted">
                                        <?= formatBytes($migration['size']) ?>
                                    </span>
                                </td>
                                <td class="modern-table-td">
                                    <div class="modern-table-date">
                                        <?= date('d/m/Y H:i', $migration['modified']) ?>
                                    </div>
                                </td>
                                <td class="modern-table-td">
                                    <?php if ($isPending): ?>
                                    <form method="POST" style="display: inline;">
                                        <input type="hidden" name="action" value="run_migration">
                                        <input type="hidden" name="migration_file" value="<?= $migration['file'] ?>">
                                        <button type="submit" class="modern-btn modern-btn-sm modern-btn-primary" onclick="return confirm('Run this migration?')">
                                            <i class="fas fa-play"></i>
                                            Run
                                        </button>
                                    </form>
                                    <?php else: ?>
                                    <span class="modern-text-success">
                                        <i class="fas fa-check"></i>
                                        Applied
                                    </span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php else: ?>
                <div class="modern-empty-state modern-py-xl">
                    <div class="modern-empty-icon">
                        <i class="fas fa-database"></i>
                    </div>
                    <h6 class="modern-empty-title">No Migrations Found</h6>
                    <p class="modern-empty-description">No migration files found in the database/migrations directory.</p>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Migration History -->
        <div class="modern-card">
            <div class="modern-card-header">
                <h5 class="modern-card-title">
                    <i class="fas fa-history modern-text-primary modern-mr-sm"></i>
                    Migration History
                </h5>
                <div class="modern-card-actions">
                    <span class="modern-badge modern-badge-info">
                        Last <?= count($migrationHistory) ?> migrations
                    </span>
                </div>
            </div>
            <div class="modern-card-body modern-p-0">
                <?php if (!empty($migrationHistory)): ?>
                <div class="modern-table-responsive">
                    <table class="modern-table">
                        <thead class="modern-table-header">
                            <tr>
                                <th class="modern-table-th">Version</th>
                                <th class="modern-table-th">Description</th>
                                <th class="modern-table-th">Executed By</th>
                                <th class="modern-table-th">Date</th>
                                <th class="modern-table-th">Status</th>
                            </tr>
                        </thead>
                        <tbody class="modern-table-body">
                            <?php foreach ($migrationHistory as $history): ?>
                            <tr class="modern-table-row">
                                <td class="modern-table-td">
                                    <span class="modern-badge modern-badge-<?= $history['status'] === 'success' ? 'success' : 'danger' ?>">
                                        v<?= $history['version'] ?>
                                    </span>
                                </td>
                                <td class="modern-table-td">
                                    <div class="modern-migration-description">
                                        <?= htmlspecialchars($history['description']) ?>
                                    </div>
                                </td>
                                <td class="modern-table-td">
                                    <div class="modern-user-info">
                                        <i class="fas fa-user modern-text-muted modern-mr-sm"></i>
                                        <?= htmlspecialchars($history['executed_by']) ?>
                                    </div>
                                </td>
                                <td class="modern-table-td">
                                    <div class="modern-table-date">
                                        <?= date('d/m/Y H:i:s', strtotime($history['executed_at'])) ?>
                                    </div>
                                </td>
                                <td class="modern-table-td">
                                    <span class="modern-badge modern-badge-<?= $history['status'] === 'success' ? 'success' : 'danger' ?>">
                                        <i class="fas fa-<?= $history['status'] === 'success' ? 'check' : 'times' ?>"></i>
                                        <?= ucfirst($history['status']) ?>
                                    </span>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php else: ?>
                <div class="modern-empty-state modern-py-xl">
                    <div class="modern-empty-icon">
                        <i class="fas fa-history"></i>
                    </div>
                    <h6 class="modern-empty-title">No Migration History</h6>
                    <p class="modern-empty-description">No migration history found.</p>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/views/layouts/footer.php'; ?>

<style>
.modern-version-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modern-version-current {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.modern-version-number {
    font-size: 24px;
    font-weight: 700;
    color: #2c3e50;
}

.modern-version-date {
    font-size: 14px;
    color: #6c757d;
}

.modern-version-badge {
    display: flex;
    align-items: center;
}

.modern-migration-description {
    font-size: 14px;
    line-height: 1.4;
}

.modern-user-info {
    display: flex;
    align-items: center;
    font-size: 14px;
}

.modern-code {
    background-color: #f8f9fa;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    color: #e83e8c;
}
</style>
