<?php
/**
 * Real-time Notifications API
 * 
 * This endpoint provides real-time notification updates
 */

header('Content-Type: application/json');
header('Cache-Control: no-cache');

require_once '../includes/config/database.php';
require_once '../includes/helpers/function_check.php';
require_once '../includes/helpers/functions.php';

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

$currentUser = getCurrentUser();
$action = $_GET['action'] ?? 'get_notifications';

try {
    switch ($action) {
        case 'get_notifications':
            // Get unread notifications count
            $stmt = $pdo->prepare("
                SELECT COUNT(*) as unread_count 
                FROM notifications 
                WHERE user_id = ? AND is_read = FALSE
            ");
            $stmt->execute([$currentUser['id']]);
            $unreadCount = $stmt->fetchColumn();
            
            // Get recent notifications
            $stmt = $pdo->prepare("
                SELECT 
                    id,
                    title,
                    message,
                    type,
                    is_read,
                    created_at,
                    TIMESTAMPDIFF(MINUTE, created_at, NOW()) as minutes_ago
                FROM notifications 
                WHERE user_id = ? 
                ORDER BY created_at DESC 
                LIMIT 10
            ");
            $stmt->execute([$currentUser['id']]);
            $notifications = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Format notifications
            foreach ($notifications as &$notification) {
                $notification['time_ago'] = formatTimeAgo($notification['minutes_ago']);
                $notification['created_formatted'] = formatTanggal($notification['created_at']);
            }
            
            echo json_encode([
                'success' => true,
                'unread_count' => $unreadCount,
                'notifications' => $notifications
            ]);
            break;
            
        case 'mark_read':
            $notificationId = $_POST['notification_id'] ?? 0;
            
            if ($notificationId) {
                $stmt = $pdo->prepare("
                    UPDATE notifications 
                    SET is_read = TRUE 
                    WHERE id = ? AND user_id = ?
                ");
                $stmt->execute([$notificationId, $currentUser['id']]);
                
                echo json_encode(['success' => true]);
            } else {
                echo json_encode(['success' => false, 'error' => 'Invalid notification ID']);
            }
            break;
            
        case 'mark_all_read':
            $stmt = $pdo->prepare("
                UPDATE notifications 
                SET is_read = TRUE 
                WHERE user_id = ? AND is_read = FALSE
            ");
            $stmt->execute([$currentUser['id']]);
            
            echo json_encode(['success' => true]);
            break;
            
        case 'delete_notification':
            $notificationId = $_POST['notification_id'] ?? 0;
            
            if ($notificationId) {
                $stmt = $pdo->prepare("
                    DELETE FROM notifications 
                    WHERE id = ? AND user_id = ?
                ");
                $stmt->execute([$notificationId, $currentUser['id']]);
                
                echo json_encode(['success' => true]);
            } else {
                echo json_encode(['success' => false, 'error' => 'Invalid notification ID']);
            }
            break;
            
        case 'create_notification':
            // For testing purposes
            $title = $_POST['title'] ?? 'Test Notification';
            $message = $_POST['message'] ?? 'This is a test notification';
            $type = $_POST['type'] ?? 'info';
            
            $stmt = $pdo->prepare("
                INSERT INTO notifications (user_id, title, message, type) 
                VALUES (?, ?, ?, ?)
            ");
            $stmt->execute([$currentUser['id'], $title, $message, $type]);
            
            echo json_encode(['success' => true, 'message' => 'Notification created']);
            break;
            
        case 'get_system_stats':
            // Get system statistics for admin
            if ($currentUser['role'] !== 'admin') {
                http_response_code(403);
                echo json_encode(['error' => 'Access denied']);
                exit;
            }
            
            // Get various system stats
            $stats = [];
            
            // Total users
            $stmt = $pdo->query("SELECT COUNT(*) FROM users WHERE status = 'active'");
            $stats['active_users'] = $stmt->fetchColumn();
            
            // Total transactions today
            $stmt = $pdo->query("SELECT COUNT(*) FROM transaksi WHERE DATE(created_at) = CURDATE()");
            $stats['transactions_today'] = $stmt->fetchColumn();
            
            // Low stock items
            $stmt = $pdo->query("SELECT COUNT(*) FROM inventory WHERE stok_saat_ini <= 10 AND status = 'aktif'");
            $stats['low_stock_items'] = $stmt->fetchColumn();
            
            // Pending returns
            $stmt = $pdo->query("SELECT COUNT(*) FROM returns WHERE status = 'pending'");
            $stats['pending_returns'] = $stmt->fetchColumn();
            
            echo json_encode([
                'success' => true,
                'stats' => $stats
            ]);
            break;
            
        case 'check_alerts':
            // Check for system alerts that need notifications
            $alerts = [];
            
            // Check for low stock
            $stmt = $pdo->prepare("
                SELECT nama_produk, stok_saat_ini 
                FROM inventory 
                WHERE user_id = ? AND stok_saat_ini <= 5 AND status = 'aktif'
                LIMIT 5
            ");
            $stmt->execute([$currentUser['id']]);
            $lowStockItems = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            if (!empty($lowStockItems)) {
                $alerts[] = [
                    'type' => 'warning',
                    'title' => 'Stok Rendah',
                    'message' => count($lowStockItems) . ' produk memiliki stok rendah',
                    'data' => $lowStockItems
                ];
            }
            
            // Check for targets near deadline
            $stmt = $pdo->prepare("
                SELECT nama, target_amount, current_amount, tanggal_selesai
                FROM target 
                WHERE user_id = ? AND status = 'aktif' 
                AND tanggal_selesai BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 7 DAY)
                LIMIT 3
            ");
            $stmt->execute([$currentUser['id']]);
            $nearDeadlineTargets = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            if (!empty($nearDeadlineTargets)) {
                $alerts[] = [
                    'type' => 'info',
                    'title' => 'Target Mendekati Deadline',
                    'message' => count($nearDeadlineTargets) . ' target akan berakhir dalam 7 hari',
                    'data' => $nearDeadlineTargets
                ];
            }
            
            echo json_encode([
                'success' => true,
                'alerts' => $alerts
            ]);
            break;
            
        default:
            http_response_code(400);
            echo json_encode(['error' => 'Invalid action']);
    }
    
} catch (Exception $e) {
    error_log("Notifications API error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Internal server error']);
}

/**
 * Format time ago
 * @param int $minutes Minutes ago
 * @return string Formatted time
 */
function formatTimeAgo($minutes) {
    if ($minutes < 1) {
        return 'Baru saja';
    } elseif ($minutes < 60) {
        return $minutes . ' menit yang lalu';
    } elseif ($minutes < 1440) {
        $hours = floor($minutes / 60);
        return $hours . ' jam yang lalu';
    } else {
        $days = floor($minutes / 1440);
        return $days . ' hari yang lalu';
    }
}
?>
