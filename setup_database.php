<?php
require_once 'includes/config/database.php';

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

$messages = [];
$errors = [];

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['run_migration'])) {
    try {
        // Check if status column exists in supplier table
        $stmt = $pdo->query("SHOW COLUMNS FROM supplier LIKE 'status'");
        
        if ($stmt->rowCount() == 0) {
            $messages[] = "Adding status column to supplier table...";
            
            // Add status column
            $pdo->exec("ALTER TABLE supplier ADD COLUMN status ENUM('aktif', 'nonaktif') DEFAULT 'aktif' AFTER keterangan");
            
            // Update all existing suppliers to have 'aktif' status
            $pdo->exec("UPDATE supplier SET status = 'aktif' WHERE status IS NULL");
            
            $messages[] = "✅ Status column added successfully!";
        } else {
            $messages[] = "✅ Status column already exists in supplier table.";
        }
        
        // Check if inventory table exists and has required columns
        $stmt = $pdo->query("SHOW TABLES LIKE 'inventory'");
        if ($stmt->rowCount() == 0) {
            $messages[] = "Creating inventory table...";
            
            $pdo->exec("CREATE TABLE IF NOT EXISTS inventory (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                kode_barang VARCHAR(50) NOT NULL,
                nama_barang VARCHAR(255) NOT NULL,
                kategori_barang VARCHAR(100),
                satuan VARCHAR(50) DEFAULT 'pcs',
                stok_minimum INT DEFAULT 0,
                stok_saat_ini INT DEFAULT 0,
                harga_beli DECIMAL(15,2) DEFAULT 0,
                harga_jual DECIMAL(15,2) DEFAULT 0,
                lokasi_penyimpanan VARCHAR(255),
                supplier_id INT,
                tanggal_masuk DATE,
                tanggal_kadaluarsa DATE,
                status ENUM('aktif', 'nonaktif', 'habis') DEFAULT 'aktif',
                keterangan TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                INDEX idx_user_kode (user_id, kode_barang),
                INDEX idx_status (status),
                INDEX idx_stok (stok_saat_ini)
            )");
            
            $messages[] = "✅ Inventory table created successfully!";
        } else {
            $messages[] = "✅ Inventory table already exists.";
        }
        
        // Check if stock_movements table exists
        $stmt = $pdo->query("SHOW TABLES LIKE 'stock_movements'");
        if ($stmt->rowCount() == 0) {
            $messages[] = "Creating stock_movements table...";
            
            $pdo->exec("CREATE TABLE IF NOT EXISTS stock_movements (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                inventory_id INT NOT NULL,
                jenis_gerakan ENUM('masuk', 'keluar', 'adjustment') NOT NULL,
                jumlah INT NOT NULL,
                stok_sebelum INT NOT NULL,
                stok_sesudah INT NOT NULL,
                keterangan TEXT,
                referensi VARCHAR(100),
                tanggal_gerakan DATETIME DEFAULT CURRENT_TIMESTAMP,
                created_by INT,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                FOREIGN KEY (inventory_id) REFERENCES inventory(id) ON DELETE CASCADE,
                FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
                INDEX idx_inventory_date (inventory_id, tanggal_gerakan),
                INDEX idx_user_date (user_id, tanggal_gerakan)
            )");
            
            $messages[] = "✅ Stock movements table created successfully!";
        } else {
            $messages[] = "✅ Stock movements table already exists.";
        }
        
        // Check if returns table exists
        $stmt = $pdo->query("SHOW TABLES LIKE 'returns'");
        if ($stmt->rowCount() == 0) {
            $messages[] = "Creating returns table...";
            
            $pdo->exec("CREATE TABLE IF NOT EXISTS returns (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                nomor_return VARCHAR(50) NOT NULL,
                jenis_return ENUM('penjualan', 'pembelian') NOT NULL,
                referensi_id INT,
                referensi_nomor VARCHAR(50),
                tanggal_return DATE NOT NULL,
                total_return DECIMAL(15,2) NOT NULL DEFAULT 0,
                alasan_return TEXT,
                status ENUM('pending', 'disetujui', 'ditolak', 'selesai') DEFAULT 'pending',
                catatan TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                INDEX idx_user_nomor (user_id, nomor_return),
                INDEX idx_status (status),
                INDEX idx_tanggal (tanggal_return)
            )");
            
            $pdo->exec("CREATE TABLE IF NOT EXISTS return_items (
                id INT AUTO_INCREMENT PRIMARY KEY,
                return_id INT NOT NULL,
                produk_id INT,
                nama_produk VARCHAR(255) NOT NULL,
                qty_return INT NOT NULL,
                harga_satuan DECIMAL(15,2) NOT NULL,
                subtotal DECIMAL(15,2) NOT NULL,
                kondisi_barang ENUM('baik', 'rusak', 'cacat') DEFAULT 'baik',
                keterangan TEXT,
                FOREIGN KEY (return_id) REFERENCES returns(id) ON DELETE CASCADE
            )");
            
            $messages[] = "✅ Returns tables created successfully!";
        } else {
            $messages[] = "✅ Returns tables already exist.";
        }
        
        // Check if pengingat table exists
        $stmt = $pdo->query("SHOW TABLES LIKE 'pengingat'");
        if ($stmt->rowCount() == 0) {
            $messages[] = "Creating pengingat table...";
            
            $pdo->exec("CREATE TABLE IF NOT EXISTS pengingat (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                judul VARCHAR(255) NOT NULL,
                deskripsi TEXT,
                tanggal_pengingat DATE NOT NULL,
                waktu_pengingat TIME,
                jenis ENUM('tagihan', 'cicilan', 'target', 'investasi', 'lainnya') NOT NULL,
                jumlah DECIMAL(15,2),
                status ENUM('aktif', 'selesai', 'terlewat') DEFAULT 'aktif',
                pengulangan ENUM('sekali', 'harian', 'mingguan', 'bulanan', 'tahunan') DEFAULT 'sekali',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                INDEX idx_user_tanggal (user_id, tanggal_pengingat),
                INDEX idx_status (status)
            )");
            
            $messages[] = "✅ Pengingat table created successfully!";
        } else {
            $messages[] = "✅ Pengingat table already exists.";
        }
        
        $messages[] = "🎉 All database migrations completed successfully!";
        $messages[] = "You can now access all the new features.";
        
    } catch (PDOException $e) {
        $errors[] = "❌ Error during migration: " . $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Setup - KeuanganKu</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">
                            <i class="fas fa-database me-2"></i>Database Setup - KeuanganKu
                        </h4>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($messages)): ?>
                        <div class="alert alert-success">
                            <h6><i class="fas fa-check-circle me-2"></i>Migration Results:</h6>
                            <?php foreach ($messages as $message): ?>
                                <div><?= $message ?></div>
                            <?php endforeach; ?>
                        </div>
                        <?php endif; ?>

                        <?php if (!empty($errors)): ?>
                        <div class="alert alert-danger">
                            <h6><i class="fas fa-exclamation-triangle me-2"></i>Errors:</h6>
                            <?php foreach ($errors as $error): ?>
                                <div><?= $error ?></div>
                            <?php endforeach; ?>
                        </div>
                        <?php endif; ?>

                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>What this will do:</h6>
                            <ul class="mb-0">
                                <li>Add missing 'status' column to supplier table</li>
                                <li>Create inventory management tables</li>
                                <li>Create stock movement tracking tables</li>
                                <li>Create return management tables</li>
                                <li>Create reminder (pengingat) tables</li>
                                <li>Add proper indexes for better performance</li>
                            </ul>
                        </div>

                        <form method="POST">
                            <div class="text-center">
                                <button type="submit" name="run_migration" class="btn btn-primary btn-lg">
                                    <i class="fas fa-play me-2"></i>Run Database Migration
                                </button>
                            </div>
                        </form>

                        <?php if (!empty($messages) && empty($errors)): ?>
                        <div class="text-center mt-4">
                            <a href="index.php" class="btn btn-success">
                                <i class="fas fa-home me-2"></i>Go to Dashboard
                            </a>
                            <a href="supplier.php" class="btn btn-outline-primary">
                                <i class="fas fa-truck me-2"></i>Test Supplier Page
                            </a>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
