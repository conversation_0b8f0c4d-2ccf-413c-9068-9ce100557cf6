<?php

namespace PhpOffice\PhpSpreadsheet\Calculation;

abstract class Category
{
    // Function categories
    const CATEGORY_CUBE = 'Cube';
    const CATEGORY_DATABASE = 'Database';
    const CATEGORY_DATE_AND_TIME = 'Date and Time';
    const CATEGORY_ENGINEERING = 'Engineering';
    const CATEGORY_FINANCIAL = 'Financial';
    const CATEGORY_INFORMATION = 'Information';
    const CATEGORY_LOGICAL = 'Logical';
    const CATEGORY_LOOKUP_AND_REFERENCE = 'Lookup and Reference';
    const CATEGORY_MATH_AND_TRIG = 'Math and Trig';
    const CATEGORY_STATISTICAL = 'Statistical';
    const CATEGORY_TEXT_AND_DATA = 'Text and Data';
    const CATEGORY_WEB = 'Web';
    const CATEGORY_UNCATEGORISED = 'Uncategorised';
    const CATEGORY_MICROSOFT_INTERNAL = 'MS Internal';
}
