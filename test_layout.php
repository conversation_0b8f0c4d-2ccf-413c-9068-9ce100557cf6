<?php
require_once 'includes/config/database.php';
require_once 'includes/helpers/functions.php';
require_once 'includes/helpers/layout_helper.php';

// Check if user is logged in
$currentUser = getCurrentUser();
if (!$currentUser) {
    redirect('login.php');
}

$pageTitle = 'Layout Test Page';

// Handle test layout application
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_layout'])) {
    try {
        // Create test layout preferences
        $stmt = $pdo->prepare("
            INSERT INTO layout_preferences (user_id, layout_type, sidebar_style, navbar_style, footer_style, main_content_style, color_scheme, border_radius, shadow_style, animation_style) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ON DUPLICATE KEY UPDATE 
            layout_type = VALUES(layout_type),
            sidebar_style = VALUES(sidebar_style),
            navbar_style = VALUES(navbar_style),
            footer_style = VALUES(footer_style),
            main_content_style = VALUES(main_content_style),
            color_scheme = VALUES(color_scheme),
            border_radius = VALUES(border_radius),
            shadow_style = VALUES(shadow_style),
            animation_style = VALUES(animation_style),
            updated_at = CURRENT_TIMESTAMP
        ");
        
        $result = $stmt->execute([
            $currentUser['id'],
            $_POST['layout_type'],
            $_POST['sidebar_style'],
            $_POST['navbar_style'],
            $_POST['footer_style'],
            $_POST['main_content_style'],
            $_POST['color_scheme'],
            $_POST['border_radius'],
            $_POST['shadow_style'],
            $_POST['animation_style']
        ]);

        if ($result) {
            setFlashMessage('success', 'Test layout applied successfully! Refresh the page to see changes.');
        } else {
            setFlashMessage('danger', 'Failed to apply test layout');
        }
        redirect('test_layout.php');
    } catch (Exception $e) {
        setFlashMessage('danger', 'Error: ' . $e->getMessage());
    }
}

include 'includes/views/layouts/header.php';
?>

<div class="container-fluid px-4 py-3">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h3 class="mb-1">
                        <i class="fas fa-flask me-2 text-primary"></i>
                        Layout Test Page
                    </h3>
                    <p class="text-muted mb-0">Test different layout configurations and see the results immediately</p>
                </div>
                <div class="btn-group">
                    <a href="layout_manager.php" class="btn btn-primary">
                        <i class="fas fa-paint-brush me-1"></i>Layout Manager
                    </a>
                    <a href="theme_manager.php" class="btn btn-outline-secondary">
                        <i class="fas fa-palette me-1"></i>Theme Manager
                    </a>
                </div>
            </div>

            <!-- Current Layout Info -->
            <?php
            $currentLayout = getUserLayoutPreferences($currentUser['id']);
            ?>
            <div class="card mb-4">
                <div class="card-header bg-gradient-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        Current Layout Configuration
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <strong>Layout Type:</strong><br>
                            <span class="badge bg-primary"><?= ucfirst($currentLayout['layout_type']) ?></span>
                        </div>
                        <div class="col-md-3 mb-3">
                            <strong>Color Scheme:</strong><br>
                            <span class="badge bg-success"><?= ucfirst($currentLayout['color_scheme']) ?></span>
                        </div>
                        <div class="col-md-3 mb-3">
                            <strong>Border Radius:</strong><br>
                            <span class="badge bg-warning"><?= ucfirst($currentLayout['border_radius']) ?></span>
                        </div>
                        <div class="col-md-3 mb-3">
                            <strong>Shadow Style:</strong><br>
                            <span class="badge bg-info"><?= ucfirst($currentLayout['shadow_style']) ?></span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <strong>Sidebar Style:</strong><br>
                            <span class="badge bg-secondary"><?= ucfirst($currentLayout['sidebar_style']) ?></span>
                        </div>
                        <div class="col-md-3 mb-3">
                            <strong>Navbar Style:</strong><br>
                            <span class="badge bg-secondary"><?= ucfirst($currentLayout['navbar_style']) ?></span>
                        </div>
                        <div class="col-md-3 mb-3">
                            <strong>Footer Style:</strong><br>
                            <span class="badge bg-secondary"><?= ucfirst($currentLayout['footer_style']) ?></span>
                        </div>
                        <div class="col-md-3 mb-3">
                            <strong>Content Style:</strong><br>
                            <span class="badge bg-secondary"><?= ucfirst($currentLayout['main_content_style']) ?></span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Test Layouts -->
            <div class="row">
                <div class="col-md-4 mb-4">
                    <div class="card h-100">
                        <div class="card-header bg-gradient-primary text-white">
                            <h6 class="mb-0">🌈 Colorful Modern</h6>
                        </div>
                        <div class="card-body">
                            <p class="card-text">Vibrant gradient design with modern styling</p>
                            <form method="POST">
                                <input type="hidden" name="test_layout" value="1">
                                <input type="hidden" name="layout_type" value="colorful">
                                <input type="hidden" name="sidebar_style" value="modern">
                                <input type="hidden" name="navbar_style" value="modern">
                                <input type="hidden" name="footer_style" value="modern">
                                <input type="hidden" name="main_content_style" value="modern">
                                <input type="hidden" name="color_scheme" value="vibrant">
                                <input type="hidden" name="border_radius" value="large">
                                <input type="hidden" name="shadow_style" value="medium">
                                <input type="hidden" name="animation_style" value="smooth">
                                <button type="submit" class="btn btn-primary w-100">Apply Test</button>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="col-md-4 mb-4">
                    <div class="card h-100">
                        <div class="card-header bg-gradient-success text-white">
                            <h6 class="mb-0">💎 Glassmorphism</h6>
                        </div>
                        <div class="card-body">
                            <p class="card-text">Modern glass effect with transparency</p>
                            <form method="POST">
                                <input type="hidden" name="test_layout" value="1">
                                <input type="hidden" name="layout_type" value="glassmorphism">
                                <input type="hidden" name="sidebar_style" value="glassmorphism">
                                <input type="hidden" name="navbar_style" value="glassmorphism">
                                <input type="hidden" name="footer_style" value="transparent">
                                <input type="hidden" name="main_content_style" value="glassmorphism">
                                <input type="hidden" name="color_scheme" value="pastel">
                                <input type="hidden" name="border_radius" value="medium">
                                <input type="hidden" name="shadow_style" value="soft">
                                <input type="hidden" name="animation_style" value="subtle">
                                <button type="submit" class="btn btn-success w-100">Apply Test</button>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="col-md-4 mb-4">
                    <div class="card h-100">
                        <div class="card-header bg-gradient-warning text-white">
                            <h6 class="mb-0">⚪ Minimal Clean</h6>
                        </div>
                        <div class="card-body">
                            <p class="card-text">Clean, minimalist design with subtle elements</p>
                            <form method="POST">
                                <input type="hidden" name="test_layout" value="1">
                                <input type="hidden" name="layout_type" value="minimal">
                                <input type="hidden" name="sidebar_style" value="classic">
                                <input type="hidden" name="navbar_style" value="classic">
                                <input type="hidden" name="footer_style" value="minimal">
                                <input type="hidden" name="main_content_style" value="classic">
                                <input type="hidden" name="color_scheme" value="default">
                                <input type="hidden" name="border_radius" value="small">
                                <input type="hidden" name="shadow_style" value="none">
                                <input type="hidden" name="animation_style" value="none">
                                <button type="submit" class="btn btn-warning w-100">Apply Test</button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sample Content to Test Layout -->
            <div class="row">
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">Sample Chart Card</h6>
                        </div>
                        <div class="card-body">
                            <div style="height: 200px; background: linear-gradient(45deg, #007bff, #0056b3); border-radius: 8px; display: flex; align-items: center; justify-content: center; color: white; font-size: 1.2rem;">
                                📊 Chart Placeholder
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">Sample Statistics</h6>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-4">
                                    <div class="h3 text-primary">1,234</div>
                                    <small class="text-muted">Users</small>
                                </div>
                                <div class="col-4">
                                    <div class="h3 text-success">5,678</div>
                                    <small class="text-muted">Sales</small>
                                </div>
                                <div class="col-4">
                                    <div class="h3 text-warning">9,012</div>
                                    <small class="text-muted">Views</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Debug Information -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-bug me-2"></i>
                        Debug Information
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Generated CSS Classes:</h6>
                            <?php
                            $layoutClasses = getLayoutClasses($currentLayout);
                            foreach ($layoutClasses as $type => $class) {
                                echo "<code>{$type}: {$class}</code><br>";
                            }
                            ?>
                        </div>
                        <div class="col-md-6">
                            <h6>Database Values:</h6>
                            <pre><?= htmlspecialchars(json_encode($currentLayout, JSON_PRETTY_PRINT)) ?></pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('Layout Test Page Loaded');
    console.log('Current layout classes:', document.body.className);
    
    // Show applied styles
    const sidebar = document.querySelector('.sidebar, .modern-sidebar, #sidebar');
    const navbar = document.querySelector('.navbar, .modern-navbar, #mainNavbar');
    
    if (sidebar) {
        console.log('Sidebar computed styles:', window.getComputedStyle(sidebar).background);
    }
    
    if (navbar) {
        console.log('Navbar computed styles:', window.getComputedStyle(navbar).background);
    }
});
</script>

<?php include 'includes/views/layouts/footer.php'; ?>
