<?php
require_once 'includes/config/database.php';
require_once 'includes/helpers/functions.php';

echo "<h1>🔧 Setup Menu Permissions System</h1>";

try {
    // Create tables if not exist
    echo "<h2>📋 Creating Tables...</h2>";
    
    // Create roles table
    $pdo->exec("CREATE TABLE IF NOT EXISTS roles (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(50) NOT NULL UNIQUE,
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )");
    echo "✅ Roles table created/verified<br>";
    
    // Create role_menu_access table
    $pdo->exec("CREATE TABLE IF NOT EXISTS role_menu_access (
        role_id INT,
        menu_id VARCHAR(50),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (role_id, menu_id),
        FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE
    )");
    echo "✅ Role menu access table created/verified<br>";
    
    // Insert default roles
    echo "<h2>👥 Setting up Roles...</h2>";
    $stmt = $pdo->prepare("INSERT IGNORE INTO roles (name, description) VALUES (?, ?)");
    $stmt->execute(['admin', 'Administrator dengan akses penuh']);
    $stmt->execute(['user', 'Pengguna biasa dengan akses terbatas']);
    echo "✅ Default roles created<br>";
    
    // Get role IDs
    $stmt = $pdo->prepare("SELECT id FROM roles WHERE name = ?");
    $stmt->execute(['admin']);
    $adminRoleId = $stmt->fetchColumn();
    
    $stmt->execute(['user']);
    $userRoleId = $stmt->fetchColumn();
    
    echo "Admin Role ID: $adminRoleId<br>";
    echo "User Role ID: $userRoleId<br>";
    
    // Clear existing permissions
    echo "<h2>🧹 Clearing existing permissions...</h2>";
    $pdo->exec("DELETE FROM role_menu_access");
    echo "✅ Existing permissions cleared<br>";
    
    // Set admin permissions (all menus)
    echo "<h2>👨‍💼 Setting Admin Permissions...</h2>";
    $adminMenus = [
        'dashboard', 'keuangan', 'transaksi', 'kategori', 'target', 'anggaran', 'investasi', 'hutang',
        'bisnis', 'produk', 'penjualan', 'pembelian', 'supplier', 'inventory', 'retur',
        'laporan', 'laporan_keuangan', 'laporan_bisnis', 'laporan_tax',
        'tools', 'kalkulator', 'konverter', 'kalender', 'pengingat',
        'admin_panel', 'users', 'permissions', 'backup', 'logs', 'notifications', 'api', 'settings',
        'profile', 'bantuan', 'panduan', 'faq', 'tutorial', 'support', 'logout'
    ];
    
    $stmt = $pdo->prepare("INSERT INTO role_menu_access (role_id, menu_id) VALUES (?, ?)");
    foreach ($adminMenus as $menuId) {
        $stmt->execute([$adminRoleId, $menuId]);
    }
    echo "✅ Admin permissions set (" . count($adminMenus) . " menus)<br>";
    
    // Set user permissions (limited menus)
    echo "<h2>👤 Setting User Permissions...</h2>";
    $userMenus = [
        'dashboard', 'transaksi', 'kategori', 'target', 'profile', 'logout'
    ];
    
    foreach ($userMenus as $menuId) {
        $stmt->execute([$userRoleId, $menuId]);
    }
    echo "✅ User permissions set (" . count($userMenus) . " menus)<br>";
    
    // Verify setup
    echo "<h2>✅ Verification</h2>";
    $stmt = $pdo->query("SELECT r.name, COUNT(rma.menu_id) as menu_count 
                        FROM roles r 
                        LEFT JOIN role_menu_access rma ON r.id = rma.role_id 
                        GROUP BY r.id, r.name");
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Role</th><th>Menu Count</th></tr>";
    while ($row = $stmt->fetch()) {
        echo "<tr><td>{$row['name']}</td><td>{$row['menu_count']}</td></tr>";
    }
    echo "</table>";
    
    echo "<h2>🎯 Next Steps</h2>";
    echo "<ol>";
    echo "<li><a href='simple_permissions.php'>Go to Simple Permissions Management</a></li>";
    echo "<li><a href='debug_menu_system.php'>Debug the Menu System</a></li>";
    echo "<li><a href='dashboard.php'>Test Menu Changes on Dashboard</a></li>";
    echo "<li>Login as different users to test permissions</li>";
    echo "</ol>";
    
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<strong>✅ Setup Complete!</strong><br>";
    echo "The menu permissions system is now ready to use.<br>";
    echo "Admin can see all menus, User can see limited menus.";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<strong>❌ Error:</strong> " . $e->getMessage();
    echo "</div>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; background: #f8f9fa; }
h1, h2 { color: #333; }
table { background: white; padding: 10px; }
th, td { padding: 8px 12px; text-align: left; }
th { background: #e9ecef; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
</style>
