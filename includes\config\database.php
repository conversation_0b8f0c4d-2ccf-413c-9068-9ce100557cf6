<?php
// Konfigurasi Database
$db_host = 'localhost';
$db_name = 'keuangan';
$db_user = 'root';
$db_pass = '';

class Database {
    private static $instance = null;
    private $conn;
    private $host;
    private $name;
    private $user;
    private $pass;

    private function __construct() {
        global $db_host, $db_name, $db_user, $db_pass;
        $this->host = $db_host;
        $this->name = $db_name;
        $this->user = $db_user;
        $this->pass = $db_pass;

        try {
            $this->conn = new PDO(
                "mysql:host={$this->host};dbname={$this->name};charset=utf8mb4",
                $this->user,
                $this->pass,
                [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::ATTR_EMULATE_PREPARES => false,
                    PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
                ]
            );
        } catch (PDOException $e) {
            error_log("Database Connection Error: " . $e->getMessage());
            throw new Exception("Koneksi database gagal. Silakan coba beberapa saat lagi.");
        }
    }

    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    public function getConnection() {
        return $this->conn;
    }
}

// Buat instance database
try {
    $db = Database::getInstance();
    $pdo = $db->getConnection();
} catch (Exception $e) {
    die("Terjadi kesalahan: " . $e->getMessage());
} 