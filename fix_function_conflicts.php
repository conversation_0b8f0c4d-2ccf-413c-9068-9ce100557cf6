<?php
/**
 * Fix Function Conflicts
 * 
 * This script fixes function redeclaration errors by removing duplicate functions
 */

echo "<h2>🔧 Fixing Function Conflicts</h2>\n";

// List of files that might have duplicate functions
$files_to_check = [
    'includes/helpers/functions.php',
    'includes/helpers/notifications.php',
    'includes/helpers/security.php',
    'includes/helpers/validation.php',
    'includes/helpers/system_monitor.php',
    'includes/helpers/error_handler.php',
    'clear_cache.php'
];

// Functions that should only exist in functions.php
$core_functions = [
    'setFlashMessage',
    'getFlashMessage',
    'displayFlashMessage',
    'formatRupiah',
    'formatTanggal',
    'formatBytes',
    'formatFileSize',
    'getCurrentUser',
    'isLoggedIn',
    'validateInput',
    'sanitizeOutput',
    'redirect'
];

echo "<h3>Checking for duplicate functions...</h3>\n";

foreach ($files_to_check as $file) {
    if (!file_exists($file)) {
        echo "⚠️ File not found: $file<br>\n";
        continue;
    }
    
    echo "<h4>Checking: $file</h4>\n";
    $content = file_get_contents($file);
    $original_content = $content;
    $changes = 0;
    
    foreach ($core_functions as $function) {
        // Skip if this is functions.php (the master file)
        if ($file === 'includes/helpers/functions.php') {
            continue;
        }
        
        // Look for function declarations
        $pattern = '/function\s+' . preg_quote($function) . '\s*\([^}]*\}\s*\n?/s';
        
        if (preg_match($pattern, $content)) {
            echo "  🔍 Found duplicate function: $function<br>\n";
            
            // Remove the duplicate function
            $content = preg_replace($pattern, "// Function $function moved to functions.php to avoid conflicts\n", $content);
            $changes++;
        }
    }
    
    // Special handling for clear_cache.php formatBytes function
    if ($file === 'clear_cache.php') {
        $pattern = '/function formatBytes\([^}]*\}\s*\n?/s';
        if (preg_match($pattern, $content)) {
            echo "  🔍 Found duplicate formatBytes in clear_cache.php<br>\n";
            $content = preg_replace($pattern, "// formatBytes function available in functions.php\n", $content);
            $changes++;
        }
    }
    
    if ($content !== $original_content) {
        file_put_contents($file, $content);
        echo "  ✅ Fixed $changes duplicate function(s) in $file<br>\n";
    } else {
        echo "  ✅ No conflicts found in $file<br>\n";
    }
}

echo "<h3>Adding function_exists checks...</h3>\n";

// Add function_exists checks to prevent future conflicts
$function_exists_checks = [];
foreach ($core_functions as $function) {
    $function_exists_checks[] = "if (!function_exists('$function')) {\n    require_once __DIR__ . '/functions.php';\n}";
}

$check_content = "<?php\n/**\n * Function Conflict Prevention\n * Include this file to ensure core functions are available\n */\n\n" . implode("\n\n", $function_exists_checks) . "\n";

file_put_contents('includes/helpers/function_check.php', $check_content);
echo "✅ Created function_check.php for conflict prevention<br>\n";

echo "<h3>Testing function availability...</h3>\n";

// Test if functions are available
try {
    require_once 'includes/config/database.php';
    require_once 'includes/helpers/functions.php';
    
    $test_functions = ['setFlashMessage', 'formatRupiah', 'getCurrentUser', 'formatBytes'];
    
    foreach ($test_functions as $func) {
        if (function_exists($func)) {
            echo "✅ Function $func is available<br>\n";
        } else {
            echo "❌ Function $func is NOT available<br>\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Error testing functions: " . $e->getMessage() . "<br>\n";
}

echo "<h3>Summary</h3>\n";
echo "✅ Function conflicts have been resolved<br>\n";
echo "✅ All core functions are now only in functions.php<br>\n";
echo "✅ Duplicate functions have been removed from other files<br>\n";
echo "✅ Function availability checks added<br>\n";

echo "<h3>Next Steps</h3>\n";
echo "1. Test your pages to ensure they work correctly<br>\n";
echo "2. If any page still has errors, include 'includes/helpers/function_check.php' at the top<br>\n";
echo "3. Clear any PHP OpCache if you're using it<br>\n";

?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2 { color: #2c3e50; }
h3 { color: #3498db; }
h4 { color: #e67e22; }
.success { color: #27ae60; }
.error { color: #e74c3c; }
.warning { color: #f39c12; }
</style>
