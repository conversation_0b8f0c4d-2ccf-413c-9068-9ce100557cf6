<?php
require_once 'includes/config/database.php';
require_once 'includes/helpers/functions.php';
require_once 'includes/helpers/notifications.php';

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isLoggedIn()) {
    setFlashMessage('danger', 'Silakan login terlebih dahulu');
    redirect('/keuangan/login.php');
}

$currentUser = getCurrentUser();
$currentPage = 'konverter';

// Currency data (in real app, this would come from API)
$currencies = [
    'IDR' => ['name' => 'Indonesian Rupiah', 'symbol' => 'Rp', 'rate' => 1],
    'USD' => ['name' => 'US Dollar', 'symbol' => '$', 'rate' => 0.000067],
    'EUR' => ['name' => 'Euro', 'symbol' => '€', 'rate' => 0.000061],
    'GBP' => ['name' => 'British Pound', 'symbol' => '£', 'rate' => 0.000053],
    'JPY' => ['name' => 'Japanese Yen', 'symbol' => '¥', 'rate' => 0.0097],
    'SGD' => ['name' => 'Singapore Dollar', 'symbol' => 'S$', 'rate' => 0.000090],
    'MYR' => ['name' => 'Malaysian Ringgit', 'symbol' => 'RM', 'rate' => 0.00031],
    'THB' => ['name' => 'Thai Baht', 'symbol' => '฿', 'rate' => 0.0024],
    'CNY' => ['name' => 'Chinese Yuan', 'symbol' => '¥', 'rate' => 0.00048],
    'KRW' => ['name' => 'South Korean Won', 'symbol' => '₩', 'rate' => 0.089],
    'AUD' => ['name' => 'Australian Dollar', 'symbol' => 'A$', 'rate' => 0.000103],
    'CAD' => ['name' => 'Canadian Dollar', 'symbol' => 'C$', 'rate' => 0.000091]
];

// Include header and sidebar
require_once 'includes/views/layouts/header.php';
require_once 'includes/views/layouts/sidebar.php';
?>

<div class="modern-layout">
    <div class="modern-container">
        <!-- Modern Page Header -->
        <div class="modern-page-header modern-flex modern-justify-between modern-items-center modern-mb-xl">
            <div>
                <h1 class="modern-page-title modern-mb-0">Konverter Mata Uang</h1>
                <p class="modern-page-subtitle">Konversi mata uang dengan nilai tukar terkini dan akurat</p>
            </div>
            <div class="modern-page-info">
                <div class="modern-badge modern-badge-info">
                    <i class="fas fa-clock modern-mr-xs"></i>
                    Update: <?= date('d/m/Y H:i') ?>
                </div>
            </div>
        </div>

        <div class="modern-grid modern-grid-cols-3 modern-gap-lg">
            <!-- Modern Currency Converter -->
            <div class="modern-converter-card" style="grid-column: span 2;">
                <div class="modern-converter-header">
                    <h5 class="modern-converter-title">
                        <i class="fas fa-exchange-alt modern-text-primary modern-mr-sm"></i>
                        Konverter Mata Uang
                    </h5>
                </div>
                <div class="modern-converter-body">
                    <div class="modern-converter-form">
                        <!-- From Currency -->
                        <div class="modern-converter-section">
                            <div class="modern-form-group">
                                <label class="modern-form-label">
                                    <i class="fas fa-arrow-up modern-text-primary"></i>
                                    Dari
                                </label>
                                <select id="fromCurrency" class="modern-form-control">
                                    <?php foreach ($currencies as $code => $currency): ?>
                                    <option value="<?= $code ?>" <?= $code === 'IDR' ? 'selected' : '' ?>>
                                        <?= $currency['symbol'] ?> <?= $code ?> - <?= $currency['name'] ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="modern-form-group">
                                <label class="modern-form-label">
                                    <i class="fas fa-calculator modern-text-primary"></i>
                                    Jumlah
                                </label>
                                <div class="modern-input-group">
                                    <span class="modern-input-group-text" id="fromSymbol">Rp</span>
                                    <input type="text" id="fromAmount" class="modern-form-control number-format" value="1,000,000" placeholder="0">
                                </div>
                            </div>
                        </div>

                        <!-- Swap Button -->
                        <div class="modern-converter-swap">
                            <button type="button" id="swapCurrencies" class="modern-swap-btn">
                                <i class="fas fa-exchange-alt"></i>
                            </button>
                        </div>

                        <!-- To Currency -->
                        <div class="modern-converter-section">
                            <div class="modern-form-group">
                                <label class="modern-form-label">
                                    <i class="fas fa-arrow-down modern-text-success"></i>
                                    Ke
                                </label>
                                <select id="toCurrency" class="modern-form-control">
                                    <?php foreach ($currencies as $code => $currency): ?>
                                    <option value="<?= $code ?>" <?= $code === 'USD' ? 'selected' : '' ?>>
                                        <?= $currency['symbol'] ?> <?= $code ?> - <?= $currency['name'] ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="modern-form-group">
                                <label class="modern-form-label">
                                    <i class="fas fa-equals modern-text-success"></i>
                                    Hasil
                                </label>
                                <div class="modern-input-group">
                                    <span class="modern-input-group-text" id="toSymbol">$</span>
                                    <input type="text" id="toAmount" class="modern-form-control modern-result-input" readonly>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Exchange Rate Info -->
                    <div class="modern-rate-info">
                        <div class="modern-rate-display">
                            <div class="modern-rate-text">
                                <strong id="rateInfo">1 IDR = 0.000067 USD</strong>
                                <small class="modern-rate-subtitle">Nilai tukar saat ini</small>
                            </div>
                            <button type="button" class="modern-btn modern-btn-primary modern-btn-sm" onclick="convert()">
                                <i class="fas fa-sync"></i>
                                Konversi
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Conversion -->
            <div class="card border-0 shadow-sm mt-4">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0"><i class="fas fa-bolt me-2"></i>Konversi Cepat</h5>
                </div>
                <div class="card-body">
                    <div class="row" id="quickConversion">
                        <!-- Quick conversion buttons will be generated here -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Currency List & Rates -->
        <div class="col-lg-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0"><i class="fas fa-list me-2"></i>Daftar Mata Uang</h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>Mata Uang</th>
                                    <th class="text-end">Rate (IDR)</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($currencies as $code => $currency): ?>
                                <?php if ($code !== 'IDR'): ?>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="currency-flag me-2">
                                                <span class="badge bg-light text-dark"><?= $code ?></span>
                                            </div>
                                            <div>
                                                <div class="fw-bold"><?= $code ?></div>
                                                <small class="text-muted"><?= $currency['name'] ?></small>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="text-end">
                                        <span class="fw-bold"><?= number_format(1 / $currency['rate'], 0) ?></span>
                                    </td>
                                </tr>
                                <?php endif; ?>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Popular Conversions -->
            <div class="card border-0 shadow-sm mt-4">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0"><i class="fas fa-star me-2"></i>Konversi Populer</h5>
                </div>
                <div class="card-body">
                    <div class="popular-conversion mb-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <span>1 USD</span>
                            <span class="fw-bold"><?= number_format(1 / $currencies['USD']['rate'], 0) ?> IDR</span>
                        </div>
                    </div>
                    <div class="popular-conversion mb-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <span>1 EUR</span>
                            <span class="fw-bold"><?= number_format(1 / $currencies['EUR']['rate'], 0) ?> IDR</span>
                        </div>
                    </div>
                    <div class="popular-conversion mb-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <span>1 SGD</span>
                            <span class="fw-bold"><?= number_format(1 / $currencies['SGD']['rate'], 0) ?> IDR</span>
                        </div>
                    </div>
                    <div class="popular-conversion mb-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <span>1 MYR</span>
                            <span class="fw-bold"><?= number_format(1 / $currencies['MYR']['rate'], 0) ?> IDR</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Conversion History -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-secondary text-white">
                    <h5 class="mb-0"><i class="fas fa-history me-2"></i>Riwayat Konversi</h5>
                </div>
                <div class="card-body">
                    <div id="conversionHistory">
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-clock fa-2x mb-3"></i>
                            <p>Riwayat konversi akan muncul di sini</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Currency data from PHP
const currencies = <?= json_encode($currencies) ?>;
let conversionHistory = [];

document.addEventListener('DOMContentLoaded', function() {
    // Initialize
    updateSymbols();
    generateQuickConversion();
    convert();

    // Event listeners
    document.getElementById('fromCurrency').addEventListener('change', function() {
        updateSymbols();
        convert();
    });

    document.getElementById('toCurrency').addEventListener('change', function() {
        updateSymbols();
        convert();
    });

    document.getElementById('fromAmount').addEventListener('input', function() {
        convert();
    });

    document.getElementById('swapCurrencies').addEventListener('click', function() {
        swapCurrencies();
    });

    // Format number inputs
    const numberInputs = document.querySelectorAll('.number-format');
    numberInputs.forEach(function(input) {
        input.addEventListener('input', function(e) {
            let value = e.target.value.replace(/[^\d]/g, '');
            if (value) {
                e.target.value = formatNumber(value);
            }
            convert();
        });
    });
});

function updateSymbols() {
    const fromCurrency = document.getElementById('fromCurrency').value;
    const toCurrency = document.getElementById('toCurrency').value;
    
    document.getElementById('fromSymbol').textContent = currencies[fromCurrency].symbol;
    document.getElementById('toSymbol').textContent = currencies[toCurrency].symbol;
}

function convert() {
    const fromCurrency = document.getElementById('fromCurrency').value;
    const toCurrency = document.getElementById('toCurrency').value;
    const fromAmountStr = document.getElementById('fromAmount').value.replace(/[^\d]/g, '');
    const fromAmount = parseFloat(fromAmountStr) || 0;

    if (fromAmount === 0) {
        document.getElementById('toAmount').value = '0';
        return;
    }

    // Convert to IDR first, then to target currency
    const idrAmount = fromAmount / currencies[fromCurrency].rate;
    const toAmount = idrAmount * currencies[toCurrency].rate;

    document.getElementById('toAmount').value = formatNumber(Math.round(toAmount * 100) / 100);
    
    // Update rate info
    const rate = currencies[toCurrency].rate / currencies[fromCurrency].rate;
    document.getElementById('rateInfo').textContent = `1 ${fromCurrency} = ${rate.toFixed(6)} ${toCurrency}`;

    // Add to history
    addToHistory(fromAmount, fromCurrency, toAmount, toCurrency);
}

function swapCurrencies() {
    const fromCurrency = document.getElementById('fromCurrency').value;
    const toCurrency = document.getElementById('toCurrency').value;
    
    document.getElementById('fromCurrency').value = toCurrency;
    document.getElementById('toCurrency').value = fromCurrency;
    
    updateSymbols();
    convert();
}

function formatNumber(num) {
    return new Intl.NumberFormat('id-ID').format(num);
}

function generateQuickConversion() {
    const quickAmounts = [100000, 500000, 1000000, 5000000, 10000000];
    const container = document.getElementById('quickConversion');
    
    quickAmounts.forEach(amount => {
        const col = document.createElement('div');
        col.className = 'col-md-4 col-6 mb-2';
        
        const button = document.createElement('button');
        button.className = 'btn btn-outline-success w-100 btn-sm';
        button.textContent = `Rp ${formatNumber(amount)}`;
        button.onclick = () => {
            document.getElementById('fromAmount').value = formatNumber(amount);
            document.getElementById('fromCurrency').value = 'IDR';
            updateSymbols();
            convert();
        };
        
        col.appendChild(button);
        container.appendChild(col);
    });
}

function addToHistory(fromAmount, fromCurrency, toAmount, toCurrency) {
    const historyItem = {
        fromAmount: fromAmount,
        fromCurrency: fromCurrency,
        toAmount: toAmount,
        toCurrency: toCurrency,
        timestamp: new Date()
    };
    
    conversionHistory.unshift(historyItem);
    
    // Keep only last 10 conversions
    if (conversionHistory.length > 10) {
        conversionHistory = conversionHistory.slice(0, 10);
    }
    
    updateHistoryDisplay();
}

function updateHistoryDisplay() {
    const container = document.getElementById('conversionHistory');
    
    if (conversionHistory.length === 0) {
        container.innerHTML = `
            <div class="text-center text-muted py-4">
                <i class="fas fa-clock fa-2x mb-3"></i>
                <p>Riwayat konversi akan muncul di sini</p>
            </div>
        `;
        return;
    }
    
    let html = '<div class="table-responsive"><table class="table table-sm"><thead><tr><th>Waktu</th><th>Dari</th><th>Ke</th></tr></thead><tbody>';
    
    conversionHistory.forEach(item => {
        html += `
            <tr>
                <td><small>${item.timestamp.toLocaleTimeString()}</small></td>
                <td>${formatNumber(item.fromAmount)} ${item.fromCurrency}</td>
                <td>${formatNumber(Math.round(item.toAmount * 100) / 100)} ${item.toCurrency}</td>
            </tr>
        `;
    });
    
    html += '</tbody></table></div>';
    container.innerHTML = html;
}
</script>

<style>
.popular-conversion {
    padding: 0.5rem 0;
    border-bottom: 1px solid #eee;
}

.popular-conversion:last-child {
    border-bottom: none;
}

.currency-flag {
    width: 30px;
    text-align: center;
}

#swapCurrencies {
    transition: transform 0.2s ease-in-out;
}

#swapCurrencies:hover {
    transform: rotate(180deg);
}
</style>

<?php include 'includes/views/layouts/footer.php'; ?>
