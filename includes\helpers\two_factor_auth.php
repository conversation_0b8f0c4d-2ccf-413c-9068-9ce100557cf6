<?php
/**
 * Two-Factor Authentication Helper Functions
 * 
 * This file contains functions for 2FA implementation
 */

/**
 * Generate 2FA secret key
 * @return string Base32 encoded secret
 */
function generate2FASecret() {
    $chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567';
    $secret = '';
    for ($i = 0; $i < 32; $i++) {
        $secret .= $chars[random_int(0, strlen($chars) - 1)];
    }
    return $secret;
}

/**
 * Generate TOTP code
 * @param string $secret Base32 encoded secret
 * @param int $timestamp Unix timestamp (optional)
 * @return string 6-digit TOTP code
 */
function generateTOTP($secret, $timestamp = null) {
    if ($timestamp === null) {
        $timestamp = time();
    }
    
    // Convert timestamp to 30-second intervals
    $timeSlice = floor($timestamp / 30);
    
    // Convert secret from base32
    $secretBinary = base32Decode($secret);
    
    // Pack time slice as 64-bit big-endian
    $timeBytes = pack('N*', 0) . pack('N*', $timeSlice);
    
    // Generate HMAC-SHA1
    $hash = hash_hmac('sha1', $timeBytes, $secretBinary, true);
    
    // Extract dynamic binary code
    $offset = ord($hash[19]) & 0xf;
    $code = (
        ((ord($hash[$offset]) & 0x7f) << 24) |
        ((ord($hash[$offset + 1]) & 0xff) << 16) |
        ((ord($hash[$offset + 2]) & 0xff) << 8) |
        (ord($hash[$offset + 3]) & 0xff)
    ) % 1000000;
    
    return str_pad($code, 6, '0', STR_PAD_LEFT);
}

/**
 * Verify TOTP code
 * @param string $secret Base32 encoded secret
 * @param string $code 6-digit code to verify
 * @param int $window Time window in 30-second intervals (default: 1)
 * @return bool True if code is valid
 */
function verifyTOTP($secret, $code, $window = 1) {
    $timestamp = time();
    
    // Check current time and surrounding windows
    for ($i = -$window; $i <= $window; $i++) {
        $testTime = $timestamp + ($i * 30);
        if (generateTOTP($secret, $testTime) === $code) {
            return true;
        }
    }
    
    return false;
}

/**
 * Generate QR code URL for 2FA setup
 * @param string $secret Base32 encoded secret
 * @param string $email User email
 * @param string $issuer Application name
 * @return string QR code URL
 */
function generate2FAQRCode($secret, $email, $issuer = 'Sistem Keuangan') {
    $otpauth = sprintf(
        'otpauth://totp/%s:%s?secret=%s&issuer=%s',
        urlencode($issuer),
        urlencode($email),
        $secret,
        urlencode($issuer)
    );
    
    return 'https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=' . urlencode($otpauth);
}

/**
 * Base32 decode function
 * @param string $data Base32 encoded data
 * @return string Binary data
 */
function base32Decode($data) {
    $alphabet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567';
    $data = strtoupper($data);
    $output = '';
    $v = 0;
    $vbits = 0;
    
    for ($i = 0; $i < strlen($data); $i++) {
        $c = $data[$i];
        if ($c === '=') break;
        
        $pos = strpos($alphabet, $c);
        if ($pos === false) continue;
        
        $v = ($v << 5) | $pos;
        $vbits += 5;
        
        if ($vbits >= 8) {
            $output .= chr(($v >> ($vbits - 8)) & 0xff);
            $vbits -= 8;
        }
    }
    
    return $output;
}

/**
 * Enable 2FA for user
 * @param int $userId User ID
 * @param string $secret 2FA secret
 * @return bool Success status
 */
function enable2FA($userId, $secret) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("
            UPDATE users 
            SET two_factor_secret = ?, two_factor_enabled = 1 
            WHERE id = ?
        ");
        
        $result = $stmt->execute([$secret, $userId]);
        
        if ($result) {
            logActivity($userId, "2FA enabled for account");
            logSystemEvent("2FA enabled", 'info', ['user_id' => $userId]);
        }
        
        return $result;
        
    } catch (Exception $e) {
        error_log("Enable 2FA error: " . $e->getMessage());
        return false;
    }
}

/**
 * Disable 2FA for user
 * @param int $userId User ID
 * @return bool Success status
 */
function disable2FA($userId) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("
            UPDATE users 
            SET two_factor_secret = NULL, two_factor_enabled = 0 
            WHERE id = ?
        ");
        
        $result = $stmt->execute([$userId]);
        
        if ($result) {
            logActivity($userId, "2FA disabled for account");
            logSystemEvent("2FA disabled", 'warning', ['user_id' => $userId]);
        }
        
        return $result;
        
    } catch (Exception $e) {
        error_log("Disable 2FA error: " . $e->getMessage());
        return false;
    }
}

/**
 * Check if user has 2FA enabled
 * @param int $userId User ID
 * @return bool True if 2FA is enabled
 */
function is2FAEnabled($userId) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("
            SELECT two_factor_enabled 
            FROM users 
            WHERE id = ?
        ");
        $stmt->execute([$userId]);
        
        return (bool)$stmt->fetchColumn();
        
    } catch (Exception $e) {
        error_log("Check 2FA status error: " . $e->getMessage());
        return false;
    }
}

/**
 * Get user's 2FA secret
 * @param int $userId User ID
 * @return string|null 2FA secret or null if not set
 */
function get2FASecret($userId) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("
            SELECT two_factor_secret 
            FROM users 
            WHERE id = ?
        ");
        $stmt->execute([$userId]);
        
        return $stmt->fetchColumn() ?: null;
        
    } catch (Exception $e) {
        error_log("Get 2FA secret error: " . $e->getMessage());
        return null;
    }
}

/**
 * Generate backup codes for 2FA
 * @param int $userId User ID
 * @return array Array of backup codes
 */
function generate2FABackupCodes($userId) {
    global $pdo;
    
    try {
        $codes = [];
        
        // Generate 10 backup codes
        for ($i = 0; $i < 10; $i++) {
            $codes[] = strtoupper(bin2hex(random_bytes(4)));
        }
        
        // Store hashed backup codes in database
        $hashedCodes = array_map('password_hash', $codes, array_fill(0, 10, PASSWORD_DEFAULT));
        
        $stmt = $pdo->prepare("
            UPDATE users 
            SET two_factor_backup_codes = ? 
            WHERE id = ?
        ");
        
        $stmt->execute([json_encode($hashedCodes), $userId]);
        
        logActivity($userId, "2FA backup codes generated");
        
        return $codes;
        
    } catch (Exception $e) {
        error_log("Generate backup codes error: " . $e->getMessage());
        return [];
    }
}

/**
 * Verify 2FA backup code
 * @param int $userId User ID
 * @param string $code Backup code to verify
 * @return bool True if code is valid
 */
function verify2FABackupCode($userId, $code) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("
            SELECT two_factor_backup_codes 
            FROM users 
            WHERE id = ?
        ");
        $stmt->execute([$userId]);
        
        $backupCodesJson = $stmt->fetchColumn();
        if (!$backupCodesJson) {
            return false;
        }
        
        $backupCodes = json_decode($backupCodesJson, true);
        if (!$backupCodes) {
            return false;
        }
        
        // Check if code matches any backup code
        foreach ($backupCodes as $index => $hashedCode) {
            if (password_verify($code, $hashedCode)) {
                // Remove used backup code
                unset($backupCodes[$index]);
                
                // Update database
                $stmt = $pdo->prepare("
                    UPDATE users 
                    SET two_factor_backup_codes = ? 
                    WHERE id = ?
                ");
                $stmt->execute([json_encode(array_values($backupCodes)), $userId]);
                
                logActivity($userId, "2FA backup code used");
                
                return true;
            }
        }
        
        return false;
        
    } catch (Exception $e) {
        error_log("Verify backup code error: " . $e->getMessage());
        return false;
    }
}

/**
 * Create 2FA table if not exists
 */
function create2FATable() {
    global $pdo;
    
    try {
        // Add 2FA columns to users table if they don't exist
        $pdo->exec("
            ALTER TABLE users 
            ADD COLUMN IF NOT EXISTS two_factor_enabled BOOLEAN DEFAULT FALSE,
            ADD COLUMN IF NOT EXISTS two_factor_secret VARCHAR(255) NULL,
            ADD COLUMN IF NOT EXISTS two_factor_backup_codes TEXT NULL
        ");
        
        return true;
        
    } catch (Exception $e) {
        error_log("Create 2FA table error: " . $e->getMessage());
        return false;
    }
}
?>
