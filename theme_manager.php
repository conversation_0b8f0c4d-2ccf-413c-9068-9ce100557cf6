<?php
require_once 'includes/config/database.php';
require_once 'includes/helpers/functions.php';
require_once 'includes/helpers/theme_helper.php';

// Check admin access
$currentUser = getCurrentUser();
if (!$currentUser || $currentUser['role'] !== 'admin') {
    setFlashMessage('danger', 'Akses ditolak! Anda tidak memiliki izin untuk mengakses halaman ini.');
    redirect('dashboard.php');
}

$pageTitle = 'Theme Manager';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    try {
        switch ($_POST['action']) {
            case 'apply_theme':
                $themeId = $_POST['theme_id'] ?? '';
                if ($themeId) {
                    $predefinedThemes = getPredefinedThemes();

                    if (isset($predefinedThemes[$themeId])) {
                        $theme = $predefinedThemes[$themeId];

                        // Update user theme preferences
                        $result = updateUserThemePreferences($currentUser['id'], [
                            'theme_mode' => $theme['theme_mode'],
                            'primary_color' => $theme['primary_color'],
                            'secondary_color' => $theme['secondary_color'],
                            'sidebar_color' => $theme['sidebar_color'],
                            'navbar_color' => $theme['navbar_color']
                        ]);

                        if ($result) {
                            setFlashMessage('success', 'Theme "' . $theme['name'] . '" berhasil diterapkan');
                        } else {
                            setFlashMessage('danger', 'Gagal menerapkan theme');
                        }
                    } else {
                        setFlashMessage('danger', 'Theme tidak ditemukan');
                    }
                }
                break;

            case 'create_custom_theme':
                $themeName = trim($_POST['theme_name'] ?? '');
                $themeData = [
                    'theme_mode' => $_POST['theme_mode'] ?? 'light',
                    'primary_color' => $_POST['primary_color'] ?? '#2563eb',
                    'secondary_color' => $_POST['secondary_color'] ?? '#64748b',
                    'sidebar_color' => $_POST['sidebar_color'] ?? 'dark',
                    'navbar_color' => $_POST['navbar_color'] ?? 'primary'
                ];

                if (!empty($themeName)) {
                    $stmt = $pdo->prepare("INSERT INTO custom_themes (name, theme_data, created_by) VALUES (?, ?, ?)");
                    $result = $stmt->execute([$themeName, json_encode($themeData), $currentUser['id']]);

                    if ($result) {
                        setFlashMessage('success', 'Custom theme "' . $themeName . '" berhasil dibuat');
                    } else {
                        setFlashMessage('danger', 'Gagal membuat custom theme');
                    }
                } else {
                    setFlashMessage('danger', 'Nama theme tidak boleh kosong');
                }
                break;

            case 'apply_custom_theme':
                $customThemeId = $_POST['custom_theme_id'] ?? '';
                if ($customThemeId) {
                    $stmt = $pdo->prepare("SELECT * FROM custom_themes WHERE id = ? AND created_by = ?");
                    $stmt->execute([$customThemeId, $currentUser['id']]);
                    $customTheme = $stmt->fetch();

                    if ($customTheme) {
                        $themeData = json_decode($customTheme['theme_data'], true);
                        $result = updateUserThemePreferences($currentUser['id'], $themeData);

                        if ($result) {
                            setFlashMessage('success', 'Custom theme "' . $customTheme['name'] . '" berhasil diterapkan');
                        } else {
                            setFlashMessage('danger', 'Gagal menerapkan custom theme');
                        }
                    } else {
                        setFlashMessage('danger', 'Custom theme tidak ditemukan');
                    }
                }
                break;
        }
        redirect('theme_manager.php');
    } catch (Exception $e) {
        setFlashMessage('danger', 'Error: ' . $e->getMessage());
    }
}

// Get current user theme preferences
$userTheme = getUserThemePreferences($currentUser['id']);

// Get predefined themes
$predefinedThemes = getPredefinedThemes();

// Get custom themes
try {
    $stmt = $pdo->prepare("SELECT * FROM custom_themes WHERE created_by = ? ORDER BY created_at DESC");
    $stmt->execute([$currentUser['id']]);
    $customThemes = $stmt->fetchAll();
} catch (PDOException $e) {
    $customThemes = [];
}

include 'includes/views/layouts/header.php';
?>

<div class="container-fluid px-4 py-3">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h3 class="mb-1">Theme Manager</h3>
                    <p class="text-muted mb-0">Manage and customize system themes</p>
                </div>
                <div class="btn-group">
                    <a href="system_customization.php" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>Back to Customization
                    </a>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createThemeModal">
                        <i class="fas fa-plus me-1"></i>Create Theme
                    </button>
                </div>
            </div>

            <!-- Current Theme -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Current Theme Settings</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label class="form-label">Theme Mode</label>
                                <div class="p-3 border rounded text-center" style="background: <?= $userTheme['theme_mode'] === 'dark' ? '#1e293b' : '#ffffff' ?>; color: <?= $userTheme['theme_mode'] === 'dark' ? '#ffffff' : '#1e293b' ?>;">
                                    <i class="fas fa-<?= $userTheme['theme_mode'] === 'dark' ? 'moon' : 'sun' ?> me-2"></i>
                                    <?= ucfirst($userTheme['theme_mode'] ?? 'light') ?> Mode
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label class="form-label">Primary Color</label>
                                <div class="p-3 border rounded text-center text-white" style="background: <?= $userTheme['primary_color'] ?? '#2563eb' ?>;">
                                    <i class="fas fa-palette me-2"></i>
                                    <?= $userTheme['primary_color'] ?? '#2563eb' ?>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label class="form-label">Sidebar Color</label>
                                <div class="p-3 border rounded bg-<?= $userTheme['sidebar_color'] ?? 'dark' ?> text-white text-center">
                                    <i class="fas fa-bars me-2"></i>
                                    <?= ucfirst($userTheme['sidebar_color'] ?? 'dark') ?>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label class="form-label">Navbar Color</label>
                                <div class="p-3 border rounded bg-<?= $userTheme['navbar_color'] ?? 'primary' ?> text-white text-center">
                                    <i class="fas fa-window-maximize me-2"></i>
                                    <?= ucfirst($userTheme['navbar_color'] ?? 'primary') ?>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="text-center mt-3">
                        <button type="button" class="btn btn-outline-primary me-2" data-bs-toggle="modal" data-bs-target="#customizeThemeModal">
                            <i class="fas fa-cog me-2"></i>Customize Current Theme
                        </button>
                        <a href="layout_manager.php" class="btn btn-outline-info">
                            <i class="fas fa-th-large me-2"></i>Layout Manager
                        </a>
                    </div>
                </div>
            </div>

            <!-- Predefined Themes -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Predefined Themes</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Default Theme -->
                        <div class="col-md-3 mb-3">
                            <div class="card theme-card">
                                <div class="card-body text-center">
                                    <div class="theme-preview mb-3">
                                        <div class="d-flex" style="height: 60px;">
                                            <div class="bg-dark" style="width: 30%; border-radius: 4px 0 0 4px;"></div>
                                            <div class="flex-grow-1">
                                                <div class="bg-primary" style="height: 20px; border-radius: 0 4px 0 0;"></div>
                                                <div class="bg-light border" style="height: 40px; border-radius: 0 0 4px 0;"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <h6>Default Theme</h6>
                                    <p class="small text-muted">Classic blue and dark sidebar</p>
                                    <form method="POST" class="d-inline">
                                        <input type="hidden" name="action" value="apply_theme">
                                        <input type="hidden" name="theme_id" value="default">
                                        <button type="submit" class="btn btn-outline-primary btn-sm">Apply</button>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <!-- Dark Theme -->
                        <div class="col-md-3 mb-3">
                            <div class="card theme-card">
                                <div class="card-body text-center">
                                    <div class="theme-preview mb-3">
                                        <div class="d-flex" style="height: 60px;">
                                            <div class="bg-dark" style="width: 30%; border-radius: 4px 0 0 4px;"></div>
                                            <div class="flex-grow-1">
                                                <div class="bg-dark" style="height: 20px; border-radius: 0 4px 0 0;"></div>
                                                <div class="bg-secondary" style="height: 40px; border-radius: 0 0 4px 0;"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <h6>Dark Theme</h6>
                                    <p class="small text-muted">Full dark mode</p>
                                    <form method="POST" class="d-inline">
                                        <input type="hidden" name="action" value="apply_theme">
                                        <input type="hidden" name="theme_id" value="dark">
                                        <button type="submit" class="btn btn-outline-dark btn-sm">Apply</button>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <!-- Modern Theme -->
                        <div class="col-md-3 mb-3">
                            <div class="card theme-card">
                                <div class="card-body text-center">
                                    <div class="theme-preview mb-3">
                                        <div class="d-flex" style="height: 60px;">
                                            <div class="bg-primary" style="width: 30%; border-radius: 4px 0 0 4px;"></div>
                                            <div class="flex-grow-1">
                                                <div class="bg-primary" style="height: 20px; border-radius: 0 4px 0 0;"></div>
                                                <div class="bg-light border" style="height: 40px; border-radius: 0 0 4px 0;"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <h6>Modern Theme</h6>
                                    <p class="small text-muted">Blue modern design</p>
                                    <form method="POST" class="d-inline">
                                        <input type="hidden" name="action" value="apply_theme">
                                        <input type="hidden" name="theme_id" value="modern">
                                        <button type="submit" class="btn btn-outline-primary btn-sm">Apply</button>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <!-- Minimal Theme -->
                        <div class="col-md-3 mb-3">
                            <div class="card theme-card">
                                <div class="card-body text-center">
                                    <div class="theme-preview mb-3">
                                        <div class="d-flex" style="height: 60px;">
                                            <div class="bg-light border" style="width: 30%; border-radius: 4px 0 0 4px;"></div>
                                            <div class="flex-grow-1">
                                                <div class="bg-light border" style="height: 20px; border-radius: 0 4px 0 0;"></div>
                                                <div class="bg-white border" style="height: 40px; border-radius: 0 0 4px 0;"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <h6>Minimal Theme</h6>
                                    <p class="small text-muted">Clean light design</p>
                                    <form method="POST" class="d-inline">
                                        <input type="hidden" name="action" value="apply_theme">
                                        <input type="hidden" name="theme_id" value="minimal">
                                        <button type="submit" class="btn btn-outline-secondary btn-sm">Apply</button>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <!-- Ocean Theme -->
                        <div class="col-md-3 mb-3">
                            <div class="card theme-card">
                                <div class="card-body text-center">
                                    <div class="theme-preview mb-3">
                                        <div class="d-flex" style="height: 60px;">
                                            <div class="bg-info" style="width: 30%; border-radius: 4px 0 0 4px;"></div>
                                            <div class="flex-grow-1">
                                                <div class="bg-info" style="height: 20px; border-radius: 0 4px 0 0;"></div>
                                                <div class="bg-light border" style="height: 40px; border-radius: 0 0 4px 0;"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <h6>Ocean Theme</h6>
                                    <p class="small text-muted">Blue ocean inspired</p>
                                    <form method="POST" class="d-inline">
                                        <input type="hidden" name="action" value="apply_theme">
                                        <input type="hidden" name="theme_id" value="ocean">
                                        <button type="submit" class="btn btn-outline-info btn-sm">Apply</button>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <!-- Forest Theme -->
                        <div class="col-md-3 mb-3">
                            <div class="card theme-card">
                                <div class="card-body text-center">
                                    <div class="theme-preview mb-3">
                                        <div class="d-flex" style="height: 60px;">
                                            <div class="bg-success" style="width: 30%; border-radius: 4px 0 0 4px;"></div>
                                            <div class="flex-grow-1">
                                                <div class="bg-success" style="height: 20px; border-radius: 0 4px 0 0;"></div>
                                                <div class="bg-light border" style="height: 40px; border-radius: 0 0 4px 0;"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <h6>Forest Theme</h6>
                                    <p class="small text-muted">Green nature inspired</p>
                                    <form method="POST" class="d-inline">
                                        <input type="hidden" name="action" value="apply_theme">
                                        <input type="hidden" name="theme_id" value="forest">
                                        <button type="submit" class="btn btn-outline-success btn-sm">Apply</button>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <!-- Colorful Modern Theme -->
                        <div class="col-md-3 mb-3">
                            <div class="card theme-card">
                                <div class="card-body text-center">
                                    <div class="theme-preview mb-3">
                                        <div class="d-flex" style="height: 60px;">
                                            <div style="width: 30%; border-radius: 4px 0 0 4px; background: linear-gradient(135deg, #ff6b6b 0%, #4ecdc4 100%);"></div>
                                            <div class="flex-grow-1">
                                                <div style="height: 20px; border-radius: 0 4px 0 0; background: linear-gradient(90deg, #ff6b6b 0%, #4ecdc4 100%);"></div>
                                                <div class="bg-light border" style="height: 40px; border-radius: 0 0 4px 0;"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <h6>Colorful Modern</h6>
                                    <p class="small text-muted">Vibrant gradient design</p>
                                    <form method="POST" class="d-inline">
                                        <input type="hidden" name="action" value="apply_theme">
                                        <input type="hidden" name="theme_id" value="colorful">
                                        <button type="submit" class="btn btn-outline-primary btn-sm">Apply</button>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <!-- Glassmorphism Theme -->
                        <div class="col-md-3 mb-3">
                            <div class="card theme-card">
                                <div class="card-body text-center">
                                    <div class="theme-preview mb-3">
                                        <div class="d-flex" style="height: 60px;">
                                            <div style="width: 30%; border-radius: 4px 0 0 4px; background: rgba(255, 255, 255, 0.25); backdrop-filter: blur(10px); border: 1px solid rgba(255, 255, 255, 0.18);"></div>
                                            <div class="flex-grow-1">
                                                <div style="height: 20px; border-radius: 0 4px 0 0; background: rgba(255, 255, 255, 0.25); backdrop-filter: blur(10px); border: 1px solid rgba(255, 255, 255, 0.18);"></div>
                                                <div class="bg-light border" style="height: 40px; border-radius: 0 0 4px 0;"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <h6>Glassmorphism</h6>
                                    <p class="small text-muted">Modern glass effect</p>
                                    <form method="POST" class="d-inline">
                                        <input type="hidden" name="action" value="apply_theme">
                                        <input type="hidden" name="theme_id" value="glassmorphism">
                                        <button type="submit" class="btn btn-outline-info btn-sm">Apply</button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Layout Design Manager Link -->
                    <div class="text-center mt-4">
                        <div class="alert alert-info">
                            <h6 class="alert-heading">
                                <i class="fas fa-th-large me-2"></i>
                                Want More Layout Options?
                            </h6>
                            <p class="mb-3">Customize individual components like sidebar, navbar, footer, and main content with modern and colorful designs!</p>
                            <a href="layout_manager.php" class="btn btn-primary">
                                <i class="fas fa-paint-brush me-2"></i>
                                Open Layout Design Manager
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Custom Themes -->
            <?php if (!empty($customThemes)): ?>
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Custom Themes</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <?php foreach ($customThemes as $theme): ?>
                            <?php $themeData = json_decode($theme['theme_data'], true); ?>
                            <div class="col-md-3 mb-3">
                                <div class="card theme-card">
                                    <div class="card-body text-center">
                                        <div class="theme-preview mb-3">
                                            <div class="d-flex" style="height: 60px;">
                                                <div class="bg-<?= $themeData['sidebar_color'] ?? 'dark' ?>" style="width: 30%; border-radius: 4px 0 0 4px;"></div>
                                                <div class="flex-grow-1">
                                                    <div class="bg-<?= $themeData['navbar_color'] ?? 'primary' ?>" style="height: 20px; border-radius: 0 4px 0 0;"></div>
                                                    <div class="bg-light border" style="height: 40px; border-radius: 0 0 4px 0;"></div>
                                                </div>
                                            </div>
                                        </div>
                                        <h6><?= htmlspecialchars($theme['name']) ?></h6>
                                        <p class="small text-muted">Custom theme</p>
                                        <form method="POST" class="d-inline">
                                            <input type="hidden" name="action" value="apply_custom_theme">
                                            <input type="hidden" name="custom_theme_id" value="<?= $theme['id'] ?>">
                                            <button type="submit" class="btn btn-outline-success btn-sm">Apply</button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Customize Current Theme Modal -->
<div class="modal fade" id="customizeThemeModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Customize Current Theme</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="customizeThemeForm">
                <div class="modal-body">
                    <input type="hidden" name="action" value="create_custom_theme">

                    <div class="mb-3">
                        <label class="form-label">Theme Name</label>
                        <input type="text" class="form-control" name="theme_name" placeholder="My Custom Theme" required>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Theme Mode</label>
                                <select class="form-select" name="theme_mode">
                                    <option value="light" <?= ($userTheme['theme_mode'] ?? 'light') === 'light' ? 'selected' : '' ?>>Light</option>
                                    <option value="dark" <?= ($userTheme['theme_mode'] ?? 'light') === 'dark' ? 'selected' : '' ?>>Dark</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Primary Color</label>
                                <input type="color" class="form-control form-control-color" name="primary_color" value="<?= $userTheme['primary_color'] ?? '#2563eb' ?>">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Secondary Color</label>
                                <input type="color" class="form-control form-control-color" name="secondary_color" value="<?= $userTheme['secondary_color'] ?? '#64748b' ?>">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Sidebar Color</label>
                                <select class="form-select" name="sidebar_color">
                                    <option value="dark" <?= ($userTheme['sidebar_color'] ?? 'dark') === 'dark' ? 'selected' : '' ?>>Dark</option>
                                    <option value="light" <?= ($userTheme['sidebar_color'] ?? 'dark') === 'light' ? 'selected' : '' ?>>Light</option>
                                    <option value="primary" <?= ($userTheme['sidebar_color'] ?? 'dark') === 'primary' ? 'selected' : '' ?>>Primary</option>
                                    <option value="secondary" <?= ($userTheme['sidebar_color'] ?? 'dark') === 'secondary' ? 'selected' : '' ?>>Secondary</option>
                                    <option value="success" <?= ($userTheme['sidebar_color'] ?? 'dark') === 'success' ? 'selected' : '' ?>>Success</option>
                                    <option value="info" <?= ($userTheme['sidebar_color'] ?? 'dark') === 'info' ? 'selected' : '' ?>>Info</option>
                                    <option value="warning" <?= ($userTheme['sidebar_color'] ?? 'dark') === 'warning' ? 'selected' : '' ?>>Warning</option>
                                    <option value="danger" <?= ($userTheme['sidebar_color'] ?? 'dark') === 'danger' ? 'selected' : '' ?>>Danger</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Navbar Color</label>
                        <select class="form-select" name="navbar_color">
                            <option value="primary" <?= ($userTheme['navbar_color'] ?? 'primary') === 'primary' ? 'selected' : '' ?>>Primary</option>
                            <option value="dark" <?= ($userTheme['navbar_color'] ?? 'primary') === 'dark' ? 'selected' : '' ?>>Dark</option>
                            <option value="light" <?= ($userTheme['navbar_color'] ?? 'primary') === 'light' ? 'selected' : '' ?>>Light</option>
                            <option value="secondary" <?= ($userTheme['navbar_color'] ?? 'primary') === 'secondary' ? 'selected' : '' ?>>Secondary</option>
                            <option value="success" <?= ($userTheme['navbar_color'] ?? 'primary') === 'success' ? 'selected' : '' ?>>Success</option>
                            <option value="info" <?= ($userTheme['navbar_color'] ?? 'primary') === 'info' ? 'selected' : '' ?>>Info</option>
                            <option value="warning" <?= ($userTheme['navbar_color'] ?? 'primary') === 'warning' ? 'selected' : '' ?>>Warning</option>
                            <option value="danger" <?= ($userTheme['navbar_color'] ?? 'primary') === 'danger' ? 'selected' : '' ?>>Danger</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Preview</label>
                        <div class="theme-preview-large border rounded p-3">
                            <div class="d-flex" style="height: 120px;">
                                <div id="preview-sidebar" class="bg-dark" style="width: 25%; border-radius: 6px 0 0 6px;"></div>
                                <div class="flex-grow-1">
                                    <div id="preview-navbar" class="bg-primary" style="height: 30px; border-radius: 0 6px 0 0;"></div>
                                    <div id="preview-content" class="bg-light border-start border-bottom border-end" style="height: 90px; border-radius: 0 0 6px 0;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Save Custom Theme</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Create Custom Theme Modal -->
<div class="modal fade" id="createThemeModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Create Custom Theme</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="create_custom_theme">

                    <div class="mb-3">
                        <label class="form-label">Theme Name</label>
                        <input type="text" class="form-control" name="theme_name" required>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Sidebar Color</label>
                                <select class="form-select" name="sidebar_color">
                                    <option value="dark">Dark</option>
                                    <option value="light">Light</option>
                                    <option value="primary">Primary</option>
                                    <option value="secondary">Secondary</option>
                                    <option value="success">Success</option>
                                    <option value="info">Info</option>
                                    <option value="warning">Warning</option>
                                    <option value="danger">Danger</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Navbar Color</label>
                                <select class="form-select" name="navbar_color">
                                    <option value="primary">Primary</option>
                                    <option value="dark">Dark</option>
                                    <option value="light">Light</option>
                                    <option value="secondary">Secondary</option>
                                    <option value="success">Success</option>
                                    <option value="info">Info</option>
                                    <option value="warning">Warning</option>
                                    <option value="danger">Danger</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Theme Mode</label>
                                <select class="form-select" name="theme_mode">
                                    <option value="light">Light</option>
                                    <option value="dark">Dark</option>
                                    <option value="auto">Auto</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Primary Color</label>
                                <input type="color" class="form-control form-control-color" name="primary_color" value="#007bff">
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Secondary Color</label>
                        <input type="color" class="form-control form-control-color" name="secondary_color" value="#6c757d">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Create Theme</button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.theme-card {
    transition: all 0.3s ease;
    cursor: pointer;
}

.theme-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.theme-preview {
    border: 1px solid #dee2e6;
    border-radius: 6px;
    overflow: hidden;
}

.theme-preview-large {
    background: #f8f9fa;
}

.color-dot {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 1px solid #dee2e6;
    margin-right: 4px;
}

.theme-card.active {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.2);
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Live preview functionality
    const customizeForm = document.getElementById('customizeThemeForm');
    if (customizeForm) {
        const previewSidebar = document.getElementById('preview-sidebar');
        const previewNavbar = document.getElementById('preview-navbar');
        const previewContent = document.getElementById('preview-content');

        // Update preview when form changes
        customizeForm.addEventListener('change', updatePreview);
        customizeForm.addEventListener('input', updatePreview);

        function updatePreview() {
            const formData = new FormData(customizeForm);
            const sidebarColor = formData.get('sidebar_color');
            const navbarColor = formData.get('navbar_color');
            const themeMode = formData.get('theme_mode');
            const primaryColor = formData.get('primary_color');

            // Update sidebar preview
            previewSidebar.className = `bg-${sidebarColor}`;
            previewSidebar.style.width = '25%';
            previewSidebar.style.borderRadius = '6px 0 0 6px';

            // Update navbar preview
            if (navbarColor === 'primary') {
                previewNavbar.style.backgroundColor = primaryColor;
                previewNavbar.className = '';
            } else {
                previewNavbar.className = `bg-${navbarColor}`;
                previewNavbar.style.backgroundColor = '';
            }
            previewNavbar.style.height = '30px';
            previewNavbar.style.borderRadius = '0 6px 0 0';

            // Update content preview based on theme mode
            if (themeMode === 'dark') {
                previewContent.className = 'bg-dark border-start border-bottom border-end';
                previewContent.style.color = '#ffffff';
            } else {
                previewContent.className = 'bg-light border-start border-bottom border-end';
                previewContent.style.color = '#000000';
            }
            previewContent.style.height = '90px';
            previewContent.style.borderRadius = '0 0 6px 0';
        }

        // Initialize preview
        updatePreview();
    }

    // Apply theme with AJAX for instant preview
    const themeCards = document.querySelectorAll('.theme-card');
    themeCards.forEach(card => {
        const applyBtn = card.querySelector('button[type="submit"]');
        if (applyBtn) {
            applyBtn.addEventListener('click', function(e) {
                e.preventDefault();

                const form = this.closest('form');
                const formData = new FormData(form);

                // Show loading state
                const originalText = this.innerHTML;
                this.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Applying...';
                this.disabled = true;

                // Apply theme via AJAX
                fetch('api/theme.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'apply_predefined_theme',
                        theme_id: formData.get('theme_id')
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Apply theme immediately
                        applyThemeToPage(data.theme);

                        // Show success message
                        showNotification('Theme applied successfully!', 'success');

                        // Reload page after short delay to update UI
                        setTimeout(() => {
                            window.location.reload();
                        }, 1000);
                    } else {
                        showNotification(data.message || 'Failed to apply theme', 'error');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showNotification('Failed to apply theme', 'error');
                })
                .finally(() => {
                    // Restore button state
                    this.innerHTML = originalText;
                    this.disabled = false;
                });
            });
        }
    });

    // Apply theme to page immediately
    function applyThemeToPage(theme) {
        if (!theme) return;

        // Update theme mode
        document.documentElement.setAttribute('data-bs-theme', theme.theme_mode);

        // Update CSS variables
        const root = document.documentElement;
        root.style.setProperty('--primary-color', theme.primary_color);
        root.style.setProperty('--secondary-color', theme.secondary_color);

        // Update navbar theme toggle icon
        const themeToggleIcons = document.querySelectorAll('[data-theme-toggle] i');
        themeToggleIcons.forEach(icon => {
            icon.className = theme.theme_mode === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
        });

        // Store theme preference
        localStorage.setItem('theme', theme.theme_mode);
    }

    // Show notification
    function showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(notification);

        // Auto remove after 3 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 3000);
    }

    // Real-time theme sync with navbar toggle
    document.addEventListener('themeChanged', function(e) {
        const newTheme = e.detail.theme;

        // Update user preferences via API
        fetch('api/theme.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'update_preferences',
                theme_mode: newTheme,
                primary_color: getComputedStyle(document.documentElement).getPropertyValue('--primary-color').trim() || '#2563eb',
                secondary_color: getComputedStyle(document.documentElement).getPropertyValue('--secondary-color').trim() || '#64748b',
                sidebar_color: 'dark', // Default
                navbar_color: 'primary' // Default
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                console.log('Theme preferences updated');
            }
        })
        .catch(error => {
            console.error('Error updating theme preferences:', error);
        });
    });
});
</script>

<?php include 'includes/views/layouts/footer.php'; ?>
