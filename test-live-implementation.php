<?php
// Simple test page that mimics the live environment structure
session_start();

// Set dummy user data for testing
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1;
    $_SESSION['user_name'] = 'Test User';
    $_SESSION['user_role'] = 'admin';
}

// Mock current user data
$currentUser = [
    'id' => 1,
    'nama' => 'Test User',
    'email' => '<EMAIL>',
    'role' => 'admin'
];

$pageTitle = 'Live Implementation Test - KeuanganKu';
$unreadNotifications = 3;

// Include header (this will load all CSS and JS)
include 'includes/views/layouts/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-rocket me-2"></i>Live Implementation Test
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-success">
                        <h6><i class="fas fa-check-circle me-2"></i>Live Environment Test</h6>
                        <p class="mb-2">This page tests the Customize Layout and Dark Mode functionality in the live environment using the actual layout structure.</p>
                        <ul class="mb-0">
                            <li>✅ Uses actual header.php, navbar.php, and footer.php</li>
                            <li>✅ Loads all CSS and JavaScript files</li>
                            <li>✅ Tests real navbar buttons</li>
                            <li>✅ Tests Control Sidebar functionality</li>
                        </ul>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0"><i class="fas fa-cogs me-2"></i>Customize Layout Test</h6>
                                </div>
                                <div class="card-body">
                                    <p>Click the <strong>gear icon (⚙️)</strong> in the navbar to test:</p>
                                    <ul>
                                        <li>Control Sidebar opens from right</li>
                                        <li>Quick Settings (Dark Mode, Sidebar Collapse)</li>
                                        <li>Navbar color customization</li>
                                        <li>Icon color customization</li>
                                        <li>Layout options</li>
                                    </ul>
                                    <button class="btn btn-primary btn-sm" onclick="testControlSidebar()">
                                        <i class="fas fa-cogs me-1"></i>Manual Test
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0"><i class="fas fa-moon me-2"></i>Dark Mode Test</h6>
                                </div>
                                <div class="card-body">
                                    <p>Click the <strong>moon/sun icon (🌙/☀️)</strong> in the navbar to test:</p>
                                    <ul>
                                        <li>Theme toggles between light/dark</li>
                                        <li>Icon changes (moon ↔ sun)</li>
                                        <li>All UI elements change color</li>
                                        <li>Settings persist on reload</li>
                                    </ul>
                                    <button class="btn btn-secondary btn-sm" onclick="testDarkMode()">
                                        <i class="fas fa-moon me-1"></i>Manual Test
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h6>Current Theme</h6>
                                    <p id="currentTheme" class="mb-0 fw-bold">Loading...</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h6>Control Sidebar</h6>
                                    <p id="controlSidebarState" class="mb-0 fw-bold">Loading...</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h6>Sidebar State</h6>
                                    <p id="sidebarState" class="mb-0 fw-bold">Loading...</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mt-4">
                        <h6>Test Results</h6>
                        <div id="testResults" class="alert alert-info">
                            <div class="d-flex align-items-center">
                                <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                                <span>Running tests...</span>
                            </div>
                        </div>
                    </div>

                    <div class="mt-4">
                        <h6>Sample UI Elements (for Dark Mode Testing)</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Sample Input</label>
                                    <input type="text" class="form-control" placeholder="Test input field">
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="sampleCheck">
                                    <label class="form-check-label" for="sampleCheck">Sample checkbox</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-primary">Primary</button>
                                    <button type="button" class="btn btn-outline-secondary">Outline</button>
                                    <button type="button" class="btn btn-success">Success</button>
                                </div>
                                <div class="mt-3">
                                    <div class="alert alert-warning mb-2">Sample warning alert</div>
                                    <div class="alert alert-info mb-0">Sample info alert</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Test functions
function testControlSidebar() {
    console.log('Testing Control Sidebar...');
    
    // Try global function first
    if (typeof window.toggleControlSidebarGlobal === 'function') {
        window.toggleControlSidebarGlobal();
        updateTestResults('✅ Control Sidebar: Global function works');
    } else {
        // Fallback to manual toggle
        const controlSidebar = document.getElementById('controlSidebar');
        const controlSidebarOverlay = document.getElementById('controlSidebarOverlay');
        
        if (controlSidebar && controlSidebarOverlay) {
            controlSidebar.classList.toggle('open');
            controlSidebarOverlay.classList.toggle('show');
            document.body.style.overflow = controlSidebar.classList.contains('open') ? 'hidden' : '';
            updateTestResults('✅ Control Sidebar: Manual toggle works');
        } else {
            updateTestResults('❌ Control Sidebar: Elements not found');
        }
    }
    refreshStatus();
}

function testDarkMode() {
    console.log('Testing Dark Mode...');
    
    if (typeof toggleTheme === 'function') {
        toggleTheme();
        updateTestResults('✅ Dark Mode: Toggle function works');
    } else {
        updateTestResults('❌ Dark Mode: Toggle function not found');
    }
    refreshStatus();
}

function refreshStatus() {
    // Update current theme
    const currentTheme = document.documentElement.getAttribute('data-bs-theme') || 'light';
    document.getElementById('currentTheme').textContent = currentTheme;
    
    // Update sidebar state
    const sidebarCollapsed = document.body.classList.contains('sidebar-collapsed');
    document.getElementById('sidebarState').textContent = sidebarCollapsed ? 'Collapsed' : 'Expanded';
    
    // Update control sidebar state
    const controlSidebar = document.getElementById('controlSidebar');
    const controlSidebarOpen = controlSidebar ? controlSidebar.classList.contains('open') : false;
    document.getElementById('controlSidebarState').textContent = controlSidebarOpen ? 'Open' : 'Closed';
}

function updateTestResults(message) {
    const resultsDiv = document.getElementById('testResults');
    const currentContent = resultsDiv.innerHTML;
    
    if (currentContent.includes('Running tests...')) {
        resultsDiv.innerHTML = '<div><strong>Test Results:</strong></div>';
        resultsDiv.className = 'alert alert-success';
    }
    
    resultsDiv.innerHTML += '<div>' + message + '</div>';
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    console.log('Live Implementation Test: Page loaded');
    
    // Run automatic tests
    setTimeout(function() {
        runAutomaticTests();
        refreshStatus();
    }, 1000);
});

function runAutomaticTests() {
    let results = [];
    
    // Test 1: Check if Control Sidebar elements exist
    const controlSidebar = document.getElementById('controlSidebar');
    const controlSidebarOverlay = document.getElementById('controlSidebarOverlay');
    const controlSidebarToggle = document.querySelector('[data-widget="control-sidebar"]');
    
    results.push(controlSidebar ? '✅ Control Sidebar element exists' : '❌ Control Sidebar element missing');
    results.push(controlSidebarOverlay ? '✅ Control Sidebar overlay exists' : '❌ Control Sidebar overlay missing');
    results.push(controlSidebarToggle ? '✅ Control Sidebar toggle button exists' : '❌ Control Sidebar toggle button missing');
    
    // Test 2: Check if Dark Mode toggle exists
    const darkModeToggle = document.querySelector('[data-theme-toggle]');
    results.push(darkModeToggle ? '✅ Dark Mode toggle button exists' : '❌ Dark Mode toggle button missing');
    
    // Test 3: Check if functions are available
    results.push(typeof toggleTheme === 'function' ? '✅ toggleTheme function available' : '❌ toggleTheme function missing');
    results.push(typeof window.toggleControlSidebarGlobal === 'function' ? '✅ toggleControlSidebarGlobal function available' : '❌ toggleControlSidebarGlobal function missing');
    
    // Test 4: Check if CSS files are loaded
    const layoutManagerCSS = document.querySelector('link[href*="layout-manager.css"]');
    const controlSidebarCSS = document.querySelector('link[href*="control-sidebar.css"]');
    
    results.push(layoutManagerCSS ? '✅ Layout Manager CSS loaded' : '❌ Layout Manager CSS missing');
    results.push(controlSidebarCSS ? '✅ Control Sidebar CSS loaded' : '❌ Control Sidebar CSS missing');
    
    // Update results
    const resultsDiv = document.getElementById('testResults');
    resultsDiv.innerHTML = '<div><strong>Automatic Test Results:</strong></div>' + results.map(r => '<div>' + r + '</div>').join('');
    
    // Set alert class based on results
    const hasErrors = results.some(r => r.includes('❌'));
    resultsDiv.className = hasErrors ? 'alert alert-warning' : 'alert alert-success';
}
</script>

<?php include 'includes/views/layouts/footer.php'; ?>
