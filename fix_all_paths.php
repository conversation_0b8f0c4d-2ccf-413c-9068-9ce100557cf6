<?php
// Comprehensive script to fix all path issues in PHP files

$files = glob('*.php');
$results = [];

// Patterns to fix
$patterns = [
    // Redirect patterns
    "redirect('/login.php')" => "redirect('/keuangan/login.php')",
    "redirect('/supplier.php')" => "redirect('/keuangan/supplier.php')",
    "redirect('/inventory.php')" => "redirect('/keuangan/inventory.php')",
    "redirect('/return.php')" => "redirect('/keuangan/return.php')",
    "redirect('/pembelian.php')" => "redirect('/keuangan/pembelian.php')",
    "redirect('/pengingat.php')" => "redirect('/keuangan/pengingat.php')",
    "redirect('/backup_restore.php')" => "redirect('/keuangan/backup_restore.php')",
    "redirect('/kalkulator.php')" => "redirect('/keuangan/kalkulator.php')",
    "redirect('/panduan.php')" => "redirect('/keuangan/panduan.php')",
    "redirect('/faq.php')" => "redirect('/keuangan/faq.php')",
    "redirect('/support.php')" => "redirect('/keuangan/support.php')",
    "redirect('/tutorial.php')" => "redirect('/keuangan/tutorial.php')",
    "redirect('/konverter.php')" => "redirect('/keuangan/konverter.php')",
    "redirect('/kalender.php')" => "redirect('/keuangan/kalender.php')",
    "redirect('/laporan_bisnis.php')" => "redirect('/keuangan/laporan_bisnis.php')",
    "redirect('/laporan_pajak.php')" => "redirect('/keuangan/laporan_pajak.php')",
    
    // Href patterns for refresh links
    'href="supplier.php"' => 'href="/keuangan/supplier.php"',
    'href="inventory.php"' => 'href="/keuangan/inventory.php"',
    'href="return.php"' => 'href="/keuangan/return.php"',
    'href="pembelian.php"' => 'href="/keuangan/pembelian.php"',
    'href="pengingat.php"' => 'href="/keuangan/pengingat.php"',
    'href="backup_restore.php"' => 'href="/keuangan/backup_restore.php"',
    'href="kalkulator.php"' => 'href="/keuangan/kalkulator.php"',
    'href="panduan.php"' => 'href="/keuangan/panduan.php"',
    'href="faq.php"' => 'href="/keuangan/faq.php"',
    'href="support.php"' => 'href="/keuangan/support.php"',
    'href="tutorial.php"' => 'href="/keuangan/tutorial.php"',
    'href="konverter.php"' => 'href="/keuangan/konverter.php"',
    'href="kalender.php"' => 'href="/keuangan/kalender.php"',
    'href="laporan_bisnis.php"' => 'href="/keuangan/laporan_bisnis.php"',
    'href="laporan_pajak.php"' => 'href="/keuangan/laporan_pajak.php"',
    
    // Index.php links
    'href="index.php"' => 'href="/keuangan/index.php"',
    
    // Dashboard links
    'href="dashboard.php"' => 'href="/keuangan/dashboard.php"',
];

foreach ($files as $file) {
    if (in_array($file, ['fix_all_paths.php', 'fix_redirects.php', 'migrate_supplier_status.php', 'setup_database.php'])) {
        continue; // Skip utility files
    }
    
    $content = file_get_contents($file);
    $originalContent = $content;
    $changes = 0;
    
    foreach ($patterns as $old => $new) {
        $newContent = str_replace($old, $new, $content);
        if ($newContent !== $content) {
            $changes += substr_count($content, $old);
            $content = $newContent;
        }
    }
    
    if ($content !== $originalContent) {
        file_put_contents($file, $content);
        $results[] = "✅ Fixed $changes path(s) in: $file";
    } else {
        $results[] = "ℹ️ No paths to fix in: $file";
    }
}

// Also check includes/views/layouts files
$layoutFiles = [
    'includes/views/layouts/header.php',
    'includes/views/layouts/sidebar.php',
    'includes/views/layouts/footer.php'
];

foreach ($layoutFiles as $file) {
    if (file_exists($file)) {
        $content = file_get_contents($file);
        $originalContent = $content;
        $changes = 0;
        
        foreach ($patterns as $old => $new) {
            $newContent = str_replace($old, $new, $content);
            if ($newContent !== $content) {
                $changes += substr_count($content, $old);
                $content = $newContent;
            }
        }
        
        if ($content !== $originalContent) {
            file_put_contents($file, $content);
            $results[] = "✅ Fixed $changes path(s) in: $file";
        } else {
            $results[] = "ℹ️ No paths to fix in: $file";
        }
    }
}

?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fix All Paths - KeuanganKu</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="card shadow">
                    <div class="card-header bg-success text-white">
                        <h4 class="mb-0">
                            <i class="fas fa-check-circle me-2"></i>Path Fix Results
                        </h4>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-success">
                            <h6><i class="fas fa-info-circle me-2"></i>All paths have been checked and fixed!</h6>
                            <p class="mb-0">All redirect URLs and href links now use the correct /keuangan/ path.</p>
                        </div>
                        
                        <h6>Detailed Results:</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <ul class="list-group list-group-flush">
                                    <?php foreach (array_slice($results, 0, ceil(count($results)/2)) as $result): ?>
                                    <li class="list-group-item border-0 px-0">
                                        <small><?= $result ?></small>
                                    </li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <ul class="list-group list-group-flush">
                                    <?php foreach (array_slice($results, ceil(count($results)/2)) as $result): ?>
                                    <li class="list-group-item border-0 px-0">
                                        <small><?= $result ?></small>
                                    </li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        </div>
                        
                        <div class="text-center mt-4">
                            <a href="/keuangan/index.php" class="btn btn-primary me-2">
                                <i class="fas fa-home me-2"></i>Go to Dashboard
                            </a>
                            <a href="/keuangan/supplier.php" class="btn btn-success me-2">
                                <i class="fas fa-truck me-2"></i>Test Supplier
                            </a>
                            <a href="/keuangan/inventory.php" class="btn btn-info">
                                <i class="fas fa-boxes me-2"></i>Test Inventory
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
