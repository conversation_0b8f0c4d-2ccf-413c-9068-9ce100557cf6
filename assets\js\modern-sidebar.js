/**
 * Modern Responsive Sidebar
 * Handles all sidebar functionality including responsive behavior, animations, and interactions
 */

class ModernSidebar {
    constructor() {
        this.sidebar = document.querySelector('.modern-sidebar');
        this.overlay = document.querySelector('.sidebar-overlay');
        this.toggleBtn = document.querySelector('.sidebar-toggle-btn');
        this.mobileToggleBtn = document.querySelector('.mobile-sidebar-toggle');
        this.body = document.body;

        this.isCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
        this.isMobile = window.innerWidth <= 1024;
        this.submenuOverlays = new Map();

        this.init();
    }

    init() {
        console.log('🚀 Modern Sidebar initialized');

        this.createOverlay();
        this.setupEventListeners();
        this.setupResponsive();
        this.loadSavedState();
        this.setupTooltips();
        this.setupSubmenuHandlers();
        this.setupSubmenuOverlays(); // Add this line
        this.setupActiveMenuDetection();

        // Apply initial state
        this.updateSidebarState();
    }

    createOverlay() {
        if (!this.overlay) {
            this.overlay = document.createElement('div');
            this.overlay.className = 'sidebar-overlay';
            document.body.appendChild(this.overlay);
        }
    }

    setupEventListeners() {
        // Toggle button click
        if (this.toggleBtn) {
            this.toggleBtn.addEventListener('click', () => this.toggle());
        }

        // Mobile toggle button
        if (this.mobileToggleBtn) {
            this.mobileToggleBtn.addEventListener('click', () => this.toggleMobile());
        }

        // Overlay click (mobile)
        if (this.overlay) {
            this.overlay.addEventListener('click', () => this.closeMobile());
        }

        // Window resize
        window.addEventListener('resize', () => {
            this.handleResize();
            this.hideAllDropdowns(); // Hide dropdowns on resize to avoid positioning issues
        });

        // Escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                if (this.isMobileOpen()) {
                    this.closeMobile();
                }
                // Also close dropdowns on Escape
                this.hideAllDropdowns();
            }
        });
    }

    setupResponsive() {
        this.handleResize();
    }

    handleResize() {
        const wasMobile = this.isMobile;
        this.isMobile = window.innerWidth <= 1024;

        if (wasMobile !== this.isMobile) {
            if (this.isMobile) {
                // Switched to mobile
                this.body.classList.remove('sidebar-collapsed');
                this.closeMobile();
            } else {
                // Switched to desktop
                this.body.classList.remove('sidebar-mobile-open');
                this.updateSidebarState();
            }
        }

        this.updateMainContentMargin();
    }

    updateMainContentMargin() {
        const mainContent = document.querySelector('.main-content');
        if (!mainContent) return;

        if (this.isMobile) {
            mainContent.style.marginLeft = '0';
        } else {
            const sidebarWidth = this.isCollapsed ? '70px' : '280px';
            mainContent.style.marginLeft = sidebarWidth;
        }
    }

    toggle() {
        if (this.isMobile) {
            this.toggleMobile();
        } else {
            this.toggleCollapse();
        }
    }

    toggleCollapse() {
        this.isCollapsed = !this.isCollapsed;
        this.updateSidebarState();
        this.saveState();

        console.log(`📱 Sidebar ${this.isCollapsed ? 'collapsed' : 'expanded'}`);
    }

    toggleMobile() {
        if (this.isMobileOpen()) {
            this.closeMobile();
        } else {
            this.openMobile();
        }
    }

    openMobile() {
        this.body.classList.add('sidebar-mobile-open');
        console.log('📱 Mobile sidebar opened');
    }

    closeMobile() {
        this.body.classList.remove('sidebar-mobile-open');
        console.log('📱 Mobile sidebar closed');
    }

    isMobileOpen() {
        return this.body.classList.contains('sidebar-mobile-open');
    }

    updateSidebarState() {
        if (this.isMobile) {
            this.body.classList.remove('sidebar-collapsed');
        } else {
            this.body.classList.toggle('sidebar-collapsed', this.isCollapsed);
        }

        this.updateMainContentMargin();
        this.updateToggleButton();
    }

    updateToggleButton() {
        if (this.toggleBtn) {
            const icon = this.toggleBtn.querySelector('i');
            if (icon) {
                if (this.isMobile) {
                    icon.className = 'fas fa-bars';
                } else {
                    icon.className = this.isCollapsed ? 'fas fa-chevron-right' : 'fas fa-chevron-left';
                }
            }
        }
    }

    loadSavedState() {
        const saved = localStorage.getItem('sidebarCollapsed');
        if (saved !== null) {
            this.isCollapsed = saved === 'true';
        }
    }

    saveState() {
        localStorage.setItem('sidebarCollapsed', this.isCollapsed.toString());
    }

    setupTooltips() {
        const menuItems = document.querySelectorAll('.menu-item');
        menuItems.forEach(item => {
            const text = item.querySelector('.menu-text');
            if (text) {
                item.setAttribute('data-tooltip', text.textContent.trim());
            }
        });
    }

    setupSubmenuHandlers() {
        const submenuToggles = document.querySelectorAll('.menu-item[data-bs-toggle="collapse"]');

        submenuToggles.forEach(toggle => {
            toggle.addEventListener('click', (e) => {
                if (this.isCollapsed && !this.isMobile) {
                    e.preventDefault();
                    e.stopPropagation();
                    return false;
                }
            });
        });
    }

    setupActiveMenuDetection() {
        // Check if PHP has already set active menu correctly
        const existingActive = document.querySelector('.menu-item.active, .submenu-item.active');

        if (existingActive) {
            console.log('✅ Active menu already set by PHP:', existingActive.textContent.trim());
            // Just ensure submenu is open if needed
            this.ensureSubmenuOpen(existingActive);
        } else {
            // Fallback to JavaScript detection
            const currentPath = window.location.pathname;
            const currentPage = currentPath.split('/').pop().replace('.php', '') || 'dashboard';

            console.log('🔍 No active menu found, detecting for page:', currentPage);
            this.setActiveMenu(currentPage);
        }

        // Setup click handlers for menu items
        this.setupMenuClickHandlers();
    }

    ensureSubmenuOpen(activeItem) {
        if (!activeItem) return;

        // If it's a submenu item, ensure its parent submenu is open
        if (activeItem.classList.contains('submenu-item')) {
            const parentSubmenu = activeItem.closest('.submenu');
            if (parentSubmenu) {
                // Find parent toggle by submenu ID
                const parentToggle = document.querySelector(`[data-bs-target="#${parentSubmenu.id}"]`);

                if (parentToggle && parentSubmenu) {
                    parentSubmenu.classList.add('show');
                    parentToggle.classList.add('active');
                    parentToggle.setAttribute('aria-expanded', 'true');
                    console.log('📂 Opened parent submenu for active item:', parentSubmenu.id);
                }
            }
        }

        // If it's a main menu item that should have submenu open, check for active submenu items
        if (activeItem.classList.contains('menu-item') && activeItem.hasAttribute('data-bs-target')) {
            const targetSubmenu = document.querySelector(activeItem.getAttribute('data-bs-target'));
            if (targetSubmenu) {
                const hasActiveSubmenuItem = targetSubmenu.querySelector('.submenu-item.active');
                if (hasActiveSubmenuItem) {
                    targetSubmenu.classList.add('show');
                    activeItem.setAttribute('aria-expanded', 'true');
                    console.log('📂 Opened submenu with active item:', targetSubmenu.id);
                }
            }
        }
    }

    setActiveMenu(currentPage) {
        // Don't override if already set correctly
        const existingActive = document.querySelector('.menu-item.active, .submenu-item.active');
        if (existingActive) {
            const href = existingActive.getAttribute('href');
            if (href) {
                const menuPage = href.replace('.php', '').split('/').pop();
                if (menuPage === currentPage ||
                    (currentPage === 'dashboard' && (menuPage === 'index' || menuPage === '' || menuPage === 'dashboard'))) {
                    console.log('✅ Correct active menu already set, keeping it');
                    this.ensureSubmenuOpen(existingActive);
                    return;
                }
            }
        }

        // Remove all active classes first
        document.querySelectorAll('.menu-item, .submenu-item').forEach(item => {
            item.classList.remove('active');
        });

        // Close all submenus first
        document.querySelectorAll('.submenu').forEach(submenu => {
            submenu.classList.remove('show');
        });

        // Comprehensive page mapping for all menus and submenus
        const pageMapping = {
            // Main menus
            'dashboard': ['dashboard', 'index', '', 'admin-dashboard'],
            'admin_dashboard': ['admin-dashboard', 'admin_dashboard'],

            // Keuangan submenu
            'transaksi': ['transaksi', 'transactions'],
            'kategori': ['kategori', 'categories'],
            'target': ['target', 'targets'],
            'anggaran': ['anggaran', 'budget'],
            'investasi': ['investasi', 'investment'],
            'hutang': ['hutang', 'debt', 'piutang'],

            // Laporan submenu
            'laporan': ['laporan', 'reports', 'laporan_keuangan'],
            'laporan_keuangan': ['laporan_keuangan', 'laporan', 'financial_reports'],
            'analisis': ['analisis', 'analysis'],
            'grafik': ['grafik', 'charts'],
            'export': ['export', 'export_data'],

            // Bisnis submenu
            'produk': ['produk', 'products'],
            'penjualan': ['penjualan', 'sales'],
            'pembelian': ['pembelian', 'purchases'],
            'supplier': ['supplier', 'suppliers'],
            'inventory': ['inventory', 'stock'],
            'retur': ['retur', 'returns'],

            // Admin Panel submenu
            'users': ['users', 'user', 'kelola_user'],
            'permissions': ['permissions', 'menu_permissions_advanced', 'simple_permissions'],
            'notifications': ['notifications', 'notifikasi'],
            'system_customization': ['system_customization', 'customization'],
            'database_management': ['database_management', 'database'],
            'fix_errors': ['fix_errors', 'errors'],
            'update_database': ['update_database', 'update'],
            'system_check': ['system_check', 'health'],
            'clear_cache': ['clear_cache', 'cache'],
            'backup': ['backup', 'backup_data'],
            'logs': ['logs', 'log_aktivitas', 'activities'],
            'settings': ['settings', 'pengaturan'],

            // Customization submenu
            'customize': ['customize', 'customize-layout'],
            'themes': ['themes', 'theme'],
            'layout': ['layout', 'layout_manager'],

            // Bantuan submenu
            'panduan': ['panduan', 'guide'],
            'faq': ['faq', 'frequently_asked'],
            'tutorial': ['tutorial', 'tutorials'],
            'support': ['support', 'help'],

            // Profile and others
            'profile': ['profile', 'profil'],
            'logout': ['logout', 'keluar']
        };

        // Find and activate the correct menu item
        const menuItems = document.querySelectorAll('.menu-item[href], .submenu-item[href]');
        let foundActive = false;

        menuItems.forEach(item => {
            const href = item.getAttribute('href');
            if (href) {
                const menuPage = href.replace('.php', '').split('/').pop();

                // Check direct match
                if (menuPage === currentPage) {
                    this.activateMenuItem(item);
                    foundActive = true;
                    return;
                }

                // Check mapped pages
                for (const [key, aliases] of Object.entries(pageMapping)) {
                    if (aliases.includes(currentPage) && aliases.includes(menuPage)) {
                        this.activateMenuItem(item);
                        foundActive = true;
                        return;
                    }
                }
            }
        });

        // If no specific menu found, activate dashboard as default
        if (!foundActive && !['login', 'register', 'logout'].includes(currentPage)) {
            const dashboardItem = document.querySelector('.menu-item[href*="dashboard"], .menu-item[href*="index"]');
            if (dashboardItem) {
                this.activateMenuItem(dashboardItem);
                console.log('✅ Default dashboard menu activated');
            }
        }
    }

    activateMenuItem(item) {
        item.classList.add('active');

        // If it's a submenu item, also activate and open its parent submenu
        if (item.classList.contains('submenu-item')) {
            const parentSubmenu = item.closest('.submenu');
            if (parentSubmenu) {
                const parentToggle = document.querySelector(`[data-bs-target="#${parentSubmenu.id}"]`);

                if (parentToggle) {
                    parentSubmenu.classList.add('show');
                    parentToggle.classList.add('active');
                    parentToggle.setAttribute('aria-expanded', 'true');
                    console.log('📂 Activated parent menu for submenu item');
                }
            }
        }

        console.log('✅ Active menu set:', item.textContent.trim());
    }

    setupMenuClickHandlers() {
        const menuItems = document.querySelectorAll('.menu-item[href], .submenu-item[href]');

        menuItems.forEach(item => {
            item.addEventListener('click', (e) => {
                // Don't prevent default navigation
                // Just handle mobile sidebar closing
                if (this.isMobile && !item.hasAttribute('data-bs-toggle')) {
                    setTimeout(() => this.closeMobile(), 300);
                }

                // Store the clicked menu for persistence
                const href = item.getAttribute('href');
                if (href) {
                    const menuPage = href.replace('.php', '').split('/').pop();
                    localStorage.setItem('lastActivePage', menuPage);
                    console.log('💾 Stored active page:', menuPage);
                }
            });
        });

        // Handle submenu toggles
        const submenuToggles = document.querySelectorAll('.menu-item[data-bs-toggle="collapse"], .menu-toggle[data-bs-toggle="collapse"]');
        submenuToggles.forEach(toggle => {
            toggle.addEventListener('click', (e) => {
                if (this.isCollapsed && !this.isMobile) {
                    e.preventDefault();
                    e.stopPropagation();
                    return false;
                }

                // Handle submenu toggle state
                const targetId = toggle.getAttribute('data-bs-target');
                const targetSubmenu = document.querySelector(targetId);

                if (targetSubmenu) {
                    const isExpanded = toggle.getAttribute('aria-expanded') === 'true';

                    // Close other submenus if needed (accordion behavior)
                    document.querySelectorAll('.submenu.show').forEach(openSubmenu => {
                        if (openSubmenu !== targetSubmenu) {
                            openSubmenu.classList.remove('show');
                            const otherToggle = document.querySelector(`[data-bs-target="#${openSubmenu.id}"]`);
                            if (otherToggle) {
                                otherToggle.setAttribute('aria-expanded', 'false');
                                otherToggle.classList.remove('active');
                            }
                        }
                    });

                    // Toggle current submenu
                    if (!isExpanded) {
                        targetSubmenu.classList.add('show');
                        toggle.setAttribute('aria-expanded', 'true');
                        toggle.classList.add('active');
                    } else {
                        targetSubmenu.classList.remove('show');
                        toggle.setAttribute('aria-expanded', 'false');
                        toggle.classList.remove('active');
                    }
                }
            });
        });
    }

    // Public methods
    open() {
        if (this.isMobile) {
            this.openMobile();
        }
        // Desktop sidebar is always open
    }

    close() {
        if (this.isMobile) {
            this.closeMobile();
        }
        // Desktop sidebar is always open
    }

    // Setup dropdown for collapsed state
    setupSubmenuOverlays() {
        const menuItems = document.querySelectorAll('.menu-item.has-submenu');
        console.log('🔧 Setting up submenu overlays for', menuItems.length, 'menu items');

        menuItems.forEach(menuItem => {
            const targetId = menuItem.getAttribute('data-bs-target');
            const submenu = document.querySelector(targetId);

            if (submenu) {
                // Create dropdown element
                const dropdown = this.createCollapsedDropdown(submenu, menuItem);
                this.submenuOverlays.set(menuItem, dropdown);
                console.log('✅ Created dropdown for:', menuItem.querySelector('.menu-text')?.textContent);

                // Add click event for collapsed state - use capture to override Bootstrap
                menuItem.addEventListener('click', (e) => {
                    console.log('🖱️ Menu item clicked, collapsed:', this.isCollapsed, 'mobile:', this.isMobile);
                    if (this.isCollapsed && !this.isMobile) {
                        e.preventDefault();
                        e.stopPropagation();
                        e.stopImmediatePropagation();
                        console.log('🎯 Showing dropdown for collapsed menu');
                        this.toggleCollapsedDropdown(menuItem, dropdown);
                        return false;
                    }
                }, true); // Use capture phase to override Bootstrap

                // Close dropdown when clicking outside
                document.addEventListener('click', (e) => {
                    if (this.isCollapsed && !this.isMobile) {
                        if (!menuItem.contains(e.target) && !dropdown.contains(e.target)) {
                            this.hideCollapsedDropdown(dropdown);
                        }
                    }
                });
            } else {
                console.log('❌ No submenu found for:', targetId);
            }
        });

        // Create backdrop for closing dropdowns
        this.createDropdownBackdrop();
        console.log('🎭 Dropdown backdrop created');
    }

    createCollapsedDropdown(submenu, menuItem) {
        const dropdown = document.createElement('div');
        dropdown.className = 'collapsed-dropdown';

        // Create dropdown header
        const header = document.createElement('div');
        header.className = 'collapsed-dropdown-header';

        const title = document.createElement('h6');
        title.className = 'collapsed-dropdown-title';

        const menuIcon = menuItem.querySelector('.menu-icon i');
        const menuText = menuItem.querySelector('.menu-text');

        if (menuIcon && menuText) {
            title.innerHTML = `<i class="${menuIcon.className}"></i>${menuText.textContent}`;
        }

        header.appendChild(title);
        dropdown.appendChild(header);

        // Clone submenu items
        const submenuItems = submenu.querySelectorAll('.submenu-item');
        submenuItems.forEach(item => {
            const clonedItem = item.cloneNode(true);
            dropdown.appendChild(clonedItem);
        });

        // Append to body for fixed positioning outside sidebar
        document.body.appendChild(dropdown);
        return dropdown;
    }

    createDropdownBackdrop() {
        if (!this.dropdownBackdrop) {
            this.dropdownBackdrop = document.createElement('div');
            this.dropdownBackdrop.className = 'dropdown-backdrop';
            document.body.appendChild(this.dropdownBackdrop);

            this.dropdownBackdrop.addEventListener('click', () => {
                this.hideAllDropdowns();
            });
        }
    }

    toggleCollapsedDropdown(menuItem, dropdown) {
        const isVisible = dropdown.classList.contains('show');

        // Hide all other dropdowns first
        this.hideAllDropdowns();

        if (!isVisible) {
            this.showCollapsedDropdown(menuItem, dropdown);
            this.dropdownBackdrop.classList.add('show');
        }
    }

    showCollapsedDropdown(menuItem, dropdown) {
        console.log('🎯 showCollapsedDropdown called');

        // Calculate position relative to menu item
        const rect = menuItem.getBoundingClientRect();
        const sidebar = document.querySelector('.modern-sidebar');
        const sidebarRect = sidebar.getBoundingClientRect();

        console.log('📐 Menu item rect:', rect);
        console.log('📐 Sidebar rect:', sidebarRect);

        // Position dropdown to the right of the sidebar (outside sidebar)
        const leftPosition = sidebarRect.right + 5; // Closer to sidebar
        const topPosition = rect.top - 5; // Slightly higher to align better

        console.log('📍 Initial position:', leftPosition, topPosition);

        // Set initial position
        dropdown.style.position = 'fixed';
        dropdown.style.left = leftPosition + 'px';
        dropdown.style.top = topPosition + 'px';
        dropdown.style.zIndex = '9999';

        // Force show the dropdown
        dropdown.style.display = 'block';
        dropdown.style.visibility = 'visible';
        dropdown.style.opacity = '1';
        dropdown.classList.add('show');

        console.log('✅ Dropdown should now be visible');
        console.log('📊 Dropdown element:', dropdown);
        console.log('📊 Dropdown classes:', dropdown.className);
        console.log('📊 Dropdown style:', dropdown.style.cssText);

        // Get dropdown dimensions after showing
        const dropdownRect = dropdown.getBoundingClientRect();
        const viewportHeight = window.innerHeight;
        const viewportWidth = window.innerWidth;

        console.log('📐 Dropdown rect:', dropdownRect);

        // Adjust vertical position if dropdown goes below viewport
        let finalTop = topPosition;
        if (topPosition + dropdownRect.height > viewportHeight - 20) {
            finalTop = Math.max(20, viewportHeight - dropdownRect.height - 20);
            console.log('📍 Adjusted top position:', finalTop);
        }

        // Adjust horizontal position if dropdown goes off right edge
        let finalLeft = leftPosition;
        if (leftPosition + dropdownRect.width > viewportWidth - 20) {
            finalLeft = Math.max(20, viewportWidth - dropdownRect.width - 20);
            console.log('📍 Adjusted left position:', finalLeft);
        }

        // Apply final position
        dropdown.style.left = finalLeft + 'px';
        dropdown.style.top = finalTop + 'px';

        console.log('🎯 Final dropdown position:', finalLeft, finalTop);
    }

    hideCollapsedDropdown(dropdown) {
        dropdown.classList.remove('show');
        this.dropdownBackdrop.classList.remove('show');
    }

    hideAllDropdowns() {
        document.querySelectorAll('.collapsed-dropdown.show').forEach(dropdown => {
            dropdown.classList.remove('show');
        });
        this.dropdownBackdrop.classList.remove('show');
    }

    // Public methods for collapse/expand
    collapse() {
        if (!this.isMobile) {
            this.isCollapsed = true;
            this.updateSidebarState();
            this.saveState();
            this.hideAllDropdowns();
        }
    }

    expand() {
        if (!this.isMobile) {
            this.isCollapsed = false;
            this.updateSidebarState();
            this.saveState();
            this.hideAllDropdowns();
        }
    }

    // Public method to refresh active menu (useful for SPA navigation)
    refreshActiveMenu() {
        // Check if PHP has already set the correct active menu
        const existingActive = document.querySelector('.menu-item.active, .submenu-item.active');

        if (existingActive) {
            console.log('🔄 Active menu already set correctly, ensuring submenu is open');
            this.ensureSubmenuOpen(existingActive);
        } else {
            const currentPath = window.location.pathname;
            const currentPage = currentPath.split('/').pop().replace('.php', '') || 'dashboard';
            console.log('🔄 Refreshing active menu for page:', currentPage);
            this.setActiveMenu(currentPage);
        }
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    window.modernSidebar = new ModernSidebar();

    // Global functions for backward compatibility
    window.toggleSidebar = () => window.modernSidebar.toggle();
    window.openSidebar = () => window.modernSidebar.open();
    window.closeSidebar = () => window.modernSidebar.close();
    window.refreshActiveMenu = () => window.modernSidebar.refreshActiveMenu();

    // Collapse/Expand functions
    window.collapseSidebar = () => window.modernSidebar.collapse();
    window.expandSidebar = () => window.modernSidebar.expand();

    console.log('✅ Modern Sidebar ready');
});

// Handle theme changes
document.addEventListener('themeChanged', function(e) {
    console.log(`🎨 Theme changed to: ${e.detail.theme}`);
});

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ModernSidebar;
}
