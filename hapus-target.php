<?php
require_once 'includes/config/database.php';
require_once 'includes/helpers/functions.php';

// Start session with secure settings
if (session_status() === PHP_SESSION_NONE) {
    session_set_cookie_params([
        'lifetime' => 86400,
        'path' => '/',
        'secure' => true,
        'httponly' => true,
        'samesite' => 'Strict'
    ]);
    session_start();
}

// Check if user is logged in
if (!isLoggedIn()) {
    setFlashMessage('danger', 'Silakan login terlebih dahulu');
    redirect('login.php');
}

$currentUser = getCurrentUser();

// Check if ID is provided
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    setFlashMessage('danger', 'ID target tidak valid');
    redirect('target.php');
}

$targetId = (int)$_GET['id'];

try {
    // Get target info for logging
    $stmt = executeQuery("
        SELECT nama_target, target_jumlah 
        FROM target 
        WHERE id = ? AND user_id = ?
    ", [$targetId, $currentUser['id']]);
    
    $target = $stmt->fetch();
    
    if (!$target) {
        throw new Exception('Target tidak ditemukan');
    }
    
    // Delete target
    $stmt = executeQuery("
        DELETE FROM target 
        WHERE id = ? AND user_id = ?
    ", [$targetId, $currentUser['id']]);
    
    if ($stmt->rowCount() > 0) {
        // Log aktivitas
        executeQuery("
            INSERT INTO aktivitas (user_id, aktivitas) 
            VALUES (?, ?)
        ", [
            $currentUser['id'],
            sprintf('Menghapus target %s sebesar %s', 
                $target['nama_target'],
                formatRupiah($target['target_jumlah'])
            )
        ]);
        
        // Create notification
        executeQuery("
            INSERT INTO notifikasi (user_id, judul, pesan, tipe) 
            VALUES (?, ?, ?, 'success')
        ", [
            $currentUser['id'],
            'Target Berhasil Dihapus',
            sprintf('Target %s sebesar %s berhasil dihapus',
                $target['nama_target'],
                formatRupiah($target['target_jumlah'])
            )
        ]);
        
        setFlashMessage('success', 'Target berhasil dihapus');
    } else {
        throw new Exception('Gagal menghapus target');
    }
} catch (Exception $e) {
    error_log("Error deleting target: " . $e->getMessage());
    setFlashMessage('danger', $e->getMessage());
}

redirect('target.php'); 