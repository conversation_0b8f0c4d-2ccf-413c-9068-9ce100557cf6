/**
 * Layout Manager JavaScript - Consolidated and Optimized
 * Handles Layout Managers, Sidebar, Theme, and Control Sidebar
 */

// Global variables
let isInitialized = false;
let colorSchemes = {};
let radiusValues = {};

// Apply theme immediately before DOM is ready
(function() {
    const savedTheme = localStorage.getItem('theme') || 'light';
    document.documentElement.setAttribute('data-bs-theme', savedTheme);
    if (document.body) {
        document.body.setAttribute('data-bs-theme', savedTheme);
    }
})();

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    if (isInitialized) return; // Prevent double initialization

    // Remove preload class to enable transitions
    setTimeout(() => {
        document.body.classList.remove('preload');
    }, 100);

    // Force apply theme immediately
    const currentTheme = localStorage.getItem('theme') || 'light';
    if (currentTheme === 'dark') {
        setTimeout(() => {
            forceDarkBackgrounds();
        }, 50);
    }

    initializeLayoutManager();
    initializeSidebar();
    initializeThemeSystem();
    initializeControlSidebar();
    isInitialized = true;
});

/**
 * Initialize Layout Manager
 */
function initializeLayoutManager() {
    // Define color schemes
    colorSchemes = {
        'default': { sidebar: '#343a40', navbar: '#007bff', text: '#ffffff' },
        'vibrant': {
            sidebar: 'linear-gradient(135deg, #ff0080 0%, #00ff80 50%, #8000ff 100%)',
            navbar: 'linear-gradient(90deg, #ff0080 0%, #00ff80 50%, #8000ff 100%)',
            text: '#ffffff'
        },
        'pastel': {
            sidebar: 'linear-gradient(135deg, #ffb3ba 0%, #bae1ff 50%, #baffc9 100%)',
            navbar: 'linear-gradient(90deg, #ffb3ba 0%, #bae1ff 50%, #baffc9 100%)',
            text: '#2c3e50'
        },
        'neon': {
            sidebar: 'linear-gradient(135deg, #39ff14 0%, #ff073a 50%, #00ffff 100%)',
            navbar: 'linear-gradient(90deg, #39ff14 0%, #ff073a 50%, #00ffff 100%)',
            text: '#000000'
        },
        'ocean': {
            sidebar: 'linear-gradient(135deg, #006994 0%, #0099cc 50%, #66ccff 100%)',
            navbar: 'linear-gradient(90deg, #006994 0%, #0099cc 50%, #66ccff 100%)',
            text: '#ffffff'
        },
        'sunset': {
            sidebar: 'linear-gradient(135deg, #ff4500 0%, #ff6347 50%, #ffd700 100%)',
            navbar: 'linear-gradient(90deg, #ff4500 0%, #ff6347 50%, #ffd700 100%)',
            text: '#ffffff'
        },
        'midnight': {
            sidebar: 'linear-gradient(135deg, #2c3e50 0%, #34495e 50%, #1a252f 100%)',
            navbar: 'linear-gradient(90deg, #2c3e50 0%, #34495e 50%, #1a252f 100%)',
            text: '#ecf0f1'
        },
        'royal': {
            sidebar: 'linear-gradient(135deg, #663399 0%, #9966cc 50%, #cc99ff 100%)',
            navbar: 'linear-gradient(90deg, #663399 0%, #9966cc 50%, #cc99ff 100%)',
            text: '#ffffff'
        },
        'earth': {
            sidebar: 'linear-gradient(135deg, #8b4513 0%, #228b22 50%, #daa520 100%)',
            navbar: 'linear-gradient(90deg, #8b4513 0%, #228b22 50%, #daa520 100%)',
            text: '#ffffff'
        },
        'forest': {
            sidebar: 'linear-gradient(135deg, #228b22 0%, #32cd32 50%, #90ee90 100%)',
            navbar: 'linear-gradient(90deg, #228b22 0%, #32cd32 50%, #90ee90 100%)',
            text: '#ffffff'
        },
        'cyberpunk': {
            sidebar: 'linear-gradient(135deg, #ff00ff 0%, #00ffff 50%, #ffff00 100%)',
            navbar: 'linear-gradient(90deg, #ff00ff 0%, #00ffff 50%, #ffff00 100%)',
            text: '#000000'
        },
        'autumn': {
            sidebar: 'linear-gradient(135deg, #d2691e 0%, #cd853f 50%, #daa520 100%)',
            navbar: 'linear-gradient(90deg, #d2691e 0%, #cd853f 50%, #daa520 100%)',
            text: '#ffffff'
        }
    };

    // Define radius values
    radiusValues = {
        'none': '0px',
        'small': '4px',
        'medium': '8px',
        'large': '15px',
        'xl': '25px'
    };

    // Add event listeners
    addEventListeners();

    // Initialize preview
    updateLivePreview();
}

/**
 * Add Event Listeners
 */
function addEventListeners() {
    // Radio buttons
    const radioButtons = document.querySelectorAll('input[type="radio"]');
    radioButtons.forEach(radio => {
        radio.addEventListener('change', debounce(updateLivePreview, 100));
    });

    // Select boxes
    const selectBoxes = document.querySelectorAll('select');
    selectBoxes.forEach(select => {
        select.addEventListener('change', debounce(updateLivePreview, 100));
    });

    // Form submission
    const layoutForm = document.getElementById('layoutForm');
    if (layoutForm) {
        layoutForm.addEventListener('submit', handleFormSubmission);
    }
}

/**
 * Update Live Preview
 */
function updateLivePreview() {
    // Get form values
    const layoutType = getSelectedValue('layout_type', 'classic');
    const colorScheme = getSelectedValue('color_scheme', 'default');
    const borderRadius = getSelectedValue('border_radius', 'medium');
    const shadowStyle = getSelectedValue('shadow_style', 'soft');

    // Update simple preview (if exists)
    updateSimplePreview(layoutType, colorScheme, borderRadius, shadowStyle);

    // Update advanced preview (if exists)
    updateAdvancedPreview(layoutType, colorScheme, borderRadius);
}

/**
 * Update Simple Preview
 */
function updateSimplePreview(layoutType, colorScheme, borderRadius, shadowStyle) {
    const sidebar = document.getElementById('previewSidebar');
    const navbar = document.getElementById('previewNavbar');
    const cards = document.querySelectorAll('.preview-card-large');

    if (!sidebar && !navbar) return; // Simple preview not present

    const colors = colorSchemes[colorScheme] || colorSchemes['default'];
    const radius = radiusValues[borderRadius] || '8px';

    // Apply colors
    if (sidebar) {
        sidebar.style.background = colors.sidebar;
        sidebar.style.color = colors.text;
    }
    if (navbar) {
        navbar.style.background = colors.navbar;
        navbar.style.color = colors.text;
    }

    // Apply border radius
    if (layoutType !== 'minimal') {
        if (sidebar) sidebar.style.borderRadius = `0 ${radius} ${radius} 0`;
        if (navbar) navbar.style.borderRadius = `0 0 ${radius} ${radius}`;
    }

    // Apply shadows to cards
    cards.forEach(card => {
        card.style.borderRadius = radius;

        const shadowValues = {
            'none': 'none',
            'soft': '0 2px 10px rgba(0,0,0,0.08)',
            'medium': '0 4px 15px rgba(0,0,0,0.12)',
            'strong': '0 8px 25px rgba(0,0,0,0.18)',
            'colored': '0 8px 25px rgba(0, 123, 255, 0.3)'
        };

        card.style.boxShadow = shadowValues[shadowStyle] || shadowValues['soft'];
    });

    // Apply special effects
    applySpecialEffects(layoutType, colorScheme, sidebar, navbar);
}

/**
 * Update Advanced Preview
 */
function updateAdvancedPreview(layoutType, colorScheme, borderRadius) {
    const preview = document.getElementById('livePreview');
    if (!preview) return; // Advanced preview not present

    // Remove existing classes
    preview.className = 'layout-preview-mini';

    // Add new classes
    preview.classList.add(`layout-${layoutType}`);
    preview.classList.add(`preview-${colorScheme}`);
    preview.classList.add(`radius-${borderRadius}`);

    const sidebar = preview.querySelector('.preview-sidebar-mini');
    const navbar = preview.querySelector('.preview-navbar-mini');
    const footer = preview.querySelector('.preview-footer-mini');
    const cards = preview.querySelectorAll('.preview-card-mini');

    const colors = colorSchemes[colorScheme] || colorSchemes['default'];
    const radius = radiusValues[borderRadius] || '8px';

    // Apply colors
    if (sidebar) sidebar.style.background = colors.sidebar;
    if (navbar) navbar.style.background = colors.navbar;

    // Apply border radius
    if (layoutType !== 'minimal') {
        if (sidebar) sidebar.style.borderRadius = `0 ${radius} ${radius} 0`;
        if (navbar) navbar.style.borderRadius = `0 0 ${radius} ${radius}`;
        if (footer) footer.style.borderRadius = `${radius} ${radius} 0 0`;
    }

    cards.forEach(card => {
        card.style.borderRadius = radius;
    });

    // Apply special effects
    applySpecialEffects(layoutType, colorScheme, sidebar, navbar);
}

/**
 * Apply Special Effects
 */
function applySpecialEffects(layoutType, colorScheme, sidebar, navbar) {
    if (layoutType === 'glassmorphism') {
        if (sidebar) {
            sidebar.style.background = 'rgba(255, 255, 255, 0.25)';
            sidebar.style.backdropFilter = 'blur(10px)';
            sidebar.style.border = '1px solid rgba(255, 255, 255, 0.18)';
        }
        if (navbar) {
            navbar.style.background = 'rgba(255, 255, 255, 0.25)';
            navbar.style.backdropFilter = 'blur(10px)';
            navbar.style.border = '1px solid rgba(255, 255, 255, 0.18)';
        }
    } else if (layoutType === 'neon' || colorScheme === 'neon') {
        if (sidebar) sidebar.style.boxShadow = '0 0 20px rgba(57, 255, 20, 0.4)';
        if (navbar) navbar.style.boxShadow = '0 0 15px rgba(57, 255, 20, 0.4)';
    } else if (layoutType === 'minimal') {
        if (sidebar) {
            sidebar.style.background = '#f8f9fa';
            sidebar.style.color = '#2c3e50';
            sidebar.style.borderRight = '1px solid #dee2e6';
            sidebar.style.borderRadius = '0';
        }
        if (navbar) {
            navbar.style.background = '#fff';
            sidebar.style.color = '#2c3e50';
            navbar.style.borderBottom = '1px solid #dee2e6';
            navbar.style.borderRadius = '0';
        }
    }
}

/**
 * Apply Preview to Actual Layout
 */
function applyPreview() {
    const formData = new FormData(document.getElementById('layoutForm'));

    // Show loading
    const btn = document.querySelector('button[onclick="applyPreview()"]');
    if (!btn) return;

    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Applying...';
    btn.disabled = true;

    // Generate and apply temporary CSS
    const tempCSS = generateTempCSS(formData);

    // Remove existing temp CSS
    const existingTemp = document.getElementById('temp-layout-css');
    if (existingTemp) existingTemp.remove();

    // Add new temp CSS
    const style = document.createElement('style');
    style.id = 'temp-layout-css';
    style.innerHTML = tempCSS;
    document.head.appendChild(style);

    // Restore button
    setTimeout(() => {
        btn.innerHTML = originalText;
        btn.disabled = false;
        showNotification('Preview applied! Save to make permanent.', 'info');
    }, 1000);
}

/**
 * Generate Temporary CSS
 */
function generateTempCSS(formData) {
    const layoutType = formData.get('layout_type') || 'classic';
    const colorScheme = formData.get('color_scheme') || 'default';
    const borderRadius = formData.get('border_radius') || 'medium';

    const colors = colorSchemes[colorScheme] || colorSchemes['default'];
    const radius = radiusValues[borderRadius] || '8px';

    return `
        /* Temporary Layout Preview */
        body .sidebar, body .modern-sidebar, body #sidebar {
            background: ${colors.sidebar} !important;
            ${layoutType !== 'minimal' ? `border-radius: 0 ${radius} ${radius} 0 !important;` : ''}
        }
        body .navbar, body .modern-navbar, body #mainNavbar {
            background: ${colors.navbar} !important;
            ${layoutType !== 'minimal' ? `border-radius: 0 0 ${radius} ${radius} !important;` : ''}
        }
        body .sidebar a, body .sidebar .nav-link, body .sidebar .brand-title {
            color: ${colors.text} !important;
        }
        body .card {
            border-radius: ${radius} !important;
        }
    `;
}

/**
 * Reset Layout to Default
 */
function resetLayout() {
    if (!confirm('Reset to default layout?')) return;

    // Reset form values
    setSelectedValue('layout_type', 'classic');
    setSelectedValue('color_scheme', 'default');
    setSelectedValue('border_radius', 'medium');
    setSelectedValue('shadow_style', 'soft');
    setSelectedValue('animation_style', 'subtle');

    // Update preview
    updateLivePreview();
    showNotification('Layout reset to default.', 'success');
}

/**
 * Handle Form Submission
 */
function handleFormSubmission(e) {
    const submitBtn = document.querySelector('button[name="save_layout"]');
    if (!submitBtn) return;

    const originalText = submitBtn.innerHTML;

    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Saving...';
    submitBtn.disabled = true;

    // Re-enable button after timeout (in case of errors)
    setTimeout(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }, 3000);
}

/**
 * Show Notification
 */
function showNotification(message, type = 'info') {
    // Remove existing notifications
    const existingNotifications = document.querySelectorAll('.layout-notification');
    existingNotifications.forEach(notification => notification.remove());

    // Create new notification
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show position-fixed layout-notification`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(notification);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

/**
 * Utility Functions
 */

// Get selected value from form
function getSelectedValue(name, defaultValue = '') {
    const element = document.querySelector(`input[name="${name}"]:checked, select[name="${name}"]`);
    return element ? element.value : defaultValue;
}

// Set selected value in form
function setSelectedValue(name, value) {
    const radioElement = document.getElementById(`${name.split('_')[0]}_${value}`);
    const selectElement = document.querySelector(`select[name="${name}"]`);

    if (radioElement) {
        radioElement.checked = true;
    } else if (selectElement) {
        selectElement.value = value;
    }
}

// Debounce function to prevent excessive calls
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

/**
 * Initialize Sidebar Functionality
 */
function initializeSidebar() {
    const sidebarToggleBtn = document.getElementById('sidebarToggleBtn');
    const sidebar = document.getElementById('sidebar');
    const mainContent = document.querySelector('.main-content');
    const navbar = document.querySelector('.modern-navbar');

    // Load saved sidebar state
    const savedState = localStorage.getItem('sidebarCollapsed') === 'true';
    if (savedState && window.innerWidth >= 992) {
        document.body.classList.add('sidebar-collapsed');
        if (sidebarToggleBtn) {
            sidebarToggleBtn.classList.add('active');
        }
    }

    if (sidebarToggleBtn) {
        sidebarToggleBtn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            const isCollapsed = document.body.classList.contains('sidebar-collapsed');

            // Toggle collapsed state
            document.body.classList.toggle('sidebar-collapsed');
            this.classList.toggle('active');

            // Update layout
            updateSidebarLayout(!isCollapsed);

            // Save state
            localStorage.setItem('sidebarCollapsed', !isCollapsed);

            // Dispatch custom event
            document.dispatchEvent(new CustomEvent('sidebarToggled', {
                detail: { collapsed: !isCollapsed }
            }));
        });
    }

    function updateSidebarLayout(collapsed) {
        const sidebarWidth = collapsed ? '70px' : '280px';
        const contentMargin = collapsed ? '70px' : '280px';

        if (sidebar) {
            sidebar.style.width = sidebarWidth;
        }

        if (mainContent) {
            mainContent.style.marginLeft = contentMargin;
            mainContent.style.width = `calc(100% - ${contentMargin})`;
        }

        if (navbar) {
            navbar.style.left = contentMargin;
            navbar.style.width = `calc(100% - ${contentMargin})`;
        }
    }

    // Handle window resize
    window.addEventListener('resize', function() {
        if (window.innerWidth < 992) {
            document.body.classList.remove('sidebar-collapsed');
            if (sidebarToggleBtn) {
                sidebarToggleBtn.classList.remove('active');
            }
            updateSidebarLayout(false);
        } else {
            const savedState = localStorage.getItem('sidebarCollapsed') === 'true';
            if (savedState) {
                document.body.classList.add('sidebar-collapsed');
                if (sidebarToggleBtn) {
                    sidebarToggleBtn.classList.add('active');
                }
                updateSidebarLayout(true);
            }
        }
    });

    // Initialize mobile menu
    initializeMobileMenu();
}

/**
 * Initialize Mobile Menu
 */
function initializeMobileMenu() {
    const mobileSidebarToggle = document.getElementById('mobileSidebarToggle');
    const sidebar = document.getElementById('sidebar');

    if (mobileSidebarToggle && sidebar) {
        mobileSidebarToggle.addEventListener('click', function() {
            sidebar.classList.toggle('mobile-active');

            // Add overlay for mobile
            if (sidebar.classList.contains('mobile-active')) {
                const overlay = document.createElement('div');
                overlay.className = 'mobile-sidebar-overlay';
                overlay.addEventListener('click', function() {
                    sidebar.classList.remove('mobile-active');
                    this.remove();
                });
                document.body.appendChild(overlay);
            }
        });
    }
}

/**
 * Initialize Theme System
 */
function initializeThemeSystem() {
    const html = document.documentElement;
    const savedTheme = localStorage.getItem('theme') || 'light';

    // Apply saved theme immediately
    html.setAttribute('data-bs-theme', savedTheme);

    // Apply theme to body as well for better compatibility
    document.body.setAttribute('data-bs-theme', savedTheme);

    // Force apply theme to all major containers
    applyThemeToContainers(savedTheme);

    // Update theme toggle button if exists
    setTimeout(() => {
        updateThemeToggleButton(savedTheme);
        // Force update all theme-dependent elements
        forceThemeUpdate(savedTheme);
    }, 100);

    // Listen for theme toggle events
    document.addEventListener('themeToggle', function(e) {
        const newTheme = e.detail.theme;
        html.setAttribute('data-bs-theme', newTheme);
        document.body.setAttribute('data-bs-theme', newTheme);
        localStorage.setItem('theme', newTheme);
        updateThemeToggleButton(newTheme);

        // Dispatch theme change event for other components
        document.dispatchEvent(new CustomEvent('themeChanged', {
            detail: { theme: newTheme }
        }));
    });

    // Also listen for storage changes (when theme is changed in another tab)
    window.addEventListener('storage', function(e) {
        if (e.key === 'theme') {
            const newTheme = e.newValue || 'light';
            html.setAttribute('data-bs-theme', newTheme);
            document.body.setAttribute('data-bs-theme', newTheme);
            updateThemeToggleButton(newTheme);
            applyThemeToContainers(newTheme);
        }
    });

    // Set up mutation observer to ensure dark mode persists
    setupThemeObserver();
}

/**
 * Setup Theme Observer to Maintain Dark Mode
 */
function setupThemeObserver() {
    const observer = new MutationObserver(function(mutations) {
        const currentTheme = document.documentElement.getAttribute('data-bs-theme');
        if (currentTheme === 'dark') {
            // Re-apply dark backgrounds if needed
            setTimeout(() => {
                forceDarkBackgrounds();
            }, 50);
        }
    });

    // Observe changes to the sidebar and navbar
    const elementsToObserve = document.querySelectorAll('.modern-sidebar, .modern-navbar, .sidebar, .navbar');
    elementsToObserve.forEach(element => {
        if (element) {
            observer.observe(element, {
                attributes: true,
                attributeFilter: ['style', 'class'],
                childList: true,
                subtree: true
            });
        }
    });
}

/**
 * Update Theme Toggle Button
 */
function updateThemeToggleButton(theme) {
    const toggleButtons = document.querySelectorAll('[data-theme-toggle]');
    toggleButtons.forEach(button => {
        const icon = button.querySelector('i');
        const text = button.querySelector('.theme-toggle-text');
        const isInSidebar = button.closest('.sidebar-footer');
        const isInNavbar = button.closest('.navbar');

        if (icon) {
            icon.className = theme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
        }

        if (text) {
            if (isInSidebar) {
                // Shorter text for sidebar
                text.textContent = theme === 'dark' ? 'Light Mode' : 'Dark Mode';
            } else if (isInNavbar) {
                // Even shorter text for navbar
                text.textContent = theme === 'dark' ? 'Light' : 'Dark';
            } else {
                // Full text for other locations
                text.textContent = theme === 'dark' ? 'Light Mode' : 'Dark Mode';
            }
        }

        button.setAttribute('title', theme === 'dark' ? 'Switch to Light Mode' : 'Switch to Dark Mode');
        button.setAttribute('data-bs-original-title', theme === 'dark' ? 'Switch to Light Mode' : 'Switch to Dark Mode');
    });
}

/**
 * Global Theme Toggle Function
 */
function toggleTheme() {
    const html = document.documentElement;
    const currentTheme = html.getAttribute('data-bs-theme') || 'light';
    const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

    // Update theme on both html and body
    html.setAttribute('data-bs-theme', newTheme);
    document.body.setAttribute('data-bs-theme', newTheme);

    // Save to localStorage
    localStorage.setItem('theme', newTheme);

    // Update all theme toggle buttons
    updateThemeToggleButton(newTheme);

    // Add smooth transition effect
    document.body.style.transition = 'background-color 0.3s ease, color 0.3s ease';
    setTimeout(() => {
        document.body.style.transition = '';
    }, 300);

    // Dispatch theme change event
    document.dispatchEvent(new CustomEvent('themeChanged', {
        detail: { theme: newTheme }
    }));

    // Force apply theme to all containers
    applyThemeToContainers(newTheme);

    // Force update all theme-dependent elements
    forceThemeUpdate(newTheme);

    // Log for debugging
    console.log(`🎨 Theme switched to: ${newTheme}`);
}

/**
 * Apply Theme to Major Containers
 */
function applyThemeToContainers(theme) {
    const containers = [
        '.modern-sidebar',
        '.modern-navbar',
        '.main-content',
        '.container-fluid',
        '.card',
        '.modal',
        '.dropdown-menu',
        '.offcanvas'
    ];

    containers.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach(element => {
            element.setAttribute('data-bs-theme', theme);
        });
    });

    // Force apply dark backgrounds with inline styles if needed
    if (theme === 'dark') {
        forceDarkBackgrounds();
    } else {
        removeForcedDarkBackgrounds();
    }
}

/**
 * Force Dark Backgrounds with Inline Styles
 */
function forceDarkBackgrounds() {
    // Force sidebar dark background
    const sidebars = document.querySelectorAll('.modern-sidebar, .sidebar, #sidebar');
    sidebars.forEach(sidebar => {
        sidebar.style.setProperty('background', '#111827', 'important');
        sidebar.style.setProperty('background-color', '#111827', 'important');
        sidebar.style.setProperty('background-image', 'none', 'important');
    });

    // Force navbar dark background
    const navbars = document.querySelectorAll('.modern-navbar, .navbar, nav.navbar');
    navbars.forEach(navbar => {
        navbar.style.setProperty('background', '#111827', 'important');
        navbar.style.setProperty('background-color', '#111827', 'important');
        navbar.style.setProperty('background-image', 'none', 'important');
    });

    // Force sidebar sections dark background
    const sidebarSections = document.querySelectorAll('.sidebar-header, .sidebar-navigation, .sidebar-footer, .sidebar-user');
    sidebarSections.forEach(section => {
        section.style.setProperty('background', '#111827', 'important');
        section.style.setProperty('background-color', '#111827', 'important');
        section.style.setProperty('background-image', 'none', 'important');
    });

    // Force body dark background
    document.body.style.setProperty('background', '#0f172a', 'important');
    document.body.style.setProperty('background-color', '#0f172a', 'important');
    document.body.style.setProperty('color', '#f9fafb', 'important');
}

/**
 * Remove Forced Dark Backgrounds
 */
function removeForcedDarkBackgrounds() {
    const elements = document.querySelectorAll('.modern-sidebar, .sidebar, #sidebar, .modern-navbar, .navbar, nav.navbar, .sidebar-header, .sidebar-navigation, .sidebar-footer, .sidebar-user');
    elements.forEach(element => {
        element.style.removeProperty('background');
        element.style.removeProperty('background-color');
        element.style.removeProperty('background-image');
    });

    document.body.style.removeProperty('background');
    document.body.style.removeProperty('background-color');
    document.body.style.removeProperty('color');
}

/**
 * Force Update Theme-Dependent Elements
 */
function forceThemeUpdate(theme) {
    // Update all icons in theme toggle buttons
    const themeIcons = document.querySelectorAll('[data-theme-toggle] i');
    themeIcons.forEach(icon => {
        icon.className = theme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
    });

    // Update all theme toggle texts
    const themeTexts = document.querySelectorAll('.theme-toggle-text');
    themeTexts.forEach(text => {
        const isInSidebar = text.closest('.sidebar-footer');
        const isInNavbar = text.closest('.navbar');

        if (isInSidebar) {
            text.textContent = theme === 'dark' ? 'Light Mode' : 'Dark Mode';
        } else if (isInNavbar) {
            text.textContent = theme === 'dark' ? 'Light' : 'Dark';
        } else {
            text.textContent = theme === 'dark' ? 'Light Mode' : 'Dark Mode';
        }
    });

    // Force refresh of any dynamic content
    const event = new CustomEvent('themeForceUpdate', {
        detail: { theme: theme }
    });
    document.dispatchEvent(event);
}

/**
 * Initialize Control Sidebar - Removed duplicate implementation
 * The actual implementation is in control-sidebar.js
 */
function initializeControlSidebar() {
    // This function is now handled by control-sidebar.js
    console.log('Control sidebar initialization delegated to control-sidebar.js');
}

/**
 * Initialize Control Options - Removed duplicate implementation
 * The actual implementation is in control-sidebar.js
 */

// Expose functions globally for onclick handlers
window.applyPreview = applyPreview;
window.resetLayout = resetLayout;
window.updateLivePreview = updateLivePreview;
window.showNotification = showNotification;
window.toggleTheme = toggleTheme;
