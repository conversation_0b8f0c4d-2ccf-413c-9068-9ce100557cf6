DROP TABLE IF EXISTS `activity_log`;
CREATE TABLE `activity_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `activity` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `type` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `ip_address` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `user_agent` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `activity_log_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=28 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `activity_log` VALUES ('21', '7', 'Admin updated user: <EMAIL>', 'other', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-05-24 10:30:00');
INSERT INTO `activity_log` VALUES ('22', '7', 'Admin reset password for user: <EMAIL>', 'other', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-05-24 10:31:54');
INSERT INTO `activity_log` VALUES ('23', '7', 'Admin reset password for user: <EMAIL>', 'other', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-05-24 10:31:59');
INSERT INTO `activity_log` VALUES ('24', '7', 'User  berhasil logout', 'logout', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-05-24 10:32:04');
INSERT INTO `activity_log` VALUES ('25', '7', 'User  berhasil logout', 'logout', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-05-24 10:40:21');
INSERT INTO `activity_log` VALUES ('26', '7', 'User  berhasil logout', 'logout', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-05-24 10:53:08');
INSERT INTO `activity_log` VALUES ('27', '7', 'Admin updated user: <EMAIL>', 'other', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-05-24 10:56:55');

DROP TABLE IF EXISTS `activity_logs`;
CREATE TABLE `activity_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `aktivitas` text NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `activity_logs_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=latin1;

INSERT INTO `activity_logs` VALUES ('3', '7', 'Menambahkan produk laptop', '2025-05-24 16:04:07');
INSERT INTO `activity_logs` VALUES ('4', '7', 'Menambahkan produk snack', '2025-05-24 16:04:43');

DROP TABLE IF EXISTS `aktivitas`;
CREATE TABLE `aktivitas` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `aktivitas` text NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `aktivitas_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `anggaran`;
CREATE TABLE `anggaran` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `kategori_id` int(11) NOT NULL,
  `jumlah` decimal(15,2) NOT NULL,
  `periode_bulan` int(11) NOT NULL,
  `periode_tahun` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `kategori_id` (`kategori_id`),
  CONSTRAINT `anggaran_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `anggaran_ibfk_2` FOREIGN KEY (`kategori_id`) REFERENCES `kategori` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `backup_history`;
CREATE TABLE `backup_history` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `filename` varchar(255) NOT NULL,
  `file_size` bigint(20) NOT NULL,
  `type` enum('backup','restore') NOT NULL,
  `status` enum('success','failed') NOT NULL,
  `message` text,
  `created_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `created_by` (`created_by`),
  CONSTRAINT `backup_history_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `kategori`;
CREATE TABLE `kategori` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nama_kategori` varchar(100) DEFAULT NULL,
  `nama` varchar(100) NOT NULL,
  `tipe` enum('pemasukan','pengeluaran') NOT NULL,
  `deskripsi` text,
  `user_id` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `kategori_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=20 DEFAULT CHARSET=latin1;

INSERT INTO `kategori` VALUES ('1', 'Gaji', 'Gaji', 'pemasukan', 'Pendapatan dari gaji bulanan', NULL, '2025-05-22 22:47:45', '2025-05-22 23:25:37');
INSERT INTO `kategori` VALUES ('2', 'Bonus', 'Bonus', 'pemasukan', 'Pendapatan tambahan', NULL, '2025-05-22 22:47:45', '2025-05-22 23:25:37');
INSERT INTO `kategori` VALUES ('3', 'Makanan', 'Makanan', 'pengeluaran', 'Pengeluaran untuk makanan dan minuman', NULL, '2025-05-22 22:47:45', '2025-05-22 23:25:37');
INSERT INTO `kategori` VALUES ('4', 'Transportasi', 'Transportasi', 'pengeluaran', 'Pengeluaran untuk transportasi', NULL, '2025-05-22 22:47:45', '2025-05-22 23:25:37');
INSERT INTO `kategori` VALUES ('5', 'Tagihan', 'Tagihan', 'pengeluaran', 'Pembayaran tagihan rutin', NULL, '2025-05-22 22:47:45', '2025-05-22 23:25:37');
INSERT INTO `kategori` VALUES ('8', 'Investasi', 'Investasi', 'pemasukan', NULL, NULL, '2025-05-22 23:20:24', '2025-05-22 23:25:37');
INSERT INTO `kategori` VALUES ('9', 'Makanan & Minuman', 'Makanan & Minuman', 'pengeluaran', NULL, NULL, '2025-05-22 23:20:24', '2025-05-22 23:25:37');
INSERT INTO `kategori` VALUES ('11', 'Belanja', 'Belanja', 'pengeluaran', NULL, NULL, '2025-05-22 23:20:24', '2025-05-24 15:57:23');
INSERT INTO `kategori` VALUES ('13', 'Hiburan', 'Hiburan', 'pengeluaran', NULL, NULL, '2025-05-22 23:20:24', '2025-05-22 23:25:37');
INSERT INTO `kategori` VALUES ('14', 'Kesehatan', 'Kesehatan', 'pengeluaran', NULL, NULL, '2025-05-22 23:20:24', '2025-05-22 23:25:37');
INSERT INTO `kategori` VALUES ('15', 'Pendidikan', 'Pendidikan', 'pengeluaran', NULL, NULL, '2025-05-22 23:20:24', '2025-05-22 23:25:37');
INSERT INTO `kategori` VALUES ('16', 'top up', 'top up', 'pengeluaran', NULL, '7', '2025-05-24 15:54:26', '2025-05-24 15:54:26');
INSERT INTO `kategori` VALUES ('18', '11111', '11111', 'pemasukan', NULL, NULL, '2025-05-24 16:11:31', '2025-05-24 16:25:34');
INSERT INTO `kategori` VALUES ('19', 'test', 'test', 'pemasukan', NULL, '7', '2025-05-24 16:25:54', '2025-05-24 16:25:54');

DROP TABLE IF EXISTS `notifications`;
CREATE TABLE `notifications` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `message` text NOT NULL,
  `is_read` tinyint(1) DEFAULT '0',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `notifications_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `notifikasi`;
CREATE TABLE `notifikasi` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `judul` varchar(100) NOT NULL,
  `pesan` text NOT NULL,
  `tipe` enum('info','success','warning','danger') DEFAULT 'info',
  `dibaca` tinyint(1) DEFAULT '0',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `notifikasi_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=22 DEFAULT CHARSET=latin1;

INSERT INTO `notifikasi` VALUES ('7', '7', 'Login Berhasil', 'Selamat datang kembali, Administrator', 'success', '0', '2025-05-24 10:29:50');
INSERT INTO `notifikasi` VALUES ('8', '7', 'Login Berhasil', 'Selamat datang kembali, Administrator', 'success', '0', '2025-05-24 10:32:15');
INSERT INTO `notifikasi` VALUES ('9', '7', 'Login Berhasil', 'Selamat datang kembali, Administrator', 'success', '0', '2025-05-24 10:40:35');
INSERT INTO `notifikasi` VALUES ('10', '7', 'Login Berhasil', 'Selamat datang kembali, Administrator', 'success', '0', '2025-05-24 10:55:05');
INSERT INTO `notifikasi` VALUES ('11', '7', 'Login Berhasil', 'Selamat datang kembali, Administrator', 'success', '0', '2025-05-24 11:27:45');
INSERT INTO `notifikasi` VALUES ('12', '7', 'Login Berhasil', 'Selamat datang kembali, Administrator', 'success', '0', '2025-05-24 14:23:50');
INSERT INTO `notifikasi` VALUES ('13', '7', 'Login Berhasil', 'Selamat datang kembali, Administrator', 'success', '0', '2025-05-24 15:10:32');
INSERT INTO `notifikasi` VALUES ('14', '7', 'Login Berhasil', 'Selamat datang kembali, Administrator', 'success', '0', '2025-05-24 15:11:15');
INSERT INTO `notifikasi` VALUES ('15', '9', 'Login Berhasil', 'Selamat datang kembali, Jack', 'success', '0', '2025-05-24 15:30:32');
INSERT INTO `notifikasi` VALUES ('16', '9', 'Login Berhasil', 'Selamat datang kembali, Jack', 'success', '0', '2025-05-24 15:30:56');
INSERT INTO `notifikasi` VALUES ('17', '7', 'Login Berhasil', 'Selamat datang kembali, Administrator', 'success', '0', '2025-05-24 15:31:32');
INSERT INTO `notifikasi` VALUES ('18', '7', 'Login Berhasil', 'Selamat datang kembali, Administrator', 'success', '0', '2025-05-24 15:32:12');
INSERT INTO `notifikasi` VALUES ('19', '7', 'Login Berhasil', 'Selamat datang kembali, Administrator', 'success', '0', '2025-05-24 15:38:50');
INSERT INTO `notifikasi` VALUES ('20', '9', 'Login Berhasil', 'Selamat datang kembali, Jack', 'success', '0', '2025-05-24 15:50:08');
INSERT INTO `notifikasi` VALUES ('21', '7', 'Login Berhasil', 'Selamat datang kembali, Administrator', 'success', '0', '2025-05-24 15:50:25');

DROP TABLE IF EXISTS `password_resets`;
CREATE TABLE `password_resets` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `email` varchar(100) NOT NULL,
  `token` varchar(100) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `expires_at` timestamp NULL DEFAULT NULL,
  `used` tinyint(1) DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `email` (`email`),
  KEY `token` (`token`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `permissions`;
CREATE TABLE `permissions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL,
  `description` text,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=latin1;

INSERT INTO `permissions` VALUES ('1', 'manage_users', 'Mengelola pengguna', '2025-05-24 16:51:54');
INSERT INTO `permissions` VALUES ('2', 'manage_roles', 'Mengelola peran', '2025-05-24 16:51:54');
INSERT INTO `permissions` VALUES ('3', 'manage_permissions', 'Mengelola hak akses', '2025-05-24 16:51:54');
INSERT INTO `permissions` VALUES ('4', 'view_reports', 'Melihat laporan', '2025-05-24 16:51:54');
INSERT INTO `permissions` VALUES ('5', 'manage_transactions', 'Mengelola transaksi', '2025-05-24 16:51:54');
INSERT INTO `permissions` VALUES ('6', 'manage_categories', 'Mengelola kategs', '2025-05-24 16:51:54');
INSERT INTO `permissions` VALUES ('7', 'manage_test', '11', '2025-05-24 16:53:31');

DROP TABLE IF EXISTS `produk`;
CREATE TABLE `produk` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `nama` varchar(100) NOT NULL,
  `deskripsi` text,
  `harga_beli` decimal(15,2) NOT NULL,
  `harga_jual` decimal(15,2) NOT NULL,
  `stok` int(11) DEFAULT '0',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `kategori_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `produk_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=latin1;

INSERT INTO `produk` VALUES ('1', '7', 'laptop', NULL, '100.00', '200.00', '0', '2025-05-24 16:04:07', '2025-05-24 16:11:31', NULL);
INSERT INTO `produk` VALUES ('2', '7', 'snack', NULL, '1000.00', '2000.00', '1', '2025-05-24 16:04:43', '2025-05-24 16:04:43', NULL);

DROP TABLE IF EXISTS `reminders`;
CREATE TABLE `reminders` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `title` varchar(255) NOT NULL,
  `description` text,
  `reminder_date` datetime NOT NULL,
  `is_completed` tinyint(1) DEFAULT '0',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `notification_sent` tinyint(1) DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `reminders_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `role_permissions`;
CREATE TABLE `role_permissions` (
  `role_id` int(11) NOT NULL,
  `permission_id` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`role_id`,`permission_id`),
  KEY `permission_id` (`permission_id`),
  CONSTRAINT `role_permissions_ibfk_1` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE,
  CONSTRAINT `role_permissions_ibfk_2` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `roles`;
CREATE TABLE `roles` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL,
  `description` text,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4;

INSERT INTO `roles` VALUES ('1', 'expert', 'role full acces', '2025-05-24 15:14:58', '2025-05-24 15:14:58');

DROP TABLE IF EXISTS `settings`;
CREATE TABLE `settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL,
  `value` text,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=191 DEFAULT CHARSET=latin1;

INSERT INTO `settings` VALUES ('1', 'site_name', 'Sistem Keuangan', '2025-05-24 10:58:24', '2025-05-24 10:58:24');
INSERT INTO `settings` VALUES ('2', 'site_description', 'Sistem Manajemen Keuangan', '2025-05-24 10:58:24', '2025-05-24 10:58:24');
INSERT INTO `settings` VALUES ('3', 'currency', 'IDR', '2025-05-24 10:58:24', '2025-05-24 10:58:24');
INSERT INTO `settings` VALUES ('4', 'date_format', 'd/m/Y', '2025-05-24 10:58:24', '2025-05-24 10:58:24');
INSERT INTO `settings` VALUES ('5', 'time_format', 'H:i', '2025-05-24 10:58:24', '2025-05-24 10:58:24');
INSERT INTO `settings` VALUES ('6', 'maintenance_mode', '0', '2025-05-24 10:58:24', '2025-05-24 10:58:24');
INSERT INTO `settings` VALUES ('7', 'registration_enabled', '1', '2025-05-24 10:58:24', '2025-05-24 10:58:24');
INSERT INTO `settings` VALUES ('8', 'default_role', 'user', '2025-05-24 10:58:24', '2025-05-24 10:58:24');
INSERT INTO `settings` VALUES ('9', 'max_login_attempts', '5', '2025-05-24 10:58:24', '2025-05-24 10:58:24');
INSERT INTO `settings` VALUES ('10', 'session_timeout', '30', '2025-05-24 10:58:24', '2025-05-24 10:58:24');

DROP TABLE IF EXISTS `target`;
CREATE TABLE `target` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `nama` varchar(100) NOT NULL,
  `jumlah_target` decimal(15,2) NOT NULL,
  `jumlah_terkumpul` decimal(15,2) DEFAULT '0.00',
  `tanggal_mulai` date NOT NULL,
  `tanggal_selesai` date NOT NULL,
  `status` enum('aktif','selesai','batal') DEFAULT 'aktif',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `target_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=latin1;

INSERT INTO `target` VALUES ('3', '7', 'nikah sama lisa  ', '100000000.00', '0.00', '2025-05-01', '2026-09-24', 'aktif', '2025-05-24 15:58:22', '2025-05-24 15:58:22');
INSERT INTO `target` VALUES ('4', '7', 'mobil toyota zenix', '10000000000.00', '0.00', '2025-05-01', '2041-12-24', 'aktif', '2025-05-24 15:59:21', '2025-05-24 15:59:21');

DROP TABLE IF EXISTS `target_keuangan`;
CREATE TABLE `target_keuangan` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `nama` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `jumlah_target` decimal(15,2) NOT NULL,
  `jumlah_terkumpul` decimal(15,2) DEFAULT '0.00',
  `tanggal_mulai` date NOT NULL,
  `tanggal_selesai` date NOT NULL,
  `status` enum('aktif','selesai','dibatalkan') COLLATE utf8mb4_unicode_ci DEFAULT 'aktif',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `target_keuangan_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `transaksi`;
CREATE TABLE `transaksi` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `kategori_id` int(11) NOT NULL,
  `jenis` enum('pemasukan','pengeluaran') NOT NULL,
  `jumlah` decimal(15,2) NOT NULL,
  `tanggal` date NOT NULL,
  `keterangan` text,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `kategori_id` (`kategori_id`),
  KEY `idx_tanggal` (`tanggal`),
  KEY `idx_user_tanggal` (`user_id`,`tanggal`),
  CONSTRAINT `transaksi_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `transaksi_ibfk_2` FOREIGN KEY (`kategori_id`) REFERENCES `kategori` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=latin1;

INSERT INTO `transaksi` VALUES ('3', '7', '2', 'pemasukan', '4700000.00', '2025-05-24', '22', '2025-05-24 15:51:25', '2025-05-24 15:51:25');
INSERT INTO `transaksi` VALUES ('4', '7', '18', 'pemasukan', '200.00', '2025-05-24', 'Penjualan produk: laptop', '2025-05-24 16:11:31', '2025-05-24 16:11:31');

DROP TABLE IF EXISTS `transaksi_produk`;
CREATE TABLE `transaksi_produk` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `produk_id` int(11) NOT NULL,
  `jumlah` int(11) NOT NULL,
  `harga_jual` decimal(15,2) NOT NULL,
  `tanggal` date NOT NULL,
  `keterangan` text,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `transaksi_id` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `idx_produk` (`produk_id`),
  CONSTRAINT `transaksi_produk_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `transaksi_produk_ibfk_2` FOREIGN KEY (`produk_id`) REFERENCES `produk` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=latin1;

INSERT INTO `transaksi_produk` VALUES ('1', '7', '1', '1', '200.00', '2025-05-24', '11', '2025-05-24 16:11:31', '4');

DROP TABLE IF EXISTS `users`;
CREATE TABLE `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `email` varchar(100) NOT NULL,
  `password` varchar(255) NOT NULL,
  `nama_lengkap` varchar(100) NOT NULL,
  `role` enum('admin','user') DEFAULT 'user',
  `status` enum('aktif','nonaktif') DEFAULT 'aktif',
  `reset_token` varchar(100) DEFAULT NULL,
  `reset_token_expires` datetime DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `nama` varchar(100) NOT NULL,
  `email_notifications` tinyint(1) DEFAULT '1',
  `push_notifications` tinyint(1) DEFAULT '1',
  `theme` varchar(10) DEFAULT 'light',
  `language` varchar(2) DEFAULT 'id',
  `settings` json DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=latin1;

INSERT INTO `users` VALUES ('7', 'admin', '<EMAIL>', '$2y$10$TfIdgCUnTBJpt2J9/Tlkzue0Mlr0sjDWm1AaNgSKSTtMfZNBzKWme', 'Administrator', 'admin', 'aktif', NULL, NULL, '2025-05-24 10:28:53', '2025-05-24 10:31:59', 'Administrator', '1', '1', 'light', 'id', NULL);
INSERT INTO `users` VALUES ('9', '<EMAIL>', '<EMAIL>', '$2y$10$k0q4eUnyzUCiPqPZYq7a1.aGufz1tZMvVd0v/tddWehYuE5JL5P/y', 'Jack', 'user', 'aktif', NULL, NULL, '2025-05-24 15:30:10', '2025-05-24 15:49:50', 'Jack', '1', '1', 'light', 'id', NULL);

