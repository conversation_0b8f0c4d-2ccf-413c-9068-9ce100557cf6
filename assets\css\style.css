/* CSS Variables */
:root {
    /* Layout Variables */
    --navbar-height: 72px;
    --sidebar-width: 280px;
    --sidebar-collapsed-width: 70px;

    /* Color Variables */
    --primary-color: #2563eb;
    --primary-hover: #1d4ed8;
    --success-color: #10b981;
    --danger-color: #ef4444;
    --warning-color: #f59e0b;
    --info-color: #06b6d4;

    /* Background Variables */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --navbar-bg: rgba(255, 255, 255, 0.95);

    /* Text Variables */
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --text-muted: #94a3b8;
    --text-inverse: #ffffff;

    /* Border Variables */
    --border-color: #e2e8f0;
    --border-light: #f1f5f9;
    --border-dark: #cbd5e1;
    --navbar-border: rgba(226, 232, 240, 0.8);

    /* Shadow Variables */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

    /* Radius Variables */
    --radius-sm: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;

    /* Transition Variables */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
}

/* Dark Mode Variables */
[data-bs-theme="dark"] {
    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --bg-tertiary: #334155;
    --navbar-bg: rgba(15, 23, 42, 0.95);

    --text-primary: #f8fafc;
    --text-secondary: #cbd5e1;
    --text-muted: #64748b;

    --border-color: #334155;
    --border-light: #475569;
    --border-dark: #1e293b;
    --navbar-border: rgba(51, 65, 85, 0.8);
}

/* Common styles */
body {
    font-family: 'Nunito', sans-serif;
    background-color: var(--bs-body-bg);
    color: var(--bs-body-color);
}

/* Wrapper */
.wrapper {
    display: flex;
    min-height: 100vh;
}

/* Main content */
.main-content {
    flex: 1;
    padding: 1.5rem;
    margin-left: 250px;
    transition: margin-left 0.3s ease;
}

.main-content.expanded {
    margin-left: 70px;
}

/* Card styles */
.card {
    margin-bottom: 1.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.card-header {
    background-color: transparent;
    border-bottom: 1px solid var(--bs-card-border-color);
    padding: 1rem 1.25rem;
}

.card-title {
    margin-bottom: 0;
    font-weight: 600;
}

/* Form styles */
.form-label {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.form-control:focus,
.form-select:focus {
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* Button styles */
.btn {
    font-weight: 600;
    padding: 0.5rem 1rem;
}

.btn-icon {
    padding: 0.5rem;
    line-height: 1;
}

/* Table styles */
.table th {
    font-weight: 600;
    background-color: var(--bs-table-bg);
}

/* Alert styles */
.alert {
    margin-bottom: 1.5rem;
}

/* Badge styles */
.badge {
    font-weight: 500;
    padding: 0.35em 0.65em;
}

/* Responsive styles */
@media (max-width: 768px) {
    .main-content {
        margin-left: 0;
        padding: 1rem;
    }

    .card {
        margin-bottom: 1rem;
    }
}