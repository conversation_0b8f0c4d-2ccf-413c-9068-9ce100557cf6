<?php
/**
 * Theme Helper Functions
 * Provides functions for theme management and application
 */

/**
 * Get user theme preferences
 * @param int $userId User ID
 * @return array Theme preferences
 */
function getUserThemePreferences($userId = null) {
    global $pdo;

    if ($userId === null) {
        $currentUser = getCurrentUser();
        if (!$currentUser) return getDefaultThemePreferences();
        $userId = $currentUser['id'];
    }

    try {
        // Check if database connection exists
        if (!$pdo) {
            error_log("Database connection not available");
            return getDefaultThemePreferences();
        }

        // Check if table exists
        $stmt = $pdo->query("SHOW TABLES LIKE 'user_theme_preferences'");
        if ($stmt->rowCount() == 0) {
            error_log("Theme tables not found - using default preferences");
            return getDefaultThemePreferences();
        }

        $stmt = $pdo->prepare("SELECT * FROM user_theme_preferences WHERE user_id = ?");
        $stmt->execute([$userId]);
        $preferences = $stmt->fetch();

        if (!$preferences) {
            // Create default preferences for user
            $defaults = getDefaultThemePreferences();
            createUserThemePreferences($userId, $defaults);
            return $defaults;
        }

        return $preferences;
    } catch (PDOException $e) {
        error_log("Error getting user theme preferences: " . $e->getMessage());
        return getDefaultThemePreferences();
    }
}

/**
 * Get default theme preferences
 * @return array Default theme preferences
 */
function getDefaultThemePreferences() {
    return [
        'theme_mode' => 'light',
        'primary_color' => '#2563eb',
        'secondary_color' => '#64748b',
        'sidebar_color' => 'dark',
        'navbar_color' => 'primary',
        'custom_css' => ''
    ];
}

/**
 * Create user theme preferences
 * @param int $userId User ID
 * @param array $preferences Theme preferences
 * @return bool Success status
 */
function createUserThemePreferences($userId, $preferences) {
    global $pdo;

    try {
        // Check if database connection exists
        if (!$pdo) {
            error_log("Database connection not available");
            return false;
        }

        // Check if table exists
        $stmt = $pdo->query("SHOW TABLES LIKE 'user_theme_preferences'");
        if ($stmt->rowCount() == 0) {
            error_log("Theme tables not found - cannot create preferences");
            return false;
        }

        $stmt = $pdo->prepare("
            INSERT INTO user_theme_preferences
            (user_id, theme_mode, primary_color, secondary_color, sidebar_color, navbar_color, custom_css)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ");

        return $stmt->execute([
            $userId,
            $preferences['theme_mode'] ?? 'light',
            $preferences['primary_color'] ?? '#2563eb',
            $preferences['secondary_color'] ?? '#64748b',
            $preferences['sidebar_color'] ?? 'dark',
            $preferences['navbar_color'] ?? 'primary',
            $preferences['custom_css'] ?? ''
        ]);
    } catch (PDOException $e) {
        error_log("Error creating user theme preferences: " . $e->getMessage());
        return false;
    }
}

/**
 * Update user theme preferences
 * @param int $userId User ID
 * @param array $preferences Theme preferences
 * @return bool Success status
 */
function updateUserThemePreferences($userId, $preferences) {
    global $pdo;

    try {
        // Check if database connection exists
        if (!$pdo) {
            error_log("Database connection not available");
            return false;
        }

        // Check if table exists
        $stmt = $pdo->query("SHOW TABLES LIKE 'user_theme_preferences'");
        if ($stmt->rowCount() == 0) {
            error_log("Theme tables not found - cannot update preferences");
            return false;
        }

        // Check if layout_style column exists
        $stmt = $pdo->query("SHOW COLUMNS FROM user_theme_preferences LIKE 'layout_style'");
        $hasLayoutStyle = $stmt->rowCount() > 0;

        if ($hasLayoutStyle) {
            // Use query with layout_style column
            $stmt = $pdo->prepare("
                INSERT INTO user_theme_preferences
                (user_id, theme_mode, primary_color, secondary_color, sidebar_color, navbar_color, layout_style, custom_css)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ON DUPLICATE KEY UPDATE
                theme_mode = VALUES(theme_mode),
                primary_color = VALUES(primary_color),
                secondary_color = VALUES(secondary_color),
                sidebar_color = VALUES(sidebar_color),
                navbar_color = VALUES(navbar_color),
                layout_style = VALUES(layout_style),
                custom_css = VALUES(custom_css),
                updated_at = CURRENT_TIMESTAMP
            ");

            $result = $stmt->execute([
                $userId,
                $preferences['theme_mode'] ?? 'light',
                $preferences['primary_color'] ?? '#2563eb',
                $preferences['secondary_color'] ?? '#64748b',
                $preferences['sidebar_color'] ?? 'dark',
                $preferences['navbar_color'] ?? 'primary',
                $preferences['layout_style'] ?? 'standard',
                $preferences['custom_css'] ?? ''
            ]);

            error_log("Theme update with layout_style - User: $userId, Mode: " . ($preferences['theme_mode'] ?? 'light') . ", Primary: " . ($preferences['primary_color'] ?? '#2563eb'));
        } else {
            // Use query without layout_style column (backward compatibility)
            $stmt = $pdo->prepare("
                INSERT INTO user_theme_preferences
                (user_id, theme_mode, primary_color, secondary_color, sidebar_color, navbar_color, custom_css)
                VALUES (?, ?, ?, ?, ?, ?, ?)
                ON DUPLICATE KEY UPDATE
                theme_mode = VALUES(theme_mode),
                primary_color = VALUES(primary_color),
                secondary_color = VALUES(secondary_color),
                sidebar_color = VALUES(sidebar_color),
                navbar_color = VALUES(navbar_color),
                custom_css = VALUES(custom_css),
                updated_at = CURRENT_TIMESTAMP
            ");

            $result = $stmt->execute([
                $userId,
                $preferences['theme_mode'] ?? 'light',
                $preferences['primary_color'] ?? '#2563eb',
                $preferences['secondary_color'] ?? '#64748b',
                $preferences['sidebar_color'] ?? 'dark',
                $preferences['navbar_color'] ?? 'primary',
                $preferences['custom_css'] ?? ''
            ]);
        }

        if ($result) {
            error_log("Theme preferences updated successfully for user $userId");
        }

        return $result;
    } catch (PDOException $e) {
        error_log("Error updating user theme preferences: " . $e->getMessage());
        return false;
    }
}

/**
 * Get system theme settings
 * @return array System settings
 */
function getSystemThemeSettings() {
    global $pdo;

    try {
        $stmt = $pdo->query("SELECT setting_key, setting_value FROM system_settings WHERE setting_key LIKE '%theme%' OR setting_key LIKE '%color%'");
        $settings = [];
        while ($row = $stmt->fetch()) {
            $settings[$row['setting_key']] = $row['setting_value'];
        }
        return $settings;
    } catch (PDOException $e) {
        error_log("Error getting system theme settings: " . $e->getMessage());
        return [];
    }
}

/**
 * Generate CSS variables from theme preferences
 * @param array $preferences Theme preferences
 * @return string CSS variables
 */
function generateThemeCSS($preferences) {
    // Validate and sanitize preferences
    $preferences = validateThemePreferences($preferences);

    $css = ":root {\n";

    // Primary and secondary colors
    $css .= "    --primary-color: {$preferences['primary_color']};\n";
    $css .= "    --secondary-color: {$preferences['secondary_color']};\n";

    // Generate color variations with error handling
    try {
        $primaryRgb = hexToRgb($preferences['primary_color']);
        $css .= "    --primary-rgb: {$primaryRgb['r']}, {$primaryRgb['g']}, {$primaryRgb['b']};\n";
        $css .= "    --primary-hover: " . adjustBrightness($preferences['primary_color'], -20) . ";\n";
    } catch (Exception $e) {
        error_log("Error generating theme CSS: " . $e->getMessage());
        // Use fallback colors
        $css .= "    --primary-rgb: 37, 99, 235;\n";
        $css .= "    --primary-hover: #1d4ed8;\n";
    }

    // Theme mode specific variables
    if ($preferences['theme_mode'] === 'dark') {
        $css .= "    --bg-primary: #0f172a;\n";
        $css .= "    --bg-secondary: #1e293b;\n";
        $css .= "    --bg-tertiary: #334155;\n";
        $css .= "    --text-primary: #f8fafc;\n";
        $css .= "    --text-secondary: #cbd5e1;\n";
        $css .= "    --text-muted: #64748b;\n";
        $css .= "    --border-color: #334155;\n";
    } else {
        $css .= "    --bg-primary: #ffffff;\n";
        $css .= "    --bg-secondary: #f8fafc;\n";
        $css .= "    --bg-tertiary: #f1f5f9;\n";
        $css .= "    --text-primary: #1e293b;\n";
        $css .= "    --text-secondary: #64748b;\n";
        $css .= "    --text-muted: #94a3b8;\n";
        $css .= "    --border-color: #e2e8f0;\n";
    }

    $css .= "}\n";

    // Add layout-specific CSS
    $layoutStyle = $preferences['layout_style'] ?? 'standard';
    if ($layoutStyle !== 'standard') {
        $css .= "\n/* Layout CSS */\n";
        $css .= generateLayoutCSS($layoutStyle);
    }

    // Add custom CSS if provided
    if (!empty($preferences['custom_css'])) {
        $css .= "\n/* Custom CSS */\n";
        $css .= $preferences['custom_css'];
    }

    return $css;
}

/**
 * Validate and sanitize theme preferences
 * @param array $preferences Theme preferences
 * @return array Validated preferences
 */
function validateThemePreferences($preferences) {
    $defaults = getDefaultThemePreferences();

    // Validate theme_mode
    $validModes = ['light', 'dark', 'auto'];
    if (!isset($preferences['theme_mode']) || !in_array($preferences['theme_mode'], $validModes)) {
        $preferences['theme_mode'] = $defaults['theme_mode'];
    }

    // Validate colors (must be valid hex colors)
    if (!isset($preferences['primary_color']) || !isValidHexColor($preferences['primary_color'])) {
        $preferences['primary_color'] = $defaults['primary_color'];
    }

    if (!isset($preferences['secondary_color']) || !isValidHexColor($preferences['secondary_color'])) {
        $preferences['secondary_color'] = $defaults['secondary_color'];
    }

    // Validate sidebar and navbar colors
    $validColors = ['primary', 'secondary', 'success', 'danger', 'warning', 'info', 'light', 'dark'];
    if (!isset($preferences['sidebar_color']) || !in_array($preferences['sidebar_color'], $validColors)) {
        $preferences['sidebar_color'] = $defaults['sidebar_color'];
    }

    if (!isset($preferences['navbar_color']) || !in_array($preferences['navbar_color'], $validColors)) {
        $preferences['navbar_color'] = $defaults['navbar_color'];
    }

    // Validate layout style
    $validLayouts = array_keys(getLayoutStyles());
    if (!isset($preferences['layout_style']) || !in_array($preferences['layout_style'], $validLayouts)) {
        $preferences['layout_style'] = $defaults['layout_style'] ?? 'standard';
    }

    // Sanitize custom CSS
    if (!isset($preferences['custom_css'])) {
        $preferences['custom_css'] = '';
    }

    return $preferences;
}

/**
 * Check if a string is a valid hex color
 * @param string $hex Hex color string
 * @return bool True if valid hex color
 */
function isValidHexColor($hex) {
    return preg_match('/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/', $hex);
}

/**
 * Convert hex color to RGB
 * @param string $hex Hex color
 * @return array RGB values
 */
function hexToRgb($hex) {
    // Validate input
    if (!isValidHexColor($hex)) {
        throw new InvalidArgumentException("Invalid hex color: $hex");
    }

    $hex = ltrim($hex, '#');

    if (strlen($hex) == 3) {
        $hex = $hex[0] . $hex[0] . $hex[1] . $hex[1] . $hex[2] . $hex[2];
    }

    return [
        'r' => (int) hexdec(substr($hex, 0, 2)),
        'g' => (int) hexdec(substr($hex, 2, 2)),
        'b' => (int) hexdec(substr($hex, 4, 2))
    ];
}

/**
 * Adjust color brightness
 * @param string $hex Hex color
 * @param int $percent Brightness adjustment percentage
 * @return string Adjusted hex color
 */
function adjustBrightness($hex, $percent) {
    // Validate input
    if (!isValidHexColor($hex)) {
        throw new InvalidArgumentException("Invalid hex color: $hex");
    }

    // Ensure percent is numeric
    $percent = (float) $percent;

    $hex = ltrim($hex, '#');

    if (strlen($hex) == 3) {
        $hex = $hex[0] . $hex[0] . $hex[1] . $hex[1] . $hex[2] . $hex[2];
    }

    $r = (int) hexdec(substr($hex, 0, 2));
    $g = (int) hexdec(substr($hex, 2, 2));
    $b = (int) hexdec(substr($hex, 4, 2));

    // Calculate new values with proper rounding
    $r = (int) round(max(0, min(255, $r + ($r * $percent / 100))));
    $g = (int) round(max(0, min(255, $g + ($g * $percent / 100))));
    $b = (int) round(max(0, min(255, $b + ($b * $percent / 100))));

    return '#' . str_pad(dechex($r), 2, '0', STR_PAD_LEFT) .
                 str_pad(dechex($g), 2, '0', STR_PAD_LEFT) .
                 str_pad(dechex($b), 2, '0', STR_PAD_LEFT);
}

/**
 * Get available predefined themes
 * @return array Available themes
 */
function getPredefinedThemes() {
    return [
        'default' => [
            'name' => 'Default Theme',
            'description' => 'Classic blue and dark sidebar',
            'theme_mode' => 'light',
            'primary_color' => '#2563eb',
            'secondary_color' => '#64748b',
            'sidebar_color' => 'dark',
            'navbar_color' => 'primary',
            'layout_style' => 'standard'
        ],
        'dark' => [
            'name' => 'Dark Theme',
            'description' => 'Full dark mode experience',
            'theme_mode' => 'dark',
            'primary_color' => '#3b82f6',
            'secondary_color' => '#6b7280',
            'sidebar_color' => 'dark',
            'navbar_color' => 'dark',
            'layout_style' => 'standard'
        ],
        'modern' => [
            'name' => 'Modern Theme',
            'description' => 'Purple modern design',
            'theme_mode' => 'light',
            'primary_color' => '#8b5cf6',
            'secondary_color' => '#a78bfa',
            'sidebar_color' => 'primary',
            'navbar_color' => 'primary',
            'layout_style' => 'modern'
        ],
        'minimal' => [
            'name' => 'Minimal Theme',
            'description' => 'Clean light design',
            'theme_mode' => 'light',
            'primary_color' => '#10b981',
            'secondary_color' => '#6b7280',
            'sidebar_color' => 'light',
            'navbar_color' => 'light',
            'layout_style' => 'minimal'
        ],
        'ocean' => [
            'name' => 'Ocean Theme',
            'description' => 'Blue ocean inspired',
            'theme_mode' => 'light',
            'primary_color' => '#0ea5e9',
            'secondary_color' => '#0284c7',
            'sidebar_color' => 'info',
            'navbar_color' => 'info',
            'layout_style' => 'modern'
        ],
        'forest' => [
            'name' => 'Forest Theme',
            'description' => 'Green nature inspired',
            'theme_mode' => 'light',
            'primary_color' => '#059669',
            'secondary_color' => '#047857',
            'sidebar_color' => 'success',
            'navbar_color' => 'success',
            'layout_style' => 'modern'
        ],
        'sunset' => [
            'name' => 'Sunset Theme',
            'description' => 'Warm orange sunset colors',
            'theme_mode' => 'light',
            'primary_color' => '#f97316',
            'secondary_color' => '#ea580c',
            'sidebar_color' => 'warning',
            'navbar_color' => 'warning',
            'layout_style' => 'modern'
        ],
        'midnight' => [
            'name' => 'Midnight Theme',
            'description' => 'Deep dark with blue accents',
            'theme_mode' => 'dark',
            'primary_color' => '#1e40af',
            'secondary_color' => '#1e3a8a',
            'sidebar_color' => 'dark',
            'navbar_color' => 'dark',
            'layout_style' => 'compact'
        ],
        'cherry' => [
            'name' => 'Cherry Blossom',
            'description' => 'Soft pink cherry blossom theme',
            'theme_mode' => 'light',
            'primary_color' => '#ec4899',
            'secondary_color' => '#db2777',
            'sidebar_color' => 'light',
            'navbar_color' => 'light',
            'layout_style' => 'minimal'
        ],
        'corporate' => [
            'name' => 'Corporate Theme',
            'description' => 'Professional business theme',
            'theme_mode' => 'light',
            'primary_color' => '#374151',
            'secondary_color' => '#6b7280',
            'sidebar_color' => 'dark',
            'navbar_color' => 'dark',
            'layout_style' => 'corporate'
        ],
        'neon' => [
            'name' => 'Neon Theme',
            'description' => 'Vibrant neon cyberpunk style',
            'theme_mode' => 'dark',
            'primary_color' => '#00ff88',
            'secondary_color' => '#00cc6a',
            'sidebar_color' => 'dark',
            'navbar_color' => 'dark',
            'layout_style' => 'futuristic'
        ],
        'autumn' => [
            'name' => 'Autumn Theme',
            'description' => 'Warm autumn colors',
            'theme_mode' => 'light',
            'primary_color' => '#dc2626',
            'secondary_color' => '#b91c1c',
            'sidebar_color' => 'danger',
            'navbar_color' => 'danger',
            'layout_style' => 'modern'
        ]
    ];
}

/**
 * Get available layout styles
 * @return array Available layout styles
 */
function getLayoutStyles() {
    return [
        'standard' => [
            'name' => 'Standard Layout',
            'description' => 'Traditional sidebar and navbar layout',
            'sidebar_width' => '280px',
            'navbar_height' => '72px',
            'sidebar_position' => 'left',
            'navbar_position' => 'top',
            'content_padding' => '1.5rem'
        ],
        'modern' => [
            'name' => 'Modern Layout',
            'description' => 'Sleek modern design with rounded corners',
            'sidebar_width' => '300px',
            'navbar_height' => '80px',
            'sidebar_position' => 'left',
            'navbar_position' => 'top',
            'content_padding' => '2rem'
        ],
        'minimal' => [
            'name' => 'Minimal Layout',
            'description' => 'Clean minimal design with thin sidebar',
            'sidebar_width' => '240px',
            'navbar_height' => '60px',
            'sidebar_position' => 'left',
            'navbar_position' => 'top',
            'content_padding' => '1rem'
        ],
        'compact' => [
            'name' => 'Compact Layout',
            'description' => 'Space-efficient compact design',
            'sidebar_width' => '220px',
            'navbar_height' => '56px',
            'sidebar_position' => 'left',
            'navbar_position' => 'top',
            'content_padding' => '1rem'
        ],
        'corporate' => [
            'name' => 'Corporate Layout',
            'description' => 'Professional corporate design',
            'sidebar_width' => '320px',
            'navbar_height' => '76px',
            'sidebar_position' => 'left',
            'navbar_position' => 'top',
            'content_padding' => '2rem'
        ],
        'futuristic' => [
            'name' => 'Futuristic Layout',
            'description' => 'Sci-fi inspired futuristic design',
            'sidebar_width' => '280px',
            'navbar_height' => '70px',
            'sidebar_position' => 'left',
            'navbar_position' => 'top',
            'content_padding' => '1.5rem'
        ]
    ];
}

/**
 * Generate theme layout CSS (legacy function - renamed to avoid conflict)
 * @param string $layoutStyle Layout style name
 * @return string CSS for layout
 */
function generateThemeLayoutCSS($layoutStyle = 'standard') {
    $layouts = getLayoutStyles();
    $layout = $layouts[$layoutStyle] ?? $layouts['standard'];

    $css = "
    /* Layout: {$layout['name']} */
    :root {
        --sidebar-width: {$layout['sidebar_width']};
        --navbar-height: {$layout['navbar_height']};
        --content-padding: {$layout['content_padding']};
    }

    .main-content {
        margin-left: var(--sidebar-width);
        padding-top: var(--navbar-height);
        padding: var(--content-padding);
    }

    .navbar {
        height: var(--navbar-height);
        left: var(--sidebar-width);
        width: calc(100% - var(--sidebar-width));
    }

    .modern-sidebar {
        width: var(--sidebar-width);
    }
    ";

    // Add layout-specific styles
    switch ($layoutStyle) {
        case 'modern':
            $css .= "
            .modern-sidebar {
                border-radius: 0 20px 20px 0;
                backdrop-filter: blur(20px);
            }
            .navbar {
                border-radius: 0 0 20px 20px;
                backdrop-filter: blur(20px);
            }
            .main-content {
                border-radius: 20px;
                margin: 10px;
                margin-left: calc(var(--sidebar-width) + 10px);
                margin-top: calc(var(--navbar-height) + 10px);
                background: var(--bg-primary);
                box-shadow: var(--shadow-lg);
            }
            ";
            break;

        case 'minimal':
            $css .= "
            .modern-sidebar {
                border-right: 1px solid var(--border-light);
                box-shadow: none;
            }
            .navbar {
                border-bottom: 1px solid var(--border-light);
                box-shadow: none;
            }
            .sidebar-header {
                padding: 1rem;
            }
            .menu-item {
                padding: 0.5rem 1rem;
            }
            ";
            break;

        case 'compact':
            $css .= "
            .sidebar-header {
                padding: 1rem;
            }
            .menu-item {
                padding: 0.4rem 0.8rem;
                font-size: 0.9rem;
            }
            .navbar .container-fluid {
                padding: 0 1rem;
            }
            ";
            break;

        case 'corporate':
            $css .= "
            .modern-sidebar {
                background: linear-gradient(180deg, var(--bg-primary), var(--bg-secondary));
                border-right: 3px solid var(--primary-color);
            }
            .navbar {
                background: linear-gradient(90deg, var(--bg-primary), var(--bg-secondary));
                border-bottom: 3px solid var(--primary-color);
            }
            .sidebar-header {
                background: var(--primary-color);
                color: white;
            }
            ";
            break;

        case 'futuristic':
            $css .= "
            .modern-sidebar {
                background: linear-gradient(135deg, rgba(0,0,0,0.9), rgba(0,255,136,0.1));
                border-right: 2px solid var(--primary-color);
                box-shadow: 0 0 20px rgba(0,255,136,0.3);
            }
            .navbar {
                background: linear-gradient(90deg, rgba(0,0,0,0.9), rgba(0,255,136,0.1));
                border-bottom: 2px solid var(--primary-color);
                box-shadow: 0 0 20px rgba(0,255,136,0.3);
            }
            .menu-item {
                border-left: 3px solid transparent;
                transition: all 0.3s ease;
            }
            .menu-item:hover {
                border-left-color: var(--primary-color);
                box-shadow: 0 0 10px rgba(0,255,136,0.5);
            }
            ";
            break;
    }

    return $css;
}
?>
