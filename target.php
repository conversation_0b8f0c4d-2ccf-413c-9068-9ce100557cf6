<?php
// <PERSON><PERSON> session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Include file yang diperlukan
require_once 'includes/config/database.php';
require_once 'includes/helpers/function_check.php';
require_once 'includes/helpers/functions.php';

// Cek apakah user sudah login
if (!isLoggedIn()) {
    setFlashMessage('danger', 'Silakan login terlebih dahulu');
    redirect('login.php');
}

// Dapatkan data user yang sedang login
$currentUser = getCurrentUser();
if (!$currentUser) {
    session_destroy();
    setFlashMessage('danger', 'Sesi tidak valid. Silakan login kembali.');
    redirect('login.php');
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        try {
            switch ($_POST['action']) {
                case 'add':
                    $stmt = $pdo->prepare("
                        INSERT INTO target (user_id, nama, jumlah_target, tanggal_mulai, tanggal_selesai)
                        VALUES (?, ?, ?, ?, ?)
                    ");
                    $stmt->execute([
                        $currentUser['id'],
                        $_POST['nama'],
                        $_POST['jumlah_target'],
                        $_POST['tanggal_mulai'],
                        $_POST['tanggal_selesai']
                    ]);
                    setFlashMessage('success', 'Target berhasil ditambahkan');
                    break;

                case 'update_status':
                    $stmt = $pdo->prepare("
                        UPDATE target 
                        SET status = ? 
                        WHERE id = ? AND user_id = ?
                    ");
                    $stmt->execute([$_POST['status'], $_POST['id'], $currentUser['id']]);
                    setFlashMessage('success', 'Status target berhasil diperbarui');
                    break;

                case 'update_progress':
                    $stmt = $pdo->prepare("
                        UPDATE target 
                        SET jumlah_terkumpul = ? 
                        WHERE id = ? AND user_id = ?
                    ");
                    $stmt->execute([$_POST['jumlah_terkumpul'], $_POST['id'], $currentUser['id']]);
                    setFlashMessage('success', 'Progress target berhasil diperbarui');
                    break;

                case 'delete':
                    $stmt = $pdo->prepare("DELETE FROM target WHERE id = ? AND user_id = ?");
                    $stmt->execute([$_POST['id'], $currentUser['id']]);
                    setFlashMessage('success', 'Target berhasil dihapus');
                    break;
            }
        } catch (PDOException $e) {
            error_log("Target Error: " . $e->getMessage());
            setFlashMessage('danger', 'Terjadi kesalahan. Silakan coba lagi.');
        }
        redirect('target.php');
    }
}

// Get all targets
try {
    $stmt = $pdo->prepare("
        SELECT * FROM target 
        WHERE user_id = ?
        ORDER BY 
            CASE 
                WHEN status = 'aktif' THEN 1
                WHEN status = 'selesai' THEN 2
                ELSE 3
            END,
            tanggal_selesai ASC
    ");
    $stmt->execute([$currentUser['id']]);
    $targets = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    error_log("Target Error: " . $e->getMessage());
    setFlashMessage('danger', 'Terjadi kesalahan saat mengambil data target.');
    $targets = [];
}

// Set page title
$page_title = 'Target Keuangan';

// Include header and sidebar
require_once 'includes/views/layouts/header.php';
require_once 'includes/views/layouts/sidebar.php';
?>

<div class="modern-layout">
    <div class="modern-container">
        <!-- Modern Page Header -->
        <div class="modern-page-header modern-flex modern-justify-between modern-items-center modern-mb-xl">
            <div>
                <h1 class="modern-page-title modern-mb-0">Target Keuangan</h1>
                <p class="modern-page-subtitle">Kelola dan pantau target keuangan Anda dengan mudah</p>
            </div>
            <div class="modern-page-actions">
                <button type="button" class="modern-btn modern-btn-primary" data-bs-toggle="modal" data-bs-target="#addTargetModal">
                    <i class="fas fa-plus"></i>
                    Tambah Target
                </button>
            </div>
        </div>

        <!-- Flash Messages -->
        <?php if ($flash = getFlashMessage()): ?>
        <div class="modern-alert modern-alert-<?= $flash['type'] ?> modern-mb-lg">
            <div class="modern-alert-content">
                <i class="fas fa-<?= $flash['type'] === 'success' ? 'check-circle' : 'exclamation-triangle' ?> modern-alert-icon"></i>
                <div class="modern-alert-message"><?= $flash['message'] ?></div>
            </div>
            <button type="button" class="modern-alert-close" onclick="this.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <?php endif; ?>

        <div class="modern-grid modern-grid-cols-3 modern-gap-lg">
            <?php if (empty($targets)): ?>
            <div class="modern-grid-cols-1" style="grid-column: 1 / -1;">
                <div class="modern-empty-state">
                    <div class="modern-empty-icon">
                        <i class="fas fa-bullseye"></i>
                    </div>
                    <div class="modern-empty-content">
                        <h6 class="modern-empty-title">Belum Ada Target Keuangan</h6>
                        <p class="modern-empty-text">Mulai merencanakan masa depan dengan membuat target keuangan pertama Anda</p>
                        <button type="button" class="modern-btn modern-btn-primary" data-bs-toggle="modal" data-bs-target="#addTargetModal">
                            <i class="fas fa-plus"></i>
                            Tambah Target Pertama
                        </button>
                    </div>
                </div>
            </div>
            <?php else: ?>
            <?php foreach ($targets as $target):
                $progress = ($target['jumlah_terkumpul'] / $target['jumlah_target']) * 100;
                $progress = min($progress, 100);
                $status_class = [
                    'aktif' => 'success',
                    'selesai' => 'primary',
                    'batal' => 'danger'
                ][$target['status']];
            ?>
            <div class="modern-target-card">
                <div class="modern-target-header">
                    <h5 class="modern-target-title"><?= htmlspecialchars($target['nama']) ?></h5>
                    <div class="modern-badge modern-badge-<?= $status_class ?>">
                        <?php if ($target['status'] === 'aktif'): ?>
                            <i class="fas fa-play"></i>
                        <?php elseif ($target['status'] === 'selesai'): ?>
                            <i class="fas fa-check"></i>
                        <?php else: ?>
                            <i class="fas fa-times"></i>
                        <?php endif; ?>
                        <?= ucfirst($target['status']) ?>
                    </div>
                </div>

                <div class="modern-target-progress">
                    <div class="modern-target-amounts">
                        <div class="modern-target-amount">
                            <span class="modern-target-label">Target</span>
                            <span class="modern-target-value"><?= formatRupiah($target['jumlah_target']) ?></span>
                        </div>
                        <div class="modern-target-amount">
                            <span class="modern-target-label">Terkumpul</span>
                            <span class="modern-target-value modern-text-success"><?= formatRupiah($target['jumlah_terkumpul']) ?></span>
                        </div>
                    </div>

                    <div class="modern-progress-container">
                        <div class="modern-progress-bar">
                            <div class="modern-progress-fill modern-progress-<?= $progress >= 100 ? 'complete' : 'active' ?>"
                                 style="width: <?= $progress ?>%">
                            </div>
                        </div>
                        <div class="modern-progress-text"><?= number_format($progress, 1) ?>%</div>
                    </div>
                </div>

                <div class="modern-target-period">
                    <div class="modern-target-period-header">
                        <i class="fas fa-calendar-alt modern-text-primary"></i>
                        <span>Periode Target</span>
                    </div>
                    <div class="modern-target-period-dates">
                        <?= formatTanggal($target['tanggal_mulai']) ?> - <?= formatTanggal($target['tanggal_selesai']) ?>
                    </div>
                </div>

                <?php if ($target['status'] === 'aktif'): ?>
                <div class="modern-target-actions">
                    <button type="button"
                            class="modern-btn modern-btn-success modern-flex-1"
                            data-bs-toggle="modal"
                            data-bs-target="#updateProgressModal"
                            data-id="<?= $target['id'] ?>"
                            data-nama="<?= htmlspecialchars($target['nama']) ?>"
                            data-terkumpul="<?= $target['jumlah_terkumpul'] ?>">
                        <i class="fas fa-sync-alt"></i>
                        Update Progress
                    </button>
                    <button type="button"
                            class="modern-btn modern-btn-danger"
                            data-bs-toggle="modal"
                            data-bs-target="#deleteTargetModal"
                            data-id="<?= $target['id'] ?>"
                            data-nama="<?= htmlspecialchars($target['nama']) ?>">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
                <?php endif; ?>
            </div>
            <?php endforeach; ?>
        <?php endif; ?>
        </div>
    </div>
</div>

<!-- Modern Add Target Modal -->
<div class="modal fade" id="addTargetModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modern-modal-content">
            <div class="modern-modal-header">
                <h5 class="modern-modal-title">
                    <i class="fas fa-plus modern-text-primary modern-mr-sm"></i>
                    Tambah Target Baru
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="" method="POST" class="needs-validation" novalidate>
                <div class="modern-modal-body">
                    <input type="hidden" name="action" value="add">

                    <div class="modern-form-group">
                        <label class="modern-form-label">
                            <i class="fas fa-bullseye modern-text-primary"></i>
                            Nama Target <span class="modern-text-danger">*</span>
                        </label>
                        <input type="text" class="modern-form-control" name="nama" required placeholder="Contoh: Dana Darurat, Liburan, dll">
                        <div class="invalid-feedback">Nama target harus diisi</div>
                    </div>

                    <div class="modern-form-group">
                        <label class="modern-form-label">
                            <i class="fas fa-money-bill modern-text-primary"></i>
                            Jumlah Target <span class="modern-text-danger">*</span>
                        </label>
                        <div class="modern-input-group">
                            <span class="modern-input-group-text">Rp</span>
                            <input type="number" class="modern-form-control" name="jumlah_target" required min="0" placeholder="0">
                        </div>
                        <div class="invalid-feedback">Jumlah target harus diisi dan lebih dari 0</div>
                    </div>

                    <div class="modern-grid modern-grid-cols-2 modern-gap-md">
                        <div class="modern-form-group">
                            <label class="modern-form-label">
                                <i class="fas fa-calendar-alt modern-text-primary"></i>
                                Tanggal Mulai <span class="modern-text-danger">*</span>
                            </label>
                            <input type="date" class="modern-form-control" name="tanggal_mulai" required>
                            <div class="invalid-feedback">Tanggal mulai harus diisi</div>
                        </div>

                        <div class="modern-form-group">
                            <label class="modern-form-label">
                                <i class="fas fa-calendar-check modern-text-primary"></i>
                                Tanggal Selesai <span class="modern-text-danger">*</span>
                            </label>
                            <input type="date" class="modern-form-control" name="tanggal_selesai" required>
                            <div class="invalid-feedback">Tanggal selesai harus diisi</div>
                        </div>
                    </div>
                </div>
                <div class="modern-modal-footer">
                    <button type="button" class="modern-btn modern-btn-light" data-bs-dismiss="modal">
                        <i class="fas fa-times"></i>
                        Batal
                    </button>
                    <button type="submit" class="modern-btn modern-btn-primary">
                        <i class="fas fa-save"></i>
                        Simpan Target
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Update Progress Modal -->
<div class="modal fade" id="updateProgressModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content border-0 shadow">
            <div class="modal-header border-0">
                <h5 class="modal-title">Update Progress</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="" method="POST" class="needs-validation" novalidate>
                <div class="modal-body">
                    <input type="hidden" name="action" value="update_progress">
                    <input type="hidden" name="id" id="updateProgressId">
                    
                    <div class="mb-3">
                        <label class="form-label">Target</label>
                        <input type="text" class="form-control" id="updateProgressNama" readonly>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Jumlah Terkumpul</label>
                        <div class="input-group">
                            <span class="input-group-text">Rp</span>
                            <input type="number" class="form-control" name="jumlah_terkumpul" id="updateProgressTerkumpul" required min="0">
                        </div>
                        <div class="invalid-feedback">Jumlah terkumpul harus diisi dan lebih dari 0</div>
                    </div>
                </div>
                <div class="modal-footer border-0">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">Update</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Target Modal -->
<div class="modal fade" id="deleteTargetModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content border-0 shadow">
            <div class="modal-header border-0">
                <h5 class="modal-title">Hapus Target</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="" method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="delete">
                    <input type="hidden" name="id" id="deleteTargetId">
                    <div class="text-center mb-4">
                        <i class="fas fa-exclamation-triangle text-warning fa-3x mb-3"></i>
                        <p class="mb-0">Apakah Anda yakin ingin menghapus target "<span id="deleteTargetNama" class="fw-bold"></span>"?</p>
                    </div>
                </div>
                <div class="modal-footer border-0">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-danger">Hapus</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php include 'includes/views/layouts/footer.php'; ?>

<style>
.card {
    transition: transform 0.2s ease-in-out;
    background: #fff;
}

.card:hover {
    transform: translateY(-5px);
}

.progress {
    background-color: #f0f0f0;
    border-radius: 10px;
}

.progress-bar {
    border-radius: 10px;
}

.btn {
    padding: 0.5rem 1rem;
    border-radius: 8px;
    transition: all 0.2s ease-in-out;
}

.btn:hover {
    transform: translateY(-2px);
}

.modal-content {
    border-radius: 15px;
}

.form-control {
    border-radius: 8px;
    padding: 0.6rem 1rem;
    border: 1px solid #e0e0e0;
}

.form-control:focus {
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.15);
    border-color: #86b7fe;
}

.input-group-text {
    border-radius: 8px 0 0 8px;
    background-color: #f8f9fa;
    border: 1px solid #e0e0e0;
}

.badge {
    font-weight: 500;
    padding: 0.5rem 1rem;
}

.bg-success {
    background-color: #198754 !important;
}

.bg-primary {
    background-color: #0d6efd !important;
}

.bg-danger {
    background-color: #dc3545 !important;
}

.btn-success {
    background-color: #198754;
    border-color: #198754;
}

.btn-primary {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.btn-danger {
    background-color: #dc3545;
    border-color: #dc3545;
}

.btn-light {
    background-color: #f8f9fa;
    border-color: #e0e0e0;
}

.btn-outline-danger {
    color: #dc3545;
    border-color: #dc3545;
}

.btn-outline-danger:hover {
    background-color: #dc3545;
    color: #fff;
}

.text-muted {
    color: #6c757d !important;
}

.text-gray-800 {
    color: #212529 !important;
}

.shadow-sm {
    box-shadow: 0 .125rem .25rem rgba(0,0,0,.075) !important;
}

.shadow {
    box-shadow: 0 .5rem 1rem rgba(0,0,0,.15) !important;
}
</style>

<script>
    // Update Progress Modal
    document.getElementById('updateProgressModal').addEventListener('show.bs.modal', function (event) {
        const button = event.relatedTarget;
        const id = button.getAttribute('data-id');
        const nama = button.getAttribute('data-nama');
        const terkumpul = button.getAttribute('data-terkumpul');

        document.getElementById('updateProgressId').value = id;
        document.getElementById('updateProgressNama').value = nama;
        document.getElementById('updateProgressTerkumpul').value = terkumpul;
    });

    // Delete Target Modal
    document.getElementById('deleteTargetModal').addEventListener('show.bs.modal', function (event) {
        const button = event.relatedTarget;
        const id = button.getAttribute('data-id');
        const nama = button.getAttribute('data-nama');

        document.getElementById('deleteTargetId').value = id;
        document.getElementById('deleteTargetNama').textContent = nama;
    });
</script> 