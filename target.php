<?php
// <PERSON><PERSON> session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Include file yang diperlukan
require_once 'includes/config/database.php';
require_once 'includes/helpers/functions.php';

// Cek apakah user sudah login
if (!isLoggedIn()) {
    setFlashMessage('danger', 'Silakan login terlebih dahulu');
    redirect('login.php');
}

// Dapatkan data user yang sedang login
$currentUser = getCurrentUser();
if (!$currentUser) {
    session_destroy();
    setFlashMessage('danger', 'Sesi tidak valid. Silakan login kembali.');
    redirect('login.php');
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        try {
            switch ($_POST['action']) {
                case 'add':
                    $stmt = $pdo->prepare("
                        INSERT INTO target (user_id, nama, jumlah_target, tanggal_mulai, tanggal_selesai)
                        VALUES (?, ?, ?, ?, ?)
                    ");
                    $stmt->execute([
                        $currentUser['id'],
                        $_POST['nama'],
                        $_POST['jumlah_target'],
                        $_POST['tanggal_mulai'],
                        $_POST['tanggal_selesai']
                    ]);
                    setFlashMessage('success', 'Target berhasil ditambahkan');
                    break;

                case 'update_status':
                    $stmt = $pdo->prepare("
                        UPDATE target 
                        SET status = ? 
                        WHERE id = ? AND user_id = ?
                    ");
                    $stmt->execute([$_POST['status'], $_POST['id'], $currentUser['id']]);
                    setFlashMessage('success', 'Status target berhasil diperbarui');
                    break;

                case 'update_progress':
                    $stmt = $pdo->prepare("
                        UPDATE target 
                        SET jumlah_terkumpul = ? 
                        WHERE id = ? AND user_id = ?
                    ");
                    $stmt->execute([$_POST['jumlah_terkumpul'], $_POST['id'], $currentUser['id']]);
                    setFlashMessage('success', 'Progress target berhasil diperbarui');
                    break;

                case 'delete':
                    $stmt = $pdo->prepare("DELETE FROM target WHERE id = ? AND user_id = ?");
                    $stmt->execute([$_POST['id'], $currentUser['id']]);
                    setFlashMessage('success', 'Target berhasil dihapus');
                    break;
            }
        } catch (PDOException $e) {
            error_log("Target Error: " . $e->getMessage());
            setFlashMessage('danger', 'Terjadi kesalahan. Silakan coba lagi.');
        }
        redirect('target.php');
    }
}

// Get all targets
try {
    $stmt = $pdo->prepare("
        SELECT * FROM target 
        WHERE user_id = ?
        ORDER BY 
            CASE 
                WHEN status = 'aktif' THEN 1
                WHEN status = 'selesai' THEN 2
                ELSE 3
            END,
            tanggal_selesai ASC
    ");
    $stmt->execute([$currentUser['id']]);
    $targets = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    error_log("Target Error: " . $e->getMessage());
    setFlashMessage('danger', 'Terjadi kesalahan saat mengambil data target.');
    $targets = [];
}

// Set page title
$page_title = 'Target Keuangan';

// Include header
include 'includes/views/layouts/header.php';
?>

<!-- Main Content -->
<div class="container-fluid px-4 py-5">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-1 text-gray-800">Target Keuangan</h1>
            <p class="text-muted">Kelola target keuangan Anda</p>
        </div>
        <button type="button" class="btn btn-primary d-flex align-items-center gap-2" data-bs-toggle="modal" data-bs-target="#addTargetModal">
            <i class="fas fa-plus"></i>
            <span>Tambah Target</span>
        </button>
    </div>

    <!-- Flash Messages -->
    <?php if ($flash = getFlashMessage()): ?>
    <div class="alert alert-<?= $flash['type'] ?> alert-dismissible fade show" role="alert">
        <?= $flash['message'] ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    <?php endif; ?>

    <div class="row g-4">
        <?php if (empty($targets)): ?>
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center py-5">
                    <i class="fas fa-bullseye fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">Belum ada target keuangan</h5>
                    <p class="text-muted mb-0">Silakan tambahkan target baru untuk memulai</p>
                </div>
            </div>
        </div>
        <?php else: ?>
            <?php foreach ($targets as $target): 
                $progress = ($target['jumlah_terkumpul'] / $target['jumlah_target']) * 100;
                $progress = min($progress, 100);
                $status_class = [
                    'aktif' => 'bg-success',
                    'selesai' => 'bg-primary',
                    'batal' => 'bg-danger'
                ][$target['status']];
            ?>
            <div class="col-md-6 col-lg-4">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start mb-3">
                            <h5 class="card-title mb-0"><?= htmlspecialchars($target['nama']) ?></h5>
                            <span class="badge <?= $status_class ?> rounded-pill px-3 py-2">
                                <?= ucfirst($target['status']) ?>
                            </span>
                        </div>
                        
                        <div class="mb-4">
                            <div class="d-flex justify-content-between mb-2">
                                <small class="text-muted">Target</small>
                                <small class="text-muted fw-bold">
                                    <?= formatCurrency($target['jumlah_target']) ?>
                                </small>
                            </div>
                            <div class="progress mb-2" style="height: 8px;">
                                <div class="progress-bar <?= $progress >= 100 ? 'bg-success' : 'bg-primary' ?>" 
                                     role="progressbar" 
                                     style="width: <?= $progress ?>%">
                                </div>
                            </div>
                            <div class="d-flex justify-content-between">
                                <small class="text-muted">Terkumpul</small>
                                <small class="text-muted fw-bold">
                                    <?= formatCurrency($target['jumlah_terkumpul']) ?>
                                </small>
                            </div>
                        </div>

                        <div class="mb-4">
                            <div class="d-flex align-items-center gap-2 text-muted mb-2">
                                <i class="fas fa-calendar-alt"></i>
                                <small>Periode</small>
                            </div>
                            <small class="d-block">
                                <?= formatDate($target['tanggal_mulai']) ?> - 
                                <?= formatDate($target['tanggal_selesai']) ?>
                            </small>
                        </div>

                        <?php if ($target['status'] === 'aktif'): ?>
                        <div class="d-flex gap-2 mt-auto">
                            <button type="button" 
                                    class="btn btn-success flex-grow-1 d-flex align-items-center justify-content-center gap-2"
                                    data-bs-toggle="modal" 
                                    data-bs-target="#updateProgressModal"
                                    data-id="<?= $target['id'] ?>"
                                    data-nama="<?= htmlspecialchars($target['nama']) ?>"
                                    data-terkumpul="<?= $target['jumlah_terkumpul'] ?>">
                                <i class="fas fa-sync-alt"></i>
                                <span>Update Progress</span>
                            </button>
                            <button type="button" 
                                    class="btn btn-outline-danger"
                                    data-bs-toggle="modal" 
                                    data-bs-target="#deleteTargetModal"
                                    data-id="<?= $target['id'] ?>"
                                    data-nama="<?= htmlspecialchars($target['nama']) ?>">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>
</div>

<!-- Add Target Modal -->
<div class="modal fade" id="addTargetModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content border-0 shadow">
            <div class="modal-header border-0">
                <h5 class="modal-title">Tambah Target Baru</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="" method="POST" class="needs-validation" novalidate>
                <div class="modal-body">
                    <input type="hidden" name="action" value="add">
                    
                    <div class="mb-3">
                        <label class="form-label">Nama Target</label>
                        <input type="text" class="form-control" name="nama" required>
                        <div class="invalid-feedback">Nama target harus diisi</div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Jumlah Target</label>
                        <div class="input-group">
                            <span class="input-group-text">Rp</span>
                            <input type="number" class="form-control" name="jumlah_target" required min="0">
                        </div>
                        <div class="invalid-feedback">Jumlah target harus diisi dan lebih dari 0</div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Tanggal Mulai</label>
                            <input type="date" class="form-control" name="tanggal_mulai" required>
                            <div class="invalid-feedback">Tanggal mulai harus diisi</div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Tanggal Selesai</label>
                            <input type="date" class="form-control" name="tanggal_selesai" required>
                            <div class="invalid-feedback">Tanggal selesai harus diisi</div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer border-0">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">Simpan</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Update Progress Modal -->
<div class="modal fade" id="updateProgressModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content border-0 shadow">
            <div class="modal-header border-0">
                <h5 class="modal-title">Update Progress</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="" method="POST" class="needs-validation" novalidate>
                <div class="modal-body">
                    <input type="hidden" name="action" value="update_progress">
                    <input type="hidden" name="id" id="updateProgressId">
                    
                    <div class="mb-3">
                        <label class="form-label">Target</label>
                        <input type="text" class="form-control" id="updateProgressNama" readonly>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Jumlah Terkumpul</label>
                        <div class="input-group">
                            <span class="input-group-text">Rp</span>
                            <input type="number" class="form-control" name="jumlah_terkumpul" id="updateProgressTerkumpul" required min="0">
                        </div>
                        <div class="invalid-feedback">Jumlah terkumpul harus diisi dan lebih dari 0</div>
                    </div>
                </div>
                <div class="modal-footer border-0">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">Update</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Target Modal -->
<div class="modal fade" id="deleteTargetModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content border-0 shadow">
            <div class="modal-header border-0">
                <h5 class="modal-title">Hapus Target</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="" method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="delete">
                    <input type="hidden" name="id" id="deleteTargetId">
                    <div class="text-center mb-4">
                        <i class="fas fa-exclamation-triangle text-warning fa-3x mb-3"></i>
                        <p class="mb-0">Apakah Anda yakin ingin menghapus target "<span id="deleteTargetNama" class="fw-bold"></span>"?</p>
                    </div>
                </div>
                <div class="modal-footer border-0">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-danger">Hapus</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php include 'includes/views/layouts/footer.php'; ?>

<style>
.card {
    transition: transform 0.2s ease-in-out;
    background: #fff;
}

.card:hover {
    transform: translateY(-5px);
}

.progress {
    background-color: #f0f0f0;
    border-radius: 10px;
}

.progress-bar {
    border-radius: 10px;
}

.btn {
    padding: 0.5rem 1rem;
    border-radius: 8px;
    transition: all 0.2s ease-in-out;
}

.btn:hover {
    transform: translateY(-2px);
}

.modal-content {
    border-radius: 15px;
}

.form-control {
    border-radius: 8px;
    padding: 0.6rem 1rem;
    border: 1px solid #e0e0e0;
}

.form-control:focus {
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.15);
    border-color: #86b7fe;
}

.input-group-text {
    border-radius: 8px 0 0 8px;
    background-color: #f8f9fa;
    border: 1px solid #e0e0e0;
}

.badge {
    font-weight: 500;
    padding: 0.5rem 1rem;
}

.bg-success {
    background-color: #198754 !important;
}

.bg-primary {
    background-color: #0d6efd !important;
}

.bg-danger {
    background-color: #dc3545 !important;
}

.btn-success {
    background-color: #198754;
    border-color: #198754;
}

.btn-primary {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.btn-danger {
    background-color: #dc3545;
    border-color: #dc3545;
}

.btn-light {
    background-color: #f8f9fa;
    border-color: #e0e0e0;
}

.btn-outline-danger {
    color: #dc3545;
    border-color: #dc3545;
}

.btn-outline-danger:hover {
    background-color: #dc3545;
    color: #fff;
}

.text-muted {
    color: #6c757d !important;
}

.text-gray-800 {
    color: #212529 !important;
}

.shadow-sm {
    box-shadow: 0 .125rem .25rem rgba(0,0,0,.075) !important;
}

.shadow {
    box-shadow: 0 .5rem 1rem rgba(0,0,0,.15) !important;
}
</style>

<script>
    // Update Progress Modal
    document.getElementById('updateProgressModal').addEventListener('show.bs.modal', function (event) {
        const button = event.relatedTarget;
        const id = button.getAttribute('data-id');
        const nama = button.getAttribute('data-nama');
        const terkumpul = button.getAttribute('data-terkumpul');

        document.getElementById('updateProgressId').value = id;
        document.getElementById('updateProgressNama').value = nama;
        document.getElementById('updateProgressTerkumpul').value = terkumpul;
    });

    // Delete Target Modal
    document.getElementById('deleteTargetModal').addEventListener('show.bs.modal', function (event) {
        const button = event.relatedTarget;
        const id = button.getAttribute('data-id');
        const nama = button.getAttribute('data-nama');

        document.getElementById('deleteTargetId').value = id;
        document.getElementById('deleteTargetNama').textContent = nama;
    });
</script> 