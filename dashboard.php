<?php
require_once 'includes/config/database.php';
require_once 'includes/helpers/functions.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isLoggedIn()) {
    setFlashMessage('danger', 'Silakan login terlebih dahulu.');
    redirect('login.php');
}

// Get current user data
$currentUser = getCurrentUser();
if (!$currentUser) {
    // Session exists but user not found in database - clear session
    session_unset();
    session_destroy();
    setFlashMessage('danger', 'Sesi tidak valid. Silakan login kembali.');
    redirect('login.php');
}

$currentPage = 'dashboard';
$pageTitle = 'Dashboard';

// Get dashboard statistics
try {
    // Get current month and year
    $currentMonth = date('m');
    $currentYear = date('Y');

    // Total balance (all time)
    $stmt = $pdo->prepare("
        SELECT
            COALESCE(SUM(CASE WHEN k.tipe = 'pemasukan' THEN t.jumlah ELSE 0 END), 0) as total_pemasukan,
            COALESCE(SUM(CASE WHEN k.tipe = 'pengeluaran' THEN t.jumlah ELSE 0 END), 0) as total_pengeluaran
        FROM transaksi t
        LEFT JOIN kategori k ON t.kategori_id = k.id
        WHERE t.user_id = ?
    ");
    $stmt->execute([$currentUser['id']]);
    $totalStats = $stmt->fetch();

    $totalSaldo = $totalStats['total_pemasukan'] - $totalStats['total_pengeluaran'];

    // Monthly statistics (current month)
    $stmt = $pdo->prepare("
        SELECT
            COALESCE(SUM(CASE WHEN k.tipe = 'pemasukan' THEN t.jumlah ELSE 0 END), 0) as pemasukan_bulan,
            COALESCE(SUM(CASE WHEN k.tipe = 'pengeluaran' THEN t.jumlah ELSE 0 END), 0) as pengeluaran_bulan,
            COUNT(*) as total_transaksi_bulan
        FROM transaksi t
        LEFT JOIN kategori k ON t.kategori_id = k.id
        WHERE t.user_id = ? AND MONTH(t.tanggal) = ? AND YEAR(t.tanggal) = ?
    ");
    $stmt->execute([$currentUser['id'], $currentMonth, $currentYear]);
    $monthlyStats = $stmt->fetch();

    // Recent transactions (last 5)
    $stmt = $pdo->prepare("
        SELECT
            t.*,
            k.nama as kategori_nama,
            k.tipe
        FROM transaksi t
        LEFT JOIN kategori k ON t.kategori_id = k.id
        WHERE t.user_id = ?
        ORDER BY t.tanggal DESC, t.created_at DESC
        LIMIT 5
    ");
    $stmt->execute([$currentUser['id']]);
    $recentTransactions = $stmt->fetchAll();

    // Active targets
    $stmt = $pdo->prepare("
        SELECT * FROM target
        WHERE user_id = ? AND status = 'aktif'
        ORDER BY tanggal_selesai ASC
        LIMIT 3
    ");
    $stmt->execute([$currentUser['id']]);
    $activeTargets = $stmt->fetchAll();

    // Top categories this month
    $stmt = $pdo->prepare("
        SELECT
            k.nama as kategori_nama,
            k.tipe,
            SUM(t.jumlah) as total_jumlah,
            COUNT(t.id) as jumlah_transaksi
        FROM transaksi t
        LEFT JOIN kategori k ON t.kategori_id = k.id
        WHERE t.user_id = ? AND MONTH(t.tanggal) = ? AND YEAR(t.tanggal) = ?
        GROUP BY t.kategori_id, k.nama, k.tipe
        ORDER BY total_jumlah DESC
        LIMIT 5
    ");
    $stmt->execute([$currentUser['id'], $currentMonth, $currentYear]);
    $topCategories = $stmt->fetchAll();

} catch (PDOException $e) {
    error_log("Dashboard Error: " . $e->getMessage());
    setFlashMessage('danger', 'Terjadi kesalahan saat mengambil data dashboard.');
}

include 'includes/views/layouts/header.php';
?>

<div class="container-fluid py-4">
    <!-- Welcome Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm bg-gradient-primary text-white">
                <div class="card-body p-4">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h2 class="mb-2">Selamat datang, <?= htmlspecialchars($currentUser['nama']) ?>!</h2>
                            <p class="mb-0 opacity-75">Kelola keuangan Anda dengan mudah dan efisien</p>
                        </div>
                        <div class="col-md-4 text-end">
                            <div class="d-flex justify-content-end">
                                <div class="bg-white bg-opacity-20 p-3 rounded-circle">
                                    <i class="fas fa-wallet fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Statistics Cards -->
    <div class="row g-3 mb-4">
        <div class="col-md-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted mb-1">Total Saldo</h6>
                            <h3 class="mb-0 <?= $totalSaldo >= 0 ? 'text-success' : 'text-danger' ?>">
                                <?= formatRupiah($totalSaldo) ?>
                            </h3>
                        </div>
                        <div class="bg-primary bg-opacity-10 p-3 rounded">
                            <i class="fas fa-wallet text-primary"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted mb-1">Pemasukan Bulan Ini</h6>
                            <h3 class="mb-0 text-success"><?= formatRupiah($monthlyStats['pemasukan_bulan'] ?? 0) ?></h3>
                        </div>
                        <div class="bg-success bg-opacity-10 p-3 rounded">
                            <i class="fas fa-arrow-up text-success"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted mb-1">Pengeluaran Bulan Ini</h6>
                            <h3 class="mb-0 text-danger"><?= formatRupiah($monthlyStats['pengeluaran_bulan'] ?? 0) ?></h3>
                        </div>
                        <div class="bg-danger bg-opacity-10 p-3 rounded">
                            <i class="fas fa-arrow-down text-danger"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted mb-1">Transaksi Bulan Ini</h6>
                            <h3 class="mb-0 text-info"><?= number_format($monthlyStats['total_transaksi_bulan'] ?? 0) ?></h3>
                        </div>
                        <div class="bg-info bg-opacity-10 p-3 rounded">
                            <i class="fas fa-exchange-alt text-info"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0">
                    <h5 class="card-title mb-0">Aksi Cepat</h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-3">
                            <a href="transaksi.php" class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3">
                                <i class="fas fa-plus-circle fa-2x mb-2"></i>
                                <span>Tambah Transaksi</span>
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="kategori.php" class="btn btn-outline-success w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3">
                                <i class="fas fa-tags fa-2x mb-2"></i>
                                <span>Kelola Kategori</span>
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="target.php" class="btn btn-outline-warning w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3">
                                <i class="fas fa-bullseye fa-2x mb-2"></i>
                                <span>Target Keuangan</span>
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="laporan.php" class="btn btn-outline-info w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3">
                                <i class="fas fa-chart-bar fa-2x mb-2"></i>
                                <span>Lihat Laporan</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row g-4">
        <!-- Recent Transactions -->
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-white border-0 d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">Transaksi Terbaru</h5>
                    <a href="transaksi.php" class="btn btn-sm btn-outline-primary">Lihat Semua</a>
                </div>
                <div class="card-body">
                    <?php if (empty($recentTransactions)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <p class="text-muted mb-0">Belum ada transaksi</p>
                        <a href="transaksi.php" class="btn btn-primary mt-2">Tambah Transaksi Pertama</a>
                    </div>
                    <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover align-middle">
                            <tbody>
                                <?php foreach ($recentTransactions as $t): ?>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="bg-<?= $t['tipe'] === 'pemasukan' ? 'success' : 'danger' ?> bg-opacity-10 p-2 rounded me-3">
                                                <i class="fas fa-<?= $t['tipe'] === 'pemasukan' ? 'arrow-up' : 'arrow-down' ?> text-<?= $t['tipe'] === 'pemasukan' ? 'success' : 'danger' ?>"></i>
                                            </div>
                                            <div>
                                                <div class="fw-medium"><?= htmlspecialchars($t['keterangan']) ?></div>
                                                <small class="text-muted"><?= htmlspecialchars($t['kategori_nama']) ?> • <?= formatTanggal($t['tanggal']) ?></small>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="text-end">
                                        <span class="fw-bold text-<?= $t['tipe'] === 'pemasukan' ? 'success' : 'danger' ?>">
                                            <?= $t['tipe'] === 'pemasukan' ? '+' : '-' ?><?= formatRupiah($t['jumlah']) ?>
                                        </span>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Sidebar Content -->
        <div class="col-lg-4">
            <!-- Active Targets -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white border-0 d-flex justify-content-between align-items-center">
                    <h6 class="card-title mb-0">Target Aktif</h6>
                    <a href="target.php" class="btn btn-sm btn-outline-primary">Kelola</a>
                </div>
                <div class="card-body">
                    <?php if (empty($activeTargets)): ?>
                    <div class="text-center py-3">
                        <i class="fas fa-bullseye fa-2x text-muted mb-2"></i>
                        <p class="text-muted mb-0 small">Belum ada target aktif</p>
                    </div>
                    <?php else: ?>
                    <?php foreach ($activeTargets as $target): ?>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center mb-1">
                            <span class="fw-medium small"><?= htmlspecialchars($target['nama']) ?></span>
                            <span class="text-muted small"><?= formatRupiah($target['jumlah_target']) ?></span>
                        </div>
                        <div class="progress" style="height: 6px;">
                            <div class="progress-bar bg-warning" style="width: 0%"></div>
                        </div>
                        <small class="text-muted">Target: <?= formatTanggal($target['tanggal_selesai']) ?></small>
                    </div>
                    <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Top Categories -->
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0">
                    <h6 class="card-title mb-0">Kategori Teratas Bulan Ini</h6>
                </div>
                <div class="card-body">
                    <?php if (empty($topCategories)): ?>
                    <div class="text-center py-3">
                        <i class="fas fa-tags fa-2x text-muted mb-2"></i>
                        <p class="text-muted mb-0 small">Belum ada transaksi bulan ini</p>
                    </div>
                    <?php else: ?>
                    <?php foreach ($topCategories as $category): ?>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <div class="d-flex align-items-center">
                            <span class="badge bg-<?= $category['tipe'] === 'pemasukan' ? 'success' : 'danger' ?> bg-opacity-10 text-<?= $category['tipe'] === 'pemasukan' ? 'success' : 'danger' ?> me-2">
                                <i class="fas fa-<?= $category['tipe'] === 'pemasukan' ? 'arrow-up' : 'arrow-down' ?>"></i>
                            </span>
                            <span class="small"><?= htmlspecialchars($category['kategori_nama']) ?></span>
                        </div>
                        <span class="fw-medium small"><?= formatRupiah($category['total_jumlah']) ?></span>
                    </div>
                    <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/views/layouts/footer.php'; ?>

<style>
/* Dashboard Specific Styles */
.bg-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.card {
    transition: all 0.3s ease;
    border-radius: 12px;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

.btn {
    border-radius: 8px;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
}

.progress {
    border-radius: 10px;
}

.table td {
    border: none;
    padding: 1rem 0.75rem;
}

.table tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.02);
}

/* Quick Action Cards */
.btn-outline-primary:hover,
.btn-outline-success:hover,
.btn-outline-warning:hover,
.btn-outline-info:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Statistics Cards Animation */
.card-body h3 {
    font-weight: 700;
    font-size: 1.8rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .card-body h3 {
        font-size: 1.5rem;
    }

    .btn .fa-2x {
        font-size: 1.5em;
    }
}

/* Custom badge styles */
.badge {
    font-size: 0.75rem;
    padding: 0.35em 0.65em;
    border-radius: 6px;
}

/* Icon containers */
.bg-opacity-10 {
    border-radius: 10px;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add animation to statistics cards
    const statCards = document.querySelectorAll('.card h3');
    statCards.forEach(card => {
        const finalValue = card.textContent;
        card.textContent = 'Rp 0';

        // Simple counter animation (you can enhance this)
        setTimeout(() => {
            card.textContent = finalValue;
        }, 500);
    });

    // Add hover effects to quick action buttons
    const quickActions = document.querySelectorAll('.btn-outline-primary, .btn-outline-success, .btn-outline-warning, .btn-outline-info');
    quickActions.forEach(btn => {
        btn.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
        });

        btn.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
});
</script>
