<?php
/**
 * Simple File-based Cache System
 * 
 * This file contains functions for caching data to improve performance.
 */

// Define cache directory
define('CACHE_DIR', dirname(dirname(dirname(__FILE__))) . '/cache');

/**
 * Initialize cache system
 */
function initializeCache() {
    if (!is_dir(CACHE_DIR)) {
        mkdir(CACHE_DIR, 0755, true);
    }
    
    // Create .htaccess to protect cache directory
    $htaccessFile = CACHE_DIR . '/.htaccess';
    if (!file_exists($htaccessFile)) {
        file_put_contents($htaccessFile, "Deny from all\n");
    }
}

/**
 * Generate cache key
 * @param string $key Original key
 * @return string
 */
function generateCacheKey($key) {
    return md5($key);
}

/**
 * Get cache file path
 * @param string $key Cache key
 * @return string
 */
function getCacheFilePath($key) {
    $hashedKey = generateCacheKey($key);
    return CACHE_DIR . '/' . $hashedKey . '.cache';
}

/**
 * Set cache data
 * @param string $key Cache key
 * @param mixed $data Data to cache
 * @param int $ttl Time to live in seconds (default: 1 hour)
 * @return bool
 */
function setCache($key, $data, $ttl = 3600) {
    initializeCache();
    
    $cacheFile = getCacheFilePath($key);
    $cacheData = [
        'data' => $data,
        'expires' => time() + $ttl,
        'created' => time()
    ];
    
    $serializedData = serialize($cacheData);
    return file_put_contents($cacheFile, $serializedData, LOCK_EX) !== false;
}

/**
 * Get cache data
 * @param string $key Cache key
 * @return mixed|null
 */
function getCache($key) {
    $cacheFile = getCacheFilePath($key);
    
    if (!file_exists($cacheFile)) {
        return null;
    }
    
    $serializedData = file_get_contents($cacheFile);
    if ($serializedData === false) {
        return null;
    }
    
    $cacheData = unserialize($serializedData);
    if ($cacheData === false) {
        // Invalid cache data, delete file
        unlink($cacheFile);
        return null;
    }
    
    // Check if cache has expired
    if (time() > $cacheData['expires']) {
        unlink($cacheFile);
        return null;
    }
    
    return $cacheData['data'];
}

/**
 * Check if cache exists and is valid
 * @param string $key Cache key
 * @return bool
 */
function hasCache($key) {
    return getCache($key) !== null;
}

/**
 * Delete cache
 * @param string $key Cache key
 * @return bool
 */
function deleteCache($key) {
    $cacheFile = getCacheFilePath($key);
    
    if (file_exists($cacheFile)) {
        return unlink($cacheFile);
    }
    
    return true;
}

/**
 * Clear all cache
 * @return bool
 */
function clearAllCache() {
    if (!is_dir(CACHE_DIR)) {
        return true;
    }
    
    $files = glob(CACHE_DIR . '/*.cache');
    $success = true;
    
    foreach ($files as $file) {
        if (!unlink($file)) {
            $success = false;
        }
    }
    
    return $success;
}

/**
 * Clean expired cache files
 * @return int Number of files cleaned
 */
function cleanExpiredCache() {
    if (!is_dir(CACHE_DIR)) {
        return 0;
    }
    
    $files = glob(CACHE_DIR . '/*.cache');
    $cleaned = 0;
    
    foreach ($files as $file) {
        $serializedData = file_get_contents($file);
        if ($serializedData === false) {
            continue;
        }
        
        $cacheData = unserialize($serializedData);
        if ($cacheData === false || time() > $cacheData['expires']) {
            if (unlink($file)) {
                $cleaned++;
            }
        }
    }
    
    return $cleaned;
}

/**
 * Get cache statistics
 * @return array
 */
function getCacheStats() {
    if (!is_dir(CACHE_DIR)) {
        return [
            'total_files' => 0,
            'total_size' => 0,
            'expired_files' => 0
        ];
    }
    
    $files = glob(CACHE_DIR . '/*.cache');
    $totalFiles = count($files);
    $totalSize = 0;
    $expiredFiles = 0;
    
    foreach ($files as $file) {
        $totalSize += filesize($file);
        
        $serializedData = file_get_contents($file);
        if ($serializedData !== false) {
            $cacheData = unserialize($serializedData);
            if ($cacheData !== false && time() > $cacheData['expires']) {
                $expiredFiles++;
            }
        }
    }
    
    return [
        'total_files' => $totalFiles,
        'total_size' => $totalSize,
        'expired_files' => $expiredFiles,
        'total_size_formatted' => formatFileSize($totalSize)
    ];
}

/**
 * Cache function result
 * @param string $key Cache key
 * @param callable $callback Function to execute if cache miss
 * @param int $ttl Time to live in seconds
 * @return mixed
 */
function cacheFunction($key, $callback, $ttl = 3600) {
    $cachedData = getCache($key);
    
    if ($cachedData !== null) {
        return $cachedData;
    }
    
    $result = call_user_func($callback);
    setCache($key, $result, $ttl);
    
    return $result;
}

/**
 * Cache database query result
 * @param string $sql SQL query
 * @param array $params Query parameters
 * @param int $ttl Time to live in seconds
 * @return array
 */
function cacheQuery($sql, $params = [], $ttl = 3600) {
    $cacheKey = 'query_' . md5($sql . serialize($params));
    
    return cacheFunction($cacheKey, function() use ($sql, $params) {
        global $pdo;
        
        try {
            $stmt = $pdo->prepare($sql);
            $stmt->execute($params);
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Cache query error: " . $e->getMessage());
            return [];
        }
    }, $ttl);
}

/**
 * Invalidate cache by pattern
 * @param string $pattern Pattern to match cache keys
 * @return int Number of files deleted
 */
function invalidateCacheByPattern($pattern) {
    if (!is_dir(CACHE_DIR)) {
        return 0;
    }
    
    $files = glob(CACHE_DIR . '/*.cache');
    $deleted = 0;
    
    foreach ($files as $file) {
        $filename = basename($file, '.cache');
        
        // Simple pattern matching (you can enhance this)
        if (strpos($filename, md5($pattern)) !== false) {
            if (unlink($file)) {
                $deleted++;
            }
        }
    }
    
    return $deleted;
}

/**
 * Get cache file info
 * @param string $key Cache key
 * @return array|null
 */
function getCacheInfo($key) {
    $cacheFile = getCacheFilePath($key);
    
    if (!file_exists($cacheFile)) {
        return null;
    }
    
    $serializedData = file_get_contents($cacheFile);
    if ($serializedData === false) {
        return null;
    }
    
    $cacheData = unserialize($serializedData);
    if ($cacheData === false) {
        return null;
    }
    
    return [
        'key' => $key,
        'file' => $cacheFile,
        'size' => filesize($cacheFile),
        'created' => $cacheData['created'],
        'expires' => $cacheData['expires'],
        'ttl' => $cacheData['expires'] - $cacheData['created'],
        'is_expired' => time() > $cacheData['expires'],
        'age' => time() - $cacheData['created']
    ];
}

/**
 * Refresh cache (delete and regenerate)
 * @param string $key Cache key
 * @param callable $callback Function to regenerate data
 * @param int $ttl Time to live in seconds
 * @return mixed
 */
function refreshCache($key, $callback, $ttl = 3600) {
    deleteCache($key);
    return cacheFunction($key, $callback, $ttl);
}

/**
 * Warm up cache with predefined data
 * @param array $cacheData Array of cache key => data pairs
 * @param int $ttl Time to live in seconds
 * @return bool
 */
function warmUpCache($cacheData, $ttl = 3600) {
    $success = true;
    
    foreach ($cacheData as $key => $data) {
        if (!setCache($key, $data, $ttl)) {
            $success = false;
        }
    }
    
    return $success;
}

// Initialize cache system when this file is included
initializeCache();
