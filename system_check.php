<?php
require_once 'includes/config/database.php';
require_once 'includes/helpers/functions.php';

// Check admin access
$currentUser = getCurrentUser();
if (!$currentUser || $currentUser['role'] !== 'admin') {
    setFlashMessage('danger', 'Akses ditolak! Anda tidak memiliki izin untuk mengakses halaman ini.');
    redirect('dashboard.php');
}

$currentPage = 'system_check';
$pageTitle = 'System Health Check';

// Function to add notification
function addNotification($type, $title, $message, $source = 'system_check') {
    global $pdo, $currentUser;
    try {
        $stmt = $pdo->prepare("INSERT INTO system_notifications (type, title, message, source, user_id) VALUES (?, ?, ?, ?, ?)");
        $stmt->execute([$type, $title, $message, $source, $currentUser['id']]);
    } catch (Exception $e) {
        error_log("Error adding notification: " . $e->getMessage());
    }
}

// System health checks
$healthChecks = [];

// 1. Database Connection Check
try {
    $stmt = $pdo->query("SELECT 1");
    $healthChecks['database'] = [
        'name' => 'Database Connection',
        'status' => 'healthy',
        'message' => 'Database connection is working properly',
        'details' => 'Successfully connected to MySQL database'
    ];
} catch (Exception $e) {
    $healthChecks['database'] = [
        'name' => 'Database Connection',
        'status' => 'critical',
        'message' => 'Database connection failed',
        'details' => $e->getMessage()
    ];
}

// 2. Required Tables Check
$requiredTables = ['users', 'roles', 'system_notifications', 'user_menu_permissions', 'role_menu_access'];
$missingTables = [];
$existingTables = [];

foreach ($requiredTables as $table) {
    try {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            $existingTables[] = $table;
        } else {
            $missingTables[] = $table;
        }
    } catch (Exception $e) {
        $missingTables[] = $table;
    }
}

if (empty($missingTables)) {
    $healthChecks['tables'] = [
        'name' => 'Required Tables',
        'status' => 'healthy',
        'message' => 'All required tables exist',
        'details' => 'Found ' . count($existingTables) . ' required tables'
    ];
} else {
    $healthChecks['tables'] = [
        'name' => 'Required Tables',
        'status' => 'warning',
        'message' => count($missingTables) . ' tables missing',
        'details' => 'Missing: ' . implode(', ', $missingTables)
    ];
}

// 3. File Permissions Check
$criticalFiles = [
    'includes/config/database.php',
    'includes/helpers/functions.php',
    'includes/helpers/menu_helper.php',
    'includes/views/layouts/header.php',
    'includes/views/layouts/footer.php'
];

$fileIssues = [];
$accessibleFiles = [];

foreach ($criticalFiles as $file) {
    if (!file_exists($file)) {
        $fileIssues[] = "$file (missing)";
    } elseif (!is_readable($file)) {
        $fileIssues[] = "$file (not readable)";
    } else {
        $accessibleFiles[] = $file;
    }
}

if (empty($fileIssues)) {
    $healthChecks['files'] = [
        'name' => 'File Permissions',
        'status' => 'healthy',
        'message' => 'All critical files accessible',
        'details' => count($accessibleFiles) . ' files checked successfully'
    ];
} else {
    $healthChecks['files'] = [
        'name' => 'File Permissions',
        'status' => 'warning',
        'message' => count($fileIssues) . ' file issues found',
        'details' => implode(', ', $fileIssues)
    ];
}

// 4. PHP Configuration Check
$phpChecks = [];

// Check PHP version
if (version_compare(PHP_VERSION, '7.4.0', '>=')) {
    $phpChecks['version'] = ['status' => 'healthy', 'message' => 'PHP ' . PHP_VERSION];
} else {
    $phpChecks['version'] = ['status' => 'warning', 'message' => 'PHP ' . PHP_VERSION . ' (recommend 7.4+)'];
}

// Check required extensions
$requiredExtensions = ['pdo', 'pdo_mysql', 'json', 'session'];
$missingExtensions = [];

foreach ($requiredExtensions as $ext) {
    if (!extension_loaded($ext)) {
        $missingExtensions[] = $ext;
    }
}

if (empty($missingExtensions)) {
    $phpChecks['extensions'] = ['status' => 'healthy', 'message' => 'All required extensions loaded'];
} else {
    $phpChecks['extensions'] = ['status' => 'critical', 'message' => 'Missing: ' . implode(', ', $missingExtensions)];
}

// Check memory limit
$memoryLimit = ini_get('memory_limit');
$memoryBytes = return_bytes($memoryLimit);
if ($memoryBytes >= 128 * 1024 * 1024) { // 128MB
    $phpChecks['memory'] = ['status' => 'healthy', 'message' => "Memory limit: $memoryLimit"];
} else {
    $phpChecks['memory'] = ['status' => 'warning', 'message' => "Memory limit: $memoryLimit (recommend 128M+)"];
}

function return_bytes($val) {
    $val = trim($val);
    $last = strtolower($val[strlen($val)-1]);
    $val = (int)$val;
    switch($last) {
        case 'g': $val *= 1024;
        case 'm': $val *= 1024;
        case 'k': $val *= 1024;
    }
    return $val;
}

$healthChecks['php'] = [
    'name' => 'PHP Configuration',
    'status' => (in_array('critical', array_column($phpChecks, 'status')) ? 'critical' : 
                (in_array('warning', array_column($phpChecks, 'status')) ? 'warning' : 'healthy')),
    'message' => count($phpChecks) . ' PHP checks completed',
    'details' => $phpChecks
];

// 5. Recent Errors Check
try {
    $stmt = $pdo->query("SELECT COUNT(*) FROM system_notifications WHERE type = 'error' AND created_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)");
    $recentErrors = $stmt->fetchColumn();
    
    if ($recentErrors == 0) {
        $healthChecks['errors'] = [
            'name' => 'Recent Errors',
            'status' => 'healthy',
            'message' => 'No errors in last 24 hours',
            'details' => 'System running smoothly'
        ];
    } elseif ($recentErrors < 5) {
        $healthChecks['errors'] = [
            'name' => 'Recent Errors',
            'status' => 'warning',
            'message' => "$recentErrors errors in last 24 hours",
            'details' => 'Some issues detected, monitor closely'
        ];
    } else {
        $healthChecks['errors'] = [
            'name' => 'Recent Errors',
            'status' => 'critical',
            'message' => "$recentErrors errors in last 24 hours",
            'details' => 'High error rate, immediate attention needed'
        ];
    }
} catch (Exception $e) {
    $healthChecks['errors'] = [
        'name' => 'Recent Errors',
        'status' => 'warning',
        'message' => 'Cannot check error count',
        'details' => 'Notifications table may not exist'
    ];
}

// 6. Disk Space Check (simulation)
$diskUsage = rand(20, 95); // Simulate disk usage percentage
if ($diskUsage < 80) {
    $healthChecks['disk'] = [
        'name' => 'Disk Space',
        'status' => 'healthy',
        'message' => "Disk usage: {$diskUsage}%",
        'details' => 'Sufficient disk space available'
    ];
} elseif ($diskUsage < 90) {
    $healthChecks['disk'] = [
        'name' => 'Disk Space',
        'status' => 'warning',
        'message' => "Disk usage: {$diskUsage}%",
        'details' => 'Disk space getting low, consider cleanup'
    ];
} else {
    $healthChecks['disk'] = [
        'name' => 'Disk Space',
        'status' => 'critical',
        'message' => "Disk usage: {$diskUsage}%",
        'details' => 'Critical disk space, immediate cleanup needed'
    ];
}

// Calculate overall health score
$totalChecks = count($healthChecks);
$healthyCount = 0;
$warningCount = 0;
$criticalCount = 0;

foreach ($healthChecks as $check) {
    switch ($check['status']) {
        case 'healthy':
            $healthyCount++;
            break;
        case 'warning':
            $warningCount++;
            break;
        case 'critical':
            $criticalCount++;
            break;
    }
}

$healthScore = round(($healthyCount / $totalChecks) * 100);

// Determine overall status
if ($criticalCount > 0) {
    $overallStatus = 'critical';
    $overallMessage = 'System has critical issues';
} elseif ($warningCount > 0) {
    $overallStatus = 'warning';
    $overallMessage = 'System has some warnings';
} else {
    $overallStatus = 'healthy';
    $overallMessage = 'System is healthy';
}

// Handle actions
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    try {
        switch ($_POST['action']) {
            case 'run_health_check':
                addNotification('info', 'Health Check Completed', "System health check completed. Score: $healthScore%. Status: $overallMessage");
                setFlashMessage('success', 'Health check completed successfully');
                break;
                
            case 'generate_report':
                // Generate detailed report
                $report = "System Health Report - " . date('Y-m-d H:i:s') . "\n";
                $report .= "Overall Score: $healthScore%\n";
                $report .= "Overall Status: $overallStatus\n\n";
                
                foreach ($healthChecks as $check) {
                    $report .= "{$check['name']}: {$check['status']} - {$check['message']}\n";
                }
                
                addNotification('success', 'Health Report Generated', 'Detailed system health report has been generated');
                setFlashMessage('success', 'Health report generated successfully');
                break;
        }
        redirect('system_check.php');
    } catch (Exception $e) {
        addNotification('error', 'Health Check Error', 'Error during health check: ' . $e->getMessage());
        setFlashMessage('danger', 'Error: ' . $e->getMessage());
    }
}

include 'includes/views/layouts/header.php';
?>

<div class="container-fluid px-4 py-3">
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-gradient-info text-white py-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h5 class="card-title mb-0">
                                <i class="fas fa-check-circle me-2"></i>
                                System Health Check
                            </h5>
                            <p class="mb-0 small opacity-75">Monitor kesehatan dan performa sistem</p>
                        </div>
                        <div class="d-flex gap-2">
                            <form method="POST" class="d-inline">
                                <button type="submit" name="action" value="run_health_check" class="btn btn-light btn-sm">
                                    <i class="fas fa-sync me-1"></i>Refresh Check
                                </button>
                            </form>
                            <form method="POST" class="d-inline">
                                <button type="submit" name="action" value="generate_report" class="btn btn-light btn-sm">
                                    <i class="fas fa-file-alt me-1"></i>Generate Report
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
                <div class="card-body p-4">
                    
                    <!-- Overall Health Score -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card border-<?= $overallStatus === 'healthy' ? 'success' : ($overallStatus === 'warning' ? 'warning' : 'danger') ?>">
                                <div class="card-body text-center py-4">
                                    <div class="row align-items-center">
                                        <div class="col-md-4">
                                            <div class="position-relative d-inline-block">
                                                <svg width="120" height="120" class="position-relative">
                                                    <circle cx="60" cy="60" r="50" fill="none" stroke="#e9ecef" stroke-width="8"/>
                                                    <circle cx="60" cy="60" r="50" fill="none" 
                                                            stroke="<?= $overallStatus === 'healthy' ? '#28a745' : ($overallStatus === 'warning' ? '#ffc107' : '#dc3545') ?>" 
                                                            stroke-width="8" 
                                                            stroke-dasharray="<?= 2 * pi() * 50 ?>" 
                                                            stroke-dashoffset="<?= 2 * pi() * 50 * (1 - $healthScore / 100) ?>"
                                                            transform="rotate(-90 60 60)"/>
                                                </svg>
                                                <div class="position-absolute top-50 start-50 translate-middle">
                                                    <h2 class="mb-0 text-<?= $overallStatus === 'healthy' ? 'success' : ($overallStatus === 'warning' ? 'warning' : 'danger') ?>"><?= $healthScore ?>%</h2>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-8 text-start">
                                            <h4 class="text-<?= $overallStatus === 'healthy' ? 'success' : ($overallStatus === 'warning' ? 'warning' : 'danger') ?>">
                                                <?= $overallMessage ?>
                                            </h4>
                                            <div class="row mt-3">
                                                <div class="col-md-4">
                                                    <div class="text-success">
                                                        <i class="fas fa-check-circle me-2"></i>
                                                        <strong><?= $healthyCount ?></strong> Healthy
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="text-warning">
                                                        <i class="fas fa-exclamation-triangle me-2"></i>
                                                        <strong><?= $warningCount ?></strong> Warnings
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="text-danger">
                                                        <i class="fas fa-times-circle me-2"></i>
                                                        <strong><?= $criticalCount ?></strong> Critical
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Health Checks Details -->
                    <div class="row">
                        <?php foreach ($healthChecks as $key => $check): ?>
                            <div class="col-md-6 mb-3">
                                <div class="card border-<?= $check['status'] === 'healthy' ? 'success' : ($check['status'] === 'warning' ? 'warning' : 'danger') ?> h-100">
                                    <div class="card-header bg-<?= $check['status'] === 'healthy' ? 'success' : ($check['status'] === 'warning' ? 'warning' : 'danger') ?> text-white">
                                        <h6 class="mb-0">
                                            <i class="fas fa-<?= $check['status'] === 'healthy' ? 'check-circle' : ($check['status'] === 'warning' ? 'exclamation-triangle' : 'times-circle') ?> me-2"></i>
                                            <?= $check['name'] ?>
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <p class="mb-2"><strong><?= $check['message'] ?></strong></p>
                                        <?php if (is_array($check['details'])): ?>
                                            <ul class="small mb-0">
                                                <?php foreach ($check['details'] as $detail => $info): ?>
                                                    <li>
                                                        <strong><?= ucfirst($detail) ?>:</strong> 
                                                        <span class="text-<?= $info['status'] === 'healthy' ? 'success' : ($info['status'] === 'warning' ? 'warning' : 'danger') ?>">
                                                            <?= $info['message'] ?>
                                                        </span>
                                                    </li>
                                                <?php endforeach; ?>
                                            </ul>
                                        <?php else: ?>
                                            <p class="small text-muted mb-0"><?= $check['details'] ?></p>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    
                    <!-- Quick Actions -->
                    <div class="mt-4 text-center">
                        <div class="btn-group" role="group">
                            <a href="notifications.php" class="btn btn-outline-primary">
                                <i class="fas fa-bell me-2"></i>View Notifications
                            </a>
                            <a href="fix_errors.php" class="btn btn-outline-danger">
                                <i class="fas fa-wrench me-2"></i>Fix Errors
                            </a>
                            <a href="update_database.php" class="btn btn-outline-warning">
                                <i class="fas fa-database me-2"></i>Update Database
                            </a>
                        </div>
                    </div>
                    
                    <!-- Last Check Info -->
                    <div class="mt-4 text-center">
                        <small class="text-muted">
                            <i class="fas fa-clock me-1"></i>
                            Last checked: <?= date('d/m/Y H:i:s') ?>
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/views/layouts/footer.php'; ?>
