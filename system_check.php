<?php
require_once 'includes/config/database.php';
require_once 'includes/helpers/function_check.php';
require_once 'includes/helpers/functions.php';

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in and is admin
if (!isLoggedIn()) {
    setFlashMessage('danger', 'Silakan login terlebih dahulu');
    redirect('login.php');
}

$currentUser = getCurrentUser();
if ($currentUser['role'] !== 'admin') {
    setFlashMessage('danger', 'Akses ditolak. Hanya admin yang dapat mengakses halaman ini.');
    redirect('dashboard.php');
}

$currentPage = 'system_check';

// Perform system health checks
$healthChecks = performSystemHealthChecks();

// Include header and sidebar
require_once 'includes/views/layouts/header.php';
require_once 'includes/views/layouts/sidebar.php';

/**
 * Perform comprehensive system health checks
 */
function performSystemHealthChecks() {
    $checks = [
        'database' => checkDatabaseHealth(),
        'files' => checkFileSystemHealth(),
        'php' => checkPHPConfiguration(),
        'security' => checkSecurityConfiguration(),
        'performance' => checkPerformanceMetrics(),
        'dependencies' => checkDependencies()
    ];

    // Calculate overall health score
    $totalChecks = 0;
    $passedChecks = 0;

    foreach ($checks as $category => $categoryChecks) {
        foreach ($categoryChecks['checks'] as $check) {
            $totalChecks++;
            if ($check['status'] === 'pass') {
                $passedChecks++;
            }
        }
    }

    $checks['overall_score'] = $totalChecks > 0 ? round(($passedChecks / $totalChecks) * 100) : 0;
    $checks['total_checks'] = $totalChecks;
    $checks['passed_checks'] = $passedChecks;

    return $checks;
}

/**
 * Check database health
 */
function checkDatabaseHealth() {
    global $pdo;

    $checks = [];

    // Database connection
    try {
        $pdo->query('SELECT 1');
        $checks[] = [
            'name' => 'Database Connection',
            'status' => 'pass',
            'message' => 'Database connection is working',
            'details' => 'Successfully connected to MySQL database'
        ];
    } catch (Exception $e) {
        $checks[] = [
            'name' => 'Database Connection',
            'status' => 'fail',
            'message' => 'Database connection failed',
            'details' => $e->getMessage()
        ];
    }

    // Check required tables
    $requiredTables = ['users', 'transaksi', 'kategori', 'target', 'supplier', 'inventory', 'notifications'];
    $missingTables = [];

    foreach ($requiredTables as $table) {
        try {
            $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
            if ($stmt->rowCount() === 0) {
                $missingTables[] = $table;
            }
        } catch (Exception $e) {
            $missingTables[] = $table;
        }
    }

    if (empty($missingTables)) {
        $checks[] = [
            'name' => 'Required Tables',
            'status' => 'pass',
            'message' => 'All required tables exist',
            'details' => 'Found ' . count($requiredTables) . ' required tables'
        ];
    } else {
        $checks[] = [
            'name' => 'Required Tables',
            'status' => 'fail',
            'message' => 'Missing required tables',
            'details' => 'Missing tables: ' . implode(', ', $missingTables)
        ];
    }

    // Database size check
    try {
        $stmt = $pdo->query("
            SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS size_mb
            FROM information_schema.tables
            WHERE table_schema = DATABASE()
        ");
        $sizeResult = $stmt->fetch();
        $sizeMB = $sizeResult['size_mb'] ?? 0;

        if ($sizeMB < 100) {
            $checks[] = [
                'name' => 'Database Size',
                'status' => 'pass',
                'message' => 'Database size is optimal',
                'details' => "Current size: {$sizeMB} MB"
            ];
        } elseif ($sizeMB < 500) {
            $checks[] = [
                'name' => 'Database Size',
                'status' => 'warning',
                'message' => 'Database size is moderate',
                'details' => "Current size: {$sizeMB} MB - Consider optimization"
            ];
        } else {
            $checks[] = [
                'name' => 'Database Size',
                'status' => 'fail',
                'message' => 'Database size is large',
                'details' => "Current size: {$sizeMB} MB - Optimization recommended"
            ];
        }
    } catch (Exception $e) {
        $checks[] = [
            'name' => 'Database Size',
            'status' => 'warning',
            'message' => 'Could not check database size',
            'details' => $e->getMessage()
        ];
    }

    return [
        'category' => 'Database Health',
        'checks' => $checks
    ];
}

/**
 * Check file system health
 */
function checkFileSystemHealth() {
    $checks = [];

    // Required directories
    $requiredDirs = [
        'includes' => 'Core includes directory',
        'assets' => 'Assets directory',
        'uploads' => 'File uploads directory',
        'cache' => 'Cache directory',
        'logs' => 'Log files directory',
        'backups' => 'Backup files directory'
    ];

    foreach ($requiredDirs as $dir => $description) {
        if (is_dir($dir)) {
            if (is_writable($dir)) {
                $checks[] = [
                    'name' => "Directory: $dir",
                    'status' => 'pass',
                    'message' => 'Directory exists and is writable',
                    'details' => $description
                ];
            } else {
                $checks[] = [
                    'name' => "Directory: $dir",
                    'status' => 'warning',
                    'message' => 'Directory exists but not writable',
                    'details' => $description . ' - Check permissions'
                ];
            }
        } else {
            $checks[] = [
                'name' => "Directory: $dir",
                'status' => 'fail',
                'message' => 'Directory does not exist',
                'details' => $description . ' - Directory missing'
            ];
        }
    }

    // Check disk space
    $freeBytes = disk_free_space('.');
    $totalBytes = disk_total_space('.');

    if ($freeBytes && $totalBytes) {
        $freePercent = ($freeBytes / $totalBytes) * 100;

        if ($freePercent > 20) {
            $checks[] = [
                'name' => 'Disk Space',
                'status' => 'pass',
                'message' => 'Sufficient disk space available',
                'details' => number_format($freePercent, 1) . '% free (' . formatBytes($freeBytes) . ' available)'
            ];
        } elseif ($freePercent > 10) {
            $checks[] = [
                'name' => 'Disk Space',
                'status' => 'warning',
                'message' => 'Low disk space',
                'details' => number_format($freePercent, 1) . '% free (' . formatBytes($freeBytes) . ' available)'
            ];
        } else {
            $checks[] = [
                'name' => 'Disk Space',
                'status' => 'fail',
                'message' => 'Very low disk space',
                'details' => number_format($freePercent, 1) . '% free (' . formatBytes($freeBytes) . ' available)'
            ];
        }
    }

    return [
        'category' => 'File System Health',
        'checks' => $checks
    ];
}

/**
 * Check PHP configuration
 */
function checkPHPConfiguration() {
    $checks = [];

    // PHP version
    $phpVersion = PHP_VERSION;
    $minVersion = '7.4.0';

    if (version_compare($phpVersion, $minVersion, '>=')) {
        $checks[] = [
            'name' => 'PHP Version',
            'status' => 'pass',
            'message' => 'PHP version is supported',
            'details' => "Current: $phpVersion (Minimum: $minVersion)"
        ];
    } else {
        $checks[] = [
            'name' => 'PHP Version',
            'status' => 'fail',
            'message' => 'PHP version is outdated',
            'details' => "Current: $phpVersion (Minimum: $minVersion)"
        ];
    }

    // Required extensions
    $requiredExtensions = [
        'pdo' => 'PDO extension for database',
        'pdo_mysql' => 'MySQL PDO driver',
        'mbstring' => 'Multibyte string support',
        'openssl' => 'OpenSSL for encryption',
        'curl' => 'cURL for HTTP requests',
        'json' => 'JSON support'
    ];

    foreach ($requiredExtensions as $ext => $description) {
        if (extension_loaded($ext)) {
            $checks[] = [
                'name' => "Extension: $ext",
                'status' => 'pass',
                'message' => 'Extension is loaded',
                'details' => $description
            ];
        } else {
            $checks[] = [
                'name' => "Extension: $ext",
                'status' => 'fail',
                'message' => 'Extension is missing',
                'details' => $description . ' - Please install this extension'
            ];
        }
    }

    // Memory limit
    $memoryLimit = ini_get('memory_limit');
    $memoryLimitBytes = return_bytes($memoryLimit);
    $recommendedBytes = return_bytes('128M');

    if ($memoryLimitBytes >= $recommendedBytes) {
        $checks[] = [
            'name' => 'Memory Limit',
            'status' => 'pass',
            'message' => 'Memory limit is sufficient',
            'details' => "Current: $memoryLimit (Recommended: 128M+)"
        ];
    } else {
        $checks[] = [
            'name' => 'Memory Limit',
            'status' => 'warning',
            'message' => 'Memory limit is low',
            'details' => "Current: $memoryLimit (Recommended: 128M+)"
        ];
    }

    return [
        'category' => 'PHP Configuration',
        'checks' => $checks
    ];
}

/**
 * Convert memory limit to bytes
 */
function return_bytes($val) {
    $val = trim($val);
    $last = strtolower($val[strlen($val)-1]);
    $val = (int)$val;

    switch($last) {
        case 'g':
            $val *= 1024;
        case 'm':
            $val *= 1024;
        case 'k':
            $val *= 1024;
    }

    return $val;
}

/**
 * Check security configuration
 */
function checkSecurityConfiguration() {
    $checks = [];

    // Check if admin user exists
    global $pdo;
    try {
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE role = 'admin'");
        $stmt->execute();
        $adminCount = $stmt->fetchColumn();

        if ($adminCount > 0) {
            $checks[] = [
                'name' => 'Admin User',
                'status' => 'pass',
                'message' => 'Admin user exists',
                'details' => "$adminCount admin user(s) found"
            ];
        } else {
            $checks[] = [
                'name' => 'Admin User',
                'status' => 'fail',
                'message' => 'No admin user found',
                'details' => 'At least one admin user is required'
            ];
        }
    } catch (Exception $e) {
        $checks[] = [
            'name' => 'Admin User',
            'status' => 'warning',
            'message' => 'Could not check admin users',
            'details' => $e->getMessage()
        ];
    }

    // Check session configuration
    if (session_status() === PHP_SESSION_ACTIVE) {
        $checks[] = [
            'name' => 'Session Management',
            'status' => 'pass',
            'message' => 'Sessions are working',
            'details' => 'Session ID: ' . session_id()
        ];
    } else {
        $checks[] = [
            'name' => 'Session Management',
            'status' => 'warning',
            'message' => 'Session not active',
            'details' => 'Sessions may not be configured properly'
        ];
    }

    // Check HTTPS
    $isHTTPS = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') || $_SERVER['SERVER_PORT'] == 443;

    if ($isHTTPS) {
        $checks[] = [
            'name' => 'HTTPS Security',
            'status' => 'pass',
            'message' => 'HTTPS is enabled',
            'details' => 'Secure connection is active'
        ];
    } else {
        $checks[] = [
            'name' => 'HTTPS Security',
            'status' => 'warning',
            'message' => 'HTTPS is not enabled',
            'details' => 'Consider enabling HTTPS for production'
        ];
    }

    return [
        'category' => 'Security Configuration',
        'checks' => $checks
    ];
}

/**
 * Check performance metrics
 */
function checkPerformanceMetrics() {
    $checks = [];

    // Memory usage
    $memoryUsage = memory_get_usage(true);
    $memoryLimit = return_bytes(ini_get('memory_limit'));
    $memoryPercent = ($memoryUsage / $memoryLimit) * 100;

    if ($memoryPercent < 50) {
        $checks[] = [
            'name' => 'Memory Usage',
            'status' => 'pass',
            'message' => 'Memory usage is optimal',
            'details' => formatBytes($memoryUsage) . ' used (' . number_format($memoryPercent, 1) . '%)'
        ];
    } elseif ($memoryPercent < 80) {
        $checks[] = [
            'name' => 'Memory Usage',
            'status' => 'warning',
            'message' => 'Memory usage is moderate',
            'details' => formatBytes($memoryUsage) . ' used (' . number_format($memoryPercent, 1) . '%)'
        ];
    } else {
        $checks[] = [
            'name' => 'Memory Usage',
            'status' => 'fail',
            'message' => 'Memory usage is high',
            'details' => formatBytes($memoryUsage) . ' used (' . number_format($memoryPercent, 1) . '%)'
        ];
    }

    // Check if OPcache is enabled
    if (function_exists('opcache_get_status')) {
        $opcacheStatus = opcache_get_status();
        if ($opcacheStatus && $opcacheStatus['opcache_enabled']) {
            $checks[] = [
                'name' => 'OPcache',
                'status' => 'pass',
                'message' => 'OPcache is enabled',
                'details' => 'PHP bytecode caching is active'
            ];
        } else {
            $checks[] = [
                'name' => 'OPcache',
                'status' => 'warning',
                'message' => 'OPcache is disabled',
                'details' => 'Enable OPcache for better performance'
            ];
        }
    } else {
        $checks[] = [
            'name' => 'OPcache',
            'status' => 'warning',
            'message' => 'OPcache not available',
            'details' => 'OPcache extension is not installed'
        ];
    }

    return [
        'category' => 'Performance Metrics',
        'checks' => $checks
    ];
}

/**
 * Check dependencies
 */
function checkDependencies() {
    $checks = [];

    // Check if composer autoload exists
    if (file_exists('vendor/autoload.php')) {
        $checks[] = [
            'name' => 'Composer Dependencies',
            'status' => 'pass',
            'message' => 'Composer dependencies are installed',
            'details' => 'vendor/autoload.php found'
        ];
    } else {
        $checks[] = [
            'name' => 'Composer Dependencies',
            'status' => 'warning',
            'message' => 'Composer dependencies may be missing',
            'details' => 'Run "composer install" if needed'
        ];
    }

    // Check critical files
    $criticalFiles = [
        'includes/config/database.php' => 'Database configuration',
        'includes/helpers/functions.php' => 'Core functions',
        'includes/views/layouts/header.php' => 'Header layout',
        'includes/views/layouts/sidebar.php' => 'Sidebar layout',
        'includes/views/layouts/footer.php' => 'Footer layout'
    ];

    foreach ($criticalFiles as $file => $description) {
        if (file_exists($file)) {
            $checks[] = [
                'name' => "File: " . basename($file),
                'status' => 'pass',
                'message' => 'Critical file exists',
                'details' => $description
            ];
        } else {
            $checks[] = [
                'name' => "File: " . basename($file),
                'status' => 'fail',
                'message' => 'Critical file missing',
                'details' => $description . ' - File not found'
            ];
        }
    }

    return [
        'category' => 'Dependencies',
        'checks' => $checks
    ];
}
?>

<div class="modern-layout">
    <div class="modern-container">
        <!-- Modern Page Header -->
        <div class="modern-page-header modern-flex modern-justify-between modern-items-center modern-mb-xl">
            <div>
                <h1 class="modern-page-title modern-mb-0">System Health Check</h1>
                <p class="modern-page-subtitle">Comprehensive system health and configuration analysis</p>
            </div>
            <div class="modern-page-actions">
                <button type="button" class="modern-btn modern-btn-primary" onclick="location.reload()">
                    <i class="fas fa-sync-alt"></i>
                    Refresh Check
                </button>
            </div>
        </div>

        <!-- Overall Health Score -->
        <div class="modern-card modern-mb-lg">
            <div class="modern-card-header">
                <h5 class="modern-card-title">
                    <i class="fas fa-heartbeat modern-text-primary modern-mr-sm"></i>
                    Overall System Health
                </h5>
            </div>
            <div class="modern-card-body">
                <div class="modern-health-score">
                    <div class="modern-score-circle modern-score-<?= $healthChecks['overall_score'] >= 80 ? 'good' : ($healthChecks['overall_score'] >= 60 ? 'warning' : 'poor') ?>">
                        <div class="modern-score-percentage"><?= $healthChecks['overall_score'] ?>%</div>
                        <div class="modern-score-label">Health Score</div>
                    </div>
                    <div class="modern-score-details">
                        <div class="modern-score-stat">
                            <div class="modern-score-number"><?= $healthChecks['passed_checks'] ?></div>
                            <div class="modern-score-text">Passed</div>
                        </div>
                        <div class="modern-score-stat">
                            <div class="modern-score-number"><?= $healthChecks['total_checks'] - $healthChecks['passed_checks'] ?></div>
                            <div class="modern-score-text">Issues</div>
                        </div>
                        <div class="modern-score-stat">
                            <div class="modern-score-number"><?= $healthChecks['total_checks'] ?></div>
                            <div class="modern-score-text">Total</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Health Check Categories -->
        <?php foreach ($healthChecks as $key => $category): ?>
        <?php if (is_array($category) && isset($category['category'])): ?>
        <div class="modern-card modern-mb-lg">
            <div class="modern-card-header">
                <h5 class="modern-card-title">
                    <i class="fas fa-<?= getIconForCategory($key) ?> modern-text-primary modern-mr-sm"></i>
                    <?= $category['category'] ?>
                </h5>
                <div class="modern-card-actions">
                    <?php
                    $categoryPassed = 0;
                    $categoryTotal = count($category['checks']);
                    foreach ($category['checks'] as $check) {
                        if ($check['status'] === 'pass') $categoryPassed++;
                    }
                    $categoryScore = $categoryTotal > 0 ? round(($categoryPassed / $categoryTotal) * 100) : 0;
                    ?>
                    <span class="modern-badge modern-badge-<?= $categoryScore >= 80 ? 'success' : ($categoryScore >= 60 ? 'warning' : 'danger') ?>">
                        <?= $categoryPassed ?>/<?= $categoryTotal ?> passed
                    </span>
                </div>
            </div>
            <div class="modern-card-body modern-p-0">
                <div class="modern-health-checks">
                    <?php foreach ($category['checks'] as $check): ?>
                    <div class="modern-health-check modern-health-<?= $check['status'] ?>">
                        <div class="modern-check-icon">
                            <i class="fas fa-<?= $check['status'] === 'pass' ? 'check-circle' : ($check['status'] === 'warning' ? 'exclamation-triangle' : 'times-circle') ?>"></i>
                        </div>
                        <div class="modern-check-content">
                            <div class="modern-check-name"><?= htmlspecialchars($check['name']) ?></div>
                            <div class="modern-check-message"><?= htmlspecialchars($check['message']) ?></div>
                            <div class="modern-check-details"><?= htmlspecialchars($check['details']) ?></div>
                        </div>
                        <div class="modern-check-status">
                            <span class="modern-status-badge modern-status-<?= $check['status'] ?>">
                                <?= ucfirst($check['status']) ?>
                            </span>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
        <?php endif; ?>
        <?php endforeach; ?>
    </div>
</div>

<?php include 'includes/views/layouts/footer.php'; ?>

<?php
/**
 * Get icon for category
 */
function getIconForCategory($category) {
    $icons = [
        'database' => 'database',
        'files' => 'folder',
        'php' => 'code',
        'security' => 'shield-alt',
        'performance' => 'tachometer-alt',
        'dependencies' => 'puzzle-piece'
    ];

    return $icons[$category] ?? 'cog';
}
?>

<style>
.modern-health-score {
    display: flex;
    align-items: center;
    gap: 30px;
}

.modern-score-circle {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border: 6px solid;
    position: relative;
}

.modern-score-good { border-color: #28a745; color: #28a745; }
.modern-score-warning { border-color: #ffc107; color: #ffc107; }
.modern-score-poor { border-color: #dc3545; color: #dc3545; }

.modern-score-percentage {
    font-size: 24px;
    font-weight: 700;
}

.modern-score-label {
    font-size: 12px;
    margin-top: 5px;
}

.modern-score-details {
    display: flex;
    gap: 30px;
}

.modern-score-stat {
    text-align: center;
}

.modern-score-number {
    font-size: 28px;
    font-weight: 700;
    color: #2c3e50;
}

.modern-score-text {
    font-size: 14px;
    color: #6c757d;
    margin-top: 5px;
}

.modern-health-checks {
    display: flex;
    flex-direction: column;
}

.modern-health-check {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    padding: 20px;
    border-bottom: 1px solid #eee;
    transition: background-color 0.2s;
}

.modern-health-check:hover {
    background-color: #f8f9fa;
}

.modern-health-check:last-child {
    border-bottom: none;
}

.modern-check-icon {
    font-size: 20px;
    margin-top: 2px;
}

.modern-health-pass .modern-check-icon { color: #28a745; }
.modern-health-warning .modern-check-icon { color: #ffc107; }
.modern-health-fail .modern-check-icon { color: #dc3545; }

.modern-check-content {
    flex: 1;
}

.modern-check-name {
    font-weight: 600;
    font-size: 16px;
    margin-bottom: 5px;
    color: #2c3e50;
}

.modern-check-message {
    font-size: 14px;
    margin-bottom: 3px;
    color: #495057;
}

.modern-check-details {
    font-size: 12px;
    color: #6c757d;
    line-height: 1.4;
}

.modern-check-status {
    margin-top: 5px;
}

.modern-status-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
}

.modern-status-pass {
    background-color: #d4edda;
    color: #155724;
}

.modern-status-warning {
    background-color: #fff3cd;
    color: #856404;
}

.modern-status-fail {
    background-color: #f8d7da;
    color: #721c24;
}
</style>