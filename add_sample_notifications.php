<?php
require_once 'includes/config/database.php';
require_once 'includes/helpers/functions.php';

// Check admin access
$currentUser = getCurrentUser();
if (!$currentUser || $currentUser['role'] !== 'admin') {
    setFlashMessage('danger', 'Aks<PERSON> ditolak! Anda tidak memiliki izin untuk mengakses halaman ini.');
    redirect('dashboard.php');
}

$pageTitle = 'Add Sample Notifications';

// Ensure system_notifications table exists
try {
    $pdo->exec("CREATE TABLE IF NOT EXISTS system_notifications (
        id INT AUTO_INCREMENT PRIMARY KEY,
        type ENUM('error', 'warning', 'info', 'success') DEFAULT 'info',
        title VARCHAR(255) NOT NULL,
        message TEXT NOT NULL,
        source VARCHAR(100),
        user_id INT,
        is_read BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_type (type),
        INDEX idx_created_at (created_at),
        INDEX idx_is_read (is_read)
    )");
} catch (PDOException $e) {
    error_log("Error creating system_notifications table: " . $e->getMessage());
}

// Handle actions
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    try {
        switch ($_POST['action']) {
            case 'add_sample_notifications':
                // Sample notifications
                $sampleNotifications = [
                    [
                        'type' => 'success',
                        'title' => 'Sistem Berhasil Diinisialisasi',
                        'message' => 'Sistem keuangan telah berhasil diinisialisasi dan siap digunakan. Semua tabel database telah dibuat dengan sukses.',
                        'source' => 'system_init'
                    ],
                    [
                        'type' => 'info',
                        'title' => 'Selamat Datang di Sistem Keuangan',
                        'message' => 'Terima kasih telah menggunakan sistem keuangan. Anda dapat mulai mengelola transaksi, kategori, target, dan anggaran keuangan Anda.',
                        'source' => 'welcome'
                    ],
                    [
                        'type' => 'info',
                        'title' => 'Fitur Menu Lengkap Tersedia',
                        'message' => 'Sistem dilengkapi dengan 44 menu items termasuk manajemen keuangan, laporan, tools admin, dan fitur maintenance yang lengkap.',
                        'source' => 'features'
                    ],
                    [
                        'type' => 'warning',
                        'title' => 'Backup Data Secara Berkala',
                        'message' => 'Disarankan untuk melakukan backup data secara berkala melalui menu Admin Panel > Backup Data untuk menjaga keamanan data Anda.',
                        'source' => 'backup_reminder'
                    ],
                    [
                        'type' => 'info',
                        'title' => 'Panduan Penggunaan',
                        'message' => 'Untuk panduan lengkap penggunaan sistem, silakan kunjungi menu Bantuan > Panduan Pengguna atau hubungi administrator sistem.',
                        'source' => 'user_guide'
                    ],
                    [
                        'type' => 'success',
                        'title' => 'Database Health Check',
                        'message' => 'Semua tabel database telah berhasil dibuat dan sistem dalam kondisi sehat. Anda dapat mulai menggunakan semua fitur yang tersedia.',
                        'source' => 'health_check'
                    ],
                    [
                        'type' => 'info',
                        'title' => 'Menu Permissions Configured',
                        'message' => 'Sistem permissions telah dikonfigurasi dengan benar. Admin memiliki akses penuh ke semua menu, sedangkan user memiliki akses terbatas.',
                        'source' => 'permissions'
                    ],
                    [
                        'type' => 'success',
                        'title' => 'Financial Features Ready',
                        'message' => 'Semua fitur keuangan telah siap digunakan: Transaksi, Kategori, Target Keuangan, Anggaran, dan Laporan Keuangan.',
                        'source' => 'financial_features'
                    ],
                    [
                        'type' => 'warning',
                        'title' => 'Security Reminder',
                        'message' => 'Pastikan untuk menggunakan password yang kuat dan tidak membagikan kredensial login Anda kepada orang lain.',
                        'source' => 'security'
                    ],
                    [
                        'type' => 'info',
                        'title' => 'System Monitoring Active',
                        'message' => 'Sistem monitoring telah aktif dan akan memberikan notifikasi untuk error, warning, dan informasi penting lainnya.',
                        'source' => 'monitoring'
                    ]
                ];
                
                $added = 0;
                $stmt = $pdo->prepare("INSERT INTO system_notifications (type, title, message, source, user_id) VALUES (?, ?, ?, ?, ?)");
                
                foreach ($sampleNotifications as $notification) {
                    try {
                        $stmt->execute([
                            $notification['type'],
                            $notification['title'],
                            $notification['message'],
                            $notification['source'],
                            $currentUser['id']
                        ]);
                        $added++;
                    } catch (Exception $e) {
                        error_log("Error adding notification: " . $e->getMessage());
                    }
                }
                
                setFlashMessage('success', "Berhasil menambahkan $added notifikasi sample");
                break;
                
            case 'clear_all_notifications':
                $stmt = $pdo->prepare("DELETE FROM system_notifications");
                $stmt->execute();
                $deleted = $stmt->rowCount();
                setFlashMessage('success', "Berhasil menghapus $deleted notifikasi");
                break;
                
            case 'add_custom_notification':
                $type = $_POST['type'] ?? 'info';
                $title = trim($_POST['title'] ?? '');
                $message = trim($_POST['message'] ?? '');
                $source = trim($_POST['source'] ?? 'manual');
                
                if (empty($title) || empty($message)) {
                    throw new Exception('Title dan message harus diisi');
                }
                
                $stmt = $pdo->prepare("INSERT INTO system_notifications (type, title, message, source, user_id) VALUES (?, ?, ?, ?, ?)");
                $stmt->execute([$type, $title, $message, $source, $currentUser['id']]);
                
                setFlashMessage('success', 'Notifikasi custom berhasil ditambahkan');
                break;
        }
        redirect('add_sample_notifications.php');
    } catch (Exception $e) {
        setFlashMessage('danger', 'Error: ' . $e->getMessage());
    }
}

// Get current notification count
$stmt = $pdo->query("SELECT COUNT(*) FROM system_notifications");
$notificationCount = $stmt->fetchColumn();

// Get notification statistics
$stmt = $pdo->query("SELECT 
    COUNT(*) as total,
    SUM(CASE WHEN type = 'error' THEN 1 ELSE 0 END) as errors,
    SUM(CASE WHEN type = 'warning' THEN 1 ELSE 0 END) as warnings,
    SUM(CASE WHEN type = 'info' THEN 1 ELSE 0 END) as info,
    SUM(CASE WHEN type = 'success' THEN 1 ELSE 0 END) as success,
    SUM(CASE WHEN is_read = FALSE THEN 1 ELSE 0 END) as unread
    FROM system_notifications");
$stats = $stmt->fetch();

include 'includes/views/layouts/header.php';
?>

<div class="container-fluid px-4 py-3">
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-gradient-info text-white py-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h5 class="card-title mb-0">
                                <i class="fas fa-bell me-2"></i>
                                Add Sample Notifications
                            </h5>
                            <p class="mb-0 small opacity-75">Tambahkan notifikasi sample untuk testing sistem</p>
                        </div>
                        <div class="d-flex gap-2">
                            <a href="notifications.php" class="btn btn-light btn-sm">
                                <i class="fas fa-arrow-left me-1"></i>Back to Notifications
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body p-4">
                    
                    <!-- Current Statistics -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="mb-3">
                                <i class="fas fa-chart-bar me-2"></i>Current Notification Statistics
                            </h6>
                            <div class="row">
                                <div class="col-md-2">
                                    <div class="card bg-primary text-white">
                                        <div class="card-body text-center py-3">
                                            <h4 class="mb-1"><?= $stats['total'] ?></h4>
                                            <small>Total</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="card bg-danger text-white">
                                        <div class="card-body text-center py-3">
                                            <h4 class="mb-1"><?= $stats['errors'] ?></h4>
                                            <small>Errors</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="card bg-warning text-white">
                                        <div class="card-body text-center py-3">
                                            <h4 class="mb-1"><?= $stats['warnings'] ?></h4>
                                            <small>Warnings</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="card bg-info text-white">
                                        <div class="card-body text-center py-3">
                                            <h4 class="mb-1"><?= $stats['info'] ?></h4>
                                            <small>Info</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="card bg-success text-white">
                                        <div class="card-body text-center py-3">
                                            <h4 class="mb-1"><?= $stats['success'] ?></h4>
                                            <small>Success</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="card bg-secondary text-white">
                                        <div class="card-body text-center py-3">
                                            <h4 class="mb-1"><?= $stats['unread'] ?></h4>
                                            <small>Unread</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Actions -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card border-success">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-plus me-2"></i>Add Sample Notifications
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <p class="mb-3">Tambahkan 10 notifikasi sample dengan berbagai tipe untuk testing sistem.</p>
                                    <form method="POST" onsubmit="return confirm('Yakin ingin menambahkan notifikasi sample?')">
                                        <input type="hidden" name="action" value="add_sample_notifications">
                                        <button type="submit" class="btn btn-success w-100">
                                            <i class="fas fa-plus me-2"></i>Add Sample Notifications
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border-danger">
                                <div class="card-header bg-danger text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-trash me-2"></i>Clear All Notifications
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <p class="mb-3">Hapus semua notifikasi yang ada di sistem (<?= $notificationCount ?> notifikasi).</p>
                                    <form method="POST" onsubmit="return confirm('Yakin ingin menghapus SEMUA notifikasi? Tindakan ini tidak dapat dibatalkan!')">
                                        <input type="hidden" name="action" value="clear_all_notifications">
                                        <button type="submit" class="btn btn-danger w-100" <?= $notificationCount == 0 ? 'disabled' : '' ?>>
                                            <i class="fas fa-trash me-2"></i>Clear All Notifications
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Add Custom Notification -->
                    <div class="row">
                        <div class="col-12">
                            <div class="card border-info">
                                <div class="card-header bg-info text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-edit me-2"></i>Add Custom Notification
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <form method="POST">
                                        <input type="hidden" name="action" value="add_custom_notification">
                                        <div class="row">
                                            <div class="col-md-3">
                                                <div class="mb-3">
                                                    <label class="form-label">Type</label>
                                                    <select class="form-select" name="type" required>
                                                        <option value="info">Info</option>
                                                        <option value="success">Success</option>
                                                        <option value="warning">Warning</option>
                                                        <option value="error">Error</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="mb-3">
                                                    <label class="form-label">Source</label>
                                                    <input type="text" class="form-control" name="source" value="manual" required>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label class="form-label">Title</label>
                                                    <input type="text" class="form-control" name="title" placeholder="Notification title" required>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Message</label>
                                            <textarea class="form-control" name="message" rows="3" placeholder="Notification message" required></textarea>
                                        </div>
                                        <button type="submit" class="btn btn-info">
                                            <i class="fas fa-plus me-2"></i>Add Custom Notification
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Status -->
                    <?php if ($notificationCount == 0): ?>
                        <div class="alert alert-warning mt-4">
                            <h6 class="alert-heading">
                                <i class="fas fa-exclamation-triangle me-2"></i>No Notifications Found
                            </h6>
                            <p class="mb-0">Tidak ada notifikasi di sistem. Klik "Add Sample Notifications" untuk menambahkan notifikasi sample.</p>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-info mt-4">
                            <h6 class="alert-heading">
                                <i class="fas fa-info-circle me-2"></i>Notifications Available
                            </h6>
                            <p class="mb-0">Terdapat <?= $notificationCount ?> notifikasi di sistem. <a href="notifications.php" class="alert-link">Lihat semua notifikasi</a></p>
                        </div>
                    <?php endif; ?>
                    
                    <!-- Navigation -->
                    <div class="mt-4 text-center">
                        <a href="notifications.php" class="btn btn-outline-primary me-2">
                            <i class="fas fa-bell me-2"></i>View All Notifications
                        </a>
                        <a href="check_system_health.php" class="btn btn-outline-success">
                            <i class="fas fa-heartbeat me-2"></i>System Health Check
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/views/layouts/footer.php'; ?>
