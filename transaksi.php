<?php
require_once 'includes/config/database.php';
require_once 'includes/helpers/functions.php';
require_once 'includes/helpers/notifications.php';

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isLoggedIn()) {
    setFlashMessage('danger', 'Silakan login terlebih dahulu');
    redirect('/keuangan/login.php');
}

$currentUser = getCurrentUser();
$currentPage = 'transaksi';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        try {
            switch ($_POST['action']) {
                case 'add':
                    // Validasi input
                    $errors = [];
                    
                    if (empty($_POST['kategori_id'])) {
                        $errors[] = 'Kategori harus dipilih';
                    }
                    
                    if (empty($_POST['jenis'])) {
                        $errors[] = 'Jenis transaksi harus dipilih';
                    }
                    
                    if (empty($_POST['jumlah'])) {
                        $errors[] = 'Jumlah harus diisi';
                    }
                    
                    if (empty($_POST['tanggal'])) {
                        $errors[] = 'Tanggal harus diisi';
                    }
                    
                    if (empty($errors)) {
                        try {
                            // Format jumlah (hapus format angka)
                            $jumlah = str_replace(['.', ','], '', $_POST['jumlah']);
                            
                            // Format tanggal ke format Y-m-d
                            $tanggal = date('Y-m-d', strtotime($_POST['tanggal']));
                            
                            // Cek apakah kategori valid
                            $stmt = $pdo->prepare("SELECT id FROM kategori WHERE id = ? AND (user_id = ? OR user_id IS NULL)");
                            $stmt->execute([$_POST['kategori_id'], $currentUser['id']]);
                            if (!$stmt->fetch()) {
                                throw new Exception('Kategori tidak valid');
                            }
                            
                            // Insert transaksi dengan prepared statement
                            $sql = "INSERT INTO transaksi (user_id, kategori_id, jenis, jumlah, tanggal, keterangan, created_at) 
                                   VALUES (:user_id, :kategori_id, :jenis, :jumlah, :tanggal, :keterangan, NOW())";
                            
                            $stmt = $pdo->prepare($sql);
                            
                            $params = [
                                ':user_id' => $currentUser['id'],
                                ':kategori_id' => $_POST['kategori_id'],
                                ':jenis' => $_POST['jenis'],
                                ':jumlah' => $jumlah,
                                ':tanggal' => $tanggal,
                                ':keterangan' => $_POST['keterangan']
                            ];
                            
                            $result = $stmt->execute($params);
                            
                            if ($result) {
                                    // Log aktivitas
                                    logActivity($currentUser['id'], sprintf(
                                        'Menambahkan transaksi %s sebesar %s',
                                        $_POST['jenis'],
                                        formatRupiah($jumlah)
                                    ));
                                    
                                    setFlashMessage('success', 'Transaksi berhasil ditambahkan');
                                redirect('/transaksi.php');
                            } else {
                                throw new Exception('Gagal menyimpan transaksi');
                            }
                        } catch (Exception $e) {
                            setFlashMessage('danger', $e->getMessage());
                        }
                    } else {
                        setFlashMessage('danger', implode('<br>', $errors));
                    }
                    break;

                case 'update':
                    if (empty($_POST['id'])) {
                        setFlashMessage('danger', 'ID transaksi tidak valid');
                        break;
                    }

                    // Format jumlah (hapus format angka)
                    $jumlah = str_replace(['.', ','], '', $_POST['jumlah']);
                    
                    // Format tanggal ke format Y-m-d
                    $tanggal = date('Y-m-d', strtotime($_POST['tanggal']));
                    
                    $stmt = $pdo->prepare("
                        UPDATE transaksi 
                        SET kategori_id = ?, jenis = ?, jumlah = ?, tanggal = ?, keterangan = ?
                        WHERE id = ? AND user_id = ?
                    ");
                    
                    $result = $stmt->execute([
                        $_POST['kategori_id'],
                        $_POST['jenis'],
                        $jumlah,
                        $tanggal,
                        $_POST['keterangan'],
                        $_POST['id'],
                        $currentUser['id']
                    ]);

                    if ($result) {
                        setFlashMessage('success', 'Transaksi berhasil diperbarui');
                    } else {
                        setFlashMessage('danger', 'Gagal memperbarui transaksi');
                    }
                    break;
            }
        } catch (PDOException $e) {
            setFlashMessage('danger', 'Terjadi kesalahan. Silakan coba lagi.');
        }
        redirect('/transaksi.php');
    }
}

// Get transactions with pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$perPage = 10;
$offset = ($page - 1) * $perPage;

// Get total records with filters
$where = ["t.user_id = ?"];
$params = [$currentUser['id']];

if (!empty($_GET['start_date'])) {
    $where[] = "t.tanggal >= ?";
    $params[] = $_GET['start_date'];
}

if (!empty($_GET['end_date'])) {
    $where[] = "t.tanggal <= ?";
    $params[] = $_GET['end_date'];
}

if (!empty($_GET['kategori_id'])) {
    $where[] = "t.kategori_id = ?";
    $params[] = $_GET['kategori_id'];
}

if (!empty($_GET['jenis'])) {
    $where[] = "t.jenis = ?";
    $params[] = $_GET['jenis'];
}

$whereClause = implode(" AND ", $where);

// Get total records
$stmt = $pdo->prepare("
    SELECT COUNT(*) as total 
    FROM transaksi t
    WHERE $whereClause
");
$stmt->execute($params);
$totalRecords = $stmt->fetch()['total'];
$totalPages = ceil($totalRecords / $perPage);

// Get transactions with category information
$stmt = $pdo->prepare("
    SELECT 
        t.*,
        k.nama as kategori_nama,
        k.tipe as kategori_tipe
    FROM transaksi t
    LEFT JOIN kategori k ON t.kategori_id = k.id
    WHERE $whereClause
    ORDER BY t.tanggal DESC, t.created_at DESC
    LIMIT ? OFFSET ?
");

// Create new array for pagination parameters
$paginationParams = $params;
$paginationParams[] = $perPage;
$paginationParams[] = $offset;

$stmt->execute($paginationParams);
$transaksi = $stmt->fetchAll();

// Get categories for filter
$stmt = $pdo->prepare("
    SELECT * FROM kategori 
    WHERE user_id = ? OR user_id IS NULL 
    ORDER BY nama ASC
");
$stmt->execute([$currentUser['id']]);
$kategori = $stmt->fetchAll();

// Get statistics
$stmt = $pdo->prepare("
    SELECT 
        SUM(CASE WHEN t.jenis = 'pemasukan' THEN t.jumlah ELSE 0 END) as total_pemasukan,
        SUM(CASE WHEN t.jenis = 'pengeluaran' THEN t.jumlah ELSE 0 END) as total_pengeluaran
    FROM transaksi t
    WHERE $whereClause
");
$stmt->execute($params);
$totals = $stmt->fetch();

// Calculate balance
$saldo = ($totals['total_pemasukan'] ?? 0) - ($totals['total_pengeluaran'] ?? 0);

// Include header and sidebar
require_once 'includes/views/layouts/header.php';
require_once 'includes/views/layouts/sidebar.php';
?>

<div class="modern-layout">
    <div class="modern-container">
        <!-- Modern Page Header -->
        <div class="modern-page-header modern-flex modern-justify-between modern-items-center modern-mb-xl">
            <div>
                <h1 class="modern-page-title modern-mb-0">Transaksi</h1>
                <p class="modern-page-subtitle">Kelola semua transaksi keuangan Anda dengan mudah dan efisien</p>
            </div>
            <div class="modern-page-actions">
                <button type="button" class="modern-btn modern-btn-primary" data-bs-toggle="modal" data-bs-target="#addTransactionModal">
                    <i class="fas fa-plus"></i>
                    Tambah Transaksi
                </button>
            </div>
        </div>

        <!-- Flash Messages -->
        <?php if ($flash = getFlashMessage()): ?>
        <div class="modern-alert modern-alert-<?= $flash['type'] ?> modern-mb-lg">
            <div class="modern-alert-content">
                <i class="fas fa-<?= $flash['type'] === 'success' ? 'check-circle' : 'exclamation-triangle' ?> modern-alert-icon"></i>
                <div class="modern-alert-message"><?= $flash['message'] ?></div>
            </div>
            <button type="button" class="modern-alert-close" onclick="this.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <?php endif; ?>

        <!-- Modern Statistics Cards -->
        <div class="modern-grid modern-grid-cols-3 modern-gap-lg modern-mb-xl">
            <div class="modern-stats-card modern-stats-success">
                <div class="modern-stats-content">
                    <div class="modern-stats-info">
                        <div class="modern-stats-label">Total Pemasukan</div>
                        <div class="modern-stats-value"><?= formatRupiah($totals['total_pemasukan'] ?? 0) ?></div>
                        <div class="modern-stats-meta">Pendapatan masuk</div>
                    </div>
                    <div class="modern-stats-icon">
                        <i class="fas fa-arrow-up"></i>
                    </div>
                </div>
                <div class="modern-stats-glow"></div>
            </div>

            <div class="modern-stats-card modern-stats-danger">
                <div class="modern-stats-content">
                    <div class="modern-stats-info">
                        <div class="modern-stats-label">Total Pengeluaran</div>
                        <div class="modern-stats-value"><?= formatRupiah($totals['total_pengeluaran'] ?? 0) ?></div>
                        <div class="modern-stats-meta">Pengeluaran keluar</div>
                    </div>
                    <div class="modern-stats-icon">
                        <i class="fas fa-arrow-down"></i>
                    </div>
                </div>
                <div class="modern-stats-glow"></div>
            </div>

            <div class="modern-stats-card modern-stats-primary">
                <div class="modern-stats-content">
                    <div class="modern-stats-info">
                        <div class="modern-stats-label">Saldo</div>
                        <div class="modern-stats-value"><?= formatRupiah($saldo) ?></div>
                        <div class="modern-stats-meta"><?= $saldo >= 0 ? '✅ Surplus' : '⚠️ Defisit' ?></div>
                    </div>
                    <div class="modern-stats-icon">
                        <i class="fas fa-wallet"></i>
                    </div>
                </div>
                <div class="modern-stats-glow"></div>
            </div>
        </div>

        <!-- Modern Filter Section -->
        <div class="modern-card modern-mb-lg">
            <div class="modern-card-header">
                <h5 class="modern-card-title">
                    <i class="fas fa-filter modern-text-primary modern-mr-sm"></i>
                    Filter Transaksi
                </h5>
            </div>
            <div class="modern-card-body">
                <form action="" method="GET" class="modern-grid modern-grid-cols-4 modern-gap-md modern-items-end">
                    <div class="modern-form-group">
                        <label class="modern-form-label">
                            <i class="fas fa-calendar-alt modern-text-primary"></i>
                            Tanggal Mulai
                        </label>
                        <input type="date" name="start_date" class="modern-form-control" value="<?= $_GET['start_date'] ?? '' ?>">
                    </div>

                    <div class="modern-form-group">
                        <label class="modern-form-label">
                            <i class="fas fa-calendar-check modern-text-primary"></i>
                            Tanggal Akhir
                        </label>
                        <input type="date" name="end_date" class="modern-form-control" value="<?= $_GET['end_date'] ?? '' ?>">
                    </div>

                    <div class="modern-form-group">
                        <label class="modern-form-label">
                            <i class="fas fa-tags modern-text-primary"></i>
                            Kategori
                        </label>
                        <select name="kategori_id" class="modern-form-control">
                            <option value="">🔍 Semua Kategori</option>
                            <?php foreach ($kategori as $k): ?>
                            <option value="<?= $k['id'] ?>" <?= (isset($_GET['kategori_id']) && $_GET['kategori_id'] == $k['id']) ? 'selected' : '' ?>>
                                <?= $k['tipe'] === 'pemasukan' ? '📈' : '📉' ?> <?= htmlspecialchars($k['nama']) ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="modern-form-group">
                        <label class="modern-form-label">
                            <i class="fas fa-exchange-alt modern-text-primary"></i>
                            Jenis
                        </label>
                        <select name="jenis" class="modern-form-control">
                            <option value="">🔍 Semua Jenis</option>
                            <option value="pemasukan" <?= (isset($_GET['jenis']) && $_GET['jenis'] == 'pemasukan') ? 'selected' : '' ?>>📈 Pemasukan</option>
                            <option value="pengeluaran" <?= (isset($_GET['jenis']) && $_GET['jenis'] == 'pengeluaran') ? 'selected' : '' ?>>📉 Pengeluaran</option>
                        </select>
                    </div>

                    <div class="modern-form-group modern-flex modern-gap-sm modern-mb-0">
                        <button type="submit" class="modern-btn modern-btn-primary modern-flex-1">
                            <i class="fas fa-search"></i>
                            Filter
                        </button>
                        <a href="transaksi.php" class="modern-btn modern-btn-light">
                            <i class="fas fa-sync"></i>
                            Reset
                        </a>
                    </div>
                </form>
            </div>
        </div>

                <!-- Transactions Table -->
    <div class="card border-0 shadow-sm">
                    <div class="card-body">
                        <div class="table-responsive">
                <table class="table table-hover align-middle">
                    <thead>
                                    <tr>
                                        <th>Tanggal</th>
                                        <th>Kategori</th>
                                        <th>Keterangan</th>
                                        <th class="text-end">Jumlah</th>
                                        <th class="text-center">Aksi</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (empty($transaksi)): ?>
                                    <tr>
                            <td colspan="5" class="text-center py-5">
                                <div class="text-muted">
                                    <i class="fas fa-inbox fa-3x mb-3"></i>
                                    <p class="mb-0">Belum ada transaksi</p>
                                </div>
                            </td>
                                    </tr>
                                    <?php else: ?>
                                    <?php foreach ($transaksi as $t): ?>
                                    <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="icon-container primary">
                                        <i class="fas fa-calendar-alt"></i>
                                    </div>
                                    <?= formatTanggal($t['tanggal']) ?>
                                </div>
                            </td>
                                        <td>
                                <span class="badge bg-<?= $t['kategori_tipe'] === 'pemasukan' ? 'success' : 'danger' ?> bg-opacity-10 text-<?= $t['kategori_tipe'] === 'pemasukan' ? 'success' : 'danger' ?>">
                                    <i class="fas fa-<?= $t['kategori_tipe'] === 'pemasukan' ? 'arrow-up' : 'arrow-down' ?> me-1"></i>
                                                <?= htmlspecialchars($t['kategori_nama']) ?>
                                            </span>
                                        </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="icon-container success">
                                        <i class="fas fa-info-circle"></i>
                                    </div>
                                    <?= htmlspecialchars($t['keterangan']) ?>
                                </div>
                            </td>
                            <td class="text-end">
                                <span class="text-<?= $t['kategori_tipe'] === 'pemasukan' ? 'success' : 'danger' ?> fw-bold">
                                            <?= $t['kategori_tipe'] === 'pemasukan' ? '+' : '-' ?>
                                            <?= formatRupiah($t['jumlah']) ?>
                                </span>
                                        </td>
                                        <td class="text-center">
                                <div class="btn-group">
                                    <button type="button" class="btn btn-sm btn-light" onclick="editTransaction(
                                                '<?= $t['id'] ?>',
                                                '<?= $t['kategori_id'] ?>',
                                                '<?= $t['jenis'] ?>',
                                                '<?= $t['jumlah'] ?>',
                                                '<?= date('Y-m-d', strtotime($t['tanggal'])) ?>',
                                                '<?= htmlspecialchars($t['keterangan']) ?>'
                                            )">
                                        <i class="fas fa-edit text-primary"></i>
                                            </button>
                                    <button type="button" class="btn btn-sm btn-light" onclick="deleteTransaction(<?= $t['id'] ?>)">
                                        <i class="fas fa-trash text-danger"></i>
                                            </button>
                                </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <?php if ($totalPages > 1): ?>
                        <nav class="mt-4">
                            <ul class="pagination justify-content-center">
                                <?php if ($page > 1): ?>
                                <li class="page-item">
                        <a class="page-link" href="?page=<?= $page - 1 ?><?= isset($_GET['kategori_id']) ? '&kategori_id='.$_GET['kategori_id'] : '' ?><?= isset($_GET['jenis']) ? '&jenis='.$_GET['jenis'] : '' ?>">
                                        <i class="fas fa-chevron-left"></i>
                                    </a>
                                </li>
                                <?php endif; ?>
                                
                                <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                                <li class="page-item <?= $i === $page ? 'active' : '' ?>">
                        <a class="page-link" href="?page=<?= $i ?><?= isset($_GET['kategori_id']) ? '&kategori_id='.$_GET['kategori_id'] : '' ?><?= isset($_GET['jenis']) ? '&jenis='.$_GET['jenis'] : '' ?>"><?= $i ?></a>
                                </li>
                                <?php endfor; ?>
                                
                                <?php if ($page < $totalPages): ?>
                                <li class="page-item">
                        <a class="page-link" href="?page=<?= $page + 1 ?><?= isset($_GET['kategori_id']) ? '&kategori_id='.$_GET['kategori_id'] : '' ?><?= isset($_GET['jenis']) ? '&jenis='.$_GET['jenis'] : '' ?>">
                                        <i class="fas fa-chevron-right"></i>
                                    </a>
                                </li>
                                <?php endif; ?>
                            </ul>
                        </nav>
                        <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Add Transaction Modal -->
    <div class="modal fade" id="addTransactionModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
            <div class="modal-header border-0">
                    <h5 class="modal-title">Tambah Transaksi</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form action="" method="POST" class="needs-validation" novalidate>
                    <div class="modal-body">
                        <input type="hidden" name="action" value="add">
                        
                        <div class="mb-3">
                            <label class="form-label">Kategori</label>
                            <select name="kategori_id" class="form-select select2" required>
                                <option value="">Pilih Kategori</option>
                                <?php foreach ($kategori as $k): ?>
                                <option value="<?= $k['id'] ?>">
                                    <?= htmlspecialchars($k['nama']) ?> 
                                    (<?= $k['tipe'] === 'pemasukan' ? 'Pemasukan' : 'Pengeluaran' ?>)
                                </option>
                                <?php endforeach; ?>
                            </select>
                            <div class="invalid-feedback">Kategori harus dipilih</div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Jenis</label>
                            <select name="jenis" class="form-select" required>
                                <option value="">Pilih Jenis</option>
                                <option value="pemasukan">Pemasukan</option>
                                <option value="pengeluaran">Pengeluaran</option>
                            </select>
                            <div class="invalid-feedback">Jenis transaksi harus dipilih</div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Jumlah</label>
                            <div class="input-group">
                                <span class="input-group-text">Rp</span>
                                <input type="text" name="jumlah" class="form-control number-format" required>
                            </div>
                            <div class="invalid-feedback">Jumlah harus diisi</div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Tanggal</label>
                            <input type="date" name="tanggal" class="form-control" required 
                                   value="<?= date('Y-m-d') ?>">
                            <div class="invalid-feedback">Tanggal harus diisi</div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Keterangan</label>
                            <textarea name="keterangan" class="form-control" rows="3"></textarea>
                        </div>
                    </div>
                <div class="modal-footer border-0">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">Batal</button>
                        <button type="submit" class="btn btn-primary">Simpan</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Edit Transaction Modal -->
    <div class="modal fade" id="editTransactionModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
            <div class="modal-header border-0">
                    <h5 class="modal-title">Edit Transaksi</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form action="transaksi.php" method="POST">
                    <input type="hidden" name="action" value="update">
                    <input type="hidden" name="id" id="edit_id">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label class="form-label">Kategori</label>
                            <select name="kategori_id" class="form-select" required>
                                <?php foreach ($kategori as $k): ?>
                                <option value="<?= $k['id'] ?>"><?= htmlspecialchars($k['nama']) ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Jenis</label>
                            <select name="jenis" class="form-select" required>
                                <option value="pemasukan">Pemasukan</option>
                                <option value="pengeluaran">Pengeluaran</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Jumlah</label>
                        <div class="input-group">
                            <span class="input-group-text">Rp</span>
                            <input type="text" name="jumlah" class="form-control number-format" required>
                        </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Tanggal</label>
                            <input type="date" name="tanggal" class="form-control" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Keterangan</label>
                            <textarea name="keterangan" class="form-control" rows="3"></textarea>
                        </div>
                    </div>
                <div class="modal-footer border-0">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">Batal</button>
                        <button type="submit" class="btn btn-primary">Simpan</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

<?php include 'includes/views/layouts/footer.php'; ?>

<style>
/* Custom styles */
.card {
    transition: all 0.3s ease;
    background: linear-gradient(145deg, #ffffff, #f8f9fa);
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

/* Gradient backgrounds for stat cards */
.card:nth-child(1) {
    background: linear-gradient(145deg, #ffffff, #e8f5e9);
    border-left: 4px solid #4caf50;
}

.card:nth-child(2) {
    background: linear-gradient(145deg, #ffffff, #ffebee);
    border-left: 4px solid #f44336;
}

.card:nth-child(3) {
    background: linear-gradient(145deg, #ffffff, #e3f2fd);
    border-left: 4px solid #2196f3;
}

/* Text colors for stat cards */
.card:nth-child(1) h6 {
    color: #2e7d32;
}

.card:nth-child(1) h3 {
    color: #1b5e20;
}

.card:nth-child(2) h6 {
    color: #c62828;
}

.card:nth-child(2) h3 {
    color: #b71c1c;
}

.card:nth-child(3) h6 {
    color: #1565c0;
}

.card:nth-child(3) h3 {
    color: #0d47a1;
}

.table > :not(caption) > * > * {
    padding: 1rem;
}

.table th {
    color: #1a237e;
    font-weight: 600;
}

.table td {
    color: #37474f;
}

.badge {
    padding: 0.5rem 0.75rem;
    font-weight: 500;
}

.badge.bg-success {
    background: linear-gradient(45deg, #4caf50, #8bc34a) !important;
    color: #fff !important;
}

.badge.bg-danger {
    background: linear-gradient(45deg, #f44336, #ff5252) !important;
    color: #fff !important;
}

.btn-group .btn {
    padding: 0.5rem;
}

.btn-group .btn:hover {
    background-color: var(--bs-light);
}

.btn-primary {
    background: linear-gradient(45deg, #2196f3, #1976d2);
    border: none;
    color: #fff;
}

.btn-primary:hover {
    background: linear-gradient(45deg, #1976d2, #1565c0);
    transform: translateY(-1px);
    color: #fff;
}

.btn-light {
    color: #37474f;
}

.modal-content {
    border: none;
    border-radius: 1rem;
    background: linear-gradient(145deg, #ffffff, #f8f9fa);
}

.modal-header {
    padding: 1.5rem;
    background: linear-gradient(45deg, #f8f9fa, #ffffff);
    border-bottom: 1px solid rgba(0,0,0,0.05);
}

.modal-title {
    color: #1a237e;
    font-weight: 600;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    padding: 1.5rem;
    background: linear-gradient(45deg, #ffffff, #f8f9fa);
    border-top: 1px solid rgba(0,0,0,0.05);
}

.form-label {
    color: #37474f;
    font-weight: 500;
}

.form-control, .form-select {
    padding: 0.625rem 1rem;
    border-radius: 0.5rem;
    border: 1px solid rgba(0,0,0,0.1);
    background: linear-gradient(145deg, #ffffff, #f8f9fa);
    color: #37474f;
}

.form-control:focus, .form-select:focus {
    box-shadow: 0 0 0 0.25rem rgba(33, 150, 243, 0.1);
    border-color: #2196f3;
    color: #1a237e;
}

.input-group-text {
    border-radius: 0.5rem 0 0 0.5rem;
    background: linear-gradient(145deg, #f8f9fa, #ffffff);
    border: 1px solid rgba(0,0,0,0.1);
    color: #37474f;
}

.pagination {
    margin-bottom: 0;
}

.page-link {
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    margin: 0 0.25rem;
    border: none;
    color: #2196f3;
    background: linear-gradient(145deg, #ffffff, #f8f9fa);
}

.page-item.active .page-link {
    background: linear-gradient(45deg, #2196f3, #1976d2);
    color: white;
}

.page-link:hover {
    background: linear-gradient(145deg, #e3f2fd, #bbdefb);
    color: #1976d2;
}

/* Icon containers */
.icon-container {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
}

.icon-container.primary {
    background: linear-gradient(45deg, #e3f2fd, #bbdefb);
    color: #1976d2;
}

.icon-container.success {
    background: linear-gradient(45deg, #e8f5e9, #c8e6c9);
    color: #2e7d32;
}

.icon-container.danger {
    background: linear-gradient(45deg, #ffebee, #ffcdd2);
    color: #c62828;
}

/* Table row hover effect */
.table tbody tr {
    transition: all 0.2s ease;
}

.table tbody tr:hover {
    background: linear-gradient(145deg, #f8f9fa, #ffffff);
    transform: scale(1.01);
}

/* Dark mode support */
[data-bs-theme="dark"] .card {
    background: linear-gradient(145deg, #2d3748, #1a202c);
    border-color: rgba(255,255,255,0.1);
}

[data-bs-theme="dark"] .card:nth-child(1) {
    background: linear-gradient(145deg, #2d3748, #1a202c);
    border-left: 4px solid #4caf50;
}

[data-bs-theme="dark"] .card:nth-child(2) {
    background: linear-gradient(145deg, #2d3748, #1a202c);
    border-left: 4px solid #f44336;
}

[data-bs-theme="dark"] .card:nth-child(3) {
    background: linear-gradient(145deg, #2d3748, #1a202c);
    border-left: 4px solid #2196f3;
}

[data-bs-theme="dark"] .card:nth-child(1) h6 {
    color: #81c784;
}

[data-bs-theme="dark"] .card:nth-child(1) h3 {
    color: #a5d6a7;
}

[data-bs-theme="dark"] .card:nth-child(2) h6 {
    color: #e57373;
}

[data-bs-theme="dark"] .card:nth-child(2) h3 {
    color: #ef9a9a;
}

[data-bs-theme="dark"] .card:nth-child(3) h6 {
    color: #64b5f6;
}

[data-bs-theme="dark"] .card:nth-child(3) h3 {
    color: #90caf9;
}

[data-bs-theme="dark"] .table th {
    color: #e3f2fd;
}

[data-bs-theme="dark"] .table td {
    color: #cfd8dc;
}

[data-bs-theme="dark"] .bg-light {
    background: rgba(255, 255, 255, 0.1) !important;
}

[data-bs-theme="dark"] .text-muted {
    color: rgba(255, 255, 255, 0.6) !important;
}

[data-bs-theme="dark"] .table {
    color: var(--bs-body-color);
}

[data-bs-theme="dark"] .table > :not(caption) > * > * {
    border-bottom-color: rgba(255,255,255,0.1);
}

[data-bs-theme="dark"] .modal-content {
    background: linear-gradient(145deg, #2d3748, #1a202c);
}

[data-bs-theme="dark"] .modal-title {
    color: #e3f2fd;
}

[data-bs-theme="dark"] .form-label {
    color: #cfd8dc;
}

[data-bs-theme="dark"] .form-control,
[data-bs-theme="dark"] .form-select {
    background: linear-gradient(145deg, #2d3748, #1a202c);
    border-color: rgba(255,255,255,0.1);
    color: #cfd8dc;
}

[data-bs-theme="dark"] .input-group-text {
    background: linear-gradient(145deg, #2d3748, #1a202c);
    border-color: rgba(255,255,255,0.1);
    color: #cfd8dc;
}

[data-bs-theme="dark"] .table tbody tr:hover {
    background: linear-gradient(145deg, #2d3748, #1a202c);
}

[data-bs-theme="dark"] .icon-container.primary {
    background: linear-gradient(45deg, #1a202c, #2d3748);
    color: #64b5f6;
}

[data-bs-theme="dark"] .icon-container.success {
    background: linear-gradient(45deg, #1a202c, #2d3748);
    color: #81c784;
}

[data-bs-theme="dark"] .icon-container.danger {
    background: linear-gradient(45deg, #1a202c, #2d3748);
    color: #e57373;
}

[data-bs-theme="dark"] .btn-light {
    color: #e3f2fd;
}
</style>

<script>
// Format currency input
function formatCurrency(input) {
    let value = input.value.replace(/\D/g, '');
    value = parseInt(value, 10);
    if (isNaN(value)) value = 0;
    input.value = value.toLocaleString('id-ID');
                }

// Edit transaction
function editTransaction(id, kategoriId, jenis, jumlah, tanggal, keterangan) {
    document.getElementById('edit_id').value = id;
    document.querySelector('select[name="kategori_id"]').value = kategoriId;
    document.querySelector('select[name="jenis"]').value = jenis;
    document.querySelector('input[name="jumlah"]').value = jumlah;
    document.querySelector('input[name="tanggal"]').value = tanggal;
    document.querySelector('textarea[name="keterangan"]').value = keterangan;
    
    new bootstrap.Modal(document.getElementById('editTransactionModal')).show();
}

// Delete transaction with modern popup
function deleteTransaction(id) {
                Swal.fire({
                    title: 'Apakah Anda yakin?',
        text: "Data transaksi yang dihapus tidak dapat dikembalikan!",
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#d33',
                    cancelButtonColor: '#3085d6',
                    confirmButtonText: 'Ya, hapus!',
        cancelButtonText: 'Batal',
        reverseButtons: true,
        customClass: {
            confirmButton: 'btn btn-danger',
            cancelButton: 'btn btn-secondary'
        },
        buttonsStyling: true
                }).then((result) => {
                    if (result.isConfirmed) {
            // Tampilkan loading
            Swal.fire({
                title: 'Menghapus...',
                text: 'Mohon tunggu sebentar',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            // Redirect ke halaman hapus
            window.location.href = 'hapus-transaksi.php?id=' + id;
                    }
                });
}

// Initialize currency formatters
document.querySelectorAll('input[name="jumlah"]').forEach(input => {
    input.addEventListener('input', function() {
        formatCurrency(this);
            });
        });

// Tampilkan notifikasi flash message dengan SweetAlert2
        <?php if ($flash = getFlashMessage()): ?>
        Swal.fire({
            icon: '<?= $flash['type'] === 'success' ? 'success' : 'error' ?>',
    title: '<?= $flash['type'] === 'success' ? 'Berhasil!' : 'Error!' ?>',
            text: '<?= $flash['message'] ?>',
    timer: 3000,
    timerProgressBar: true,
    showConfirmButton: false
        });
        <?php endif; ?>
    </script>