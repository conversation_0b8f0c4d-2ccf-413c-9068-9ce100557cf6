<?php
require_once 'includes/config/database.php';
require_once 'includes/helpers/functions.php';
require_once 'includes/helpers/notifications.php';

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isLoggedIn()) {
    setFlashMessage('danger', 'Silakan login terlebih dahulu');
    redirect('/keuangan/login.php');
}

$currentUser = getCurrentUser();
$currentPage = 'transaksi';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        try {
            switch ($_POST['action']) {
                case 'add':
                    // Validasi input
                    $errors = [];
                    
                    if (empty($_POST['kategori_id'])) {
                        $errors[] = 'Kategori harus dipilih';
                    }
                    
                    if (empty($_POST['jenis'])) {
                        $errors[] = 'Jenis transaksi harus dipilih';
                    }
                    
                    if (empty($_POST['jumlah'])) {
                        $errors[] = 'Jumlah harus diisi';
                    }
                    
                    if (empty($_POST['tanggal'])) {
                        $errors[] = 'Tanggal harus diisi';
                    }
                    
                    if (empty($errors)) {
                        try {
                            // Format jumlah (hapus format angka)
                            $jumlah = str_replace(['.', ','], '', $_POST['jumlah']);
                            
                            // Format tanggal ke format Y-m-d
                            $tanggal = date('Y-m-d', strtotime($_POST['tanggal']));
                            
                            // Cek apakah kategori valid
                            $stmt = $pdo->prepare("SELECT id FROM kategori WHERE id = ? AND (user_id = ? OR user_id IS NULL)");
                            $stmt->execute([$_POST['kategori_id'], $currentUser['id']]);
                            if (!$stmt->fetch()) {
                                throw new Exception('Kategori tidak valid');
                            }
                            
                            // Insert transaksi dengan prepared statement
                            $sql = "INSERT INTO transaksi (user_id, kategori_id, jenis, jumlah, tanggal, keterangan, created_at) 
                                   VALUES (:user_id, :kategori_id, :jenis, :jumlah, :tanggal, :keterangan, NOW())";
                            
                            $stmt = $pdo->prepare($sql);
                            
                            $params = [
                                ':user_id' => $currentUser['id'],
                                ':kategori_id' => $_POST['kategori_id'],
                                ':jenis' => $_POST['jenis'],
                                ':jumlah' => $jumlah,
                                ':tanggal' => $tanggal,
                                ':keterangan' => $_POST['keterangan']
                            ];
                            
                            $result = $stmt->execute($params);
                            
                            if ($result) {
                                    // Log aktivitas
                                    logActivity($currentUser['id'], sprintf(
                                        'Menambahkan transaksi %s sebesar %s',
                                        $_POST['jenis'],
                                        formatRupiah($jumlah)
                                    ));
                                    
                                    setFlashMessage('success', 'Transaksi berhasil ditambahkan');
                                redirect('/transaksi.php');
                            } else {
                                throw new Exception('Gagal menyimpan transaksi');
                            }
                        } catch (Exception $e) {
                            setFlashMessage('danger', $e->getMessage());
                        }
                    } else {
                        setFlashMessage('danger', implode('<br>', $errors));
                    }
                    break;

                case 'update':
                    if (empty($_POST['id'])) {
                        setFlashMessage('danger', 'ID transaksi tidak valid');
                        break;
                    }

                    // Format jumlah (hapus format angka)
                    $jumlah = str_replace(['.', ','], '', $_POST['jumlah']);
                    
                    // Format tanggal ke format Y-m-d
                    $tanggal = date('Y-m-d', strtotime($_POST['tanggal']));
                    
                    $stmt = $pdo->prepare("
                        UPDATE transaksi 
                        SET kategori_id = ?, jenis = ?, jumlah = ?, tanggal = ?, keterangan = ?
                        WHERE id = ? AND user_id = ?
                    ");
                    
                    $result = $stmt->execute([
                        $_POST['kategori_id'],
                        $_POST['jenis'],
                        $jumlah,
                        $tanggal,
                        $_POST['keterangan'],
                        $_POST['id'],
                        $currentUser['id']
                    ]);

                    if ($result) {
                        setFlashMessage('success', 'Transaksi berhasil diperbarui');
                    } else {
                        setFlashMessage('danger', 'Gagal memperbarui transaksi');
                    }
                    break;
            }
        } catch (PDOException $e) {
            setFlashMessage('danger', 'Terjadi kesalahan. Silakan coba lagi.');
        }
        redirect('/transaksi.php');
    }
}

// Get transactions with pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$perPage = 10;
$offset = ($page - 1) * $perPage;

// Get total records with filters
$where = ["t.user_id = ?"];
$params = [$currentUser['id']];

if (!empty($_GET['start_date'])) {
    $where[] = "t.tanggal >= ?";
    $params[] = $_GET['start_date'];
}

if (!empty($_GET['end_date'])) {
    $where[] = "t.tanggal <= ?";
    $params[] = $_GET['end_date'];
}

if (!empty($_GET['kategori_id'])) {
    $where[] = "t.kategori_id = ?";
    $params[] = $_GET['kategori_id'];
}

if (!empty($_GET['jenis'])) {
    $where[] = "t.jenis = ?";
    $params[] = $_GET['jenis'];
}

$whereClause = implode(" AND ", $where);

// Get total records
$stmt = $pdo->prepare("
    SELECT COUNT(*) as total 
    FROM transaksi t
    WHERE $whereClause
");
$stmt->execute($params);
$totalRecords = $stmt->fetch()['total'];
$totalPages = ceil($totalRecords / $perPage);

// Get transactions with category information
$stmt = $pdo->prepare("
    SELECT 
        t.*,
        k.nama as kategori_nama,
        k.tipe as kategori_tipe
    FROM transaksi t
    LEFT JOIN kategori k ON t.kategori_id = k.id
    WHERE $whereClause
    ORDER BY t.tanggal DESC, t.created_at DESC
    LIMIT ? OFFSET ?
");

// Create new array for pagination parameters
$paginationParams = $params;
$paginationParams[] = $perPage;
$paginationParams[] = $offset;

$stmt->execute($paginationParams);
$transaksi = $stmt->fetchAll();

// Get categories for filter
$stmt = $pdo->prepare("
    SELECT * FROM kategori 
    WHERE user_id = ? OR user_id IS NULL 
    ORDER BY nama ASC
");
$stmt->execute([$currentUser['id']]);
$kategori = $stmt->fetchAll();

// Get statistics
$stmt = $pdo->prepare("
    SELECT 
        SUM(CASE WHEN t.jenis = 'pemasukan' THEN t.jumlah ELSE 0 END) as total_pemasukan,
        SUM(CASE WHEN t.jenis = 'pengeluaran' THEN t.jumlah ELSE 0 END) as total_pengeluaran
    FROM transaksi t
    WHERE $whereClause
");
$stmt->execute($params);
$totals = $stmt->fetch();

// Calculate balance
$saldo = ($totals['total_pemasukan'] ?? 0) - ($totals['total_pengeluaran'] ?? 0);

// Include header and sidebar
require_once 'includes/views/layouts/header.php';
require_once 'includes/views/layouts/sidebar.php';
?>

<div class="modern-layout">
    <div class="modern-container">
        <!-- Modern Page Header -->
        <div class="modern-page-header modern-flex modern-justify-between modern-items-center modern-mb-xl">
            <div>
                <h1 class="modern-page-title modern-mb-0">Transaksi</h1>
                <p class="modern-page-subtitle">Kelola semua transaksi keuangan Anda dengan mudah dan efisien</p>
            </div>
            <div class="modern-page-actions">
                <button type="button" class="modern-btn modern-btn-primary" data-bs-toggle="modal" data-bs-target="#addTransactionModal">
                    <i class="fas fa-plus"></i>
                    Tambah Transaksi
                </button>
            </div>
        </div>

        <!-- Flash Messages -->
        <?php if ($flash = getFlashMessage()): ?>
        <div class="modern-alert modern-alert-<?= $flash['type'] ?> modern-mb-lg">
            <div class="modern-alert-content">
                <i class="fas fa-<?= $flash['type'] === 'success' ? 'check-circle' : 'exclamation-triangle' ?> modern-alert-icon"></i>
                <div class="modern-alert-message"><?= $flash['message'] ?></div>
            </div>
            <button type="button" class="modern-alert-close" onclick="this.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <?php endif; ?>

        <!-- Modern Statistics Cards -->
        <div class="modern-grid modern-grid-cols-3 modern-gap-lg modern-mb-xl">
            <div class="modern-stats-card modern-stats-success">
                <div class="modern-stats-content">
                    <div class="modern-stats-info">
                        <div class="modern-stats-label">Total Pemasukan</div>
                        <div class="modern-stats-value"><?= formatRupiah($totals['total_pemasukan'] ?? 0) ?></div>
                        <div class="modern-stats-meta">Pendapatan masuk</div>
                    </div>
                    <div class="modern-stats-icon">
                        <i class="fas fa-arrow-up"></i>
                    </div>
                </div>
                <div class="modern-stats-glow"></div>
            </div>

            <div class="modern-stats-card modern-stats-danger">
                <div class="modern-stats-content">
                    <div class="modern-stats-info">
                        <div class="modern-stats-label">Total Pengeluaran</div>
                        <div class="modern-stats-value"><?= formatRupiah($totals['total_pengeluaran'] ?? 0) ?></div>
                        <div class="modern-stats-meta">Pengeluaran keluar</div>
                    </div>
                    <div class="modern-stats-icon">
                        <i class="fas fa-arrow-down"></i>
                    </div>
                </div>
                <div class="modern-stats-glow"></div>
            </div>

            <div class="modern-stats-card modern-stats-primary">
                <div class="modern-stats-content">
                    <div class="modern-stats-info">
                        <div class="modern-stats-label">Saldo</div>
                        <div class="modern-stats-value"><?= formatRupiah($saldo) ?></div>
                        <div class="modern-stats-meta"><?= $saldo >= 0 ? '✅ Surplus' : '⚠️ Defisit' ?></div>
                    </div>
                    <div class="modern-stats-icon">
                        <i class="fas fa-wallet"></i>
                    </div>
                </div>
                <div class="modern-stats-glow"></div>
            </div>
        </div>

        <!-- Modern Filter Section -->
        <div class="modern-card modern-mb-lg">
            <div class="modern-card-header">
                <h5 class="modern-card-title">
                    <i class="fas fa-filter modern-text-primary modern-mr-sm"></i>
                    Filter Transaksi
                </h5>
            </div>
            <div class="modern-card-body">
                <form action="" method="GET" class="modern-grid modern-grid-cols-4 modern-gap-md modern-items-end">
                    <div class="modern-form-group">
                        <label class="modern-form-label">
                            <i class="fas fa-calendar-alt modern-text-primary"></i>
                            Tanggal Mulai
                        </label>
                        <input type="date" name="start_date" class="modern-form-control" value="<?= $_GET['start_date'] ?? '' ?>">
                    </div>

                    <div class="modern-form-group">
                        <label class="modern-form-label">
                            <i class="fas fa-calendar-check modern-text-primary"></i>
                            Tanggal Akhir
                        </label>
                        <input type="date" name="end_date" class="modern-form-control" value="<?= $_GET['end_date'] ?? '' ?>">
                    </div>

                    <div class="modern-form-group">
                        <label class="modern-form-label">
                            <i class="fas fa-tags modern-text-primary"></i>
                            Kategori
                        </label>
                        <select name="kategori_id" class="modern-form-control">
                            <option value="">🔍 Semua Kategori</option>
                            <?php foreach ($kategori as $k): ?>
                            <option value="<?= $k['id'] ?>" <?= (isset($_GET['kategori_id']) && $_GET['kategori_id'] == $k['id']) ? 'selected' : '' ?>>
                                <?= $k['tipe'] === 'pemasukan' ? '📈' : '📉' ?> <?= htmlspecialchars($k['nama']) ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="modern-form-group">
                        <label class="modern-form-label">
                            <i class="fas fa-exchange-alt modern-text-primary"></i>
                            Jenis
                        </label>
                        <select name="jenis" class="modern-form-control">
                            <option value="">🔍 Semua Jenis</option>
                            <option value="pemasukan" <?= (isset($_GET['jenis']) && $_GET['jenis'] == 'pemasukan') ? 'selected' : '' ?>>📈 Pemasukan</option>
                            <option value="pengeluaran" <?= (isset($_GET['jenis']) && $_GET['jenis'] == 'pengeluaran') ? 'selected' : '' ?>>📉 Pengeluaran</option>
                        </select>
                    </div>

                    <div class="modern-form-group modern-flex modern-gap-sm modern-mb-0">
                        <button type="submit" class="modern-btn modern-btn-primary modern-flex-1">
                            <i class="fas fa-search"></i>
                            Filter
                        </button>
                        <a href="transaksi.php" class="modern-btn modern-btn-light">
                            <i class="fas fa-sync"></i>
                            Reset
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- Modern Transactions Table -->
        <div class="modern-card">
            <div class="modern-card-header">
                <h5 class="modern-card-title">
                    <i class="fas fa-list modern-text-primary modern-mr-sm"></i>
                    Daftar Transaksi
                </h5>
                <div class="modern-card-actions">
                    <span class="modern-badge modern-badge-info">
                        Total: <?= count($transaksi) ?> transaksi
                    </span>
                </div>
            </div>
            <div class="modern-card-body modern-p-0">
                <div class="modern-table-responsive">
                    <table class="modern-table">
                        <thead class="modern-table-header">
                            <tr>
                                <th class="modern-table-th">
                                    <i class="fas fa-calendar modern-mr-xs"></i>
                                    Tanggal
                                </th>
                                <th class="modern-table-th">
                                    <i class="fas fa-tags modern-mr-xs"></i>
                                    Kategori
                                </th>
                                <th class="modern-table-th">
                                    <i class="fas fa-info-circle modern-mr-xs"></i>
                                    Keterangan
                                </th>
                                <th class="modern-table-th modern-text-end">
                                    <i class="fas fa-money-bill modern-mr-xs"></i>
                                    Jumlah
                                </th>
                                <th class="modern-table-th modern-text-center">
                                    <i class="fas fa-cogs modern-mr-xs"></i>
                                    Aksi
                                </th>
                            </tr>
                        </thead>
                        <tbody class="modern-table-body">
                            <?php if (empty($transaksi)): ?>
                            <tr>
                                <td colspan="5" class="modern-table-td">
                                    <div class="modern-empty-state">
                                        <div class="modern-empty-icon">
                                            <i class="fas fa-receipt"></i>
                                        </div>
                                        <div class="modern-empty-content">
                                            <h6 class="modern-empty-title">Belum Ada Transaksi</h6>
                                            <p class="modern-empty-text">Mulai catat transaksi keuangan Anda</p>
                                            <button type="button" class="modern-btn modern-btn-primary modern-btn-sm" data-bs-toggle="modal" data-bs-target="#addTransactionModal">
                                                <i class="fas fa-plus"></i>
                                                Tambah Transaksi Pertama
                                            </button>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <?php else: ?>
                            <?php foreach ($transaksi as $t): ?>
                            <tr class="modern-table-row">
                                <td class="modern-table-td">
                                    <div class="modern-transaction-date">
                                        <div class="modern-date-icon">
                                            <i class="fas fa-calendar-alt"></i>
                                        </div>
                                        <div class="modern-date-info">
                                            <div class="modern-date-main"><?= formatDate($t['tanggal']) ?></div>
                                            <div class="modern-date-time"><?= date('H:i', strtotime($t['created_at'] ?? $t['tanggal'])) ?></div>
                                        </div>
                                    </div>
                                </td>
                                <td class="modern-table-td">
                                    <div class="modern-badge modern-badge-<?= $t['kategori_tipe'] === 'pemasukan' ? 'success' : 'danger' ?>">
                                        <?php if ($t['kategori_tipe'] === 'pemasukan'): ?>
                                            <i class="fas fa-arrow-up"></i>
                                        <?php else: ?>
                                            <i class="fas fa-arrow-down"></i>
                                        <?php endif; ?>
                                        <?= htmlspecialchars($t['kategori_nama']) ?>
                                    </div>
                                </td>
                                <td class="modern-table-td">
                                    <div class="modern-transaction-desc">
                                        <div class="modern-desc-text"><?= htmlspecialchars($t['keterangan']) ?></div>
                                        <?php if (strlen($t['keterangan']) > 50): ?>
                                            <div class="modern-desc-tooltip" title="<?= htmlspecialchars($t['keterangan']) ?>">
                                                <i class="fas fa-info-circle"></i>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td class="modern-table-td modern-text-end">
                                    <div class="modern-amount modern-amount-<?= $t['kategori_tipe'] === 'pemasukan' ? 'income' : 'expense' ?>">
                                        <span class="modern-amount-symbol">
                                            <?= $t['kategori_tipe'] === 'pemasukan' ? '+' : '-' ?>
                                        </span>
                                        <span class="modern-amount-value">
                                            <?= formatRupiah($t['jumlah']) ?>
                                        </span>
                                    </div>
                                </td>
                                <td class="modern-table-td modern-text-center">
                                    <div class="modern-action-buttons">
                                        <button type="button" class="modern-btn modern-btn-light modern-btn-sm"
                                                onclick="editTransaction(
                                                    '<?= $t['id'] ?>',
                                                    '<?= $t['kategori_id'] ?>',
                                                    '<?= $t['jenis'] ?>',
                                                    '<?= $t['jumlah'] ?>',
                                                    '<?= date('Y-m-d', strtotime($t['tanggal'])) ?>',
                                                    '<?= htmlspecialchars($t['keterangan']) ?>'
                                                )" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button type="button" class="modern-btn modern-btn-danger modern-btn-sm"
                                                onclick="deleteTransaction(<?= $t['id'] ?>)" title="Hapus">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Modern Pagination -->
                <?php if ($totalPages > 1): ?>
                <div class="modern-card-footer">
                    <div class="modern-pagination-wrapper">
                        <div class="modern-pagination-info">
                            <span class="modern-text-muted">
                                Halaman <?= $page ?> dari <?= $totalPages ?>
                                (<?= $totalRecords ?> total transaksi)
                            </span>
                        </div>
                        <nav class="modern-pagination">
                            <?php if ($page > 1): ?>
                            <a class="modern-pagination-btn modern-pagination-prev"
                               href="?page=<?= $page - 1 ?>&<?= http_build_query(array_filter($_GET, function($key) { return $key !== 'page'; }, ARRAY_FILTER_USE_KEY)) ?>">
                                <i class="fas fa-chevron-left"></i>
                                Sebelumnya
                            </a>
                            <?php endif; ?>

                            <div class="modern-pagination-numbers">
                                <?php
                                $start = max(1, $page - 2);
                                $end = min($totalPages, $page + 2);

                                if ($start > 1): ?>
                                    <a class="modern-pagination-number" href="?page=1&<?= http_build_query(array_filter($_GET, function($key) { return $key !== 'page'; }, ARRAY_FILTER_USE_KEY)) ?>">1</a>
                                    <?php if ($start > 2): ?>
                                        <span class="modern-pagination-dots">...</span>
                                    <?php endif; ?>
                                <?php endif; ?>

                                <?php for ($i = $start; $i <= $end; $i++): ?>
                                <a class="modern-pagination-number <?= $i === $page ? 'active' : '' ?>"
                                   href="?page=<?= $i ?>&<?= http_build_query(array_filter($_GET, function($key) { return $key !== 'page'; }, ARRAY_FILTER_USE_KEY)) ?>"><?= $i ?></a>
                                <?php endfor; ?>

                                <?php if ($end < $totalPages): ?>
                                    <?php if ($end < $totalPages - 1): ?>
                                        <span class="modern-pagination-dots">...</span>
                                    <?php endif; ?>
                                    <a class="modern-pagination-number" href="?page=<?= $totalPages ?>&<?= http_build_query(array_filter($_GET, function($key) { return $key !== 'page'; }, ARRAY_FILTER_USE_KEY)) ?>"><?= $totalPages ?></a>
                                <?php endif; ?>
                            </div>

                            <?php if ($page < $totalPages): ?>
                            <a class="modern-pagination-btn modern-pagination-next"
                               href="?page=<?= $page + 1 ?>&<?= http_build_query(array_filter($_GET, function($key) { return $key !== 'page'; }, ARRAY_FILTER_USE_KEY)) ?>">
                                Selanjutnya
                                <i class="fas fa-chevron-right"></i>
                            </a>
                            <?php endif; ?>
                        </nav>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Modern Add Transaction Modal -->
<div class="modal fade" id="addTransactionModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modern-modal-content">
            <div class="modern-modal-header">
                <h5 class="modern-modal-title">
                    <i class="fas fa-plus modern-text-primary modern-mr-sm"></i>
                    Tambah Transaksi
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="" method="POST" class="needs-validation" novalidate>
                <div class="modern-modal-body">
                    <input type="hidden" name="action" value="add">

                    <div class="modern-form-group">
                        <label class="modern-form-label">
                            <i class="fas fa-tags modern-text-primary"></i>
                            Kategori <span class="modern-text-danger">*</span>
                        </label>
                        <select name="kategori_id" class="modern-form-control" required>
                            <option value="">Pilih Kategori</option>
                            <?php foreach ($kategori as $k): ?>
                            <option value="<?= $k['id'] ?>">
                                <?= $k['tipe'] === 'pemasukan' ? '📈' : '📉' ?> <?= htmlspecialchars($k['nama']) ?>
                                (<?= $k['tipe'] === 'pemasukan' ? 'Pemasukan' : 'Pengeluaran' ?>)
                            </option>
                            <?php endforeach; ?>
                        </select>
                        <div class="invalid-feedback">Kategori harus dipilih</div>
                    </div>

                    <div class="modern-form-group">
                        <label class="modern-form-label">
                            <i class="fas fa-exchange-alt modern-text-primary"></i>
                            Jenis <span class="modern-text-danger">*</span>
                        </label>
                        <select name="jenis" class="modern-form-control" required>
                            <option value="">Pilih Jenis</option>
                            <option value="pemasukan">📈 Pemasukan</option>
                            <option value="pengeluaran">📉 Pengeluaran</option>
                        </select>
                        <div class="invalid-feedback">Jenis transaksi harus dipilih</div>
                    </div>

                    <div class="modern-form-group">
                        <label class="modern-form-label">
                            <i class="fas fa-money-bill modern-text-primary"></i>
                            Jumlah <span class="modern-text-danger">*</span>
                        </label>
                        <div class="modern-input-group">
                            <span class="modern-input-group-text">Rp</span>
                            <input type="text" name="jumlah" class="modern-form-control number-format" required placeholder="0">
                        </div>
                        <div class="invalid-feedback">Jumlah harus diisi</div>
                    </div>

                    <div class="modern-form-group">
                        <label class="modern-form-label">
                            <i class="fas fa-calendar-alt modern-text-primary"></i>
                            Tanggal <span class="modern-text-danger">*</span>
                        </label>
                        <input type="date" name="tanggal" class="modern-form-control" required
                               value="<?= date('Y-m-d') ?>">
                        <div class="invalid-feedback">Tanggal harus diisi</div>
                    </div>

                    <div class="modern-form-group modern-mb-0">
                        <label class="modern-form-label">
                            <i class="fas fa-sticky-note modern-text-primary"></i>
                            Keterangan
                        </label>
                        <textarea name="keterangan" class="modern-form-control" rows="3" placeholder="Tambahkan keterangan transaksi..."></textarea>
                    </div>
                </div>
                <div class="modern-modal-footer">
                    <button type="button" class="modern-btn modern-btn-light" data-bs-dismiss="modal">
                        <i class="fas fa-times"></i>
                        Batal
                    </button>
                    <button type="submit" class="modern-btn modern-btn-primary">
                        <i class="fas fa-save"></i>
                        Simpan Transaksi
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modern Edit Transaction Modal -->
<div class="modal fade" id="editTransactionModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modern-modal-content">
            <div class="modern-modal-header">
                <h5 class="modern-modal-title">
                    <i class="fas fa-edit modern-text-primary modern-mr-sm"></i>
                    Edit Transaksi
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="transaksi.php" method="POST" class="needs-validation" novalidate>
                <input type="hidden" name="action" value="update">
                <input type="hidden" name="id" id="edit_id">
                <div class="modern-modal-body">
                    <div class="modern-form-group">
                        <label class="modern-form-label">
                            <i class="fas fa-tags modern-text-primary"></i>
                            Kategori <span class="modern-text-danger">*</span>
                        </label>
                        <select name="kategori_id" id="edit_kategori_id" class="modern-form-control" required>
                            <?php foreach ($kategori as $k): ?>
                            <option value="<?= $k['id'] ?>">
                                <?= $k['tipe'] === 'pemasukan' ? '📈' : '📉' ?> <?= htmlspecialchars($k['nama']) ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                        <div class="invalid-feedback">Kategori harus dipilih</div>
                    </div>

                    <div class="modern-form-group">
                        <label class="modern-form-label">
                            <i class="fas fa-exchange-alt modern-text-primary"></i>
                            Jenis <span class="modern-text-danger">*</span>
                        </label>
                        <select name="jenis" id="edit_jenis" class="modern-form-control" required>
                            <option value="pemasukan">📈 Pemasukan</option>
                            <option value="pengeluaran">📉 Pengeluaran</option>
                        </select>
                        <div class="invalid-feedback">Jenis transaksi harus dipilih</div>
                    </div>

                    <div class="modern-form-group">
                        <label class="modern-form-label">
                            <i class="fas fa-money-bill modern-text-primary"></i>
                            Jumlah <span class="modern-text-danger">*</span>
                        </label>
                        <div class="modern-input-group">
                            <span class="modern-input-group-text">Rp</span>
                            <input type="text" name="jumlah" id="edit_jumlah" class="modern-form-control number-format" required>
                        </div>
                        <div class="invalid-feedback">Jumlah harus diisi</div>
                    </div>

                    <div class="modern-form-group">
                        <label class="modern-form-label">
                            <i class="fas fa-calendar-alt modern-text-primary"></i>
                            Tanggal <span class="modern-text-danger">*</span>
                        </label>
                        <input type="date" name="tanggal" id="edit_tanggal" class="modern-form-control" required>
                        <div class="invalid-feedback">Tanggal harus diisi</div>
                    </div>

                    <div class="modern-form-group modern-mb-0">
                        <label class="modern-form-label">
                            <i class="fas fa-sticky-note modern-text-primary"></i>
                            Keterangan
                        </label>
                        <textarea name="keterangan" id="edit_keterangan" class="modern-form-control" rows="3"></textarea>
                    </div>
                </div>
                <div class="modern-modal-footer">
                    <button type="button" class="modern-btn modern-btn-light" data-bs-dismiss="modal">
                        <i class="fas fa-times"></i>
                        Batal
                    </button>
                    <button type="submit" class="modern-btn modern-btn-primary">
                        <i class="fas fa-save"></i>
                        Update Transaksi
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php include 'includes/views/layouts/footer.php'; ?>

<style>
/* Custom styles */
.card {
    transition: all 0.3s ease;
    background: linear-gradient(145deg, #ffffff, #f8f9fa);
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

/* Gradient backgrounds for stat cards */
.card:nth-child(1) {
    background: linear-gradient(145deg, #ffffff, #e8f5e9);
    border-left: 4px solid #4caf50;
}

.card:nth-child(2) {
    background: linear-gradient(145deg, #ffffff, #ffebee);
    border-left: 4px solid #f44336;
}

.card:nth-child(3) {
    background: linear-gradient(145deg, #ffffff, #e3f2fd);
    border-left: 4px solid #2196f3;
}

/* Text colors for stat cards */
.card:nth-child(1) h6 {
    color: #2e7d32;
}

.card:nth-child(1) h3 {
    color: #1b5e20;
}

.card:nth-child(2) h6 {
    color: #c62828;
}

.card:nth-child(2) h3 {
    color: #b71c1c;
}

.card:nth-child(3) h6 {
    color: #1565c0;
}

.card:nth-child(3) h3 {
    color: #0d47a1;
}

.table > :not(caption) > * > * {
    padding: 1rem;
}

.table th {
    color: #1a237e;
    font-weight: 600;
}

.table td {
    color: #37474f;
}

.badge {
    padding: 0.5rem 0.75rem;
    font-weight: 500;
}

.badge.bg-success {
    background: linear-gradient(45deg, #4caf50, #8bc34a) !important;
    color: #fff !important;
}

.badge.bg-danger {
    background: linear-gradient(45deg, #f44336, #ff5252) !important;
    color: #fff !important;
}

.btn-group .btn {
    padding: 0.5rem;
}

.btn-group .btn:hover {
    background-color: var(--bs-light);
}

.btn-primary {
    background: linear-gradient(45deg, #2196f3, #1976d2);
    border: none;
    color: #fff;
}

.btn-primary:hover {
    background: linear-gradient(45deg, #1976d2, #1565c0);
    transform: translateY(-1px);
    color: #fff;
}

.btn-light {
    color: #37474f;
}

.modal-content {
    border: none;
    border-radius: 1rem;
    background: linear-gradient(145deg, #ffffff, #f8f9fa);
}

.modal-header {
    padding: 1.5rem;
    background: linear-gradient(45deg, #f8f9fa, #ffffff);
    border-bottom: 1px solid rgba(0,0,0,0.05);
}

.modal-title {
    color: #1a237e;
    font-weight: 600;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    padding: 1.5rem;
    background: linear-gradient(45deg, #ffffff, #f8f9fa);
    border-top: 1px solid rgba(0,0,0,0.05);
}

.form-label {
    color: #37474f;
    font-weight: 500;
}

.form-control, .form-select {
    padding: 0.625rem 1rem;
    border-radius: 0.5rem;
    border: 1px solid rgba(0,0,0,0.1);
    background: linear-gradient(145deg, #ffffff, #f8f9fa);
    color: #37474f;
}

.form-control:focus, .form-select:focus {
    box-shadow: 0 0 0 0.25rem rgba(33, 150, 243, 0.1);
    border-color: #2196f3;
    color: #1a237e;
}

.input-group-text {
    border-radius: 0.5rem 0 0 0.5rem;
    background: linear-gradient(145deg, #f8f9fa, #ffffff);
    border: 1px solid rgba(0,0,0,0.1);
    color: #37474f;
}

.pagination {
    margin-bottom: 0;
}

.page-link {
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    margin: 0 0.25rem;
    border: none;
    color: #2196f3;
    background: linear-gradient(145deg, #ffffff, #f8f9fa);
}

.page-item.active .page-link {
    background: linear-gradient(45deg, #2196f3, #1976d2);
    color: white;
}

.page-link:hover {
    background: linear-gradient(145deg, #e3f2fd, #bbdefb);
    color: #1976d2;
}

/* Icon containers */
.icon-container {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
}

.icon-container.primary {
    background: linear-gradient(45deg, #e3f2fd, #bbdefb);
    color: #1976d2;
}

.icon-container.success {
    background: linear-gradient(45deg, #e8f5e9, #c8e6c9);
    color: #2e7d32;
}

.icon-container.danger {
    background: linear-gradient(45deg, #ffebee, #ffcdd2);
    color: #c62828;
}

/* Table row hover effect */
.table tbody tr {
    transition: all 0.2s ease;
}

.table tbody tr:hover {
    background: linear-gradient(145deg, #f8f9fa, #ffffff);
    transform: scale(1.01);
}

/* Dark mode support */
[data-bs-theme="dark"] .card {
    background: linear-gradient(145deg, #2d3748, #1a202c);
    border-color: rgba(255,255,255,0.1);
}

[data-bs-theme="dark"] .card:nth-child(1) {
    background: linear-gradient(145deg, #2d3748, #1a202c);
    border-left: 4px solid #4caf50;
}

[data-bs-theme="dark"] .card:nth-child(2) {
    background: linear-gradient(145deg, #2d3748, #1a202c);
    border-left: 4px solid #f44336;
}

[data-bs-theme="dark"] .card:nth-child(3) {
    background: linear-gradient(145deg, #2d3748, #1a202c);
    border-left: 4px solid #2196f3;
}

[data-bs-theme="dark"] .card:nth-child(1) h6 {
    color: #81c784;
}

[data-bs-theme="dark"] .card:nth-child(1) h3 {
    color: #a5d6a7;
}

[data-bs-theme="dark"] .card:nth-child(2) h6 {
    color: #e57373;
}

[data-bs-theme="dark"] .card:nth-child(2) h3 {
    color: #ef9a9a;
}

[data-bs-theme="dark"] .card:nth-child(3) h6 {
    color: #64b5f6;
}

[data-bs-theme="dark"] .card:nth-child(3) h3 {
    color: #90caf9;
}

[data-bs-theme="dark"] .table th {
    color: #e3f2fd;
}

[data-bs-theme="dark"] .table td {
    color: #cfd8dc;
}

[data-bs-theme="dark"] .bg-light {
    background: rgba(255, 255, 255, 0.1) !important;
}

[data-bs-theme="dark"] .text-muted {
    color: rgba(255, 255, 255, 0.6) !important;
}

[data-bs-theme="dark"] .table {
    color: var(--bs-body-color);
}

[data-bs-theme="dark"] .table > :not(caption) > * > * {
    border-bottom-color: rgba(255,255,255,0.1);
}

[data-bs-theme="dark"] .modal-content {
    background: linear-gradient(145deg, #2d3748, #1a202c);
}

[data-bs-theme="dark"] .modal-title {
    color: #e3f2fd;
}

[data-bs-theme="dark"] .form-label {
    color: #cfd8dc;
}

[data-bs-theme="dark"] .form-control,
[data-bs-theme="dark"] .form-select {
    background: linear-gradient(145deg, #2d3748, #1a202c);
    border-color: rgba(255,255,255,0.1);
    color: #cfd8dc;
}

[data-bs-theme="dark"] .input-group-text {
    background: linear-gradient(145deg, #2d3748, #1a202c);
    border-color: rgba(255,255,255,0.1);
    color: #cfd8dc;
}

[data-bs-theme="dark"] .table tbody tr:hover {
    background: linear-gradient(145deg, #2d3748, #1a202c);
}

[data-bs-theme="dark"] .icon-container.primary {
    background: linear-gradient(45deg, #1a202c, #2d3748);
    color: #64b5f6;
}

[data-bs-theme="dark"] .icon-container.success {
    background: linear-gradient(45deg, #1a202c, #2d3748);
    color: #81c784;
}

[data-bs-theme="dark"] .icon-container.danger {
    background: linear-gradient(45deg, #1a202c, #2d3748);
    color: #e57373;
}

[data-bs-theme="dark"] .btn-light {
    color: #e3f2fd;
}
</style>

<script>
// Format currency input
function formatCurrency(input) {
    let value = input.value.replace(/\D/g, '');
    value = parseInt(value, 10);
    if (isNaN(value)) value = 0;
    input.value = value.toLocaleString('id-ID');
                }

// Edit transaction
function editTransaction(id, kategoriId, jenis, jumlah, tanggal, keterangan) {
    document.getElementById('edit_id').value = id;
    document.querySelector('select[name="kategori_id"]').value = kategoriId;
    document.querySelector('select[name="jenis"]').value = jenis;
    document.querySelector('input[name="jumlah"]').value = jumlah;
    document.querySelector('input[name="tanggal"]').value = tanggal;
    document.querySelector('textarea[name="keterangan"]').value = keterangan;
    
    new bootstrap.Modal(document.getElementById('editTransactionModal')).show();
}

// Delete transaction with modern popup
function deleteTransaction(id) {
                Swal.fire({
                    title: 'Apakah Anda yakin?',
        text: "Data transaksi yang dihapus tidak dapat dikembalikan!",
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#d33',
                    cancelButtonColor: '#3085d6',
                    confirmButtonText: 'Ya, hapus!',
        cancelButtonText: 'Batal',
        reverseButtons: true,
        customClass: {
            confirmButton: 'btn btn-danger',
            cancelButton: 'btn btn-secondary'
        },
        buttonsStyling: true
                }).then((result) => {
                    if (result.isConfirmed) {
            // Tampilkan loading
            Swal.fire({
                title: 'Menghapus...',
                text: 'Mohon tunggu sebentar',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            // Redirect ke halaman hapus
            window.location.href = 'hapus-transaksi.php?id=' + id;
                    }
                });
}

// Initialize currency formatters
document.querySelectorAll('input[name="jumlah"]').forEach(input => {
    input.addEventListener('input', function() {
        formatCurrency(this);
            });
        });

// Tampilkan notifikasi flash message dengan SweetAlert2
        <?php if ($flash = getFlashMessage()): ?>
        Swal.fire({
            icon: '<?= $flash['type'] === 'success' ? 'success' : 'error' ?>',
    title: '<?= $flash['type'] === 'success' ? 'Berhasil!' : 'Error!' ?>',
            text: '<?= $flash['message'] ?>',
    timer: 3000,
    timerProgressBar: true,
    showConfirmButton: false
        });
        <?php endif; ?>
    </script>