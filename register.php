<?php
// Mulai session jika belum dimulai
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once 'includes/config/database.php';
require_once 'includes/helpers/functions.php';

// Pastikan koneksi database tersedia setelah menyertakan database.php
// Variabel $pdo seharusnya tersedia dari includes/config/database.php
if (!isset($pdo) || !$pdo instanceof PDO) {
     error_log("PDO connection not available after including database.php in register.php");
     die('Terjadi kesalahan sistem: Koneksi database tidak tersedia.');
}

// Jika user sudah login, redirect ke dashboard
if (isLoggedIn()) {
    redirect('index.php');
}

$errors = [];
$nama = '';
$email = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validasi CSRF token
    if (!isset($_POST['csrf_token']) || !validateCSRFToken($_POST['csrf_token'])) {
        $errors['general'] = 'Invalid request. Please try again.';
    } else {
        $nama = cleanInput($_POST['nama'] ?? '');
        $email = cleanInput($_POST['email'] ?? '');
        $password = $_POST['password'] ?? '';
        $konfirmasi_password = $_POST['konfirmasi_password'] ?? '';

        // Validasi input
        if (empty($nama)) {
            $errors['nama'] = 'Nama harus diisi';
        } elseif (strlen($nama) < 3) {
            $errors['nama'] = 'Nama minimal 3 karakter';
        }

        if (empty($email)) {
            $errors['email'] = 'Email harus diisi';
        } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $errors['email'] = 'Format email tidak valid';
        } else {
            // Cek apakah email sudah terdaftar
            try {
                $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ? LIMIT 1");
                $stmt->execute([$email]);
                if ($stmt->fetch()) {
                    $errors['email'] = 'Email sudah terdaftar';
                }
            } catch (PDOException $e) {
                error_log("Error checking email in register.php: " . $e->getMessage());
                $errors['general'] = 'Terjadi kesalahan saat memeriksa email. Silakan coba lagi.';
            }
        }

        if (empty($password)) {
            $errors['password'] = 'Password harus diisi';
        } elseif (strlen($password) < 8) {
            $errors['password'] = 'Password minimal 8 karakter';
        } elseif (!preg_match('/[A-Z]/', $password)) {
            $errors['password'] = 'Password harus mengandung huruf besar';
        } elseif (!preg_match('/[a-z]/', $password)) {
            $errors['password'] = 'Password harus mengandung huruf kecil';
        } elseif (!preg_match('/[0-9]/', $password)) {
            $errors['password'] = 'Password harus mengandung angka';
        }

        if ($password !== $konfirmasi_password) {
            $errors['konfirmasi_password'] = 'Konfirmasi password tidak sesuai';
        }

        // Jika tidak ada error, proses pendaftaran
        if (empty($errors)) {
            try {
                // Hash password
                $hashed_password = password_hash($password, PASSWORD_DEFAULT);
                
                // Insert user baru
                $stmt = $pdo->prepare("
                    INSERT INTO users (nama, nama_lengkap, email, username, password, role, status, created_at, updated_at) 
                    VALUES (?, ?, ?, ?, ?, 'user', 'aktif', NOW(), NOW())
                ");
                
                if ($stmt->execute([$nama, $nama, $email, $email, $hashed_password])) {
                    // Log aktivitas (jika fungsi tersedia)
                    if (function_exists('logActivity')) {
                        logActivity($pdo->lastInsertId(), 'User registered');
                    }
                    
                    // Set flash message dan redirect (jika fungsi tersedia)
                    if (function_exists('setFlashMessage') && function_exists('redirect')) {
                        setFlashMessage('success', 'Pendaftaran berhasil! Silakan login.');
                        redirect('login.php');
                    } else {
                        // Fallback jika fungsi helper tidak ada
                        echo '<script>alert(\'Pendaftaran berhasil! Silakan login.\'); window.location.href = \'login.php\';</script>';
                        exit; // Penting untuk menghentikan eksekusi setelah redirect JS
                    }
                    
                } else {
                    error_log("Failed to execute user insert statement in register.php");
                    $errors['general'] = 'Gagal mendaftar. Terjadi kesalahan saat menyimpan data.';
                }
            } catch (PDOException $e) {
                error_log("Registration Insert Error in register.php: " . $e->getMessage());
                // Untuk debugging, tampilkan error di layar
                echo "<div class=\"alert alert-danger\">Terjadi kesalahan saat menyimpan data: " . $e->getMessage() . "</div>";
                $errors['general'] = 'Terjadi kesalahan saat menyimpan data. Silakan coba lagi.';
            }
        }
    }
}

// Generate CSRF token
$csrf_token = generateCSRFToken();
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Daftar - Sistem Keuangan</title>
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container">
        <div class="row justify-content-center mt-5">
            <div class="col-md-6">
                <div class="card shadow">
                    <div class="card-body p-5">
                        <h2 class="text-center mb-4">Daftar Akun</h2>
                        
                        <?php if (isset($errors['general'])): ?>
                        <div class="alert alert-danger"><?= htmlspecialchars($errors['general']) ?></div>
                        <?php endif; ?>
                        
                        <form method="POST" action="" novalidate>
                            <input type="hidden" name="csrf_token" value="<?= $csrf_token ?>">
                            
                            <div class="mb-3">
                                <label for="nama" class="form-label">Nama Lengkap</label>
                                <input type="text" class="form-control <?= isset($errors['nama']) ? 'is-invalid' : '' ?>" 
                                       id="nama" name="nama" value="<?= htmlspecialchars($nama) ?>" required>
                                <?php if (isset($errors['nama'])): ?>
                                <div class="invalid-feedback"><?= htmlspecialchars($errors['nama']) ?></div>
                                <?php endif; ?>
                            </div>
                            
                            <div class="mb-3">
                                <label for="email" class="form-label">Email</label>
                                <input type="email" class="form-control <?= isset($errors['email']) ? 'is-invalid' : '' ?>" 
                                       id="email" name="email" value="<?= htmlspecialchars($email) ?>" required>
                                <?php if (isset($errors['email'])): ?>
                                <div class="invalid-feedback"><?= htmlspecialchars($errors['email']) ?></div>
                                <?php endif; ?>
                            </div>
                            
                            <div class="mb-3">
                                <label for="password" class="form-label">Password</label>
                                <div class="input-group">
                                    <input type="password" class="form-control <?= isset($errors['password']) ? 'is-invalid' : '' ?>" 
                                           id="password" name="password" required>
                                    <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <?php if (isset($errors['password'])): ?>
                                    <div class="invalid-feedback"><?= htmlspecialchars($errors['password']) ?></div>
                                    <?php endif; ?>
                                </div>
                                <small class="form-text text-muted">
                                    Password harus minimal 8 karakter, mengandung huruf besar, huruf kecil, dan angka
                                </small>
                            </div>
                            
                            <div class="mb-4">
                                <label for="konfirmasi_password" class="form-label">Konfirmasi Password</label>
                                <div class="input-group">
                                    <input type="password" class="form-control <?= isset($errors['konfirmasi_password']) ? 'is-invalid' : '' ?>" 
                                           id="konfirmasi_password" name="konfirmasi_password" required>
                                    <button class="btn btn-outline-secondary" type="button" id="toggleConfirmPassword">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <?php if (isset($errors['konfirmasi_password'])): ?>
                                    <div class="invalid-feedback"><?= htmlspecialchars($errors['konfirmasi_password']) ?></div>
                                    <?php endif; ?>
                                </div>
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">Daftar</button>
                            </div>
                            
                            <div class="text-center mt-3">
                                <p>Sudah punya akun? <a href="login.php">Login di sini</a></p>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
    // Toggle password visibility
    document.getElementById('togglePassword').addEventListener('click', function() {
        const password = document.getElementById('password');
        const icon = this.querySelector('i');
        
        if (password.type === 'password') {
            password.type = 'text';
            icon.classList.remove('fa-eye');
            icon.classList.add('fa-eye-slash');
        } else {
            password.type = 'password';
            icon.classList.remove('fa-eye-slash');
            icon.classList.add('fa-eye');
        }
    });
    
    document.getElementById('toggleConfirmPassword').addEventListener('click', function() {
        const password = document.getElementById('konfirmasi_password');
        const icon = this.querySelector('i');
        
        if (password.type === 'password') {
            password.type = 'text';
            icon.classList.remove('fa-eye');
            icon.classList.add('fa-eye-slash');
        } else {
            password.type = 'password';
            icon.classList.remove('fa-eye-slash');
            icon.classList.add('fa-eye');
        }
    });
    </script>
</body>
</html> 