<?php
require_once 'includes/config/database.php';
require_once 'includes/helpers/functions.php';

// Check admin access
$currentUser = getCurrentUser();
if (!$currentUser || $currentUser['role'] !== 'admin') {
    setFlashMessage('danger', 'Aks<PERSON> ditolak! Anda tidak memiliki izin untuk mengakses halaman ini.');
    redirect('dashboard.php');
}

$currentPage = 'notifications';
$pageTitle = 'Notifikasi & System Maintenance';

// Create notifications table if not exists
try {
    $pdo->exec("CREATE TABLE IF NOT EXISTS system_notifications (
        id INT AUTO_INCREMENT PRIMARY KEY,
        type ENUM('error', 'warning', 'info', 'success') DEFAULT 'info',
        title VARCHAR(255) NOT NULL,
        message TEXT NOT NULL,
        source VARCHAR(100),
        user_id INT,
        is_read BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_type (type),
        INDEX idx_created_at (created_at),
        INDEX idx_is_read (is_read)
    )");

    // Check if there are any notifications, if not, add some default ones
    $stmt = $pdo->query("SELECT COUNT(*) FROM system_notifications");
    $notificationCount = $stmt->fetchColumn();

    if ($notificationCount == 0) {
        // Add default notifications
        $defaultNotifications = [
            [
                'type' => 'success',
                'title' => 'Sistem Berhasil Diinisialisasi',
                'message' => 'Sistem keuangan telah berhasil diinisialisasi dan siap digunakan. Semua tabel database telah dibuat dengan sukses.',
                'source' => 'system_init'
            ],
            [
                'type' => 'info',
                'title' => 'Selamat Datang di Sistem Keuangan',
                'message' => 'Terima kasih telah menggunakan sistem keuangan. Anda dapat mulai mengelola transaksi, kategori, target, dan anggaran keuangan Anda.',
                'source' => 'welcome'
            ],
            [
                'type' => 'info',
                'title' => 'Fitur Menu Lengkap Tersedia',
                'message' => 'Sistem dilengkapi dengan 44 menu items termasuk manajemen keuangan, laporan, tools admin, dan fitur maintenance yang lengkap.',
                'source' => 'features'
            ],
            [
                'type' => 'warning',
                'title' => 'Backup Data Secara Berkala',
                'message' => 'Disarankan untuk melakukan backup data secara berkala melalui menu Admin Panel > Backup Data untuk menjaga keamanan data Anda.',
                'source' => 'backup_reminder'
            ],
            [
                'type' => 'info',
                'title' => 'Panduan Penggunaan',
                'message' => 'Untuk panduan lengkap penggunaan sistem, silakan kunjungi menu Bantuan > Panduan Pengguna atau hubungi administrator sistem.',
                'source' => 'user_guide'
            ],
            [
                'type' => 'success',
                'title' => 'Database Health Check',
                'message' => 'Semua tabel database telah berhasil dibuat dan sistem dalam kondisi sehat. Anda dapat mulai menggunakan semua fitur yang tersedia.',
                'source' => 'health_check'
            ]
        ];

        $stmt = $pdo->prepare("INSERT INTO system_notifications (type, title, message, source, user_id) VALUES (?, ?, ?, ?, ?)");
        foreach ($defaultNotifications as $notification) {
            $stmt->execute([
                $notification['type'],
                $notification['title'],
                $notification['message'],
                $notification['source'],
                $currentUser['id']
            ]);
        }
    }
} catch (PDOException $e) {
    error_log("Error creating notifications table: " . $e->getMessage());
}

// Handle actions
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    try {
        switch ($_POST['action']) {
            case 'mark_read':
                $notificationId = $_POST['notification_id'] ?? '';
                if ($notificationId) {
                    $stmt = $pdo->prepare("UPDATE system_notifications SET is_read = TRUE WHERE id = ?");
                    $stmt->execute([$notificationId]);
                    setFlashMessage('success', 'Notifikasi ditandai sebagai sudah dibaca');
                }
                break;

            case 'mark_all_read':
                $stmt = $pdo->prepare("UPDATE system_notifications SET is_read = TRUE WHERE is_read = FALSE");
                $stmt->execute();
                setFlashMessage('success', 'Semua notifikasi ditandai sebagai sudah dibaca');
                break;

            case 'delete_notification':
                $notificationId = $_POST['notification_id'] ?? '';
                if ($notificationId) {
                    $stmt = $pdo->prepare("DELETE FROM system_notifications WHERE id = ?");
                    $stmt->execute([$notificationId]);
                    setFlashMessage('success', 'Notifikasi berhasil dihapus');
                }
                break;

            case 'clear_old_notifications':
                $days = $_POST['days'] ?? 30;
                $stmt = $pdo->prepare("DELETE FROM system_notifications WHERE created_at < DATE_SUB(NOW(), INTERVAL ? DAY)");
                $stmt->execute([$days]);
                $affected = $stmt->rowCount();
                setFlashMessage('success', "Berhasil menghapus $affected notifikasi lama");
                break;

            case 'add_test_notification':
                $type = $_POST['test_type'] ?? 'info';
                $title = $_POST['test_title'] ?? 'Test Notification';
                $message = $_POST['test_message'] ?? 'This is a test notification';

                $stmt = $pdo->prepare("INSERT INTO system_notifications (type, title, message, source, user_id) VALUES (?, ?, ?, ?, ?)");
                $stmt->execute([$type, $title, $message, 'manual_test', $currentUser['id']]);
                setFlashMessage('success', 'Test notifikasi berhasil ditambahkan');
                break;
        }
        redirect('notifications.php');
    } catch (Exception $e) {
        setFlashMessage('danger', 'Error: ' . $e->getMessage());
    }
}

// Get notifications with pagination
$page = $_GET['page'] ?? 1;
$limit = 20;
$offset = ($page - 1) * $limit;

$filter = $_GET['filter'] ?? 'all';
$whereClause = '';
$params = [];

if ($filter !== 'all') {
    if ($filter === 'unread') {
        $whereClause = 'WHERE is_read = FALSE';
    } else {
        $whereClause = 'WHERE type = ?';
        $params[] = $filter;
    }
}

// Get total count
$countSql = "SELECT COUNT(*) FROM system_notifications $whereClause";
$stmt = $pdo->prepare($countSql);
$stmt->execute($params);
$totalNotifications = $stmt->fetchColumn();

// Get notifications
$sql = "SELECT * FROM system_notifications $whereClause ORDER BY created_at DESC LIMIT $limit OFFSET $offset";
$stmt = $pdo->prepare($sql);
$stmt->execute($params);
$notifications = $stmt->fetchAll();

// Get statistics
$stmt = $pdo->query("SELECT
    COUNT(*) as total,
    SUM(CASE WHEN type = 'error' THEN 1 ELSE 0 END) as errors,
    SUM(CASE WHEN type = 'warning' THEN 1 ELSE 0 END) as warnings,
    SUM(CASE WHEN type = 'info' THEN 1 ELSE 0 END) as info,
    SUM(CASE WHEN type = 'success' THEN 1 ELSE 0 END) as success,
    SUM(CASE WHEN is_read = FALSE THEN 1 ELSE 0 END) as unread
    FROM system_notifications");
$stats = $stmt->fetch();

include 'includes/views/layouts/header.php';
?>

<div class="container-fluid px-4 py-3">
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-gradient-primary text-white py-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h5 class="card-title mb-0">
                                <i class="fas fa-bell me-2"></i>
                                Notifikasi & System Maintenance
                            </h5>
                            <p class="mb-0 small opacity-75">Monitor notifikasi sistem dan maintenance tools</p>
                        </div>
                        <div class="d-flex gap-2">
                            <a href="view_all_notifications.php" class="btn btn-info btn-sm">
                                <i class="fas fa-list me-1"></i>View All Details
                            </a>
                            <a href="logs.php" class="btn btn-light btn-sm">
                                <i class="fas fa-history me-1"></i>Log Aktivitas
                            </a>
                            <a href="backup.php" class="btn btn-light btn-sm">
                                <i class="fas fa-database me-1"></i>Backup
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body p-4">

                    <!-- Statistics Cards -->
                    <div class="row mb-4">
                        <div class="col-md-2">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center py-3">
                                    <h4 class="mb-1"><?= $stats['total'] ?></h4>
                                    <small>Total</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="card bg-danger text-white">
                                <div class="card-body text-center py-3">
                                    <h4 class="mb-1"><?= $stats['errors'] ?></h4>
                                    <small>Errors</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="card bg-warning text-white">
                                <div class="card-body text-center py-3">
                                    <h4 class="mb-1"><?= $stats['warnings'] ?></h4>
                                    <small>Warnings</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center py-3">
                                    <h4 class="mb-1"><?= $stats['info'] ?></h4>
                                    <small>Info</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center py-3">
                                    <h4 class="mb-1"><?= $stats['success'] ?></h4>
                                    <small>Success</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="card bg-secondary text-white">
                                <div class="card-body text-center py-3">
                                    <h4 class="mb-1"><?= $stats['unread'] ?></h4>
                                    <small>Unread</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card border-success">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-tools me-2"></i>System Maintenance
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="d-grid gap-2">
                                        <a href="system_customization.php" class="btn btn-primary btn-sm">
                                            <i class="fas fa-paint-brush me-2"></i>System Customization
                                        </a>
                                        <a href="fix_errors.php" class="btn btn-danger btn-sm">
                                            <i class="fas fa-wrench me-2"></i>Fix System Errors
                                        </a>
                                        <a href="update_database.php" class="btn btn-warning btn-sm">
                                            <i class="fas fa-database me-2"></i>Update Database
                                        </a>
                                        <a href="system_check.php" class="btn btn-info btn-sm">
                                            <i class="fas fa-check-circle me-2"></i>System Health Check
                                        </a>
                                        <a href="clear_cache.php" class="btn btn-secondary btn-sm">
                                            <i class="fas fa-broom me-2"></i>Clear Cache
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border-info">
                                <div class="card-header bg-info text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-cog me-2"></i>Quick Actions
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <form method="POST" class="d-grid gap-2">
                                        <button type="submit" name="action" value="mark_all_read" class="btn btn-success btn-sm">
                                            <i class="fas fa-check-double me-2"></i>Mark All as Read
                                        </button>
                                        <div class="input-group input-group-sm">
                                            <input type="number" class="form-control" name="days" value="30" min="1" max="365">
                                            <button type="submit" name="action" value="clear_old_notifications" class="btn btn-outline-danger">
                                                <i class="fas fa-trash me-1"></i>Clear Old
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Filters -->
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div class="btn-group" role="group">
                            <a href="?filter=all" class="btn btn-outline-primary <?= $filter === 'all' ? 'active' : '' ?>">
                                All (<?= $stats['total'] ?>)
                            </a>
                            <a href="?filter=unread" class="btn btn-outline-secondary <?= $filter === 'unread' ? 'active' : '' ?>">
                                Unread (<?= $stats['unread'] ?>)
                            </a>
                            <a href="?filter=error" class="btn btn-outline-danger <?= $filter === 'error' ? 'active' : '' ?>">
                                Errors (<?= $stats['errors'] ?>)
                            </a>
                            <a href="?filter=warning" class="btn btn-outline-warning <?= $filter === 'warning' ? 'active' : '' ?>">
                                Warnings (<?= $stats['warnings'] ?>)
                            </a>
                        </div>

                        <!-- Add Test Notification -->
                        <button type="button" class="btn btn-outline-info btn-sm" data-bs-toggle="modal" data-bs-target="#testNotificationModal">
                            <i class="fas fa-plus me-1"></i>Add Test Notification
                        </button>
                    </div>

                    <!-- Notifications List -->
                    <div class="row">
                        <?php if (empty($notifications)): ?>
                            <div class="col-12">
                                <div class="text-center py-5">
                                    <i class="fas fa-bell-slash fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">Tidak ada notifikasi</h5>
                                    <p class="text-muted">Belum ada notifikasi untuk ditampilkan</p>
                                </div>
                            </div>
                        <?php else: ?>
                            <?php foreach ($notifications as $notification): ?>
                                <?php
                                // Ensure all required fields exist with default values
                                $type = $notification['type'] ?? 'info';
                                $title = $notification['title'] ?? 'No Title';
                                $message = $notification['message'] ?? 'No Message';
                                $source = $notification['source'] ?? '';
                                $is_read = $notification['is_read'] ?? false;
                                $created_at = $notification['created_at'] ?? date('Y-m-d H:i:s');
                                $id = $notification['id'] ?? 0;
                                ?>
                                <div class="col-12 mb-3">
                                    <div class="card border-start border-4 border-<?= $type === 'error' ? 'danger' : ($type === 'warning' ? 'warning' : ($type === 'success' ? 'success' : 'info')) ?> <?= !$is_read ? 'bg-light' : '' ?>">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between align-items-start">
                                                <div class="flex-grow-1">
                                                    <div class="d-flex align-items-center mb-2">
                                                        <i class="fas fa-<?= $type === 'error' ? 'exclamation-triangle text-danger' : ($type === 'warning' ? 'exclamation-circle text-warning' : ($type === 'success' ? 'check-circle text-success' : 'info-circle text-info')) ?> me-2"></i>
                                                        <h6 class="mb-0 <?= !$is_read ? 'fw-bold' : '' ?>">
                                                            <?= htmlspecialchars($title) ?>
                                                        </h6>
                                                        <?php if (!$is_read): ?>
                                                            <span class="badge bg-primary ms-2">New</span>
                                                        <?php endif; ?>
                                                    </div>
                                                    <p class="text-muted mb-2"><?= htmlspecialchars($message) ?></p>
                                                    <div class="small text-muted">
                                                        <i class="fas fa-clock me-1"></i>
                                                        <?= date('d/m/Y H:i', strtotime($created_at)) ?>
                                                        <?php if ($source): ?>
                                                            | <i class="fas fa-tag me-1"></i><?= htmlspecialchars($source) ?>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                                <div class="dropdown">
                                                    <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="dropdown">
                                                        <i class="fas fa-ellipsis-v"></i>
                                                    </button>
                                                    <ul class="dropdown-menu">
                                                        <?php if (!$is_read): ?>
                                                            <li>
                                                                <form method="POST" class="d-inline">
                                                                    <input type="hidden" name="action" value="mark_read">
                                                                    <input type="hidden" name="notification_id" value="<?= $id ?>">
                                                                    <button type="submit" class="dropdown-item">
                                                                        <i class="fas fa-check me-2"></i>Mark as Read
                                                                    </button>
                                                                </form>
                                                            </li>
                                                        <?php endif; ?>
                                                        <li>
                                                            <form method="POST" class="d-inline" onsubmit="return confirm('Yakin ingin menghapus notifikasi ini?')">
                                                                <input type="hidden" name="action" value="delete_notification">
                                                                <input type="hidden" name="notification_id" value="<?= $id ?>">
                                                                <button type="submit" class="dropdown-item text-danger">
                                                                    <i class="fas fa-trash me-2"></i>Delete
                                                                </button>
                                                            </form>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>

                    <!-- Pagination -->
                    <?php if ($totalNotifications > $limit): ?>
                        <nav aria-label="Notifications pagination">
                            <ul class="pagination justify-content-center">
                                <?php
                                $totalPages = ceil($totalNotifications / $limit);
                                $currentPage = $page;

                                // Previous button
                                if ($currentPage > 1): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?page=<?= $currentPage - 1 ?>&filter=<?= $filter ?>">Previous</a>
                                    </li>
                                <?php endif;

                                // Page numbers
                                for ($i = max(1, $currentPage - 2); $i <= min($totalPages, $currentPage + 2); $i++): ?>
                                    <li class="page-item <?= $i == $currentPage ? 'active' : '' ?>">
                                        <a class="page-link" href="?page=<?= $i ?>&filter=<?= $filter ?>"><?= $i ?></a>
                                    </li>
                                <?php endfor;

                                // Next button
                                if ($currentPage < $totalPages): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?page=<?= $currentPage + 1 ?>&filter=<?= $filter ?>">Next</a>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </nav>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Test Notification Modal -->
<div class="modal fade" id="testNotificationModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add Test Notification</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="add_test_notification">
                    <div class="mb-3">
                        <label class="form-label">Type</label>
                        <select class="form-select" name="test_type" required>
                            <option value="info">Info</option>
                            <option value="success">Success</option>
                            <option value="warning">Warning</option>
                            <option value="error">Error</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Title</label>
                        <input type="text" class="form-control" name="test_title" value="Test Notification" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Message</label>
                        <textarea class="form-control" name="test_message" rows="3" required>This is a test notification message</textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Notification</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php include 'includes/views/layouts/footer.php'; ?>
