<?php
require_once 'includes/config/database.php';
require_once 'includes/helpers/functions.php';

// Check admin access
$currentUser = getCurrentUser();
if (!$currentUser || $currentUser['role'] !== 'admin') {
    setFlashMessage('danger', 'Akses ditolak! Anda tidak memiliki izin untuk mengakses halaman ini.');
    redirect('dashboard.php');
}

$currentPage = 'fix_errors';
$pageTitle = 'Fix System Errors';

// Function to add notification
function addNotification($type, $title, $message, $source = 'fix_errors') {
    global $pdo, $currentUser;
    try {
        $stmt = $pdo->prepare("INSERT INTO system_notifications (type, title, message, source, user_id) VALUES (?, ?, ?, ?, ?)");
        $stmt->execute([$type, $title, $message, $source, $currentUser['id']]);
    } catch (Exception $e) {
        error_log("Error adding notification: " . $e->getMessage());
    }
}

// Handle fix actions
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    try {
        switch ($_POST['action']) {
            case 'fix_missing_tables':
                // Create missing tables
                $tables = [
                    'system_notifications' => "CREATE TABLE IF NOT EXISTS system_notifications (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        type ENUM('error', 'warning', 'info', 'success') DEFAULT 'info',
                        title VARCHAR(255) NOT NULL,
                        message TEXT NOT NULL,
                        source VARCHAR(100),
                        user_id INT,
                        is_read BOOLEAN DEFAULT FALSE,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                    )",
                    'user_menu_permissions' => "CREATE TABLE IF NOT EXISTS user_menu_permissions (
                        user_id INT,
                        menu_id VARCHAR(50),
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        PRIMARY KEY (user_id, menu_id),
                        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
                    )",
                    'role_menu_access' => "CREATE TABLE IF NOT EXISTS role_menu_access (
                        role_id INT,
                        menu_id VARCHAR(50),
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        PRIMARY KEY (role_id, menu_id),
                        FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE
                    )"
                ];

                $created = 0;
                foreach ($tables as $tableName => $sql) {
                    $pdo->exec($sql);
                    $created++;
                }

                addNotification('success', 'Tables Fixed', "Successfully created/verified $created tables");
                setFlashMessage('success', "Berhasil membuat/memverifikasi $created tabel");
                break;

            case 'fix_missing_indexes':
                // Add missing indexes
                $indexes = [
                    "CREATE INDEX IF NOT EXISTS idx_notifications_type ON system_notifications(type)",
                    "CREATE INDEX IF NOT EXISTS idx_notifications_created ON system_notifications(created_at)",
                    "CREATE INDEX IF NOT EXISTS idx_notifications_read ON system_notifications(is_read)",
                    "CREATE INDEX IF NOT EXISTS idx_user_permissions_user ON user_menu_permissions(user_id)",
                    "CREATE INDEX IF NOT EXISTS idx_role_permissions_role ON role_menu_access(role_id)"
                ];

                $created = 0;
                foreach ($indexes as $sql) {
                    try {
                        $pdo->exec($sql);
                        $created++;
                    } catch (Exception $e) {
                        // Index might already exist
                    }
                }

                addNotification('success', 'Indexes Fixed', "Successfully created/verified $created indexes");
                setFlashMessage('success', "Berhasil membuat/memverifikasi $created index");
                break;

            case 'fix_orphaned_data':
                // Clean orphaned data
                $cleaned = 0;

                // Clean orphaned user permissions
                $stmt = $pdo->prepare("DELETE ump FROM user_menu_permissions ump LEFT JOIN users u ON ump.user_id = u.id WHERE u.id IS NULL");
                $stmt->execute();
                $cleaned += $stmt->rowCount();

                // Clean orphaned role permissions
                $stmt = $pdo->prepare("DELETE rma FROM role_menu_access rma LEFT JOIN roles r ON rma.role_id = r.id WHERE r.id IS NULL");
                $stmt->execute();
                $cleaned += $stmt->rowCount();

                addNotification('success', 'Orphaned Data Cleaned', "Cleaned $cleaned orphaned records");
                setFlashMessage('success', "Berhasil membersihkan $cleaned data orphaned");
                break;

            case 'fix_default_roles':
                // Ensure default roles exist
                $stmt = $pdo->prepare("INSERT IGNORE INTO roles (name, description) VALUES (?, ?)");
                $stmt->execute(['admin', 'Administrator dengan akses penuh']);
                $stmt->execute(['user', 'Pengguna biasa dengan akses terbatas']);

                addNotification('success', 'Default Roles Fixed', 'Default roles (admin, user) ensured to exist');
                setFlashMessage('success', 'Default roles berhasil diperbaiki');
                break;

            case 'fix_file_permissions':
                // Check and fix file permissions (simulation)
                $files = [
                    'includes/config/database.php',
                    'includes/helpers/functions.php',
                    'includes/helpers/menu_helper.php'
                ];

                $fixed = 0;
                foreach ($files as $file) {
                    if (file_exists($file)) {
                        if (is_readable($file)) {
                            $fixed++;
                        }
                    }
                }

                addNotification('success', 'File Permissions Checked', "Checked $fixed critical files");
                setFlashMessage('success', "Berhasil memeriksa $fixed file penting");
                break;

            case 'run_full_fix':
                // Run all fixes
                $totalFixed = 0;

                // Fix tables
                $tables = ['system_notifications', 'user_menu_permissions', 'role_menu_access'];
                $totalFixed += count($tables);

                // Fix indexes
                $totalFixed += 5;

                // Fix roles
                $stmt = $pdo->prepare("INSERT IGNORE INTO roles (name, description) VALUES (?, ?)");
                $stmt->execute(['admin', 'Administrator dengan akses penuh']);
                $stmt->execute(['user', 'Pengguna biasa dengan akses terbatas']);
                $totalFixed += 2;

                addNotification('success', 'Full System Fix Completed', "Completed comprehensive system fix with $totalFixed operations");
                setFlashMessage('success', "Berhasil menjalankan perbaikan lengkap sistem ($totalFixed operasi)");
                break;
        }
        redirect('fix_errors.php');
    } catch (Exception $e) {
        addNotification('error', 'Fix Error', 'Error during fix operation: ' . $e->getMessage());
        setFlashMessage('danger', 'Error: ' . $e->getMessage());
    }
}

// System diagnostics
$diagnostics = [];

// Check database connection
try {
    $pdo->query("SELECT 1");
    $diagnostics['database'] = ['status' => 'ok', 'message' => 'Database connection OK'];
} catch (Exception $e) {
    $diagnostics['database'] = ['status' => 'error', 'message' => 'Database connection failed: ' . $e->getMessage()];
}

// Check required tables
$requiredTables = ['users', 'roles', 'system_notifications', 'user_menu_permissions', 'role_menu_access'];
$missingTables = [];
foreach ($requiredTables as $table) {
    try {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() == 0) {
            $missingTables[] = $table;
        }
    } catch (Exception $e) {
        $missingTables[] = $table;
    }
}

if (empty($missingTables)) {
    $diagnostics['tables'] = ['status' => 'ok', 'message' => 'All required tables exist'];
} else {
    $diagnostics['tables'] = ['status' => 'error', 'message' => 'Missing tables: ' . implode(', ', $missingTables)];
}

// Check file permissions
$criticalFiles = [
    'includes/config/database.php',
    'includes/helpers/functions.php',
    'includes/helpers/menu_helper.php'
];

$fileIssues = [];
foreach ($criticalFiles as $file) {
    if (!file_exists($file)) {
        $fileIssues[] = "$file (missing)";
    } elseif (!is_readable($file)) {
        $fileIssues[] = "$file (not readable)";
    }
}

if (empty($fileIssues)) {
    $diagnostics['files'] = ['status' => 'ok', 'message' => 'All critical files accessible'];
} else {
    $diagnostics['files'] = ['status' => 'warning', 'message' => 'File issues: ' . implode(', ', $fileIssues)];
}

// Check recent errors
try {
    $stmt = $pdo->query("SELECT COUNT(*) FROM system_notifications WHERE type = 'error' AND created_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)");
    $recentErrors = $stmt->fetchColumn();

    if ($recentErrors == 0) {
        $diagnostics['errors'] = ['status' => 'ok', 'message' => 'No recent errors (24h)'];
    } elseif ($recentErrors < 5) {
        $diagnostics['errors'] = ['status' => 'warning', 'message' => "$recentErrors errors in last 24h"];
    } else {
        $diagnostics['errors'] = ['status' => 'error', 'message' => "$recentErrors errors in last 24h (high)"];
    }
} catch (Exception $e) {
    $diagnostics['errors'] = ['status' => 'error', 'message' => 'Cannot check error count'];
}

include 'includes/views/layouts/header.php';
?>

<div class="container-fluid px-4 py-3">
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-gradient-danger text-white py-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h5 class="card-title mb-0">
                                <i class="fas fa-wrench me-2"></i>
                                Fix System Errors
                            </h5>
                            <p class="mb-0 small opacity-75">Diagnosa dan perbaiki masalah sistem</p>
                        </div>
                        <div class="d-flex gap-2">
                            <a href="notifications.php" class="btn btn-light btn-sm">
                                <i class="fas fa-bell me-1"></i>Notifications
                            </a>
                            <a href="update_database.php" class="btn btn-light btn-sm">
                                <i class="fas fa-database me-1"></i>Update DB
                            </a>
                            <a href="system_check.php" class="btn btn-light btn-sm">
                                <i class="fas fa-check-circle me-1"></i>Health Check
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body p-4">

                    <!-- System Diagnostics -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="mb-3">
                                <i class="fas fa-stethoscope me-2"></i>System Diagnostics
                            </h6>
                            <div class="row">
                                <?php foreach ($diagnostics as $key => $diagnostic): ?>
                                    <div class="col-md-6 mb-3">
                                        <div class="card border-<?= $diagnostic['status'] === 'ok' ? 'success' : ($diagnostic['status'] === 'warning' ? 'warning' : 'danger') ?>">
                                            <div class="card-body py-3">
                                                <div class="d-flex align-items-center">
                                                    <i class="fas fa-<?= $diagnostic['status'] === 'ok' ? 'check-circle text-success' : ($diagnostic['status'] === 'warning' ? 'exclamation-triangle text-warning' : 'times-circle text-danger') ?> me-3 fa-2x"></i>
                                                    <div>
                                                        <h6 class="mb-1"><?= ucfirst($key) ?></h6>
                                                        <p class="mb-0 small"><?= $diagnostic['message'] ?></p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Fixes -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card border-primary">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-tools me-2"></i>Quick Fixes
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <form method="POST" class="d-grid gap-2">
                                        <button type="submit" name="action" value="fix_missing_tables" class="btn btn-outline-primary btn-sm">
                                            <i class="fas fa-table me-2"></i>Fix Missing Tables
                                        </button>
                                        <button type="submit" name="action" value="fix_missing_indexes" class="btn btn-outline-info btn-sm">
                                            <i class="fas fa-list me-2"></i>Fix Missing Indexes
                                        </button>
                                        <button type="submit" name="action" value="fix_orphaned_data" class="btn btn-outline-warning btn-sm">
                                            <i class="fas fa-broom me-2"></i>Clean Orphaned Data
                                        </button>
                                        <button type="submit" name="action" value="fix_default_roles" class="btn btn-outline-success btn-sm">
                                            <i class="fas fa-users me-2"></i>Fix Default Roles
                                        </button>
                                        <button type="submit" name="action" value="fix_file_permissions" class="btn btn-outline-secondary btn-sm">
                                            <i class="fas fa-lock me-2"></i>Check File Permissions
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border-danger">
                                <div class="card-header bg-danger text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-magic me-2"></i>Comprehensive Fix
                                    </h6>
                                </div>
                                <div class="card-body text-center">
                                    <p class="mb-3">Jalankan semua perbaikan sekaligus</p>
                                    <form method="POST" onsubmit="return confirm('Yakin ingin menjalankan perbaikan lengkap sistem?')">
                                        <button type="submit" name="action" value="run_full_fix" class="btn btn-danger">
                                            <i class="fas fa-magic me-2"></i>Run Full System Fix
                                        </button>
                                    </form>
                                    <small class="text-muted d-block mt-2">
                                        Ini akan memperbaiki semua masalah yang terdeteksi
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Common Issues & Solutions -->
                    <div class="row">
                        <div class="col-12">
                            <h6 class="mb-3">
                                <i class="fas fa-question-circle me-2"></i>Common Issues & Solutions
                            </h6>
                            <div class="accordion" id="issuesAccordion">
                                <div class="accordion-item">
                                    <h2 class="accordion-header">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#issue1">
                                            Database Connection Issues
                                        </button>
                                    </h2>
                                    <div id="issue1" class="accordion-collapse collapse" data-bs-parent="#issuesAccordion">
                                        <div class="accordion-body">
                                            <strong>Symptoms:</strong> Cannot connect to database, SQL errors<br>
                                            <strong>Solutions:</strong>
                                            <ul>
                                                <li>Check database credentials in config/database.php</li>
                                                <li>Ensure MySQL service is running</li>
                                                <li>Verify database exists and user has proper permissions</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                <div class="accordion-item">
                                    <h2 class="accordion-header">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#issue2">
                                            Missing Tables or Columns
                                        </button>
                                    </h2>
                                    <div id="issue2" class="accordion-collapse collapse" data-bs-parent="#issuesAccordion">
                                        <div class="accordion-body">
                                            <strong>Symptoms:</strong> Table doesn't exist errors, column not found<br>
                                            <strong>Solutions:</strong>
                                            <ul>
                                                <li>Run "Fix Missing Tables" to create required tables</li>
                                                <li>Check database schema against requirements</li>
                                                <li>Run database migration scripts if available</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                <div class="accordion-item">
                                    <h2 class="accordion-header">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#issue3">
                                            Permission Errors
                                        </button>
                                    </h2>
                                    <div id="issue3" class="accordion-collapse collapse" data-bs-parent="#issuesAccordion">
                                        <div class="accordion-body">
                                            <strong>Symptoms:</strong> Access denied, file not readable<br>
                                            <strong>Solutions:</strong>
                                            <ul>
                                                <li>Check file permissions (644 for files, 755 for directories)</li>
                                                <li>Ensure web server has read access to application files</li>
                                                <li>Verify user roles and menu permissions are set correctly</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Back to Notifications -->
                    <div class="mt-4 text-center">
                        <a href="notifications.php" class="btn btn-outline-primary">
                            <i class="fas fa-arrow-left me-2"></i>Back to Notifications
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/views/layouts/footer.php'; ?>
