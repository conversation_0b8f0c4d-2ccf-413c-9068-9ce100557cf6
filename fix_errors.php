<?php
require_once 'includes/config/database.php';
require_once 'includes/helpers/function_check.php';
require_once 'includes/helpers/functions.php';

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in and is admin
if (!isLoggedIn()) {
    setFlashMessage('danger', 'Silakan login terlebih dahulu');
    redirect('login.php');
}

$currentUser = getCurrentUser();
if ($currentUser['role'] !== 'admin') {
    setFlashMessage('danger', 'Akses ditolak. Hanya admin yang dapat mengakses halaman ini.');
    redirect('dashboard.php');
}

$currentPage = 'fix_errors';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'fix_database_issues':
                $result = fixDatabaseIssues();
                if ($result['success']) {
                    setFlashMessage('success', $result['message']);
                } else {
                    setFlashMessage('danger', $result['message']);
                }
                break;
                
            case 'fix_file_permissions':
                $result = fixFilePermissions();
                if ($result['success']) {
                    setFlashMessage('success', $result['message']);
                } else {
                    setFlashMessage('danger', $result['message']);
                }
                break;
                
            case 'clear_error_logs':
                $result = clearErrorLogs();
                if ($result['success']) {
                    setFlashMessage('success', $result['message']);
                } else {
                    setFlashMessage('danger', $result['message']);
                }
                break;
                
            case 'reset_user_sessions':
                $result = resetUserSessions();
                if ($result['success']) {
                    setFlashMessage('success', $result['message']);
                } else {
                    setFlashMessage('danger', $result['message']);
                }
                break;
                
            case 'fix_missing_tables':
                $result = fixMissingTables();
                if ($result['success']) {
                    setFlashMessage('success', $result['message']);
                } else {
                    setFlashMessage('danger', $result['message']);
                }
                break;
        }
        redirect('fix_errors.php');
    }
}

// Detect system issues
$systemIssues = detectSystemIssues();

// Include header and sidebar
require_once 'includes/views/layouts/header.php';
require_once 'includes/views/layouts/sidebar.php';

/**
 * Detect system issues
 */
function detectSystemIssues() {
    $issues = [
        'database' => detectDatabaseIssues(),
        'files' => detectFileIssues(),
        'permissions' => detectPermissionIssues(),
        'sessions' => detectSessionIssues(),
        'logs' => detectLogIssues()
    ];
    
    return $issues;
}

/**
 * Detect database issues
 */
function detectDatabaseIssues() {
    global $pdo;
    $issues = [];
    
    try {
        // Check for missing tables
        $requiredTables = [
            'users' => 'CREATE TABLE IF NOT EXISTS users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                username VARCHAR(50) UNIQUE NOT NULL,
                email VARCHAR(100) UNIQUE NOT NULL,
                password VARCHAR(255) NOT NULL,
                role ENUM("admin", "user") DEFAULT "user",
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )',
            'transaksi' => 'CREATE TABLE IF NOT EXISTS transaksi (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                jenis ENUM("pemasukan", "pengeluaran") NOT NULL,
                jumlah DECIMAL(15,2) NOT NULL,
                kategori_id INT,
                keterangan TEXT,
                tanggal DATE NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
            )',
            'kategori' => 'CREATE TABLE IF NOT EXISTS kategori (
                id INT AUTO_INCREMENT PRIMARY KEY,
                nama VARCHAR(100) NOT NULL,
                jenis ENUM("pemasukan", "pengeluaran") NOT NULL,
                user_id INT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
            )',
            'notifications' => 'CREATE TABLE IF NOT EXISTS notifications (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                title VARCHAR(255) NOT NULL,
                message TEXT NOT NULL,
                type ENUM("info", "success", "warning", "danger") DEFAULT "info",
                is_read BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
            )'
        ];
        
        $missingTables = [];
        foreach ($requiredTables as $table => $createSQL) {
            $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
            if ($stmt->rowCount() === 0) {
                $missingTables[$table] = $createSQL;
            }
        }
        
        if (!empty($missingTables)) {
            $issues[] = [
                'type' => 'missing_tables',
                'severity' => 'high',
                'title' => 'Missing Database Tables',
                'description' => 'Some required database tables are missing',
                'details' => 'Missing tables: ' . implode(', ', array_keys($missingTables)),
                'fixable' => true,
                'fix_data' => $missingTables
            ];
        }
        
        // Check for corrupted tables
        $stmt = $pdo->query("SHOW TABLES");
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        $corruptedTables = [];
        
        foreach ($tables as $table) {
            try {
                $checkStmt = $pdo->query("CHECK TABLE `$table`");
                $result = $checkStmt->fetch();
                if ($result['Msg_text'] !== 'OK') {
                    $corruptedTables[] = $table;
                }
            } catch (Exception $e) {
                $corruptedTables[] = $table;
            }
        }
        
        if (!empty($corruptedTables)) {
            $issues[] = [
                'type' => 'corrupted_tables',
                'severity' => 'medium',
                'title' => 'Corrupted Database Tables',
                'description' => 'Some database tables may be corrupted',
                'details' => 'Affected tables: ' . implode(', ', $corruptedTables),
                'fixable' => true
            ];
        }
        
    } catch (Exception $e) {
        $issues[] = [
            'type' => 'database_connection',
            'severity' => 'high',
            'title' => 'Database Connection Error',
            'description' => 'Cannot connect to database',
            'details' => $e->getMessage(),
            'fixable' => false
        ];
    }
    
    return $issues;
}

/**
 * Detect file issues
 */
function detectFileIssues() {
    $issues = [];
    
    // Check for missing critical files
    $criticalFiles = [
        'includes/config/database.php' => 'Database configuration file',
        'includes/helpers/functions.php' => 'Core functions file',
        'includes/views/layouts/header.php' => 'Header layout file',
        'includes/views/layouts/sidebar.php' => 'Sidebar layout file',
        'includes/views/layouts/footer.php' => 'Footer layout file'
    ];
    
    $missingFiles = [];
    foreach ($criticalFiles as $file => $description) {
        if (!file_exists($file)) {
            $missingFiles[$file] = $description;
        }
    }
    
    if (!empty($missingFiles)) {
        $issues[] = [
            'type' => 'missing_files',
            'severity' => 'high',
            'title' => 'Missing Critical Files',
            'description' => 'Some critical system files are missing',
            'details' => 'Missing files: ' . implode(', ', array_keys($missingFiles)),
            'fixable' => false
        ];
    }
    
    return $issues;
}

/**
 * Detect permission issues
 */
function detectPermissionIssues() {
    $issues = [];
    
    // Check directory permissions
    $requiredDirs = [
        'uploads' => 'File upload directory',
        'cache' => 'Cache directory',
        'logs' => 'Log files directory',
        'backups' => 'Backup files directory'
    ];
    
    $permissionIssues = [];
    foreach ($requiredDirs as $dir => $description) {
        if (!is_dir($dir)) {
            // Try to create directory
            if (!mkdir($dir, 0755, true)) {
                $permissionIssues[$dir] = "Directory does not exist and cannot be created";
            }
        } elseif (!is_writable($dir)) {
            $permissionIssues[$dir] = "Directory is not writable";
        }
    }
    
    if (!empty($permissionIssues)) {
        $issues[] = [
            'type' => 'permission_issues',
            'severity' => 'medium',
            'title' => 'Directory Permission Issues',
            'description' => 'Some directories have permission problems',
            'details' => implode(', ', array_keys($permissionIssues)),
            'fixable' => true,
            'fix_data' => $permissionIssues
        ];
    }
    
    return $issues;
}

/**
 * Detect session issues
 */
function detectSessionIssues() {
    $issues = [];
    
    // Check if sessions are working
    if (session_status() !== PHP_SESSION_ACTIVE) {
        $issues[] = [
            'type' => 'session_not_active',
            'severity' => 'medium',
            'title' => 'Session Not Active',
            'description' => 'PHP sessions are not properly configured',
            'details' => 'Session status: ' . session_status(),
            'fixable' => true
        ];
    }
    
    return $issues;
}

/**
 * Detect log issues
 */
function detectLogIssues() {
    $issues = [];
    
    // Check log file sizes
    $logFiles = ['logs/error.log', 'logs/system.log', 'logs/access.log'];
    $largeLogs = [];
    
    foreach ($logFiles as $logFile) {
        if (file_exists($logFile)) {
            $size = filesize($logFile);
            if ($size > 10 * 1024 * 1024) { // 10MB
                $largeLogs[$logFile] = formatBytes($size);
            }
        }
    }
    
    if (!empty($largeLogs)) {
        $issues[] = [
            'type' => 'large_log_files',
            'severity' => 'low',
            'title' => 'Large Log Files',
            'description' => 'Some log files are getting large',
            'details' => 'Large logs: ' . implode(', ', array_keys($largeLogs)),
            'fixable' => true
        ];
    }
    
    return $issues;
}

/**
 * Fix database issues
 */
function fixDatabaseIssues() {
    global $pdo;
    
    try {
        $fixed = 0;
        
        // Get database issues
        $issues = detectDatabaseIssues();
        
        foreach ($issues as $issue) {
            if ($issue['type'] === 'missing_tables' && isset($issue['fix_data'])) {
                foreach ($issue['fix_data'] as $table => $createSQL) {
                    try {
                        $pdo->exec($createSQL);
                        $fixed++;
                    } catch (Exception $e) {
                        error_log("Failed to create table $table: " . $e->getMessage());
                    }
                }
            } elseif ($issue['type'] === 'corrupted_tables') {
                // Try to repair corrupted tables
                $stmt = $pdo->query("SHOW TABLES");
                $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
                
                foreach ($tables as $table) {
                    try {
                        $pdo->exec("REPAIR TABLE `$table`");
                        $fixed++;
                    } catch (Exception $e) {
                        error_log("Failed to repair table $table: " . $e->getMessage());
                    }
                }
            }
        }
        
        logSystemEvent("Database issues fixed", 'info', ['issues_fixed' => $fixed]);
        
        return [
            'success' => true,
            'message' => "Database issues fixed successfully. $fixed issues resolved."
        ];
        
    } catch (Exception $e) {
        error_log("Fix database issues error: " . $e->getMessage());
        return [
            'success' => false,
            'message' => 'Failed to fix database issues: ' . $e->getMessage()
        ];
    }
}

/**
 * Fix file permissions
 */
function fixFilePermissions() {
    try {
        $fixed = 0;
        
        $requiredDirs = ['uploads', 'cache', 'logs', 'backups'];
        
        foreach ($requiredDirs as $dir) {
            if (!is_dir($dir)) {
                if (mkdir($dir, 0755, true)) {
                    $fixed++;
                }
            } else {
                if (chmod($dir, 0755)) {
                    $fixed++;
                }
            }
        }
        
        logSystemEvent("File permissions fixed", 'info', ['directories_fixed' => $fixed]);
        
        return [
            'success' => true,
            'message' => "File permissions fixed successfully. $fixed directories processed."
        ];
        
    } catch (Exception $e) {
        error_log("Fix file permissions error: " . $e->getMessage());
        return [
            'success' => false,
            'message' => 'Failed to fix file permissions: ' . $e->getMessage()
        ];
    }
}

/**
 * Clear error logs
 */
function clearErrorLogs() {
    try {
        $cleared = 0;
        
        $logFiles = ['logs/error.log', 'logs/system.log', 'logs/access.log'];
        
        foreach ($logFiles as $logFile) {
            if (file_exists($logFile)) {
                if (file_put_contents($logFile, '') !== false) {
                    $cleared++;
                }
            }
        }
        
        logSystemEvent("Error logs cleared", 'info', ['logs_cleared' => $cleared]);
        
        return [
            'success' => true,
            'message' => "Error logs cleared successfully. $cleared log files processed."
        ];
        
    } catch (Exception $e) {
        error_log("Clear error logs error: " . $e->getMessage());
        return [
            'success' => false,
            'message' => 'Failed to clear error logs: ' . $e->getMessage()
        ];
    }
}

/**
 * Reset user sessions
 */
function resetUserSessions() {
    try {
        // Clear session data
        if (session_status() === PHP_SESSION_ACTIVE) {
            session_destroy();
        }
        
        // Clear session files if using file-based sessions
        $sessionPath = session_save_path();
        if ($sessionPath && is_dir($sessionPath)) {
            $files = glob($sessionPath . '/sess_*');
            $cleared = 0;
            foreach ($files as $file) {
                if (unlink($file)) {
                    $cleared++;
                }
            }
        }
        
        logSystemEvent("User sessions reset", 'info');
        
        return [
            'success' => true,
            'message' => "User sessions reset successfully."
        ];
        
    } catch (Exception $e) {
        error_log("Reset user sessions error: " . $e->getMessage());
        return [
            'success' => false,
            'message' => 'Failed to reset user sessions: ' . $e->getMessage()
        ];
    }
}

/**
 * Fix missing tables
 */
function fixMissingTables() {
    global $pdo;
    
    try {
        $created = 0;
        
        // Run database creation script
        if (file_exists('database/create_all_tables.sql')) {
            $sql = file_get_contents('database/create_all_tables.sql');
            $statements = explode(';', $sql);
            
            foreach ($statements as $statement) {
                $statement = trim($statement);
                if (!empty($statement)) {
                    try {
                        $pdo->exec($statement);
                        $created++;
                    } catch (Exception $e) {
                        // Continue with other statements
                    }
                }
            }
        }
        
        logSystemEvent("Missing tables fixed", 'info', ['tables_created' => $created]);
        
        return [
            'success' => true,
            'message' => "Missing tables fixed successfully. $created statements executed."
        ];
        
    } catch (Exception $e) {
        error_log("Fix missing tables error: " . $e->getMessage());
        return [
            'success' => false,
            'message' => 'Failed to fix missing tables: ' . $e->getMessage()
        ];
    }
}
?>

<div class="modern-layout">
    <div class="modern-container">
        <!-- Modern Page Header -->
        <div class="modern-page-header modern-flex modern-justify-between modern-items-center modern-mb-xl">
            <div>
                <h1 class="modern-page-title modern-mb-0">Fix System Errors</h1>
                <p class="modern-page-subtitle">Detect and fix common system issues automatically</p>
            </div>
            <div class="modern-page-actions">
                <button type="button" class="modern-btn modern-btn-primary" onclick="location.reload()">
                    <i class="fas fa-sync-alt"></i>
                    Refresh Scan
                </button>
            </div>
        </div>

        <!-- Flash Messages -->
        <?php if ($flash = getFlashMessage()): ?>
        <div class="modern-alert modern-alert-<?= $flash['type'] ?> modern-mb-lg">
            <div class="modern-alert-content">
                <i class="fas fa-<?= $flash['type'] === 'success' ? 'check-circle' : 'exclamation-triangle' ?> modern-alert-icon"></i>
                <div class="modern-alert-message"><?= $flash['message'] ?></div>
            </div>
            <button type="button" class="modern-alert-close" onclick="this.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <?php endif; ?>

        <!-- Quick Fix Actions -->
        <div class="modern-card modern-mb-lg">
            <div class="modern-card-header">
                <h5 class="modern-card-title">
                    <i class="fas fa-magic modern-text-primary modern-mr-sm"></i>
                    Quick Fix Actions
                </h5>
            </div>
            <div class="modern-card-body">
                <div class="modern-grid modern-grid-cols-5 modern-gap-sm">
                    <form method="POST" style="display: inline;">
                        <input type="hidden" name="action" value="fix_database_issues">
                        <button type="submit" class="modern-btn modern-btn-success modern-w-full" onclick="return confirm('Fix database issues? This may take a few moments.')">
                            <i class="fas fa-database"></i>
                            Fix Database
                        </button>
                    </form>
                    
                    <form method="POST" style="display: inline;">
                        <input type="hidden" name="action" value="fix_file_permissions">
                        <button type="submit" class="modern-btn modern-btn-warning modern-w-full" onclick="return confirm('Fix file permissions?')">
                            <i class="fas fa-folder"></i>
                            Fix Permissions
                        </button>
                    </form>
                    
                    <form method="POST" style="display: inline;">
                        <input type="hidden" name="action" value="clear_error_logs">
                        <button type="submit" class="modern-btn modern-btn-info modern-w-full" onclick="return confirm('Clear all error logs?')">
                            <i class="fas fa-trash"></i>
                            Clear Logs
                        </button>
                    </form>
                    
                    <form method="POST" style="display: inline;">
                        <input type="hidden" name="action" value="reset_user_sessions">
                        <button type="submit" class="modern-btn modern-btn-danger modern-w-full" onclick="return confirm('Reset all user sessions? Users will need to login again.')">
                            <i class="fas fa-users"></i>
                            Reset Sessions
                        </button>
                    </form>
                    
                    <form method="POST" style="display: inline;">
                        <input type="hidden" name="action" value="fix_missing_tables">
                        <button type="submit" class="modern-btn modern-btn-primary modern-w-full" onclick="return confirm('Create missing database tables?')">
                            <i class="fas fa-table"></i>
                            Fix Tables
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- System Issues -->
        <?php foreach ($systemIssues as $category => $issues): ?>
        <?php if (!empty($issues)): ?>
        <div class="modern-card modern-mb-lg">
            <div class="modern-card-header">
                <h5 class="modern-card-title">
                    <i class="fas fa-<?= getIconForIssueCategory($category) ?> modern-text-primary modern-mr-sm"></i>
                    <?= ucfirst($category) ?> Issues
                </h5>
                <div class="modern-card-actions">
                    <span class="modern-badge modern-badge-danger">
                        <?= count($issues) ?> issue(s)
                    </span>
                </div>
            </div>
            <div class="modern-card-body modern-p-0">
                <div class="modern-issues-list">
                    <?php foreach ($issues as $issue): ?>
                    <div class="modern-issue-item modern-issue-<?= $issue['severity'] ?>">
                        <div class="modern-issue-icon">
                            <i class="fas fa-<?= getSeverityIcon($issue['severity']) ?>"></i>
                        </div>
                        <div class="modern-issue-content">
                            <div class="modern-issue-title"><?= htmlspecialchars($issue['title']) ?></div>
                            <div class="modern-issue-description"><?= htmlspecialchars($issue['description']) ?></div>
                            <div class="modern-issue-details"><?= htmlspecialchars($issue['details']) ?></div>
                        </div>
                        <div class="modern-issue-status">
                            <?php if ($issue['fixable']): ?>
                            <span class="modern-badge modern-badge-success">
                                <i class="fas fa-wrench"></i>
                                Fixable
                            </span>
                            <?php else: ?>
                            <span class="modern-badge modern-badge-warning">
                                <i class="fas fa-exclamation-triangle"></i>
                                Manual Fix Required
                            </span>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
        <?php endif; ?>
        <?php endforeach; ?>

        <!-- No Issues Found -->
        <?php 
        $totalIssues = 0;
        foreach ($systemIssues as $issues) {
            $totalIssues += count($issues);
        }
        ?>
        <?php if ($totalIssues === 0): ?>
        <div class="modern-card">
            <div class="modern-card-body modern-text-center modern-py-xl">
                <div class="modern-success-icon modern-mb-lg">
                    <i class="fas fa-check-circle"></i>
                </div>
                <h4 class="modern-text-success modern-mb-sm">No Issues Found!</h4>
                <p class="modern-text-muted">Your system is running smoothly without any detected issues.</p>
            </div>
        </div>
        <?php endif; ?>
    </div>
</div>

<?php include 'includes/views/layouts/footer.php'; ?>

<?php
/**
 * Get icon for issue category
 */
function getIconForIssueCategory($category) {
    $icons = [
        'database' => 'database',
        'files' => 'file',
        'permissions' => 'lock',
        'sessions' => 'users',
        'logs' => 'list'
    ];
    
    return $icons[$category] ?? 'exclamation-triangle';
}

/**
 * Get icon for severity
 */
function getSeverityIcon($severity) {
    $icons = [
        'high' => 'times-circle',
        'medium' => 'exclamation-triangle',
        'low' => 'info-circle'
    ];
    
    return $icons[$severity] ?? 'exclamation-triangle';
}
?>

<style>
.modern-issues-list {
    display: flex;
    flex-direction: column;
}

.modern-issue-item {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    padding: 20px;
    border-bottom: 1px solid #eee;
    border-left: 4px solid;
}

.modern-issue-item:last-child {
    border-bottom: none;
}

.modern-issue-high { border-left-color: #dc3545; }
.modern-issue-medium { border-left-color: #ffc107; }
.modern-issue-low { border-left-color: #17a2b8; }

.modern-issue-icon {
    font-size: 20px;
    margin-top: 2px;
}

.modern-issue-high .modern-issue-icon { color: #dc3545; }
.modern-issue-medium .modern-issue-icon { color: #ffc107; }
.modern-issue-low .modern-issue-icon { color: #17a2b8; }

.modern-issue-content {
    flex: 1;
}

.modern-issue-title {
    font-weight: 600;
    font-size: 16px;
    margin-bottom: 5px;
    color: #2c3e50;
}

.modern-issue-description {
    font-size: 14px;
    margin-bottom: 3px;
    color: #495057;
}

.modern-issue-details {
    font-size: 12px;
    color: #6c757d;
    line-height: 1.4;
}

.modern-issue-status {
    margin-top: 5px;
}

.modern-success-icon {
    font-size: 64px;
    color: #28a745;
}
</style>
