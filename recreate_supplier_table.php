<?php
require_once 'includes/config/database.php';

$results = [];

try {
    // 1. Drop existing supplier table if exists
    $pdo->exec("DROP TABLE IF EXISTS supplier");
    $results[] = "✅ Dropped existing supplier table";
    
    // 2. Create new supplier table with proper structure
    $createTableSQL = "
    CREATE TABLE supplier (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        nama_supplier VARCHAR(255) NOT NULL,
        kontak VARCHAR(100) DEFAULT NULL,
        email VARCHAR(255) DEFAULT NULL,
        alamat TEXT DEFAULT NULL,
        keterangan TEXT DEFAULT NULL,
        status ENUM('aktif', 'nonaktif') DEFAULT 'aktif',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_user_id (user_id),
        INDEX idx_status (status),
        INDEX idx_nama (nama_supplier)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($createTableSQL);
    $results[] = "✅ Created new supplier table with proper structure";
    
    // 3. Add foreign key constraint (if users table exists)
    try {
        $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
        if ($stmt->rowCount() > 0) {
            $pdo->exec("ALTER TABLE supplier ADD CONSTRAINT fk_supplier_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE");
            $results[] = "✅ Added foreign key constraint to users table";
        } else {
            $results[] = "⚠️ Users table not found, skipping foreign key constraint";
        }
    } catch (PDOException $e) {
        $results[] = "⚠️ Could not add foreign key constraint: " . $e->getMessage();
    }
    
    // 4. Insert sample data for testing
    $sampleData = [
        [
            'user_id' => 1,
            'nama_supplier' => 'PT Supplier Utama',
            'kontak' => '021-12345678',
            'email' => '<EMAIL>',
            'alamat' => 'Jl. Sudirman No. 123, Jakarta',
            'keterangan' => 'Supplier utama untuk kebutuhan kantor',
            'status' => 'aktif'
        ],
        [
            'user_id' => 1,
            'nama_supplier' => 'CV Mitra Jaya',
            'kontak' => '081234567890',
            'email' => '<EMAIL>',
            'alamat' => 'Jl. Gatot Subroto No. 456, Bandung',
            'keterangan' => 'Supplier untuk kebutuhan produksi',
            'status' => 'aktif'
        ],
        [
            'user_id' => 1,
            'nama_supplier' => 'Toko Berkah',
            'kontak' => '022-87654321',
            'email' => '<EMAIL>',
            'alamat' => 'Jl. Asia Afrika No. 789, Surabaya',
            'keterangan' => 'Supplier lokal untuk kebutuhan harian',
            'status' => 'nonaktif'
        ]
    ];
    
    $stmt = $pdo->prepare("INSERT INTO supplier (user_id, nama_supplier, kontak, email, alamat, keterangan, status) VALUES (?, ?, ?, ?, ?, ?, ?)");
    
    foreach ($sampleData as $data) {
        $stmt->execute([
            $data['user_id'],
            $data['nama_supplier'],
            $data['kontak'],
            $data['email'],
            $data['alamat'],
            $data['keterangan'],
            $data['status']
        ]);
    }
    
    $results[] = "✅ Inserted " . count($sampleData) . " sample suppliers";
    
    // 5. Verify table structure
    $stmt = $pdo->query("DESCRIBE supplier");
    $columns = $stmt->fetchAll();
    $results[] = "✅ Table structure verified - " . count($columns) . " columns created";
    
    // 6. Test basic operations
    $stmt = $pdo->query("SELECT COUNT(*) FROM supplier");
    $count = $stmt->fetchColumn();
    $results[] = "✅ Test SELECT successful - $count records found";
    
    $results[] = "🎉 Supplier table recreated successfully!";
    
} catch (PDOException $e) {
    $results[] = "❌ Database error: " . $e->getMessage();
    $results[] = "❌ Error code: " . $e->getCode();
} catch (Exception $e) {
    $results[] = "❌ General error: " . $e->getMessage();
}

?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Recreate Supplier Table - KeuanganKu</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card shadow">
                    <div class="card-header bg-success text-white">
                        <h4 class="mb-0">
                            <i class="fas fa-redo me-2"></i>Recreate Supplier Table Results
                        </h4>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>Operation Results:</h6>
                            <ul class="mb-0">
                                <?php foreach ($results as $result): ?>
                                <li><?= $result ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                        
                        <?php if (strpos(end($results), '🎉') !== false): ?>
                        <div class="alert alert-success">
                            <h6><i class="fas fa-check-circle me-2"></i>Success!</h6>
                            <p class="mb-0">The supplier table has been recreated successfully with sample data. You can now use the supplier management features.</p>
                        </div>
                        <?php endif; ?>
                        
                        <div class="text-center">
                            <a href="/keuangan/supplier.php" class="btn btn-primary me-2">
                                <i class="fas fa-truck me-2"></i>Go to Supplier Page
                            </a>
                            <a href="/keuangan/diagnose_database.php" class="btn btn-info me-2">
                                <i class="fas fa-stethoscope me-2"></i>Run Diagnostics
                            </a>
                            <a href="/keuangan/index.php" class="btn btn-success">
                                <i class="fas fa-home me-2"></i>Dashboard
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
