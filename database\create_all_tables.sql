-- Create all required tables for the system

-- Create system_settings table
CREATE TABLE IF NOT EXISTS `system_settings` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `setting_key` varchar(100) NOT NULL,
    `setting_value` text,
    `setting_type` enum('string','number','boolean','json') DEFAULT 'string',
    `description` text,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `setting_key` (`setting_key`),
    KEY `idx_setting_type` (`setting_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert default system settings
INSERT INTO `system_settings` (`setting_key`, `setting_value`, `setting_type`, `description`) VALUES
('app_name', '<PERSON>ste<PERSON>', 'string', 'Application name'),
('app_version', '1.6.0', 'string', 'Application version'),
('theme', 'default', 'string', 'Default theme'),
('sidebar_color', 'dark', 'string', 'Sidebar color scheme'),
('navbar_color', 'primary', 'string', 'Navbar color scheme'),
('theme_mode', 'light', 'string', 'Theme mode (light/dark/auto)'),
('primary_color', '#007bff', 'string', 'Primary color'),
('secondary_color', '#6c757d', 'string', 'Secondary color'),
('cache_enabled', '1', 'boolean', 'Enable caching'),
('cache_duration', '3600', 'number', 'Cache duration in seconds'),
('maintenance_mode', '0', 'boolean', 'Maintenance mode'),
('registration_enabled', '1', 'boolean', 'Allow user registration'),
('email_verification', '0', 'boolean', 'Require email verification'),
('two_factor_auth', '0', 'boolean', 'Enable two-factor authentication'),
('session_timeout', '1800', 'number', 'Session timeout in seconds'),
('max_login_attempts', '5', 'number', 'Maximum login attempts'),
('password_min_length', '8', 'number', 'Minimum password length'),
('backup_enabled', '1', 'boolean', 'Enable automatic backups'),
('backup_frequency', 'daily', 'string', 'Backup frequency'),
('log_level', 'info', 'string', 'Logging level'),
('timezone', 'Asia/Jakarta', 'string', 'System timezone'),
('date_format', 'Y-m-d', 'string', 'Date format'),
('time_format', 'H:i:s', 'string', 'Time format'),
('currency', 'IDR', 'string', 'Default currency'),
('language', 'id', 'string', 'Default language')
ON DUPLICATE KEY UPDATE 
    `setting_value` = VALUES(`setting_value`),
    `updated_at` = CURRENT_TIMESTAMP;

-- Create custom_themes table
CREATE TABLE IF NOT EXISTS `custom_themes` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `name` varchar(100) NOT NULL,
    `theme_data` json NOT NULL,
    `is_active` tinyint(1) DEFAULT 0,
    `created_by` int(11) NOT NULL,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_created_by` (`created_by`),
    KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create custom_menus table
CREATE TABLE IF NOT EXISTS `custom_menus` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `menu_id` varchar(50) NOT NULL,
    `label` varchar(100) NOT NULL,
    `icon` varchar(50) DEFAULT NULL,
    `url` varchar(255) DEFAULT NULL,
    `parent_id` int(11) DEFAULT NULL,
    `sort_order` int(11) DEFAULT 0,
    `is_active` tinyint(1) DEFAULT 1,
    `created_by` int(11) NOT NULL,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `menu_id` (`menu_id`),
    KEY `idx_parent_id` (`parent_id`),
    KEY `idx_sort_order` (`sort_order`),
    KEY `idx_is_active` (`is_active`),
    KEY `idx_created_by` (`created_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create layout_components table
CREATE TABLE IF NOT EXISTS `layout_components` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `component_name` varchar(100) NOT NULL,
    `component_type` enum('widget','block','section') DEFAULT 'widget',
    `component_data` json NOT NULL,
    `position` varchar(50) DEFAULT NULL,
    `sort_order` int(11) DEFAULT 0,
    `is_active` tinyint(1) DEFAULT 1,
    `created_by` int(11) NOT NULL,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_component_type` (`component_type`),
    KEY `idx_position` (`position`),
    KEY `idx_sort_order` (`sort_order`),
    KEY `idx_is_active` (`is_active`),
    KEY `idx_created_by` (`created_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create custom_css table
CREATE TABLE IF NOT EXISTS `custom_css` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `css_name` varchar(100) NOT NULL,
    `css_content` longtext NOT NULL,
    `is_active` tinyint(1) DEFAULT 1,
    `created_by` int(11) NOT NULL,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_is_active` (`is_active`),
    KEY `idx_created_by` (`created_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create page_templates table
CREATE TABLE IF NOT EXISTS `page_templates` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `name` varchar(100) NOT NULL,
    `type` enum('page','section','component','layout','email') DEFAULT 'page',
    `category` varchar(50) DEFAULT 'general',
    `description` text,
    `html_content` longtext,
    `css_content` longtext,
    `js_content` longtext,
    `is_active` tinyint(1) DEFAULT 1,
    `created_by` int(11) NOT NULL,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_type` (`type`),
    KEY `idx_category` (`category`),
    KEY `idx_is_active` (`is_active`),
    KEY `idx_created_by` (`created_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create customization_presets table
CREATE TABLE IF NOT EXISTS `customization_presets` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `name` varchar(100) NOT NULL,
    `description` text,
    `preset_data` json NOT NULL,
    `preset_type` enum('theme','layout','complete') DEFAULT 'complete',
    `is_default` tinyint(1) DEFAULT 0,
    `is_active` tinyint(1) DEFAULT 1,
    `created_by` int(11) NOT NULL,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_preset_type` (`preset_type`),
    KEY `idx_is_default` (`is_default`),
    KEY `idx_is_active` (`is_active`),
    KEY `idx_created_by` (`created_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create user_customizations table
CREATE TABLE IF NOT EXISTS `user_customizations` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `user_id` int(11) NOT NULL,
    `customization_key` varchar(100) NOT NULL,
    `customization_value` json NOT NULL,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `unique_user_key` (`user_id`, `customization_key`),
    KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create database_versions table
CREATE TABLE IF NOT EXISTS `database_versions` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `version` varchar(20) NOT NULL,
    `description` text,
    `sql_file` varchar(255) DEFAULT NULL,
    `executed_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `executed_by` varchar(100) DEFAULT NULL,
    `status` enum('success','failed','pending') DEFAULT 'success',
    `error_message` text,
    PRIMARY KEY (`id`),
    UNIQUE KEY `version` (`version`),
    KEY `idx_status` (`status`),
    KEY `idx_executed_at` (`executed_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert initial version records
INSERT INTO `database_versions` (`version`, `description`, `sql_file`, `executed_by`, `status`) VALUES
('1.0.0', 'Initial database setup', 'initial_setup.sql', 'system', 'success'),
('1.1.0', 'Added user management tables', 'user_management.sql', 'system', 'success'),
('1.2.0', 'Added financial management tables', 'financial_tables.sql', 'system', 'success'),
('1.3.0', 'Added business management tables', 'business_tables.sql', 'system', 'success'),
('1.4.0', 'Added notification system', 'notification_system.sql', 'system', 'success'),
('1.5.0', 'Added system settings and customization', 'system_settings.sql', 'system', 'success'),
('1.6.0', 'Added customization features', 'customization_features.sql', 'system', 'success')
ON DUPLICATE KEY UPDATE description = VALUES(description);

-- Create database_migrations table
CREATE TABLE IF NOT EXISTS `database_migrations` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `migration_name` varchar(255) NOT NULL,
    `batch` int(11) NOT NULL,
    `executed_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `migration_name` (`migration_name`),
    KEY `idx_batch` (`batch`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert migration records
INSERT INTO `database_migrations` (`migration_name`, `batch`) VALUES
('2024_01_01_000001_create_users_table', 1),
('2024_01_01_000002_create_roles_table', 1),
('2024_01_01_000003_create_permissions_table', 1),
('2024_01_01_000004_create_financial_tables', 2),
('2024_01_01_000005_create_business_tables', 3),
('2024_01_01_000006_create_notification_tables', 4),
('2024_01_01_000007_create_system_settings', 5),
('2024_01_01_000008_create_customization_tables', 6)
ON DUPLICATE KEY UPDATE batch = VALUES(batch);

-- Create system_info table
CREATE TABLE IF NOT EXISTS `system_info` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `info_key` varchar(100) NOT NULL,
    `info_value` text,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `info_key` (`info_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert system information
INSERT INTO `system_info` (`info_key`, `info_value`) VALUES
('system_version', '1.6.0'),
('database_version', '1.6.0'),
('last_update', NOW()),
('installation_date', NOW()),
('system_status', 'active'),
('maintenance_mode', '0')
ON DUPLICATE KEY UPDATE 
    info_value = VALUES(info_value),
    updated_at = CURRENT_TIMESTAMP;
