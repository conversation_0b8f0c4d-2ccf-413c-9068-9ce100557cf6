<?php
/**
 * Test All Pages for Function Conflicts
 * 
 * This script tests if all pages can be loaded without function conflicts
 */

echo "<h2>🧪 Testing All Pages for Function Conflicts</h2>\n";

// List of main pages to test
$pages_to_test = [
    'dashboard.php',
    'admin-dashboard.php',
    'menu_permissions_advanced.php',
    'transaksi.php',
    'kategori.php',
    'target.php',
    'laporan.php',
    'users.php',
    'supplier.php',
    'inventory.php',
    'return.php'
];

$success_count = 0;
$error_count = 0;
$results = [];

foreach ($pages_to_test as $page) {
    if (!file_exists($page)) {
        $results[] = "⚠️ File not found: $page";
        continue;
    }
    
    echo "<h3>Testing: $page</h3>\n";
    
    // Capture any errors
    ob_start();
    $old_error_reporting = error_reporting(E_ALL);
    
    try {
        // Test just the includes without executing the full page
        $content = file_get_contents($page);
        
        // Extract require_once statements
        preg_match_all("/require_once\s+['\"]([^'\"]+)['\"]/", $content, $matches);
        
        $includes_ok = true;
        $missing_includes = [];
        
        foreach ($matches[1] as $include_file) {
            if (!file_exists($include_file)) {
                $missing_includes[] = $include_file;
                $includes_ok = false;
            }
        }
        
        if ($includes_ok) {
            echo "  ✅ All includes found<br>\n";
            
            // Test for function conflicts by checking if functions are declared multiple times
            $functions_declared = [];
            $conflict_found = false;
            
            foreach ($matches[1] as $include_file) {
                if (file_exists($include_file)) {
                    $include_content = file_get_contents($include_file);
                    
                    // Find function declarations
                    preg_match_all("/function\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\(/", $include_content, $func_matches);
                    
                    foreach ($func_matches[1] as $func_name) {
                        if (isset($functions_declared[$func_name])) {
                            echo "  ❌ Function conflict: $func_name declared in both {$functions_declared[$func_name]} and $include_file<br>\n";
                            $conflict_found = true;
                        } else {
                            $functions_declared[$func_name] = $include_file;
                        }
                    }
                }
            }
            
            if (!$conflict_found) {
                echo "  ✅ No function conflicts detected<br>\n";
                $success_count++;
                $results[] = "✅ $page - OK";
            } else {
                $error_count++;
                $results[] = "❌ $page - Function conflicts detected";
            }
            
        } else {
            echo "  ❌ Missing includes: " . implode(', ', $missing_includes) . "<br>\n";
            $error_count++;
            $results[] = "❌ $page - Missing includes";
        }
        
    } catch (Exception $e) {
        echo "  ❌ Error: " . $e->getMessage() . "<br>\n";
        $error_count++;
        $results[] = "❌ $page - Error: " . $e->getMessage();
    }
    
    error_reporting($old_error_reporting);
    ob_end_clean();
}

echo "<h3>📊 Test Summary</h3>\n";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 10px 0;'>\n";
echo "<strong>Total Pages Tested:</strong> " . count($pages_to_test) . "<br>\n";
echo "<strong>✅ Successful:</strong> $success_count<br>\n";
echo "<strong>❌ Errors:</strong> $error_count<br>\n";
echo "</div>\n";

echo "<h3>📋 Detailed Results</h3>\n";
echo "<ul>\n";
foreach ($results as $result) {
    echo "<li>$result</li>\n";
}
echo "</ul>\n";

if ($error_count > 0) {
    echo "<h3>🔧 Recommended Actions</h3>\n";
    echo "<ol>\n";
    echo "<li>Check the error details above</li>\n";
    echo "<li>Ensure all required files exist</li>\n";
    echo "<li>Remove any duplicate function declarations</li>\n";
    echo "<li>Add function_check.php to files that need it</li>\n";
    echo "<li>Clear PHP OpCache and restart web server</li>\n";
    echo "</ol>\n";
} else {
    echo "<h3>🎉 All Tests Passed!</h3>\n";
    echo "<p style='color: green; font-weight: bold;'>All pages should work without function conflicts!</p>\n";
}

// Test core functions availability
echo "<h3>🔍 Testing Core Functions</h3>\n";

try {
    require_once 'includes/config/database.php';
    require_once 'includes/helpers/function_check.php';
    
    $core_functions = [
        'setFlashMessage',
        'getFlashMessage',
        'formatRupiah',
        'formatTanggal',
        'getCurrentUser',
        'isLoggedIn',
        'validateDate',
        'formatBytes'
    ];
    
    $functions_available = 0;
    foreach ($core_functions as $func) {
        if (function_exists($func)) {
            echo "✅ $func is available<br>\n";
            $functions_available++;
        } else {
            echo "❌ $func is NOT available<br>\n";
        }
    }
    
    echo "<p><strong>Functions Available:</strong> $functions_available / " . count($core_functions) . "</p>\n";
    
} catch (Exception $e) {
    echo "❌ Error testing core functions: " . $e->getMessage() . "<br>\n";
}

?>

<style>
body { 
    font-family: Arial, sans-serif; 
    margin: 20px; 
    background: #f5f5f5;
}
h2 { 
    color: #2c3e50; 
    background: white;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
h3 { 
    color: #3498db; 
    margin-top: 20px;
}
ul, ol { 
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
.success { color: #27ae60; }
.error { color: #e74c3c; }
.warning { color: #f39c12; }
</style>
