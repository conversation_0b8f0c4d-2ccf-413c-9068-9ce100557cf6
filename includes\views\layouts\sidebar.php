<?php
// Dapatkan data user yang sedang login
$currentUser = getCurrentUser();
if (!$currentUser) {
    // Jika session ada tapi user tidak ditemukan, logout
    session_destroy();
    setFlashMessage('danger', 'Sesi tidak valid. Silakan login kembali.');
    redirect('/keuangan/login.php');
}

// Include menu helper and get filtered menus (with user-specific permissions)
$filteredMenus = [];
try {
    require_once 'includes/helpers/menu_helper.php';
    $filteredMenus = getFilteredSidebarMenus($currentUser['role'], $currentUser['id']);

    // Debug: Log what menus are loaded
    error_log("Sidebar: User ID = " . $currentUser['id'] . ", Role = " . $currentUser['role']);
    error_log("Sidebar: Filtered menus count = " . count($filteredMenus));

} catch (Exception $e) {
    error_log("Error loading menu helper: " . $e->getMessage());

    // Fallback menus based on role
    if ($currentUser['role'] === 'admin') {
        $filteredMenus = [
            ['id' => 'dashboard', 'label' => 'Dashboard', 'icon' => 'fas fa-home', 'url' => '/keuangan/dashboard.php'],
            ['id' => 'admin_panel', 'label' => 'Admin Panel', 'icon' => 'fas fa-cogs', 'submenu' => [
                ['id' => 'users', 'label' => 'Kelola User', 'icon' => 'fas fa-users', 'url' => '/keuangan/users.php'],
                ['id' => 'permissions', 'label' => 'Kelola Hak Akses', 'icon' => 'fas fa-key', 'url' => '/keuangan/simple_permissions.php'],
            ]],
            ['id' => 'profile', 'label' => 'Profil', 'icon' => 'fas fa-user', 'url' => '/keuangan/profile.php'],
            ['id' => 'logout', 'label' => 'Keluar', 'icon' => 'fas fa-sign-out-alt', 'url' => '/keuangan/logout.php'],
        ];
    } else {
        $filteredMenus = [
            ['id' => 'dashboard', 'label' => 'Dashboard', 'icon' => 'fas fa-home', 'url' => '/keuangan/dashboard.php'],
            ['id' => 'profile', 'label' => 'Profil', 'icon' => 'fas fa-user', 'url' => '/keuangan/profile.php'],
            ['id' => 'logout', 'label' => 'Keluar', 'icon' => 'fas fa-sign-out-alt', 'url' => '/keuangan/logout.php'],
        ];
    }
}

// Detect current page if not already set
if (!isset($currentPage)) {
    $currentPage = basename($_SERVER['PHP_SELF'], '.php');
    // Handle special cases
    if ($currentPage === 'index') {
        $currentPage = 'dashboard';
    }
}

// Debug: Show what menus will be displayed
if (isset($_GET['debug'])) {
    echo "<!-- DEBUG: Current page: $currentPage, Filtered menus for " . $currentUser['role'] . ": " . json_encode($filteredMenus) . " -->";
}
?>
<div class="modern-sidebar sidebar" id="sidebar">
    <!-- Sidebar Header -->
    <div class="sidebar-header">
        <div class="brand-container">
            <div class="brand-logo">
                <div class="logo-icon">
                    <i class="fas fa-wallet"></i>
                </div>
                <div class="logo-glow"></div>
            </div>
            <div class="brand-content">
                <h3 class="brand-title">KeuanganKu</h3>
                <span class="brand-subtitle">Financial Manager</span>
            </div>
        </div>
    </div>

    <!-- User Profile Section -->
    <div class="sidebar-user">
        <div class="user-profile">
            <div class="user-avatar">
                <img src="https://ui-avatars.com/api/?name=<?= urlencode($currentUser['nama']) ?>&background=2563eb&color=fff&size=48"
                     alt="User Avatar" class="avatar-img">
                <div class="status-indicator online"></div>
            </div>
            <div class="user-details">
                <h6 class="user-name"><?= htmlspecialchars($currentUser['nama']) ?></h6>
                <span class="user-role"><?= ucfirst($currentUser['role']) ?></span>
                <div class="user-status">
                    <i class="fas fa-circle text-success me-1"></i>
                    <small>Online</small>
                </div>
            </div>
        </div>
    </div>
    <!-- Navigation Menu -->
    <div class="sidebar-navigation">
        <nav class="nav-menu">
            <?php foreach ($filteredMenus as $menu): ?>
                <?php if (isset($menu['submenu']) && !empty($menu['submenu'])): ?>
                    <!-- Menu with submenu -->
                    <div class="menu-group">
                        <button class="menu-item menu-toggle has-submenu <?= in_array($currentPage, array_column($menu['submenu'], 'id')) ? 'active' : '' ?>"
                                data-bs-toggle="collapse"
                                data-bs-target="#<?= $menu['id'] ?>Submenu"
                                aria-expanded="<?= in_array($currentPage, array_column($menu['submenu'], 'id')) ? 'true' : 'false' ?>"
                                aria-controls="<?= $menu['id'] ?>Submenu"
                                data-tooltip="<?= htmlspecialchars($menu['label']) ?>"
                                data-submenu="<?= $menu['id'] ?>">
                            <div class="menu-icon">
                                <i class="<?= $menu['icon'] ?>"></i>
                            </div>
                            <span class="menu-text"><?= htmlspecialchars($menu['label']) ?></span>
                            <i class="fas fa-chevron-down menu-arrow"></i>
                        </button>
                        <div class="collapse submenu <?= in_array($currentPage, array_column($menu['submenu'], 'id')) ? 'show' : '' ?>"
                             id="<?= $menu['id'] ?>Submenu">
                            <div class="submenu-container">
                                <?php foreach ($menu['submenu'] as $submenu): ?>
                                    <a class="submenu-item <?= $currentPage === str_replace('.php', '', basename($submenu['url'])) ? 'active' : '' ?>"
                                       href="<?= $submenu['url'] ?>">
                                        <div class="submenu-icon">
                                            <i class="<?= $submenu['icon'] ?>"></i>
                                        </div>
                                        <span class="submenu-text"><?= htmlspecialchars($submenu['label']) ?></span>
                                        <div class="submenu-indicator"></div>
                                    </a>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                <?php else: ?>
                    <!-- Single menu item -->
                    <a class="menu-item <?= $currentPage === str_replace('.php', '', basename($menu['url'])) ? 'active' : '' ?>"
                       href="<?= $menu['url'] ?>">
                        <div class="menu-icon">
                            <i class="<?= $menu['icon'] ?>"></i>
                        </div>
                        <span class="menu-text"><?= htmlspecialchars($menu['label']) ?></span>
                        <div class="menu-indicator"></div>
                    </a>
                <?php endif; ?>
            <?php endforeach; ?>
        </nav>
    </div>

    <!-- Modern Compact Sidebar Footer -->
    <div class="sidebar-footer">
        <div class="sidebar-footer-content">
            <button class="theme-toggle-btn" onclick="toggleTheme()" data-theme-toggle
                    data-bs-toggle="tooltip" title="Toggle Dark Mode">
                <i class="fas fa-moon"></i>
                <span class="theme-toggle-text">Dark Mode</span>
            </button>
        </div>
    </div>
</div>

<!-- Active Menu Detection Script -->
<script>
// Pass current page info to JavaScript
window.currentPageInfo = {
    page: '<?= $currentPage ?>',
    phpActiveSet: <?= json_encode(isset($currentPage)) ?>
};

// Ensure active menu is preserved after sidebar initialization
document.addEventListener('DOMContentLoaded', function() {
    // Wait for modern sidebar to initialize
    setTimeout(function() {
        if (window.modernSidebar) {
            // Only refresh if no active menu is already set by PHP
            const existingActive = document.querySelector('.menu-item.active, .submenu-item.active');
            if (!existingActive) {
                window.modernSidebar.refreshActiveMenu();
                console.log('🔄 No active menu found, refreshing for current page');
            } else {
                // Just ensure submenu is open if needed
                window.modernSidebar.ensureSubmenuOpen(existingActive);
                console.log('✅ Active menu preserved from PHP:', existingActive.textContent.trim());
            }
        }
    }, 150);
});

// Handle browser navigation (back/forward)
window.addEventListener('popstate', function() {
    if (window.modernSidebar) {
        setTimeout(function() {
            window.modernSidebar.refreshActiveMenu();
        }, 50);
    }
});
</script>

<!-- All CSS and JavaScript moved to external files for better performance and no conflicts -->