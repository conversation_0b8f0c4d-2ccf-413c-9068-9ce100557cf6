<?php
require_once 'includes/config/database.php';
require_once 'includes/helpers/functions.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Log the logout activity if user was logged in
if (isset($_SESSION['user_id'])) {
    try {
        executeQuery("
            INSERT INTO aktivitas (user_id, aktivitas) 
            VALUES (?, 'User logged out')
        ", [$_SESSION['user_id']]);
    } catch (Exception $e) {
        error_log("Logout Activity Log Error: " . $e->getMessage());
    }
}

// Destroy all session data
session_unset();
session_destroy();

// Set flash message
setFlashMessage('success', 'Anda telah berhasil logout');

// Redirect to login page using relative path
header('Location: login.php');
exit; 