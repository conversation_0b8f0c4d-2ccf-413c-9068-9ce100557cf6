<!DOCTYPE html>
<html lang="id" data-bs-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Dropdown Test - KeuanganKu</title>

    <!-- CSS Libraries -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="assets/css/layout-manager.css?v=<?= time() ?>" rel="stylesheet">
    <link href="assets/css/control-sidebar.css?v=<?= time() ?>" rel="stylesheet">
</head>
<body>
    <!-- Simple Test Sidebar -->
    <aside class="modern-sidebar">
        <!-- Sidebar Header -->
        <div class="sidebar-header">
            <div class="brand-container">
                <div class="brand-logo">
                    <div class="logo-icon">
                        <i class="fas fa-test-tube"></i>
                    </div>
                </div>
                <div class="brand-content">
                    <h1 class="brand-title">Test</h1>
                    <p class="brand-subtitle">Dropdown</p>
                </div>
            </div>
        </div>

        <!-- User Profile Section -->
        <div class="sidebar-user">
            <div class="user-profile">
                <div class="user-avatar">
                    <img src="https://ui-avatars.com/api/?name=Test&background=28a745&color=fff&size=40" alt="Test" class="avatar-img">
                    <div class="status-indicator"></div>
                </div>
                <div class="user-details">
                    <h6 class="user-name">Test User</h6>
                    <span class="user-role">Tester</span>
                </div>
            </div>
        </div>

        <!-- Navigation Menu -->
        <div class="sidebar-navigation">
            <nav class="nav-menu">
                <!-- Test Menu with Submenu -->
                <div class="menu-section">
                    <div class="menu-section-title">Test Section</div>
                    
                    <button class="menu-item menu-toggle has-submenu" 
                            data-bs-toggle="collapse" 
                            data-bs-target="#testSubmenu" 
                            aria-expanded="false" 
                            data-tooltip="Test Menu"
                            data-submenu="test">
                        <div class="menu-icon">
                            <i class="fas fa-flask"></i>
                        </div>
                        <span class="menu-text">Test Menu</span>
                        <i class="fas fa-chevron-down menu-arrow"></i>
                    </button>
                    <div class="collapse submenu" id="testSubmenu">
                        <div class="submenu-container">
                            <a class="submenu-item" href="#test1">
                                <div class="submenu-icon">
                                    <i class="fas fa-vial"></i>
                                </div>
                                <span class="submenu-text">Test Item 1</span>
                            </a>
                            <a class="submenu-item" href="#test2">
                                <div class="submenu-icon">
                                    <i class="fas fa-microscope"></i>
                                </div>
                                <span class="submenu-text">Test Item 2</span>
                            </a>
                            <a class="submenu-item" href="#test3">
                                <div class="submenu-icon">
                                    <i class="fas fa-atom"></i>
                                </div>
                                <span class="submenu-text">Test Item 3</span>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Another Test Menu -->
                <div class="menu-section">
                    <div class="menu-section-title">Another Section</div>
                    
                    <button class="menu-item menu-toggle has-submenu" 
                            data-bs-toggle="collapse" 
                            data-bs-target="#anotherSubmenu" 
                            aria-expanded="false" 
                            data-tooltip="Another Menu"
                            data-submenu="another">
                        <div class="menu-icon">
                            <i class="fas fa-cog"></i>
                        </div>
                        <span class="menu-text">Another Menu</span>
                        <i class="fas fa-chevron-down menu-arrow"></i>
                    </button>
                    <div class="collapse submenu" id="anotherSubmenu">
                        <div class="submenu-container">
                            <a class="submenu-item" href="#another1">
                                <div class="submenu-icon">
                                    <i class="fas fa-wrench"></i>
                                </div>
                                <span class="submenu-text">Another Item 1</span>
                            </a>
                            <a class="submenu-item" href="#another2">
                                <div class="submenu-icon">
                                    <i class="fas fa-screwdriver"></i>
                                </div>
                                <span class="submenu-text">Another Item 2</span>
                            </a>
                        </div>
                    </div>
                </div>
            </nav>
        </div>

        <!-- Sidebar Footer -->
        <div class="sidebar-footer">
            <div class="sidebar-footer-content">
                <div style="display: flex; gap: 0.5rem;">
                    <button class="theme-toggle-btn" onclick="toggleTheme()" style="flex: 1;">
                        <i class="fas fa-moon"></i>
                        <span class="theme-toggle-text">Dark</span>
                    </button>
                    <button class="sidebar-toggle-btn" onclick="window.modernSidebar?.toggle()">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                </div>
            </div>
        </div>
    </aside>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Navbar -->
        <nav class="navbar navbar-expand-lg modern-navbar">
            <div class="container-fluid">
                <div class="navbar-brand">
                    <i class="fas fa-bug text-success me-2"></i>
                    <span class="text-success fw-bold">Simple Dropdown Test</span>
                </div>
            </div>
        </nav>

        <!-- Content -->
        <div class="content-container">
            <div class="container-fluid">
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header bg-success text-white">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-play me-2"></i>Simple Test Instructions
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-info">
                                    <h6><i class="fas fa-info-circle me-2"></i>Test Steps:</h6>
                                    <ol class="mb-0">
                                        <li><strong>Step 1:</strong> Click the toggle button (arrow) in sidebar footer to collapse sidebar</li>
                                        <li><strong>Step 2:</strong> Click on "Test Menu" or "Another Menu" when sidebar is collapsed</li>
                                        <li><strong>Step 3:</strong> Check if dropdown appears outside the sidebar</li>
                                        <li><strong>Step 4:</strong> Open browser console (F12) to see debug messages</li>
                                    </ol>
                                </div>

                                <div class="row mt-4">
                                    <div class="col-md-6">
                                        <h6>Quick Actions:</h6>
                                        <div class="d-grid gap-2">
                                            <button class="btn btn-primary" onclick="testCollapse()">
                                                <i class="fas fa-compress me-1"></i>Collapse Sidebar
                                            </button>
                                            <button class="btn btn-success" onclick="testExpand()">
                                                <i class="fas fa-expand me-1"></i>Expand Sidebar
                                            </button>
                                            <button class="btn btn-warning" onclick="testClick()">
                                                <i class="fas fa-mouse-pointer me-1"></i>Test Click Menu
                                            </button>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <h6>Current Status:</h6>
                                        <div id="status">
                                            <div>Sidebar: <span id="sidebarStatus" class="badge bg-info">Loading...</span></div>
                                            <div>Menu Items: <span id="menuItems" class="badge bg-secondary">Loading...</span></div>
                                            <div>Dropdowns: <span id="dropdowns" class="badge bg-warning">Loading...</span></div>
                                            <div>Mobile: <span id="mobile" class="badge bg-dark">Loading...</span></div>
                                        </div>
                                    </div>
                                </div>

                                <div class="mt-4">
                                    <h6>Console Output:</h6>
                                    <div id="console" style="background: #1a1a1a; color: #00ff00; padding: 1rem; border-radius: 4px; height: 200px; overflow-y: auto; font-family: 'Courier New', monospace; font-size: 0.875rem;">
                                        Waiting for console output...
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/modern-sidebar.js?v=<?= time() ?>"></script>
    <script src="assets/js/theme-manager.js?v=<?= time() ?>"></script>

    <script>
        // Console capture
        function addToConsole(message) {
            const console = document.getElementById('console');
            const time = new Date().toLocaleTimeString();
            console.innerHTML += `<div>[${time}] ${message}</div>`;
            console.scrollTop = console.scrollHeight;
        }

        // Override console.log
        const originalLog = console.log;
        console.log = function(...args) {
            addToConsole(args.join(' '));
            originalLog.apply(console, args);
        };

        // Test functions
        function testCollapse() {
            addToConsole('🔧 Testing collapse...');
            if (window.modernSidebar) {
                window.modernSidebar.collapse();
            } else {
                addToConsole('❌ modernSidebar not found!');
            }
            updateStatus();
        }

        function testExpand() {
            addToConsole('🔧 Testing expand...');
            if (window.modernSidebar) {
                window.modernSidebar.expand();
            } else {
                addToConsole('❌ modernSidebar not found!');
            }
            updateStatus();
        }

        function testClick() {
            addToConsole('🔧 Testing menu click...');
            const menu = document.querySelector('.menu-item.has-submenu');
            if (menu) {
                addToConsole('🖱️ Clicking menu: ' + menu.querySelector('.menu-text').textContent);
                menu.click();
            } else {
                addToConsole('❌ No submenu found!');
            }
        }

        function updateStatus() {
            const isCollapsed = document.body.classList.contains('sidebar-collapsed');
            const menuItems = document.querySelectorAll('.menu-item.has-submenu');
            const dropdowns = document.querySelectorAll('.collapsed-dropdown');
            const isMobile = window.innerWidth <= 1024;

            document.getElementById('sidebarStatus').textContent = isCollapsed ? 'Collapsed' : 'Expanded';
            document.getElementById('sidebarStatus').className = isCollapsed ? 'badge bg-warning' : 'badge bg-success';
            
            document.getElementById('menuItems').textContent = menuItems.length;
            document.getElementById('dropdowns').textContent = dropdowns.length;
            document.getElementById('mobile').textContent = isMobile ? 'Yes' : 'No';
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            addToConsole('🚀 Page loaded');
            setTimeout(() => {
                updateStatus();
                addToConsole('📊 Status updated');
                
                // Check if modernSidebar exists
                if (window.modernSidebar) {
                    addToConsole('✅ modernSidebar found');
                } else {
                    addToConsole('❌ modernSidebar NOT found');
                }
            }, 1000);
        });

        // Monitor changes
        const observer = new MutationObserver(function() {
            updateStatus();
        });
        observer.observe(document.body, { 
            attributes: true, 
            attributeFilter: ['class'] 
        });
    </script>
</body>
</html>
