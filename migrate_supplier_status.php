<?php
require_once 'includes/config/database.php';

try {
    // Check if status column exists in supplier table
    $stmt = $pdo->query("SHOW COLUMNS FROM supplier LIKE 'status'");
    
    if ($stmt->rowCount() == 0) {
        echo "Adding status column to supplier table...\n";
        
        // Add status column
        $pdo->exec("ALTER TABLE supplier ADD COLUMN status ENUM('aktif', 'nonaktif') DEFAULT 'aktif' AFTER keterangan");
        
        // Update all existing suppliers to have 'aktif' status
        $pdo->exec("UPDATE supplier SET status = 'aktif' WHERE status IS NULL");
        
        echo "Status column added successfully!\n";
    } else {
        echo "Status column already exists in supplier table.\n";
    }
    
    // Check if inventory table exists and has required columns
    $stmt = $pdo->query("SHOW TABLES LIKE 'inventory'");
    if ($stmt->rowCount() == 0) {
        echo "Creating inventory table...\n";
        
        $pdo->exec("CREATE TABLE IF NOT EXISTS inventory (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            kode_barang VARCHAR(50) UNIQUE NOT NULL,
            nama_barang VARCHAR(255) NOT NULL,
            kategori_barang VARCHAR(100),
            satuan VARCHAR(50) DEFAULT 'pcs',
            stok_minimum INT DEFAULT 0,
            stok_saat_ini INT DEFAULT 0,
            harga_beli DECIMAL(15,2) DEFAULT 0,
            harga_jual DECIMAL(15,2) DEFAULT 0,
            lokasi_penyimpanan VARCHAR(255),
            supplier_id INT,
            tanggal_masuk DATE,
            tanggal_kadaluarsa DATE,
            status ENUM('aktif', 'nonaktif', 'habis') DEFAULT 'aktif',
            keterangan TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            FOREIGN KEY (supplier_id) REFERENCES supplier(id) ON DELETE SET NULL
        )");
        
        echo "Inventory table created successfully!\n";
    }
    
    // Check if stock_movements table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'stock_movements'");
    if ($stmt->rowCount() == 0) {
        echo "Creating stock_movements table...\n";
        
        $pdo->exec("CREATE TABLE IF NOT EXISTS stock_movements (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            inventory_id INT NOT NULL,
            jenis_gerakan ENUM('masuk', 'keluar', 'adjustment') NOT NULL,
            jumlah INT NOT NULL,
            stok_sebelum INT NOT NULL,
            stok_sesudah INT NOT NULL,
            keterangan TEXT,
            referensi VARCHAR(100),
            tanggal_gerakan DATETIME DEFAULT CURRENT_TIMESTAMP,
            created_by INT,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            FOREIGN KEY (inventory_id) REFERENCES inventory(id) ON DELETE CASCADE,
            FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
        )");
        
        echo "Stock movements table created successfully!\n";
    }
    
    // Check if returns table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'returns'");
    if ($stmt->rowCount() == 0) {
        echo "Creating returns table...\n";
        
        $pdo->exec("CREATE TABLE IF NOT EXISTS returns (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            nomor_return VARCHAR(50) UNIQUE NOT NULL,
            jenis_return ENUM('penjualan', 'pembelian') NOT NULL,
            referensi_id INT,
            referensi_nomor VARCHAR(50),
            tanggal_return DATE NOT NULL,
            total_return DECIMAL(15,2) NOT NULL DEFAULT 0,
            alasan_return TEXT,
            status ENUM('pending', 'disetujui', 'ditolak', 'selesai') DEFAULT 'pending',
            catatan TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        )");
        
        $pdo->exec("CREATE TABLE IF NOT EXISTS return_items (
            id INT AUTO_INCREMENT PRIMARY KEY,
            return_id INT NOT NULL,
            produk_id INT,
            nama_produk VARCHAR(255) NOT NULL,
            qty_return INT NOT NULL,
            harga_satuan DECIMAL(15,2) NOT NULL,
            subtotal DECIMAL(15,2) NOT NULL,
            kondisi_barang ENUM('baik', 'rusak', 'cacat') DEFAULT 'baik',
            keterangan TEXT,
            FOREIGN KEY (return_id) REFERENCES returns(id) ON DELETE CASCADE
        )");
        
        echo "Returns tables created successfully!\n";
    }
    
    // Check if pengingat table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'pengingat'");
    if ($stmt->rowCount() == 0) {
        echo "Creating pengingat table...\n";
        
        $pdo->exec("CREATE TABLE IF NOT EXISTS pengingat (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            judul VARCHAR(255) NOT NULL,
            deskripsi TEXT,
            tanggal_pengingat DATE NOT NULL,
            waktu_pengingat TIME,
            jenis ENUM('tagihan', 'cicilan', 'target', 'investasi', 'lainnya') NOT NULL,
            jumlah DECIMAL(15,2),
            status ENUM('aktif', 'selesai', 'terlewat') DEFAULT 'aktif',
            pengulangan ENUM('sekali', 'harian', 'mingguan', 'bulanan', 'tahunan') DEFAULT 'sekali',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        )");
        
        echo "Pengingat table created successfully!\n";
    }
    
    echo "\nAll database migrations completed successfully!\n";
    echo "You can now access all the new features.\n";
    
} catch (PDOException $e) {
    echo "Error during migration: " . $e->getMessage() . "\n";
    exit(1);
}
?>
