-- Create database_versions table
CREATE TABLE IF NOT EXISTS `database_versions` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `version` varchar(20) NOT NULL,
    `description` text,
    `sql_file` varchar(255) DEFAULT NULL,
    `executed_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `executed_by` varchar(100) DEFAULT NULL,
    `status` enum('success','failed','pending') DEFAULT 'success',
    `error_message` text,
    PRIMARY KEY (`id`),
    UNIQUE KEY `version` (`version`),
    KEY `idx_status` (`status`),
    KEY `idx_executed_at` (`executed_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert initial version records
INSERT INTO `database_versions` (`version`, `description`, `sql_file`, `executed_by`, `status`) VALUES
('1.0.0', 'Initial database setup', 'initial_setup.sql', 'system', 'success'),
('1.1.0', 'Added user management tables', 'user_management.sql', 'system', 'success'),
('1.2.0', 'Added financial management tables', 'financial_tables.sql', 'system', 'success'),
('1.3.0', 'Added business management tables', 'business_tables.sql', 'system', 'success'),
('1.4.0', 'Added notification system', 'notification_system.sql', 'system', 'success'),
('1.5.0', 'Added system settings and customization', 'system_settings.sql', 'system', 'success'),
('1.6.0', 'Added customization features', 'customization_features.sql', 'system', 'success')
ON DUPLICATE KEY UPDATE description = VALUES(description);

-- Create database_migrations table for tracking migrations
CREATE TABLE IF NOT EXISTS `database_migrations` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `migration_name` varchar(255) NOT NULL,
    `batch` int(11) NOT NULL,
    `executed_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `migration_name` (`migration_name`),
    KEY `idx_batch` (`batch`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert migration records
INSERT INTO `database_migrations` (`migration_name`, `batch`) VALUES
('2024_01_01_000001_create_users_table', 1),
('2024_01_01_000002_create_roles_table', 1),
('2024_01_01_000003_create_permissions_table', 1),
('2024_01_01_000004_create_financial_tables', 2),
('2024_01_01_000005_create_business_tables', 3),
('2024_01_01_000006_create_notification_tables', 4),
('2024_01_01_000007_create_system_settings', 5),
('2024_01_01_000008_create_customization_tables', 6)
ON DUPLICATE KEY UPDATE batch = VALUES(batch);

-- Create system_info table for storing system information
CREATE TABLE IF NOT EXISTS `system_info` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `info_key` varchar(100) NOT NULL,
    `info_value` text,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `info_key` (`info_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert system information
INSERT INTO `system_info` (`info_key`, `info_value`) VALUES
('system_version', '1.6.0'),
('database_version', '1.6.0'),
('last_update', NOW()),
('installation_date', NOW()),
('system_status', 'active'),
('maintenance_mode', '0')
ON DUPLICATE KEY UPDATE 
    info_value = VALUES(info_value),
    updated_at = CURRENT_TIMESTAMP;
