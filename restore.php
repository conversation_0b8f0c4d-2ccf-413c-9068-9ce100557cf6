<?php
// Fungsi untuk membuat direktori jika belum ada
function createDirectory($path) {
    if (!file_exists($path)) {
        mkdir($path, 0777, true);
    }
}

// Buat direktori yang diperlukan
$directories = [
    'assets',
    'assets/css',
    'assets/img',
    'includes',
    'includes/views',
    'includes/views/layouts'
];

foreach ($directories as $dir) {
    createDirectory($dir);
}

// Buat file style.css
$style_css = <<<CSS
/* Custom styles */
body {
    font-family: 'Nunito', sans-serif;
    background-color: #f8f9fa;
}

.wrapper {
    display: flex;
    width: 100%;
    align-items: stretch;
}

#sidebar {
    min-width: 250px;
    max-width: 250px;
    background: #343a40;
    color: #fff;
    transition: all 0.3s;
    height: 100vh;
    position: fixed;
    z-index: 1000;
}

#sidebar.collapsed {
    min-width: 70px;
    max-width: 70px;
}

#sidebar .nav-link {
    color: rgba(255, 255, 255, 0.8);
    padding: 0.8rem 1rem;
    display: flex;
    align-items: center;
}

#sidebar .nav-link:hover {
    color: #fff;
    background: rgba(255, 255, 255, 0.1);
}

#sidebar .nav-link i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
}

#sidebar .nav-link.active {
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
}

.main-content {
    width: 100%;
    min-height: 100vh;
    transition: all 0.3s;
    margin-left: 250px;
}

.main-content.expanded {
    margin-left: 70px;
}

.navbar {
    padding: 15px 10px;
    background: #fff;
    border: none;
    border-radius: 0;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
}

.card {
    border: none;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid rgba(0,0,0,0.1);
}

.btn-primary {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.btn-primary:hover {
    background-color: #0b5ed7;
    border-color: #0a58ca;
}

/* Responsive */
@media (max-width: 768px) {
    #sidebar {
        min-width: 70px;
        max-width: 70px;
    }
    
    #sidebar .nav-link span {
        display: none;
    }
    
    #sidebar .nav-link i {
        margin-right: 0;
    }
    
    .main-content {
        margin-left: 70px;
    }
    
    #sidebar.expanded {
        min-width: 250px;
        max-width: 250px;
    }
    
    #sidebar.expanded .nav-link span {
        display: inline;
    }
    
    #sidebar.expanded .nav-link i {
        margin-right: 10px;
    }
}
CSS;

file_put_contents('assets/css/style.css', $style_css);

// Buat file site.webmanifest
$manifest = <<<JSON
{
    "name": "Sistem Keuangan",
    "short_name": "Keuangan",
    "icons": [
        {
            "src": "/assets/img/android-chrome-192x192.png",
            "sizes": "192x192",
            "type": "image/png"
        },
        {
            "src": "/assets/img/android-chrome-512x512.png",
            "sizes": "512x512",
            "type": "image/png"
        }
    ],
    "theme_color": "#ffffff",
    "background_color": "#ffffff",
    "display": "standalone"
}
JSON;

file_put_contents('assets/img/site.webmanifest', $manifest);

// Buat file sidebar.php
$sidebar = <<<PHP
<div id="sidebar">
    <div class="sidebar-header p-3">
        <h3 class="text-center mb-0">Keuangan</h3>
    </div>
    
    <ul class="nav flex-column">
        <li class="nav-item">
            <a class="nav-link <?= $currentPage === 'dashboard' ? 'active' : '' ?>" href="/keuangan/index.php">
                <i class="fas fa-home"></i>
                <span>Dashboard</span>
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link <?= $currentPage === 'transaksi' ? 'active' : '' ?>" href="transaksi.php">
                <i class="fas fa-exchange-alt"></i>
                <span>Transaksi</span>
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link <?= $currentPage === 'kategori' ? 'active' : '' ?>" href="kategori.php">
                <i class="fas fa-tags"></i>
                <span>Kategori</span>
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link <?= $currentPage === 'target' ? 'active' : '' ?>" href="target.php">
                <i class="fas fa-bullseye"></i>
                <span>Target</span>
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link <?= $currentPage === 'laporan' ? 'active' : '' ?>" href="laporan.php">
                <i class="fas fa-chart-bar"></i>
                <span>Laporan</span>
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link <?= $currentPage === 'pengaturan' ? 'active' : '' ?>" href="pengaturan.php">
                <i class="fas fa-cog"></i>
                <span>Pengaturan</span>
            </a>
        </li>
    </ul>
</div>
PHP;

file_put_contents('includes/views/layouts/sidebar.php', $sidebar);

// Buat file navbar.php
$navbar = <<<PHP
<nav class="navbar navbar-expand-lg">
    <div class="container-fluid">
        <button id="sidebarToggle" class="btn btn-link">
            <i class="fas fa-bars"></i>
        </button>
        
        <ul class="navbar-nav ms-auto">
            <li class="nav-item dropdown">
                <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                    <i class="fas fa-user-circle"></i>
                    <?= htmlspecialchars($currentUser['nama'] ?? 'User') ?>
                </a>
                <ul class="dropdown-menu dropdown-menu-end">
                    <li><a class="dropdown-item" href="profil.php"><i class="fas fa-user me-2"></i>Profil</a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="logout.php"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                </ul>
            </li>
        </ul>
    </div>
</nav>
PHP;

file_put_contents('includes/views/layouts/navbar.php', $navbar);

echo "Restore completed successfully!";
?> 