<?php
// Konfigurasi Database
$db_host = 'localhost';
$db_user = 'root';
$db_pass = '';

try {
    // Buat koneksi ke MySQL tanpa memilih database
    $pdo = new PDO("mysql:host=$db_host", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Cek apakah database sudah ada
    $stmt = $pdo->query("SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = 'keuangan'");
    if ($stmt->rowCount() == 0) {
        // Buat database jika belum ada
        $pdo->exec("CREATE DATABASE keuangan CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        echo "Database 'keuangan' berhasil dibuat.<br>";
        
        // Pilih database
        $pdo->exec("USE keuangan");
        
        // Import struktur database
        $sql = file_get_contents(__DIR__ . '/../../database.sql');
        $pdo->exec($sql);
        echo "Struktur database berhasil diimpor.<br>";
    } else {
        echo "Database 'keuangan' sudah ada.<br>";
    }
    
    echo "Instalasi database selesai.";
} catch(PDOException $e) {
    die("Error: " . $e->getMessage());
}
?> 