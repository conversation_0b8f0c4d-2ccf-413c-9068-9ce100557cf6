<?php
require_once 'includes/config/database.php';
require_once 'includes/helpers/functions.php';

// Jika user sudah login, redirect ke dashboard
if (isLoggedIn()) {
    redirect('index.php');
}

$errors = [];
$success = false;

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = cleanInput($_POST['email'] ?? '');
    
    // Validasi email
    if (empty($email)) {
        $errors['email'] = 'Email harus diisi';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors['email'] = 'Format email tidak valid';
    } else {
        try {
            // Cek apakah email terdaftar
            $stmt = $pdo->prepare("SELECT id, nama FROM users WHERE email = ?");
            $stmt->execute([$email]);
            $user = $stmt->fetch();
            
            if ($user) {
                // Generate token reset password
                $token = bin2hex(random_bytes(32));
                $expires = date('Y-m-d H:i:s', strtotime('+1 hour'));
                
                // Simpan token ke database
                $stmt = $pdo->prepare("
                    INSERT INTO password_resets (user_id, token, expires_at) 
                    VALUES (?, ?, ?)
                ");
                
                if ($stmt->execute([$user['id'], $token, $expires])) {
                    // Kirim email reset password
                    $reset_link = "http://keuangan.test/forgot-password.php?token=" . $token;
                    $to = $email;
                    $subject = "Reset Password - Sistem Keuangan";
                    $message = "
                    <html>
                    <head>
                        <title>Reset Password</title>
                    </head>
                    <body>
                        <h2>Reset Password</h2>
                        <p>Halo {$user['nama']},</p>
                        <p>Anda telah meminta untuk mereset password Anda. Klik link di bawah ini untuk melanjutkan:</p>
                        <p><a href='{$reset_link}'>{$reset_link}</a></p>
                        <p>Link ini akan kadaluarsa dalam 1 jam.</p>
                        <p>Jika Anda tidak meminta reset password, abaikan email ini.</p>
                    </body>
                    </html>
                    ";
                    
                    $headers = "MIME-Version: 1.0" . "\r\n";
                    $headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";
                    $headers .= 'From: Sistem Keuangan <<EMAIL>>' . "\r\n";
                    
                    if (mail($to, $subject, $message, $headers)) {
                        $success = true;
                        setFlashMessage('success', 'Link reset password telah dikirim ke email Anda.');
                    } else {
                        $errors['general'] = 'Gagal mengirim email. Silakan coba lagi.';
                    }
                } else {
                    $errors['general'] = 'Gagal memproses permintaan. Silakan coba lagi.';
                }
            } else {
                $errors['email'] = 'Email tidak terdaftar';
            }
        } catch (PDOException $e) {
            error_log("Reset Password Error: " . $e->getMessage());
            $errors['general'] = 'Terjadi kesalahan. Silakan coba lagi.';
        }
    }
}
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Password - Sistem Keuangan</title>
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container">
        <div class="row justify-content-center mt-5">
            <div class="col-md-6">
                <div class="card shadow">
                    <div class="card-body p-5">
                        <h2 class="text-center mb-4">Reset Password</h2>
                        
                        <?php if ($success): ?>
                        <div class="alert alert-success">
                            Link reset password telah dikirim ke email Anda. Silakan cek email Anda untuk melanjutkan.
                        </div>
                        <div class="text-center mt-3">
                            <a href="login.php" class="btn btn-primary">Kembali ke Login</a>
                        </div>
                        <?php else: ?>
                        
                        <?php if (isset($errors['general'])): ?>
                        <div class="alert alert-danger"><?= $errors['general'] ?></div>
                        <?php endif; ?>
                        
                        <form method="POST" action="" novalidate>
                            <div class="mb-4">
                                <label for="email" class="form-label">Email</label>
                                <input type="email" class="form-control <?= isset($errors['email']) ? 'is-invalid' : '' ?>" 
                                       id="email" name="email" value="<?= htmlspecialchars($email ?? '') ?>" required>
                                <?php if (isset($errors['email'])): ?>
                                <div class="invalid-feedback"><?= $errors['email'] ?></div>
                                <?php endif; ?>
                                <div class="form-text">
                                    Masukkan email yang terdaftar untuk menerima link reset password.
                                </div>
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">Kirim Link Reset</button>
                            </div>
                            
                            <div class="text-center mt-3">
                                <p>Ingat password Anda? <a href="login.php">Login di sini</a></p>
                            </div>
                        </form>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 