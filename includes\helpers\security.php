<?php
/**
 * Security Helper Functions
 * 
 * This file contains security-related functions for the application.
 */

/**
 * Generate secure random token
 * @param int $length Token length
 * @return string
 */
function generateSecureToken($length = 32) {
    return bin2hex(random_bytes($length));
}

/**
 * Hash password securely
 * @param string $password Plain text password
 * @return string
 */
function hashPassword($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

/**
 * Verify password
 * @param string $password Plain text password
 * @param string $hash Hashed password
 * @return bool
 */
function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

/**
 * Sanitize input data
 * @param mixed $data Input data
 * @return mixed
 */
function sanitizeInput($data) {
    if (is_array($data)) {
        return array_map('sanitizeInput', $data);
    }
    return htmlspecialchars(trim($data), ENT_QUOTES, 'UTF-8');
}

/**
 * Validate email format
 * @param string $email Email address
 * @return bool
 */
function isValidEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * Check password strength
 * @param string $password Password to check
 * @return array
 */
function checkPasswordStrength($password) {
    $score = 0;
    $feedback = [];
    
    // Length check
    if (strlen($password) >= 8) {
        $score += 1;
    } else {
        $feedback[] = 'Password harus minimal 8 karakter';
    }
    
    // Uppercase check
    if (preg_match('/[A-Z]/', $password)) {
        $score += 1;
    } else {
        $feedback[] = 'Password harus mengandung huruf besar';
    }
    
    // Lowercase check
    if (preg_match('/[a-z]/', $password)) {
        $score += 1;
    } else {
        $feedback[] = 'Password harus mengandung huruf kecil';
    }
    
    // Number check
    if (preg_match('/[0-9]/', $password)) {
        $score += 1;
    } else {
        $feedback[] = 'Password harus mengandung angka';
    }
    
    // Special character check
    if (preg_match('/[^A-Za-z0-9]/', $password)) {
        $score += 1;
    } else {
        $feedback[] = 'Password harus mengandung karakter khusus';
    }
    
    $strength = 'Sangat Lemah';
    if ($score >= 4) $strength = 'Kuat';
    elseif ($score >= 3) $strength = 'Sedang';
    elseif ($score >= 2) $strength = 'Lemah';
    
    return [
        'score' => $score,
        'strength' => $strength,
        'feedback' => $feedback
    ];
}

/**
 * Rate limiting check
 * @param string $identifier Unique identifier (IP, user ID, etc.)
 * @param int $maxAttempts Maximum attempts allowed
 * @param int $timeWindow Time window in seconds
 * @return bool
 */
function checkRateLimit($identifier, $maxAttempts = 5, $timeWindow = 300) {
    global $pdo;
    
    try {
        // Create rate_limits table if not exists
        $pdo->exec("CREATE TABLE IF NOT EXISTS rate_limits (
            id INT AUTO_INCREMENT PRIMARY KEY,
            identifier VARCHAR(255) NOT NULL,
            attempts INT DEFAULT 1,
            first_attempt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_attempt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY unique_identifier (identifier)
        )");
        
        // Clean old entries
        $pdo->prepare("DELETE FROM rate_limits WHERE last_attempt < DATE_SUB(NOW(), INTERVAL ? SECOND)")
            ->execute([$timeWindow]);
        
        // Check current attempts
        $stmt = $pdo->prepare("SELECT attempts, first_attempt FROM rate_limits WHERE identifier = ?");
        $stmt->execute([$identifier]);
        $result = $stmt->fetch();
        
        if (!$result) {
            // First attempt
            $pdo->prepare("INSERT INTO rate_limits (identifier) VALUES (?)")
                ->execute([$identifier]);
            return true;
        }
        
        if ($result['attempts'] >= $maxAttempts) {
            return false; // Rate limit exceeded
        }
        
        // Increment attempts
        $pdo->prepare("UPDATE rate_limits SET attempts = attempts + 1 WHERE identifier = ?")
            ->execute([$identifier]);
        
        return true;
        
    } catch (PDOException $e) {
        error_log("Rate limit check error: " . $e->getMessage());
        return true; // Allow on error
    }
}

/**
 * Clear rate limit for identifier
 * @param string $identifier Identifier to clear
 * @return bool
 */
function clearRateLimit($identifier) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("DELETE FROM rate_limits WHERE identifier = ?");
        return $stmt->execute([$identifier]);
    } catch (PDOException $e) {
        error_log("Clear rate limit error: " . $e->getMessage());
        return false;
    }
}

/**
 * Log security event
 * @param string $event Event description
 * @param string $severity Severity level (low, medium, high, critical)
 * @param array $context Additional context
 * @return bool
 */
function logSecurityEvent($event, $severity = 'medium', $context = []) {
    global $pdo;
    
    try {
        // Create security_logs table if not exists
        $pdo->exec("CREATE TABLE IF NOT EXISTS security_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            event TEXT NOT NULL,
            severity ENUM('low', 'medium', 'high', 'critical') DEFAULT 'medium',
            context JSON,
            ip_address VARCHAR(45),
            user_agent TEXT,
            user_id INT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
        )");
        
        $currentUser = getCurrentUser();
        $stmt = $pdo->prepare("
            INSERT INTO security_logs (event, severity, context, ip_address, user_agent, user_id)
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        
        return $stmt->execute([
            $event,
            $severity,
            json_encode($context),
            $_SERVER['REMOTE_ADDR'] ?? null,
            $_SERVER['HTTP_USER_AGENT'] ?? null,
            $currentUser['id'] ?? null
        ]);
        
    } catch (PDOException $e) {
        error_log("Security log error: " . $e->getMessage());
        return false;
    }
}

/**
 * Check if IP is in whitelist
 * @param string $ip IP address to check
 * @return bool
 */
function isIPWhitelisted($ip) {
    $whitelist = [
        '127.0.0.1',
        '::1',
        'localhost'
    ];
    
    return in_array($ip, $whitelist);
}

/**
 * Check if IP is blacklisted
 * @param string $ip IP address to check
 * @return bool
 */
function isIPBlacklisted($ip) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("
            SELECT COUNT(*) FROM ip_blacklist 
            WHERE ip_address = ? AND (expires_at IS NULL OR expires_at > NOW())
        ");
        $stmt->execute([$ip]);
        return $stmt->fetchColumn() > 0;
    } catch (PDOException $e) {
        error_log("IP blacklist check error: " . $e->getMessage());
        return false;
    }
}

/**
 * Add IP to blacklist
 * @param string $ip IP address
 * @param string $reason Reason for blacklisting
 * @param int $duration Duration in seconds (null for permanent)
 * @return bool
 */
function blacklistIP($ip, $reason = 'Security violation', $duration = null) {
    global $pdo;
    
    try {
        // Create ip_blacklist table if not exists
        $pdo->exec("CREATE TABLE IF NOT EXISTS ip_blacklist (
            id INT AUTO_INCREMENT PRIMARY KEY,
            ip_address VARCHAR(45) NOT NULL,
            reason TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            expires_at TIMESTAMP NULL,
            UNIQUE KEY unique_ip (ip_address)
        )");
        
        $expiresAt = $duration ? date('Y-m-d H:i:s', time() + $duration) : null;
        
        $stmt = $pdo->prepare("
            INSERT INTO ip_blacklist (ip_address, reason, expires_at) 
            VALUES (?, ?, ?)
            ON DUPLICATE KEY UPDATE reason = VALUES(reason), expires_at = VALUES(expires_at)
        ");
        
        return $stmt->execute([$ip, $reason, $expiresAt]);
        
    } catch (PDOException $e) {
        error_log("IP blacklist error: " . $e->getMessage());
        return false;
    }
}

/**
 * Validate file upload security
 * @param array $file Uploaded file info
 * @param array $allowedTypes Allowed MIME types
 * @param int $maxSize Maximum file size in bytes
 * @return array
 */
function validateFileUpload($file, $allowedTypes = [], $maxSize = 5242880) {
    $errors = [];
    
    // Check if file was uploaded
    if (!isset($file['tmp_name']) || !is_uploaded_file($file['tmp_name'])) {
        $errors[] = 'File tidak valid atau tidak terupload';
        return $errors;
    }
    
    // Check file size
    if ($file['size'] > $maxSize) {
        $errors[] = 'Ukuran file terlalu besar. Maksimal ' . formatFileSize($maxSize);
    }
    
    // Check MIME type
    if (!empty($allowedTypes)) {
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_file($finfo, $file['tmp_name']);
        finfo_close($finfo);
        
        if (!in_array($mimeType, $allowedTypes)) {
            $errors[] = 'Tipe file tidak diizinkan';
        }
    }
    
    // Check for malicious content (basic)
    $content = file_get_contents($file['tmp_name'], false, null, 0, 1024);
    if (strpos($content, '<?php') !== false || strpos($content, '<script') !== false) {
        $errors[] = 'File mengandung konten berbahaya';
    }
    
    return $errors;
}
