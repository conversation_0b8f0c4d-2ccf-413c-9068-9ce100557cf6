<?php
require_once 'includes/config/database.php';

$results = [];

try {
    // 1. Check current supplier table structure
    $stmt = $pdo->query("DESCRIBE supplier");
    $currentColumns = $stmt->fetchAll();
    $columnNames = array_column($currentColumns, 'Field');
    
    $results[] = "✅ Current supplier table columns: " . implode(', ', $columnNames);
    
    // 2. Add missing columns if needed
    $requiredColumns = [
        'status' => "ENUM('aktif', 'nonaktif') DEFAULT 'aktif'",
        'created_at' => "TIMESTAMP DEFAULT CURRENT_TIMESTAMP",
        'updated_at' => "TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"
    ];
    
    foreach ($requiredColumns as $column => $definition) {
        if (!in_array($column, $columnNames)) {
            try {
                $pdo->exec("ALTER TABLE supplier ADD COLUMN $column $definition");
                $results[] = "✅ Added missing column: $column";
            } catch (PDOException $e) {
                $results[] = "⚠️ Could not add column $column: " . $e->getMessage();
            }
        } else {
            $results[] = "✅ Column $column already exists";
        }
    }
    
    // 3. Update any NULL status values to 'aktif'
    $stmt = $pdo->query("SELECT COUNT(*) FROM supplier WHERE status IS NULL");
    $nullCount = $stmt->fetchColumn();
    if ($nullCount > 0) {
        $pdo->exec("UPDATE supplier SET status = 'aktif' WHERE status IS NULL");
        $results[] = "✅ Updated $nullCount records with NULL status to 'aktif'";
    } else {
        $results[] = "✅ No NULL status values found";
    }
    
    // 4. Ensure user_id is not NULL
    $stmt = $pdo->query("SELECT COUNT(*) FROM supplier WHERE user_id IS NULL");
    $nullUserCount = $stmt->fetchColumn();
    if ($nullUserCount > 0) {
        $pdo->exec("UPDATE supplier SET user_id = 1 WHERE user_id IS NULL");
        $results[] = "✅ Updated $nullUserCount records with NULL user_id to 1";
    } else {
        $results[] = "✅ No NULL user_id values found";
    }
    
    // 5. Add sample data if table is empty
    $stmt = $pdo->query("SELECT COUNT(*) FROM supplier");
    $count = $stmt->fetchColumn();
    
    if ($count == 0) {
        $stmt = $pdo->prepare("INSERT INTO supplier (user_id, nama_supplier, kontak, email, alamat, keterangan, status) VALUES (?, ?, ?, ?, ?, ?, ?)");
        
        $sampleData = [
            [1, 'PT Supplier Utama', '021-12345678', '<EMAIL>', 'Jl. Sudirman No. 123, Jakarta', 'Supplier utama untuk kebutuhan kantor', 'aktif'],
            [1, 'CV Mitra Jaya', '081234567890', '<EMAIL>', 'Jl. Gatot Subroto No. 456, Bandung', 'Supplier untuk kebutuhan produksi', 'aktif'],
            [1, 'Toko Berkah', '022-87654321', '<EMAIL>', 'Jl. Asia Afrika No. 789, Surabaya', 'Supplier lokal untuk kebutuhan harian', 'nonaktif']
        ];
        
        foreach ($sampleData as $data) {
            $stmt->execute($data);
        }
        $results[] = "✅ Inserted 3 sample suppliers (table was empty)";
    } else {
        $results[] = "✅ Table already has $count suppliers";
    }
    
    // 6. Test all operations
    // Test SELECT
    $stmt = $pdo->query("SELECT COUNT(*) FROM supplier");
    $finalCount = $stmt->fetchColumn();
    $results[] = "✅ Test SELECT: $finalCount suppliers found";
    
    // Test INSERT
    $stmt = $pdo->prepare("INSERT INTO supplier (user_id, nama_supplier, kontak, email, alamat, keterangan, status) VALUES (?, ?, ?, ?, ?, ?, ?)");
    $testResult = $stmt->execute([
        1,
        'Test Supplier ' . date('Y-m-d H:i:s'),
        '081999888777',
        '<EMAIL>',
        'Test Address',
        'Test supplier for validation',
        'aktif'
    ]);
    
    if ($testResult) {
        $testId = $pdo->lastInsertId();
        $results[] = "✅ Test INSERT successful (ID: $testId)";
        
        // Test UPDATE
        $stmt = $pdo->prepare("UPDATE supplier SET nama_supplier = ? WHERE id = ?");
        $updateResult = $stmt->execute(['Updated Test Supplier', $testId]);
        if ($updateResult) {
            $results[] = "✅ Test UPDATE successful";
        }
        
        // Test DELETE
        $stmt = $pdo->prepare("DELETE FROM supplier WHERE id = ?");
        $deleteResult = $stmt->execute([$testId]);
        if ($deleteResult) {
            $results[] = "✅ Test DELETE successful";
        }
    } else {
        $results[] = "❌ Test INSERT failed";
    }
    
    // 7. Final structure check
    $stmt = $pdo->query("DESCRIBE supplier");
    $finalColumns = $stmt->fetchAll();
    $results[] = "✅ Final table structure verified (" . count($finalColumns) . " columns)";
    
    $results[] = "🎉 Supplier table is now fully functional and ready for use!";
    
} catch (PDOException $e) {
    $results[] = "❌ Database error: " . $e->getMessage();
    $results[] = "❌ Error code: " . $e->getCode();
} catch (Exception $e) {
    $results[] = "❌ General error: " . $e->getMessage();
}

?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Safe Supplier Fix - KeuanganKu</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="card shadow">
                    <div class="card-header bg-success text-white">
                        <h4 class="mb-0">
                            <i class="fas fa-shield-alt me-2"></i>Safe Supplier Table Fix
                        </h4>
                    </div>
                    <div class="card-body">
                        
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>Safe Fix Results:</h6>
                            <ul class="mb-0">
                                <?php foreach ($results as $result): ?>
                                <li><?= $result ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                        
                        <?php if (strpos(end($results), '🎉') !== false): ?>
                        <div class="alert alert-success">
                            <h6><i class="fas fa-check-circle me-2"></i>Success!</h6>
                            <p class="mb-0">The supplier table has been safely fixed without losing any existing data. All operations are now working properly.</p>
                        </div>
                        
                        <div class="text-center mt-4">
                            <a href="/keuangan/supplier.php" class="btn btn-primary btn-lg me-2">
                                <i class="fas fa-truck me-2"></i>Go to Supplier Page
                            </a>
                            <a href="/keuangan/index.php" class="btn btn-success btn-lg">
                                <i class="fas fa-home me-2"></i>Dashboard
                            </a>
                        </div>
                        <?php else: ?>
                        <div class="alert alert-warning">
                            <h6><i class="fas fa-exclamation-triangle me-2"></i>Some Issues Found</h6>
                            <p class="mb-0">There were some issues during the fix. Please check the results above and try the alternative fixes below.</p>
                        </div>
                        
                        <div class="text-center mt-4">
                            <a href="/keuangan/fix_foreign_key_issue.php" class="btn btn-warning me-2">
                                <i class="fas fa-unlink me-2"></i>Fix Foreign Keys
                            </a>
                            <a href="/keuangan/diagnose_database.php" class="btn btn-info me-2">
                                <i class="fas fa-stethoscope me-2"></i>Full Diagnostics
                            </a>
                            <a href="/keuangan/recreate_supplier_table.php" class="btn btn-danger">
                                <i class="fas fa-redo me-2"></i>Recreate Table
                            </a>
                        </div>
                        <?php endif; ?>
                        
                        <!-- Additional Info -->
                        <div class="card mt-4">
                            <div class="card-header">
                                <h6><i class="fas fa-info me-2"></i>What This Fix Does</h6>
                            </div>
                            <div class="card-body">
                                <ul class="mb-0">
                                    <li>✅ Preserves all existing supplier data</li>
                                    <li>✅ Adds missing columns (status, created_at, updated_at)</li>
                                    <li>✅ Updates NULL values to proper defaults</li>
                                    <li>✅ Adds sample data if table is empty</li>
                                    <li>✅ Tests all CRUD operations</li>
                                    <li>✅ Maintains foreign key relationships</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
