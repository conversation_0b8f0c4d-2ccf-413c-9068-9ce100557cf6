<?php
require_once '../includes/config/database.php';
require_once '../includes/helpers/functions.php';

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Set content type to JSON
header('Content-Type: application/json');

// Check if user is logged in
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

$currentUser = getCurrentUser();
if (!$currentUser) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'User not found']);
    exit;
}

try {
    // Check database connection
    if (!$pdo) {
        throw new Exception("Database connection failed");
    }

    $method = $_SERVER['REQUEST_METHOD'];
    $input = json_decode(file_get_contents('php://input'), true);

    switch ($method) {
        case 'GET':
            // Get user theme preferences
            $action = $_GET['action'] ?? 'get_preferences';

            if ($action === 'get_preferences') {
                // Check if table exists first
                $stmt = $pdo->query("SHOW TABLES LIKE 'user_theme_preferences'");
                if ($stmt->rowCount() == 0) {
                    throw new Exception("Theme tables not found. Please run setup first.");
                }

                $stmt = $pdo->prepare("
                    SELECT * FROM user_theme_preferences
                    WHERE user_id = ?
                ");
                $stmt->execute([$currentUser['id']]);
                $preferences = $stmt->fetch();

                if (!$preferences) {
                    // Create default preferences
                    $stmt = $pdo->prepare("
                        INSERT INTO user_theme_preferences (user_id, theme_mode, primary_color, secondary_color, sidebar_color, navbar_color)
                        VALUES (?, 'light', '#2563eb', '#64748b', 'dark', 'primary')
                    ");
                    $stmt->execute([$currentUser['id']]);

                    // Get the newly created preferences
                    $stmt = $pdo->prepare("SELECT * FROM user_theme_preferences WHERE user_id = ?");
                    $stmt->execute([$currentUser['id']]);
                    $preferences = $stmt->fetch();
                }

                echo json_encode([
                    'success' => true,
                    'preferences' => $preferences
                ]);
            }

            elseif ($action === 'get_system_settings') {
                $stmt = $pdo->query("SELECT setting_key, setting_value FROM system_settings");
                $settings = [];
                while ($row = $stmt->fetch()) {
                    $settings[$row['setting_key']] = $row['setting_value'];
                }

                echo json_encode([
                    'success' => true,
                    'settings' => $settings
                ]);
            }
            break;

        case 'POST':
            $action = $input['action'] ?? '';

            if ($action === 'update_preferences') {
                $themeMode = $input['theme_mode'] ?? 'light';
                $primaryColor = $input['primary_color'] ?? '#2563eb';
                $secondaryColor = $input['secondary_color'] ?? '#64748b';
                $sidebarColor = $input['sidebar_color'] ?? 'dark';
                $navbarColor = $input['navbar_color'] ?? 'primary';
                $customCss = $input['custom_css'] ?? '';

                $stmt = $pdo->prepare("
                    INSERT INTO user_theme_preferences
                    (user_id, theme_mode, primary_color, secondary_color, sidebar_color, navbar_color, custom_css)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                    ON DUPLICATE KEY UPDATE
                    theme_mode = VALUES(theme_mode),
                    primary_color = VALUES(primary_color),
                    secondary_color = VALUES(secondary_color),
                    sidebar_color = VALUES(sidebar_color),
                    navbar_color = VALUES(navbar_color),
                    custom_css = VALUES(custom_css),
                    updated_at = CURRENT_TIMESTAMP
                ");

                $result = $stmt->execute([
                    $currentUser['id'],
                    $themeMode,
                    $primaryColor,
                    $secondaryColor,
                    $sidebarColor,
                    $navbarColor,
                    $customCss
                ]);

                if ($result) {
                    echo json_encode([
                        'success' => true,
                        'message' => 'Theme preferences updated successfully'
                    ]);
                } else {
                    echo json_encode([
                        'success' => false,
                        'message' => 'Failed to update theme preferences'
                    ]);
                }
            }

            elseif ($action === 'apply_predefined_theme') {
                $themeId = $input['theme_id'] ?? 'default';

                $themes = [
                    'default' => [
                        'theme_mode' => 'light',
                        'primary_color' => '#2563eb',
                        'secondary_color' => '#64748b',
                        'sidebar_color' => 'dark',
                        'navbar_color' => 'primary'
                    ],
                    'dark' => [
                        'theme_mode' => 'dark',
                        'primary_color' => '#3b82f6',
                        'secondary_color' => '#6b7280',
                        'sidebar_color' => 'dark',
                        'navbar_color' => 'dark'
                    ],
                    'modern' => [
                        'theme_mode' => 'light',
                        'primary_color' => '#8b5cf6',
                        'secondary_color' => '#a78bfa',
                        'sidebar_color' => 'primary',
                        'navbar_color' => 'primary'
                    ],
                    'minimal' => [
                        'theme_mode' => 'light',
                        'primary_color' => '#10b981',
                        'secondary_color' => '#6b7280',
                        'sidebar_color' => 'light',
                        'navbar_color' => 'light'
                    ],
                    'ocean' => [
                        'theme_mode' => 'light',
                        'primary_color' => '#0ea5e9',
                        'secondary_color' => '#0284c7',
                        'sidebar_color' => 'info',
                        'navbar_color' => 'info'
                    ],
                    'forest' => [
                        'theme_mode' => 'light',
                        'primary_color' => '#059669',
                        'secondary_color' => '#047857',
                        'sidebar_color' => 'success',
                        'navbar_color' => 'success'
                    ]
                ];

                if (isset($themes[$themeId])) {
                    $theme = $themes[$themeId];

                    $stmt = $pdo->prepare("
                        INSERT INTO user_theme_preferences
                        (user_id, theme_mode, primary_color, secondary_color, sidebar_color, navbar_color)
                        VALUES (?, ?, ?, ?, ?, ?)
                        ON DUPLICATE KEY UPDATE
                        theme_mode = VALUES(theme_mode),
                        primary_color = VALUES(primary_color),
                        secondary_color = VALUES(secondary_color),
                        sidebar_color = VALUES(sidebar_color),
                        navbar_color = VALUES(navbar_color),
                        updated_at = CURRENT_TIMESTAMP
                    ");

                    $result = $stmt->execute([
                        $currentUser['id'],
                        $theme['theme_mode'],
                        $theme['primary_color'],
                        $theme['secondary_color'],
                        $theme['sidebar_color'],
                        $theme['navbar_color']
                    ]);

                    if ($result) {
                        echo json_encode([
                            'success' => true,
                            'message' => 'Theme applied successfully',
                            'theme' => $theme
                        ]);
                    } else {
                        echo json_encode([
                            'success' => false,
                            'message' => 'Failed to apply theme'
                        ]);
                    }
                } else {
                    echo json_encode([
                        'success' => false,
                        'message' => 'Theme not found'
                    ]);
                }
            }
            break;

        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'message' => 'Method not allowed']);
            break;
    }

} catch (PDOException $e) {
    error_log("Theme API error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Database error']);
} catch (Exception $e) {
    error_log("Theme API error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Server error']);
}
?>
