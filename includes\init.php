<?php
/**
 * Application Initialization
 * 
 * This file handles all initialization tasks including:
 * - Session management
 * - Database connection
 * - Error handling
 * - Security settings
 * - Required includes
 */

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Set timezone
date_default_timezone_set('Asia/Jakarta');

// Define constants
define('ROOT_PATH', dirname(__DIR__));
define('INCLUDES_PATH', ROOT_PATH . '/includes');
define('ASSETS_PATH', ROOT_PATH . '/assets');
define('UPLOADS_PATH', ROOT_PATH . '/uploads');

// Load configuration
require_once INCLUDES_PATH . '/config.php';

// Load helper functions
require_once INCLUDES_PATH . '/helpers/functions.php';

// Initialize database connection
try {
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false
        ]
    );
} catch (PDOException $e) {
    error_log("Database connection failed: " . $e->getMessage());
    die("Database connection failed. Please try again later.");
}

// Initialize notifications if user is logged in
if (isset($_SESSION['user_id'])) {
    require_once INCLUDES_PATH . '/helpers/notifications.php';
    $notifications = new Notifications($pdo, $_SESSION['user_id']);
}

// Security functions
function sanitize_input($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

function generate_csrf_token() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

function verify_csrf_token($token) {
    if (!isset($_SESSION['csrf_token']) || $token !== $_SESSION['csrf_token']) {
        return false;
    }
    return true;
}

// Authentication check
function isLoggedIn() {
    return isset($_SESSION['user_id']);
}

function requireLogin() {
    if (!isLoggedIn()) {
        header('Location: /login.php');
        exit;
    }
}

// Permission check
function hasPermission($permission) {
    if (!isLoggedIn()) {
        return false;
    }
    
    // Get user permissions from session
    $permissions = $_SESSION['permissions'] ?? [];
    
    return in_array($permission, $permissions);
}

function requirePermission($permission) {
    if (!hasPermission($permission)) {
        header('HTTP/1.1 403 Forbidden');
        die('You do not have permission to access this resource.');
    }
}

// Error handling
function handle_error($errno, $errstr, $errfile, $errline) {
    $error = [
        'type' => $errno,
        'message' => $errstr,
        'file' => $errfile,
        'line' => $errline
    ];
    
    error_log(json_encode($error));
    
    if (ini_get('display_errors')) {
        echo "<pre>";
        print_r($error);
        echo "</pre>";
    }
    
    return true;
}

set_error_handler('handle_error');

// Exception handling
function handle_exception($exception) {
    $error = [
        'type' => get_class($exception),
        'message' => $exception->getMessage(),
        'file' => $exception->getFile(),
        'line' => $exception->getLine(),
        'trace' => $exception->getTraceAsString()
    ];
    
    error_log(json_encode($error));
    
    if (ini_get('display_errors')) {
        echo "<pre>";
        print_r($error);
        echo "</pre>";
    }
}

set_exception_handler('handle_exception');

// Session security
function regenerate_session() {
    if (!isset($_SESSION['last_regeneration'])) {
        $_SESSION['last_regeneration'] = time();
    } else {
        $interval = 30 * 60; // 30 minutes
        if (time() - $_SESSION['last_regeneration'] >= $interval) {
            session_regenerate_id(true);
            $_SESSION['last_regeneration'] = time();
        }
    }
}

regenerate_session();

// Output buffering
ob_start();

// Set headers
header('X-Frame-Options: DENY');
header('X-XSS-Protection: 1; mode=block');
header('X-Content-Type-Options: nosniff');
header('Referrer-Policy: strict-origin-when-cross-origin');
header('Content-Security-Policy: default-src \'self\'; script-src \'self\' \'unsafe-inline\' \'unsafe-eval\'; style-src \'self\' \'unsafe-inline\'; img-src \'self\' data:; font-src \'self\' data:;');

// Clean output buffer on shutdown
register_shutdown_function(function() {
    $output = ob_get_clean();
    
    // Remove any whitespace before DOCTYPE
    $output = preg_replace('/^\s+/', '', $output);
    
    echo $output;
});

// Function to get total income
function getTotalPemasukan() {
    global $pdo;
    try {
        $stmt = $pdo->prepare("
            SELECT COALESCE(SUM(t.jumlah), 0) as total
            FROM transaksi t
            JOIN kategori k ON t.kategori_id = k.id
            WHERE t.user_id = ? AND k.tipe = 'pemasukan'
        ");
        $stmt->execute([$_SESSION['user_id']]);
        $result = $stmt->fetch();
        return $result['total'] ?? 0;
    } catch (PDOException $e) {
        error_log("Error getting total pemasukan: " . $e->getMessage());
        return 0;
    }
}

// Function to get total expenses
function getTotalPengeluaran() {
    global $pdo;
    try {
        $stmt = $pdo->prepare("
            SELECT COALESCE(SUM(t.jumlah), 0) as total
            FROM transaksi t
            JOIN kategori k ON t.kategori_id = k.id
            WHERE t.user_id = ? AND k.tipe = 'pengeluaran'
        ");
        $stmt->execute([$_SESSION['user_id']]);
        $result = $stmt->fetch();
        return $result['total'] ?? 0;
    } catch (PDOException $e) {
        error_log("Error getting total pengeluaran: " . $e->getMessage());
        return 0;
    }
}

// Function to get recent transactions
function getRecentTransactions($limit = 5) {
    global $pdo;
    try {
        $stmt = $pdo->prepare("
            SELECT t.*, k.nama_kategori, k.tipe
            FROM transaksi t
            JOIN kategori k ON t.kategori_id = k.id
            WHERE t.user_id = ?
            ORDER BY t.tanggal DESC
            LIMIT ?
        ");
        $stmt->execute([$_SESSION['user_id'], $limit]);
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        error_log("Error getting recent transactions: " . $e->getMessage());
        return [];
    }
}

// Fungsi untuk mendapatkan transaksi terakhir
function getTransaksiTerakhir($limit = 5) {
    global $pdo;
    try {
        $stmt = $pdo->prepare("
            SELECT t.*, k.nama_kategori, k.tipe
            FROM transaksi t
            JOIN kategori k ON t.kategori_id = k.id
            WHERE t.user_id = ?
            ORDER BY t.tanggal DESC, t.created_at DESC
            LIMIT ?
        ");
        $stmt->execute([$_SESSION['user_id'], $limit]);
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        error_log("Error getting recent transactions: " . $e->getMessage());
        return [];
    }
}

// Fungsi untuk mendapatkan kategori terbanyak
function getKategoriTerbanyak($limit = 5) {
    global $pdo;
    try {
        $stmt = $pdo->prepare("
            SELECT 
                k.nama_kategori,
                COUNT(t.id) as total_transaksi,
                COALESCE(SUM(t.jumlah), 0) as total_jumlah
            FROM kategori k
            LEFT JOIN transaksi t ON k.id = t.kategori_id AND t.user_id = ?
            WHERE k.user_id = ?
            GROUP BY k.id
            ORDER BY total_transaksi DESC
            LIMIT ?
        ");
        $stmt->execute([$_SESSION['user_id'], $_SESSION['user_id'], $limit]);
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        error_log("Error getting top categories: " . $e->getMessage());
        return [];
    }
}

// Fungsi untuk mendapatkan target terdekat
function getTargetTerdekat($limit = 3) {
    global $pdo;
    try {
        $stmt = $pdo->prepare("
            SELECT 
                nama as nama_target,
                jumlah_target as target_jumlah,
                jumlah_terkumpul as terkumpul,
                tanggal_selesai
            FROM target
            WHERE user_id = ? AND status = 'aktif'
            ORDER BY tanggal_selesai ASC
            LIMIT ?
        ");
        $stmt->execute([$_SESSION['user_id'], $limit]);
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        error_log("Error getting nearest targets: " . $e->getMessage());
        return [];
    }
}

// Fungsi untuk mendapatkan produk terlaris
function getProdukTerlaris($limit = 5) {
    global $pdo;
    try {
        $stmt = $pdo->prepare("
            SELECT 
                p.nama as nama_produk,
                COUNT(tp.id) as total_terjual,
                COALESCE(SUM(tp.jumlah * tp.harga_jual), 0) as total_penjualan
            FROM produk p
            LEFT JOIN transaksi_produk tp ON p.id = tp.produk_id AND tp.user_id = ?
            WHERE p.user_id = ?
            GROUP BY p.id
            ORDER BY total_terjual DESC
            LIMIT ?
        ");
        $stmt->execute([$_SESSION['user_id'], $_SESSION['user_id'], $limit]);
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        error_log("Error getting top products: " . $e->getMessage());
        return [];
    }
}

// Fungsi untuk mendapatkan penjualan hari ini
function getPenjualanHariIni() {
    global $pdo;
    try {
        $stmt = $pdo->prepare("
            SELECT COALESCE(SUM(jumlah * harga_jual), 0) as total
            FROM transaksi_produk
            WHERE user_id = ? AND DATE(tanggal) = CURDATE()
        ");
        $stmt->execute([$_SESSION['user_id']]);
        $result = $stmt->fetch();
        return $result['total'] ?? 0;
    } catch (PDOException $e) {
        error_log("Error getting today's sales: " . $e->getMessage());
        return 0;
    }
}

// Fungsi untuk mendapatkan penjualan minggu ini
function getPenjualanMingguIni() {
    global $pdo;
    try {
        $stmt = $pdo->prepare("
            SELECT COALESCE(SUM(jumlah * harga_jual), 0) as total
            FROM transaksi_produk
            WHERE user_id = ? 
            AND YEARWEEK(tanggal) = YEARWEEK(CURDATE())
        ");
        $stmt->execute([$_SESSION['user_id']]);
        $result = $stmt->fetch();
        return $result['total'] ?? 0;
    } catch (PDOException $e) {
        error_log("Error getting this week's sales: " . $e->getMessage());
        return 0;
    }
}

// Fungsi untuk mendapatkan penjualan bulan ini
function getPenjualanBulanIni() {
    global $pdo;
    try {
        $stmt = $pdo->prepare("
            SELECT COALESCE(SUM(jumlah * harga_jual), 0) as total
            FROM transaksi_produk
            WHERE user_id = ? 
            AND MONTH(tanggal) = MONTH(CURDATE())
            AND YEAR(tanggal) = YEAR(CURDATE())
        ");
        $stmt->execute([$_SESSION['user_id']]);
        $result = $stmt->fetch();
        return $result['total'] ?? 0;
    } catch (PDOException $e) {
        error_log("Error getting this month's sales: " . $e->getMessage());
        return 0;
    }
} 