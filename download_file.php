<?php
require_once 'includes/config/database.php';
require_once 'includes/helpers/functions.php';

// Check admin access
$currentUser = getCurrentUser();
if (!$currentUser || $currentUser['role'] !== 'admin') {
    http_response_code(403);
    echo 'Access denied';
    exit;
}

try {
    $file = $_GET['file'] ?? '';
    
    if (empty($file)) {
        throw new Exception('File parameter is required');
    }
    
    // Security check - only allow certain file types and paths
    $allowedExtensions = ['php', 'sql', 'txt', 'md', 'json', 'css', 'js', 'html'];
    $allowedPaths = ['database/', 'includes/', ''];
    
    $extension = strtolower(pathinfo($file, PATHINFO_EXTENSION));
    if (!in_array($extension, $allowedExtensions)) {
        throw new Exception('File type not allowed');
    }
    
    $isAllowedPath = false;
    foreach ($allowedPaths as $allowedPath) {
        if (strpos($file, $allowedPath) === 0) {
            $isAllowedPath = true;
            break;
        }
    }
    
    if (!$isAllowedPath) {
        throw new Exception('File path not allowed');
    }
    
    // Check if file exists
    if (!file_exists($file)) {
        throw new Exception('File not found: ' . $file);
    }
    
    // Check if file is readable
    if (!is_readable($file)) {
        throw new Exception('File is not readable: ' . $file);
    }
    
    // Set headers for download
    $filename = basename($file);
    $size = filesize($file);
    
    header('Content-Type: application/octet-stream');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Content-Length: ' . $size);
    header('Cache-Control: must-revalidate');
    header('Pragma: public');
    
    // Output file content
    readfile($file);
    
} catch (Exception $e) {
    http_response_code(400);
    echo 'Error: ' . $e->getMessage();
}
?>
